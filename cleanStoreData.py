from datetime import datetime
import os
import csv
import psycopg2
from sqlalchemy import create_engine

import psycopg2
import pandas as pd
from datetime import datetime

# Connect to the PostgreSQL database


def read_csv_files(deviceId: str) -> None:
    engine = create_engine(
        'postgresql://krushallocal:krushallocal@localhost:5432/krushallocal')
    ''' conn = psycopg2.connect(
        host="localhost",
        database="krushallocal",
        user="krushallocal",
        password="krushallocal",
        options="-c search_path=location"
        
    )'''
    rootDir = '../../experiments/Data/' + deviceId + '/Trajectory/'
    for root, dirs, files in os.walk(rootDir):
        for file in files:
            if file.endswith(".plt"):
                file_path = os.path.join(root, file)
                # Load the CSV file using pandas
                columns = ['lat', 'lng', 'fake', 'altitude', 'timedec', 'date', 'time']
                df = pd.read_csv(file_path, names=columns)
                # now do some conversions
                df['accuracy'] = 899.0
                df['bearing'] = 0.0
                df['location_on_device_id'] = 0
                df['is_from_mock_provider'] = False
                df['mock_locations_enabled'] = False
                df['location_provider'] = 1
                df['provider'] = 'fused'
                df['speed'] = 0.0
                df['user_device_id'] = deviceId
                df['time_at'] = df.apply(lambda x: datetime.strptime(
                    x['date'] + ':' + x['time'], '%Y-%m-%d:%H:%M:%S'), axis=1)
            #    df['created_at']=datetime.now()
            #    df['updated_at'] = datetime.now()
            #    df['raw_location_id'] = df.index
            #    last_col = df.columns[-1]
            #    new_cols = [last_col] + list(df.columns[:-1])
            #    df = df[new_cols]                # Create a cursor object
               # cur = conn.cursor()
                # Insert the data into PostgreSQL
                df = df.drop(['timedec', 'date', 'time', 'fake'], axis=1)
                res = df.to_sql('raw_location', engine, if_exists='append',
                                index=False, schema='location')
                print(res)


if __name__ == "__main__":
    # Example usage
    # Connect to the PostgreSQL databas
    for deviceId in ["001", "002"]:
        read_csv_files(deviceId)
