module.exports = function(grunt) {
  grunt.initConfig({
    terser: {
      target: {
        files: [{
          expand: true,
          src: ['**/*.js', '!db/migrations/**', '!node_modules/**', '!*Gruntfile.js', '!migration-service-build/**', '!package-build/**'],
          dest: 'package-build'
        }]
      }
    },
    copy: {
      files: {
        src: ['package.json', '!**/*.js', 'db/migrations/**', '!node_modules/**', '.npm*', '.gitignore*'],
        dest: 'package-build',    // destination folder
        expand: true      // required when using cwd
      }
    }
  });
  grunt.loadNpmTasks('grunt-terser');
  grunt.loadNpmTasks('grunt-contrib-copy');
  grunt.registerTask('default', ['copy', 'terser']);
}