package in.krushal.ah.native_app;

import android.app.Application;
import android.util.Log;
import androidx.work.Data;
import androidx.work.Constraints;
import androidx.work.ExistingPeriodicWorkPolicy;
import androidx.work.ExistingWorkPolicy;
import androidx.work.NetworkType;
import androidx.work.OneTimeWorkRequest;
import androidx.work.PeriodicWorkRequest;
import androidx.work.WorkManager;
import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint;
import com.facebook.react.defaults.DefaultReactNativeHost;
import com.facebook.soloader.SoLoader;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.json.JSONObject;
import org.json.JSONArray;

public class MainApplication extends Application implements ReactApplication {
  private static final String TAG = "MainApplication";

  private static final String SYNCHRONIZATION_CONFIGURATION = "SYNCHRONIZATION_CONFIGURATION";
  private static final String PERIODIC_SYNCHRONIZATION_WORK = "PERIODIC_SYNCHRONIZATION_WORK";
  private static final String LOCATION_UPDATE_WORK = "LOCATION_UPDATE_WORK";

  private final ReactNativeHost mReactNativeHost = new DefaultReactNativeHost(this) {
    @Override
    public boolean getUseDeveloperSupport() {
      Log.i(TAG, "ReactNativeH getUseDeveloperSupport 1");
      return BuildConfig.DEBUG;
    }

    @Override
    protected List<ReactPackage> getPackages() {
      @SuppressWarnings("UnnecessaryLocalVariable")
      List<ReactPackage> packages = new PackageList(this).getPackages();
      Log.i(TAG, "ReactNativeH getPackages 1");
      packages.add(new KrushalRNAndroidBridgePackage());
      Log.i(TAG, "ReactNativeH getPackages 2");
      return packages;
    }

    @Override
    protected String getJSMainModuleName() {
      return "index";
    }

    @Override
    protected boolean isNewArchEnabled() {
      Log.i(TAG, "ReactNativeH isNewArchEnabled 1");
      return BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
    }

    @Override
    protected Boolean isHermesEnabled() {
      Log.i(TAG, "ReactNativeH isHermesEnabled 1");
      return BuildConfig.IS_HERMES_ENABLED;
    }
  };

  @Override
  public ReactNativeHost getReactNativeHost() {
    Log.i(TAG, "getReactNativeHost 1");
    return mReactNativeHost;
  }

  @Override
  public void onCreate() {
    super.onCreate();
    Log.i(TAG, "onCreate 1");
    SoLoader.init(this, /* native exopackage */ false);
    Log.i(TAG, "onCreate 2");
    Log.i(TAG, "onCreate 3 schedulePeriodicSynchronization commented out");

    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      Log.i(TAG, "onCreate 4");
      DefaultNewArchitectureEntryPoint.load();
      Log.i(TAG, "onCreate 5");
    }
    Log.i(TAG, "onCreate 6");
    ReactNativeFlipper.initializeFlipper(this, getReactNativeHost().getReactInstanceManager());
    Log.i(TAG, "onCreate 7");
  }

  private String constructSynchronizationConfigurationAsJSONString() {
    String returnValue = "{}";
    try {
      Log.i(TAG, "constructSynchronizationConfigurationAsJSONString 1");
      JSONObject synchronizationConfiguration = new JSONObject();
      JSONArray serverMain = new JSONArray();
      JSONArray serverConfig = new JSONArray();
      JSONArray clientMain = new JSONArray();
      JSONArray clientConfig = new JSONArray();

      JSONObject serverTables = new JSONObject();
      JSONObject clientTables = new JSONObject();

      serverTables.put("main", serverMain);
      serverTables.put("config", serverConfig);
      clientTables.put("main", clientMain);
      clientTables.put("config", clientConfig);

      synchronizationConfiguration.put("uploadUnsyncedFiles", false);
      synchronizationConfiguration.put("syncUnsyncedClientTables", true);
      synchronizationConfiguration.put("syncUnsyncedServerTables", true);
      synchronizationConfiguration.put("downloadUnsyncedFiles", true);
      synchronizationConfiguration.put("serverTables", serverTables);
      synchronizationConfiguration.put("clientTables", clientTables);
      Log.i(TAG, "constructSynchronizationConfigurationAsJSONString 2");

      returnValue = synchronizationConfiguration.toString();
    } catch (Exception exception) {
      Log.i(TAG, "constructSynchronizationConfigurationAsJSONString 3");
      Log.e(TAG, "constructSynchronizationConfigurationAsJSONString 3", exception);
    }
    return returnValue;
  }

  private long calculateInitialDelayFor6AM() {
    Calendar now = Calendar.getInstance();
    Calendar sixAM = Calendar.getInstance();
    sixAM.set(Calendar.HOUR_OF_DAY, 6);
    sixAM.set(Calendar.MINUTE, 0);
    sixAM.set(Calendar.SECOND, 0);

    if (now.after(sixAM)) {
      sixAM.add(Calendar.DATE, 1);
    }

    return sixAM.getTimeInMillis() - now.getTimeInMillis();
  }
}