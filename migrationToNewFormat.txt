SECTION 1 START - change schema -> run in public schema -> admin user
ALTER TABLE animal_classification SET SCHEMA main;
ALTER TABLE animal_disease SET SCHEMA main;
ALTER TABLE animal_disease_classification SET SCHEMA main;
ALTER TABLE care_calendar SET SCHEMA main;
ALTER TABLE care_calendar_classification SET SCHEMA main;
ALTER TABLE customer SET SCHEMA main;
ALTER TABLE customer_classification SET SCHEMA main;
ALTER TABLE document SET SCHEMA main;
ALTER TABLE entity_geography SET SCHEMA main;
ALTER TABLE entity_relationship SET SCHEMA main;
ALTER TABLE job_tracker SET SCHEMA main;
ALTER TABLE mst_activity SET SCHEMA main;
ALTER TABLE mst_animal SET SCHEMA main;
ALTER TABLE mst_district SET SCHEMA main;
ALTER TABLE mst_reference SET SCHEMA main;
ALTER TABLE mst_reference_category SET SCHEMA main;
ALTER TABLE mst_staff SET SCHEMA main;
ALTER TABLE mst_state SET SCHEMA main;
ALTER TABLE mst_taluka SET SCHEMA main;
ALTER TABLE mst_village SET SCHEMA main;
ALTER TABLE note SET SCHEMA main;
ALTER TABLE staff_activity SET SCHEMA main;
ALTER TABLE staff_classification SET SCHEMA main;
ALTER TABLE ticket SET SCHEMA main;
ALTER TABLE dump_document SET SCHEMA main;
SECTION 1 END

SECTION 2 START - rename tables -> mst_staff to staff, mst_animal to animal
set search_path = main;

ALTER TABLE mst_staff rename to staff;
ALTER TABLE mst_activity rename to ref_activity;
ALTER TABLE mst_animal rename to animal;
ALTER TABLE mst_reference_category rename to ref_reference_category;
ALTER TABLE mst_reference rename to ref_reference;
ALTER TABLE mst_state rename to ref_state;
ALTER TABLE mst_district rename to ref_district;
ALTER TABLE mst_taluka rename to ref_taluk;
ALTER TABLE mst_village rename to ref_village;
SECTION 2 END

SECTION 2A START drop default of uuid pimary key columns

ALTER TABLE "main"."staff_classification" ALTER COLUMN "staff_classification_id" DROP DEFAULT;
ALTER TABLE "main"."customer_classification" ALTER COLUMN "customer_classification_id" DROP DEFAULT;
ALTER TABLE "main"."animal_classification" ALTER COLUMN "animal_classification_id" DROP DEFAULT;
ALTER TABLE "main"."entity_relationship" ALTER COLUMN "entity_relationship_id" DROP DEFAULT;
ALTER TABLE "main"."document" ALTER COLUMN "document_id" DROP DEFAULT;
ALTER TABLE "main"."animal_disease" ALTER COLUMN "animal_disease_id" DROP DEFAULT;
ALTER TABLE "main"."animal_disease_classification" ALTER COLUMN "animal_disease_classification_id" DROP DEFAULT;
ALTER TABLE "main"."care_calendar" ALTER COLUMN "care_calendar_id" DROP DEFAULT;
ALTER TABLE "main"."staff_activity" ALTER COLUMN "staff_activity_id" DROP DEFAULT;
ALTER TABLE "main"."ticket" ALTER COLUMN "ticket_id" DROP DEFAULT;
ALTER TABLE "main"."job_tracker" ALTER COLUMN "job_tracker_id" DROP DEFAULT;
ALTER TABLE "main"."note" ALTER COLUMN "note_id" DROP DEFAULT;
ALTER TABLE "main"."entity_geography" ALTER COLUMN "entity_geography_id" DROP DEFAULT;
ALTER TABLE "main"."care_calendar_classification" ALTER COLUMN "care_calendar_classification_id" DROP DEFAULT;


ALTER TABLE "main"."animal" ALTER COLUMN "animal_id" DROP DEFAULT;
ALTER TABLE "main"."customer" ALTER COLUMN "customer_id" DROP DEFAULT;
ALTER TABLE "main"."staff" ALTER COLUMN "staff_id" DROP DEFAULT;
ALTER TABLE "main"."dump_document" ALTER COLUMN "dump_document_id" DROP DEFAULT;
SECTION 2A END

SECTION 2B START - move uuid
set search_path = public;
DROP EXTENSION IF EXISTS "uuid-ossp";
set search_path = common;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" SCHEMA common;
set search_path = main;
SECTION 2B END

SECTION 2C START - re-add default of uuid primary key columns
ALTER TABLE "main"."staff_classification" ALTER COLUMN "staff_classification_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."customer_classification" ALTER COLUMN "customer_classification_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."animal_classification" ALTER COLUMN "animal_classification_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."entity_relationship" ALTER COLUMN "entity_relationship_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."document" ALTER COLUMN "document_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."animal_disease" ALTER COLUMN "animal_disease_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."animal_disease_classification" ALTER COLUMN "animal_disease_classification_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."care_calendar" ALTER COLUMN "care_calendar_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."staff_activity" ALTER COLUMN "staff_activity_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."ticket" ALTER COLUMN "ticket_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."job_tracker" ALTER COLUMN "job_tracker_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."note" ALTER COLUMN "note_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."entity_geography" ALTER COLUMN "entity_geography_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."care_calendar_classification" ALTER COLUMN "care_calendar_classification_id" SET DEFAULT common.uuid_generate_v1mc();
ALTER TABLE "main"."dump_document" ALTER COLUMN "dump_document_id" SET DEFAULT common.uuid_generate_v1mc();
SECTION 2C END


SECTION 3 START - customer, staff, animal
alter table "main"."customer" add column "customer_id_int" integer NULL
update "main"."customer" set customer_id_int = customer_id

ALTER TABLE "main"."customer"
DROP CONSTRAINT "PK_cde3d123fc6077bcd75eb051226",
ALTER COLUMN "customer_id" DROP DEFAULT,
ALTER COLUMN "customer_id" DROP NOT NULL,
ALTER COLUMN "customer_id" TYPE uuid using (common.uuid_generate_v1mc()),
ALTER COLUMN "customer_id" SET DEFAULT common.uuid_generate_v1mc(),
ADD CONSTRAINT "PK_customer_id" PRIMARY KEY ("customer_id")

alter table "main"."animal" add column "animal_id_int" integer NULL
update "main"."animal" set animal_id_int = animal_id

ALTER TABLE "main"."animal"
DROP CONSTRAINT "PK_ae47c92f44b9ad0af66217e9b8b",
ALTER COLUMN "animal_id" DROP DEFAULT,
ALTER COLUMN "animal_id" DROP NOT NULL,
ALTER COLUMN "animal_id" TYPE uuid using (common.uuid_generate_v1mc()),
ALTER COLUMN "animal_id" SET DEFAULT common.uuid_generate_v1mc(),
ADD CONSTRAINT "PK_animal_id" PRIMARY KEY ("animal_id")


alter table "main"."staff" add column "staff_id_int" integer NULL
update "main"."staff" set staff_id_int = staff_id

ALTER TABLE "main"."staff"
DROP CONSTRAINT "PK_caecf718a9b48364532a9c8ab6d",
ALTER COLUMN "staff_id" DROP DEFAULT,
ALTER COLUMN "staff_id" DROP NOT NULL,
ALTER COLUMN "staff_id" TYPE uuid using (common.uuid_generate_v1mc()),
ALTER COLUMN "staff_id" SET DEFAULT common.uuid_generate_v1mc(),
ADD CONSTRAINT "PK_staff_id" PRIMARY KEY ("staff_id")
SECTION 3 END

SECTION 3A START - taluka becomes taluk
ALTER TABLE "main"."ref_taluk" RENAME COLUMN "taluka_id" to "taluk_id";
ALTER TABLE "main"."ref_taluk" RENAME COLUMN "taluka_census_id" to "taluk_census_id";
ALTER TABLE "main"."ref_taluk" RENAME COLUMN "taluka_name" to "taluk_name";
ALTER TABLE "main"."ref_taluk" RENAME COLUMN "taluka_code" to "taluk_code";
ALTER TABLE "main"."ref_taluk" RENAME COLUMN "taluka_name_json" to "taluk_name_json";
ALTER TABLE "main"."ref_village" RENAME COLUMN "taluka_id" to "taluk_id";
ALTER TABLE "main"."ref_reference_category" RENAME COLUMN "mst_reference_category_id" to "reference_category_id";
ALTER TABLE "main"."ref_reference" RENAME COLUMN "reference_name_json" to "reference_name_l10n";
ALTER TABLE "main"."ref_state" RENAME COLUMN "state_name_json" to "state_name_l10n";
ALTER TABLE "main"."ref_state" RENAME COLUMN "state_code" to "state_short_code";
ALTER TABLE "main"."ref_district" RENAME COLUMN "district_name_json" to "district_name_l10n";
ALTER TABLE "main"."ref_district" RENAME COLUMN "district_code" to "district_short_code";
ALTER TABLE "main"."ref_taluk" RENAME COLUMN "taluk_name_json" to "taluk_name_l10n";
ALTER TABLE "main"."ref_taluk" RENAME COLUMN "taluk_code" to "taluk_short_code";
ALTER TABLE "main"."ref_village" RENAME COLUMN "village_name_json" to "village_name_l10n";
ALTER TABLE "main"."ref_village" RENAME COLUMN "village_code" to "village_short_code";

ALTER TABLE "main"."ref_activity" RENAME COLUMN "activity_name_json" to "activity_name_l10n";

ALTER TABLE "main"."document" RENAME COLUMN "entity_1_id" to "entity_1_entity_id";
ALTER TABLE "main"."document" RENAME COLUMN "entity_2_id" to "entity_2_entity_id";
ALTER TABLE "main"."document" RENAME COLUMN "entity_3_id" to "entity_3_entity_id";

ALTER TABLE "main"."staff" RENAME COLUMN "staff_type" to "staff_type_id";
ALTER TABLE "main"."staff" RENAME COLUMN "name" to "staff_name_l10n";
ALTER TABLE "main"."staff" RENAME COLUMN "mobile" to "mobile_number";
ALTER TABLE "main"."staff" RENAME COLUMN "email" to "email_address";

ALTER TABLE "main"."customer" RENAME COLUMN "customer_type" to "customer_type_id";
ALTER TABLE "main"."customer" RENAME COLUMN "name" to "customer_name_l10n";
ALTER TABLE "main"."customer" RENAME COLUMN "mobile" to "mobile_number";
ALTER TABLE "main"."customer" RENAME COLUMN "email" to "email_address";




ALTER TABLE ref_reference ALTER COLUMN type_of_data TYPE varchar(256);
ALTER TABLE "main"."ref_reference" ALTER COLUMN "reference_name" TYPE character varying(256);
ALTER TABLE "main"."ref_reference" ALTER COLUMN "field_name" TYPE character varying(256);
ALTER TABLE "main"."ref_reference" ALTER COLUMN "reference_name_l10n" TYPE jsonb;
ALTER TABLE "main"."ref_state" ALTER COLUMN "state_name_l10n" TYPE jsonb;
ALTER TABLE "main"."ref_state" ALTER COLUMN "state_short_code" TYPE character varying(256);
ALTER TABLE "main"."ref_district" ALTER COLUMN "district_name_l10n" TYPE jsonb;
ALTER TABLE "main"."ref_district" ALTER COLUMN "district_short_code" TYPE character varying(256);
ALTER TABLE "main"."ref_taluk" ALTER COLUMN "taluk_name_l10n" TYPE jsonb;
ALTER TABLE "main"."ref_taluk" ALTER COLUMN "taluk_short_code" TYPE character varying(256);
ALTER TABLE "main"."ref_village" ALTER COLUMN "village_name_l10n" TYPE jsonb;
ALTER TABLE "main"."ref_village" ALTER COLUMN "village_short_code" TYPE character varying(256);

ALTER TABLE "main"."document" ALTER COLUMN "document_name" TYPE character varying(256);
ALTER TABLE "main"."document" ALTER COLUMN "document_information" TYPE jsonb;

ALTER TABLE "main"."entity_relationship" ALTER COLUMN "start_date" TYPE TIMESTAMP, ALTER COLUMN "start_date" SET DEFAULT TO_TIMESTAMP('1900-01-01 00:00:01', 'YYYY-MM-DD HH24:MI:SS');
ALTER TABLE "main"."entity_relationship" ALTER COLUMN "end_date" TYPE TIMESTAMP, ALTER COLUMN "end_date" SET DEFAULT TO_TIMESTAMP('2099-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS');

ALTER TABLE "main"."entity_relationship" ALTER COLUMN "entity_information" TYPE jsonb;

ALTER TABLE "main"."entity_geography" ALTER COLUMN "start_date" TYPE TIMESTAMP, ALTER COLUMN "start_date" SET DEFAULT TO_TIMESTAMP('1900-01-01 00:00:01', 'YYYY-MM-DD HH24:MI:SS');
ALTER TABLE "main"."entity_geography" ALTER COLUMN "end_date" TYPE TIMESTAMP, ALTER COLUMN "end_date" SET DEFAULT TO_TIMESTAMP('2099-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS');

ALTER TABLE "main"."staff" ALTER COLUMN "staff_visual_id" TYPE character varying(256);
ALTER TABLE "main"."staff" ALTER COLUMN "staff_name_l10n" TYPE jsonb;
ALTER TABLE "main"."staff" ALTER COLUMN "mobile_number" TYPE character varying(256);
ALTER TABLE "main"."staff" ALTER COLUMN "email_address" TYPE character varying(256);
ALTER TABLE "main"."staff_classification" ALTER COLUMN "value_string_256" TYPE character varying(256);
ALTER TABLE "main"."staff_classification" ALTER COLUMN "value_string_2000" TYPE character varying(2000);
ALTER TABLE "main"."staff_classification" ALTER COLUMN "value_double" TYPE numeric(14,4);
ALTER TABLE "main"."staff_classification" ALTER COLUMN "value_date" TYPE TIMESTAMP;
ALTER TABLE "main"."staff_classification" ALTER COLUMN "value_json" TYPE jsonb;
ALTER TABLE "main"."customer" ALTER COLUMN "customer_visual_id" TYPE character varying(256);
ALTER TABLE "main"."customer" ALTER COLUMN "customer_name_l10n" TYPE jsonb;
ALTER TABLE "main"."customer" ALTER COLUMN "mobile_number" TYPE character varying(256);
ALTER TABLE "main"."customer" ALTER COLUMN "email_address" TYPE character varying(256);
ALTER TABLE "main"."customer_classification" ALTER COLUMN "value_string_256" TYPE character varying(256);
ALTER TABLE "main"."customer_classification" ALTER COLUMN "value_string_2000" TYPE character varying(2000);
ALTER TABLE "main"."customer_classification" ALTER COLUMN "value_double" TYPE numeric(14,4);
ALTER TABLE "main"."customer_classification" ALTER COLUMN "value_date" TYPE TIMESTAMP;
ALTER TABLE "main"."customer_classification" ALTER COLUMN "value_json" TYPE jsonb;
ALTER TABLE "main"."animal" ALTER COLUMN "animal_visual_id" TYPE character varying(256);
ALTER TABLE "main"."animal_classification" ALTER COLUMN "value_string_256" TYPE character varying(256);
ALTER TABLE "main"."animal_classification" ALTER COLUMN "value_string_2000" TYPE character varying(2000);
ALTER TABLE "main"."animal_classification" ALTER COLUMN "value_double" TYPE numeric(14,4);
ALTER TABLE "main"."animal_classification" ALTER COLUMN "value_date" TYPE TIMESTAMP;
ALTER TABLE "main"."animal_classification" ALTER COLUMN "value_json" TYPE jsonb;
ALTER TABLE "main"."care_calendar_classification" ALTER COLUMN "value_string_256" TYPE character varying(256);
ALTER TABLE "main"."care_calendar_classification" ALTER COLUMN "value_string_2000" TYPE character varying(2000);
ALTER TABLE "main"."care_calendar_classification" ALTER COLUMN "value_double" TYPE numeric(14,4);
ALTER TABLE "main"."care_calendar_classification" ALTER COLUMN "value_date" TYPE TIMESTAMP;
ALTER TABLE "main"."care_calendar_classification" ALTER COLUMN "value_json" TYPE jsonb;
ALTER TABLE "main"."animal_disease_classification" ALTER COLUMN "value_string_256" TYPE character varying(256);
ALTER TABLE "main"."animal_disease_classification" ALTER COLUMN "value_string_2000" TYPE character varying(2000);
ALTER TABLE "main"."animal_disease_classification" ALTER COLUMN "value_double" TYPE numeric(14,4);
ALTER TABLE "main"."animal_disease_classification" ALTER COLUMN "value_date" TYPE TIMESTAMP;
ALTER TABLE "main"."animal_disease_classification" ALTER COLUMN "value_json" TYPE jsonb;
ALTER TABLE "main"."note" ALTER COLUMN "note" TYPE jsonb;
ALTER TABLE "main"."note" RENAME COLUMN "entity_1_id" to "entity_1_uuid";
ALTER TABLE "main"."ref_activity" ALTER COLUMN "activity_name_l10n" TYPE jsonb;
ALTER TABLE "main"."job_tracker" ALTER COLUMN "job_type" TYPE character varying(256);
ALTER TABLE "main"."job_tracker" ALTER COLUMN "job_information" TYPE jsonb;
ALTER TABLE "main"."animal_classification" ALTER COLUMN "value_string_256_encrypted" TYPE character varying(256);
ALTER TABLE "main"."animal_classification" ALTER COLUMN "value_string_2000_encrypted" TYPE character varying(2000);
ALTER TABLE "main"."animal_disease_classification" ALTER COLUMN "value_string_256_encrypted" TYPE character varying(256);
ALTER TABLE "main"."animal_disease_classification" ALTER COLUMN "value_string_2000_encrypted" TYPE character varying(2000);
ALTER TABLE "main"."care_calendar_classification" ALTER COLUMN "value_string_256_encrypted" TYPE character varying(256);
ALTER TABLE "main"."care_calendar_classification" ALTER COLUMN "value_string_2000_encrypted" TYPE character varying(2000);
ALTER TABLE "main"."customer_classification" ALTER COLUMN "value_string_256_encrypted" TYPE character varying(256);
ALTER TABLE "main"."customer_classification" ALTER COLUMN "value_string_2000_encrypted" TYPE character varying(2000);
ALTER TABLE "main"."staff_classification" ALTER COLUMN "value_string_256_encrypted" TYPE character varying(256);
ALTER TABLE "main"."staff_classification" ALTER COLUMN "value_string_2000_encrypted" TYPE character varying(2000);



ALTER TABLE "main"."animal_disease" ALTER COLUMN "disease_date" TYPE TIMESTAMP;


SECTION 3A END

SECTION 3B START - rename PK constraints
ALTER TABLE "main"."animal_classification" RENAME CONSTRAINT "PK_9e49fa07f79468f17666aa653dd" TO "PK_animal_classification_id";
ALTER TABLE "main"."animal_disease" RENAME CONSTRAINT "PK_cfbfb1e786c93e3c7287516b287" TO "PK_animal_disease_id";
ALTER TABLE "main"."animal_disease_classification" RENAME CONSTRAINT "PK_c986497dab43bb0ea8872d70aee" TO "PK_animal_disease_classification_id";
ALTER TABLE "main"."care_calendar" RENAME CONSTRAINT "PK_e895d5932c08918b2f8da92a1f1" TO "PK_care_calendar_id";
ALTER TABLE "main"."care_calendar_classification" RENAME CONSTRAINT "PK_727867b706823d1ec539a5c9c6f" TO "PK_care_calendar_classification_id";
ALTER TABLE "main"."customer_classification" RENAME CONSTRAINT "PK_45bc35a93b7bb99821872762cff" TO "PK_customer_classification_id";
ALTER TABLE "main"."document" RENAME CONSTRAINT "PK_78f5e16f1322a7b2b150364dddc" TO "PK_document_id";
ALTER TABLE "main"."entity_geography" RENAME CONSTRAINT "PK_8988dfb02468f133c63f4ba04d3" TO "PK_entity_geography_id";
ALTER TABLE "main"."entity_relationship" RENAME CONSTRAINT "PK_1cf7ab8df1ac08ef019bd0f6041" TO "PK_entity_relationship_id";
ALTER TABLE "main"."job_tracker" RENAME CONSTRAINT "PK_1d9dcb432f3e171bcbf8f917180" TO "PK_job_tracker_id";
ALTER TABLE "main"."ref_activity" RENAME CONSTRAINT "PK_d8bf8b1cf653ee56d5932030183" TO "PK_activity_id";
ALTER TABLE "main"."note" RENAME CONSTRAINT "PK_bf848d92c68dc271fef5993dba9" TO "PK_note_id";
ALTER TABLE "main"."ref_district" RENAME CONSTRAINT "PK_714017421395983f0c47764d0f6" TO "PK_district_id";
ALTER TABLE "main"."ref_reference" RENAME CONSTRAINT "PK_e0e71d7172e6407e36b0c3c560d" TO "PK_reference_id";
ALTER TABLE "main"."ref_reference_category" RENAME CONSTRAINT "PK_19b3231ca0992681389f2eee715" TO "PK_reference_category_id";
ALTER TABLE "main"."ref_state" RENAME CONSTRAINT "PK_03477b4d68627d867e7736b66ac" TO "PK_state_id";

ALTER TABLE "main"."ref_taluk" RENAME CONSTRAINT "PK_050a9152b863d150851450b5b0f" TO "PK_taluk_id";

ALTER TABLE "main"."ref_village" RENAME CONSTRAINT "PK_d538dd269681c2ec769ae24d4ea" TO "PK_village_id";
ALTER TABLE "main"."staff_activity" RENAME CONSTRAINT "PK_9d5c73a4f5ad847f47c0f2fe303" TO "PK_staff_activity_id";
ALTER TABLE "main"."staff_classification" RENAME CONSTRAINT "PK_6f9bab198d5207deca8635f4b9f" TO "PK_staff_classification_id";
ALTER TABLE "main"."ticket" RENAME CONSTRAINT "PK_a7b0a31430509c3d3e22832e341" TO "PK_ticket_id";





SECTION 3B END

SECTION 4 START - customer_classification, staff_classification, animal_classification, staff_activity, staff_disease, staff_activity (entity_id)
ALTER TABLE "main"."customer_classification" ADD COLUMN "customer_id_int" INTEGER NULL
UPDATE "main"."customer_classification" SET customer_id_int = customer_id


ALTER TABLE "main"."customer_classification"
ALTER COLUMN "customer_id" DROP NOT NULL

update "main"."customer_classification" SET customer_id = NULL

ALTER TABLE "main"."customer_classification"
ALTER COLUMN "customer_id" TYPE uuid using (common.uuid_generate_v1mc())

update "main"."customer_classification" SET customer_id = NULL

//update customer_classification.customer_id using customer_id_int
UPDATE "main"."customer_classification" ct SET customer_id = pt.customer_id
FROM "main"."customer" pt
WHERE ct.customer_id_int = pt.customer_id_int

// nul constraint remains ALTER TABLE "main"."customer_classification" ALTER COLUMN "customer_id" SET NOT NULL;



ALTER TABLE "main"."animal_classification" ADD COLUMN "animal_id_int" INTEGER NULL
UPDATE "main"."animal_classification" SET animal_id_int = animal_id


ALTER TABLE "main"."animal_classification"
ALTER COLUMN "animal_id" DROP NOT NULL

update "main"."animal_classification" SET animal_id = NULL

ALTER TABLE "main"."animal_classification"
ALTER COLUMN "animal_id" TYPE uuid using (common.uuid_generate_v1mc())

update "main"."animal_classification" SET animal_id = NULL


//update animal_classification.animal_id using customer_id_int
UPDATE "main"."animal_classification" ct SET animal_id = pt.animal_id
FROM "main"."animal" pt
WHERE ct.animal_id_int = pt.animal_id_int

//  retaining not null constraint ALTER TABLE "main"."animal_classification" ALTER COLUMN "animal_id" SET NOT NULL;



ALTER TABLE "main"."staff_classification" ADD COLUMN "staff_id_int" INTEGER NULL
UPDATE "main"."staff_classification" SET staff_id_int = staff_id


ALTER TABLE "main"."staff_classification"
ALTER COLUMN "staff_id" DROP NOT NULL

update "main"."staff_classification" SET staff_id = NULL

ALTER TABLE "main"."staff_classification"
ALTER COLUMN "staff_id" TYPE uuid using (common.uuid_generate_v1mc())

update "main"."staff_classification" SET staff_id = NULL


//update animal_classification.animal_id using customer_id_int
UPDATE "main"."staff_classification" ct SET staff_id = pt.staff_id
FROM "main"."staff" pt
WHERE ct.staff_id_int = pt.staff_id_int

// retaining not null constraint ALTER TABLE "main"."staff_classification" ALTER COLUMN "staff_id" SET NOT NULL;




ALTER TABLE "main"."staff_activity" ADD COLUMN "staff_id_int" INTEGER NULL
UPDATE "main"."staff_activity" SET staff_id_int = staff_id WHERE staff_id is not null


update "main"."staff_activity" SET staff_id = NULL

ALTER TABLE "main"."staff_activity"
ALTER COLUMN "staff_id" TYPE uuid using (common.uuid_generate_v1mc())

update "main"."staff_activity" SET staff_id = NULL


//update animal_classification.animal_id using customer_id_int
UPDATE "main"."staff_activity" ct SET staff_id = pt.staff_id
FROM "main"."staff" pt
WHERE ct.staff_id_int = pt.staff_id_int and ct.staff_id_int is not NULL




ALTER TABLE "main"."animal_disease" ADD COLUMN "animal_id_int" INTEGER NULL
UPDATE "main"."animal_disease" SET animal_id_int = animal_id


ALTER TABLE "main"."animal_disease"
ALTER COLUMN "animal_id" DROP NOT NULL

update "main"."animal_disease" SET animal_id = NULL

ALTER TABLE "main"."animal_disease"
ALTER COLUMN "animal_id" TYPE uuid using (common.uuid_generate_v1mc())

update "main"."animal_disease" SET animal_id = NULL


//update animal_classification.animal_id using customer_id_int
UPDATE "main"."animal_disease" ct SET animal_id = pt.animal_id
FROM "main"."animal" pt
WHERE ct.animal_id_int = pt.animal_id_int

//  retaining not null constraint ALTER TABLE "main"."animal_disease" ALTER COLUMN "animal_id" SET NOT NULL;




ALTER TABLE "main"."staff_activity" ADD COLUMN "entity_id_int" INTEGER NULL
UPDATE "main"."staff_activity" SET entity_id_int = entity_id


ALTER TABLE "main"."staff_activity"
ALTER COLUMN "entity_id" DROP NOT NULL

update "main"."staff_activity" SET entity_id = NULL

ALTER TABLE "main"."staff_activity"
ALTER COLUMN "entity_id" TYPE uuid using (common.uuid_generate_v1mc())

update "main"."staff_activity" SET entity_id = NULL


//update animal_classification.animal_id using customer_id_int
UPDATE "main"."staff_activity" ct SET entity_id = pt.animal_id
FROM "main"."animal" pt
WHERE ct.entity_id_int = pt.animal_id_int

//  retaining not null constraint ALTER TABLE "main"."staff_activity" ALTER COLUMN "entity_id" SET NOT NULL;




SECTION 4 END

SECTION 4A START - change default for active or enums
ALTER TABLE "main"."animal" DROP COLUMN "active";
ALTER TABLE "main"."animal" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."animal_classification" DROP COLUMN "active";
ALTER TABLE "main"."animal_classification" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."animal_disease" DROP COLUMN "active";
ALTER TABLE "main"."animal_disease" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."animal_disease_classification" DROP COLUMN "active";
ALTER TABLE "main"."animal_disease_classification" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."care_calendar_classification" DROP COLUMN "active";
ALTER TABLE "main"."care_calendar_classification" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."customer" DROP COLUMN "active";
ALTER TABLE "main"."customer" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."customer_classification" DROP COLUMN "active";
ALTER TABLE "main"."customer_classification" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."entity_relationship" DROP COLUMN "active";
ALTER TABLE "main"."entity_relationship" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."ref_reference" DROP COLUMN "active";
ALTER TABLE "main"."ref_reference" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."ref_reference_category" DROP COLUMN "active";
ALTER TABLE "main"."ref_reference_category" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."staff" DROP COLUMN "active";
ALTER TABLE "main"."staff" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."note" DROP COLUMN "active";
ALTER TABLE "main"."note" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."staff_classification" DROP COLUMN "active";
ALTER TABLE "main"."staff_classification" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."animal" DROP COLUMN "active";
ALTER TABLE "main"."animal" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."animal_classification" DROP COLUMN "active";
ALTER TABLE "main"."animal_classification" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."animal_disease" DROP COLUMN "active";
ALTER TABLE "main"."animal_disease" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001

ALTER TABLE "main"."animal_disease_classification" DROP COLUMN "active";
ALTER TABLE "main"."animal_disease_classification" ADD COLUMN "active" INTEGER NOT NULL DEFAULT 1000100001


ALTER TABLE "main"."ticket" DROP COLUMN "ticket_status";
ALTER TABLE "main"."ticket" ADD COLUMN "ticket_status_id" INTEGER;

update main.entity_geography set geography_type_id = 1000320004 where geography_type_id is null
SECTION 4A END

SECTION 4B START
Before running migration check for animal_classification null entries

SECTION 4B END
SECTION 5 START - POST MIGRATION, note, document, entity_relationship, entity_geography, care_calendar
entity_1_entity_id / entity_id => entity_1_entity_id  / entity_id, entity_1_entity_uuid / entity_uuid
// copy corresponding uuid based on id into the uuid column

UPDATE "main"."entity_relationship" ct SET entity_1_entity_uuid = pt.customer_id
FROM "main"."customer" pt
WHERE ct.entity_1_entity_id = pt.customer_id_int

UPDATE "main"."entity_relationship" ct SET entity_2_entity_uuid = pt.animal_id
FROM "main"."animal" pt
WHERE ct.entity_2_entity_id = pt.animal_id_int

UPDATE "main"."entity_geography" ct SET entity_uuid = pt.staff_id
FROM "main"."staff" pt
WHERE ct.entity_id = pt.staff_id_int

UPDATE "main"."care_calendar" ct SET entity_uuid = pt.animal_id
FROM "main"."animal" pt
WHERE ct.entity_id = pt.animal_id_int


UPDATE "main"."document" ct SET entity_1_entity_uuid = pt.staff_id
FROM "main"."staff" pt
WHERE ct.entity_1_entity_id = pt.staff_id_int and ct.entity_1_type_id in (1000230001, 1000230002, 1000230004)

UPDATE "main"."document" ct SET entity_1_entity_uuid = pt.customer_id
FROM "main"."customer" pt
WHERE ct.entity_1_entity_id = pt.customer_id_int and ct.entity_1_type_id in (1000220001)

UPDATE "main"."document" ct SET entity_1_entity_uuid = pt.animal_id
FROM "main"."animal" pt
WHERE ct.entity_1_entity_id = pt.animal_id_int and ct.entity_1_type_id in (1000220002)

UPDATE "main"."note" ct SET creator_uuid = pt.staff_id
FROM "main"."staff" pt
WHERE ct.creator_id = pt.staff_id_int

SECTION 5 END




Create ah-orm-migration-service
before running on stage / production, insert 1672035003652-InitialDesign, 1673810500879-CreateSDTVView, 1677422615634-model_shifts into migrations
run the modifications script
migrate database
run the script of 1673810500879-CreateSDTVView after complete