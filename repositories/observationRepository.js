const { dbConnections, postProcessRecords } = require('@krushal-it/ah-orm')
const { getCompiledQuery } = require('../queries/index.js')
const { note_type, entity_type, record_statuses } = require('../ENUMS.js')

class ObservationRepository {
    
    async getAll(care_calendar_id) {

        try {

            let query = getCompiledQuery("get-all-observations");

            const queryResult = 
                await dbConnections().main.manager.createQueryBuilder()
                    .from(`(${query})`, "get-all-observations")
                    .setParameters(
                        {
                            care_calendar_id: care_calendar_id
                        }
                    )
                    .getRawMany();
            
            postProcessRecords(
                undefined, 
                queryResult, 
                { 
                    json_columns: [
                        'observation_category', 
                        'observation_name', 
                        'observation_val'
                    ] 
                }
            )
            
            return queryResult;

        } catch (error) {
            throw new Error(`Error getting observations from database: ${error.message}`);
        }
    }

    async getNotes(care_calendar_id) {

        try {
            let queryResult = await dbConnections().main.repos["note"].findOne({
            where: { 
                note_type_id: note_type.OBSERVATION_NOTES, 
                entity_1_type_id: entity_type.TASK_LEGACY, 
                entity_1_uuid: care_calendar_id, 
                active: record_statuses.ACTIVE 
            },
            select: { 
                note: true, 
            },
            order: {
                created_at: 'DESC' 
            }
            });

            postProcessRecords(
                undefined, 
                queryResult, 
                { 
                    json_columns: ["note"] 
                }
            );

          return queryResult;

        } catch (error) {
            throw new Error(`Error getting observation notes from database: ${error.message}`);
        }
    }
   
}

module.exports = new ObservationRepository();
