const { dbConnections, postProcessRecords , preProcessRecords} = require('@krushal-it/ah-orm')

const { ensureArray } = require('../utils/utils.js')
const { In } = require('typeorm')
const { configurationJSON } = require('@krushal-it/common-core')
const { RELATION_DOC } = require('../utils/constant.js')

class DocumentRepository {
    insertToDocument = async (value ,transaction) => {
        try {
            const isPostgres = (configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1) ? true : false
            const entity_document = dbConnections().main.entities[RELATION_DOC];
            const preProcessedValue = preProcessRecords(entity_document, value, entity_document.additionalAttributes)
            let qb = await transaction.createQueryBuilder()
                .insert().into(RELATION_DOC).values(preProcessedValue)
            if (isPostgres) {
                qb = qb.returning("document_id")
            }
            let data =await qb.execute()
            if (isPostgres) {
                if(data.raw){
                    data = data.identifiers.map(doc => doc.document_id)
                }
        
                return data
            } else {
                if (data.identifiers) {
                    data = data.identifiers.map(doc => doc.document_id)
                }
                return data
            }
        } catch (error) {
            throw new Error(error)
        }

    };
    getDocument = async (document_id) => {
        let loadDocument = await dbConnections().main.repos['document'].find({
            where: {
              document_id: In(ensureArray(document_id)),
              active: ACTIVE
            },
            select: { document_information: true }
          })
          loadDocument = postProcessRecords(null, loadDocument, { json_columns: ['document_information'] })
          return loadDocument.map(doument => doument.document_information)
    }

}

module.exports = new DocumentRepository()
