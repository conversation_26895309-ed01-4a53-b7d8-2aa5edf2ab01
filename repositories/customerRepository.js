const { postProcessRecords } = require('@krushal-it/ah-orm')
const { record_statuses, relationship_types } = require('../ENUMS.js')
const { ensureArray } = require('../utils/utils.js')
const { In } = require('typeorm')

class CustomerRepository {
  // can process one or multiple customer ids
  
  async getCustomerDetails(transaction, customer_id) {
    try {
      customer_id = ensureArray(customer_id)
      let response = {}

      const customerDetails = await transaction.getRepository('customer').find({
        where: [
          {
            customer_id: In(customer_id),
            active: record_statuses.ACTIVE,
          },
        ],
      })
      postProcessRecords(undefined, customerDetails, {
        json_columns: ['customer_name_l10n'],
      })

      const customerToAnimalData = await transaction.getRepository('entity_relationship').find({
        where: [
          {
            entity_1_entity_uuid: In(customer_id),
            entity_relationship_type_id: relationship_types.FARMER_TO_ANIMAL,
            active: record_statuses.ACTIVE,
          },
        ],
      })

      const customerClassificationDetails = await transaction.getRepository('customer_classification').find({
        where: [
          {
            customer_id: In(customer_id),
            active: record_statuses.ACTIVE,
          },
        ],
      })

      postProcessRecords(undefined, customerClassificationDetails, {
        json_columns: ['value_json', 'value_l10n'],
      })

      // Populate the object with primary details
      customerDetails.forEach((customer) => {
        
        const animal_ids = customerToAnimalData.map(item => item.entity_2_entity_uuid);
        response[customer.customer_id] = {
          primary: customer,
          animal_id: animal_ids,
          classification: [],
        }
      })

      // Add classification details to the corresponding customer_id
      customerClassificationDetails.forEach((customerClassification) => {
        if (response[customerClassification.customer_id]) {
          response[customerClassification.customer_id].classification.push(customerClassification)
        }
      })

      return response
    } catch (error) {
      throw new Error(`Error getting customer data from database: ${error.message}`)
    }
  }

  async findOne(customer_data, classifier_id) {
    
    if(customer_data && customer_data.classification) {
      return customer_data.classification.find(item => item["classifier_id"] === classifier_id)
    }
    return undefined;
  }

}

module.exports = new CustomerRepository()
