const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm')
const { getCompiledQuery } = require('../queries/index.js')
const { entity_type, record_statuses, relationship_types ,staff_activity_statuses ,document_type,preventive_healthcare_activities} = require('../ENUMS.js')
const { ensureArray } = require('../utils/utils.js')
const { In } = require('typeorm')
const { RELATION_CALENDAR_CLASSIFICATION, RELATION_CALENDAR } = require('../utils/constant.js')

class TaskRepository {
  // can process one or multiple care calendar ids
  async getTaskDetails(transaction, care_calendar_id) {
    try {
      care_calendar_id = ensureArray(care_calendar_id)
      let response = {}

      const taskDetails = await transaction.getRepository(RELATION_CALENDAR).find({
        where: [
          {
            care_calendar_id: In(care_calendar_id),
            active: record_statuses.ACTIVE,
          },
        ],
      })

      const taskClassificationDetails = await transaction.getRepository(RELATION_CALENDAR_CLASSIFICATION).find({
        where: [
          {
            care_calendar_id: In(care_calendar_id),
            active: record_statuses.ACTIVE,
          },
        ],
      })

      postProcessRecords(undefined, taskClassificationDetails, {
        json_columns: ['value_json', 'value_l10n'],
      })

      // Populate the object with primary details
      taskDetails.forEach((task) => {
        response[task.care_calendar_id] = {
          primary: task,
          classification: [],
        }
      })

      // Add classification details to the corresponding care_calendar_id
      taskClassificationDetails.forEach((taskClassification) => {
        if (response[taskClassification.care_calendar_id]) {
          response[taskClassification.care_calendar_id].classification.push(taskClassification)
        }
      })

      return response
    } catch (error) {
      throw new Error(`Error getting task data: ${error.message}`)
    }
  }

  async getTaskPrimaryData(transaction, care_calendar_id, filters) {
    
    try {

      care_calendar_id = ensureArray(care_calendar_id)

      if(!care_calendar_id) {
        throw new Error(`Bad Request: Task id is mandatory`)
      }

      filters = filters || {}
      let response = {};

      const taskDetails = await transaction.getRepository(RELATION_CALENDAR).find({
        where: [
          {
            care_calendar_id: In(care_calendar_id),
            active: record_statuses.ACTIVE,
            ...filters
          },
        ],
      })

      response = taskDetails.reduce((acc, item) => {

        acc[item.care_calendar_id] = item;
        return acc;
      }, {});

      return response

    } catch (error) {
      throw new Error(`Error getting task primary data: ${error.message}`)
    }
  }

  async getTaskClassificationData(transaction, care_calendar_id, classifierIds, filters) {
    try {
      care_calendar_id = ensureArray(care_calendar_id)
      classifierIds = ensureArray(classifierIds)
      
      if(!classifierIds) {
        throw new Error(`Bad Request: classifier id is mandatory`)
      }

      filters = filters || {}
      let response = {}
      
      if(care_calendar_id) {
        filters['care_calendar_id'] = In(care_calendar_id)
      }

      const taskClassificationDetails = await transaction.getRepository(RELATION_CALENDAR_CLASSIFICATION).find({
        where: [
          {
            classifier_id: In(classifierIds),
            active: record_statuses.ACTIVE,
            ...filters
          },
        ],
      })

      postProcessRecords(undefined, taskClassificationDetails, {
        json_columns: ['value_json', 'value_l10n'],
      })

      response = taskClassificationDetails.reduce((acc, item) => {

        if (!acc[item.care_calendar_id]) {
          acc[item.care_calendar_id] = {};
        }
        
        if (!acc[item.care_calendar_id][item.classifier_id]) {
          acc[item.care_calendar_id][item.classifier_id] = [];
        }
        
        // Add the fields grouped by classifier_id
        acc[item.care_calendar_id][item.classifier_id].push(item)
        
        return acc;
      }, {});


      return response

    } catch (error) {
      throw new Error(`Error getting task classification data: ${error.message}`)
    }
  }

  // returns the customer id associated with a task
  async getCustomerId(transaction, taskData) {
    try {

      const taskDataMain = taskData.primary;
      const taskDataClassification = taskData.classification;
      let customer_id = taskDataMain.entity_uuid;

      if (taskDataMain.entity_type_id === entity_type.ANIMAL) {
        
        const entityRelationshipDetails = await transaction.getRepository('entity_relationship').findOne({
          where: [
            {
              entity_2_entity_uuid: taskDataMain.entity_uuid,
              entity_relationship_type_id: relationship_types.FARMER_TO_ANIMAL,
              active: record_statuses.ACTIVE,
            },
          ],
        })
        customer_id = entityRelationshipDetails.entity_1_entity_uuid
        
      }
      return customer_id
    } catch (error) {
      throw new Error(`Error getting customer id: ${error.message}`)
    }
  }

  // returns the customer id associated with a task
  async getAnimalId(transaction, taskData) {
    try {

      const taskDataMain = taskData.primary;
      const taskDataClassification = taskData.classification;
      let animal_id = taskDataMain.entity_uuid;

      if (taskDataMain.entity_type_id === entity_type.CUSTOMER) {
  
        const entityRelationshipDetails = await transaction.getRepository('entity_relationship').findOne({
          where: [
            {
              entity_1_entity_uuid: taskDataMain.entity_uuid,
              entity_relationship_type_id: relationship_types.FARMER_TO_ANIMAL,
              active: record_statuses.ACTIVE,
            },
          ],
        })
  
        animal_id = entityRelationshipDetails.entity_2_entity_uuid
      } 
      return animal_id
    } catch (error) {
      throw new Error(`Error getting animal id: ${error.message}`)
    }
  }

  // can process one or multiple care calendar entries
  async createTask(transaction, care_calendar_data) {
    try {
      care_calendar_data = ensureArray(care_calendar_data)
      const careCalendarEntity = dbConnections().main.entities[RELATION_CALENDAR]

      const preProcessedValues = preProcessRecords(careCalendarEntity, care_calendar_data, careCalendarEntity.additionalAttributes)

      const careCalendarEntityRow = await transaction.getRepository(RELATION_CALENDAR).insert(preProcessedValues)

      return careCalendarEntityRow.identifiers[0].care_calendar_id
    } catch (error) {
      throw new Error(`Error creating task: ${error.message}`)
    }
  }

  // can update one or multiple care calendar entries
  async updateTask(transaction, care_calendar_data) {
    try {
      care_calendar_data = ensureArray(care_calendar_data)
      const careCalendarEntity = dbConnections().main.entities[RELATION_CALENDAR]

      const preProcessedValues = preProcessRecords(careCalendarEntity, care_calendar_data, careCalendarEntity.additionalAttributes)

      await transaction.getRepository(RELATION_CALENDAR).save(preProcessedValues)

    } catch (error) {
      throw new Error(`Error updating task: ${error.message}`)
    }
  }

  // can process one or multiple care calendar classification entries
  // updates the exisiting ones to inactive and inserts new ones
  async saveTaskClassifications(transaction, care_calendar_classifications_data) {
    try {
      care_calendar_classifications_data = ensureArray(care_calendar_classifications_data)
      const careCalendarClassificationEntity = dbConnections().main.entities[RELATION_CALENDAR_CLASSIFICATION]

      // Filter classifiers which does not have an existing care calendar classification id
      // Inactivate those before inserting new ones
      let classifierIdsToInactivate = care_calendar_classifications_data.reduce((result, care_calendar_classifications_item) => {
        if (!care_calendar_classifications_item.care_calendar_classification_id) {
          result.push(care_calendar_classifications_item.classifier_id)
        }
        return result
      }, [])

      let careCalendarIds = care_calendar_classifications_data.map(item => item.care_calendar_id);

      if (classifierIdsToInactivate.length > 0) {
        await transaction.getRepository(RELATION_CALENDAR_CLASSIFICATION).update(
          {
            care_calendar_id: In(careCalendarIds),
            classifier_id: In(classifierIdsToInactivate),
            active: record_statuses.ACTIVE,
          },
          { active: record_statuses.INACTIVE }
        )
      }

      // Populate the object with primary details
      const preProcessedValues = preProcessRecords(careCalendarClassificationEntity, care_calendar_classifications_data, careCalendarClassificationEntity.additionalAttributes)
      await transaction.getRepository(RELATION_CALENDAR_CLASSIFICATION).save(preProcessedValues)

      return true
    } catch (error) {
      throw new Error(`Error updating task classifications: ${error.message}`)
    }
  }

  // get all completed carecalendar (Farm level) by customer id  
  async getFarmLevelTaskByCustomerId(transaction , customer_id) {
    try {
      const care_calendars = await transaction.getRepository('care_calendar').find({
        where : {
          entity_type_id : entity_type.CUSTOMER,
          entity_uuid: customer_id,
          active: record_statuses.ACTIVE,
          calendar_activity_status : staff_activity_statuses.VISIT_COMPLETED
        }
      })
      postProcessRecords(undefined, care_calendars, {
        json_columns: [],
      })
      return care_calendars
    } catch (error) {
      throw new Error(`Error while getting Farm level task`,{ cause: error })

    }
  }

    // get all completed farm management tasks by customer id  
    async getFarmManagementTaskByCustomerId(transaction , customer_id) {
      try {
        const care_calendars = await transaction.getRepository('care_calendar').find({
          where : {
            entity_type_id : entity_type.CUSTOMER,
            entity_uuid: customer_id,
            active: record_statuses.ACTIVE,
            calendar_activity_status : staff_activity_statuses.VISIT_COMPLETED,
            activity_id: preventive_healthcare_activities.FARM_MANAGEMENT
          }
        })
        postProcessRecords(undefined, care_calendars, {
          json_columns: [],
        })
        return care_calendars
      } catch (error) {
        throw new Error(`Error while getting Farm Management task`,{ cause: error })
  
      }
    }

    
  // get all  carecalendar (Farm level) by customer id  
    async getAllFarmLevelTaskByCustomerId(transaction , customer_id) {
      try {
        const care_calendars = await transaction.getRepository('care_calendar').find({
          where : {
            entity_type_id : entity_type.CUSTOMER,
            entity_uuid: customer_id,
            active: record_statuses.ACTIVE
          }
        })
        postProcessRecords(undefined, care_calendars, {
          json_columns: [],
        })
        return care_calendars
      } catch (error) {
        throw new Error(`Error while getting Farm level task`,{ cause: error })
  
      }
    }
        // get all  carecalendar (Farm level) by customer id   with new doc type

        async getAllFarmLevelTaskAndDocumentByCustomerId(transaction , customer_id) {
          try {
            const care_calendars = await transaction
            .getRepository('care_calendar')
            .createQueryBuilder('cc')
            .leftJoin(
              qb => {
                return qb
                  .select('d.document_id', 'document_id')
                  .addSelect('d.entity_1_entity_uuid', 'entity_1_entity_uuid')
                  .from('document', 'd')
                  .where('d.document_type_id = :document_type_id', { document_type_id: document_type.FARM_MANAGEMENT_PDF })
                  .orderBy('d.entity_1_entity_uuid')
                  .addOrderBy('d.document_id')
              },
              'd',
              'd.entity_1_entity_uuid = cc.care_calendar_id'
            )
            .select([
              "cc.care_calendar_id AS care_calendar_id",
              "cc.activity_id AS activity_id",
              "MAX(cc.calendar_activity_status) AS calendar_activity_status",
              "MAX(cc.completion_date) AS completion_date",
              "COALESCE(MAX(d.document_id::text), NULL) AS document_id", 
              "MAX(cc.activity_date) AS activity_date"
            ])
            .where('cc.entity_type_id = :entityTypeId', { entityTypeId: entity_type.CUSTOMER })
            .andWhere('cc.entity_uuid = :customerId', { customerId: customer_id })
            .andWhere('cc.activity_id = :activityId', { activityId: preventive_healthcare_activities.FARM_MANAGEMENT })
            .andWhere('cc.active = :activeStatus', { activeStatus: record_statuses.ACTIVE })
            .groupBy("cc.care_calendar_id")
            .orderBy('MAX(cc.activity_date)', 'DESC')
            .getRawMany();
             
            postProcessRecords(undefined, care_calendars, {
              json_columns: [],
            })
            return care_calendars
          } catch (error) {
            throw new Error(`Error while getting Farm level task`,{ cause: error })
      
          }
        }
}

module.exports = new TaskRepository()
