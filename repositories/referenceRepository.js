const { postProcessRecords } = require('@krushal-it/ah-orm')
const { record_statuses } = require('../ENUMS.js')
const { ensureArray } = require('../utils/utils.js')
const { In } = require('typeorm')
const { RELATION_REFERENCE } = require('../utils/constant.js')

class ReferenceRepository {

  // can process one or multiple care calendar ids
  async getReferenceDetails(transaction, reference_id) {
    try {
      reference_id = ensureArray(reference_id)
      let response = {}

      const referenceDetails = await transaction.getRepository(RELATION_REFERENCE).find({
        where: [
          {
            reference_id: In(reference_id),
            active: record_statuses.ACTIVE,
          },
        ],
      })

      postProcessRecords(undefined, referenceDetails, {
        json_columns: ['reference_name_l10n', 'reference_information'],
      })

      // Populate the object with primary details
      referenceDetails.forEach((item) => {
        response[item.reference_id] = item
      })

      return response
    } catch (error) {
      throw new Error(`Error getting reference data: ${error.message}`)
    }
  }

  async getReferenceDetailsByCategoryId(transaction, reference_category_id) {
    try {
      reference_category_id = ensureArray(reference_category_id)
      let response = {}

      const referenceDetails = await transaction.getRepository(RELATION_REFERENCE).find({
        where: [
          {
            reference_category_id: In(reference_category_id),
            active: record_statuses.ACTIVE,
          },
        ],
      })

      postProcessRecords(undefined, referenceDetails, {
        json_columns: ['reference_name_l10n', 'reference_information'],
      })

      // Populate the object with primary details
      referenceDetails.forEach((item) => {
        response[item.reference_id] = item
      })

      return response
    } catch (error) {
      throw new Error(`Error getting reference data: ${error.message}`)
    }
  }
}

module.exports = new ReferenceRepository()
