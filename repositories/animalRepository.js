const { postProcessRecords } = require('@krushal-it/ah-orm')
const { record_statuses, relationship_types } = require('../ENUMS.js')
const { ensureArray } = require('../utils/utils.js')
const { In } = require('typeorm')

class AnimalRepository {
  // can process one or multiple animal ids
  async getCattleDetails(transaction, animal_id) {
    try {
      animal_id = ensureArray(animal_id)
      let response = {}

      const animalDetails = await transaction.getRepository('animal').find({
        where: [
          {
            animal_id: In(animal_id),
            active: record_statuses.ACTIVE,
          },
        ],
      })

      const customerToAnimalData = await transaction.getRepository('entity_relationship').find({
        where: [
          {
            entity_2_entity_uuid: In(animal_id),
            entity_relationship_type_id: relationship_types.FARMER_TO_ANIMAL,
            active: record_statuses.ACTIVE,
          },
        ],
      })

      const animalClassificationDetails = await transaction.getRepository('animal_classification').find({
        where: [
          {
            animal_id: In(animal_id),
            active: record_statuses.ACTIVE,
          },
        ],
      })

      postProcessRecords(undefined, animalClassificationDetails, {
        json_columns: ['value_json', 'value_l10n'],
      })
      
      // Populate the object with primary details
      animalDetails.forEach((animal) => {
        const customerIdRow = customerToAnimalData.find(item => item.entity_2_entity_uuid === animal.animal_id)
        response[animal.animal_id] = {
          primary: animal,
          customer_id: customerIdRow.entity_1_entity_uuid,
          classification: [],
        }
      })

      // Add classification details to the corresponding animal_id
      animalClassificationDetails.forEach((animalClassification) => {
        if (response[animalClassification.animal_id]) {
          response[animalClassification.animal_id].classification.push(animalClassification)
        }
      })

      return response
    } catch (error) {
      throw new Error(`Error getting cattle data from database: ${error.message}`)
    }
  }

  async findOne(animal_data, classifier_id) {
    
    if(animal_data && animal_data.classification) {
      return animal_data.classification.find(item => item["classifier_id"] === classifier_id)
    }
    return undefined;
  }

}

module.exports = new AnimalRepository()
