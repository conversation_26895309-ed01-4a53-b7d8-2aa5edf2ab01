version: "3"

services:
  location_analytics_debug:
    container_name: location_analytics_debug
    build: .
    image: location_analytics:latest
    env_file:
      - ./.env
    command:
      [
        "sh",
        "-c",
        "poetry install && python -m debugpy --wait-for-client --listen 0.0.0.0:4101 -m uvicorn main:app --reload --host 0.0.0.0 --port 4001 "
      ]
    volumes:
      - ./app/:/app
    ports:
      - 44101:4101
      - 44001:4001

#volumes:
#  app:
networks:
  default:
    external:
      name: krushal_network
