<<<<<<< HEAD

upstream node_app4 {
    server **************;
}


server {
    charset utf-8;
    server_name ahs1.krushal.in www.ahs1.krushal.in;
    client_max_body_size 200M;
    listen 443;
    ssl on;
    ssl_certificate /etc/nginx/ssl/signed.cert.chain.crt;
    ssl_certificate_key /etc/nginx/ssl/domain.key;


    location / {

        root /usr/share/nginx/html/app1;
        index index.html index.htm;
        try_files $uri /index.html;

    }

    location /api {
        proxy_pass http://localhost:3001/;
        proxy_set_header X-Real-IP $remote_addr;
    }
    location /backend4/ {
        proxy_pass http://node_app4/;
        proxy_set_header X-Real-IP $remote_addr;
    }


}
server {
    charset utf-8;
    server_name paahs1.krushal.in www.paahs1.krushal.in;
    client_max_body_size 200M;
    listen 443;
    ssl on;
    ssl_certificate /etc/nginx/ssl/signed.cert.chain.crt;
    ssl_certificate_key /etc/nginx/ssl/domain.key;


    location / {

        root /usr/share/nginx/html/app2;
        index index.html index.htm;
        try_files $uri /index.html;

    }
    location /api {
        proxy_pass http://localhost:3001/;
        proxy_set_header X-Real-IP $remote_addr;
    }
    location /backend4/ {
        proxy_pass http://node_app4/;
        proxy_set_header X-Real-IP $remote_addr;
    }


}
=======
# server {
#     listen 80;
#     listen [::]:80;

#     server_name localhost;

#     error_page 497 https://$host:$server_port$request_uri;
# }



# server {
#   listen 80;
#   listen 443 ssl;
#   server_name ahs1.krushal.in www.ahs1.krushal.in;
#   ssl_certificate /etc/nginx/ssl/signed.cert.chain.crt;
#   ssl_certificate_key /etc/nginx/ssl/domain.key;
#   error_page 497 https://$host:$server_port$request_uri;


#   location / {
#   root   /usr/share/nginx/html;
#   index  index.html index.htm;
#   try_files $uri /index.html; 
#   }
#    location /backend/ {
#    proxy_pass http://localhost:3001/;
#   #  proxy_http_version 1.1;
#   #  proxy_set_header Upgrade $http_upgrade;
#   #  proxy_set_header Connection 'upgrade';
#   #  proxy_set_header Host $host;
#   #  proxy_cache_bypass $http_upgrade;

#   }


#   error_page   500 502 503 504  /50x.html;

#   location = /50x.html {
#     root   /usr/share/nginx/html;
#   }
  


# }
 upstream node_app {
        server 0.0.0.0:3001;
    }
 upstream node_app2 {
        server localhost:3001;
    }
 upstream node_app1 {
        server **************;
    }
 upstream node_app4 {
        server **************;
    }



server {
        charset utf-8;
      server_name ahs1.krushal.in www.ahs1.krushal.in;
        client_max_body_size 200M;
        
        listen 443;
        ssl on;
          ssl_certificate /etc/nginx/ssl/signed.cert.chain.crt;
  ssl_certificate_key /etc/nginx/ssl/domain.key;

         
        location / {
                # First attempt to serve request as file, then
                # as directory, then fall back to displaying a 404.
                root   /usr/share/nginx/html;
  index  index.html index.htm;
  try_files $uri /index.html; 
  
        }

         location /backend/{
              proxy_pass http://node_app/;
              proxy_set_header X-Real-IP $remote_addr;
   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
   proxy_set_header X-Forwarded-Proto $scheme;
   proxy_set_header Host $http_host;
   proxy_set_header X-NginX-Proxy true;
    proxy_redirect off;
    proxy_read_timeout 5m;
    proxy_connect_timeout 5m;
    proxy_pass_header Set-Cookie;
   
    proxy_buffer_size   128k;
    proxy_buffers   4 256k;
    proxy_busy_buffers_size   256k;
   
         }
         location /backend1/{
              proxy_pass http://node_app1/;
              proxy_set_header X-Real-IP $remote_addr;
   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
   proxy_set_header X-Forwarded-Proto $scheme;
   proxy_set_header Host $http_host;
   proxy_set_header X-NginX-Proxy true;
   proxy_redirect off;
    proxy_read_timeout 5m;
    proxy_connect_timeout 5m;
    proxy_pass_header Set-Cookie;
    proxy_redirect off;
    proxy_buffer_size   128k;
    proxy_buffers   4 256k;
    proxy_busy_buffers_size   256k;

         }
           location /backend4/{
              proxy_pass http://node_app4/;
              proxy_set_header X-Real-IP $remote_addr;
         }
           location /backend5/{
              proxy_pass http://node_app2/;
              proxy_set_header X-Real-IP $remote_addr;
         }
        
}
server {
        if ($host = www.ahs1.krushal.in) {
           return 301 https://$host$request_uri;
        }


        if ($host = ahs1.krushal.in) {
           return 301 https://$host$request_uri;
        }
        listen 80 default_server;
          listen [::]:80 default_server;
          server_name www.ahs1krushal.in ahs1.krushal.in;
           return 404;
           
}
>>>>>>> 89b85a6dccdca02a24c5fcf12d5faa78cf5200ee
