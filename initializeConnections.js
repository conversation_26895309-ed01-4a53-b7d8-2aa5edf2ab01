// const dotEnv = require('dotenv')
// dotEnv.config()

/*const dbModels = require('./db/models')
const postGresDB = require('./server.config')
const sqliteDB = require('./mobile.config')

postGresDB.entities = [...dbModels.main.models] */
/*sqliteDB.entities = [ new EntitySchema(student) 
] */

// console.log('process.env = ', process.env)

/*
  "host": "localhost", 
  "port": 5432, 
  "username": "krushallocal", 
  "password": "krushallocal", 
  "database": "krushallocal",
*/

/*
const { loadConfiguration, configurationJSON } = require('@krushal-it/common-core')
loadConfiguration(process.env.ENV_JSON)
const {dbConnections, initializeAllRepositories} = require('./utils/connectionHelper')
initializeAllRepositories(dbConnections()).then(() => {
  console.log('dbConnections.main = ', dbConnections().main)
  console.log('dbConnections.main.manager = ', dbConnections().main.manager)
  dbConnections().main.manager.query('select * from main.staff').then((dbResult) => {
  console.log('dbResult = ', dbResult)})
})
*/

const { loadConfiguration, configurationJSON } = require('@krushal-it/common-core')
const { dbConnections, getDBConnections, initializeAllRepositories } = require('./utils/connectionHelper')
const { In } = require('typeorm')
const fs = require('fs');

const envJson = '{"IS_SERVER":"False","DB_TYPE":"POSTGRES"}'

;(async () => {
  loadConfiguration(envJson)
  await initializeAllRepositories(dbConnections())
  const clientDelta = []
  const dbSpecificClientDelta = []
  //for (const key of Object.keys(global.dbConnections.main.repos)) {
    for (const key of global.dbConnections.main.syncConfiguration.two_way_sync_tables) {
    const notSyncedRecordsForTable = await global.dbConnections.main.repos[key].find({
      where: {
        data_sync_status: In([1000101002, 1000101003]),
      },
    })
    const primaryKey = global.dbConnections.main.entities[key].primaryColumns[0].propertyName
    let processed1NotSyncedRecordsForTable
    if (key === 'document') {
      processed1NotSyncedRecordsForTable = notSyncedRecordsForTable.map(({ created_at, updated_at, client_document_information, document_sync_status, data_sync_status, ...rest }) => rest)
    } else {
      processed1NotSyncedRecordsForTable = notSyncedRecordsForTable.map(({ created_at, updated_at, data_sync_status, ...rest }) => rest)
    }
    if (processed1NotSyncedRecordsForTable.length > 0) {
      dbSpecificClientDelta.push({
        table_name: key,
        table_delta: processed1NotSyncedRecordsForTable,
      })
    }
  }

  if (dbSpecificClientDelta.length > 0) {
    clientDelta.push({
      database_name: 'main',
      database_delta: dbSpecificClientDelta,
    })
  }

  console.log('clientDelta', clientDelta)
  for(const d of clientDelta[0].database_delta) {
    console.log(d.table_name, d.table_delta.length)
  }
  
  const output = {}
  output.sync_reference_time = new Date().toISOString();
  output.client_delta = clientDelta;
  try {
    fs.writeFileSync('output.json', JSON.stringify(output));
    console.log('File written successfully');
} catch (err) {
    console.error('Error writing file', err);
}

})()
