const { DataSource } = require("typeorm");
const {loadConfiguration, configurationJSON} = require('@krushal-it/common-core')
console.log('torm d d mm 1')
let configuration
try {
  if (process.env.FORCE_LOAD_CONFIGURATION === 'True') {
    if (process.env.ENV_JSON) {
      loadConfiguration(process.env.ENV_JSON)
    } else {
      throw new Error('No configuration to load')
    }
  }
  configuration = configurationJSON()
} catch (error) {
  console.log('Configuration not available')
  throw error
}

console.log('torm d d mm 2')
const mobileDatabaseConfigurations = require('../configs/mobile.databases.config')
const dbModels = require('../models')
const sqliteMainDB = mobileDatabaseConfigurations.sqliteMain
sqliteMainDB.entities = [...dbModels.main.models] 
if (process.env.IS_SERVER === 'False' && process.env.GENERATE_MOBILE_MIGRATION_ON_SERVER === 'True' && sqliteMainDB.type === 'react-native') {
    sqliteMainDB.type = "sqlite"
}

const mobileMainDS = new DataSource(sqliteMainDB)
exports.default = mobileMainDS
