const { DataSource } = require("typeorm")
const {loadConfiguration, configurationJSON} = require('@krushal-it/common-core')
// const { SQLite } = require('sqlite3')
const serverDatabaseConfigurations = require('../configs/server.databases.config')
const dbModels = require('../models')
let serverConfigDS
let configuration
try {
  if (process.env.FORCE_LOAD_CONFIGURATION === 'True') {
    if (process.env.ENV_JSON) {
      loadConfiguration(process.env.ENV_JSON)
    } else {
      throw new Error('No configuration to load')
    }
  }
  configuration = configurationJSON()
} catch (error) {
  console.log('Configuration not available')
  throw error
}

if (process.env.DB_TYPE === 'POSTGRES') {
  const postGresConfigDB = serverDatabaseConfigurations.postGresConfig
  if (configuration && configuration.DB_VARIABLES && configuration.DB_VARIABLES.POSTGRES_CONFIG) {
    if (configuration.DB_VARIABLES.POSTGRES_CONFIG.HOST) {
      postGresConfigDB.host = configuration.DB_VARIABLES.POSTGRES_CONFIG.HOST
    }
    if (configuration.DB_VARIABLES.POSTGRES_CONFIG.PORT) {
      postGresConfigDB.port = configuration.DB_VARIABLES.POSTGRES_CONFIG.PORT
    }
    if (configuration.DB_VARIABLES.POSTGRES_CONFIG.USER_NAME) {
      postGresConfigDB.username = configuration.DB_VARIABLES.POSTGRES_CONFIG.USER_NAME
    }
    if (configuration.DB_VARIABLES.POSTGRES_CONFIG.PASSWORD) {
      postGresConfigDB.password = configuration.DB_VARIABLES.POSTGRES_CONFIG.PASSWORD
    }
    if (configuration.DB_VARIABLES.POSTGRES_CONFIG.DATABASE) {
      postGresConfigDB.database = configuration.DB_VARIABLES.POSTGRES_CONFIG.DATABASE
    }
    /*
     If the orm logging configuration is present in env file, use that 
     else 
     fall back to the setting specified in databases.config file  
    */
     if (configuration.DB_VARIABLES.POSTGRES_CONFIG.LOGGING) {
      postGresConfigDB.logging = configuration.DB_VARIABLES.POSTGRES_CONFIG.LOGGING
    }

  }  
  postGresConfigDB.entities = [...dbModels.config.models] 
  serverConfigDS = new DataSource(postGresConfigDB)
} else if (process.env.DB_TYPE === 'SQLITE') {
  const sqliteConfigDB = serverDatabaseConfigurations.sqliteConfig
  sqliteConfigDB.entities = [...dbModels.config.models] 
  serverConfigDS = new DataSource(sqliteConfigDB)
}
// module.exports = {dataSources}
exports.default = serverConfigDS
