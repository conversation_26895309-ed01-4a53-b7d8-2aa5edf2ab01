const { DataSource } = require("typeorm")
const {loadConfiguration, configurationJSON} = require('@krushal-it/common-core')
//const SQLite = require('sqlite3')

const serverDatabaseConfigurations = require('../configs/server.databases.config')
const dbModels = require('../models')
let serverLocationDS
let configuration
try {
  if (process.env.FORCE_LOAD_CONFIGURATION === 'True') {
    if (process.env.ENV_JSON) {
      loadConfiguration(process.env.ENV_JSON)
    } else {
      throw new Error('No configuration to load')
    }
  }
  configuration = configurationJSON()
} catch (error) {
  console.log('Configuration not available')
  throw error
}
if (process.env.DB_TYPE === 'POSTGRES') {
  const postGresLocationDB = serverDatabaseConfigurations.postGresLocation
  if (configuration && configuration.DB_VARIABLES && configuration.DB_VARIABLES.POSTGRES_LOCATION) {
    if (configuration.DB_VARIABLES.POSTGRES_LOCATION.HOST) {
      postGresLocationDB.host = configuration.DB_VARIABLES.POSTGRES_LOCATION.HOST
    }
    if (configuration.DB_VARIABLES.POSTGRES_LOCATION.PORT) {
      postGresLocationDB.port = configuration.DB_VARIABLES.POSTGRES_LOCATION.PORT
    }
    if (configuration.DB_VARIABLES.POSTGRES_LOCATION.USER_NAME) {
      postGresLocationDB.username = configuration.DB_VARIABLES.POSTGRES_LOCATION.USER_NAME
    }
    if (configuration.DB_VARIABLES.POSTGRES_LOCATION.PASSWORD) {
      postGresLocationDB.password = configuration.DB_VARIABLES.POSTGRES_LOCATION.PASSWORD
    }
    if (configuration.DB_VARIABLES.POSTGRES_LOCATION.DATABASE) {
      postGresLocationDB.database = configuration.DB_VARIABLES.POSTGRES_LOCATION.DATABASE
    }
    /*
     If the orm logging configuration is present in env file, use that 
     else 
     fall back to the setting specified in databases.config file  
    */
     if (configuration.DB_VARIABLES.POSTGRES_LOCATION.LOGGING) {
      postGresLocationDB.logging = configuration.DB_VARIABLES.POSTGRES_LOCATION.LOGGING
    }

  }  
  postGresLocationDB.entities = [...dbModels.location.models] 
  serverLocationDS = new DataSource(postGresLocationDB)
} else if (process.env.DB_TYPE === 'SQLITE') {
  const sqliteLocationDB = serverDatabaseConfigurations.sqliteLocation
  sqliteLocationDB.entities = [...dbModels.location.models] 
  serverLocationDS = new DataSource(sqliteLocationDB)
}
exports.default = serverLocationDS
