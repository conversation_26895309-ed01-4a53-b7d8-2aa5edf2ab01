const { DataSource } = require("typeorm");
const mobileDatabaseConfigurations = require('../configs/mobile.databases.config')
const dbModels = require('../models')

const sqliteConfigDB = mobileDatabaseConfigurations.sqliteConfig
sqliteConfigDB.entities = [...dbModels.config.models] 
if (process.env.IS_SERVER === 'False' && process.env.GENERATE_MOBILE_MIGRATION_ON_SERVER === 'True' && sqliteConfigDB.type === 'react-native') {
    sqliteConfigDB.type = "sqlite"
}
const mobileConfigDS = new DataSource(sqliteConfigDB)
// module.exports = {dataSources}
exports.default = mobileConfigDS
