const { DataSource } = require("typeorm")
const {loadConfiguration, configurationJSON} = require('@krushal-it/common-core')

let serverMainDS
let configuration
try {
  if (process.env.FORCE_LOAD_CONFIGURATION === 'True') {
    if (process.env.ENV_JSON) {
      loadConfiguration(process.env.ENV_JSON)
    } else {
      throw new Error('No configuration to load')
    }
  }
  configuration = configurationJSON()
} catch (error) {
  console.log('Configuration not available')
  throw error
}
const serverDatabaseConfigurations = require('../configs/server.databases.config')
const dbModels = require('../models')
if (process.env.DB_TYPE === 'POSTGRES') {
  const postGresMainDB = serverDatabaseConfigurations.postGresMain
  if (configuration && configuration.DB_VARIABLES && configuration.DB_VARIABLES.POSTGRES_MAIN) {
    if (configuration.DB_VARIABLES.POSTGRES_MAIN.HOST) {
      postGresMainDB.host = configuration.DB_VARIABLES.POSTGRES_MAIN.HOST
    }
    if (configuration.DB_VARIABLES.POSTGRES_MAIN.PORT) {
      postGresMainDB.port = configuration.DB_VARIABLES.POSTGRES_MAIN.PORT
    }
    if (configuration.DB_VARIABLES.POSTGRES_MAIN.USER_NAME) {
      postGresMainDB.username = configuration.DB_VARIABLES.POSTGRES_MAIN.USER_NAME
    }
    if (configuration.DB_VARIABLES.POSTGRES_MAIN.PASSWORD) {
      postGresMainDB.password = configuration.DB_VARIABLES.POSTGRES_MAIN.PASSWORD
    }
    if (configuration.DB_VARIABLES.POSTGRES_MAIN.DATABASE) {
      postGresMainDB.database = configuration.DB_VARIABLES.POSTGRES_MAIN.DATABASE
    }
    /*
     If the orm logging configuration is present in env file, use that 
     else 
     fall back to the setting specified in databases.config file  
    */
    if (configuration.DB_VARIABLES.POSTGRES_MAIN.LOGGING) {
      postGresMainDB.logging = configuration.DB_VARIABLES.POSTGRES_MAIN.LOGGING
    }

  }  
  postGresMainDB.entities = [...dbModels.main.models] 
  serverMainDS = new DataSource(postGresMainDB)
} else if (process.env.DB_TYPE === 'SQLITE') {
  const sqliteMainDB = serverDatabaseConfigurations.sqliteMain
  sqliteMainDB.entities = [...dbModels.main.models] 
  serverMainDS = new DataSource(sqliteMainDB)
}
exports.default = serverMainDS
