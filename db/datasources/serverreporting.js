const { DataSource } = require("typeorm")
const {loadConfiguration, configurationJSON} = require('@krushal-it/common-core')
const serverDatabaseConfigurations = require('../configs/server.databases.config')
const dbModels = require('../models')
let serverReportingDS
let configuration
try {
  if (process.env.FORCE_LOAD_CONFIGURATION === 'True') {
    if (process.env.ENV_JSON) {
      loadConfiguration(process.env.ENV_JSON)
    } else {
      throw new Error('No configuration to load')
    }
  }
  configuration = configurationJSON()
} catch (error) {
  console.log('Configuration not available')
  throw error
}

if (process.env.DB_TYPE === 'POSTGRES') {
  const postGresReportingDB = serverDatabaseConfigurations.postGresReporting
  if (configuration && configuration.DB_VARIABLES && configuration.DB_VARIABLES.POSTGRES_REPORTING) {
    if (configuration.DB_VARIABLES.POSTGRES_REPORTING.HOST) {
      postGresReportingDB.host = configuration.DB_VARIABLES.POSTGRES_REPORTING.HOST
    }
    if (configuration.DB_VARIABLES.POSTGRES_REPORTING.PORT) {
      postGresReportingDB.port = configuration.DB_VARIABLES.POSTGRES_REPORTING.PORT
    }
    if (configuration.DB_VARIABLES.POSTGRES_REPORTING.USER_NAME) {
      postGresReportingDB.username = configuration.DB_VARIABLES.POSTGRES_REPORTING.USER_NAME
    }
    if (configuration.DB_VARIABLES.POSTGRES_REPORTING.PASSWORD) {
      postGresReportingDB.password = configuration.DB_VARIABLES.POSTGRES_REPORTING.PASSWORD
    }
    if (configuration.DB_VARIABLES.POSTGRES_REPORTING.DATABASE) {
      postGresReportingDB.database = configuration.DB_VARIABLES.POSTGRES_REPORTING.DATABASE
    }
    /*
     If the orm logging configuration is present in env file, use that 
     else 
     fall back to the setting specified in databases.config file  
    */
     if (configuration.DB_VARIABLES.POSTGRES_REPORTING.LOGGING) {
      postGresReportingDB.logging = configuration.DB_VARIABLES.POSTGRES_REPORTING.LOGGING
    }

  }  
  postGresReportingDB.entities = [...dbModels.reporting.models] 
  serverReportingDS = new DataSource(postGresReportingDB)
}
// module.exports = {dataSources}
exports.default = serverReportingDS
