const {loadConfiguration, configurationJSON} = require('@krushal-it/common-core')
const { DataSource } = require("typeorm")
// const { SQLite } = require('sqlite3')
const serverDatabaseConfigurations = require('../configs/server.databases.config')
serverDatabaseConfigurations
// const dbModels = require('../models')
let serverCommonDS
let configuration
try {
  if (process.env.FORCE_LOAD_CONFIGURATION === 'True') {
    if (process.env.ENV_JSON) {
      loadConfiguration(process.env.ENV_JSON)
    } else {
      throw new Error('No configuration to load')
    }
  }
  configuration = configurationJSON()
} catch (error) {
  console.log('Configuration not available')
  throw error
}
if (process.env.DB_TYPE === 'POSTGRES') {
  const postGresCommonDB = serverDatabaseConfigurations.postGresCommon
  if (configuration && configuration.DB_VARIABLES && configuration.DB_VARIABLES.POSTGRES_COMMON) {
    if (configuration.DB_VARIABLES.POSTGRES_COMMON.HOST) {
      postGresCommonDB.host = configuration.DB_VARIABLES.POSTGRES_COMMON.HOST
    }
    if (configuration.DB_VARIABLES.POSTGRES_COMMON.PORT) {
      postGresCommonDB.port = configuration.DB_VARIABLES.POSTGRES_COMMON.PORT
    }
    if (configuration.DB_VARIABLES.POSTGRES_COMMON.USER_NAME) {
      postGresCommonDB.username = configuration.DB_VARIABLES.POSTGRES_COMMON.USER_NAME
    }
    if (configuration.DB_VARIABLES.POSTGRES_COMMON.PASSWORD) {
      postGresCommonDB.password = configuration.DB_VARIABLES.POSTGRES_COMMON.PASSWORD
    }
    if (configuration.DB_VARIABLES.POSTGRES_COMMON.DATABASE) {
      postGresCommonDB.database = configuration.DB_VARIABLES.POSTGRES_COMMON.DATABASE
    }
    /*
     If the orm logging configuration is present in env file, use that 
     else 
     fall back to the setting specified in databases.config file  
    */
     if (configuration.DB_VARIABLES.POSTGRES_COMMON.LOGGING) {
      postGresCommonDB.logging = configuration.DB_VARIABLES.POSTGRES_COMMON.LOGGING
    }

  }
  serverCommonDS = new DataSource(postGresCommonDB)
}
// module.exports = {dataSources}
exports.default = serverCommonDS