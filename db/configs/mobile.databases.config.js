const { configurationJSON } = require('@krushal-it/common-core')
const { mainMobileMigrations, configMobileMigrations } = require('../migrations/mobile')

const sqliteMain = {
  name: 'main',
  execute_updated_at_triggers: true,
  type: 'sqlite',
  database: 'main.db',
  location: 'main',
  synchronize: false,
  migrationsRun: true,
  logging: configurationJSON().SUPPRESS_ORM_ERRORS === 'True' ? false : true,
  migrations: [...mainMobileMigrations],
  migrationsTableName: 'migrations-main-mobile',
}

const sqliteConfig = {
  name: 'config',
  execute_updated_at_triggers: true,
  type: 'sqlite',
  database: 'config.db',
  location: 'config',
  synchronize: false,
  migrationsRun: true,
  logging: configurationJSON().SUPPRESS_ORM_ERRORS === 'True' ? false : true,
  migrations: [...configMobileMigrations],
  migrationsTableName: 'migrations-config-mobile',
}

module.exports = { sqliteMain, sqliteConfig }
