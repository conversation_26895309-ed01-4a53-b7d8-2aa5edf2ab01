const {mainMobileMigrations, configMobileMigrations} = require('../migrations/mobile')

const postGresCommon = {
  "name": "common",
  "type": "postgres", 
  "host": "localhost", 
  "port": 5432, 
  "username": "krushallocal", 
  "password": "krushallocal", 
  "database": "krushallocal",
  "synchronize": false, 
  "logging": true,
  "migrations": ["db/migrations/server/common/*.js"],
  "migrationsTableName": "migrations-common"
}

const postGresMain = {
  "name": "main",
  "execute_updated_at_triggers": true,
  "type": "postgres", 
  "host": "localhost", 
  "port": 5432, 
  "username": "krushallocal", 
  "password": "krushallocal", 
  "database": "krushallocal",
  "synchronize": false, 
  "logging": true,
  "migrations": ["db/migrations/server/main/*.js"],
  "migrationsTableName": "migrations-main"
}

const postGresLocation = {
  "name": "location",
  "execute_updated_at_triggers": true,
  "type": "postgres", 
  "host": "localhost", 
  "port": 5432, 
  "username": "krushallocal", 
  "password": "krushallocal", 
  "database": "krushallocal",
  "synchronize": false, 
  "logging": true,
  "migrations": ["db/migrations/server/location/*.js"],
  "migrationsTableName": "migrations-location"
}


const postGresConfig = {
  "name": "config",
  "execute_updated_at_triggers": true,
  "type": "postgres", 
  "host": "localhost", 
  "port": 5432, 
  "username": "krushallocal", 
  "password": "krushallocal", 
  "database": "krushallocal",
  "synchronize": false, 
  "logging": true,
  "migrations": ["db/migrations/server/config/*.js"],
  "migrationsTableName": "migrations-config"
}

const postGresReporting = {
  "name": "reporting",
  "execute_updated_at_triggers": true,
  "type": "postgres", 
  "host": "localhost", 
  "port": 5432, 
  "username": "krushallocal", 
  "password": "krushallocal", 
  "database": "krushallocal",
  "synchronize": false, 
  "logging": true,
  "migrations": ["db/migrations/server/reporting/*.js"],
  "migrationsTableName": "migrations-reporting"
}

const sqliteMain = {
  "name": "main", 
  "execute_updated_at_triggers": true,
  "type": "sqlite", 
  "database": "mainv1.db",
  "location": "main",
  "synchronize": true, 
  "logging": true,
  "migrations": [...mainMobileMigrations],
  "migrationsTableName": "migrations-main-sqlite"
}

const sqliteConfig = {
  "name": "config", 
  "execute_updated_at_triggers": true,
  "type": "sqlite", 
  "database": "configv1.db",
  "location": "config",
  "synchronize": false, 
  "logging": true,
  "migrations": [...configMobileMigrations],
  "migrationsTableName": "migrations-config-sqlite"
}

module.exports = { postGresCommon, postGresMain, postGresConfig, postGresReporting, postGresLocation, sqliteMain, sqliteConfig}