const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForDocumentSyncStatus, defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const ocorderpayment = { 
  "name": "OcOrderPayment",
  "target": "oc_order_payment",
  "schema": "main",
  "columns": { 
    "oc_order_payment_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_oc_order_payment_uuid"
    }, 
    "oc_order_uuid": { 
      "type": "uuid",
      "nullable": true,
    },
    "entity_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_entity_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "entity_entity_id": { 
      "type": "int",
      "nullable": true
    },
    "payment_amount": { 
      "type": "decimal",
      "precision": 14,
      "scale": 4,
      "nullable": true
    }, 
    "oc_pricing_attribute_id": { 
      "type": "int",
      "nullable": true
    },
    "payment_date": {
      "type": timestampColumn(),
      "nullable": true
    },
    "order_payment_information": {
      "type": jsonColumn(),
      "nullable": true
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  },
}

const OcOrderPayment = new EntitySchema(ocorderpayment)

module.exports = { OcOrderPayment }