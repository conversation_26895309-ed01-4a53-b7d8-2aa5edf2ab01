const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema

const teacher = { 
  "name": "Teacher", 
  "target": "teacher",
  "schema": "main",
  "columns": { 
    "id": { 
      "primary": true, 
      "type": "int", 
      "generated": true 
    }, 
    "name": { 
      "type": "varchar" 
    }, 
    "teaching_experience_2": { 
      "type": "integer" 
    },
    "teaching_experience": { 
      "type": "integer" 
    } 
  } 
}

const Teacher = new EntitySchema(teacher)

module.exports = { Teacher }
