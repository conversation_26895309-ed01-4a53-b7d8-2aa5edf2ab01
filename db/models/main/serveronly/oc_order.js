const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const ocorder = { 
  "name": "OcOrder",
  "target": "oc_order",
  "schema": "main",
  "columns": { 
    "oc_order_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_oc_order_uuid"
    }, 
    "order_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "order_category_id": { 
      "type": "int",
      "nullable": true
    }, 
    "buyer_entity_type_id": { 
      "type": "int",
      "nullable": true
    },
    "buyer_entity_type_id": { 
      "type": "int",
      "nullable": true
    },
    "buyer_entity_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "order_grammar_id": { 
      "type": "int",
      "nullable": true
    },
    "order_information": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  },
}

const OcOrder = new EntitySchema(ocorder)

module.exports = { OcOrder }