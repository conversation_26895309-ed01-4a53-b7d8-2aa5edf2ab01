const { Teacher } = require('./teacher')
const {Job<PERSON>raker} = require("./job_tracker")
const { UserDevice } = require('./user_device');
const { AnimalClassificationHistory } = require('./animal_classification_history')
const {Translate} = require('./translate')
const {OcItem} = require('./oc_item')
const {OcItemClassification} = require('./oc_item_classification')
const {OcItemListing} = require('./oc_item_listing')
const {OcItemDocument} = require('./oc_item_document')
const {OcOrder} = require('./oc_order')
const {OcOrderDocument} = require('./oc_order_document')
const {OcOrderPayment} = require('./oc_order_payment')


const models = [UserDevice, JobTraker,Translate,
    OcItem, OcItemClassification, OcItemListing, OcItemDocument,
    Oc<PERSON>rder, Oc<PERSON>rderDocument, Oc<PERSON>rderPayment,
    AnimalClassificationHistory ]
module.exports = { models }