const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const ocitem = { 
  "name": "OcItem",
  "target": "oc_item",
  "schema": "main",
  "columns": { 
    "oc_item_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_oc_item_uuid"
    }, 
    "oc_item_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "oc_item_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "linked_entity_type_id": { 
      "type": "int",
      "nullable": true
    },
    "linked_entity_id": { 
      "type": "int",
      "nullable": true
    },
    "linked_entity_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "inventory_details_id": { 
      "type": "int",
      "nullable": true
    },
    "item_information": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  },
}

const OcItem = new EntitySchema(ocitem)

module.exports = { OcItem }