const typeorm = require("typeorm");
const { defaultValueForUUIDPrimaryKey } = require("../../../../utils/ormHelper");
const EntitySchema = typeorm.EntitySchema;

const translate = {
    name: "Translate",
    target: "translate",
    schema: "main",
    columns: {
        translate_id: {
            primary: true,
            type: "uuid",
            default: () => defaultValueForUUIDPrimaryKey(),
            primaryKeyConstraintName: "PK_translate_id"
        },
        text_id: {
            type: "uuid",
            default: () => defaultValueForUUIDPrimaryKey(),
        },
        language: {
            type: "varchar",
            length: 5
        },
        text: {
            type: "varchar",
            length: 2000
        }
    }
};

const Translate = new EntitySchema(translate);
module.exports = Translate;