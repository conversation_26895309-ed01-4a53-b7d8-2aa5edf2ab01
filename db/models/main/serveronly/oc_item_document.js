const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimary<PERSON>ey, defaultValueForCreatedAt, defaultValueForDocumentSyncStatus, defaultValueForDataSyncStatus, defaultEmptyJSONValue, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const ocitemdocument = { 
  "name": "OcItemDocument", 
  "target": "oc_item_document",
  "schema": "main",
  "columns": { 
    "oc_item_document_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_oc_item_document_uuid"
    }, 
    "oc_item_listing_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "document_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "document_type_id": { 
      "type": "int",
    }, 
    "document_status_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_1_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_1_entity_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "entity_1_entity_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_2_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_2_entity_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "entity_2_entity_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_3_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_3_entity_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "entity_3_entity_id": { 
      "type": "int",
      "nullable": true
    }, 
    "client_document_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "document_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "document_meta_data_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "document_sync_status": { 
      "type": "int",        
      "default": defaultValueForDocumentSyncStatus()
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  }
}

const OcItemDocument = new EntitySchema(ocitemdocument)

module.exports = { OcItemDocument }