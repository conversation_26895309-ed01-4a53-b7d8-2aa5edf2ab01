const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultTimestampColumn, defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const ocitemlisting = { 
  "name": "OcItemListing",
  "target": "oc_item_listing",
  "schema": "main",
  "columns": { 
    "oc_item_listing_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_oc_item_listing_uuid"
    }, 
    "listing_item_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "listing_item_id": { 
      "type": "int",
      "nullable": true
    }, 
    "listing_item_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "payment_term_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "item_pricing_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "start_date": { 
      "type": timestampColumn(),
      "nullable": true,
      "default": () => defaultTimestampColumn('1900-01-01 00:00:01'),
    },
    "end_date": { 
      "type": timestampColumn(),
      "nullable": true,
      "default": () => defaultTimestampColumn("2099-12-31 23:59:59")
    },
    "item_listing_information": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  },
}

const OcItemListing = new EntitySchema(ocitemlisting)

module.exports = { OcItemListing }