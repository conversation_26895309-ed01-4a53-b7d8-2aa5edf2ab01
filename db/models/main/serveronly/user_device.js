const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const userdevice = { 
  "name": "UserDevice", 
  "target": "user_device",
  "schema": "main",
  "columns": { 
    "user_device_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_user_device_id"
    }, 
    "entity_type_id": { 
      "type": "int",
    }, 
    "entity_uuid": { 
      "type": "uuid",
    }, 
    "uid": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "instance_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "device_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "fcm_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "device_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }
  }
}

const UserDevice = new EntitySchema(userdevice)

module.exports = { UserDevice }
