const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimary<PERSON>ey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const animalclassificationhistory = { 
  "name": "AnimalClassificationHistory", 
  "target": "animal_classification_history",
  "schema": "main",
  "columns": { 
    "animal_classification_history_id": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_animal_classification_history_id"
    },
    "animal_classification_id": { 
      "type": "uuid",
    }, 
    "animal_id": { 
      "type": "uuid",
      "nullable": true
    }, 
    "classifier_id": { 
      "type": "int",
      "nullable": true
    }, 
    "temporal_entity_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "temporal_entity_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "temporal_entity_id": { 
      "type": "int",
      "nullable": true
    }, 
    "value_int": { 
      "type": "int",
      "nullable": true
    }, 
    "value_reference_id": { 
      "type": "int",
      "nullable": true
    }, 
    "value_reference_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "value_string_256": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "value_string_2000": { 
      "type": "varchar",
      "length": 2000,
      "nullable": true
    }, 
    "value_double": { 
      "type": "decimal",
      "precision": 14,
      "scale": 4,
      "nullable": true
    }, 
    "value_date": { 
      "type": timestampColumn(),
      "nullable": true
    }, 
    "value_json": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "value_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "value_string_256_encrypted": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "value_string_2000_encrypted": { 
      "type": "varchar",
      "length": 2000,
      "nullable": true
    }, 
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "source_created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt(),
            "nullable": false

    }, 
    "source_updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt(),
      "nullable": false
    }, 

    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  } 
}

const AnimalClassificationHistory = new EntitySchema(animalclassificationhistory)

module.exports = { AnimalClassificationHistory }
