const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForDocumentSyncStatus, defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const ocorderdocument = { 
  "name": "OcOrderDocument",
  "target": "oc_order_document",
  "schema": "main",
  "columns": { 
    "oc_order_document_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_oc_order_document_uuid"
    },
    "oc_order_uuid": { 
      "type": "uuid",
      "nullable": true,
    },
    "document_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "document_type_id": { 
      "type": "int",
      "nullable": true,
    },
    "document_status_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_1_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_1_entity_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "entity_1_entity_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_2_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_2_entity_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "entity_2_entity_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_3_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_3_entity_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "entity_3_entity_id": { 
      "type": "int",
      "nullable": true
    }, 
    "client_document_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "document_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "document_meta_data_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "document_sync_status": { 
      "type": "int",        
      "default": defaultValueForDocumentSyncStatus()
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  },
}

const OcOrderDocument = new EntitySchema(ocorderdocument)

module.exports = { OcOrderDocument }