const typeorm = require("typeorm");
const EntitySchema = typeorm.EntitySchema;
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper');

const job_tracker = {
    "name": "JobTracker",
    "target": "job_tracker",
    "schema":"main",
    "columns": {
        "job_tracker_id": {
            "primary": true,
            "type": "uuid",
            "default": () => defaultValueForUUIDPrimaryKey(),
            "primaryKeyConstraintName": "PK_job_tracker_id"
        },
        "job_type": {
            "type": "varchar",
            "length": 256
        },
        "job_status": {
            "type": "varchar",
            "default": "PENDING",
        },
        "job_completed_at": {
            "type": timestampColumn(),
            "nullable": true,
        },
        "job_information": {
            "type": jsonColumn(),
            "nullable": true,
        },
        "created_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),

        },
        "updated_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt()
        },
    },
};

const JobTraker = new EntitySchema(job_tracker);
module.exports = { JobTraker };
