const typeorm = require('typeorm')
const EntitySchema = typeorm.EntitySchema
const {
    defaultValueForUUIDPrimaryKey,
    defaultValueForDataSyncStatus,
    defaultValueForCreatedAt,
    jsonColumn,
    timestampColumn,
} = require('../../../../utils/ormHelper')

const dumpDoc = {
    "name": 'DumpDoc',
    "target": 'dump_doc',
    "schema": 'main',
    "columns": {
        "dump_document_id": {
            "primary": true,
            "type": 'uuid',
            "default": () => defaultValueForUUIDPrimaryKey(),
            "primaryKeyConstraintName": 'PK_dump_document_id',
        },
        "document_key": {
            "type": 'varchar',
        },
        "error": {
            "type": jsonColumn(),
            "nullable": true,
        },
        "created_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),
        },
        "updated_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),
        },
    },
}
const DumpDoc = new EntitySchema(dumpDoc)

module.exports = { DumpDoc }