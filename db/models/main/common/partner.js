const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const partner = { 
  "name": "Partner", 
  "target": "partner",
  "schema": "main",
  "columns": { 
    "partner_id": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_partner_id"
    }, 
    "partner_type_id": { 
      "type": "int",
      "nullable":true
    }, 
    "partner_visual_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "partner_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "mobile_number": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "email_address": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  }       

}

const Partner = new EntitySchema(partner)

module.exports = { Partner }