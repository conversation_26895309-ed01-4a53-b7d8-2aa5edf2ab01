const typeorm = require('typeorm')
const EntitySchema = typeorm.EntitySchema
const {
    defaultValueForUUIDPrimary<PERSON>ey,
    defaultValueForCreatedAt,
    defaultValueForDataSyncStatus,
    jsonColumn,
    timestampColumn,
} = require('../../../../utils/ormHelper')

const ref_diagnosis = {
    "name": "ReferenceDiagnosis",
    "target": "ref_diagnosis",
    "schema": "main",
    "columns": {
        "reference_id": {
            "primary": true,
            "type": "int",
            "generated": true,
            "primaryKeyConstraintName": "PK_ref_diagnosis_id",
        },
        "name": {
            "type": "varchar",
        },
        "category": {
            "type": "int",
        },
        "reference_name_l10n": {
            "type": jsonColumn(),
            "nullable": true,
        },
        "reference_information": {
            "type": jsonColumn(),
            "nullable": true,
        },
        "active": {
            "type": "int",
            "default": 1000100001,
        },
        "last_modifying_user_id": {
            "type": "uuid",
            "nullable": true,
        },
        "correlation_id": {
            "type": "varchar",
            "length": 256,
            "nullable": true,
        },
        "data_sync_status": {
            "type": "int",
            "default": defaultValueForDataSyncStatus(),
        },
        "created_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),
        },
        "updated_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),
        },
    },
}
const ReferenceDiagnosis = new EntitySchema(ref_diagnosis)

module.exports = { ReferenceDiagnosis }