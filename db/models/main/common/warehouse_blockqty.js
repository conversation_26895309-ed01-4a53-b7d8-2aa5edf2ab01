const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, defaultValueForDataSyncStatus, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const warehouseblockqty= { 
    "name": "WarehouseBlockQty", 
    "target": "warehouse_blockqty",
    "schema": "main",
    "columns": { 
      "warehouse_blockqty_id": { 
        "primary": true, 
        "type": "uuid",
        "default": () => defaultValueForUUIDPrimaryKey(),
        "primaryKeyConstraintName": "PK_warehouse_blockqty_id"
      },
      "source_type_id": { 
        "type": "int",
        "nullable": true
      },
      "source_uuid": { 
        "type": "uuid",
        "nullable": true
      },
      "warehouse_type_id": { 
        "type": "int",
        "nullable": true
      },
      "warehouse_uuid": { 
        "type": "uuid",
        "nullable": true
      },
      "medicine_id": { 
        "type": "int",
        "nullable": true
      },
      "unit": {
        "type": "varchar",
        "nullable":true,
      }, 
      "blocked_qty": {
        "type": "decimal",
        "precision": 14,
        "scale": 4,
        "nullable":true,
        "default":0.0
      },
      "unblocked_qty": {
        "type": "decimal",
        "precision": 14,
        "scale": 4,
        "nullable":true,
        "default":0.0
      },
      "balance_qty": {
        "type": "decimal",
        "precision": 14,
        "scale": 4,
        "nullable":true,
        "default":0.0
      },
      "active": { 
        "type": "int",        
        "default": 1000100001
      },
      "last_modifying_user_id": { 
        "type": "uuid",        
        "nullable": true
      },
      "data_sync_status": { 
        "type": "int",        
        "default": defaultValueForDataSyncStatus()
      },
      "created_at": { 
        "type": timestampColumn(),
        "default": () => defaultValueForCreatedAt()
      }, 
      "updated_at": { 
        "type": timestampColumn(),
        "default": () => defaultValueForCreatedAt()
      }, 
    }, 
    "indices": [
        {
          name: "idx_warehouseblockqty_warehouse_uuid",
          columns: ["warehouse_uuid"]
        },
        {
          name: "idx_warehouseblockqty_source_uuid",
          columns: ["source_uuid"]
        },
    ]
  }
  
  

const WarehouseBlockQty = new EntitySchema(warehouseblockqty)

module.exports = { WarehouseBlockQty }