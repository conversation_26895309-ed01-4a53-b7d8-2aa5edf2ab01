const typeorm = require('typeorm')
const EntitySchema = typeorm.EntitySchema
const {
    defaultValueForUUIDPrimaryKey,
    defaultValueForDataSyncStatus,
    defaultValueForCreatedAt,
    timestampColumn,
} = require('../../../../utils/ormHelper')
const staffActivity = {
    "name": 'StaffActivity',
    "target": 'staff_activity',
    "schema": 'main',
    "columns": {
        "staff_activity_id": {
            "primary": true,
            "type": 'uuid',
            "default": () => defaultValueForUUIDPrimaryKey(),
            "primaryKeyConstraintName": 'PK_staff_activity_id',
        },
        "staff_id": {
            "type": 'uuid',
            "nullable": true,
        },
        "staff_activity_type_id": {
            "type": 'int',
        },
        "visit_id": {
            "type": 'varchar',
            "nullable": true,
        },
        "activity_id": {
            "type": 'int',
        },
        "activity_duration": {
            "type": timestampColumn(),
        },
        "entity_type_id": {
            "type": "int",
        },
        "entity_id": {
            "type": "uuid",
        },
        "care_calendar": {
            "type": "uuid",
        },
        "ticket_id": {
            "type": "uuid",
            "nullable": true,
        },
        "correlation_id": {
            "type": "varchar",
            "length": 256,
            "nullable": true,
        },
        "data_sync_status": {
            "type": "int",
            "default": defaultValueForDataSyncStatus(),
        },
        "created_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),
        },
        "updated_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),
        },
    },
    "indices": [
        {
          name: "idx_staff_activity_staff_id",
          columns: ["staff_id"]
        },
        {
            name: "idx_staff_activity_care_calendar_id",
            columns: ["care_calendar"]
          },
      ]      
    
}
const StaffActivity = new EntitySchema(staffActivity)

module.exports = { StaffActivity }