const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, defaultValueForDataSyncStatus, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const inventoryledger= { 
    "name": "InventoryLedger", 
    "target": "inventory_ledger",
    "schema": "main",
    "columns": { 
      "inventory_ledger_id": { 
        "primary": true, 
        "type": "uuid",
        "default": () => defaultValueForUUIDPrimaryKey(),
        "primaryKeyConstraintName": "PK_inventory_ledger_id"
      },
      "requisition_id": { 
        "type": "uuid",
        "nullable": true
      },
      "medicine_id": { 
        "type": "int",
        "nullable": true
      },
      "warehouse_type_id": { 
        "type": "int",
        "nullable": true
      },
      "warehouse_uuid": { 
        "type": "uuid",
        "nullable": true
      }, 
      "source_type_id": {
          "type": "int",
          "nullable":true,
      },
      "source_uuid": {
          "type": "uuid",
          "nullable":true,
      },
      "consumer_type_id": {
          "type": "int",
          "nullable":true,
          "default":0
      },
      "consumer_uuid":{
          "type":"uuid",
          "nullable":true
      },
      "unit": {
          "type": "varchar",
          "nullable":true,
      },
      "credit_qty": { 
        "type": "decimal",
        "precision": 14,
        "scale": 4,        
        "default": 0.0
      },
      "debit_qty": { 
        "type": "decimal",
        "precision": 14,
        "scale": 4,        
        "default": 0.0
      },
      "balance": { 
        "type": "decimal",
        "precision": 14,
        "scale": 4,        
        "default": 0.0
      },
      "active": { 
        "type": "int",        
        "default": 1000100001
      },
      "last_modifying_user_id": { 
        "type": "uuid",        
        "nullable": true
      },
      "data_sync_status": { 
        "type": "int",        
        "default": defaultValueForDataSyncStatus()
      },
      "created_at": { 
        "type": timestampColumn(),
        "default": () => defaultValueForCreatedAt()
      }, 
      "updated_at": { 
        "type": timestampColumn(),
        "default": () => defaultValueForCreatedAt()
      },
      "inserted_at": {
        "type": timestampColumn(),
        "nullable": true
      } 
    }, 
    "indices": [
        {
          name: "idx_inventoryledger_source_uuid",
          columns: ["source_uuid"]
        },
        {
          name: "idx_inventoryledger_warehouse_uuid",
          columns: ["warehouse_uuid"]
        },
        {
          name: "idx_inventoryledger_inserted_at",
          columns: ["inserted_at"]
        }
    ]
  }

const InventoryLedger = new EntitySchema(inventoryledger)

module.exports = { InventoryLedger }
