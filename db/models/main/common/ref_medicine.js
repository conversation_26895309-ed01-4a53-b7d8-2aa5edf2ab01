const typeorm = require('typeorm')
const EntitySchema = typeorm.EntitySchema
const {
  defaultValueForUUIDPrimaryKey,
  defaultValueForCreatedAt,
  defaultValueForDataSyncStatus,
  jsonColumn,
  timestampColumn,
} = require('../../../../utils/ormHelper')

const medicine = {
  "name": "Medicine",
  "target": "ref_medicine",
  "schema": "main",
  "columns": {
    "medicine_id": {
      "primary": true,
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_medicine_id",
    },
    "medicine_name": {
      "type": "varchar",
      "length": 256,
      "nullable": true,
    },
    "medicine_name_l10n": {
      "type": jsonColumn(),
      "nullable": true,
    },
    /* "medicine_unit_id": {
      "type": "int",// reference_category_id = 10009500
      "nullable": true,
    },
    "type_id": {
      "type": "int",// reference_category_id = 10009600 for medicine_type, 10009700 for types of Consumable
      "nullable": true,
    },
    "unit_size": {
      "type": "decimal",
      "precision": 14,
      "scale": 4,
      "nullable": true,
    },
    "usage_flag_id": {
      "type": "int", //reference_category_id = 10011000 for usage
      "nullable": true,
    },

    "is_rx_only_flag_id": { // classifier
      "type": "int",//use yes no 1000105001 / 1000105002
      "nullable": true,
    },
    "is_vaccine_flag_id": { // classifier
      "type": "int",//use yes no 1000105001 / 1000105002
      "nullable": true,
    },
    "is_antibiotic_flag_id": { // classifier
      "type": "int",//use yes no 1000105001 / 1000105002
      "nullable": true,
    },
    "is_medicine_or_consumable_flag_id": { // classifier
      "type": "int",//reference_category_id = 10009700
      "nullable": true,
    },
    "medicine_activity_category_id": { // classifier
      "type": "int",// reference_category_id = 10009400 for medicines / ... for consumables
      "nullable": true,
    },
    "manufacturer_id": { // classifier
      "type": "int",// reference_category_id = 10009800
      "nullable": true,
    },
    "brand_id": { // classifier
      "type": "int",// reference_category_id = 10009900
      "nullable": true,
    },
    "api_name": { // classifier
      "type": "varchar",
      "length": 256,
      "nullable": true,
    }, */
    "medicine_information": {
      "type": jsonColumn(),
      "nullable": true,
    },
    "active": {
      "type": "int",
      "default": 1000100001,
    },
    "last_modifying_user_id": {
      "type": "uuid",
      "nullable": true,
    },
    "correlation_id": {
      "type": "varchar",
      "length": 256,
      "nullable": true,
    },
    "data_sync_status": {
      "type": "int",
      "default": defaultValueForDataSyncStatus(),
    },
    "created_at": {
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt(),
    },
    "updated_at": {
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt(),
    },
  },
}
const Medicine = new EntitySchema(medicine)

module.exports = { Medicine }