const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn, defaultTimestampColumn } = require('../../../../utils/ormHelper')

const entitystatus = { 
  "name": "EntityStatus", 
  "target": "entity_status",
  "schema": "main",
  "columns": { 
    "entity_status_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_entity_status_uuid"
    }, 
    "status_category_id": {
      "type": "int",
      "nullable": true,
    },
    "status_id": {
      "type": "int",
      "nullable": true,
    },
    "entity_1_type_id": { 
      "type": "int",
      "nullable": true,
    }, 
    "entity_1_entity_uuid": { 
      "type": "uuid",
      "nullable": true,
    }, 
    "entity_1_entity_id": { 
      "type": "int",
      "nullable": true,
    }, 
    "entity_2_type_id": { 
      "type": "int",
      "nullable": true,
    }, 
    "entity_2_entity_uuid": { 
      "type": "uuid",
      "nullable": true,
    },
    "entity_2_entity_id": { 
      "type": "int",
      "nullable": true,
    },
    "start_date": { 
      "type": timestampColumn(),
      "nullable": true,
      "default": () => defaultTimestampColumn('1900-01-01 00:00:01'),
    },
    "end_date": { 
      "type": timestampColumn(),
      "nullable": true,
      "default": () => defaultTimestampColumn("2099-12-31 23:59:59")
    },
    "status_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  },      
}

const EntityStatus = new EntitySchema(entitystatus)

module.exports = { EntityStatus }