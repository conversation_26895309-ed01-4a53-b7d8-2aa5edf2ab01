const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const principal = { 
  "name": "Principal", 
  "target": "principal",
  "schema": "main",
  "columns": { 
    "principal_id": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_pricipal_id"
    }, 
    "name": { 
      "type": "varchar",
      "length": 100,
      "nullable": true
    }, 
    "second_name": { 
      "type": "text",
      "nullable": true
    }, 
    "salary": { 
      "type": "decimal",
      "precision": 14,
      "scale": 4,
      "nullable": true
    },
    "principal_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "joining_date": { 
      "type": "date",
      "nullable": true
    }, 
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "teaching_experience_2": { 
      "type": "integer" ,
      "nullable": true
    },
    "teaching_experience": { 
      "type": "integer",
      "nullable": true
    } 
  }, 
  "uniques": [
    {
      name: "UQ_principal_name",
      columns: ["name"]
    }
  ],
  "indices": [
    {
      name: "idx_principal_teaching_experience",
      columns: ["teaching_experience"]
    }
  ]
}

const Principal = new EntitySchema(principal)

module.exports = { Principal }
