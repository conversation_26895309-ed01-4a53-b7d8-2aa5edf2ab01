const typeorm = require('typeorm');
const EntitySchema = typeorm.EntitySchema;
const {
    defaultValueForUUIDPrimaryKey,
    defaultValueForCreatedAt,
    defaultValueForDataSyncStatus,
    jsonColumn,
    timestampColumn,
} = require('../../../../utils/ormHelper');

const careCalendar = {
    "name": "CareCalendar",
    "target": "care_calendar",
    "schema": "main",
    "columns": {
        "care_calendar_id": {
            "primary": true,
            "type": 'uuid',
            default: () => defaultValueForUUIDPrimaryKey(),
            "primaryKeyConstraintName": 'PK_care_calendar_id',
        },
        "entity_type_id": {
            "type": 'int',
        },
        "entity_id": {
            "type": 'int',
            "nullable": true
        },
        "entity_uuid": {
            "type": 'uuid',
            "nullable": true
        },
        "activity_id": {
            "type": 'int',
        },
        "visit_id": {
            "type": 'int',
            "nullable": true,
        },
        "activity_date": {
            "type": timestampColumn(),
        },
        "calendar_activity_status": {
            "type": 'int',
            "default": 1000300001,
        },
        "completion_date": {
            "type": timestampColumn(),
            "nullable":true,
        },
        "correlation_id": {
            "type": 'varchar',
            "length": 256,
            "nullable": true,
        },
        "data_sync_status": {
            "type": 'int',
            "default": defaultValueForDataSyncStatus(),
        },
        "created_at": {
            "type": timestampColumn(),
            default: () => defaultValueForCreatedAt(),
        },
        "updated_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),
        },
        "system_task_completion_time": {
            "type": timestampColumn(),
            "nullable":true,
        },
        "visit_schedule_time": {
            "type": timestampColumn(),
            "nullable":true,
        },
        "visit_creation_time": {
            "type": timestampColumn(),
            "nullable":true,
        },
        "user_task_completion_time": {
            "type": timestampColumn(),
            "nullable":true,
        },
        "activity_created_by":{
            "type": 'uuid',
            "nullable":true,
        },
        "activity_completed_by":{
            "type": "uuid",
            "nullable":true,
        },
        "active": {
            "type": "int",
            "default": 1000100001,
        },

    },
    "indices": [
        {
            name: "idx_care_calendar_entity_uuid",
            columns: ["entity_uuid"]
        },
        {
            name: "idx_care_calendar_entity_type_id",
            columns: ["entity_type_id"]
        },
        {
            name: "idx_care_calendar_entity_id",
            columns: ["entity_id"]
        },
        {
            name: "idx_care_calendar_calendar_activity_status",
            columns: ["calendar_activity_status"]
        },
        {
            name: "idx_care_calendar_activity_id",
            columns: ["activity_id"]
        },
        {
            name: "idx_care_calendar_activity_date_desc",
            columns: ["activity_date"]
        },
    ]    
};

const CareCalendar = new EntitySchema(careCalendar);

module.exports = { CareCalendar };