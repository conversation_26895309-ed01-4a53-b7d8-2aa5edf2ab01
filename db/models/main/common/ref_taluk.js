const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const reftaluk = { 
  "name": "Taluk", 
  "target": "ref_taluk",
  "schema": "main",
  "columns": { 
    "taluk_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_taluk_id"
    }, 
    "district_id": { 
      "type": "int",
    }, 
    "taluk_census_id": { 
      "type": "int",
      "nullable": true
    }, 
    "taluk_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "taluk_short_code": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
  "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }
  },
  "indices": [
    {
      name: "idx_ref_taluk_district_id",
      columns: ["district_id"]
    }
  ]

}

const Taluk = new EntitySchema(reftaluk)

module.exports = { Taluk }
