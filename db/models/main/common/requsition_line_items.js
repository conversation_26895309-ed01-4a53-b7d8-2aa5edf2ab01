const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, defaultValueForDataSyncStatus, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const requisitionlineitems= { 
    "name": "RequisitionLineItems", 
    "target": "requisition_line_items",
    "schema": "main",
    "columns": { 
      "requisition_line_items_id": { 
        "primary": true, 
        "type": "uuid",
        "default": () => defaultValueForUUIDPrimaryKey(),
        "primaryKeyConstraintName": "PK_requisition_line_items_id"
      },
      "requisition_id": { 
        "type": "uuid",
        "nullable": true
      },
      "requisitioner_uuid": { 
        "type": "uuid",
        "nullable": true
      },
      "medicine_id": { 
        "type": "int",
        "nullable": true
      },
      "unit": {
        "type": "varchar",
        "nullable":true,
      }, 
      "quantity": {
        "type": "decimal",
        "precision": 14,
        "scale": 4,
        "nullable":true,
        "default":0.0
      },
      "item_status_id": { 
        "type": "int",       
        "nullable":true,
        "default":0
      },
      "active": { 
        "type": "int",        
        "default": 1000100001
      },
      "last_modifying_user_id": { 
        "type": "uuid",        
        "nullable": true
      },
      "data_sync_status": { 
        "type": "int",        
        "default": defaultValueForDataSyncStatus()
      },
      "created_at": { 
        "type": timestampColumn(),
        "default": () => defaultValueForCreatedAt()
      }, 
      "updated_at": { 
        "type": timestampColumn(),
        "default": () => defaultValueForCreatedAt()
      }, 
    }, 
    "indices": [
      {
          name: "idx_requisitionlineitems_requisition_id",
          columns: ["requisition_id"]
        }
    ]
  }

const RequisitionLineItems = new EntitySchema(requisitionlineitems)

module.exports = { RequisitionLineItems }