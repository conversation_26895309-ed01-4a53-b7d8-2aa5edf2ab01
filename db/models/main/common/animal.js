const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, defaultValueForDataSyncStatus, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const animal = { 
  "name": "Animal", 
  "target": "animal",
  "schema": "main",
  "columns": { 
    "animal_id": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_animal_id"
    }, 
    "animal_visual_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "animal_type": {
      "type": "int",
      "nullable":true,
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  }, 
  "indices": [
    {
      name: "idx_animal_animal_type_id",
      columns: ["animal_type"]
    },
    {
      name: "idx_animal_active",
      columns: ["active"]
    },        
  ]
}

const Animal = new EntitySchema(animal)

module.exports = { Animal }