const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, defaultValueForDataSyncStatus, jsonColumn, timestampColumn, defaultTimestampColumn } = require('../../../../utils/ormHelper')

const asset = { 
  "name": "Asset", 
  "target": "asset",
  "schema": "main",
  "columns": { 
    "asset_id": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_asset_id"
    }, 
    "asset_visual_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "asset_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "asset_type_id": {
      "type": "int",
      "nullable":true,
    },
    "start_date": { 
      "type": timestampColumn(),
      "nullable": true,
      "default": () => defaultTimestampColumn('1900-01-01 00:00:01'),
    },
    "end_date": { 
      "type": timestampColumn(),
      "nullable": true,
      "default": () => defaultTimestampColumn("2099-12-31 23:59:59")
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  }, 
  "indices": [
    {
      name: "idx_asset_asset_type_id",
      columns: ["asset_type_id"]
    }
  ]
}

const Asset = new EntitySchema(asset)

module.exports = { Asset }