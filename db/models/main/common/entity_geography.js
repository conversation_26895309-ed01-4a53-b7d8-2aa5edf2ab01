const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn, defaultTimestampColumn } = require('../../../../utils/ormHelper')

const entitygeography = { 
  "name": "EntityGeography", 
  "target": "entity_geography",
  "schema": "main",
  "columns": { 
    "entity_geography_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_entity_geography_id"
    }, 
    "entity_type_id": { 
      "type": "int",
      "nullable": true,
    }, 
    "entity_uuid": { 
      "type": "uuid",
      "nullable": true,
    }, 
    "entity_id": { 
      "type": "int",
      "nullable": true,
    }, 
    "geography_type_id": { 
      "type": "int",
      "nullable": true,
    }, 
    "geography_id": { 
      "type": "int",
      "nullable": true,
    },
    "start_date": { 
      "type": timestampColumn(),
      "nullable": true,
      "default": () => defaultTimestampColumn('1900-01-01 00:00:01'),
    },
    "end_date": { 
      "type": timestampColumn(),
      "nullable": true,
      "default": () => defaultTimestampColumn("2099-12-31 23:59:59")
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  },
  "indices": [
    {
      name: "idx_entity_geography_geography_id",
      columns: ["geography_id"]
    },
    {
      name: "idx_entity_geography_entity_uuid",
      columns: ["entity_uuid"]
    },
    {
      name: "idx_entity_geography_entity_type_id",
      columns: ["entity_type_id"]
    },
    {
      name: "idx_entity_geography_entity_id",
      columns: ["entity_id"]
    },
  ]    

}

const EntityGeography = new EntitySchema(entitygeography)

module.exports = { EntityGeography }