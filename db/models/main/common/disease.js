const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, timestampColumn } = require('../../../../utils/ormHelper')

const animalDisease = {
    "name": "AnimalDisease",
    "target": "animal_disease",
    "schema":"main",
    "columns": {
        "animal_disease_id": {
            "primary": true,
            "type": "uuid",
            "default": () => defaultValueForUUIDPrimaryKey(),
            "primaryKeyConstraintName": "PK_animal_disease_id"
       
        },
        "animal_id": {
            "type": "uuid",
            "nullable": true,
        },
        "disease_id": {
            "type": "int",
            "nullable": true,
        },
        "disease_date": {
            "type": timestampColumn(),
            "nullable": true
        },
        "active": {
            "type": "int",
            "default": 1000100001
        },
        "last_modifying_user_id": {
            "type": "uuid",
            "nullable": true
        },
        "correlation_id": {
            "type": "varchar",
            "length": 256,
            "nullable": true
        },
        "data_sync_status": {
            "type": "int",
            "default": defaultValueForDataSyncStatus()
        },
        "created_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt()
        },
        "updated_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt()
        },
    },
};

const AnimalDisease = new EntitySchema(animalDisease)

module.exports = { AnimalDisease }