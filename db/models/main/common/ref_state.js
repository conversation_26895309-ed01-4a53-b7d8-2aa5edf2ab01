const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const refstate = { 
  "name": "State", 
  "target": "ref_state",
  "schema": "main",
  "columns": { 
    "state_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_state_id"
    }, 
    "state_census_id": { 
      "type": "int",
      "nullable": true
    }, 
    "state_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "state_short_code": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }
  }
}

const State = new EntitySchema(refstate)

module.exports = { State }
