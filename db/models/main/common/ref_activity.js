const typeorm = require('typeorm')
const EntitySchema = typeorm.EntitySchema
const {
    defaultValueForUUIDPrimary<PERSON>ey,
    defaultValueForCreatedAt,
    defaultValueForDataSyncStatus,
    jsonColumn,
    timestampColumn,
} = require('../../../../utils/ormHelper')

const ref_activity = {
    "name": "ReferenceActivity",
    "target": "ref_activity",
    "schema": "main",
    "columns": {
        "activity_id": {
            "primary": true,
            "type": "int",
            "generated": true,
            "primaryKeyConstraintName": "PK_ref_activity_id",
        },
        "activity_name": {
            "type": "varchar",
        },
        "activity_category": {
            "type": "int",
        },
        "activity_name_l10n": {
            "type": jsonColumn(),
            "nullable": true,
        },
        "activity_information": {
            "type": jsonColumn(),
            "nullable": true,
        },
        "active": {
            "type": "int",
            "default": 1000100001,
        },
        "last_modifying_user_id": {
            "type": "uuid",
            "nullable": true,
        },
        "correlation_id": {
            "type": "varchar",
            "length": 256,
            "nullable": true,
        },
        "data_sync_status": {
            "type": "int",
            "default": defaultValueForDataSyncStatus(),
        },
        "created_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),
        },
        "updated_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),
        },
    },
    "uniques": [
        {
          name: "UQ_REF_ACTIVITY_ACTIVITY_NAME",
          columns: ["activity_name", "activity_category"]
        }
      ],
}
const ReferenceActivity = new EntitySchema(ref_activity)

module.exports = { ReferenceActivity }