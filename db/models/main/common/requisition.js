const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, defaultValueForDataSyncStatus, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const requisition = { 
  "name": "Requisition", 
  "target": "requisition",
  "schema": "main",
  "columns": { 
    "requisition_id": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_requisition_id"
    }, 
    "requisitioner_type_id": { 
      "type": "int",
      "nullable": true
    },
    "requisitioner_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "fullfiller_type_id": { 
      "type": "int",
      "nullable": true
    },
    "fullfiller_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "approver_type_id": { 
      "type": "int",
      "nullable": true
    },
    "approver_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "line_items_count": { 
      "type": "int",
      "nullable": true,
      "default":0
    },
    "status_id":{
        "type":"int",
        "nullable":true,
        "default":1010000001
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "inserted_at": {
      "type": timestampColumn(),
      "nullable": true
    } 
  }, 
  "indices": [
    {
      name: "idx_requisition_status_id",
      columns: ["status_id"]
    },
    {
        name: "idx_requisition_requisitioner_uuid",
        columns: ["requisitioner_uuid"]
    },
    {
      name: "idx_requisition_approver_uuid",
      columns: ["approver_uuid"]
    },
    {
      name: "idx_requisition_fullfiller_uuid",
      columns: ["fullfiller_uuid"]
    }
  ]
}

const Requisition = new EntitySchema(requisition)

module.exports = { Requisition }