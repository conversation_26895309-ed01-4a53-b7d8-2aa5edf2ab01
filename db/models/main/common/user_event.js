const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimary<PERSON>ey, defaultValueForCreatedAt, defaultValueForDataSyncStatus, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const userEvent = { 
  "name": "userEvent", 
  "target": "user_event",
  "schema": "main",
  "columns": { 
    "user_event_id": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_user_event_id"
    }, 
    "user_device_id": { 
      "type": "int",
    }, 
    "location_tracking": { 
      "type": "int", //  This will have value default -1 0 off, 1 on.  
      "nullable": true
    }, 
    "lat": { 
      "type": "decimal",
      "precision": 14,
      "scale": 10,
      "nullable": true
    },
    "lng": { 
      "type": "decimal",
      "precision": 14,
      "scale": 10,
       "nullable": true
    },
    "time_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "additional_data": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "processed": { 
      "type": "int",
      "nullable": true,
      "default": 0
    }, 

    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  } ,
    "indices": [
        {
            name: "idx_user_event_time_at",
            columns: ["time_at"]
        },
        {
            name: "idx_user_event_user_device_id",
            columns: ["user_device_id"]
        }
        
    ] 
}

const UserEvent = new EntitySchema(userEvent)

module.exports = { UserEvent }
