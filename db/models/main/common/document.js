const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimary<PERSON>ey, defaultValueForCreatedAt, defaultValueForDocumentSyncStatus, defaultValueForDataSyncStatus, defaultEmptyJSONValue, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const document = { 
  "name": "Document", 
  "target": "document",
  "schema": "main",
  "columns": { 
    "document_id": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_document_id"
    }, 
    "document_type_id": { 
      "type": "int",
    }, 
    "document_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "document_name": { 
      "type": "varchar",
      "length":256,
      "nullable": true
    },
    "document_status_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_1_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_1_entity_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "entity_1_entity_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_2_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_2_entity_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "entity_2_entity_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_3_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_3_entity_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "entity_3_entity_id": { 
      "type": "int",
      "nullable": true
    }, 
    "client_document_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "document_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "document_meta_data_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "document_sync_status": { 
      "type": "int",        
      "default": defaultValueForDocumentSyncStatus()
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  },
  "indices": [
    {
      name: "idx_document_entity_1_id",
      columns: ["entity_1_entity_id"]
    },
    {
      name: "idx_document_entity_1_entity_uuid",
      columns: ["entity_1_entity_uuid"]
    },
    {
      name: "idx_document_entity_1_entity_id",
      columns: ["entity_1_type_id"]
    },
    {
      name: "idx_document_document_type_id",
      columns: ["document_type_id"]
    },
  ]    

}

const Document = new EntitySchema(document)

module.exports = { Document }