const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, defaultValueForDataSyncStatus, defaultTimestampColumn, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const assetfeature = { 
  "name": "AssetFeature", 
  "target": "asset_feature",
  "schema": "main",
  "columns": { 
    "asset_feature_id": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_asset_feature_id"
    }, 
    "asset_id": { 
      "type": "uuid",
      "nullable": true
    }, 
    "asset_classification_id": { 
      "type": "uuid",
      "nullable": true
    }, 
    "feature_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "feature_type_id": {
      "type": "int",
      "nullable":true,
    },
    "start_date": { 
      "type": timestampColumn(),
      "nullable": true,
      "default": () => defaultTimestampColumn('1900-01-01 00:00:01'),
    },
    "end_date": { 
      "type": timestampColumn(),
      "nullable": true,
      "default": () => defaultTimestampColumn("2099-12-31 23:59:59")
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  },
}

const AssetFeature = new EntitySchema(assetfeature)

module.exports = { AssetFeature }