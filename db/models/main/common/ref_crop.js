const typeorm = require('typeorm')
const EntitySchema = typeorm.EntitySchema
const {
  defaultValueForUUIDPrimaryKey,
  defaultValueForCreatedAt,
  defaultValueForDataSyncStatus,
  jsonColumn,
  timestampColumn,
} = require('../../../../utils/ormHelper')

const crop = {
  "name": "Crop",
  "target": "ref_crop",
  "schema": "main",
  "columns": {
    "crop_id": {
      "primary": true,
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_crop_id",
    },
    "crop_name": {
      "type": "varchar",
      "length": 256,
      "nullable": true,
    },
    "crop_name_l10n": {
      "type": jsonColumn(),
      "nullable": true,
    },
    "crop_information": {
      "type": jsonColumn(),
      "nullable": true,
    },
    "active": {
      "type": "int",
      "default": 1000100001,
    },
    "last_modifying_user_id": {
      "type": "uuid",
      "nullable": true,
    },
    "correlation_id": {
      "type": "varchar",
      "length": 256,
      "nullable": true,
    },
    "data_sync_status": {
      "type": "int",
      "default": defaultValueForDataSyncStatus(),
    },
    "created_at": {
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt(),
    },
    "updated_at": {
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt(),
    },
  },
}
const Crop = new EntitySchema(crop)

module.exports = { Crop }