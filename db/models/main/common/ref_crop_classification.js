const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimary<PERSON>ey, defaultValueForCreatedAt, defaultValueForDataSyncStatus, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const cropclassification = { 
  "name": "CropClassification", 
  "target": "ref_crop_classification",
  "schema": "main",
  "columns": { 
    "crop_classification_id": { 
      "primary": true,
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_crop_classification_id"
    }, 
    "crop_id": { 
      "type": "int",
    }, 
    "classifier_id": { 
      "type": "int",
    }, 
    "value_int": { 
      "type": "int",
      "nullable": true
    }, 
    "value_reference_id": { 
      "type": "int",
      "nullable": true
    }, 
    "value_reference_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "value_string_256": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "value_string_2000": { 
      "type": "varchar",
      "length": 2000,
      "nullable": true
    }, 
    "value_double": { 
      "type": "decimal",
      "precision": 14,
      "scale": 4,
      "nullable": true
    }, 
    "value_date": { 
      "type": timestampColumn(),
      "nullable": true
    }, 
    "value_json": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "value_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "value_string_256_encrypted": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "value_string_2000_encrypted": { 
      "type": "varchar",
      "length": 2000,
      "nullable": true
    }, 
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  },
  "indices": [
    {
        name: "idx_crop_classification_crop_id",
        columns: ["crop_id"]
    },
    {
        name: "idx_crop_classification_classifier_id",
        columns: ["classifier_id"]
    },
  ]    

}

const CropClassification = new EntitySchema(cropclassification)

module.exports = { CropClassification }