const typeorm = require('typeorm')
const EntitySchema = typeorm.EntitySchema
const {
    defaultValueForUUIDPrimary<PERSON>ey,
    defaultValueForDataSyncStatus,
    defaultValueForCreatedAt,
    jsonColumn,
    timestampColumn,
} = require('../../../../utils/ormHelper')

const careCalendarClassification = {
    "name": "CareCalendarClassification",
    "target": "care_calendar_classification",
    "schema": "main",
    "columns": {
        "care_calendar_classification_id": {
            "primary": true,
            "type": "uuid",
            "default": () => defaultValueForUUIDPrimaryKey(),
            "primaryKeyConstraintName": "PK_care_calendar_classification_id",
        },
        "care_calendar_id": {
            "type": "uuid",
        },
        "classifier_id": {
            "type": "int",
        },
        "value_int": {
            "type": "int",
            "nullable": true,
        },
        "value_reference_id": {
            "type": "int",
            "nullable": true,
        },
        "value_reference_uuid": {
            "type": "uuid",
            "nullable": true,
        },
        "value_string_256": {
            "type": "varchar",
            "length": 256,
            "nullable": true,
        },
        "value_string_2000": {
            "type": "varchar",
            "length": 2000,
            "nullable": true,
        },
        "value_double": {
            "type": "decimal",
            "precision": 14,
            "scale": 4,
            "nullable": true,
        },
        "value_date": {
            "type": timestampColumn(),
            "nullable": true,
        },
        "value_json": {
            "type": jsonColumn(),
            "nullable": true,
        },
        "value_l10n": {
            "type": jsonColumn(),
            "nullable": true,
        },
        "value_string_256_encrypted": {
            "type": "varchar",
            "length": 256,
            "nullable": true,
        },
        "value_string_2000_encrypted": {
            "type": "varchar",
            "length": 2000,
            "nullable": true,
        },
        "active": {
            "type": "int",
            "default": 1000100001,
        },
        "last_modifying_user_id": {
            "type": "uuid",
            "nullable": true,
        },
        "correlation_id": {
            "type": "varchar",
            "length": 256,
            "nullable": true,
        },
        "data_sync_status": {
            "type": "int",
            "default": defaultValueForDataSyncStatus(),
        },
        "created_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),
        },
        "updated_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),
        },
    },
    "indices": [
        {
            name: "idx_care_calendar_classification_classifier_id",
            columns: ["classifier_id"]
        },
        {
            name: "idx_care_calendar_classification_cc_id",
            columns: ["care_calendar_id"]
        },
    ]       
}

const CareCalendarClassification = new EntitySchema(careCalendarClassification)

module.exports = { CareCalendarClassification }