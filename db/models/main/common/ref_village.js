const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const refvillage = { 
  "name": "Village", 
  "target": "ref_village",
  "schema": "main",
  "columns": { 
    "village_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_village_id"
    }, 
    "taluk_id": { 
      "type": "int",
    }, 
    "village_census_id": { 
      "type": "int",
      "nullable": true
    }, 
    "village_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "village_short_code": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "village_pin_code": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }/**/
  },
  "indices": [
    {
      name: "idx_ref_village_taluk_id",
      columns: ["taluk_id"]
    },
  ]      
  
}

const Village = new EntitySchema(refvillage)

module.exports = { Village }