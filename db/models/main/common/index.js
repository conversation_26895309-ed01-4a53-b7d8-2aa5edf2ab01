const { Student } = require('./student')
const { ReferenceCategory } = require('./ref_reference_category')
const { Reference } = require('./ref_reference')
const { State } = require('./ref_state')
const { District } = require('./ref_district')
const { Taluk } = require('./ref_taluk')
const { Village } = require('./ref_village')
const { Geography } = require('./ref_geography')
const { GeographyClassification } = require('./ref_geography_classification')
const { Medicine } = require('./ref_medicine')
const { MedicineClassification } = require('./ref_medicine_classification')
const { Crop } = require('./ref_crop')
const { CropClassification } = require('./ref_crop_classification')
const { Document } = require('./document')
const { EntityRelationship } = require('./entity_relationship')
const { EntityGeography } = require('./entity_geography')
const { EntityStatus } = require('./entity_status')
const { Staff } = require('./staff')
const { StaffClassification } = require('./staff_classification')
const { Customer } = require('./customer')
const { CustomerClassification } = require('./customer_classification')
const { CustomerFeature } = require('./customer_feature')
const { CustomerFeatureClassification } = require('./customer_feature_classification')
const { UserEvent } = require('./user_event')
const { Animal } = require('./animal')
const { AnimalClassification } = require('./animal_classification')
const { Asset } = require('./asset')
const { AssetClassification } = require('./asset_classification')
const { AssetFeature } = require('./asset_feature')
const { AssetFeatureClassification } = require('./asset_feature_classification')
const { CareCalendar } = require('./care_calendar')
const { CareCalendarClassification } = require('./care_calendar_classification')
const { AnimalDisease } = require('./disease')
const { AnimalDiseaseClassification } = require('./disease_classification')
const { DumpDoc } = require('./dump_doc')
const { Note } = require('./note')
const { Period } = require('./ref_period')
const { ReferenceActivity } = require('./ref_activity')
const { ReferenceDiagnosis } = require('./ref_diagnosis')
const { StaffActivity } = require('./staff_activity')
const { Ticket } = require("./ticket");
const { tranactionLedger } = require("./transaction_ledger")
const { Requisition } = require("./requisition");
const { RequisitionClassification } = require('./requisition_classification')
const { RequisitionLineItems } = require('./requsition_line_items')
const { InventoryLedger } = require('./inventory_ledger')
const { WarehouseBlockQty } = require('./warehouse_blockqty')
const { Partner } = require('./partner')
const { PartnerClassification } = require('./partner_classification')
const { RefActivityClassification } = require('./ref_activity_classification')
const { TaskQuestion } = require('./task_question')
const { TaskQuestionData } = require("./task_question_data")
// const { Principal } = require('../common/principal')

const models = [ReferenceCategory, Reference, ReferenceActivity, ReferenceDiagnosis,
    State, District, Taluk, Village, Geography, GeographyClassification, Medicine, MedicineClassification,
    Crop, CropClassification, Period,
    EntityStatus, Document, EntityRelationship, EntityGeography, Staff, StaffClassification,
    Customer, CustomerClassification, CustomerFeature, CustomerFeatureClassification,
    UserEvent,
    Animal, AnimalClassification, AnimalDisease, AnimalDiseaseClassification,
    Asset, AssetClassification, AssetFeature, AssetFeatureClassification,
    CareCalendar, CareCalendarClassification, DumpDoc, Note,
    StaffActivity, Ticket, tranactionLedger,Requisition,RequisitionClassification,
    RequisitionLineItems,
    InventoryLedger,WarehouseBlockQty, 
    Partner, PartnerClassification,RefActivityClassification,
    TaskQuestion, TaskQuestionData]
    
module.exports = { models }