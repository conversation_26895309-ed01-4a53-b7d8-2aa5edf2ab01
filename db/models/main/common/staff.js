const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const staff = { 
  "name": "Staff", 
  "target": "staff",
  "schema": "main",
  "columns": { 
    "staff_id": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_staff_id"
    }, 
    "staff_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "staff_visual_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "staff_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "mobile_number": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "email_address": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  },
  "indices": [
    {
      name: "idx_staff_staff_type_id",
      columns: ["staff_type_id"]
    },
  ]      

}

const Staff = new EntitySchema(staff)

module.exports = { Staff }