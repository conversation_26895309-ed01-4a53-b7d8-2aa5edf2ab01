const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const note = {
    "name": "Note",
    "target": "note",
    "schema": "main",
    "columns": {
        "note_id": {
            "primary": true,
            "type": "uuid",
            "default": () => defaultValueForUUIDPrimaryKey(),
            "primaryKeyConstraintName": "PK_note_id"
        },
        "note_type_id": {
            "type": "int",
            "nullable":true
        },
        "note_status_id": {
            "type": "int",
            "nullable":true
        },
        "note_time": {
            "type": timestampColumn(),
            "nullable": true
        },
        "note": {
            "type": jsonColumn(),
        },
        "entity_1_type_id": {
            "type": "int",
            "nullable": true
        },
        "entity_1_uuid":{
            "type":"uuid",
            "nullable":true 
        },
        "entity_1_id":{
            "type":"int",
            "nullable":true 
        },
        "entity_2_type_id": {
            "type": "int",
            "nullable": true
        },
        "entity_2_uuid":{
            "type":"uuid",
            "nullable":true 
        },
        "entity_2_id":{
            "type":"int",
            "nullable":true 
        },
        "entity_3_type_id": {
            "type": "int",
            "nullable": true
        },
        "entity_3_uuid":{
            "type":"uuid",
            "nullable":true 
        },
        "entity_3_id":{
            "type":"int",
            "nullable":true 
        },
        "entity_4_type_id": {
            "type": "int",
            "nullable": true
        },
        "entity_4_uuid":{
            "type":"uuid",
            "nullable":true 
        },
        "entity_4_id":{
            "type":"int",
            "nullable":true 
        },
        "creator_uuid": {
            "type": "uuid",
            "nullable": true
        },
        "creator_id": {
            "type": "int",
            "nullable": true
        },
        "creator_type_id": {
            "type": "int",
            "nullable": true,
        },
        "active": {
            "type": "int",
            "default": 1000100001
        },
        "last_modifying_user_id": {
            "type": "uuid",
            "nullable": true
        },
        "correlation_id": {
            "type": "varchar",
            "length": 256,
            "nullable": true
        },
        "data_sync_status": {
            "type": "int",
            "default": defaultValueForDataSyncStatus()
        },
        "created_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt()
        },
        "updated_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt()
        },
    }
}

const Note = new EntitySchema(note)

module.exports = { Note }
