const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimary<PERSON>ey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const assetfeatureclassification = { 
  "name": "AssetFeatureClassification", 
  "target": "asset_feature_classification",
  "schema": "main",
  "columns": { 
    "asset_feature_classification_id": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_asset_feature_classification_id"
    }, 
    "asset_feature_id": { 
      "type": "uuid",
    }, 
    "classifier_id": { 
      "type": "int",
    }, 
    "value_int": { 
      "type": "int",
      "nullable": true
    }, 
    "value_reference_id": { 
      "type": "int",
      "nullable": true
    }, 
    "value_reference_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "value_string_256": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "value_string_2000": { 
      "type": "varchar",
      "length": 2000,
      "nullable": true
    }, 
    "value_double": { 
      "type": "decimal",
      "precision": 14,
      "scale": 4,
      "nullable": true
    }, 
    "value_date": { 
      "type": timestampColumn(),
      "nullable": true
    }, 
    "value_json": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "value_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "value_string_256_encrypted": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "value_string_2000_encrypted": { 
      "type": "varchar",
      "length": 2000,
      "nullable": true
    }, 
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  }
}

const AssetFeatureClassification = new EntitySchema(assetfeatureclassification)

module.exports = { AssetFeatureClassification }