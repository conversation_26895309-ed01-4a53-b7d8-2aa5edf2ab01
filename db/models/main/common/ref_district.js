const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const refdistrict = { 
  "name": "District", 
  "target": "ref_district",
  "schema": "main",
  "columns": { 
    "district_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_district_id"
    }, 
    "state_id": { 
      "type": "int",
    }, 
    "district_census_id": { 
      "type": "int",
      "nullable": true
    }, 
    "district_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "district_short_code": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
  "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }
  },
  "indices": [
    {
      name: "idx_ref_district_state_id",
      columns: ["state_id"]
    }
  ]
}

const District = new EntitySchema(refdistrict)

module.exports = { District }
