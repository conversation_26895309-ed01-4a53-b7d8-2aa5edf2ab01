const typeorm = require('typeorm')
const EntitySchema = typeorm.EntitySchema
const {
    defaultValueForUUIDPrimaryKey,
    defaultValueForDataSyncStatus,
    defaultValueForCreatedAt,
    timestampColumn,
} = require('../../../../utils/ormHelper')
const ticket = {
    "name": 'Ticket',
    "target": 'ticket',
    "schema": 'main',
    "columns": {
        "ticket_id": {
            "primary": true,
            "type": 'uuid',
            "default": () => defaultValueForUUIDPrimaryKey(),
            "primaryKeyConstraintName": 'PK_ticket_id',
        },
        "ticket_status": {
            "type": 'varchar',
            "length": 256,
            "default": 1000300001,
        },
        "correlation_id": {
            "type": 'varchar',
            "length": 256,
            "nullable": true,
        },
        "data_sync_status": {
            "type": 'int',
            "default": defaultValueForDataSyncStatus(),
        },
        "created_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),
        },
        "updated_at": {
            "type": timestampColumn(),
            "default": () => defaultValueForCreatedAt(),
        },
    },
}
const Ticket = new EntitySchema(ticket)

module.exports = { Ticket }