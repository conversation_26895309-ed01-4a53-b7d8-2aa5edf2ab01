const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const taskQuestionModal = { 
    "name": "TaskQuestion", 
    "target": "task_question",
    "schema": "main",
    "columns": { 
      "question_uuid": { 
        "primary": true, 
        "type": "uuid",
        "default": () => defaultValueForUUIDPrimaryKey(),
        "primaryKeyConstraintName": "PK_question_uuid"
      }, 
      "question_name": {
        "type": "varchar",
        "length": 256
       },
       "question_name_l10n": {
         "type": jsonColumn(),
         "nullable": true
       },
       "topic_name": {
        "type": "varchar",
        "length": 256
       },
       "topic_name_l10n":{
        "type": jsonColumn(),
        "nullable": true
       },
       "topic_priority":{
        "type": "int",      
        "nullable": true
       },
       "topic_question_priority":{
        "type": "int",      
        "nullable": true
       },
       "priority_grouping": {
        "type": "int",      
        "nullable": true
       },
       "form_configuration": {
        "type": jsonColumn(),
        "nullable": true
       },
       "frequency": {
        "type": "varchar",
        "length": 256
       },
       "farmer_input_required": {
        "type": "int",      
        "nullable": true
       },
       "review_date_config": {
        "type": "int",      
        "nullable": true
       },
       "active": { 
        "type": "int",        
        "default": 1000100001
      },
      "weightage": {
        "type": "int",      
        "nullable": true
       },
      "data_sync_status": { 
        "type": "int",        
        "default": defaultValueForDataSyncStatus()
      },
       "created_at": { 
         "type": timestampColumn(),
         "default": () => defaultValueForCreatedAt()
       }, 
       "updated_at": { 
         "type": timestampColumn(),
         "default": () => defaultValueForCreatedAt()
       }, 
    }, 
    
}
const TaskQuestion = new EntitySchema(taskQuestionModal)

module.exports = { TaskQuestion }