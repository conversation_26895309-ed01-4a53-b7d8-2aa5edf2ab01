const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimary<PERSON>ey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const refgeography = { 
  "name": "Geography", 
  "target": "ref_geography",
  "schema": "main",
  "columns": { 
    "geography_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_geography_id"
    }, 
    "geography_type_id": { 
      "type": "int",        
      "nullable": true
    },
    "geography_census_id": { 
      "type": "int",        
      "nullable": true
    }, 
    "geography_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "geography_short_code": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "geography_geojson": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "geography_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
  "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }
  }
}

const Geography = new EntitySchema(refgeography)

module.exports = { Geography }
