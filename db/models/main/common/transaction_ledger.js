const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const tranactionLedgerModal = { 
  "name": "TransactionLedger", 
  "target": "transaction_ledger",
  "schema": "main",
  "columns": { 
    "transaction_ledger_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_transaction_ledger_uuid"
    }, 
    "account_id": {
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "staff_uuid": {
      "type": "uuid"
    },
    "amount": {
        "type": "decimal",
        "precision": 14,
        "scale": 4
    },
    "transaction_type": {
        "type": "int"       
    },
    "transaction_category": {
        "type": "int"        
    },
    "transaction_date": {
       "type": timestampColumn(),
    },
    "transaction_description": {
        "type": jsonColumn(),
        "nullable": true
    },
    "entity_uuid": {
        "type": "uuid",
        "nullable": true
    },
    "entity_type_id": {
      "type": "int",
      "nullable": true        
    },
    "active": { 
      "type": "int",        
      "default": **********
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "entity_name": {
      "type": "varchar",
      "length": 256,
      "nullable": true        
    },
    "entity_service": {
      "type": "varchar",
      "length": 256,
      "nullable": true        
    },
    "transaction_status": {
      "type": "int",        
      "nullable": true        
    }, 
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  },

}

const tranactionLedger = new EntitySchema(tranactionLedgerModal)

module.exports = { tranactionLedger }