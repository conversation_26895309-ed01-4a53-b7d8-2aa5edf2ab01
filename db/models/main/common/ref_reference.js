const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimary<PERSON>ey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const reference = { 
  "name": "ReferenceCategory", 
  "target": "ref_reference",
  "schema": "main",
  "columns": { 
    "reference_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_reference_id"
    }, 
    "reference_category_id": { 
      "type": "int",
    },
    "field_name":{
      "type": "varchar",
      "length": 256,
      "nullable":true
    },
    "reference_name":{
      "type":"varchar",
      "length":256
    }, 
    "reference_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "reference_information": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "type_of_data":{
      "type":"varchar",
      "length":256,
      "nullable":true
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }
  },
  "uniques": [
    {
      name: "UQ_670ee86f995838d8f03081b7d84",
      columns: ["field_name"]
    }
  ],
}

const Reference = new EntitySchema(reference)

module.exports = { Reference }