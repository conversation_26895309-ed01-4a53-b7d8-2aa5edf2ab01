const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const customer = { 
  "name": "Customer", 
  "target": "customer",
  "schema": "main",
  "columns": { 
    "customer_id": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_customer_id"
    }, 
    "customer_type_id": { 
      "type": "int",
      "nullable":true,
      "default": 1000220001
    }, 
    "customer_visual_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "customer_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "mobile_number": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "email_address": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  },
  "indices": [
    {
      name: "created_at_sort_index",
      columns: ["created_at"]
    },
    {
      name: "idx_customer_active",
      columns: ["active"]
    }    
  ]    
}

const Customer = new EntitySchema(customer)

module.exports = { Customer }