const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema

const student = { 
  "name": "Student", 
  "target": "student",
  "schema": "main",
  "columns": { 
    "id": { 
      "primary": true, 
      "type": "int", 
      "generated": true 
    }, 
    "name": { 
      "type": "varchar" 
    }, 
    "age_2": { 
      "type": "integer" 
    },
    "age": { 
      "type": "integer" 
    } 
  } 
}

Student = new EntitySchema(student)

module.exports = { Student }
