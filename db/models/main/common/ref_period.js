const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultTimestampColumn, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const period = { 
  "name": "Period", 
  "target": "ref_period",
  "schema": "main",
  "columns": { 
    "period_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_period_id"
    }, 
    "period_type_id": {
      "type": "int",
      "nullable": true,
    },
    "period_name":{
      "type":"varchar",
      "length":256,
      "nullable": true
    }, 
    "period_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "period_information": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "start_date": { 
      "type": timestampColumn(),
      "nullable": true,
      "default": () => defaultTimestampColumn('1900-01-01 00:00:01'),
    },
    "end_date": { 
      "type": timestampColumn(),
      "nullable": true,
      "default": () => defaultTimestampColumn("2099-12-31 23:59:59")
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "last_modifying_user_id": { 
      "type": "uuid",        
      "nullable": true
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "data_sync_status": { 
      "type": "int",        
      "default": defaultValueForDataSyncStatus()
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }
  }
}

const Period = new EntitySchema(period)

module.exports = { Period }