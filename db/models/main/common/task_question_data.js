const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const taskQuestionDataModal = { 
    "name": "TaskQuestionData", 
    "target": "task_question_data",
    "schema": "main",
    "columns": { 
      "question_data_uuid": { 
        "primary": true, 
        "type": "uuid",
        "default": () => defaultValueForUUIDPrimaryKey(),
        "primaryKeyConstraintName": "PK_question_data_uuid"
      }, 
      "care_calendar_id": {
        "type": "uuid",
       },
       "question_uuid": {
        "type": "uuid",
       },
       "form_configuration":{
        "type": jsonColumn(),
        "nullable": true
       },
       "review_date": {
        "type": "varchar",
        "nullable": true
       },
       "priority_grouping": {
         "type": "int",
         "nullable": true
       },
       "active": { 
        "type": "int",        
        "default": 1000100001
      },
      "data_sync_status": { 
        "type": "int",        
        "default": defaultValueForDataSyncStatus()
      },
      "saved_at": {
        "type": timestampColumn(),
        "nullable": true
      },
      "weightage": {
        "type": "int",      
        "nullable": true
       },
       "score": {
        "type": "decimal",
        "precision": 3,
        "scale": 2,
        "nullable": true
       },
       "created_at": { 
         "type": timestampColumn(),
         "default": () => defaultValueForCreatedAt()
       }, 
       "updated_at": { 
         "type": timestampColumn(),
         "default": () => defaultValueForCreatedAt()
       }, 
    }, 
    
}
const TaskQuestionData = new EntitySchema(taskQuestionDataModal)

module.exports = { TaskQuestionData }