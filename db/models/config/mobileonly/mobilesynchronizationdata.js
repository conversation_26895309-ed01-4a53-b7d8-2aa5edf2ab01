const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const mobilesynchronizationdata = { 
  "name": "MobileSynchronizationData", 
  "target": "mobile_synchronization_data",
  "schema": "config",
  "columns": { 
    "mobile_synchronization_data_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_mobile_synchronization_data_id"
    }, 
    "table_name": { 
      "type": "varchar",
      "length": 256
    }, 
    "last_sync_time": { 
        "type": timestampColumn(),
        "nullable": true
      },
      "active": { 
        "type": "int",        
        "default": 1000100001,
      },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  } 
}

const MobileSynchronizationData = new EntitySchema(mobilesynchronizationdata)

module.exports = { MobileSynchronizationData }
