const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const synchronizationconflict = { 
  "name": "SynchronizationConflict", 
  "target": "synchronization_conflict",
  "schema": "config",
  "columns": { 
    "synchronization_conflict_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_synchronization_conflict_id"
    }, 
    "user_device_id": { 
      "type": "int",
    }, 
    "table_name": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "table_primary_key_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "table_primary_key_id": { 
      "type": "int",
      "nullable": true
    }, 
    "old_non_encrypted_column_data": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "new_non_encrypted_column_data": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "old_encrypted_column_data": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "new_encrypted_column_data": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }
  }
}

const SynchronizationConflict = new EntitySchema(synchronizationconflict)

module.exports = { SynchronizationConflict }
