const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const synchronizationdata = { 
  "name": "SynchronizationData", 
  "target": "synchronization_data",
  "schema": "config",
  "columns": { 
    "synchronization_data_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_synchronization_data_id"
    }, 
    "user_device_id": { 
      "type": "int",
    }, 
    "table_name": { 
      "type": "varchar",
      "length": 256
    }, 
    "last_sync_time": { 
      "type": timestampColumn(),
      "nullable": true
    },
    "active": { 
      "type": "int",        
      "default": 1000100001
    },
    "correlation_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }
  }
}

const SynchronizationData = new EntitySchema(synchronizationdata)

module.exports = { SynchronizationData }
