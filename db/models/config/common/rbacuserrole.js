const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema

const rbacuserrole = { 
  "name": "RBACUserRole", 
  "target": "rbac_user_role",
  "schema": "config",
  "columns": { 
    "id": { 
      "primary": true, 
      "type": "int", 
      "generated": true 
    }, 
    "table_name": { 
      "type": "varchar" 
    }, 
    "device_id_2": { 
      "type": "integer" 
    },
    "device_id": { 
      "type": "integer" 
    } 
  } 
}

const RBACUserRole = new EntitySchema(rbacuserrole)

module.exports = { RBACUserRole }
