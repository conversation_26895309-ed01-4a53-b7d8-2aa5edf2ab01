const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const snapshotfarmeractiveanimalcount = { 
  "name": "SnapshotFarmerActiveAnimalCount", 
  "target": "snapshot_farmer_active_animal_count",
  "schema": "reporting",
  "columns": { 
    "snapshot_farmer_active_animal_count_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_snapshot_farmer_active_animal_count_uuid"
    },
    "snapshot_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "snapshot_timestamp": { 
      "type": timestampColumn(),
      "nullable": true
    },
    "snapshot_farmer_active_animal_count_information": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "customer_id": { 
      "type": "uuid",
      "nullable": true
    }, 
    "animal_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "active_animal_count": { 
      "type": "int",
      "nullable": true
    }, 
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  } 
}

const SnapshotFarmerActiveAnimalCount = new EntitySchema(snapshotfarmeractiveanimalcount)

module.exports = { SnapshotFarmerActiveAnimalCount }
