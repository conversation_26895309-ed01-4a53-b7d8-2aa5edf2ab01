const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema

const teacherstudent = { 
  "name": "TeacherStudent", 
  "target": "teacher_student",
  "schema": "reporting",
  "columns": { 
    "id": { 
      "primary": true, 
      "type": "int", 
      "generated": true 
    }, 
    "teacher_name": { 
      "type": "varchar" 
    }, 
    "teach_duration": { 
      "type": "integer" 
    },
    "student_name": { 
      "type": "varchar" 
    }
  } 
}

const TeacherStudent = new EntitySchema(teacherstudent)

module.exports = { TeacherStudent }
