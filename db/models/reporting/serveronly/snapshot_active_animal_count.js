const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const snapshotactiveanimalcount = { 
  "name": "SnapshotActiveAnimalCount", 
  "target": "snapshot_active_animal_count",
  "schema": "reporting",
  "columns": { 
    "snapshot_active_animal_count_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_snapshot_active_animal_count_uuid"
    },
    "snapshot_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "snapshot_timestamp": { 
      "type": timestampColumn(),
      "nullable": true
    },
    "snapshot_active_animal_count_information": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "village_id": { 
      "type": "int",
      "nullable": true
    }, 
    "animal_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "active_animal_count": { 
      "type": "int",
      "nullable": true
    }, 
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  } 
}

const SnapshotActiveAnimalCount = new EntitySchema(snapshotactiveanimalcount)

module.exports = { SnapshotActiveAnimalCount }
