const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const activefarmer = { 
  "name": "ActiveFarmer", 
  "target": "farmer",
  "schema": "reporting",
  "columns": { 
    "farmer_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_farmer_uuid"
    },
    "farmer_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "farmer_name": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "farmer_mobile": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "bco_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "start_date": { 
      "type": timestampColumn(),
      "nullable": true
    },
    "end_date": { 
      "type": timestampColumn(),
      "nullable": true
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  } 
}

const ActiveFarmer = new EntitySchema(activefarmer)

module.exports = { ActiveFarmer }
