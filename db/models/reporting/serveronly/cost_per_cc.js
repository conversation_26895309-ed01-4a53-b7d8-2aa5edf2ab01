const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const costpercc = { 
  "name": "CostPerCC", 
  "target": "cost_per_cc",
  "schema": "reporting",
  "columns": { 
    "cost_per_cc_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_cost_per_cc_uuid"
    },
    "care_calendar_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "entity_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "customer_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "customer_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "mobile_number": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "village_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "taluk_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "district_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "state_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "animal_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "animal_eartag": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "animal_type": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "subscription_plan_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "start_date": { 
      "type": timestampColumn(),
      "nullable": true
    },
    "active": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "activity_id": { 
      "type": "int",
      "nullable": true
    }, 
    "activity_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "activity_category_name_l10n": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "activity_date": { 
      "type": timestampColumn(),
      "nullable": true
    },
    "activity_status_id": { 
      "type": "int",
      "nullable": true
    },
    "medicine_cost": { 
      "type": "decimal",
      "precision": 14,
      "scale": 4,
      "nullable": true
    }, 
    "pv_cost": { 
      "type": "decimal",
      "precision": 14,
      "scale": 4,
      "nullable": true
    }, 
    "vet_cost": { 
      "type": "decimal",
      "precision": 14,
      "scale": 4,
      "nullable": true
    }, 
    "information_json": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  } 
}

const CostPerCC = new EntitySchema(costpercc)

module.exports = { CostPerCC }
