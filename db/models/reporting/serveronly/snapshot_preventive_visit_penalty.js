const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const snapshotpreventivevisitpenalty = { 
  "name": "SnapshotPreventiveVisitPenalty", 
  "target": "snapshot_preventive_visit_penalty",
  "schema": "reporting",
  "columns": { 
    "snapshot_preventive_cases_penalty_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_snapshot_preventive_cases_penalty_uuid"
    },
    "care_calendar_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "entity_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "activity_id": { 
      "type": "int",
      "nullable": true
    }, 
    "vet_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "paravet_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "vet_penalty_amount": { 
      "type": "decimal",
      "precision": 14,
      "scale": 4,
      "nullable": true
    }, 
    "paravet_penalty_amount": { 
      "type": "decimal",
      "precision": 14,
      "scale": 4,
      "nullable": true
    }, 
    "activity_status_id": { 
      "type": "int",
      "nullable": true
    }, 
    "activity_category_id": { 
      "type": "int",
      "nullable": true
    }, 

    "activity_timestamp": { 
      "type": timestampColumn(),
      "nullable": true
    }, 
    
     
    "snapshot_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "snapshot_timestamp": { 
      "type": timestampColumn(),
      "nullable": true
    },
    "snapshot_visit_penalty_information": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  } 
}

const SnapshotPreventiveVisitPenalty = new EntitySchema(snapshotpreventivevisitpenalty)

module.exports = { SnapshotPreventiveVisitPenalty }
