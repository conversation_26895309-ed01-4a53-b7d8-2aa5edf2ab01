const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const activeanimal = { 
  "name": "ActiveAnimal", 
  "target": "animal",
  "schema": "reporting",
  "columns": { 
    "animal_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_animal_uuid"
    },
    "animal_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "farmer_id": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    },
    "start_date": { 
      "type": timestampColumn(),
      "nullable": true
    },
    "end_date": { 
      "type": timestampColumn(),
      "nullable": true
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  } 
}

const ActiveAnimal = new EntitySchema(activeanimal)

module.exports = { ActiveAnimal }
