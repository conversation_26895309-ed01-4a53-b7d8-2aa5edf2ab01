const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForDataSyncStatus, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const snapshotvisitpenalty = { 
  "name": "SnapshotVisitPenalty", 
  "target": "snapshot_visit_penalty",
  "schema": "reporting",
  "columns": { 
    "snapshot_penalty_uuid": { 
      "primary": true, 
      "type": "uuid",
      "default": () => defaultValueForUUIDPrimaryKey(),
      "primaryKeyConstraintName": "PK_snapshot_penalty_uuid"
    },
    "care_calendar_uuid": { 
      "type": "uuid",
      "nullable": true
    }, 
    "entity_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "entity_uuid": { 
      "type": "uuid",
      "nullable": true
    },
    "service_request_timestamp": { 
      "type": timestampColumn(),
      "nullable": true
    }, 
    "service_request_no": { 
      "type": "varchar",
      "length": 256,
      "nullable": true
    }, 
    "vet_published_status_id": { 
      "type": "int",
      "nullable": true
    }, 
    "paravet_administered_status_id": { 
      "type": "int",
      "nullable": true
    }, 
    "vet_penalty_amount": { 
      "type": "decimal",
      "precision": 14,
      "scale": 4,
      "nullable": true
    }, 
    "paravet_penalty_amount": { 
      "type": "decimal",
      "precision": 14,
      "scale": 4,
      "nullable": true
    }, 
    "activity_category_id": { 
      "type": "int",
      "nullable": true
    }, 
    "activity_id": { 
      "type": "int",
      "nullable": true
    }, 
    "activity_status_id": { 
      "type": "int",
      "nullable": true
    }, 
    "snapshot_type_id": { 
      "type": "int",
      "nullable": true
    }, 
    "snapshot_timestamp": { 
      "type": timestampColumn(),
      "nullable": true
    },
    "snapshot_visit_penalty_information": { 
      "type": jsonColumn(),
      "nullable": true
    },
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
  } 
}

const SnapshotVisitPenalty = new EntitySchema(snapshotvisitpenalty)

module.exports = { SnapshotVisitPenalty }
