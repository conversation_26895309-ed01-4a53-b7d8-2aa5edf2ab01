const common = require('./common')
const mobileonly = require('./mobileonly')
const serveronly = require('./serveronly')

const entities = { common: common, mobileonly: mobileonly, serveronly: serveronly }
let models = []
if (process.env.IS_SERVER === 'True') {
    models = [...common.models, ...serveronly.models]
} else {
    models = [...common.models, ...mobileonly.models]
}
module.exports = { entities, models }