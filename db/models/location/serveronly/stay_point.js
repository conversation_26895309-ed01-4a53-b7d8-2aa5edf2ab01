const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const staypoint = { 
  "name": "StayPoint", 
  "target": "stay_point",
  "schema": "location",
  "columns": { 
    "stay_point_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_stay_point_id"
    }, 
    

    "user_device_id": { 
      "type": "int",        
      "nullable": true
    },
  
    "lat": { 
      "type": "decimal",
      "precision": 14,
      "scale": 10,
      "nullable": false,
      "default": -90.0
    },
    "lng": { 
      "type": "decimal",
      "precision": 14,
      "scale": 10,
       "nullable": false,
      "default": -90.0
    },
    "arrived_at": { 
      "type": timestampColumn(),
      "nullable": true
    },
    "left_at": { 
        "type": timestampColumn(),
        "nullable": true
      },
  
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    },
    "processed": { 
      "type": "int",
      "default": 0,
      "nullable": false
    },
    
  } ,
    "indices": [
        {
            name: "idx_stay_point_user_device_id",
            columns: ["user_device_id"]
        },
        {
            name: "idx_stay_point_arrived_at",
            columns: ["arrived_at"]
        },
        {
            name: "idx_stay_point_left_at",
            columns: ["left_at"]
        },
        {
          name:"idx_stay_point_processed",
          columns: ["processed"]
        }
        
    ] 
}

const StayPoint = new EntitySchema(staypoint)

module.exports = { StayPoint }

