const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const travelroute = { 
  "name": "TravelRoute", 
  "target": "travel_route",
  "schema": "location",
  "columns": { 
    "travel_route_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_travel_route_id"
    }, 
    

    "user_device_id": { 
      "type": "int",        
      "nullable": true
    },
  
    "start_lat": { 
      "type": "decimal",
      "precision": 14,
      "scale": 10,
      "nullable": false,
      "default": -90.0
    },
    "start_lng": { 
      "type": "decimal",
      "precision": 14,
      "scale": 10,
       "nullable": false,
      "default": -90.0
    },
    "end_lat": { 
        "type": "decimal",
        "precision": 14,
        "scale": 10,
        "nullable": false,
        "default": -90.0
      },
      "end_lng": { 
        "type": "decimal",
        "precision": 14,
        "scale": 10,
         "nullable": false, 
        "default": -90.0
      },
  

    "start_at": { 
      "type": timestampColumn(),
      "nullable": true
    },
    "end_at": { 
        "type": timestampColumn(),
        "nullable": true
      },
    "distance": {
      "type": "decimal",
      "precision": 14,
      "scale": 6,
      "nullable": true
    },
  
    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    },
    "processed": { 
      "type": "int",
      "default": 0,
      "nullable": false
    },
    
  } ,
    "indices": [
        {
            name: "idx_travel_route_user_device_id",
            columns: ["user_device_id"]
        },
        {
            name: "idx_travel_route_start_at",
            columns: ["start_at"]
        },
        {
            name: "idx_travel_route_end_at",
            columns: ["end_at"]
        },
        {
            name: "idx_travel_route_processed",
            columns: ["processed"]
        }
        
    ] 
}

const TravelRoute = new EntitySchema(travelroute)

module.exports = { TravelRoute }

