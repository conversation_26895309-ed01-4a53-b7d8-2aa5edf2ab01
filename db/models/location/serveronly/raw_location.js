const typeorm = require("typeorm")
const EntitySchema = typeorm.EntitySchema
const { defaultValueForUUIDPrimaryKey, defaultValueForCreatedAt, jsonColumn, timestampColumn } = require('../../../../utils/ormHelper')

const rawlocation = { 
  "name": "RawLocation", 
  "target": "raw_location",
  "schema": "location",
  "columns": { 
    "raw_location_id": { 
      "primary": true, 
      "type": "int",
      "generated": true,
      "primaryKeyConstraintName": "PK_raw_location_id"
    }, 
    
    "accuracy": { 
      "type": "decimal",
      "precision": 14,
      "scale": 10,
      "nullable": true
    },
    "altitude": { 
      "type": "decimal",
      "precision": 14,
      "scale": 10,
    "nullable": true
    },
    "bearing": { 
      "type": "decimal",
      "precision": 14,
      "scale": 10,
      "nullable": true
    },

    "user_device_id": { 
      "type": "int",        
      "nullable": true
    },
    "location_on_device_id": { 
      "type": "int",        
      "nullable": true
    },
    "is_from_mock_provider": { 
      "type": "boolean",
      "nullable": true
    },

    "lat": { 
      "type": "decimal",
      "precision": 14,
      "scale": 10,
      "nullable": false,
      "default": -90.0
    },
    "lng": { 
      "type": "decimal",
      "precision": 14,
      "scale": 10,
       "nullable": false,
       "default": -90.0
      },
  
    "location_provider": { 
      "type": "int",        
      "nullable": true
    },
    "mock_locations_enabled": { 
      "type": "boolean",
      "nullable": true
    },
    "provider": { 
      "type": "varchar",
      "length": 20,
      "nullable": true
    },
    "speed": { 
      "type": "decimal",
      "precision": 14,
      "scale": 6,
       "nullable": true
    },
    "time_at": { 
      "type": timestampColumn(),
      "nullable": true
    },

    "created_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    }, 
    "updated_at": { 
      "type": timestampColumn(),
      "default": () => defaultValueForCreatedAt()
    },
    "processed": { 
      "type": "int",
      "default": 0,
      "nullable": false
    },
    "additional_data": { 
      "type": jsonColumn(),
      "nullable": true
    }, 
  },
   "indices": [
        {
            name: "idx_raw_location_user_device_id",
            columns: ["user_device_id"]
        },
        {
            name: "idx_raw_location_time_at",
            columns: ["time_at"]
        },
        {
            name: "idx_raw_location_processed",
            columns: ["processed"]
        },
        
    ] 
}

const RawLocation = new EntitySchema(rawlocation)

module.exports = { RawLocation }

