const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class CreateSDTVView1674876937211 {

  async up(queryRunner) {
    try {
      await queryRunner.query(
        `
          DROP VIEW "ref_sdtv_view"
        `
        )
      } catch (error) {
    }
    await queryRunner.query(
        `
          CREATE VIEW "ref_sdtv_view" AS
            select "rv"."village_id" as village_id, "rt"."taluk_id" as taluk_id,
              "rd"."district_id" as district_id, "rs"."state_id" as state_id,
              "rv"."village_name_l10n", "rv"."village_short_code", "rt"."taluk_name_l10n", "rt"."taluk_short_code",
              "rd"."district_name_l10n", "rd"."district_short_code", "rs"."state_name_l10n", "rs"."state_short_code"
            from "ref_village" "rv", "ref_taluk" "rt", "ref_district" "rd", "ref_state" "rs"
            where "rv"."taluk_id" = "rt"."taluk_id" and "rt"."district_id" = "rd"."district_id" 
            and "rd"."state_id" = "rs"."state_id"
        `
      )
  }

  async down(queryRunner) {
    await queryRunner.query(
      `
        DROP VIEW "ref_sdtv_view"
      `
      )
  }

}
