const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1701443354451 {
    name = 'Main1701443354451'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "entity_status" ("entity_status_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "status_category_id" integer, "status_id" integer, "entity_1_type_id" integer, "entity_1_entity_uuid" varchar, "entity_1_entity_id" integer, "entity_2_type_id" integer, "entity_2_entity_uuid" varchar, "entity_2_entity_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "status_information" text, "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "asset" ("asset_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "asset_visual_id" varchar(256), "asset_name_l10n" text, "asset_type_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "asset_classification" ("asset_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "asset_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "asset_feature" ("asset_feature_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "asset_id" varchar NOT NULL, "feature_name_l10n" text, "feature_type_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "asset_feature_classification" ("asset_feature_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "asset_feature_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "ref_period" ("period_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "period_type_id" integer, "period_name" varchar(256), "period_name_l10n" text, "period_information" text, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "transaction_ledger" ("transaction_ledger_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "account_id" varchar(256), "staff_uuid" varchar NOT NULL, "amount" decimal(14,4) NOT NULL, "transaction_type" integer NOT NULL, "transaction_category" integer NOT NULL, "transaction_date" datetime NOT NULL, "transaction_description" text, "entity_uuid" varchar, "entity_type_id" integer, "active" integer NOT NULL DEFAULT (**********), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_user_event_time_at" ON "user_event" ("time_at") `);
        await queryRunner.query(`CREATE INDEX "idx_user_event_user_device_id" ON "user_event" ("user_device_id") `);
        await queryRunner.query(`CREATE INDEX "idx_asset_asset_type_id" ON "asset" ("asset_type_id") `);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "idx_user_event_time_at"`);
        await queryRunner.query(`DROP INDEX "idx_user_event_user_device_id"`);
        await queryRunner.query(`DROP INDEX "idx_asset_asset_type_id"`);
        await queryRunner.query(`DROP TABLE "entity_status"`);
        await queryRunner.query(`DROP TABLE "asset"`);
        await queryRunner.query(`DROP TABLE "asset_classification"`);
        await queryRunner.query(`DROP TABLE "asset_feature"`);
        await queryRunner.query(`DROP TABLE "asset_feature_classification"`);
        await queryRunner.query(`DROP TABLE "ref_period"`);
        await queryRunner.query(`DROP TABLE "transaction_ledger"`);
    }
}
