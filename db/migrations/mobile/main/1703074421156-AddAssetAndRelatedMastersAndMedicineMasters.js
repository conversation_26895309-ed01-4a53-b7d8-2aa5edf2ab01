const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddAssetAndRelatedMastersAndMedicineMasters1703074421156 {
    name = 'AddAssetAndRelatedMastersAndMedicineMasters1703074421156'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "ref_medicine" ("medicine_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "medicine_name" varchar(256), "medicine_name_l10n" text, "medicine_information" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "ref_medicine_classification" ("medicine_classification_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "medicine_id" integer NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_medicine_classification_medicine_id" ON "ref_medicine_classification" ("medicine_id") `);
        await queryRunner.query(`CREATE INDEX "idx_medicine_classification_classifier_id" ON "ref_medicine_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE TABLE "ref_crop" ("crop_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "crop_name" varchar(256), "crop_name_l10n" text, "crop_information" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "ref_crop_classification" ("crop_classification_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "crop_id" integer NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_crop_classification_crop_id" ON "ref_crop_classification" ("crop_id") `);
        await queryRunner.query(`CREATE INDEX "idx_crop_classification_classifier_id" ON "ref_crop_classification" ("classifier_id") `);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "idx_crop_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "idx_crop_classification_crop_id"`);
        await queryRunner.query(`DROP TABLE "ref_crop_classification"`);
        await queryRunner.query(`DROP TABLE "ref_crop"`);
        await queryRunner.query(`DROP INDEX "idx_medicine_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "idx_medicine_classification_medicine_id"`);
        await queryRunner.query(`DROP TABLE "ref_medicine_classification"`);
        await queryRunner.query(`DROP TABLE "ref_medicine"`);
    }
}
