const { MigrationInterface, QueryRunner } = require('typeorm')

const InitialDesign1672979849177 = require("./1672979849177-InitialDesign");
const CreateSDTVView1674876937211 = require("./1674876937211-CreateSDTVView");
const AddTableUserEvent1679054201974 = require('./1679054201974-AddTableUserEvent')
const AddProcessedColumnToUserEvent1681465524868 = require('./1681465524868-AddProcessedColumnToUserEvent')
const AddIndices1687336688548 = require("./1687336688548-AddIndices")
const AddRefDiagnosis = require("./1690799159208-add_ref_diagnosis")
const AddSlaColumnsToCareCalendar = require("./1693292172349-ModifyCareCalendarAddAdditionalColumnsForSLAMgmt")
const assetsEntry = require("./1701443354451-main")
const AddAssetAndRelatedMastersAndMedicineMasters = require('./1703074421156-AddAssetAndRelatedMastersAndMedicineMasters')
const AddCustomerFeatureRelatedTables = require('./1703586198882-AddCustomerFeatureRelatedTables')
const AddQualifier1ToEntityRelationshipTable = require('./1703744998919-AddQualifier1ToEntityRelationshipTable')
const AddPartnerTables1704889488382 = require('./1704889488382-AddPartnerTables')
const ModifyAssetFeatureAddAssetClassificationId1706003937411 = require('./1706003937411-ModifyAssetFeatureAddAssetClassificationId')
const AddTablesRelatedToGenericGeography1706411120929 = require('./1706411120929-AddTablesRelatedToGenericGeography')
const InventoryRelease1709901449443 = require('./1709901449443-InventoryRelease1709901449443')
const UpdateRefActivity1710493967813 = require('./1710493967813-UpdateRefActivity')
const RequisitionInsertedAt1711534426529 = require('./1711534426529-RequisitionInsertedAt')
const MiscUpdates1711974529620 = require('./1711974529620-MiscUpdates')
const TaskQuestion = require("./1723799921070-main")
const TaskQuestionScoreWeightage = require("./1728552690378-main")
const mainMobileMigrations = [
  InitialDesign1672979849177,
  CreateSDTVView1674876937211,
  AddTableUserEvent1679054201974, 
  AddProcessedColumnToUserEvent1681465524868,
  AddIndices1687336688548,
  AddRefDiagnosis,
  AddSlaColumnsToCareCalendar,
  assetsEntry,
  AddAssetAndRelatedMastersAndMedicineMasters,
  AddCustomerFeatureRelatedTables,
  AddQualifier1ToEntityRelationshipTable,
  AddPartnerTables1704889488382,
  ModifyAssetFeatureAddAssetClassificationId1706003937411,
  AddTablesRelatedToGenericGeography1706411120929,
  InventoryRelease1709901449443,
  UpdateRefActivity1710493967813,
  RequisitionInsertedAt1711534426529,
  MiscUpdates1711974529620,
  TaskQuestion,
  TaskQuestionScoreWeightage
];

module.exports = { mainMobileMigrations };
