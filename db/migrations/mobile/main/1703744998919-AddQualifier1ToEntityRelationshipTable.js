const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddQualifier1ToEntityRelationshipTable1703744998919 {
    name = 'AddQualifier1ToEntityRelationshipTable1703744998919'

    async up(queryRunner) {
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_1_entity_id"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_1_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_2_entity_type"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_2_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_relationship_type_id"`);
        await queryRunner.query(`CREATE TABLE "temporary_entity_relationship" ("entity_relationship_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "entity_relationship_type_id" integer, "entity_1_type_id" integer, "entity_1_entity_uuid" varchar, "entity_1_entity_id" integer, "entity_2_type_id" integer, "entity_2_entity_uuid" varchar, "entity_2_entity_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "entity_information" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "qualifier_1_type_id" integer, "qualifier_1_entity_uuid" varchar, "qualifier_1_entity_id" integer)`);
        await queryRunner.query(`INSERT INTO "temporary_entity_relationship"("entity_relationship_id", "entity_relationship_type_id", "entity_1_type_id", "entity_1_entity_uuid", "entity_1_entity_id", "entity_2_type_id", "entity_2_entity_uuid", "entity_2_entity_id", "start_date", "end_date", "entity_information", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "entity_relationship_id", "entity_relationship_type_id", "entity_1_type_id", "entity_1_entity_uuid", "entity_1_entity_id", "entity_2_type_id", "entity_2_entity_uuid", "entity_2_entity_id", "start_date", "end_date", "entity_information", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "entity_relationship"`);
        await queryRunner.query(`DROP TABLE "entity_relationship"`);
        await queryRunner.query(`ALTER TABLE "temporary_entity_relationship" RENAME TO "entity_relationship"`);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_1_entity_id" ON "entity_relationship" ("entity_1_entity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_1_entity_uuid" ON "entity_relationship" ("entity_1_entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_2_entity_type" ON "entity_relationship" ("entity_2_entity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_2_entity_uuid" ON "entity_relationship" ("entity_2_entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_relationship_type_id" ON "entity_relationship" ("entity_relationship_type_id") `);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_relationship_type_id"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_2_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_2_entity_type"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_1_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_1_entity_id"`);
        await queryRunner.query(`ALTER TABLE "entity_relationship" RENAME TO "temporary_entity_relationship"`);
        await queryRunner.query(`CREATE TABLE "entity_relationship" ("entity_relationship_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "entity_relationship_type_id" integer, "entity_1_type_id" integer, "entity_1_entity_uuid" varchar, "entity_1_entity_id" integer, "entity_2_type_id" integer, "entity_2_entity_uuid" varchar, "entity_2_entity_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "entity_information" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "entity_relationship"("entity_relationship_id", "entity_relationship_type_id", "entity_1_type_id", "entity_1_entity_uuid", "entity_1_entity_id", "entity_2_type_id", "entity_2_entity_uuid", "entity_2_entity_id", "start_date", "end_date", "entity_information", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "entity_relationship_id", "entity_relationship_type_id", "entity_1_type_id", "entity_1_entity_uuid", "entity_1_entity_id", "entity_2_type_id", "entity_2_entity_uuid", "entity_2_entity_id", "start_date", "end_date", "entity_information", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "temporary_entity_relationship"`);
        await queryRunner.query(`DROP TABLE "temporary_entity_relationship"`);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_relationship_type_id" ON "entity_relationship" ("entity_relationship_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_2_entity_uuid" ON "entity_relationship" ("entity_2_entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_2_entity_type" ON "entity_relationship" ("entity_2_entity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_1_entity_uuid" ON "entity_relationship" ("entity_1_entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_1_entity_id" ON "entity_relationship" ("entity_1_entity_id") `);
    }
}
