const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1728541082294 {
    name = 'Main1728541082294'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "ref_reference_category" ("reference_category_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "reference_name" varchar(256) NOT NULL, "reference_category_name_l10n" text, "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "ref_reference" ("reference_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "reference_category_id" integer NOT NULL, "field_name" varchar(256), "reference_name" varchar(256) NOT NULL, "reference_name_l10n" text, "reference_information" text, "active" integer NOT NULL DEFAULT (**********), "type_of_data" varchar(256), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), CONSTRAINT "UQ_670ee86f995838d8f03081b7d84" UNIQUE ("field_name"))`);
        await queryRunner.query(`CREATE TABLE "ref_activity" ("activity_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "activity_name" varchar NOT NULL, "activity_category" integer NOT NULL, "activity_name_l10n" text, "activity_information" text, "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), CONSTRAINT "UQ_REF_ACTIVITY_ACTIVITY_NAME" UNIQUE ("activity_name", "activity_category"))`);
        await queryRunner.query(`CREATE TABLE "ref_diagnosis" ("reference_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "category" integer NOT NULL, "reference_name_l10n" text, "reference_information" text, "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "ref_state" ("state_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "state_census_id" integer, "state_name_l10n" text, "state_short_code" varchar(256), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "ref_district" ("district_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "state_id" integer NOT NULL, "district_census_id" integer, "district_name_l10n" text, "district_short_code" varchar(256), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_ref_district_state_id" ON "ref_district" ("state_id") `);
        await queryRunner.query(`CREATE TABLE "ref_taluk" ("taluk_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "district_id" integer NOT NULL, "taluk_census_id" integer, "taluk_name_l10n" text, "taluk_short_code" varchar(256), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_ref_taluk_district_id" ON "ref_taluk" ("district_id") `);
        await queryRunner.query(`CREATE TABLE "ref_village" ("village_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "taluk_id" integer NOT NULL, "village_census_id" integer, "village_name_l10n" text, "village_short_code" varchar(256), "village_pin_code" varchar(256), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_ref_village_taluk_id" ON "ref_village" ("taluk_id") `);
        await queryRunner.query(`CREATE TABLE "ref_geography" ("geography_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "geography_type_id" integer, "geography_census_id" integer, "geography_name_l10n" text, "geography_short_code" varchar(256), "geography_geojson" text, "geography_information" text, "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "ref_geography_classification" ("geography_classification_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "geography_id" integer NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_geography_classification_geography_id" ON "ref_geography_classification" ("geography_id") `);
        await queryRunner.query(`CREATE INDEX "idx_geography_classification_classifier_id" ON "ref_geography_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE TABLE "ref_medicine" ("medicine_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "medicine_name" varchar(256), "medicine_name_l10n" text, "medicine_information" text, "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "ref_medicine_classification" ("medicine_classification_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "medicine_id" integer NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_medicine_classification_medicine_id" ON "ref_medicine_classification" ("medicine_id") `);
        await queryRunner.query(`CREATE INDEX "idx_medicine_classification_classifier_id" ON "ref_medicine_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE TABLE "ref_crop" ("crop_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "crop_name" varchar(256), "crop_name_l10n" text, "crop_information" text, "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "ref_crop_classification" ("crop_classification_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "crop_id" integer NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_crop_classification_crop_id" ON "ref_crop_classification" ("crop_id") `);
        await queryRunner.query(`CREATE INDEX "idx_crop_classification_classifier_id" ON "ref_crop_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE TABLE "ref_period" ("period_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "period_type_id" integer, "period_name" varchar(256), "period_name_l10n" text, "period_information" text, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "entity_status" ("entity_status_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "status_category_id" integer, "status_id" integer, "entity_1_type_id" integer, "entity_1_entity_uuid" varchar, "entity_1_entity_id" integer, "entity_2_type_id" integer, "entity_2_entity_uuid" varchar, "entity_2_entity_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "status_information" text, "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "document" ("document_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "document_type_id" integer NOT NULL, "document_name_l10n" text, "document_name" varchar(256), "document_status_id" integer, "entity_1_type_id" integer, "entity_1_entity_uuid" varchar, "entity_1_entity_id" integer, "entity_2_type_id" integer, "entity_2_entity_uuid" varchar, "entity_2_entity_id" integer, "entity_3_type_id" integer, "entity_3_entity_uuid" varchar, "entity_3_entity_id" integer, "client_document_information" text, "document_information" text, "document_meta_data_information" text, "document_sync_status" integer NOT NULL DEFAULT (1000102002), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_document_entity_1_id" ON "document" ("entity_1_entity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_document_entity_1_entity_uuid" ON "document" ("entity_1_entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_document_entity_1_entity_id" ON "document" ("entity_1_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_document_document_type_id" ON "document" ("document_type_id") `);
        await queryRunner.query(`CREATE TABLE "entity_relationship" ("entity_relationship_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "entity_relationship_type_id" integer, "entity_1_type_id" integer, "entity_1_entity_uuid" varchar, "entity_1_entity_id" integer, "entity_2_type_id" integer, "entity_2_entity_uuid" varchar, "entity_2_entity_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "qualifier_1_type_id" integer, "qualifier_1_entity_uuid" varchar, "qualifier_1_entity_id" integer, "entity_information" text, "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_relationship_type_id" ON "entity_relationship" ("entity_relationship_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_2_entity_uuid" ON "entity_relationship" ("entity_2_entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_2_entity_type" ON "entity_relationship" ("entity_2_entity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_1_entity_uuid" ON "entity_relationship" ("entity_1_entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_1_entity_id" ON "entity_relationship" ("entity_1_entity_id") `);
        await queryRunner.query(`CREATE TABLE "entity_geography" ("entity_geography_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "entity_type_id" integer, "entity_uuid" varchar, "entity_id" integer, "geography_type_id" integer, "geography_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_entity_geography_geography_id" ON "entity_geography" ("geography_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_geography_entity_uuid" ON "entity_geography" ("entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_geography_entity_type_id" ON "entity_geography" ("entity_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_geography_entity_id" ON "entity_geography" ("entity_id") `);
        await queryRunner.query(`CREATE TABLE "staff" ("staff_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "staff_type_id" integer, "staff_visual_id" varchar(256), "staff_name_l10n" text, "mobile_number" varchar(256), "email_address" varchar(256), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_staff_staff_type_id" ON "staff" ("staff_type_id") `);
        await queryRunner.query(`CREATE TABLE "staff_classification" ("staff_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "staff_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "customer" ("customer_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "customer_type_id" integer DEFAULT (1000220001), "customer_visual_id" varchar(256), "customer_name_l10n" text, "mobile_number" varchar(256), "email_address" varchar(256), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "created_at_sort_index" ON "customer" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "idx_customer_active" ON "customer" ("active") `);
        await queryRunner.query(`CREATE TABLE "customer_classification" ("customer_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "customer_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_customer_classification_customer_id" ON "customer_classification" ("customer_id") `);
        await queryRunner.query(`CREATE INDEX "idx_customer_classification_classifier_id" ON "customer_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE INDEX "idx_customer_classification_active" ON "customer_classification" ("active") `);
        await queryRunner.query(`CREATE INDEX "idx_customer_classification_value_reference_id" ON "customer_classification" ("value_reference_id") `);
        await queryRunner.query(`CREATE TABLE "customer_feature" ("customer_feature_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "customer_id" varchar NOT NULL, "feature_name_l10n" text, "feature_type_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "customer_feature_classification" ("customer_feature_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "customer_feature_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "user_event" ("user_event_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "user_device_id" integer NOT NULL, "location_tracking" integer, "lat" decimal(14,10), "lng" decimal(14,10), "time_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "additional_data" text, "processed" integer DEFAULT (0), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_user_event_time_at" ON "user_event" ("time_at") `);
        await queryRunner.query(`CREATE INDEX "idx_user_event_user_device_id" ON "user_event" ("user_device_id") `);
        await queryRunner.query(`CREATE TABLE "animal" ("animal_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "animal_visual_id" varchar(256), "animal_type" integer, "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_animal_animal_type_id" ON "animal" ("animal_type") `);
        await queryRunner.query(`CREATE INDEX "idx_animal_active" ON "animal" ("active") `);
        await queryRunner.query(`CREATE TABLE "animal_classification" ("animal_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "animal_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_animal_classification_classifier_id" ON "animal_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE INDEX "idx_animal_classification_animal_id" ON "animal_classification" ("animal_id") `);
        await queryRunner.query(`CREATE INDEX "idx_animal_classification_active" ON "animal_classification" ("active") `);
        await queryRunner.query(`CREATE TABLE "animal_disease" ("animal_disease_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "animal_id" varchar, "disease_id" integer, "disease_date" datetime, "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "animal_disease_classification" ("animal_disease_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "animal_disease_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "asset" ("asset_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "asset_visual_id" varchar(256), "asset_name_l10n" text, "asset_type_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_asset_asset_type_id" ON "asset" ("asset_type_id") `);
        await queryRunner.query(`CREATE TABLE "asset_classification" ("asset_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "asset_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "asset_feature" ("asset_feature_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "asset_id" varchar, "asset_classification_id" varchar, "feature_name_l10n" text, "feature_type_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "asset_feature_classification" ("asset_feature_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "asset_feature_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "care_calendar" ("care_calendar_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "entity_type_id" integer NOT NULL, "entity_id" integer, "entity_uuid" varchar, "activity_id" integer NOT NULL, "visit_id" integer, "activity_date" datetime NOT NULL, "calendar_activity_status" integer NOT NULL DEFAULT (1000300001), "completion_date" datetime, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "system_task_completion_time" datetime, "visit_schedule_time" datetime, "visit_creation_time" datetime, "user_task_completion_time" datetime, "activity_created_by" varchar, "activity_completed_by" varchar, "active" integer NOT NULL DEFAULT (**********))`);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_entity_uuid" ON "care_calendar" ("entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_entity_type_id" ON "care_calendar" ("entity_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_entity_id" ON "care_calendar" ("entity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_calendar_activity_status" ON "care_calendar" ("calendar_activity_status") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_activity_id" ON "care_calendar" ("activity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_activity_date_desc" ON "care_calendar" ("activity_date") `);
        await queryRunner.query(`CREATE TABLE "care_calendar_classification" ("care_calendar_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "care_calendar_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_classification_classifier_id" ON "care_calendar_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_classification_cc_id" ON "care_calendar_classification" ("care_calendar_id") `);
        await queryRunner.query(`CREATE TABLE "dump_doc" ("dump_document_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "document_key" varchar NOT NULL, "error" text, "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "note" ("note_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "note_type_id" integer, "note_status_id" integer, "note_time" datetime, "note" text NOT NULL, "entity_1_type_id" integer, "entity_1_uuid" varchar, "entity_1_id" integer, "entity_2_type_id" integer, "entity_2_uuid" varchar, "entity_2_id" integer, "entity_3_type_id" integer, "entity_3_uuid" varchar, "entity_3_id" integer, "entity_4_type_id" integer, "entity_4_uuid" varchar, "entity_4_id" integer, "creator_uuid" varchar, "creator_id" integer, "creator_type_id" integer, "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "staff_activity" ("staff_activity_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "staff_id" varchar, "staff_activity_type_id" integer NOT NULL, "visit_id" varchar, "activity_id" integer NOT NULL, "activity_duration" datetime NOT NULL, "entity_type_id" integer NOT NULL, "entity_id" varchar NOT NULL, "care_calendar" varchar NOT NULL, "ticket_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_staff_activity_staff_id" ON "staff_activity" ("staff_id") `);
        await queryRunner.query(`CREATE INDEX "idx_staff_activity_care_calendar_id" ON "staff_activity" ("care_calendar") `);
        await queryRunner.query(`CREATE TABLE "ticket" ("ticket_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "ticket_status" varchar(256) NOT NULL DEFAULT (1000300001), "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "transaction_ledger" ("transaction_ledger_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "account_id" varchar(256), "staff_uuid" varchar NOT NULL, "amount" decimal(14,4) NOT NULL, "transaction_type" integer NOT NULL, "transaction_category" integer NOT NULL, "transaction_date" datetime NOT NULL, "transaction_description" text, "entity_uuid" varchar, "entity_type_id" integer, "active" integer NOT NULL DEFAULT (**********), "data_sync_status" integer NOT NULL DEFAULT (**********), "entity_name" varchar(256), "entity_service" varchar(256), "transaction_status" integer, "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "requisition" ("requisition_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "requisitioner_type_id" integer, "requisitioner_uuid" varchar, "fullfiller_type_id" integer, "fullfiller_uuid" varchar, "approver_type_id" integer, "approver_uuid" varchar, "line_items_count" integer DEFAULT (0), "status_id" integer DEFAULT (1010000001), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "inserted_at" datetime)`);
        await queryRunner.query(`CREATE INDEX "idx_requisition_status_id" ON "requisition" ("status_id") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_requisitioner_uuid" ON "requisition" ("requisitioner_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_approver_uuid" ON "requisition" ("approver_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_fullfiller_uuid" ON "requisition" ("fullfiller_uuid") `);
        await queryRunner.query(`CREATE TABLE "requisition_classification" ("requisition_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "requisition_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "requisition_line_items" ("requisition_line_items_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "requisition_id" varchar, "requisitioner_uuid" varchar, "medicine_id" integer, "unit" varchar, "quantity" decimal(14,4) DEFAULT (0), "item_status_id" integer DEFAULT (0), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_requisitionlineitems_requisition_id" ON "requisition_line_items" ("requisition_id") `);
        await queryRunner.query(`CREATE TABLE "inventory_ledger" ("inventory_ledger_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "requisition_id" varchar, "medicine_id" integer, "warehouse_type_id" integer, "warehouse_uuid" varchar, "source_type_id" integer, "source_uuid" varchar, "consumer_type_id" integer DEFAULT (0), "consumer_uuid" varchar, "unit" varchar, "credit_qty" decimal(14,4) NOT NULL DEFAULT (0), "debit_qty" decimal(14,4) NOT NULL DEFAULT (0), "balance" decimal(14,4) NOT NULL DEFAULT (0), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "inserted_at" datetime)`);
        await queryRunner.query(`CREATE INDEX "idx_inventoryledger_source_uuid" ON "inventory_ledger" ("source_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_inventoryledger_warehouse_uuid" ON "inventory_ledger" ("warehouse_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_inventoryledger_inserted_at" ON "inventory_ledger" ("inserted_at") `);
        await queryRunner.query(`CREATE TABLE "warehouse_blockqty" ("warehouse_blockqty_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "source_type_id" integer, "source_uuid" varchar, "warehouse_type_id" integer, "warehouse_uuid" varchar, "medicine_id" integer, "unit" varchar, "blocked_qty" decimal(14,4) DEFAULT (0), "unblocked_qty" decimal(14,4) DEFAULT (0), "balance_qty" decimal(14,4) DEFAULT (0), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_warehouseblockqty_warehouse_uuid" ON "warehouse_blockqty" ("warehouse_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_warehouseblockqty_source_uuid" ON "warehouse_blockqty" ("source_uuid") `);
        await queryRunner.query(`CREATE TABLE "partner" ("partner_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "partner_type_id" integer, "partner_visual_id" varchar(256), "partner_name_l10n" text, "mobile_number" varchar(256), "email_address" varchar(256), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "partner_classification" ("partner_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "partner_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "ref_activity_classification" ("activity_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "activity_id" integer NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_ref_activity_classification_classifier_id" ON "ref_activity_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE INDEX "idx_ref_activity_classification_activity_id" ON "ref_activity_classification" ("activity_id") `);
        await queryRunner.query(`CREATE TABLE "task_question" ("question_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "question_name" varchar(256) NOT NULL, "question_name_l10n" text, "topic_name" varchar(256) NOT NULL, "topic_name_l10n" text, "topic_priority" integer, "topic_question_priority" integer, "priority_grouping" integer, "form_configuration" text, "frequency" varchar(256) NOT NULL, "farmer_input_required" integer, "review_date_config" integer, "active" integer NOT NULL DEFAULT (**********), "weightage" integer, "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "task_question_data" ("question_data_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "care_calendar_id" varchar NOT NULL, "question_uuid" varchar NOT NULL, "form_configuration" text, "review_date" varchar, "priority_grouping" integer, "active" integer NOT NULL DEFAULT (**********), "data_sync_status" integer NOT NULL DEFAULT (**********), "saved_at" datetime, "weightage" integer, "score" decimal(3,2), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "task_question_data"`);
        await queryRunner.query(`DROP TABLE "task_question"`);
        await queryRunner.query(`DROP INDEX "idx_ref_activity_classification_activity_id"`);
        await queryRunner.query(`DROP INDEX "idx_ref_activity_classification_classifier_id"`);
        await queryRunner.query(`DROP TABLE "ref_activity_classification"`);
        await queryRunner.query(`DROP TABLE "partner_classification"`);
        await queryRunner.query(`DROP TABLE "partner"`);
        await queryRunner.query(`DROP INDEX "idx_warehouseblockqty_source_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_warehouseblockqty_warehouse_uuid"`);
        await queryRunner.query(`DROP TABLE "warehouse_blockqty"`);
        await queryRunner.query(`DROP INDEX "idx_inventoryledger_inserted_at"`);
        await queryRunner.query(`DROP INDEX "idx_inventoryledger_warehouse_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_inventoryledger_source_uuid"`);
        await queryRunner.query(`DROP TABLE "inventory_ledger"`);
        await queryRunner.query(`DROP INDEX "idx_requisitionlineitems_requisition_id"`);
        await queryRunner.query(`DROP TABLE "requisition_line_items"`);
        await queryRunner.query(`DROP TABLE "requisition_classification"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_fullfiller_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_approver_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_requisitioner_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_status_id"`);
        await queryRunner.query(`DROP TABLE "requisition"`);
        await queryRunner.query(`DROP TABLE "transaction_ledger"`);
        await queryRunner.query(`DROP TABLE "ticket"`);
        await queryRunner.query(`DROP INDEX "idx_staff_activity_care_calendar_id"`);
        await queryRunner.query(`DROP INDEX "idx_staff_activity_staff_id"`);
        await queryRunner.query(`DROP TABLE "staff_activity"`);
        await queryRunner.query(`DROP TABLE "note"`);
        await queryRunner.query(`DROP TABLE "dump_doc"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_classification_cc_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_classification_classifier_id"`);
        await queryRunner.query(`DROP TABLE "care_calendar_classification"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_activity_date_desc"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_activity_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_calendar_activity_status"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_entity_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_entity_type_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_entity_uuid"`);
        await queryRunner.query(`DROP TABLE "care_calendar"`);
        await queryRunner.query(`DROP TABLE "asset_feature_classification"`);
        await queryRunner.query(`DROP TABLE "asset_feature"`);
        await queryRunner.query(`DROP TABLE "asset_classification"`);
        await queryRunner.query(`DROP INDEX "idx_asset_asset_type_id"`);
        await queryRunner.query(`DROP TABLE "asset"`);
        await queryRunner.query(`DROP TABLE "animal_disease_classification"`);
        await queryRunner.query(`DROP TABLE "animal_disease"`);
        await queryRunner.query(`DROP INDEX "idx_animal_classification_active"`);
        await queryRunner.query(`DROP INDEX "idx_animal_classification_animal_id"`);
        await queryRunner.query(`DROP INDEX "idx_animal_classification_classifier_id"`);
        await queryRunner.query(`DROP TABLE "animal_classification"`);
        await queryRunner.query(`DROP INDEX "idx_animal_active"`);
        await queryRunner.query(`DROP INDEX "idx_animal_animal_type_id"`);
        await queryRunner.query(`DROP TABLE "animal"`);
        await queryRunner.query(`DROP INDEX "idx_user_event_user_device_id"`);
        await queryRunner.query(`DROP INDEX "idx_user_event_time_at"`);
        await queryRunner.query(`DROP TABLE "user_event"`);
        await queryRunner.query(`DROP TABLE "customer_feature_classification"`);
        await queryRunner.query(`DROP TABLE "customer_feature"`);
        await queryRunner.query(`DROP INDEX "idx_customer_classification_value_reference_id"`);
        await queryRunner.query(`DROP INDEX "idx_customer_classification_active"`);
        await queryRunner.query(`DROP INDEX "idx_customer_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "idx_customer_classification_customer_id"`);
        await queryRunner.query(`DROP TABLE "customer_classification"`);
        await queryRunner.query(`DROP INDEX "idx_customer_active"`);
        await queryRunner.query(`DROP INDEX "created_at_sort_index"`);
        await queryRunner.query(`DROP TABLE "customer"`);
        await queryRunner.query(`DROP TABLE "staff_classification"`);
        await queryRunner.query(`DROP INDEX "idx_staff_staff_type_id"`);
        await queryRunner.query(`DROP TABLE "staff"`);
        await queryRunner.query(`DROP INDEX "idx_entity_geography_entity_id"`);
        await queryRunner.query(`DROP INDEX "idx_entity_geography_entity_type_id"`);
        await queryRunner.query(`DROP INDEX "idx_entity_geography_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_entity_geography_geography_id"`);
        await queryRunner.query(`DROP TABLE "entity_geography"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_1_entity_id"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_1_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_2_entity_type"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_2_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_relationship_type_id"`);
        await queryRunner.query(`DROP TABLE "entity_relationship"`);
        await queryRunner.query(`DROP INDEX "idx_document_document_type_id"`);
        await queryRunner.query(`DROP INDEX "idx_document_entity_1_entity_id"`);
        await queryRunner.query(`DROP INDEX "idx_document_entity_1_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_document_entity_1_id"`);
        await queryRunner.query(`DROP TABLE "document"`);
        await queryRunner.query(`DROP TABLE "entity_status"`);
        await queryRunner.query(`DROP TABLE "ref_period"`);
        await queryRunner.query(`DROP INDEX "idx_crop_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "idx_crop_classification_crop_id"`);
        await queryRunner.query(`DROP TABLE "ref_crop_classification"`);
        await queryRunner.query(`DROP TABLE "ref_crop"`);
        await queryRunner.query(`DROP INDEX "idx_medicine_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "idx_medicine_classification_medicine_id"`);
        await queryRunner.query(`DROP TABLE "ref_medicine_classification"`);
        await queryRunner.query(`DROP TABLE "ref_medicine"`);
        await queryRunner.query(`DROP INDEX "idx_geography_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "idx_geography_classification_geography_id"`);
        await queryRunner.query(`DROP TABLE "ref_geography_classification"`);
        await queryRunner.query(`DROP TABLE "ref_geography"`);
        await queryRunner.query(`DROP INDEX "idx_ref_village_taluk_id"`);
        await queryRunner.query(`DROP TABLE "ref_village"`);
        await queryRunner.query(`DROP INDEX "idx_ref_taluk_district_id"`);
        await queryRunner.query(`DROP TABLE "ref_taluk"`);
        await queryRunner.query(`DROP INDEX "idx_ref_district_state_id"`);
        await queryRunner.query(`DROP TABLE "ref_district"`);
        await queryRunner.query(`DROP TABLE "ref_state"`);
        await queryRunner.query(`DROP TABLE "ref_diagnosis"`);
        await queryRunner.query(`DROP TABLE "ref_activity"`);
        await queryRunner.query(`DROP TABLE "ref_reference"`);
        await queryRunner.query(`DROP TABLE "ref_reference_category"`);
    }
}
