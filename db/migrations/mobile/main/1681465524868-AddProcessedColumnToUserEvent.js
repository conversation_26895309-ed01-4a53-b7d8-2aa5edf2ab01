const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddProcessedColumnToUserEvent1681465524868 {
    name = 'AddProcessedColumnToUserEvent1681465524868'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "temporary_user_event" ("user_event_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "user_device_id" integer NOT NULL, "location_tracking" integer NOT NULL, "lat" decimal(14,10), "lng" decimal(14,10), "time_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "additional_data" text, "processed" integer DEFAULT (0), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "temporary_user_event"("user_event_id", "user_device_id", "location_tracking", "lat", "lng", "time_at", "additional_data", "processed", "data_sync_status", "created_at", "updated_at") SELECT "user_event_id", "user_device_id", "location_tracking", "lat", "lng", "time_at", "additional_data", "processed", "data_sync_status", "created_at", "updated_at" FROM "user_event"`);
        await queryRunner.query(`DROP TABLE "user_event"`);
        await queryRunner.query(`ALTER TABLE "temporary_user_event" RENAME TO "user_event"`);
        await queryRunner.query(`CREATE TABLE "temporary_user_event" ("user_event_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "user_device_id" integer NOT NULL, "location_tracking" integer, "lat" decimal(14,10), "lng" decimal(14,10), "time_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "additional_data" text, "processed" integer DEFAULT (0), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "temporary_user_event"("user_event_id", "user_device_id", "location_tracking", "lat", "lng", "time_at", "additional_data", "processed", "data_sync_status", "created_at", "updated_at") SELECT "user_event_id", "user_device_id", "location_tracking", "lat", "lng", "time_at", "additional_data", "processed", "data_sync_status", "created_at", "updated_at" FROM "user_event"`);
        await queryRunner.query(`DROP TABLE "user_event"`);
        await queryRunner.query(`ALTER TABLE "temporary_user_event" RENAME TO "user_event"`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "user_event" RENAME TO "temporary_user_event"`);
        await queryRunner.query(`CREATE TABLE "user_event" ("user_event_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "user_device_id" integer NOT NULL, "location_tracking" integer NOT NULL, "lat" decimal(14,10), "lng" decimal(14,10), "time_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "additional_data" text, "processed" integer DEFAULT (0), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "user_event"("user_event_id", "user_device_id", "location_tracking", "lat", "lng", "time_at", "additional_data", "processed", "data_sync_status", "created_at", "updated_at") SELECT "user_event_id", "user_device_id", "location_tracking", "lat", "lng", "time_at", "additional_data", "processed", "data_sync_status", "created_at", "updated_at" FROM "temporary_user_event"`);
        await queryRunner.query(`DROP TABLE "temporary_user_event"`);
        await queryRunner.query(`ALTER TABLE "user_event" RENAME TO "temporary_user_event"`);
        await queryRunner.query(`CREATE TABLE "user_event" ("user_event_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "user_device_id" integer NOT NULL, "location_tracking" integer NOT NULL, "lat" decimal(14,10), "lng" decimal(14,10), "time_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "additional_data" text, "processed" integer DEFAULT (0), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "user_event"("user_event_id", "user_device_id", "location_tracking", "lat", "lng", "time_at", "additional_data", "processed", "data_sync_status", "created_at", "updated_at") SELECT "user_event_id", "user_device_id", "location_tracking", "lat", "lng", "time_at", "additional_data", "processed", "data_sync_status", "created_at", "updated_at" FROM "temporary_user_event"`);
        await queryRunner.query(`DROP TABLE "temporary_user_event"`);
    }
}
