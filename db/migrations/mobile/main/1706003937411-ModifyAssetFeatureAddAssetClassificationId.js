const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class ModifyAssetFeatureAddAssetClassificationId1706003937411 {
    name = 'ModifyAssetFeatureAddAssetClassificationId1706003937411'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "temporary_asset_feature" ("asset_feature_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "asset_id" varchar NOT NULL, "feature_name_l10n" text, "feature_type_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "asset_classification_id" varchar)`);
        await queryRunner.query(`INSERT INTO "temporary_asset_feature"("asset_feature_id", "asset_id", "feature_name_l10n", "feature_type_id", "start_date", "end_date", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "asset_feature_id", "asset_id", "feature_name_l10n", "feature_type_id", "start_date", "end_date", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "asset_feature"`);
        await queryRunner.query(`DROP TABLE "asset_feature"`);
        await queryRunner.query(`ALTER TABLE "temporary_asset_feature" RENAME TO "asset_feature"`);
        await queryRunner.query(`CREATE TABLE "temporary_asset_feature" ("asset_feature_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "asset_id" varchar, "feature_name_l10n" text, "feature_type_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "asset_classification_id" varchar)`);
        await queryRunner.query(`INSERT INTO "temporary_asset_feature"("asset_feature_id", "asset_id", "feature_name_l10n", "feature_type_id", "start_date", "end_date", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "asset_classification_id") SELECT "asset_feature_id", "asset_id", "feature_name_l10n", "feature_type_id", "start_date", "end_date", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "asset_classification_id" FROM "asset_feature"`);
        await queryRunner.query(`DROP TABLE "asset_feature"`);
        await queryRunner.query(`ALTER TABLE "temporary_asset_feature" RENAME TO "asset_feature"`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "asset_feature" RENAME TO "temporary_asset_feature"`);
        await queryRunner.query(`CREATE TABLE "asset_feature" ("asset_feature_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "asset_id" varchar NOT NULL, "feature_name_l10n" text, "feature_type_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "asset_classification_id" varchar)`);
        await queryRunner.query(`INSERT INTO "asset_feature"("asset_feature_id", "asset_id", "feature_name_l10n", "feature_type_id", "start_date", "end_date", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "asset_classification_id") SELECT "asset_feature_id", "asset_id", "feature_name_l10n", "feature_type_id", "start_date", "end_date", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "asset_classification_id" FROM "temporary_asset_feature"`);
        await queryRunner.query(`DROP TABLE "temporary_asset_feature"`);
        await queryRunner.query(`ALTER TABLE "asset_feature" RENAME TO "temporary_asset_feature"`);
        await queryRunner.query(`CREATE TABLE "asset_feature" ("asset_feature_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "asset_id" varchar NOT NULL, "feature_name_l10n" text, "feature_type_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "asset_feature"("asset_feature_id", "asset_id", "feature_name_l10n", "feature_type_id", "start_date", "end_date", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "asset_feature_id", "asset_id", "feature_name_l10n", "feature_type_id", "start_date", "end_date", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "temporary_asset_feature"`);
        await queryRunner.query(`DROP TABLE "temporary_asset_feature"`);
    }
}
