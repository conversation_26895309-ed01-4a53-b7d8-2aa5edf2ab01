const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class UpdateRefActivity1710493967813 {
    name = 'UpdateRefActivity1710493967813'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "ref_activity_classification" ("activity_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "activity_id" integer NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_ref_activity_classification_classifier_id" ON "ref_activity_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE INDEX "idx_ref_activity_classification_activity_id" ON "ref_activity_classification" ("activity_id") `);
        await queryRunner.query(`CREATE TABLE "temporary_ref_activity" ("activity_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "activity_name" varchar NOT NULL, "activity_category" integer NOT NULL, "activity_name_l10n" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "activity_information" text)`);
        await queryRunner.query(`INSERT INTO "temporary_ref_activity"("activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "ref_activity"`);
        await queryRunner.query(`DROP TABLE "ref_activity"`);
        await queryRunner.query(`ALTER TABLE "temporary_ref_activity" RENAME TO "ref_activity"`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "ref_activity" RENAME TO "temporary_ref_activity"`);
        await queryRunner.query(`CREATE TABLE "ref_activity" ("activity_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "activity_name" varchar NOT NULL, "activity_category" integer NOT NULL, "activity_name_l10n" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "ref_activity"("activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "temporary_ref_activity"`);
        await queryRunner.query(`DROP TABLE "temporary_ref_activity"`);
        await queryRunner.query(`DROP INDEX "idx_ref_activity_classification_activity_id"`);
        await queryRunner.query(`DROP INDEX "idx_ref_activity_classification_classifier_id"`);
        await queryRunner.query(`DROP TABLE "ref_activity_classification"`);
    }
}
