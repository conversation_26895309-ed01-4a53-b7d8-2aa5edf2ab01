const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddTableUserEvent1679054201974 {
    name = 'AddTableUserEvent1679054201974'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "user_event" ("user_event_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "user_device_id" integer NOT NULL, "location_tracking" integer NOT NULL, "lat" decimal(14,10), "lng" decimal(14,10), "time_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "additional_data" text, "processed" integer NULL DEFAULT 0, "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "user_event"`);
    }
}
