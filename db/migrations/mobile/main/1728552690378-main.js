const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class TaskQuestionScoreWeightage {
    name = 'Main1728552690378'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "temporary_task_question" ("question_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "question_name" varchar(256) NOT NULL, "question_name_l10n" text, "topic_name" varchar(256) NOT NULL, "topic_name_l10n" text, "topic_priority" integer, "topic_question_priority" integer, "priority_grouping" integer, "form_configuration" text, "frequency" varchar(256) NOT NULL, "farmer_input_required" integer, "review_date_config" integer, "active" integer NOT NULL DEFAULT (1000100001), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "weightage" integer)`);
        await queryRunner.query(`INSERT INTO "temporary_task_question"("question_uuid", "question_name", "question_name_l10n", "topic_name", "topic_name_l10n", "topic_priority", "topic_question_priority", "priority_grouping", "form_configuration", "frequency", "farmer_input_required", "review_date_config", "active", "data_sync_status", "created_at", "updated_at") SELECT "question_uuid", "question_name", "question_name_l10n", "topic_name", "topic_name_l10n", "topic_priority", "topic_question_priority", "priority_grouping", "form_configuration", "frequency", "farmer_input_required", "review_date_config", "active", "data_sync_status", "created_at", "updated_at" FROM "task_question"`);
        await queryRunner.query(`DROP TABLE "task_question"`);
        await queryRunner.query(`ALTER TABLE "temporary_task_question" RENAME TO "task_question"`);
        await queryRunner.query(`CREATE TABLE "temporary_task_question_data" ("question_data_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "care_calendar_id" varchar NOT NULL, "question_uuid" varchar NOT NULL, "form_configuration" text, "review_date" varchar, "priority_grouping" integer, "active" integer NOT NULL DEFAULT (1000100001), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "saved_at" datetime, "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "weightage" integer, "score" decimal(3,2))`);
        await queryRunner.query(`INSERT INTO "temporary_task_question_data"("question_data_uuid", "care_calendar_id", "question_uuid", "form_configuration", "review_date", "priority_grouping", "active", "data_sync_status", "saved_at", "created_at", "updated_at") SELECT "question_data_uuid", "care_calendar_id", "question_uuid", "form_configuration", "review_date", "priority_grouping", "active", "data_sync_status", "saved_at", "created_at", "updated_at" FROM "task_question_data"`);
        await queryRunner.query(`DROP TABLE "task_question_data"`);
        await queryRunner.query(`ALTER TABLE "temporary_task_question_data" RENAME TO "task_question_data"`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "task_question_data" RENAME TO "temporary_task_question_data"`);
        await queryRunner.query(`CREATE TABLE "task_question_data" ("question_data_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "care_calendar_id" varchar NOT NULL, "question_uuid" varchar NOT NULL, "form_configuration" text, "review_date" varchar, "priority_grouping" integer, "active" integer NOT NULL DEFAULT (1000100001), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "saved_at" datetime, "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "task_question_data"("question_data_uuid", "care_calendar_id", "question_uuid", "form_configuration", "review_date", "priority_grouping", "active", "data_sync_status", "saved_at", "created_at", "updated_at") SELECT "question_data_uuid", "care_calendar_id", "question_uuid", "form_configuration", "review_date", "priority_grouping", "active", "data_sync_status", "saved_at", "created_at", "updated_at" FROM "temporary_task_question_data"`);
        await queryRunner.query(`DROP TABLE "temporary_task_question_data"`);
        await queryRunner.query(`ALTER TABLE "task_question" RENAME TO "temporary_task_question"`);
        await queryRunner.query(`CREATE TABLE "task_question" ("question_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "question_name" varchar(256) NOT NULL, "question_name_l10n" text, "topic_name" varchar(256) NOT NULL, "topic_name_l10n" text, "topic_priority" integer, "topic_question_priority" integer, "priority_grouping" integer, "form_configuration" text, "frequency" varchar(256) NOT NULL, "farmer_input_required" integer, "review_date_config" integer, "active" integer NOT NULL DEFAULT (1000100001), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "task_question"("question_uuid", "question_name", "question_name_l10n", "topic_name", "topic_name_l10n", "topic_priority", "topic_question_priority", "priority_grouping", "form_configuration", "frequency", "farmer_input_required", "review_date_config", "active", "data_sync_status", "created_at", "updated_at") SELECT "question_uuid", "question_name", "question_name_l10n", "topic_name", "topic_name_l10n", "topic_priority", "topic_question_priority", "priority_grouping", "form_configuration", "frequency", "farmer_input_required", "review_date_config", "active", "data_sync_status", "created_at", "updated_at" FROM "temporary_task_question"`);
        await queryRunner.query(`DROP TABLE "temporary_task_question"`);
    }
}
