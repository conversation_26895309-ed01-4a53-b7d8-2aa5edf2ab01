const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddTablesRelatedToGenericGeography1706411120929 {
    name = 'AddTablesRelatedToGenericGeography1706411120929'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "ref_geography" ("geography_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "geography_type_id" integer, "geography_census_id" integer, "geography_name_l10n" text, "geography_short_code" varchar(256), "geography_geojson" text, "geography_information" text, "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "ref_geography_classification" ("geography_classification_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "geography_id" integer NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (**********), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_geography_classification_geography_id" ON "ref_geography_classification" ("geography_id") `);
        await queryRunner.query(`CREATE INDEX "idx_geography_classification_classifier_id" ON "ref_geography_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE TABLE "temporary_transaction_ledger" ("transaction_ledger_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "account_id" varchar(256), "staff_uuid" varchar NOT NULL, "amount" decimal(14,4) NOT NULL, "transaction_type" integer NOT NULL, "transaction_category" integer NOT NULL, "transaction_date" datetime NOT NULL, "transaction_description" text, "entity_uuid" varchar, "entity_type_id" integer, "active" integer NOT NULL DEFAULT (**********), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "entity_name" varchar(256), "entity_service" varchar(256), "transaction_status" integer)`);
        await queryRunner.query(`INSERT INTO "temporary_transaction_ledger"("transaction_ledger_uuid", "account_id", "staff_uuid", "amount", "transaction_type", "transaction_category", "transaction_date", "transaction_description", "entity_uuid", "entity_type_id", "active", "data_sync_status", "created_at", "updated_at") SELECT "transaction_ledger_uuid", "account_id", "staff_uuid", "amount", "transaction_type", "transaction_category", "transaction_date", "transaction_description", "entity_uuid", "entity_type_id", "active", "data_sync_status", "created_at", "updated_at" FROM "transaction_ledger"`);
        await queryRunner.query(`DROP TABLE "transaction_ledger"`);
        await queryRunner.query(`ALTER TABLE "temporary_transaction_ledger" RENAME TO "transaction_ledger"`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "transaction_ledger" RENAME TO "temporary_transaction_ledger"`);
        await queryRunner.query(`CREATE TABLE "transaction_ledger" ("transaction_ledger_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "account_id" varchar(256), "staff_uuid" varchar NOT NULL, "amount" decimal(14,4) NOT NULL, "transaction_type" integer NOT NULL, "transaction_category" integer NOT NULL, "transaction_date" datetime NOT NULL, "transaction_description" text, "entity_uuid" varchar, "entity_type_id" integer, "active" integer NOT NULL DEFAULT (**********), "data_sync_status" integer NOT NULL DEFAULT (**********), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "transaction_ledger"("transaction_ledger_uuid", "account_id", "staff_uuid", "amount", "transaction_type", "transaction_category", "transaction_date", "transaction_description", "entity_uuid", "entity_type_id", "active", "data_sync_status", "created_at", "updated_at") SELECT "transaction_ledger_uuid", "account_id", "staff_uuid", "amount", "transaction_type", "transaction_category", "transaction_date", "transaction_description", "entity_uuid", "entity_type_id", "active", "data_sync_status", "created_at", "updated_at" FROM "temporary_transaction_ledger"`);
        await queryRunner.query(`DROP TABLE "temporary_transaction_ledger"`);
        await queryRunner.query(`DROP INDEX "idx_geography_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "idx_geography_classification_geography_id"`);
        await queryRunner.query(`DROP TABLE "ref_geography_classification"`);
        await queryRunner.query(`DROP TABLE "ref_geography"`);
    }
}
