const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddIndices1687336688548 {
    name = 'AddIndices1687336688548'

    async up(queryRunner) {
        await queryRunner.query(`CREATE INDEX "idx_ref_village_taluk_id" ON "ref_village" ("taluk_id") `);
        await queryRunner.query(`CREATE INDEX "idx_document_entity_1_id" ON "document" ("entity_1_entity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_document_entity_1_entity_uuid" ON "document" ("entity_1_entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_document_entity_1_entity_id" ON "document" ("entity_1_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_document_document_type_id" ON "document" ("document_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_relationship_type_id" ON "entity_relationship" ("entity_relationship_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_2_entity_uuid" ON "entity_relationship" ("entity_2_entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_2_entity_type" ON "entity_relationship" ("entity_2_entity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_1_entity_uuid" ON "entity_relationship" ("entity_1_entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_relationship_entity_1_entity_id" ON "entity_relationship" ("entity_1_entity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_geography_geography_id" ON "entity_geography" ("geography_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_geography_entity_uuid" ON "entity_geography" ("entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_geography_entity_type_id" ON "entity_geography" ("entity_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_entity_geography_entity_id" ON "entity_geography" ("entity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_staff_staff_type_id" ON "staff" ("staff_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_customer_classification_customer_id" ON "customer_classification" ("customer_id") `);
        await queryRunner.query(`CREATE INDEX "idx_customer_classification_classifier_id" ON "customer_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE INDEX "idx_animal_animal_type_id" ON "animal" ("animal_type") `);
        await queryRunner.query(`CREATE INDEX "idx_animal_classification_classifier_id" ON "animal_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE INDEX "idx_animal_classification_animal_id" ON "animal_classification" ("animal_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_entity_uuid" ON "care_calendar" ("entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_entity_type_id" ON "care_calendar" ("entity_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_entity_id" ON "care_calendar" ("entity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_calendar_activity_status" ON "care_calendar" ("calendar_activity_status") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_activity_id" ON "care_calendar" ("activity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_activity_date_desc" ON "care_calendar" ("activity_date") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_classification_classifier_id" ON "care_calendar_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_classification_cc_id" ON "care_calendar_classification" ("care_calendar_id") `);
        await queryRunner.query(`CREATE INDEX "idx_staff_activity_staff_id" ON "staff_activity" ("staff_id") `);
        await queryRunner.query(`CREATE INDEX "idx_staff_activity_care_calendar_id" ON "staff_activity" ("care_calendar") `);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "idx_staff_activity_care_calendar_id"`);
        await queryRunner.query(`DROP INDEX "idx_staff_activity_staff_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_classification_cc_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_activity_date_desc"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_activity_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_calendar_activity_status"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_entity_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_entity_type_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_animal_classification_animal_id"`);
        await queryRunner.query(`DROP INDEX "idx_animal_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "idx_animal_animal_type_id"`);
        await queryRunner.query(`DROP INDEX "idx_customer_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "idx_customer_classification_customer_id"`);
        await queryRunner.query(`DROP INDEX "idx_staff_staff_type_id"`);
        await queryRunner.query(`DROP INDEX "idx_entity_geography_entity_id"`);
        await queryRunner.query(`DROP INDEX "idx_entity_geography_entity_type_id"`);
        await queryRunner.query(`DROP INDEX "idx_entity_geography_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_entity_geography_geography_id"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_1_entity_id"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_1_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_2_entity_type"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_2_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_entity_relationship_entity_relationship_type_id"`);
        await queryRunner.query(`DROP INDEX "idx_document_document_type_id"`);
        await queryRunner.query(`DROP INDEX "idx_document_entity_1_entity_id"`);
        await queryRunner.query(`DROP INDEX "idx_document_entity_1_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_document_entity_1_id"`);
        await queryRunner.query(`DROP INDEX "idx_ref_village_taluk_id"`);
        await queryRunner.query(`ALTER TABLE "ref_reference" RENAME TO "temporary_ref_reference"`);
        await queryRunner.query(`CREATE TABLE "ref_reference" ("reference_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "reference_category_id" integer NOT NULL, "field_name" varchar(256), "reference_name" varchar(256) NOT NULL, "reference_name_l10n" text, "reference_information" text, "active" integer NOT NULL DEFAULT (1000100001), "type_of_data" varchar(256), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "ref_reference"("reference_id", "reference_category_id", "field_name", "reference_name", "reference_name_l10n", "reference_information", "active", "type_of_data", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "reference_id", "reference_category_id", "field_name", "reference_name", "reference_name_l10n", "reference_information", "active", "type_of_data", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "temporary_ref_reference"`);
        await queryRunner.query(`DROP TABLE "temporary_ref_reference"`);
        await queryRunner.query(`ALTER TABLE "ref_reference" RENAME TO "temporary_ref_reference"`);
        await queryRunner.query(`CREATE TABLE "ref_reference" ("reference_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "reference_category_id" integer NOT NULL, "field_name" varchar(256), "reference_name" varchar(256) NOT NULL, "reference_name_l10n" text, "reference_information" text, "active" integer NOT NULL DEFAULT (1000100001), "type_of_data" varchar(256), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "ref_reference"("reference_id", "reference_category_id", "field_name", "reference_name", "reference_name_l10n", "reference_information", "active", "type_of_data", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "reference_id", "reference_category_id", "field_name", "reference_name", "reference_name_l10n", "reference_information", "active", "type_of_data", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "temporary_ref_reference"`);
        await queryRunner.query(`DROP TABLE "temporary_ref_reference"`);
    }
}
