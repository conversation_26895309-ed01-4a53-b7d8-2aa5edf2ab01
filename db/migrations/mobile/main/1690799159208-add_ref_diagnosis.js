const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddRefDiagnosis1690799159208 {
    name = 'AddRefDiagnosis1690799159208'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "temporary_ref_reference" ("reference_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "reference_category_id" integer NOT NULL, "field_name" varchar(256), "reference_name" varchar(256) NOT NULL, "reference_name_l10n" text, "reference_information" text, "active" integer NOT NULL DEFAULT (1000100001), "type_of_data" varchar(256), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "temporary_ref_reference"("reference_id", "reference_category_id", "field_name", "reference_name", "reference_name_l10n", "reference_information", "active", "type_of_data", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "reference_id", "reference_category_id", "field_name", "reference_name", "reference_name_l10n", "reference_information", "active", "type_of_data", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "ref_reference"`);
        await queryRunner.query(`DROP TABLE "ref_reference"`);
        await queryRunner.query(`ALTER TABLE "temporary_ref_reference" RENAME TO "ref_reference"`);
        await queryRunner.query(`CREATE TABLE "ref_diagnosis" ("reference_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "category" integer NOT NULL, "reference_name_l10n" text, "reference_information" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "temporary_ref_reference" ("reference_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "reference_category_id" integer NOT NULL, "field_name" varchar(256), "reference_name" varchar(256) NOT NULL, "reference_name_l10n" text, "reference_information" text, "active" integer NOT NULL DEFAULT (1000100001), "type_of_data" varchar(256), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), CONSTRAINT "UQ_670ee86f995838d8f03081b7d84" UNIQUE ("field_name"))`);
        await queryRunner.query(`INSERT INTO "temporary_ref_reference"("reference_id", "reference_category_id", "field_name", "reference_name", "reference_name_l10n", "reference_information", "active", "type_of_data", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "reference_id", "reference_category_id", "field_name", "reference_name", "reference_name_l10n", "reference_information", "active", "type_of_data", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "ref_reference"`);
        await queryRunner.query(`DROP TABLE "ref_reference"`);
        await queryRunner.query(`ALTER TABLE "temporary_ref_reference" RENAME TO "ref_reference"`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "ref_reference" RENAME TO "temporary_ref_reference"`);
        await queryRunner.query(`CREATE TABLE "ref_reference" ("reference_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "reference_category_id" integer NOT NULL, "field_name" varchar(256), "reference_name" varchar(256) NOT NULL, "reference_name_l10n" text, "reference_information" text, "active" integer NOT NULL DEFAULT (1000100001), "type_of_data" varchar(256), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "ref_reference"("reference_id", "reference_category_id", "field_name", "reference_name", "reference_name_l10n", "reference_information", "active", "type_of_data", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "reference_id", "reference_category_id", "field_name", "reference_name", "reference_name_l10n", "reference_information", "active", "type_of_data", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "temporary_ref_reference"`);
        await queryRunner.query(`DROP TABLE "temporary_ref_reference"`);
        await queryRunner.query(`DROP TABLE "ref_diagnosis"`);
        await queryRunner.query(`ALTER TABLE "ref_reference" RENAME TO "temporary_ref_reference"`);
        await queryRunner.query(`CREATE TABLE "ref_reference" ("reference_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "reference_category_id" integer NOT NULL, "field_name" varchar(256), "reference_name" varchar(256) NOT NULL, "reference_name_l10n" text, "reference_information" text, "active" integer NOT NULL DEFAULT (1000100001), "type_of_data" varchar(256), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "ref_reference"("reference_id", "reference_category_id", "field_name", "reference_name", "reference_name_l10n", "reference_information", "active", "type_of_data", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "reference_id", "reference_category_id", "field_name", "reference_name", "reference_name_l10n", "reference_information", "active", "type_of_data", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "temporary_ref_reference"`);
        await queryRunner.query(`DROP TABLE "temporary_ref_reference"`);
    }
}
