const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class InventoryRelease1709901449443 {
    name = 'InventoryRelease1709901449443'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "requisition" ("requisition_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "requisitioner_type_id" integer, "requisitioner_uuid" varchar, "fullfiller_type_id" integer, "fullfiller_uuid" varchar, "approver_type_id" integer, "approver_uuid" varchar, "line_items_count" integer DEFAULT (0), "status_id" integer DEFAULT (1010000001), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_requisition_status_id" ON "requisition" ("status_id") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_requisitioner_uuid" ON "requisition" ("requisitioner_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_approver_uuid" ON "requisition" ("approver_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_fullfiller_uuid" ON "requisition" ("fullfiller_uuid") `);
        await queryRunner.query(`CREATE TABLE "requisition_classification" ("requisition_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "requisition_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "requisition_line_items" ("requisition_line_items_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "requisition_id" varchar, "requisitioner_uuid" varchar, "medicine_id" integer, "unit" varchar, "quantity" decimal(14,4) DEFAULT (0), "item_status_id" integer DEFAULT (0), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_requisitionlineitems_requisition_id" ON "requisition_line_items" ("requisition_id") `);
        await queryRunner.query(`CREATE TABLE "inventory_ledger" ("inventory_ledger_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "requisition_id" varchar, "medicine_id" integer, "warehouse_type_id" integer, "warehouse_uuid" varchar, "source_type_id" integer, "source_uuid" varchar, "consumer_type_id" integer DEFAULT (0), "consumer_uuid" varchar, "unit" varchar, "credit_qty" decimal(14,4) NOT NULL DEFAULT (0), "debit_qty" decimal(14,4) NOT NULL DEFAULT (0), "balance" decimal(14,4) NOT NULL DEFAULT (0), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "inserted_at" datetime)`);
        await queryRunner.query(`CREATE INDEX "idx_inventoryledger_source_uuid" ON "inventory_ledger" ("source_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_inventoryledger_warehouse_uuid" ON "inventory_ledger" ("warehouse_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_inventoryledger_inserted_at" ON "inventory_ledger" ("inserted_at") `);
        await queryRunner.query(`CREATE TABLE "warehouse_blockqty" ("warehouse_blockqty_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "source_type_id" integer, "source_uuid" varchar, "warehouse_type_id" integer, "warehouse_uuid" varchar, "medicine_id" integer, "unit" varchar, "blocked_qty" decimal(14,4) DEFAULT (0), "unblocked_qty" decimal(14,4) DEFAULT (0), "balance_qty" decimal(14,4) DEFAULT (0), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE INDEX "idx_warehouseblockqty_warehouse_uuid" ON "warehouse_blockqty" ("warehouse_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_warehouseblockqty_source_uuid" ON "warehouse_blockqty" ("source_uuid") `);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "idx_warehouseblockqty_source_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_warehouseblockqty_warehouse_uuid"`);
        await queryRunner.query(`DROP TABLE "warehouse_blockqty"`);
        await queryRunner.query(`DROP INDEX "idx_inventoryledger_inserted_at"`);
        await queryRunner.query(`DROP INDEX "idx_inventoryledger_warehouse_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_inventoryledger_source_uuid"`);
        await queryRunner.query(`DROP TABLE "inventory_ledger"`);
        await queryRunner.query(`DROP INDEX "idx_requisitionlineitems_requisition_id"`);
        await queryRunner.query(`DROP TABLE "requisition_line_items"`);
        await queryRunner.query(`DROP TABLE "requisition_classification"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_fullfiller_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_approver_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_requisitioner_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_status_id"`);
        await queryRunner.query(`DROP TABLE "requisition"`);
    }
}
