const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class MiscUpdates1711974529620 {
    name = 'MiscUpdates1711974529620'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "temporary_customer" ("customer_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "customer_type_id" integer DEFAULT (1000220001), "customer_visual_id" varchar(256), "customer_name_l10n" text, "mobile_number" varchar(256), "email_address" varchar(256), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "temporary_customer"("customer_id", "customer_type_id", "customer_visual_id", "customer_name_l10n", "mobile_number", "email_address", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "customer_id", "customer_type_id", "customer_visual_id", "customer_name_l10n", "mobile_number", "email_address", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "customer"`);
        await queryRunner.query(`DROP TABLE "customer"`);
        await queryRunner.query(`ALTER TABLE "temporary_customer" RENAME TO "customer"`);
        await queryRunner.query(`CREATE INDEX "idx_ref_district_state_id" ON "ref_district" ("state_id") `);
        await queryRunner.query(`CREATE INDEX "idx_ref_taluk_district_id" ON "ref_taluk" ("district_id") `);
        await queryRunner.query(`CREATE INDEX "created_at_sort_index" ON "customer" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "idx_customer_active" ON "customer" ("active") `);
        await queryRunner.query(`CREATE INDEX "idx_customer_classification_active" ON "customer_classification" ("active") `);
        await queryRunner.query(`CREATE INDEX "idx_customer_classification_value_reference_id" ON "customer_classification" ("value_reference_id") `);
        await queryRunner.query(`CREATE INDEX "idx_animal_active" ON "animal" ("active") `);
        await queryRunner.query(`CREATE INDEX "idx_animal_classification_active" ON "animal_classification" ("active") `);
        await queryRunner.query(`CREATE TABLE "temporary_ref_activity" ("activity_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "activity_name" varchar NOT NULL, "activity_category" integer NOT NULL, "activity_name_l10n" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "activity_information" text, CONSTRAINT "UQ_REF_ACTIVITY_ACTIVITY_NAME" UNIQUE ("activity_name", "activity_category"))`);
        await queryRunner.query(`INSERT INTO "temporary_ref_activity"("activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "activity_information") SELECT "activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "activity_information" FROM "ref_activity"`);
        await queryRunner.query(`DROP TABLE "ref_activity"`);
        await queryRunner.query(`ALTER TABLE "temporary_ref_activity" RENAME TO "ref_activity"`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "ref_activity" RENAME TO "temporary_ref_activity"`);
        await queryRunner.query(`CREATE TABLE "ref_activity" ("activity_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "activity_name" varchar NOT NULL, "activity_category" integer NOT NULL, "activity_name_l10n" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "activity_information" text)`);
        await queryRunner.query(`INSERT INTO "ref_activity"("activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "activity_information") SELECT "activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "activity_information" FROM "temporary_ref_activity"`);
        await queryRunner.query(`DROP TABLE "temporary_ref_activity"`);
        await queryRunner.query(`DROP INDEX "idx_animal_classification_active"`);
        await queryRunner.query(`DROP INDEX "idx_animal_active"`);
        await queryRunner.query(`DROP INDEX "idx_customer_classification_value_reference_id"`);
        await queryRunner.query(`DROP INDEX "idx_customer_classification_active"`);
        await queryRunner.query(`DROP INDEX "idx_customer_active"`);
        await queryRunner.query(`DROP INDEX "created_at_sort_index"`);
        await queryRunner.query(`DROP INDEX "idx_ref_taluk_district_id"`);
        await queryRunner.query(`DROP INDEX "idx_ref_district_state_id"`);
        await queryRunner.query(`ALTER TABLE "customer" RENAME TO "temporary_customer"`);
        await queryRunner.query(`CREATE TABLE "customer" ("customer_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "customer_type_id" integer, "customer_visual_id" varchar(256), "customer_name_l10n" text, "mobile_number" varchar(256), "email_address" varchar(256), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "customer"("customer_id", "customer_type_id", "customer_visual_id", "customer_name_l10n", "mobile_number", "email_address", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "customer_id", "customer_type_id", "customer_visual_id", "customer_name_l10n", "mobile_number", "email_address", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "temporary_customer"`);
        await queryRunner.query(`DROP TABLE "temporary_customer"`);
    }
}
