const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class ModifyCareCalendarAddAdditionalColumnsForSLAMgmt1693292172349 {
    name = 'ModifyCareCalendarAddAdditionalColumnsForSLAMgmt1693292172349'

    async up(queryRunner) {
        await queryRunner.query(`DROP INDEX "idx_care_calendar_activity_date_desc"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_activity_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_calendar_activity_status"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_entity_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_entity_type_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_entity_uuid"`);
        await queryRunner.query(`CREATE TABLE "temporary_care_calendar" ("care_calendar_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "entity_type_id" integer NOT NULL, "entity_id" integer, "entity_uuid" varchar, "activity_id" integer NOT NULL, "visit_id" integer, "activity_date" datetime NOT NULL, "calendar_activity_status" integer NOT NULL DEFAULT (1000300001), "completion_date" datetime, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "system_task_completion_time" datetime, "visit_schedule_time" datetime, "visit_creation_time" datetime, "user_task_completion_time" datetime, "activity_created_by" varchar, "activity_completed_by" varchar, "active" integer NOT NULL DEFAULT (1000100001))`);
        await queryRunner.query(`INSERT INTO "temporary_care_calendar"("care_calendar_id", "entity_type_id", "entity_id", "entity_uuid", "activity_id", "visit_id", "activity_date", "calendar_activity_status", "completion_date", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "care_calendar_id", "entity_type_id", "entity_id", "entity_uuid", "activity_id", "visit_id", "activity_date", "calendar_activity_status", "completion_date", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "care_calendar"`);
        await queryRunner.query(`DROP TABLE "care_calendar"`);
        await queryRunner.query(`ALTER TABLE "temporary_care_calendar" RENAME TO "care_calendar"`);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_activity_date_desc" ON "care_calendar" ("activity_date") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_activity_id" ON "care_calendar" ("activity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_calendar_activity_status" ON "care_calendar" ("calendar_activity_status") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_entity_id" ON "care_calendar" ("entity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_entity_type_id" ON "care_calendar" ("entity_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_entity_uuid" ON "care_calendar" ("entity_uuid") `);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "idx_care_calendar_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_entity_type_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_entity_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_calendar_activity_status"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_activity_id"`);
        await queryRunner.query(`DROP INDEX "idx_care_calendar_activity_date_desc"`);
        await queryRunner.query(`ALTER TABLE "care_calendar" RENAME TO "temporary_care_calendar"`);
        await queryRunner.query(`CREATE TABLE "care_calendar" ("care_calendar_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "entity_type_id" integer NOT NULL, "entity_id" integer, "entity_uuid" varchar, "activity_id" integer NOT NULL, "visit_id" integer, "activity_date" datetime NOT NULL, "calendar_activity_status" integer NOT NULL DEFAULT (1000300001), "completion_date" datetime, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "care_calendar"("care_calendar_id", "entity_type_id", "entity_id", "entity_uuid", "activity_id", "visit_id", "activity_date", "calendar_activity_status", "completion_date", "correlation_id", "data_sync_status", "created_at", "updated_at") SELECT "care_calendar_id", "entity_type_id", "entity_id", "entity_uuid", "activity_id", "visit_id", "activity_date", "calendar_activity_status", "completion_date", "correlation_id", "data_sync_status", "created_at", "updated_at" FROM "temporary_care_calendar"`);
        await queryRunner.query(`DROP TABLE "temporary_care_calendar"`);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_entity_uuid" ON "care_calendar" ("entity_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_entity_type_id" ON "care_calendar" ("entity_type_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_entity_id" ON "care_calendar" ("entity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_calendar_activity_status" ON "care_calendar" ("calendar_activity_status") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_activity_id" ON "care_calendar" ("activity_id") `);
        await queryRunner.query(`CREATE INDEX "idx_care_calendar_activity_date_desc" ON "care_calendar" ("activity_date") `);
    }
}
