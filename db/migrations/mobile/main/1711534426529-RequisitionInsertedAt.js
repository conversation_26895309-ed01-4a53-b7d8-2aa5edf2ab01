const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1711534426529 {
    name = 'Main1711534426529'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "temporary_ref_activity" ("activity_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "activity_name" varchar NOT NULL, "activity_category" integer NOT NULL, "activity_name_l10n" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "activity_information" text)`);
        await queryRunner.query(`INSERT INTO "temporary_ref_activity"("activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "activity_information") SELECT "activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "activity_information" FROM "ref_activity"`);
        await queryRunner.query(`DROP TABLE "ref_activity"`);
        await queryRunner.query(`ALTER TABLE "temporary_ref_activity" RENAME TO "ref_activity"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_fullfiller_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_approver_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_requisitioner_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_status_id"`);
        await queryRunner.query(`CREATE TABLE "temporary_requisition" ("requisition_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "requisitioner_type_id" integer, "requisitioner_uuid" varchar, "fullfiller_type_id" integer, "fullfiller_uuid" varchar, "approver_type_id" integer, "approver_uuid" varchar, "line_items_count" integer DEFAULT (0), "status_id" integer DEFAULT (1010000001), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "inserted_at" datetime)`);
        await queryRunner.query(`INSERT INTO "temporary_requisition"("requisition_id", "requisitioner_type_id", "requisitioner_uuid", "fullfiller_type_id", "fullfiller_uuid", "approver_type_id", "approver_uuid", "line_items_count", "status_id", "active", "last_modifying_user_id", "data_sync_status", "created_at", "updated_at") SELECT "requisition_id", "requisitioner_type_id", "requisitioner_uuid", "fullfiller_type_id", "fullfiller_uuid", "approver_type_id", "approver_uuid", "line_items_count", "status_id", "active", "last_modifying_user_id", "data_sync_status", "created_at", "updated_at" FROM "requisition"`);
        await queryRunner.query(`DROP TABLE "requisition"`);
        await queryRunner.query(`ALTER TABLE "temporary_requisition" RENAME TO "requisition"`);
        await queryRunner.query(`CREATE INDEX "idx_requisition_fullfiller_uuid" ON "requisition" ("fullfiller_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_approver_uuid" ON "requisition" ("approver_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_requisitioner_uuid" ON "requisition" ("requisitioner_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_status_id" ON "requisition" ("status_id") `);
        await queryRunner.query(`CREATE TABLE "temporary_ref_activity" ("activity_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "activity_name" varchar NOT NULL, "activity_category" integer NOT NULL, "activity_name_l10n" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "activity_information" text, CONSTRAINT "UQ_f9186e942ead01ee34f544a18f5" UNIQUE ("activity_name"))`);
        await queryRunner.query(`INSERT INTO "temporary_ref_activity"("activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "activity_information") SELECT "activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "activity_information" FROM "ref_activity"`);
        await queryRunner.query(`DROP TABLE "ref_activity"`);
        await queryRunner.query(`ALTER TABLE "temporary_ref_activity" RENAME TO "ref_activity"`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "ref_activity" RENAME TO "temporary_ref_activity"`);
        await queryRunner.query(`CREATE TABLE "ref_activity" ("activity_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "activity_name" varchar NOT NULL, "activity_category" integer NOT NULL, "activity_name_l10n" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "activity_information" text)`);
        await queryRunner.query(`INSERT INTO "ref_activity"("activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "activity_information") SELECT "activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "activity_information" FROM "temporary_ref_activity"`);
        await queryRunner.query(`DROP TABLE "temporary_ref_activity"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_status_id"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_requisitioner_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_approver_uuid"`);
        await queryRunner.query(`DROP INDEX "idx_requisition_fullfiller_uuid"`);
        await queryRunner.query(`ALTER TABLE "requisition" RENAME TO "temporary_requisition"`);
        await queryRunner.query(`CREATE TABLE "requisition" ("requisition_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "requisitioner_type_id" integer, "requisitioner_uuid" varchar, "fullfiller_type_id" integer, "fullfiller_uuid" varchar, "approver_type_id" integer, "approver_uuid" varchar, "line_items_count" integer DEFAULT (0), "status_id" integer DEFAULT (1010000001), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`INSERT INTO "requisition"("requisition_id", "requisitioner_type_id", "requisitioner_uuid", "fullfiller_type_id", "fullfiller_uuid", "approver_type_id", "approver_uuid", "line_items_count", "status_id", "active", "last_modifying_user_id", "data_sync_status", "created_at", "updated_at") SELECT "requisition_id", "requisitioner_type_id", "requisitioner_uuid", "fullfiller_type_id", "fullfiller_uuid", "approver_type_id", "approver_uuid", "line_items_count", "status_id", "active", "last_modifying_user_id", "data_sync_status", "created_at", "updated_at" FROM "temporary_requisition"`);
        await queryRunner.query(`DROP TABLE "temporary_requisition"`);
        await queryRunner.query(`CREATE INDEX "idx_requisition_status_id" ON "requisition" ("status_id") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_requisitioner_uuid" ON "requisition" ("requisitioner_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_approver_uuid" ON "requisition" ("approver_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_fullfiller_uuid" ON "requisition" ("fullfiller_uuid") `);
        await queryRunner.query(`ALTER TABLE "ref_activity" RENAME TO "temporary_ref_activity"`);
        await queryRunner.query(`CREATE TABLE "ref_activity" ("activity_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "activity_name" varchar NOT NULL, "activity_category" integer NOT NULL, "activity_name_l10n" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "activity_information" text)`);
        await queryRunner.query(`INSERT INTO "ref_activity"("activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "activity_information") SELECT "activity_id", "activity_name", "activity_category", "activity_name_l10n", "active", "last_modifying_user_id", "correlation_id", "data_sync_status", "created_at", "updated_at", "activity_information" FROM "temporary_ref_activity"`);
        await queryRunner.query(`DROP TABLE "temporary_ref_activity"`);
    }
}
