const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class InitialDesign1672979849177 {
  name = "InitialDesign1672979849177";

  async up(queryRunner) {
    await queryRunner.query(`CREATE TABLE "ref_reference_category" ("reference_category_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "reference_name" varchar(256) NOT NULL, "reference_category_name_l10n" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "ref_reference" ("reference_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "reference_category_id" integer NOT NULL, "field_name" varchar(256), "reference_name" varchar(256) NOT NULL, "reference_name_l10n" text, "reference_information" text, "active" integer NOT NULL DEFAULT (1000100001), "type_of_data" varchar(256), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "ref_state" ("state_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "state_census_id" integer, "state_name_l10n" text, "state_short_code" varchar(256), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "ref_district" ("district_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "state_id" integer NOT NULL, "district_census_id" integer, "district_name_l10n" text, "district_short_code" varchar(256), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "ref_taluk" ("taluk_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "district_id" integer NOT NULL, "taluk_census_id" integer, "taluk_name_l10n" text, "taluk_short_code" varchar(256), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "ref_village" ("village_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "taluk_id" integer NOT NULL, "village_census_id" integer, "village_name_l10n" text, "village_short_code" varchar(256), "village_pin_code" varchar(256), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "document" ("document_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "document_type_id" integer NOT NULL, "document_name_l10n" text, "document_name" varchar(256), "document_status_id" integer, "entity_1_type_id" integer, "entity_1_entity_uuid" varchar, "entity_1_entity_id" integer, "entity_2_type_id" integer, "entity_2_entity_uuid" varchar, "entity_2_entity_id" integer, "entity_3_type_id" integer, "entity_3_entity_uuid" varchar, "entity_3_entity_id" integer, "client_document_information" text, "document_information" text, "document_meta_data_information" text, "document_sync_status" integer NOT NULL DEFAULT (1000102002), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "entity_relationship" ("entity_relationship_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "entity_relationship_type_id" integer, "entity_1_type_id" integer, "entity_1_entity_uuid" varchar, "entity_1_entity_id" integer, "entity_2_type_id" integer, "entity_2_entity_uuid" varchar, "entity_2_entity_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "entity_information" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "entity_geography" ("entity_geography_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "entity_type_id" integer, "entity_uuid" varchar, "entity_id" integer, "geography_type_id" integer, "geography_id" integer, "start_date" datetime DEFAULT ('1900-01-01 00:00:01'), "end_date" datetime DEFAULT ('2099-12-31 23:59:59'), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "staff" ("staff_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "staff_type_id" integer, "staff_visual_id" varchar(256), "staff_name_l10n" text, "mobile_number" varchar(256), "email_address" varchar(256), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "staff_classification" ("staff_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "staff_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "customer" ("customer_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "customer_type_id" integer, "customer_visual_id" varchar(256), "customer_name_l10n" text, "mobile_number" varchar(256), "email_address" varchar(256), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "customer_classification" ("customer_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "customer_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "animal" ("animal_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "animal_visual_id" varchar(256), "animal_type" integer, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "animal_classification" ("animal_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "animal_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "care_calendar" ("care_calendar_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "entity_type_id" integer NOT NULL, "entity_id" integer, "entity_uuid" varchar, "activity_id" integer NOT NULL, "visit_id" integer, "activity_date" datetime NOT NULL, "calendar_activity_status" integer NOT NULL DEFAULT (1000300001), "completion_date" datetime, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "care_calendar_classification" ("care_calendar_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "care_calendar_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "animal_disease" ("animal_disease_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "animal_id" varchar, "disease_id" integer, "disease_date" datetime, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "animal_disease_classification" ("animal_disease_classification_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "animal_disease_id" varchar NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" varchar, "value_string_256" varchar(256), "value_string_2000" varchar(2000), "value_double" decimal(14,4), "value_date" datetime, "value_json" text, "value_l10n" text, "value_string_256_encrypted" varchar(256), "value_string_2000_encrypted" varchar(2000), "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "dump_doc" ("dump_document_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "document_key" varchar NOT NULL, "error" text, "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "note" ("note_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "note_type_id" integer, "note_status_id" integer, "note_time" datetime, "note" text NOT NULL, "entity_1_type_id" integer, "entity_1_uuid" varchar, "entity_1_id" integer, "entity_2_type_id" integer, "entity_2_uuid" varchar, "entity_2_id" integer, "entity_3_type_id" integer, "entity_3_uuid" varchar, "entity_3_id" integer, "entity_4_type_id" integer, "entity_4_uuid" varchar, "entity_4_id" integer, "creator_uuid" varchar, "creator_id" integer, "creator_type_id" integer, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "ref_activity" ("activity_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "activity_name" varchar NOT NULL, "activity_category" integer NOT NULL, "activity_name_l10n" text, "active" integer NOT NULL DEFAULT (1000100001), "last_modifying_user_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "staff_activity" ("staff_activity_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "staff_id" varchar, "staff_activity_type_id" integer NOT NULL, "visit_id" varchar, "activity_id" integer NOT NULL, "activity_duration" datetime NOT NULL, "entity_type_id" integer NOT NULL, "entity_id" varchar NOT NULL, "care_calendar" varchar NOT NULL, "ticket_id" varchar, "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    await queryRunner.query(`CREATE TABLE "ticket" ("ticket_id" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "ticket_status" varchar(256) NOT NULL DEFAULT (1000300001), "correlation_id" varchar(256), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
  }

  async down(queryRunner) {
    await queryRunner.query(`DROP TABLE "ticket"`);
    await queryRunner.query(`DROP TABLE "staff_activity"`);
    await queryRunner.query(`DROP TABLE "ref_activity"`);
    await queryRunner.query(`DROP TABLE "note"`);
    await queryRunner.query(`DROP TABLE "dump_doc"`);
    await queryRunner.query(`DROP TABLE "animal_disease_classification"`);
    await queryRunner.query(`DROP TABLE "animal_disease"`);
    await queryRunner.query(`DROP TABLE "care_calendar_classification"`);
    await queryRunner.query(`DROP TABLE "care_calendar"`);
    await queryRunner.query(`DROP TABLE "animal_classification"`);
    await queryRunner.query(`DROP TABLE "animal"`);
    await queryRunner.query(`DROP TABLE "customer_classification"`);
    await queryRunner.query(`DROP TABLE "customer"`);
    await queryRunner.query(`DROP TABLE "staff_classification"`);
    await queryRunner.query(`DROP TABLE "staff"`);
    await queryRunner.query(`DROP TABLE "entity_geography"`);
    await queryRunner.query(`DROP TABLE "entity_relationship"`);
    await queryRunner.query(`DROP TABLE "document"`);
    await queryRunner.query(`DROP TABLE "ref_village"`);
    await queryRunner.query(`DROP TABLE "ref_taluk"`);
    await queryRunner.query(`DROP TABLE "ref_district"`);
    await queryRunner.query(`DROP TABLE "ref_state"`);
    await queryRunner.query(`DROP TABLE "ref_reference"`);
    await queryRunner.query(`DROP TABLE "ref_reference_category"`);
  }
};
