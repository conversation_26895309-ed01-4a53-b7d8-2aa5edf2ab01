const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1723799921070 {
    name = 'Main1723799921070'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "task_question" ("question_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "question_name" varchar(256) NOT NULL, "question_name_l10n" text, "topic_name" varchar(256) NOT NULL, "topic_name_l10n" text, "topic_priority" integer, "topic_question_priority" integer, "priority_grouping" integer, "form_configuration" text, "frequency" varchar(256) NOT NULL, "farmer_input_required" integer, "review_date_config" integer, "active" integer NOT NULL DEFAULT (1000100001), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
        await queryRunner.query(`CREATE TABLE "task_question_data" ("question_data_uuid" varchar PRIMARY KEY NOT NULL DEFAULT ((lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))), "care_calendar_id" varchar NOT NULL, "question_uuid" varchar NOT NULL, "form_configuration" text, "review_date" varchar, "priority_grouping" integer, "active" integer NOT NULL DEFAULT (1000100001), "data_sync_status" integer NOT NULL DEFAULT (1000101003), "saved_at" datetime, "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "task_question_data"`);
        await queryRunner.query(`DROP TABLE "task_question"`);
    }
}
