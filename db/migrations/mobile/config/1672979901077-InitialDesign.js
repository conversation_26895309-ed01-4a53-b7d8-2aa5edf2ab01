const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class InitialDesign1672979901077 {
    name = 'InitialDesign1672979901077'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "mobile_synchronization_data" ("mobile_synchronization_data_id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "table_name" varchar(256) NOT NULL, "last_sync_time" datetime, "active" integer NOT NULL DEFAULT (1000100001), "created_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP), "updated_at" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "mobile_synchronization_data"`);
    }
}
