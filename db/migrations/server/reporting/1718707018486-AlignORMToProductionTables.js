const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AlignORMToProductionTables1718707018486 {
    name = 'AlignORMToProductionTables1718707018486'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "reporting"."animal" ("animal_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "animal_id" character varying(256), "farmer_id" character varying(256), "start_date" TIMESTAMP, "end_date" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_animal_uuid" PRIMARY KEY ("animal_uuid"))`);
        await queryRunner.query(`CREATE TABLE "reporting"."farmer" ("farmer_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "farmer_id" character varying(256), "farmer_name" character varying(256), "farmer_mobile" character varying(256), "bco_id" character varying(256), "start_date" TIMESTAMP, "end_date" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_farmer_uuid" PRIMARY KEY ("farmer_uuid"))`);
        await queryRunner.query(`CREATE TABLE "reporting"."cost_per_cc" ("cost_per_cc_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "care_calendar_uuid" uuid, "entity_type_id" integer, "customer_uuid" uuid, "customer_name_l10n" jsonb, "mobile_number" character varying(256), "village_name_l10n" jsonb, "taluk_name_l10n" jsonb, "district_name_l10n" jsonb, "state_name_l10n" jsonb, "animal_uuid" uuid, "animal_eartag" character varying(256), "animal_type" character varying(256), "subscription_plan_l10n" jsonb, "start_date" TIMESTAMP, "active" character varying(256), "activity_id" integer, "activity_name_l10n" jsonb, "activity_category_name_l10n" jsonb, "activity_date" TIMESTAMP, "activity_status_id" integer, "medicine_cost" numeric(14,4), "pv_cost" numeric(14,4), "vet_cost" numeric(14,4), "information_json" jsonb, "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_cost_per_cc_uuid" PRIMARY KEY ("cost_per_cc_uuid"))`);
        await queryRunner.query(`CREATE TABLE "reporting"."snapshot_farmer_active_animal_count" ("snapshot_farmer_active_animal_count_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "snapshot_type_id" integer, "snapshot_timestamp" TIMESTAMP, "snapshot_farmer_active_animal_count_information" jsonb, "customer_id" uuid, "animal_type_id" integer, "active_animal_count" integer, "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_snapshot_farmer_active_animal_count_uuid" PRIMARY KEY ("snapshot_farmer_active_animal_count_uuid"))`);
        await queryRunner.query(`CREATE TABLE "reporting"."snapshot_preventive_visit_penalty" ("snapshot_preventive_cases_penalty_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "care_calendar_uuid" uuid, "entity_type_id" integer, "entity_uuid" uuid, "activity_id" integer, "vet_uuid" uuid, "paravet_uuid" uuid, "vet_penalty_amount" numeric(14,4), "paravet_penalty_amount" numeric(14,4), "activity_status_id" integer, "activity_category_id" integer, "activity_timestamp" TIMESTAMP, "snapshot_type_id" integer, "snapshot_timestamp" TIMESTAMP, "snapshot_visit_penalty_information" jsonb, "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_snapshot_preventive_cases_penalty_uuid" PRIMARY KEY ("snapshot_preventive_cases_penalty_uuid"))`);
        await queryRunner.query(`CREATE TABLE "reporting"."customer_classification_history" ("customer_classification_history_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "customer_classification_id" uuid NOT NULL, "customer_id" uuid NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "source_created_at" TIMESTAMP, "source_updated_at" TIMESTAMP, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_customer_classification_history_id" PRIMARY KEY ("customer_classification_history_id"))`);
        await queryRunner.query(`CREATE TABLE "reporting"."animal_classification_history" ("animal_classification_history_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "animal_classification_id" uuid NOT NULL, "animal_id" uuid NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "source_created_at" TIMESTAMP, "source_updated_at" TIMESTAMP, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_animal_classification_history_id" PRIMARY KEY ("animal_classification_history_id"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "reporting"."animal_classification_history"`);
        await queryRunner.query(`DROP TABLE "reporting"."customer_classification_history"`);
        await queryRunner.query(`DROP TABLE "reporting"."snapshot_preventive_visit_penalty"`);
        await queryRunner.query(`DROP TABLE "reporting"."snapshot_farmer_active_animal_count"`);
        await queryRunner.query(`DROP TABLE "reporting"."cost_per_cc"`);
        await queryRunner.query(`DROP TABLE "reporting"."farmer"`);
        await queryRunner.query(`DROP TABLE "reporting"."animal"`);
    }
}
