const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddTableSnapshotVisitPenalty1700206351145 {
    name = 'AddTableSnapshotVisitPenalty1700206351145'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "reporting"."snapshot_visit_penalty" ("snapshot_penalty_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "care_calendar_uuid" uuid, "entity_type_id" integer, "entity_uuid" uuid, "service_request_date" TIMESTAMP, "service_request_time" TIMESTAMP, "service_request_no" character varying(256), "vet_published_status_id" integer, "paravet_administered_status_id" integer, "vet_penalty_amount" numeric(14,4), "paravet_penalty_amount" numeric(14,4), "activity_category_id" integer, "activity_id" integer, "activity_status_id" integer, "snapshot_type_id" integer, "snapshot_timestamp" TIMESTAMP, "snapshot_visit_penalty_information" jsonb, "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_snapshot_penalty_uuid" PRIMARY KEY ("snapshot_penalty_uuid"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "reporting"."snapshot_visit_penalty"`);
    }
}
