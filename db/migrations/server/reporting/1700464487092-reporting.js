const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Reporting1700464487092 {
    name = 'Reporting1700464487092'

    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "reporting"."snapshot_visit_penalty" DROP COLUMN "service_request_date"`);
        await queryRunner.query(`ALTER TABLE "reporting"."snapshot_visit_penalty" DROP COLUMN "service_request_time"`);
        await queryRunner.query(`ALTER TABLE "reporting"."snapshot_visit_penalty" ADD "service_request_timestamp" TIMESTAMP`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "reporting"."snapshot_visit_penalty" DROP COLUMN "service_request_timestamp"`);
        await queryRunner.query(`ALTER TABLE "reporting"."snapshot_visit_penalty" ADD "service_request_time" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "reporting"."snapshot_visit_penalty" ADD "service_request_date" TIMESTAMP`);
    }
}
