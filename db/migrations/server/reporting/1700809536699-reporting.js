const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Reporting1700809536699 {
    name = 'Reporting1700809536699'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "reporting"."snapshot_active_animal_count" ("snapshot_active_animal_count_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "snapshot_type_id" integer, "snapshot_timestamp" TIMESTAMP, "snapshot_active_animal_count_information" jsonb, "village_id" integer, "animal_type_id" integer, "active_animal_count" integer, "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_snapshot_active_animal_count_uuid" PRIMARY KEY ("snapshot_active_animal_count_uuid"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "reporting"."snapshot_active_animal_count"`);
    }
}
