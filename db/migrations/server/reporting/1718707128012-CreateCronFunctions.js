const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class CreateCronFunctions1718707128012 {

  async up(queryRunner) {
    await queryRunner.query(
      `
        CREATE OR REPLACE FUNCTION reporting.function_cost_per_cc()
          RETURNS void
          LANGUAGE sql
          AS $function$INSERT INTO reporting.cost_per_cc
          (care_calendar_uuid,entity_type_id,
          customer_uuid,customer_name_l10n,mobile_number,
          village_name_l10n,taluk_name_l10n,district_name_l10n,state_name_l10n,animal_uuid,animal_eartag,animal_type,subscription_plan_l10n,start_date,active,
          activity_id,activity_name_l10n,activity_category_name_l10n,activity_date,
          activity_status_id,medicine_cost,pv_cost,vet_cost)
          (with medicine_prices as (
            select 
              rr.reference_id med_id,
              rr.reference_name medname,
              rr.reference_information->>'mrp' as mrp,
              rr.reference_information->>'unit' as unit,
              rr.reference_information->>'pack_size' as packsize,
              case
                when rr.reference_information->>'mrp' is null then 0
                when (rr.reference_information->>'mrp')::decimal = 0 then 0
                else round((rr.reference_information->>'mrp')::decimal/(rr.reference_information->>'pack_size')::decimal,2)
              end as ppu,
              rr.active
            from main.ref_reference rr 
            where rr.reference_category_id = 10005000
          ),
          ccc_medicines as (
            select 
              ccc.care_calendar_id ccid,
              ccc.value_reference_id as med_id,
              case
                when ccc.value_json->>'frequency' = 'TID' then ccc.value_double * 3
                when ccc.value_json->>'frequency' = 'BID' then ccc.value_double * 2
                else ccc.value_double
              end as qty,
              value_json 
            from main.care_calendar_classification ccc 
              where classifier_id in (2000000185,2000000270)
              and ccc.active = 1000100001
          ),
          ccc_medcost as (
            select 
              cm.ccid,
              cm.med_id,
              cm.qty,
              mp.mrp,
              mp.ppu,
              mp.ppu * cm.qty as medcost
            from ccc_medicines cm 
            inner join medicine_prices mp on mp.med_id = cm.med_id	
          ),
          medcost_per_cc as (
            select 
            cm.ccid,
            sum(medcost) as medcost
            from ccc_medcost cm
            group by cm.ccid
          ),
          vet_presence_old as (
            select 
            ccc.care_calendar_id as ccid,
            ccc.value_reference_id,
            case
              when ccc.value_reference_id = 1000105001 then 1
              else 0
            end as vet_present,
            row_number() over (partition by ccc.care_calendar_id, classifier_id order by ccc.created_at desc) as rownum
            from main.care_calendar_classification ccc 
            where ccc.classifier_id = 2000000191 and ccc.active = 1000100001
          ),
          vet_presence_new as (
            select 
            ccc.care_calendar_id as ccid,
            ccc.value_reference_id,
            case
              when ccc.value_reference_id = 1000750002 then 1
              else 0
            end as vet_present,
            row_number() over (partition by ccc.care_calendar_id, classifier_id order by ccc.created_at desc) as rownum
            from main.care_calendar_classification ccc 
            where ccc.classifier_id = 2000000221 and ccc.active = 1000100001
          ),
          vet_visit as (
            select cc.care_calendar_id ccid,
            coalesce(vpn.vet_present, vpo.vet_present) as vet_present
            from main.care_calendar cc 
            left join vet_presence_old vpo on vpo.ccid = cc.care_calendar_id and vpo.rownum = 1
            left join vet_presence_new vpn on vpn.ccid = cc.care_calendar_id and vpn.rownum = 1
          ),
          vet_visit_prices(activity_id,base_rate,multiplier) as (
            values 
              (1000180013,500,0.05)
          ),
          pv_visit_prices(activity_id,base_rate,multiplier,medicine_cost) as (
            values 
              (1000180001,100,0.10,179),
              (1000180002,200,0.25,35),
              (1000180003,200,0.25,43),
              (1000180004,200,0.25,210),
              (1000180005,200,0.25,73),
              (1000180006,200,0.25,200),
              (1000180007,200,0.25,28),
              (1000180008,200,0.25,50),
              (1000180009,0,0,0),
              (1000180010,500,0.50,300),
              (1000180011,100,0.10,28),
              (1000180012,100,0.10,90),
              (1000180013,200,0.10,0),
              (1000180014,200,0.25,32),
              (1000180015,100,0.25,7.5),
              (1000470001,150,1,30),
              (1000470003,100,0.50,50)
          ),
          count_active_animals_per_farmer as (
            select 
              er.entity_1_entity_uuid as farmer_id,
              count(er.entity_relationship_type_id) num_animals
            from main.entity_relationship er 
            where er.entity_relationship_type_id = 1000210004 and er.active = 1000100001
            group by er.entity_1_entity_uuid
          ),
          active_animals_count as (
            select 
            er.entity_1_entity_uuid as farmer_id,
            er.entity_2_entity_uuid as animal_id,
            capv.num_animals
            from main.entity_relationship er 
            inner join count_active_animals_per_farmer capv on capv.farmer_id = er.entity_1_entity_uuid
          ),
          animal_farmer_details as (
            select 
              a.animal_id,
              ac.value_string_256 as eartag,
              aac.farmer_id as customer_id,
              a.active
            from main.animal a
            inner join main.animal_classification ac on a.animal_id = ac.animal_id
            inner join active_animals_count aac on aac.animal_id = a.animal_id
            where ac.classifier_id = 2000000034 and ac.active = 1000100001
          ),
          vet_visit_cost as (
            select 
            cc.care_calendar_id as ccid,
            case
              when cc.activity_id = 1000180013 then
                round(vvp.base_rate::decimal*(1+(vvp.multiplier::decimal*(capv.num_animals::decimal-1))),2)
              else vv.vet_present::decimal*500
            end as vet_cost
            from main.care_calendar cc 
            inner join vet_visit vv on vv.ccid = cc.care_calendar_id and vet_present=1
            left join count_active_animals_per_farmer capv on capv.farmer_id = cc.entity_uuid
            left join vet_visit_prices vvp on vvp.activity_id = cc.activity_id
          ),
          preventive_pv_cost as (
            select cc.care_calendar_id as ccid,
            cc.activity_id,
            case
              when cc.entity_type_id = 1000460002 then
                round(pvp.base_rate::decimal*(1+pvp.multiplier::decimal*(aac.num_animals::decimal-1))
              /aac.num_animals::decimal,2)
              else 0
            end as pv_visit_cost,
            pvp.medicine_cost
            from main.care_calendar cc 
            inner join main.ref_activity ra on ra.activity_id = cc.activity_id 
              and ra.activity_category = 1000270001
            left join active_animals_count aac on aac.animal_id = cc.entity_uuid
            left join pv_visit_prices pvp on pvp.activity_id = cc.activity_id
          ),
          reproductive_pv_cost as (
            select cc.care_calendar_id as ccid,
            cc.activity_id,
            case
              when cc.entity_type_id = 1000460002 then
              round((pvp.base_rate::decimal+(pvp.multiplier::decimal*(aac.num_animals::decimal-1)))
              /aac.num_animals::decimal,2)
              else 0
            end as pv_visit_cost,
            pvp.medicine_cost
            from main.care_calendar cc 
            left join active_animals_count aac on aac.animal_id = cc.entity_uuid
            left join pv_visit_prices pvp on pvp.activity_id = cc.activity_id
            where cc.activity_id in (1000470001,1000470003)
          ),
          animal_type as (
            select 
              ac.animal_id,
              ac.value_reference_id as animal_type_id,
              rr.reference_name as animal_type
            from main.animal_classification ac 
            inner join main.ref_reference rr on rr.reference_id = ac.value_reference_id
            where ac.classifier_id = 2000000035 and ac.active = 1000100001
          ),
          subscription_plan as (
            select 
              ac.animal_id,
              ac.value_reference_id as subscription_plan_id,
              rr.reference_name_l10n as subscription_plan_l10n
            from main.animal_classification ac 
            inner join main.ref_reference rr on rr.reference_id = ac.value_reference_id
            where ac.classifier_id = 2000000051 and ac.active = 1000100001	 
          ),
          cow_start_date as (
            select 
              ac.animal_id,
              ac.value_date as start_date
            from main.animal_classification ac 
            where ac.classifier_id = 2000000124 and ac.active = 1000100001
          ),
          farmer_village as (
            select cc2.customer_id as customer_id,
            cc2.value_reference_id as village_id,
            rsv.village_name_l10n as village_name,
            rsv.taluk_name_l10n as taluk_name,
            rsv.district_name_l10n as district_name,
            rsv.state_name_l10n as state_name
            from main.customer_classification cc2 
            inner join main.ref_sdtv_view rsv on rsv.village_id = cc2.value_reference_id
            where cc2.classifier_id = 2000000055 and cc2.active = 1000100001
          ),
          customer_details_per_cc as (
            select 
              cc2.care_calendar_id as ccid,
              case 
                when cc2.entity_type_id = 1000460001 then c.customer_id 
                else afd.customer_id
              end as customer_uuid,
              afd.animal_id as animal_uuid,
              afd.eartag as animal_eartag,
              at.animal_type_id,
              at.animal_type,
              csp.subscription_plan_l10n,
              csd.start_date,
              afd.active
            from main.care_calendar cc2 
            left join main.customer c on c.customer_id = cc2.entity_uuid 
            left join animal_farmer_details afd on afd.animal_id = cc2.entity_uuid 
            left join animal_type at on at.animal_id = cc2.entity_uuid 
            left join subscription_plan csp on csp.animal_id = cc2.entity_uuid
            left join cow_start_date csd on csd.animal_id = cc2.entity_uuid
          )
          select 
            cc.care_calendar_id as care_calendar_uuid,
            cc.entity_type_id,
            cdpc.customer_uuid,
            c.customer_name_l10n,
            c.mobile_number,
            fv.village_name,
            fv.taluk_name,
            fv.district_name,
            fv.state_name,
            cdpc.animal_uuid,
            cdpc.animal_eartag,
            cdpc.animal_type,
            cdpc.subscription_plan_l10n,
            cdpc.start_date,
            case 
              when cdpc.active = 1000100001 then 'Active'
              else 'Not Active'
            end as active,
            cc.activity_id,
            ra.activity_name_l10n,
            rr_ac.reference_name_l10n as activity_category_name_l10n,
            coalesce(cc.system_task_completion_time at time zone 'gmt' at time zone 'Asia/Kolkata',
              cc.activity_date at time zone 'gmt' at time zone 'Asia/Kolkata') as activity_date,
            cc.calendar_activity_status as activity_status_id,
            case 
              when ra.activity_category = 1000270001 and cc.entity_type_id = 1000460002
                then pvp.medicine_cost
              when cc.activity_id in (1000470003)
                then rpv.medicine_cost
              else coalesce(mpc.medcost,0)
            end as medicine_cost,
            case 
              when ra.activity_category = 1000270001
                then pvp.pv_visit_cost
              when cc.activity_id in (1000470001,1000470003)
                then rpv.pv_visit_cost
              else 200 
            end as pv_cost,
            coalesce(vvc.vet_cost,0) as vet_cost
          from main.care_calendar cc 
          inner join main.ref_activity ra on ra.activity_id = cc.activity_id
          inner join main.ref_reference rr_ac on rr_ac.reference_id = ra.activity_category 
          inner join customer_details_per_cc cdpc on cdpc.ccid = cc.care_calendar_id
          inner join main.customer c on c.customer_id = cdpc.customer_uuid
          left join farmer_village fv on fv.customer_id = customer_uuid
          left join medcost_per_cc mpc on mpc.ccid = cc.care_calendar_id
          left join vet_visit_cost vvc on vvc.ccid = cc.care_calendar_id
          left join preventive_pv_cost pvp on pvp.ccid = cc.care_calendar_id 
          left join reproductive_pv_cost rpv on rpv.ccid = cc.care_calendar_id 
          where activity_date <= CURRENT_DATE
          and age(activity_date) <= '1 days'
          and cc.calendar_activity_status = 1000300050
          order by activity_date desc)$function$
          ;
      `
    )

    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION reporting.function_daily_active_animals()
        RETURNS void
        LANGUAGE sql
        AS $function$insert into reporting.snapshot_active_animal_count
        (snapshot_type_id,snapshot_timestamp,village_id,animal_type_id,active_animal_count)
        select 
        1000760001 as snapshot_type_id,
        current_date as snapshot_timestamp,
        cc.value_reference_id as village_id,
        ac.value_reference_id as animal_type_id,
        count(*) as active_animal_count
        from main.animal a
        inner join main.animal_classification ac2 on ac2.animal_id = a.animal_id
        inner join main.entity_relationship er on er.entity_2_entity_uuid = a.animal_id
        and er.active = 1000100001 and er.entity_relationship_type_id = 1000210004 
        inner join main.animal_classification ac on ac.animal_id = a.animal_id
        inner join main.customer c on c.customer_id = er.entity_1_entity_uuid 
        and c.customer_type_id = 1000220001 and c.active = 1000100001
        inner join main.customer_classification cc on cc.customer_id = er.entity_1_entity_uuid
        inner join main.ref_sdtv_view rsv on cc.value_reference_id = rsv.village_id
        where a.active = 1000100001 and a.animal_visual_id like 'MH%'
        and ac2.classifier_id = 2000000124 and ac2.active = 1000100001
        and ac.classifier_id = 2000000035 and ac.active = 1000100001
        and cc.classifier_id = 2000000055 and cc.active = 1000100001
        group by cc.value_reference_id,ac.value_reference_id
        order by village_id, animal_type_id$function$
        ;
    `)

    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION reporting.function_daily_farmers_activeanimals()
        RETURNS void
        LANGUAGE sql
        AS $function$INSERT INTO reporting.snapshot_farmer_active_animal_count 
        (snapshot_type_id,snapshot_timestamp,customer_id,animal_type_id,active_animal_count)
        select 
        1000760001 as snapshot_type_id,
        current_date as snapshot_timestamp,
        er.entity_1_entity_uuid as customer_id,
        ac.value_reference_id as animal_type_id,
        count(*) as active_animal_count
        from main.animal a
        inner join main.animal_classification ac2 on ac2.animal_id = a.animal_id 
        inner join main.entity_relationship er on er.entity_2_entity_uuid = a.animal_id
        and er.active = 1000100001 and er.entity_relationship_type_id = 1000210004
        inner join main.customer c on c.customer_id = er.entity_1_entity_uuid 
        and c.customer_type_id = 1000220001 and c.active = 1000100001
        inner join main.animal_classification ac on ac.animal_id = a.animal_id
        where a.active = 1000100001 and a.animal_visual_id like 'MH%'
        and ac2.classifier_id = 2000000124 and ac2.active = 1000100001
        and ac.classifier_id = 2000000035 and ac.active = 1000100001
        group by er.entity_1_entity_uuid,ac.value_reference_id
        order by customer_id, animal_type_id$function$
        ;
    `)
    
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION reporting.function_monthly_active_animals()
        RETURNS void
        LANGUAGE sql
        AS $function$insert into reporting.snapshot_active_animal_count
        (snapshot_type_id,snapshot_timestamp,village_id,animal_type_id,active_animal_count)
        select 
        1000760002 as snapshot_type_id,
        current_date - 1 as snapshot_timestamp,
        cc.value_reference_id as village_id,
        ac.value_reference_id as animal_type_id,
        count(*) as active_animal_count
        from main.animal a
        inner join main.animal_classification ac2 on ac2.animal_id = a.animal_id 
        inner join main.entity_relationship er on er.entity_2_entity_uuid = a.animal_id
        and er.active = 1000100001 and er.entity_relationship_type_id = 1000210004
        inner join main.animal_classification ac on ac.animal_id = a.animal_id
        inner join main.customer c on c.customer_id = er.entity_1_entity_uuid 
        and c.customer_type_id = 1000220001 and c.active = 1000100001
        inner join main.customer_classification cc on cc.customer_id = er.entity_1_entity_uuid
        inner join main.ref_sdtv_view rsv on cc.value_reference_id = rsv.village_id
        where a.active = 1000100001 and a.animal_visual_id like 'MH%'
        and ac2.classifier_id = 2000000124 and ac2.active = 1000100001
        and ac.classifier_id = 2000000035 and ac.active = 1000100001
        and cc.classifier_id = 2000000055 and cc.active = 1000100001
        group by cc.value_reference_id,ac.value_reference_id
        order by village_id, animal_type_id$function$
        ;
    `)

    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION reporting.function_monthly_farmers_activeanimals()
      RETURNS void
      LANGUAGE sql
      AS $function$INSERT INTO reporting.snapshot_farmer_active_animal_count 
      (snapshot_type_id,snapshot_timestamp,customer_id,animal_type_id,active_animal_count)
      select 
      1000760002 as snapshot_type_id,
      current_date-1 as snapshot_timestamp,
      er.entity_1_entity_uuid as customer_id,
      ac.value_reference_id as animal_type_id,
      count(*) as active_animal_count
      from main.animal a
      inner join main.animal_classification ac2 on ac2.animal_id = a.animal_id 
      inner join main.entity_relationship er on er.entity_2_entity_uuid = a.animal_id
      and er.active = 1000100001 and er.entity_relationship_type_id = 1000210004
      inner join main.customer c on c.customer_id = er.entity_1_entity_uuid 
      and c.customer_type_id = 1000220001 and c.active = 1000100001
      inner join main.animal_classification ac on ac.animal_id = a.animal_id
      where a.active = 1000100001 and a.animal_visual_id like 'MH%'
      and ac2.classifier_id = 2000000124 and ac2.active = 1000100001
      and ac.classifier_id = 2000000035 and ac.active = 1000100001
      group by er.entity_1_entity_uuid,ac.value_reference_id
      order by customer_id, animal_type_id$function$
      ;
    `)

    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION reporting.function_penalties_critical()
        RETURNS void
        LANGUAGE sql
        AS $function$insert into reporting.snapshot_visit_penalty 
        (care_calendar_uuid,entity_type_id,entity_uuid,service_request_no,
        vet_published_status_id,paravet_administered_status_id,
        vet_penalty_amount,paravet_penalty_amount,
        activity_category_id,activity_id,activity_status_id,
        snapshot_type_id,snapshot_timestamp,service_request_timestamp)
        with first_prescription_vet as (
            select 
            ccc.care_calendar_id ccid,
                row_number () over(partition by ccc.care_calendar_id order by ccc.created_at) as rownum
            from main.care_calendar_classification ccc 
            where ccc.classifier_id = 2000000185
                and ccc.value_json->>'status' = 'PUBLISHED'
            and ccc.active = 1000100001
        ),
        first_prescription_paravet as (
          select ccc.care_calendar_id ccid,
            row_number () over(partition by ccc.care_calendar_id order by ccc.created_at) as rownum
          from main.care_calendar_classification ccc 
            where ccc.classifier_id = 2000000185
            and ccc.value_json->>'status' = 'ADMINISTERED'
        ),
        vet_paravet_yes_no as (
          select cc.care_calendar_id ccid,
            case 
              when fppv.rownum = 1 then 1000105001
                    when fpv.rownum = 1 then 1000105001
                    else 1000105002
                    end as vet_published_yesno,
                case 
              when fppv.rownum = 1 then 1000105001
                    else 1000105002
                    end as paravet_administered_yesno
            from main.care_calendar cc
            left join first_prescription_vet fpv on fpv.ccid = cc.care_calendar_id 
            and fpv.rownum=1
            left join first_prescription_paravet fppv on fppv.ccid = cc.care_calendar_id
            and fppv.rownum=1
        ),
          ccc_service_requests as(
            select ccc4.care_calendar_id ccid, 
            string_agg(ccc4.value_string_256,',') as sr_number
            from main.care_calendar_classification ccc4
            where ccc4.classifier_id = 2000000125 and ccc4.active = 1000100001
            group by ccc4.care_calendar_id 
        ),
        cc_visit_creation as(
          select 
            cc.care_calendar_id ccid,
                cc.activity_date at time zone 'gmt' at time zone 'Asia/Kolkata' as activity_date,
                cc.visit_schedule_time at time zone 'gmt' at time zone 'Asia/Kolkata' as visit_schedule_time
            from main.care_calendar cc
        )
        select 
          cc.care_calendar_id as care_calendar_uuid,
            cc.entity_type_id as entity_type_id,
            cc.entity_uuid ,
            ccc_service_requests.sr_number as service_request_no,
            vpyn.vet_published_yesno as vet_published_status_id,
            vpyn.paravet_administered_yesno as paravet_administered_status_id,
            case 
            when vpyn.vet_published_yesno = 1000105002 then 100
                when vpyn.paravet_administered_yesno = 1000105002 then 50
                else 0
            end as vet_penalty_amount,
            case
            when vpyn.vet_published_yesno = 1000105001 and vpyn.paravet_administered_yesno = 1000105002 
                then 100
                else 0
                end as paravet_penalty_amount,
            ra.activity_category as activity_category_id,
            cc.activity_id as activity_id,
            cc.calendar_activity_status as activity_status_id,
          1000760001 as snapshot_type_id,
          now() as snapshot_timestamp,
          coalesce(cvc.visit_schedule_time,cvc.activity_date) as service_request_timestamp
        from main.care_calendar cc 
            inner join cc_visit_creation cvc on cvc.ccid = cc.care_calendar_id
            inner join main.ref_activity ra on cc.activity_id = ra.activity_id 
            left join vet_paravet_yes_no vpyn on vpyn.ccid = cc.care_calendar_id
            left join ccc_service_requests on ccc_service_requests.ccid = cc.care_calendar_id
            where age(cvc.activity_date) >= '1 days' 
            and age(cvc.activity_date) < '2 days'
                and cc.calendar_activity_status not in (1000300051,1000300052)
                and ra.activity_category = 1000270002
            order by cvc.activity_date desc,cc.care_calendar_id$function$
        ;
    `)

    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION reporting.function_penalties_preventive()
        RETURNS void
        LANGUAGE sql
        AS $function$insert into reporting.snapshot_preventive_visit_penalty 
        (care_calendar_uuid,entity_type_id,entity_uuid,activity_id,vet_uuid,paravet_uuid,
        vet_penalty_amount,paravet_penalty_amount,activity_status_id,activity_category_id,
        snapshot_type_id,snapshot_timestamp,activity_timestamp)
        with egu as(
          select 
            eg.geography_id, eg.entity_type_id, 
            eg.entity_uuid as staff_id,
            row_number() over (partition by eg.geography_id,eg.entity_type_id
              order by eg.geography_id,eg.entity_type_id,eg.entity_uuid) as rownum
          from main.entity_geography eg 
          where eg.geography_type_id = 1000320004
            and eg.active = 1000100001
          order by eg.geography_id
        ),
        animal_details as (
        select 
          a.animal_id as animal_id,
          c.customer_id as customer_id,
          v_lss.staff_id as paravet_uuid,
          v_bdm.staff_id as ro_uuiid,
          v_vet.staff_id as vet_uuid,
          row_number() over (partition by c.customer_id order by a.active) as rownum,
            a.active
        from main.animal a 
          inner join main.entity_relationship er on er.entity_2_entity_uuid = a.animal_id
          inner join main.customer c on er.entity_1_entity_uuid = c.customer_id
            and c.customer_type_id = 1000220001
          left join main.customer_classification cc_v on c.customer_id = cc_v.customer_id 
            and cc_v.classifier_id = 2000000055
          left join egu as v_lss 
            on cc_v.value_reference_id = v_lss.geography_id 
            and v_lss.rownum = 1 and v_lss.entity_type_id = 1000230004
          left join egu as v_bdm
            on cc_v.value_reference_id = v_bdm.geography_id 
            and v_bdm.rownum = 1 and v_bdm.entity_type_id = 1000230001
          left join egu as v_vet
            on cc_v.value_reference_id = v_vet.geography_id 
            and v_vet.rownum = 1 and v_vet.entity_type_id = 1000230003
          left join egu as v_zo
            on cc_v.value_reference_id = v_zo.geography_id 
            and v_zo.rownum = 1 and v_zo.entity_type_id = 1000230005		
        )
        select 
          cc.care_calendar_id as care_calendar_uuid,
          cc.entity_type_id,
          cc.entity_uuid,
          cc.activity_id,
          ad.vet_uuid,
          ad.paravet_uuid,
          50 as vet_penalty_amount,
          100 as paravet_penalty_amount,
          cc.calendar_activity_status as activity_status_id,
          ra.activity_category as activity_category_id,
          1000760001 as snapshot_type_id,
          now() as snapshot_timestamp,
          cc.activity_date at time zone 'gmt' at time zone 'Asia/Kolkata' as activity_timestamp
        from main.care_calendar cc 
        inner join main.ref_activity ra on ra.activity_id = cc.activity_id 
        inner join animal_details ad on ad.customer_id = cc.entity_uuid 
        and ad.rownum = 1 
        and ad.active = 1000100001
        where ra.activity_category = 1000270001
          and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
          and age(cc.activity_date at time zone 'gmt' at time zone 'Asia/Kolkata') >= '4 days'
          and age(cc.activity_date at time zone 'gmt' at time zone 'Asia/Kolkata') < '5 days'
          and cc.entity_type_id = 1000460001
        union 
        select 
          cc.care_calendar_id as care_calendar_uuid,
          cc.entity_type_id,
          cc.entity_uuid,
          cc.activity_id,
          ad.vet_uuid,
          ad.paravet_uuid,
          50 as vet_penalty_amount,
          100 as paravet_penalty_amount,
          cc.calendar_activity_status as activity_status_id,
          ra.activity_category as activity_category_id,
          1000760001 as snapshot_type_id,
          now() as snapshot_timestamp,
          cc.activity_date at time zone 'gmt' at time zone 'Asia/Kolkata' as activity_timestamp
          from main.care_calendar cc 
          inner join main.ref_activity ra on ra.activity_id = cc.activity_id 
          inner join animal_details ad on ad.animal_id = cc.entity_uuid
            where ra.activity_category = 1000270001
            and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
            and age(cc.activity_date at time zone 'gmt' at time zone 'Asia/Kolkata') >= '4 days'
            and age(cc.activity_date at time zone 'gmt' at time zone 'Asia/Kolkata') < '5 days'
            and cc.entity_type_id = 1000460002
            and ad.active = 1000100001
        order by activity_timestamp desc$function$
        ;
    `)
  }

  async down(queryRunner) {
    await queryRunner.query(`
      DROP FUNCTION IF EXISTS reporting.function_penalties_preventive
    `)

    await queryRunner.query(`
      DROP FUNCTION IF EXISTS reporting.function_penalties_critical
    `)

    await queryRunner.query(`
      DROP FUNCTION IF EXISTS reporting.function_monthly_farmers_activeanimals
    `)

    await queryRunner.query(`
      DROP FUNCTION IF EXISTS reporting.function_monthly_active_animals
    `)

    await queryRunner.query(`
      DROP FUNCTION IF EXISTS reporting.function_daily_farmers_activeanimals
    `)

    await queryRunner.query(`
      DROP FUNCTION IF EXISTS reporting.function_daily_active_animals
    `)

    await queryRunner.query(`
      DROP FUNCTION IF EXISTS reporting.function_cost_per_cc
    `)
  }
}
