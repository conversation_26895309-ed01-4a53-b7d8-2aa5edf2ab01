const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddClassiFicationHistoryTable1685956629438 {
    name = 'AddClassiFicationHistoryTable1685956629438'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."animal_classification_history" ("animal_classification_history_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "animal_classification_id" uuid NOT NULL, "animal_id" uuid, "classifier_id" integer, "temporal_entity_type_id" integer, "temporal_entity_uuid" uuid, "temporal_entity_id" integer, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "source_created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "source_updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_animal_classification_history_id" PRIMARY KEY ("animal_classification_history_id"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "main"."animal_classification_history"`);
    }
}
