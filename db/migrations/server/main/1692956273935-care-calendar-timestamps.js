const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1692956273935 {
    name = 'Main1692956273935'

    async up(queryRunner) {

        await queryRunner.query(`ALTER TABLE "main"."care_calendar" ADD "system_task_completion_time" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "main"."care_calendar" ADD "visit_schedule_time" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "main"."care_calendar" ADD "visit_creation_time" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "main"."care_calendar" ADD "user_task_completion_time" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "main"."care_calendar" ADD "activity_created_by" uuid`);
        await queryRunner.query(`ALTER TABLE "main"."care_calendar" ADD "activity_completed_by" uuid`);
        await queryRunner.query(`ALTER TABLE "main"."care_calendar" ADD "active" integer NOT NULL DEFAULT '1000100001'`);

    }

    async down(queryRunner) {
       
        await queryRunner.query(`ALTER TABLE "main"."care_calendar" DROP COLUMN "active"`);
        await queryRunner.query(`ALTER TABLE "main"."care_calendar" DROP COLUMN "activity_completed_by"`);
        await queryRunner.query(`ALTER TABLE "main"."care_calendar" DROP COLUMN "activity_created_by"`);
        await queryRunner.query(`ALTER TABLE "main"."care_calendar" DROP COLUMN "user_task_completion_time"`);
        await queryRunner.query(`ALTER TABLE "main"."care_calendar" DROP COLUMN "visit_creation_time"`);
        await queryRunner.query(`ALTER TABLE "main"."care_calendar" DROP COLUMN "visit_schedule_time"`);
        await queryRunner.query(`ALTER TABLE "main"."care_calendar" DROP COLUMN "system_task_completion_time"`);


    }
}
