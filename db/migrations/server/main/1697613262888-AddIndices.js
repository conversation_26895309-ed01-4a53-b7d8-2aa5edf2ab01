const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddIndices1697613262888 {
    name = 'AddIndices1697613262888'

    async up(queryRunner) {
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_ref_village_taluk_id" ON "main"."ref_village" ("taluk_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_document_entity_1_id" ON "main"."document" ("entity_1_entity_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_document_entity_1_entity_uuid" ON "main"."document" ("entity_1_entity_uuid") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_document_entity_1_entity_id" ON "main"."document" ("entity_1_type_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_document_document_type_id" ON "main"."document" ("document_type_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_entity_relationship_entity_relationship_type_id" ON "main"."entity_relationship" ("entity_relationship_type_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_entity_relationship_entity_2_entity_uuid" ON "main"."entity_relationship" ("entity_2_entity_uuid") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_entity_relationship_entity_2_entity_type" ON "main"."entity_relationship" ("entity_2_entity_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_entity_relationship_entity_1_entity_uuid" ON "main"."entity_relationship" ("entity_1_entity_uuid") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_entity_relationship_entity_1_entity_id" ON "main"."entity_relationship" ("entity_1_entity_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_entity_geography_geography_id" ON "main"."entity_geography" ("geography_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_entity_geography_entity_uuid" ON "main"."entity_geography" ("entity_uuid") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_entity_geography_entity_type_id" ON "main"."entity_geography" ("entity_type_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_entity_geography_entity_id" ON "main"."entity_geography" ("entity_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_staff_staff_type_id" ON "main"."staff" ("staff_type_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_customer_classification_customer_id" ON "main"."customer_classification" ("customer_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_customer_classification_classifier_id" ON "main"."customer_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_animal_animal_type_id" ON "main"."animal" ("animal_type") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_animal_classification_classifier_id" ON "main"."animal_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_animal_classification_animal_id" ON "main"."animal_classification" ("animal_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_care_calendar_entity_uuid" ON "main"."care_calendar" ("entity_uuid") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_care_calendar_entity_type_id" ON "main"."care_calendar" ("entity_type_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_care_calendar_entity_id" ON "main"."care_calendar" ("entity_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_care_calendar_calendar_activity_status" ON "main"."care_calendar" ("calendar_activity_status") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_care_calendar_activity_id" ON "main"."care_calendar" ("activity_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_care_calendar_activity_date_desc" ON "main"."care_calendar" ("activity_date") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_care_calendar_classification_classifier_id" ON "main"."care_calendar_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_care_calendar_classification_cc_id" ON "main"."care_calendar_classification" ("care_calendar_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_staff_activity_staff_id" ON "main"."staff_activity" ("staff_id") `);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS"idx_staff_activity_care_calendar_id" ON "main"."staff_activity" ("care_calendar") `);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "main"."idx_staff_activity_care_calendar_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_staff_activity_staff_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_care_calendar_classification_cc_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_care_calendar_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_care_calendar_activity_date_desc"`);
        await queryRunner.query(`DROP INDEX "main"."idx_care_calendar_activity_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_care_calendar_calendar_activity_status"`);
        await queryRunner.query(`DROP INDEX "main"."idx_care_calendar_entity_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_care_calendar_entity_type_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_care_calendar_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "main"."idx_animal_classification_animal_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_animal_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_animal_animal_type_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_customer_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_customer_classification_customer_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_staff_staff_type_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_entity_geography_entity_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_entity_geography_entity_type_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_entity_geography_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "main"."idx_entity_geography_geography_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_entity_relationship_entity_1_entity_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_entity_relationship_entity_1_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "main"."idx_entity_relationship_entity_2_entity_type"`);
        await queryRunner.query(`DROP INDEX "main"."idx_entity_relationship_entity_2_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "main"."idx_entity_relationship_entity_relationship_type_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_document_document_type_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_document_entity_1_entity_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_document_entity_1_entity_uuid"`);
        await queryRunner.query(`DROP INDEX "main"."idx_document_entity_1_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_ref_village_taluk_id"`);
    }
}
