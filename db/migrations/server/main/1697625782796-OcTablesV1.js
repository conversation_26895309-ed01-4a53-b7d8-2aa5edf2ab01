const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class OcTablesV11697625782796 {
    name = 'OcTablesV11697625782796'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."entity_status" ("entity_status_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "status_category_id" integer, "status_id" integer, "entity_1_type_id" integer, "entity_1_entity_uuid" uuid, "entity_1_entity_id" integer, "entity_2_type_id" integer, "entity_2_entity_uuid" uuid, "entity_2_entity_id" integer, "start_date" TIMESTAMP DEFAULT TO_TIMESTAMP('1900-01-01 00:00:01', 'YYYY-MM-DD HH24:MI:SS'), "end_date" TIMESTAMP DEFAULT TO_TIMESTAMP('2099-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS'), "status_information" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_entity_status_uuid" PRIMARY KEY ("entity_status_uuid"))`);
        await queryRunner.query(`CREATE TABLE "main"."oc_item" ("oc_item_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "oc_item_name_l10n" jsonb, "oc_item_type_id" integer, "linked_entity_type_id" integer, "linked_entity_id" integer, "linked_entity_uuid" uuid, "inventory_details_id" integer, "item_information" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_oc_item_uuid" PRIMARY KEY ("oc_item_uuid"))`);
        await queryRunner.query(`CREATE TABLE "main"."oc_item_classification" ("oc_item_classification_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "oc_item_uuid" uuid NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_oc_item_classification_uuid" PRIMARY KEY ("oc_item_classification_uuid"))`);
        await queryRunner.query(`CREATE TABLE "main"."oc_item_listing" ("oc_item_listing_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "listing_item_type_id" integer, "listing_item_id" integer, "listing_item_uuid" uuid, "payment_term_uuid" uuid, "item_pricing_uuid" uuid, "start_date" TIMESTAMP DEFAULT TO_TIMESTAMP('1900-01-01 00:00:01', 'YYYY-MM-DD HH24:MI:SS'), "end_date" TIMESTAMP DEFAULT TO_TIMESTAMP('2099-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS'), "item_listing_information" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_oc_item_listing_uuid" PRIMARY KEY ("oc_item_listing_uuid"))`);
        await queryRunner.query(`CREATE TABLE "main"."oc_item_document" ("oc_item_document_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "oc_item_listing_uuid" uuid, "document_name_l10n" jsonb, "document_type_id" integer NOT NULL, "document_status_id" integer, "entity_1_type_id" integer, "entity_1_entity_uuid" uuid, "entity_1_entity_id" integer, "entity_2_type_id" integer, "entity_2_entity_uuid" uuid, "entity_2_entity_id" integer, "entity_3_type_id" integer, "entity_3_entity_uuid" uuid, "entity_3_entity_id" integer, "client_document_information" jsonb, "document_information" jsonb, "document_meta_data_information" jsonb, "document_sync_status" integer NOT NULL DEFAULT '1000102001', "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_oc_item_document_uuid" PRIMARY KEY ("oc_item_document_uuid"))`);
        await queryRunner.query(`CREATE TABLE "main"."oc_order" ("oc_order_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "order_type_id" integer, "order_category_id" integer, "buyer_entity_type_id" integer, "buyer_entity_uuid" uuid, "order_grammar_id" integer, "order_information" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_oc_order_uuid" PRIMARY KEY ("oc_order_uuid"))`);
        await queryRunner.query(`CREATE TABLE "main"."oc_order_document" ("oc_order_document_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "oc_order_uuid" uuid, "document_name_l10n" jsonb, "document_type_id" integer, "document_status_id" integer, "entity_1_type_id" integer, "entity_1_entity_uuid" uuid, "entity_1_entity_id" integer, "entity_2_type_id" integer, "entity_2_entity_uuid" uuid, "entity_2_entity_id" integer, "entity_3_type_id" integer, "entity_3_entity_uuid" uuid, "entity_3_entity_id" integer, "client_document_information" jsonb, "document_information" jsonb, "document_meta_data_information" jsonb, "document_sync_status" integer NOT NULL DEFAULT '1000102001', "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_oc_order_document_uuid" PRIMARY KEY ("oc_order_document_uuid"))`);
        await queryRunner.query(`CREATE TABLE "main"."oc_order_payment" ("oc_order_payment_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "oc_order_uuid" uuid, "entity_type_id" integer, "entity_entity_uuid" uuid, "entity_entity_id" integer, "payment_amount" numeric(14,4), "oc_pricing_attribute_id" integer, "payment_date" TIMESTAMP, "order_payment_information" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_oc_order_payment_uuid" PRIMARY KEY ("oc_order_payment_uuid"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "main"."oc_order_payment"`);
        await queryRunner.query(`DROP TABLE "main"."oc_order_document"`);
        await queryRunner.query(`DROP TABLE "main"."oc_order"`);
        await queryRunner.query(`DROP TABLE "main"."oc_item_document"`);
        await queryRunner.query(`DROP TABLE "main"."oc_item_listing"`);
        await queryRunner.query(`DROP TABLE "main"."oc_item_classification"`);
        await queryRunner.query(`DROP TABLE "main"."oc_item"`);
        await queryRunner.query(`DROP TABLE "main"."entity_status"`);
    }
}
