const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class InventoryRelease1709900060310 {
    name = 'InventoryRelease1709900060310'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."requisition" ("requisition_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "requisitioner_type_id" integer, "requisitioner_uuid" uuid, "fullfiller_type_id" integer, "fullfiller_uuid" uuid, "approver_type_id" integer, "approver_uuid" uuid, "line_items_count" integer DEFAULT '0', "status_id" integer DEFAULT '1010000001', "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_requisition_id" PRIMARY KEY ("requisition_id"))`);
        await queryRunner.query(`CREATE INDEX "idx_requisition_status_id" ON "main"."requisition" ("status_id") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_requisitioner_uuid" ON "main"."requisition" ("requisitioner_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_approver_uuid" ON "main"."requisition" ("approver_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_requisition_fullfiller_uuid" ON "main"."requisition" ("fullfiller_uuid") `);
        await queryRunner.query(`CREATE TABLE "main"."requisition_classification" ("requisition_classification_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "requisition_id" uuid NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_requisition_classification_id" PRIMARY KEY ("requisition_classification_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."requisition_line_items" ("requisition_line_items_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "requisition_id" uuid, "requisitioner_uuid" uuid, "medicine_id" integer, "unit" character varying, "quantity" numeric(14,4) DEFAULT '0', "item_status_id" integer DEFAULT '0', "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_requisition_line_items_id" PRIMARY KEY ("requisition_line_items_id"))`);
        await queryRunner.query(`CREATE INDEX "idx_requisitionlineitems_requisition_id" ON "main"."requisition_line_items" ("requisition_id") `);
        await queryRunner.query(`CREATE TABLE "main"."inventory_ledger" ("inventory_ledger_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "requisition_id" uuid, "medicine_id" integer, "warehouse_type_id" integer, "warehouse_uuid" uuid, "source_type_id" integer, "source_uuid" uuid, "consumer_type_id" integer DEFAULT '0', "consumer_uuid" uuid, "unit" character varying, "credit_qty" numeric(14,4) NOT NULL DEFAULT '0', "debit_qty" numeric(14,4) NOT NULL DEFAULT '0', "balance" numeric(14,4) NOT NULL DEFAULT '0', "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), "inserted_at" TIMESTAMP, CONSTRAINT "PK_inventory_ledger_id" PRIMARY KEY ("inventory_ledger_id"))`);
        await queryRunner.query(`CREATE INDEX "idx_inventoryledger_source_uuid" ON "main"."inventory_ledger" ("source_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_inventoryledger_warehouse_uuid" ON "main"."inventory_ledger" ("warehouse_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_inventoryledger_inserted_at" ON "main"."inventory_ledger" ("inserted_at") `);
        await queryRunner.query(`CREATE TABLE "main"."warehouse_blockqty" ("warehouse_blockqty_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "source_type_id" integer, "source_uuid" uuid, "warehouse_type_id" integer, "warehouse_uuid" uuid, "medicine_id" integer, "unit" character varying, "blocked_qty" numeric(14,4) DEFAULT '0', "unblocked_qty" numeric(14,4) DEFAULT '0', "balance_qty" numeric(14,4) DEFAULT '0', "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_warehouse_blockqty_id" PRIMARY KEY ("warehouse_blockqty_id"))`);
        await queryRunner.query(`CREATE INDEX "idx_warehouseblockqty_warehouse_uuid" ON "main"."warehouse_blockqty" ("warehouse_uuid") `);
        await queryRunner.query(`CREATE INDEX "idx_warehouseblockqty_source_uuid" ON "main"."warehouse_blockqty" ("source_uuid") `);
        await queryRunner.query(`ALTER TABLE "main"."job_tracker" ALTER COLUMN "created_at" SET DEFAULT 'NOW()'`);
        await queryRunner.query(`ALTER TABLE "main"."job_tracker" ALTER COLUMN "updated_at" SET DEFAULT 'NOW()'`);
        await queryRunner.query(`CREATE INDEX "idx_user_event_time_at" ON "main"."user_event" ("time_at") `);
        await queryRunner.query(`CREATE INDEX "idx_user_event_user_device_id" ON "main"."user_event" ("user_device_id") `);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "main"."idx_user_event_user_device_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_user_event_time_at"`);
        await queryRunner.query(`ALTER TABLE "main"."job_tracker" ALTER COLUMN "updated_at" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "main"."job_tracker" ALTER COLUMN "created_at" SET DEFAULT now()`);
        await queryRunner.query(`DROP INDEX "main"."idx_warehouseblockqty_source_uuid"`);
        await queryRunner.query(`DROP INDEX "main"."idx_warehouseblockqty_warehouse_uuid"`);
        await queryRunner.query(`DROP TABLE "main"."warehouse_blockqty"`);
        await queryRunner.query(`DROP INDEX "main"."idx_inventoryledger_inserted_at"`);
        await queryRunner.query(`DROP INDEX "main"."idx_inventoryledger_warehouse_uuid"`);
        await queryRunner.query(`DROP INDEX "main"."idx_inventoryledger_source_uuid"`);
        await queryRunner.query(`DROP TABLE "main"."inventory_ledger"`);
        await queryRunner.query(`DROP INDEX "main"."idx_requisitionlineitems_requisition_id"`);
        await queryRunner.query(`DROP TABLE "main"."requisition_line_items"`);
        await queryRunner.query(`DROP TABLE "main"."requisition_classification"`);
        await queryRunner.query(`DROP INDEX "main"."idx_requisition_fullfiller_uuid"`);
        await queryRunner.query(`DROP INDEX "main"."idx_requisition_approver_uuid"`);
        await queryRunner.query(`DROP INDEX "main"."idx_requisition_requisitioner_uuid"`);
        await queryRunner.query(`DROP INDEX "main"."idx_requisition_status_id"`);
        await queryRunner.query(`DROP TABLE "main"."requisition"`);
    }
}
