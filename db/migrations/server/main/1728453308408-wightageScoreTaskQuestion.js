const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1728453308408 {
    name = 'Main1728453308408'

    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "main"."task_question" ADD "weightage" integer`);
        await queryRunner.query(`ALTER TABLE "main"."task_question_data" ADD "weightage" integer`);
        await queryRunner.query(`ALTER TABLE "main"."task_question_data" ADD "score" integer`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "main"."task_question_data" DROP COLUMN "score"`);
        await queryRunner.query(`ALTER TABLE "main"."task_question_data" DROP COLUMN "weightage"`);
        await queryRunner.query(`ALTER TABLE "main"."task_question" DROP COLUMN "weightage"`);
    }
}
