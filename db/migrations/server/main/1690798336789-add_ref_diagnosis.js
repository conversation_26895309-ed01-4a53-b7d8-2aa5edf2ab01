const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1690798336789 {
    name = 'Main1690798336789'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."ref_diagnosis" ("reference_id" SERIAL NOT NULL, "name" character varying NOT NULL, "category" integer NOT NULL, "reference_name_l10n" jsonb, "reference_information" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_ref_diagnosis_id" PRIMARY KEY ("reference_id"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "main"."ref_diagnosis"`);
    }
}
