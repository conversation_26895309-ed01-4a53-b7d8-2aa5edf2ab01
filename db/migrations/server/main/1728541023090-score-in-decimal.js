const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1728541023090 {
    name = 'Main1728541023090'

    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "main"."task_question_data" DROP COLUMN "score"`);
        await queryRunner.query(`ALTER TABLE "main"."task_question_data" ADD "score" numeric(3,2)`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "main"."task_question_data" DROP COLUMN "score"`);
        await queryRunner.query(`ALTER TABLE "main"."task_question_data" ADD "score" integer`);
    }
}
