const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1700672528658 {
    name = 'Main1700672528658'

    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "main"."transaction_ledger" ALTER COLUMN "entity_type_id" DROP NOT NULL`);

    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "main"."transaction_ledger" ALTER COLUMN "entity_type_id" SET NOT NULL`);
    }
}
