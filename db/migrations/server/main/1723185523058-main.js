const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1723185523058 {
    name = 'Main1723185523058'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."task_question_data" ("question_data_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "care_calendar_id" uuid NOT NULL, "question_uuid" uuid NOT NULL, "form_configuration" jsonb, "review_date" character varying, "priority_grouping" integer, "active" integer NOT NULL DEFAULT '1000100001', "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_question_data_uuid" PRIMARY KEY ("question_data_uuid"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "main"."task_question_data"`);
    }
}
