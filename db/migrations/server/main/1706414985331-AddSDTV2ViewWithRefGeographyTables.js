const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddSDTV2ViewWithRefGeographyTables1706414985331 {
  async up(queryRunner) {
    await queryRunner.query(
      `
        CREATE OR REPLACE VIEW "main"."ref_sdtv_view_2" AS
          select rgv.geography_id as village_id, rgv.geography_name_l10n as village_name_l10n, rgv.geography_short_code as village_short_code,
            rgt.geography_id as taluk_id, rgt.geography_name_l10n as taluk_name_l10n, rgt.geography_short_code as taluk_short_code,
            rgd.geography_id as district_id, rgd.geography_name_l10n as district_name_l10n, rgd.geography_short_code as district_short_code,
            rgs.geography_id as state_id, rgs.geography_name_l10n as state_name_l10n, rgs.geography_short_code as state_short_code	
          from main.ref_geography rgv
          inner join main.entity_relationship t2v on t2v.entity_2_type_id = 1000460015 and t2v.active = 1000100001
            and t2v.entity_1_type_id = 1000460015 and t2v.entity_relationship_type_id = 1000210031 and t2v.entity_2_entity_id = rgv.geography_id
          inner join main.ref_geography rgt on rgt.geography_type_id = 1000320003 and rgt.active = 1000100001
            and rgt.geography_id = t2v.entity_1_entity_id 
          inner join main.entity_relationship d2t on d2t.entity_2_type_id = 1000460015 and d2t.active = 1000100001
            and d2t.entity_1_type_id = 1000460015 and d2t.entity_relationship_type_id = 1000210030 and d2t.entity_2_entity_id = rgt.geography_id
          inner join main.ref_geography rgd on rgd.geography_type_id = 1000320002 and rgd.active = 1000100001
            and rgd.geography_id = d2t.entity_1_entity_id 
          inner join main.entity_relationship s2d on s2d.entity_2_type_id = 1000460015 and s2d.active = 1000100001
            and s2d.entity_1_type_id = 1000460015 and s2d.entity_relationship_type_id = 1000210029 and s2d.entity_2_entity_id = rgd.geography_id
          inner join main.ref_geography rgs on rgs.geography_type_id = 1000320001 and rgs.active = 1000100001
            and rgs.geography_id = s2d.entity_1_entity_id 
          where rgv.geography_type_id = 1000320004 and rgv.active = 1000100001      
      `
    )
  }

  async down(queryRunner) {
    await queryRunner.query(
      `
        DROP VIEW ref_sdtv_view_2
      `
    )
  }
}
