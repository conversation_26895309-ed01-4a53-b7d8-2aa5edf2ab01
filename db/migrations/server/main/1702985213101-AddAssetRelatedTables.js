const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddAssetRelatedTables1702985213101 {
    name = 'AddAssetRelatedTables1702985213101'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."asset" ("asset_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "asset_visual_id" character varying(256), "asset_name_l10n" jsonb, "asset_type_id" integer, "start_date" TIMESTAMP DEFAULT TO_TIMESTAMP('1900-01-01 00:00:01', 'YYYY-MM-DD HH24:MI:SS'), "end_date" TIMESTAMP DEFAULT TO_TIMESTAMP('2099-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS'), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_asset_id" PRIMARY KEY ("asset_id"))`);
        await queryRunner.query(`CREATE INDEX "idx_asset_asset_type_id" ON "main"."asset" ("asset_type_id") `);
        await queryRunner.query(`CREATE TABLE "main"."asset_classification" ("asset_classification_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "asset_id" uuid NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_asset_classification_id" PRIMARY KEY ("asset_classification_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."asset_feature" ("asset_feature_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "asset_id" uuid NOT NULL, "feature_name_l10n" jsonb, "feature_type_id" integer, "start_date" TIMESTAMP DEFAULT TO_TIMESTAMP('1900-01-01 00:00:01', 'YYYY-MM-DD HH24:MI:SS'), "end_date" TIMESTAMP DEFAULT TO_TIMESTAMP('2099-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS'), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_asset_feature_id" PRIMARY KEY ("asset_feature_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."asset_feature_classification" ("asset_feature_classification_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "asset_feature_id" uuid NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_asset_feature_classification_id" PRIMARY KEY ("asset_feature_classification_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."ref_period" ("period_id" SERIAL NOT NULL, "period_type_id" integer, "period_name" character varying(256), "period_name_l10n" jsonb, "period_information" jsonb, "start_date" TIMESTAMP DEFAULT TO_TIMESTAMP('1900-01-01 00:00:01', 'YYYY-MM-DD HH24:MI:SS'), "end_date" TIMESTAMP DEFAULT TO_TIMESTAMP('2099-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS'), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_period_id" PRIMARY KEY ("period_id"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "main"."ref_period"`);
        await queryRunner.query(`DROP TABLE "main"."asset_feature_classification"`);
        await queryRunner.query(`DROP TABLE "main"."asset_feature"`);
        await queryRunner.query(`DROP TABLE "main"."asset_classification"`);
        await queryRunner.query(`DROP INDEX "main"."idx_asset_asset_type_id"`);
        await queryRunner.query(`DROP TABLE "main"."asset"`);
    }
}
