const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddCustomerFeatureRelatedTables1703585945006 {
    name = 'AddCustomerFeatureRelatedTables1703585945006'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."customer_feature" ("customer_feature_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "customer_id" uuid NOT NULL, "feature_name_l10n" jsonb, "feature_type_id" integer, "start_date" TIMESTAMP DEFAULT TO_TIMESTAMP('1900-01-01 00:00:01', 'YYYY-MM-DD HH24:MI:SS'), "end_date" TIMESTAMP DEFAULT TO_TIMESTAMP('2099-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS'), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_customer_feature_id" PRIMARY KEY ("customer_feature_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."customer_feature_classification" ("customer_feature_classification_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "customer_feature_id" uuid NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_customer_feature_classification_id" PRIMARY KEY ("customer_feature_classification_id"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "main"."customer_feature_classification"`);
        await queryRunner.query(`DROP TABLE "main"."customer_feature"`);
    }
}
