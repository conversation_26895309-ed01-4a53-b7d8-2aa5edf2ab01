const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class InitialDesign1672035003652 {
    name = 'InitialDesign1672035003652'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."ref_reference_category" ("reference_category_id" SERIAL NOT NULL, "reference_name" character varying(256) NOT NULL, "reference_category_name_l10n" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_reference_category_id" PRIMARY KEY ("reference_category_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."ref_reference" ("reference_id" SERIAL NOT NULL, "reference_category_id" integer NOT NULL, "field_name" character varying(256), "reference_name" character varying(256) NOT NULL, "reference_name_l10n" jsonb, "reference_information" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "type_of_data" character varying(256), "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_reference_id" PRIMARY KEY ("reference_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."ref_state" ("state_id" SERIAL NOT NULL, "state_census_id" integer, "state_name_l10n" jsonb, "state_short_code" character varying(256), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_state_id" PRIMARY KEY ("state_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."ref_district" ("district_id" SERIAL NOT NULL, "state_id" integer NOT NULL, "district_census_id" integer, "district_name_l10n" jsonb, "district_short_code" character varying(256), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_district_id" PRIMARY KEY ("district_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."ref_taluk" ("taluk_id" SERIAL NOT NULL, "district_id" integer NOT NULL, "taluk_census_id" integer, "taluk_name_l10n" jsonb, "taluk_short_code" character varying(256), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_taluk_id" PRIMARY KEY ("taluk_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."ref_village" ("village_id" SERIAL NOT NULL, "taluk_id" integer NOT NULL, "village_census_id" integer, "village_name_l10n" jsonb, "village_short_code" character varying(256), "village_pin_code" character varying(256), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_village_id" PRIMARY KEY ("village_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."document" ("document_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "document_type_id" integer NOT NULL, "document_name_l10n" jsonb, "document_name" character varying(256), "document_status_id" integer, "entity_1_type_id" integer, "entity_1_entity_uuid" uuid, "entity_1_entity_id" integer, "entity_2_type_id" integer, "entity_2_entity_uuid" uuid, "entity_2_entity_id" integer, "entity_3_type_id" integer, "entity_3_entity_uuid" uuid, "entity_3_entity_id" integer, "client_document_information" jsonb, "document_information" jsonb, "document_meta_data_information" jsonb, "document_sync_status" integer NOT NULL DEFAULT '1000102001', "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_document_id" PRIMARY KEY ("document_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."entity_relationship" ("entity_relationship_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "entity_relationship_type_id" integer, "entity_1_type_id" integer, "entity_1_entity_uuid" uuid, "entity_1_entity_id" integer, "entity_2_type_id" integer, "entity_2_entity_uuid" uuid, "entity_2_entity_id" integer, "start_date" TIMESTAMP DEFAULT TO_TIMESTAMP('1900-01-01 00:00:01', 'YYYY-MM-DD HH24:MI:SS'), "end_date" TIMESTAMP DEFAULT TO_TIMESTAMP('2099-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS'), "entity_information" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_entity_relationship_id" PRIMARY KEY ("entity_relationship_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."entity_geography" ("entity_geography_id" SERIAL NOT NULL, "entity_type_id" integer, "entity_uuid" uuid, "entity_id" integer, "geography_type_id" integer, "geography_id" integer, "start_date" TIMESTAMP DEFAULT TO_TIMESTAMP('1900-01-01 00:00:01', 'YYYY-MM-DD HH24:MI:SS'), "end_date" TIMESTAMP DEFAULT TO_TIMESTAMP('2099-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS'), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_entity_geography_id" PRIMARY KEY ("entity_geography_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."staff" ("staff_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "staff_type_id" integer, "staff_visual_id" character varying(256), "staff_name_l10n" jsonb, "mobile_number" character varying(256), "email_address" character varying(256), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_staff_id" PRIMARY KEY ("staff_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."staff_classification" ("staff_classification_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "staff_id" uuid NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_staff_classification_id" PRIMARY KEY ("staff_classification_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."customer" ("customer_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "customer_type_id" integer, "customer_visual_id" character varying(256), "customer_name_l10n" jsonb, "mobile_number" character varying(256), "email_address" character varying(256), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_customer_id" PRIMARY KEY ("customer_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."customer_classification" ("customer_classification_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "customer_id" uuid NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_customer_classification_id" PRIMARY KEY ("customer_classification_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."animal" ("animal_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "animal_visual_id" character varying(256), "animal_type" integer, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_animal_id" PRIMARY KEY ("animal_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."animal_classification" ("animal_classification_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "animal_id" uuid NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_animal_classification_id" PRIMARY KEY ("animal_classification_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."care_calendar" ("care_calendar_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "entity_type_id" integer NOT NULL, "entity_id" integer, "entity_uuid" uuid, "activity_id" integer NOT NULL, "visit_id" integer, "activity_date" TIMESTAMP NOT NULL, "calendar_activity_status" integer NOT NULL DEFAULT '1000300001', "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_care_calendar_id" PRIMARY KEY ("care_calendar_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."care_calendar_classification" ("care_calendar_classification_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "care_calendar_id" uuid NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_care_calendar_classification_id" PRIMARY KEY ("care_calendar_classification_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."animal_disease" ("animal_disease_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "animal_id" uuid, "disease_id" integer, "disease_date" TIMESTAMP, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_animal_disease_id" PRIMARY KEY ("animal_disease_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."animal_disease_classification" ("animal_disease_classification_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "animal_disease_id" uuid NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_animal_disease_classification_id" PRIMARY KEY ("animal_disease_classification_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."dump_doc" ("dump_document_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "document_key" character varying NOT NULL, "error" jsonb, "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_dump_document_id" PRIMARY KEY ("dump_document_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."note" ("note_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "note_type_id" integer, "note_status_id" integer, "note_time" TIMESTAMP, "note" jsonb NOT NULL, "entity_1_type_id" integer, "entity_1_uuid" uuid, "entity_1_id" integer, "entity_2_type_id" integer, "entity_2_uuid" uuid, "entity_2_id" integer, "entity_3_type_id" integer, "entity_3_uuid" uuid, "entity_3_id" integer, "entity_4_type_id" integer, "entity_4_uuid" uuid, "entity_4_id" integer, "creator_uuid" uuid, "creator_id" integer, "creator_type_id" integer, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_note_id" PRIMARY KEY ("note_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."ref_activity" ("activity_id" SERIAL NOT NULL, "activity_name" character varying NOT NULL, "activity_category" integer NOT NULL, "activity_name_l10n" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_ref_activity_id" PRIMARY KEY ("activity_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."staff_activity" ("staff_activity_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "staff_id" uuid, "staff_activity_type_id" integer NOT NULL, "visit_id" character varying, "activity_id" integer NOT NULL, "activity_duration" TIMESTAMP NOT NULL, "entity_type_id" integer NOT NULL, "entity_id" uuid NOT NULL, "care_calendar" uuid NOT NULL, "ticket_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_staff_activity_id" PRIMARY KEY ("staff_activity_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."ticket" ("ticket_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "ticket_status" character varying(256) NOT NULL DEFAULT '1000300001', "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_ticket_id" PRIMARY KEY ("ticket_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."user_device" ("user_device_id" SERIAL NOT NULL, "entity_type_id" integer NOT NULL, "entity_uuid" uuid NOT NULL, "uid" character varying(256), "instance_id" character varying(256), "device_id" character varying(256), "fcm_id" character varying(256), "device_information" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_user_device_id" PRIMARY KEY ("user_device_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."job_tracker" ("job_tracker_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "job_type" character varying(256) NOT NULL, "job_status" character varying NOT NULL DEFAULT 'PENDING', "job_completed_at" TIMESTAMP, "job_information" jsonb, "created_at" TIMESTAMP NOT NULL DEFAULT 'NOW()', "updated_at" TIMESTAMP NOT NULL DEFAULT 'NOW()', CONSTRAINT "PK_job_tracker_id" PRIMARY KEY ("job_tracker_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."translate" ("translate_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "text_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "language" character varying(5) NOT NULL, "text" character varying(2000) NOT NULL, CONSTRAINT "PK_translate_id" PRIMARY KEY ("translate_id"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "main"."job_tracker"`);
        await queryRunner.query(`DROP TABLE "main"."user_device"`);
        await queryRunner.query(`DROP TABLE "main"."ticket"`);
        await queryRunner.query(`DROP TABLE "main"."staff_activity"`);
        await queryRunner.query(`DROP TABLE "main"."ref_activity"`);
        await queryRunner.query(`DROP TABLE "main"."note"`);
        await queryRunner.query(`DROP TABLE "main"."dump_doc"`);
        await queryRunner.query(`DROP TABLE "main"."animal_disease_classification"`);
        await queryRunner.query(`DROP TABLE "main"."animal_disease"`);
        await queryRunner.query(`DROP TABLE "main"."care_calendar_classification"`);
        await queryRunner.query(`DROP TABLE "main"."care_calendar"`);
        await queryRunner.query(`DROP TABLE "main"."animal_classification"`);
        await queryRunner.query(`DROP TABLE "main"."animal"`);
        await queryRunner.query(`DROP TABLE "main"."customer_classification"`);
        await queryRunner.query(`DROP TABLE "main"."customer"`);
        await queryRunner.query(`DROP TABLE "main"."staff_classification"`);
        await queryRunner.query(`DROP TABLE "main"."staff"`);
        await queryRunner.query(`DROP TABLE "main"."entity_geography"`);
        await queryRunner.query(`DROP TABLE "main"."entity_relationship"`);
        await queryRunner.query(`DROP TABLE "main"."document"`);
        await queryRunner.query(`DROP TABLE "main"."ref_village"`);
        await queryRunner.query(`DROP TABLE "main"."ref_taluk"`);
        await queryRunner.query(`DROP TABLE "main"."ref_district"`);
        await queryRunner.query(`DROP TABLE "main"."ref_state"`);
        await queryRunner.query(`DROP TABLE "main"."ref_reference"`);
        await queryRunner.query(`DROP TABLE "main"."ref_reference_category"`);
        await queryRunner.query(`DROP TABLE "main"."translate"`);
    }
}
