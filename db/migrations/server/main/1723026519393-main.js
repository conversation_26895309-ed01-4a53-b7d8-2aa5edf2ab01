const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1723026519393 {
    name = 'Main1723026519393'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."task_question" ("question_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "question_name" character varying(256) NOT NULL, "question_name_l10n" jsonb, "topic_name" character varying(256) NOT NULL, "topic_name_l10n" jsonb, "topic_priority" integer, "topic_question_priority" integer, "priority_grouping" integer, "form_configuration" jsonb, "frequency" character varying(256) NOT NULL, "farmer_input_required" integer, "review_date_config" integer, "active" integer NOT NULL DEFAULT '1000100001', "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_question_uuid" PRIMARY KEY ("question_uuid"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "main"."task_question"`);
    }
}
