const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class ModifyAssetFeatureAddAssetClassificationId1705668720903 {
    name = 'ModifyAssetFeatureAddAssetClassificationId1705668720903'

    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "main"."asset_feature" ADD "asset_classification_id" uuid`);
        await queryRunner.query(`ALTER TABLE "main"."asset_feature" ALTER COLUMN "asset_id" DROP NOT NULL`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "main"."asset_feature" ALTER COLUMN "asset_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "main"."asset_feature" DROP COLUMN "asset_classification_id"`);
    }
}
