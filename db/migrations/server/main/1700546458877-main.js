const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1700546458877 {
    name = 'Main1700546458877'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."transaction_ledger" ("transaction_ledger_uuid" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "account_id" character varying(256), "staff_uuid" uuid NOT NULL, "amount" numeric(14,4) NOT NULL, "transaction_type" integer NOT NULL, "transaction_category" integer NOT NULL, "transaction_date" TIMESTAMP NOT NULL, "transaction_description" jsonb, "entity_uuid" uuid, "entity_type_id" integer NOT NULL, "active" integer NOT NULL DEFAULT '**********', "data_sync_status" integer NOT NULL DEFAULT '**********', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_transaction_ledger_uuid" PRIMARY KEY ("transaction_ledger_uuid"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "main"."transaction_ledger"`);
    }
}
