const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddTablesRelatedToGenericGeography1706411062989 {
    name = 'AddTablesRelatedToGenericGeography1706411062989'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."ref_geography" ("geography_id" SERIAL NOT NULL, "geography_type_id" integer, "geography_census_id" integer, "geography_name_l10n" jsonb, "geography_short_code" character varying(256), "geography_geojson" jsonb, "geography_information" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_geography_id" PRIMARY KEY ("geography_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."ref_geography_classification" ("geography_classification_id" SERIAL NOT NULL, "geography_id" integer NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_geography_classification_id" PRIMARY KEY ("geography_classification_id"))`);
        await queryRunner.query(`CREATE INDEX "idx_geography_classification_geography_id" ON "main"."ref_geography_classification" ("geography_id") `);
        await queryRunner.query(`CREATE INDEX "idx_geography_classification_classifier_id" ON "main"."ref_geography_classification" ("classifier_id") `);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "main"."idx_geography_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_geography_classification_geography_id"`);
        await queryRunner.query(`DROP TABLE "main"."ref_geography_classification"`);
        await queryRunner.query(`DROP TABLE "main"."ref_geography"`);
    }
}
