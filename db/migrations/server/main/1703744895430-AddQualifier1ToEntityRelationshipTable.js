const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddQualifier1ToEntityRelationshipTable1703744895430 {
    name = 'AddQualifier1ToEntityRelationshipTable1703744895430'

    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "main"."entity_relationship" ADD "qualifier_1_type_id" integer`);
        await queryRunner.query(`ALTER TABLE "main"."entity_relationship" ADD "qualifier_1_entity_uuid" uuid`);
        await queryRunner.query(`ALTER TABLE "main"."entity_relationship" ADD "qualifier_1_entity_id" integer`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "main"."entity_relationship" DROP COLUMN "qualifier_1_entity_id"`);
        await queryRunner.query(`ALTER TABLE "main"."entity_relationship" DROP COLUMN "qualifier_1_entity_uuid"`);
        await queryRunner.query(`ALTER TABLE "main"."entity_relationship" DROP COLUMN "qualifier_1_type_id"`);
    }
}
