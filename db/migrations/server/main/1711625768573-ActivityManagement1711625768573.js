const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class ActivityManagement1711625768573 {
    name = 'ActivityManagement1711625768573'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."ref_activity_classification" ("activity_classification_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "activity_id" integer NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_activity_classification_id" PRIMARY KEY ("activity_classification_id"))`);
        await queryRunner.query(`CREATE INDEX "idx_ref_activity_classification_classifier_id" ON "main"."ref_activity_classification" ("classifier_id") `);
        await queryRunner.query(`CREATE INDEX "idx_ref_activity_classification_activity_id" ON "main"."ref_activity_classification" ("activity_id") `);
        await queryRunner.query(`ALTER TABLE "main"."ref_activity" ADD "activity_information" jsonb`);
        await queryRunner.query(`CREATE SEQUENCE IF NOT EXISTS "main"."ref_activity_activity_id_seq" OWNED BY "main"."ref_activity"."activity_id"`);
        await queryRunner.query(`ALTER SEQUENCE main.ref_activity_activity_id_seq START 1000800000`);
        await queryRunner.query(`ALTER SEQUENCE main.ref_activity_activity_id_seq RESTART WITH 1000800000`);
        await queryRunner.query(`ALTER TABLE "main"."ref_activity" ALTER COLUMN "activity_id" SET DEFAULT nextval('"main"."ref_activity_activity_id_seq"')`);
        await queryRunner.query(`ALTER TABLE "main"."ref_activity" ADD CONSTRAINT "UQ_REF_ACTIVITY_ACTIVITY_NAME" UNIQUE ("activity_name", "activity_category")`);
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "main"."ref_activity" DROP COLUMN "activity_information"`);
        await queryRunner.query(`DROP INDEX "main"."idx_ref_activity_classification_activity_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_ref_activity_classification_classifier_id"`);
        await queryRunner.query(`DROP TABLE "main"."ref_activity_classification"`);
    }
}
