const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1702377135733 {
    name = 'Main1702377135733'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."partner" ("partner_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "partner_type_id" integer, "partner_visual_id" character varying(256), "partner_name_l10n" jsonb, "mobile_number" character varying(256), "email_address" character varying(256), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_partner_id" PRIMARY KEY ("partner_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."partner_classification" ("partner_classification_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "partner_id" uuid NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_partner_classification_id" PRIMARY KEY ("partner_classification_id"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "main"."partner_classification"`);
        await queryRunner.query(`DROP TABLE "main"."partner"`);
    }
}
