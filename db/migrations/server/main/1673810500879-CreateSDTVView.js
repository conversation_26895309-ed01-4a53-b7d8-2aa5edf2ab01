const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class CreateSDTVView1673810500879 {

  async up(queryRunner) {
    await queryRunner.query(
      `
        CREATE OR REPLACE VIEW "main"."ref_sdtv_view" AS
          select "rv"."village_id" as village_id, "rt"."taluk_id" as taluk_id,
            "rd"."district_id" as district_id, "rs"."state_id" as state_id,
            "rv"."village_name_l10n", "rv"."village_short_code", "rt"."taluk_name_l10n", "rt"."taluk_short_code",
            "rd"."district_name_l10n", "rd"."district_short_code", "rs"."state_name_l10n", "rs"."state_short_code"
          from "main"."ref_village" "rv", "main"."ref_taluk" "rt", "main"."ref_district" "rd", "main"."ref_state" "rs"
          where "rv"."taluk_id" = "rt"."taluk_id" and "rt"."district_id" = "rd"."district_id" 
          and "rd"."state_id" = "rs"."state_id"
      `
    )
  }

  async down(queryRunner) {
    await queryRunner.query(
      `
        DROP VIEW ref_sdtv_view
      `
    )
  }

}
