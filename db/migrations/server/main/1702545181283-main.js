const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Main1702545181283 {
    name = 'Main1702545181283'

    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "main"."transaction_ledger" ADD "entity_name" character varying(256)`);
        await queryRunner.query(`ALTER TABLE "main"."transaction_ledger" ADD "entity_service" character varying(256)`);
        await queryRunner.query(`ALTER TABLE "main"."transaction_ledger" ADD "transaction_status" integer`);
       
    }

    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "main"."transaction_ledger" DROP COLUMN "transaction_status"`);
        await queryRunner.query(`ALTER TABLE "main"."transaction_ledger" DROP COLUMN "entity_service"`);
        await queryRunner.query(`ALTER TABLE "main"."transaction_ledger" DROP COLUMN "entity_name"`);
    }
}
