const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddCropMasterRelatedTables1703069033123 {
    name = 'AddCropMasterRelatedTables1703069033123'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."ref_crop" ("crop_id" SERIAL NOT NULL, "crop_name" character varying(256), "crop_name_l10n" jsonb, "crop_information" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_crop_id" PRIMARY KEY ("crop_id"))`);
        await queryRunner.query(`CREATE TABLE "main"."ref_crop_classification" ("crop_classification_id" SERIAL NOT NULL, "crop_id" integer NOT NULL, "classifier_id" integer NOT NULL, "value_int" integer, "value_reference_id" integer, "value_reference_uuid" uuid, "value_string_256" character varying(256), "value_string_2000" character varying(2000), "value_double" numeric(14,4), "value_date" TIMESTAMP, "value_json" jsonb, "value_l10n" jsonb, "value_string_256_encrypted" character varying(256), "value_string_2000_encrypted" character varying(2000), "active" integer NOT NULL DEFAULT '1000100001', "last_modifying_user_id" uuid, "correlation_id" character varying(256), "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_crop_classification_id" PRIMARY KEY ("crop_classification_id"))`);
        await queryRunner.query(`CREATE INDEX "idx_crop_classification_crop_id" ON "main"."ref_crop_classification" ("crop_id") `);
        await queryRunner.query(`CREATE INDEX "idx_crop_classification_classifier_id" ON "main"."ref_crop_classification" ("classifier_id") `);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "main"."idx_crop_classification_classifier_id"`);
        await queryRunner.query(`DROP INDEX "main"."idx_crop_classification_crop_id"`);
        await queryRunner.query(`DROP TABLE "main"."ref_crop_classification"`);
        await queryRunner.query(`DROP TABLE "main"."ref_crop"`);
    }
}
