const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class AddLocationAnalyticsRelatedTables1679834151719 {
    name = 'AddLocationAnalyticsRelatedTables1679834151719'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "main"."user_event" ("user_event_id" uuid NOT NULL DEFAULT common.uuid_generate_v1mc(), "user_device_id" integer NOT NULL, "location_tracking" integer, "lat" numeric(14,10), "lng" numeric(14,10), "time_at" TIMESTAMP NOT NULL DEFAULT NOW(), "additional_data" jsonb, "processed" integer DEFAULT '0', "data_sync_status" integer NOT NULL DEFAULT '1000101001', "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_user_event_id" PRIMARY KEY ("user_event_id"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "main"."user_event"`);
    }
}
