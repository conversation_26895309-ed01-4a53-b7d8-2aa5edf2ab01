const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class CreateTriggerFunction1671598643160 {

    async up(queryRunner) {
        await queryRunner.query(`CREATE OR REPLACE FUNCTION "common".set_updated_at_column_to_now()
        RETURNS TRIGGER AS $$
        BEGIN
           IF NEW IS DISTINCT FROM OLD THEN
              IF NEW.updated_at = OLD.updated_at THEN
                NEW.updated_at = now(); 
              ELSE
              END IF;
              RETURN NEW;
           ELSE
              RETURN NULL;
           END IF;
        END;
        $$ language 'plpgsql';`)
    }

    async down(queryRunner) {
        await queryRunner.query(`drop function if exists "common"."set_updated_at_column_to_now"`)
    }

}
