const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class Indices1701259504022 {
    name = 'Indices1701259504022'

    async up(queryRunner) {
        await queryRunner.query(`CREATE INDEX "idx_raw_location_user_device_id" ON "location"."raw_location" ("user_device_id") `);
        await queryRunner.query(`CREATE INDEX "idx_raw_location_time_at" ON "location"."raw_location" ("time_at") `);
        await queryRunner.query(`CREATE INDEX "idx_raw_location_processed" ON "location"."raw_location" ("processed") `);
        await queryRunner.query(`CREATE INDEX "idx_stay_point_user_device_id" ON "location"."stay_point" ("user_device_id") `);
        await queryRunner.query(`CREATE INDEX "idx_stay_point_arrived_at" ON "location"."stay_point" ("arrived_at") `);
        await queryRunner.query(`CREATE INDEX "idx_stay_point_left_at" ON "location"."stay_point" ("left_at") `);
        await queryRunner.query(`CREATE INDEX "idx_stay_point_processed" ON "location"."stay_point" ("processed") `);
        await queryRunner.query(`CREATE INDEX "idx_travel_route_user_device_id" ON "location"."travel_route" ("user_device_id") `);
        await queryRunner.query(`CREATE INDEX "idx_travel_route_start_at" ON "location"."travel_route" ("start_at") `);
        await queryRunner.query(`CREATE INDEX "idx_travel_route_end_at" ON "location"."travel_route" ("end_at") `);
        await queryRunner.query(`CREATE INDEX "idx_travel_route_processed" ON "location"."travel_route" ("processed") `);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX "location"."idx_travel_route_processed"`);
        await queryRunner.query(`DROP INDEX "location"."idx_travel_route_end_at"`);
        await queryRunner.query(`DROP INDEX "location"."idx_travel_route_start_at"`);
        await queryRunner.query(`DROP INDEX "location"."idx_travel_route_user_device_id"`);
        await queryRunner.query(`DROP INDEX "location"."idx_stay_point_processed"`);
        await queryRunner.query(`DROP INDEX "location"."idx_stay_point_left_at"`);
        await queryRunner.query(`DROP INDEX "location"."idx_stay_point_arrived_at"`);
        await queryRunner.query(`DROP INDEX "location"."idx_stay_point_user_device_id"`);
        await queryRunner.query(`DROP INDEX "location"."idx_raw_location_processed"`);
        await queryRunner.query(`DROP INDEX "location"."idx_raw_location_time_at"`);
        await queryRunner.query(`DROP INDEX "location"."idx_raw_location_user_device_id"`);
    }
}
