const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class InitialDesign1683031131237 {
    name = 'InitialDesign1683031131237'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "location"."raw_location" ("raw_location_id" SERIAL NOT NULL, "accuracy" numeric(14,10), "altitude" numeric(14,10), "bearing" numeric(14,10), "user_device_id" integer, "location_on_device_id" integer, "is_from_mock_provider" boolean, "lat" numeric(14,10) NOT NULL DEFAULT '-90', "lng" numeric(14,10) NOT NULL DEFAULT '-90', "location_provider" integer, "mock_locations_enabled" boolean, "provider" character varying(20), "speed" numeric(14,6), "time_at" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), "processed" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_raw_location_id" PRIMARY KEY ("raw_location_id"))`);
        await queryRunner.query(`CREATE TABLE "location"."stay_point" ("stay_point_id" SERIAL NOT NULL, "user_device_id" integer, "lat" numeric(14,10) NOT NULL DEFAULT '-90', "lng" numeric(14,10) NOT NULL DEFAULT '-90', "arrived_at" TIMESTAMP, "left_at" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), "processed" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_stay_point_id" PRIMARY KEY ("stay_point_id"))`);
        await queryRunner.query(`CREATE TABLE "location"."travel_route" ("travel_route_id" SERIAL NOT NULL, "user_device_id" integer, "start_lat" numeric(14,10) NOT NULL DEFAULT '-90', "start_lng" numeric(14,10) NOT NULL DEFAULT '-90', "end_lat" numeric(14,10) NOT NULL DEFAULT '-90', "end_lng" numeric(14,10) NOT NULL DEFAULT '-90', "start_at" TIMESTAMP, "end_at" TIMESTAMP, "distance" numeric(14,6), "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), "processed" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_travel_route_id" PRIMARY KEY ("travel_route_id"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "location"."travel_route"`);
        await queryRunner.query(`DROP TABLE "location"."stay_point"`);
        await queryRunner.query(`DROP TABLE "location"."raw_location"`);
    }
}
