const { MigrationInterface, QueryRunner } = require("typeorm");

module.exports = class InitialDesign1672033942041 {
    name = 'InitialDesign1672033942041'

    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "config"."synchronization_data" ("synchronization_data_id" SERIAL NOT NULL, "user_device_id" integer NOT NULL, "table_name" character varying(256) NOT NULL, "last_sync_time" TIMESTAMP, "active" integer NOT NULL DEFAULT '1000100001', "correlation_id" character varying(256), "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_synchronization_data_id" PRIMARY KEY ("synchronization_data_id"))`);
        await queryRunner.query(`CREATE TABLE "config"."synchronization_conflict" ("synchronization_conflict_id" SERIAL NOT NULL, "user_device_id" integer NOT NULL, "table_name" character varying(256), "table_primary_key_uuid" uuid, "table_primary_key_id" integer, "old_non_encrypted_column_data" jsonb, "new_non_encrypted_column_data" jsonb, "old_encrypted_column_data" jsonb, "new_encrypted_column_data" jsonb, "active" integer NOT NULL DEFAULT '1000100001', "correlation_id" character varying(256), "created_at" TIMESTAMP NOT NULL DEFAULT NOW(), "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_synchronization_conflict_id" PRIMARY KEY ("synchronization_conflict_id"))`);
    }

    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "config"."synchronization_conflict"`);
        await queryRunner.query(`DROP TABLE "config"."synchronization_data"`);
    }
}
