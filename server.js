const express = require("express");
const path = require("path");

const {
  loadConfiguration,
} = require('@krushal-it/common-core')

if (process.env && process.env.REACT_APP_ENV_JSON) {
  console.log('process.env.ENV_JSON = ', process.env.REACT_APP_ENV_JSON)
  loadConfiguration(process.env.REACT_APP_ENV_JSON)
}

const app = express();
app.use(express.static(path.join(__dirname, "build")));
app.get("/*", async (req, res) => {
  return res.sendFile(path.join(__dirname, "build", "index.html"));
});

app.listen(3003, () => {
  console.log("mobile-app running at", 3003);
});
