from fastapi import <PERSON><PERSON><PERSON>
# from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.schedulers.asyncio import AsyncIOScheduler

from location_analytics import computeSatyPointsForUsers, computeTravelDistances
from datetime import datetime
import sys
import os
from dotenv import load_dotenv
import json

stayPointTaskTimeMinutes = 20

try:
    ENV_JSON = os.getenv('ENV_JSON')
    env = json.loads(str(ENV_JSON))
    stayPointTaskTimeMinutes = env['LOCATION_ANALYTICS']['STAY_POINT_TASK_TIME_MINUTES']
    print('env loaded stayPointTaskTimeMinutes', stayPointTaskTimeMinutes)
except Exception as e:
    print('Error loading .env', e, 'expecting ENV_JSON to be set in env')
    stayPointTaskTimeMinutes = 20
    print('ENV_JSON not set, using default values for stayPointDistThreshold and stayPointTimeThreshold',
          stayPointTaskTimeMinutes)
sys.stdout.flush()

app = FastAPI()
scheduler = AsyncIOScheduler()
# scheduler.start()


@app.on_event("startup")
async def startup_event():
    print("startup event called")
    scheduler.start()


@app.on_event("shutdown")
async def shutdown_event():
    print("shutdown event called")
    scheduler.shutdown()

numLocationAnalyticsTasks = 0


async def locationAnalyticsTasks():
    global numLocationAnalyticsTasks
    print("locationAnalyticsTasks started!", numLocationAnalyticsTasks,
          datetime.now().strftime("%H:%M:%S"))
    sys.stdout.flush()
    await computeSatyPointsForUsers(user_device_id=-1, start='1970-01-01', end='')
    print('computeSatyPointsForUsers done', numLocationAnalyticsTasks,
          datetime.now().strftime("%H:%M:%S"))
    sys.stdout.flush()
    await computeTravelDistances(user_device_id=-1)
    print('computeTravelDistances done', numLocationAnalyticsTasks,
          datetime.now().strftime("%H:%M:%S"))
    sys.stdout.flush()
    numLocationAnalyticsTasks += 1


# scheduler.add_job(locationAnalyticsTasks)
# Schedule locationAnalyticsTasks to run every 60 minuts
scheduler.add_job(locationAnalyticsTasks, "interval", minutes=stayPointTaskTimeMinutes)


@ app.get("/")
async def home() -> str:
    print("dffdfd")
    return "Location Analytics running"


@ app.get('/api/locationAnalytics/computeSatyPointsForUsers')
async def computeStayPointsForUserRoute(user_device_id: int = -1, start: str = '1970-01-01', end: str = ''):
    print("computeStayPointsForUserRoute", user_device_id, start, end)
    sys.stdout.flush()
    return await computeSatyPointsForUsers(user_device_id, start, end)


@ app.get('/api/locationAnalytics/computeTravelDistances')
async def computeTravelDistancesRoute(user_device_id: int = -1):
    print("computeTravelDistancesRoute", user_device_id)
    sys.stdout.flush()
    print(user_device_id)
    return await computeTravelDistances(user_device_id)
