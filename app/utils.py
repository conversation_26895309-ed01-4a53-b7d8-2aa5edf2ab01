from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine, AsyncConnection
import json
import os
from dotenv import load_dotenv
from typing import cast
try:
    load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env'))
except Exception as e:
    print('Error loading .env', e, 'expecting ENV_JSON to be set in env')
IS_SERVER = os.getenv('IS_SERVER')
DB_TYPE = os.getenv('DB_TYPE')
ENV_JSON = os.getenv('ENV_JSON')
print('ENVS loaded', IS_SERVER, DB_TYPE)


async def dbConnect(db) -> tuple[AsyncEngine, AsyncConnection]:
    dbVars = None
    conn = cast(AsyncConnection, None)
    engine = cast(AsyncEngine, None)
    if db == 'main':
        ''' ssDict = json.loads(ss.ENV_JSON)'''
        dbVars = json.loads(str(ENV_JSON))['DB_VARIABLES']['POSTGRES_MAIN']
     #   dbVars = ssDict['DB_VARIABLES']['POSTGRES_MAIN']
        dbVars['schema'] = 'main'
     #   dbVars['HOST']='localhost'
    elif db == 'location':
        dbVars = json.loads(str(ENV_JSON))['DB_VARIABLES']['POSTGRES_LOCATION']
        dbVars['schema'] = 'location'
    elif db == 'common':
        dbVars = json.loads(str(ENV_JSON))['DB_VARIABLES']['POSTGRES_COMMON']
        dbVars['schema'] = 'common'
    elif db == 'config':
        dbVars = json.loads(str(ENV_JSON))['DB_VARIABLES']['POSTGRES_CONFIG']
        dbVars['schema'] = 'config'
    if dbVars != None:
        connectString = f'''postgresql+asyncpg://{dbVars['USER_NAME']}:{dbVars['PASSWORD']}@{dbVars['HOST']}:{dbVars['PORT']}/{dbVars['DATABASE']}'''
        print(connectString)
        engine = create_async_engine(connectString)
        engine.execution_options(schema_translate_map={None: {dbVars['schema']}})
        conn = await engine.connect()
        return (engine, conn)
    return (engine, conn)


async def closeConnection(engine, conn) -> None:
    conn.close()
    return
