# This file contains 1 function per route of main.py.
# It is done this way to run these functions seperately without http server
from datetime import datetime, timedelta, date
from asyncio import get_event_loop
from time import clock_getres
from sqlalchemy import text
from math import radians, cos, sin, asin, sqrt
from functools import reduce
from utils import dbConnect
import traceback
import sys
import os
from dotenv import load_dotenv
import json
stayPointDistThreshold = 100.0
stayPointTimeThreshold = 2 * 60

try:
    ENV_JSON = os.getenv('ENV_JSON')
    env = json.loads(str(ENV_JSON))
    stayPointDistThreshold = env['LOCATION_ANALYTICS']['STAY_POINT_DIST_THRESHOLD']
    stayPointTimeThreshold = env['LOCATION_ANALYTICS']['STAY_POINT_TIME_THRESHOLD']
    print('ENVS loaded', stayPointDistThreshold, stayPointTimeThreshold)
except Exception as e:
    print('Error loading .env', e, 'expecting ENV_JSON to be set in env')
    stayPointDistThreshold = 100.0
    stayPointTimeThreshold = 2 * 60
    print('ENV_JSON not set, using default values for stayPointDistThreshold and stayPointTimeThreshold',
          stayPointDistThreshold, stayPointTimeThreshold)

sys.stdout.flush()


def getDistance(lat1: float, lng1: float, lat2: float , lng2: float) -> float :
    lat1, lng1, lat2, lng2 = map(radians, [lat1, lng1, lat2, lng2])
    dlng = lng2 - lng1
    dlat = lat2 - lat1
    a = sin(dlat / 2)**2 + cos(lat1) * cos(lat2) * sin(dlng / 2)**2
    c = 2 * asin(sqrt(a))
    m = 6371000.0 * c
    return m


def computMeanCoord(gpsLocations):
    lat, _ = reduce(lambda x, y: (x[0] + y[0], None), gpsLocations)
    _, lng = reduce(lambda x, y: (None, x[1] + y[1]), gpsLocations)
    return (lat / len(gpsLocations), lng / len(gpsLocations))


async def executeStmt(engine, conn, stmt) -> list:
    results = await conn.execute(text(stmt))
    results = results.fetchall()
    results = [r for r in results]
   # results = [d[0] for d in results]
    return results


async def getDeviceIds(engine, conn, user_device_id: int = -1) -> list:
    user_device_ids = [user_device_id]
    if user_device_id == -1 :
        # get all the device ids from table
        stmt = f''' select distinct(user_device_id) from location.raw_location '''
        user_device_ids = await executeStmt(engine, conn, stmt)
        user_device_ids = [d[0] for d in user_device_ids]
    return user_device_ids


def getStayPoints(user_device_id: int, locations: list) -> list :
    stayPointList = []
    numLocations = len(locations)
    i = 0
    while i < numLocations - 1:
        j = i + 1
        while j < numLocations:
            dist = getDistance(locations[i][0], locations[i]
                               [1], locations[j][0], locations[j][1])
            if dist > stayPointDistThreshold:
                timeInSec = (locations[j][2] - locations[i][2]).total_seconds()
                # distance is greater time is greater, you left a stay point.
                if timeInSec > stayPointTimeThreshold:
                    meanCodrd = computMeanCoord(locations[i:j + 1])
                    stayPointList.append({'user_device_id': user_device_id, 'lat': meanCodrd[0], 'lng': meanCodrd[1], 'arrived_at': locations[i][2],
                                          'left_at': locations[j][2]})
                i = j
                break
            j += 1  # i also need to change
        # Algorithm in [1] lacks following line
        i = i + 1
    return stayPointList


async def computeSatyPointsForUsers(user_device_id: int = -1, start: str = '1970-01-01', end: str = '') -> dict:
    print("computeSatyPointsForUsers", user_device_id, start, end)
    sys.stdout.flush()
    returnResult = {}
    engine, conn = await dbConnect('location')
    try:
        user_device_ids = await getDeviceIds(engine, conn, user_device_id)
        if len(user_device_ids) == 0 :
            print('No user_device_ids to be processed')
            return returnResult
        # get the times also now so that we can iterate over the days for all users.
        user_device_id_str = ','.join([str(d) for d in user_device_ids])
        startd = datetime(
            1970, 1, 1) if start == '' else datetime.fromisoformat(start)
        endd = datetime.now() if end == '' else datetime.fromisoformat(end)
        starti = startd.strftime('%Y-%m-%d %H:%M:%S')
        endi = endd.strftime('%Y-%m-%d %H:%M:%S')
        stmt = f''' select min(time_at) as st, max(time_at) as ed from location.raw_location where
                    user_device_id in ( {user_device_id_str} )
                    and processed = 0
                    and time_at > timestamp '{starti}'
                    and time_at < timestamp '{endi}'
                    limit 1
                '''
        dates = await executeStmt(engine, conn, stmt)
        start_date = dates[0][0].date()
        days: int = (dates[0][1].date() - start_date).days + 1
        print('results = ', dates)
    except Exception as e:
        print('exception in DB while getting user_device_ids, dates', e)
        traceback.print_exc()
        await conn.close()
        sys.stdout.flush()
        sys.stderr.flush()
        return returnResult
    if (len(user_device_ids) == 0 or days == 0):
        print('No user_device_ids to be processed')
        return returnResult

    for user_device_id in user_device_ids:
        returnResult[user_device_id] = 0
        # startd = datetime(
        #     1970, 1, 1) if start == '' else datetime.fromisoformat(start)
        # endd = datetime.now() if end == '' else datetime.fromisoformat(end)
        # starti = startd.strftime('%Y-%m-%d %H:%M:%S')
        # endi = endd.strftime('%Y-%m-%d %H:%M:%S')
        # stmt = f''' select min(time_at) as st, max(time_at) as ed from location.raw_location where
        #             user_device_id = {user_device_id}
        #             and processed = 0
        #             and time_at > timestamp '{starti}'
        #             and time_at < timestamp '{endi}'
        #             limit 1
        #         '''
        # dates = await executeStmt(engine, conn, stmt)
        # start_date = dates[0][0].date()
        # days: int = (dates[0][1].date() - start_date).days + 1
        # print('results = ', dates)
        for day in [start_date + timedelta(days=n) for n in range(days)]:
            try:
                starti = datetime.combine(day, datetime.min.time())
                endi = starti + timedelta(days=1)
                stmt = f''' select lat, lng, time_at from location.raw_location where
                            user_device_id = {user_device_id}
                            and processed = 0
                            and time_at >= timestamp '{starti}'
                            and time_at < timestamp '{endi}'
                            order by time_at ASC
                        '''
                results = await executeStmt(engine, conn, stmt)
                if len(results) == 0 :
                    print('No raw locations to be processed for user_device_id on ',
                          user_device_id, day)
                    continue
                stPoints = getStayPoints(user_device_id, results)
                print('total stay points', len(stPoints),
                      ' total points', len(results))
                if len(stPoints) == 0 :
                    # if no stay points are generated dont mark location as processed as there might be some raw locations coming later that will make it a stay point
                    print(
                        'No stay points generated, skipping for user_device_id on day', user_device_id, day)
                    continue
                stmt = f''' insert into location.stay_point ( user_device_id,lat, lng, arrived_at, left_at )
                        values ( :user_device_id,:lat, :lng, :arrived_at, :left_at )'''
                result = await conn.execute(text(stmt), stPoints)
                print('insert result for user_device_id', result,
                      user_device_id, result)
                sys.stdout.flush()
                returnResult[user_device_id] += len(stPoints)
            # now update flag to processed
                stmt = f''' update location.raw_location set processed = 1 where
                            user_device_id = {user_device_id}
                            and time_at > timestamp '{starti}'
                            and time_at < '{endi}'
                        '''
                result = await conn.execute(text(stmt))
                await conn.commit()
                print('update result', result)
                sys.stdout.flush()
            except Exception as e:
                # exception for this user_device id
                print('exception while computing stay point for this user_device id',
                      e, user_device_id)
                traceback.print_exc()
                sys.stdout.flush()
    await conn.close()
    sys.stdout.flush()
    return returnResult


async def computeTravelDistances(user_device_id: int = -1) -> dict:
    # get the start and end time of stay points
    # for each day of the stay points loop through
    # get all the stay points of the day
    # get all the user events of the day
    # insert the user events if the event is start add between the start times if the event is end add between the end times
    # compute distance between the stay points  exclude between user events
    print("computeTravelDistances", user_device_id)
    returnResult = {}
    engine, conn = await dbConnect('location')
    # first get on of userEvents by day
    try:
        user_device_ids = await getDeviceIds(engine, conn, user_device_id)
        if len(user_device_ids) == 0 :
            print('No user_device_ids to be processed')
            return returnResult
        # get the times also now so that we can iterate over the days for all users.
        user_device_id_str = ','.join([str(d) for d in user_device_ids])
        stmt = f''' select min(time_at) as st, max(time_at) as ed from location.raw_location where
                    user_device_id in ( {user_device_id_str} )
                    and processed = 0
                    limit 1
                '''
        dates = await executeStmt(engine, conn, stmt)
        start_date = dates[0][0].date()
        days: int = (dates[0][1].date() - start_date).days + 1
        print('results = ', dates)
        if (len(user_device_ids) == 0 or days == 0):
            print('No user_device_ids to be processed')
            return returnResult
    except Exception as e:
        print('exception in DB while getting user_device_ids, dates', e)
        traceback.print_exc()
        await conn.close()
        sys.stdout.flush()
        sys.stderr.flush()
        return returnResult

    for user_device_id in user_device_ids:
        returnResult[user_device_id] = 0
        for day in [start_date + timedelta(days=n) for n in range(days)]:
            try:
                starti = datetime.combine(day, datetime.min.time())
                endi = starti + timedelta(days=1)
                stmt = f''' select stay_point_id, lat, lng, arrived_at, left_at from location.stay_point where
                            user_device_id = {user_device_id}
                            and arrived_at >= timestamp '{starti}'
                            and left_at < timestamp '{endi}'
                            and processed = 0
                            order by arrived_at ASC
                        '''
                stay_points = await executeStmt(engine, conn, stmt)
                print('results = ', len(stay_points))
                if len(stay_points) == 0:
                    print('No stay points to be processed')
                    continue
                # the user events for the day
                stmt = f''' select user_event_id, location_tracking, lat, lng, time_at from main.user_event where
                            user_device_id = {user_device_id}
                            and time_at >= timestamp '{starti}'
                            and time_at < timestamp '{endi}'
                            order by time_at ASC
                        '''
                user_events = await executeStmt(engine, conn, stmt)
                # now insert the user events in stay points list
                for user_event in user_events:
                    if user_event[1] == 1:  # start event
                        # find first stay point after this event with arrived_at > time_at with index
                        start_loc = next((i for i, x in enumerate(
                            stay_points) if x[3] > user_event[4]), 0)
                        # its possible that there is no stay point before this event hence add in the begining
                        #     start_loc = start_loc if start_loc else 0
                        # insert the user_event in stay_points at the index
                        stay_points.insert(
                            start_loc, (-1, user_event[2], user_event[3], user_event[4], user_event[4]))
                    elif user_event[1] == 0:  # end event
                        end_loc = next((i for i, x in enumerate(stay_points)
                                        if x[4] > user_event[4]), len(stay_points))
                        # insert the user_event in stay_points at after the index
                        stay_points.insert(
                            end_loc, (-1, user_event[2], user_event[3], user_event[4], user_event[4]))
                if len(stay_points) == 0 or len(stay_points) == 1:
                    print('No raw locations to be processed as only 1 or zero stay points',
                          stay_points, user_events)
                    continue
                # Now calculate distances
                for i in range(len(stay_points) - 1):
                    if stay_points[i][0] == -1 and stay_points[i + 1][0] == -1:
                        continue  # skip consecutive user events
                    startl = stay_points[i][4]
                    endl = stay_points[i + 1][3]

                    stmt = f''' select lat, lng, time_at from location.raw_location where
                                user_device_id = {user_device_id}
                                and time_at >= timestamp '{startl}'
                                and time_at <= timestamp '{endl}'
                                order by time_at ASC
                            '''
                    raw_locations = await executeStmt(engine, conn, stmt)
                    print('results = ', len(raw_locations))
                    distance = 0
                    for ix in range(len(raw_locations) - 1):
                        distance += getDistance(raw_locations[ix][0], raw_locations[ix][1],
                                                raw_locations[ix + 1][0], raw_locations[ix + 1][1])
                    if distance == 0:
                        continue
                    route = {'user_device_id': user_device_id, 'start_lat': stay_points[i][1], 'start_lng': stay_points[i][2],
                             'end_lat': stay_points[i + 1][1], 'end_lng': stay_points[i + 1][2],
                             'start_at': startl, 'end_at': endl, 'distance': distance}
                    # save the distance with upsert
                    stmt = ''' insert into location.travel_route ( user_device_id,start_lat, start_lng, end_lat, end_lng, start_at, end_at, distance ) 
                                                    values (:user_device_id,:start_lat, :start_lng, :end_lat, :end_lng, :start_at, :end_at, :distance )'''
                    result = await conn.execute(text(stmt), [route])
                    print('insert result', result)
                    stmt = f''' update location.stay_point set processed = 1 where
                                user_device_id = {user_device_id} 
                                and stay_point_id = {stay_points[i][0]}
                            '''
                    result = await conn.execute(text(stmt))
                    returnResult[user_device_id] += 1
                # now set processed for user_event as well
                # stmt = f''' update main.user_event set processed = 1 where
                #             user_device_id = {user_device_id}
                #             and user_event_id in ({fullUserEvent["start_user_event_id"]}, {fullUserEvent["end_user_event_id"]})
                #         '''
                # result = await conn.execute(text(stmt))
                await conn.commit()
            except Exception as e:
                traceback.print_exc()
                print('exception in DB for user_device_id for date', e, user_device_id, day)
                sys.stdout.flush()
    await conn.close()
    sys.stdout.flush()
    return returnResult


async def main():
    #    await computeSatyPointsForUsers('001',None,None)
    await computeTravelDistances(1)
    return
if __name__ == '__main__' :
    loop = get_event_loop()
    loop.run_until_complete(main())


# async def getUserEvents(engine, conn, user_device_id: int, start_time: datetime, end_time: datetime) -> list:
#     try:
#         start_time = start_time if start_time else datetime(
#             1970, 1, 1)
#         end_time = end_time if end_time else datetime.now()
#         stmt = f''' select user_event_id, location_tracking, lat, lng, time_at from main.user_event where
#                     user_device_id = {user_device_id}
#                     and time_at >= timestamp '{start_time}'
#                     and time_at <= timestamp '{end_time}'
#                     and processed = 0
#                     order by time_at ASC
#                 '''
#         userEvents = await executeStmt(engine, conn, stmt)
#         # now convert them to start and stop  start_time, start_lat, start_lng, end_time, end_lat, end_lng
#         fullUserEvents = []
#         for userEvent in userEvents:
#             if userEvent[1] == 1 :  # Idealy this should be new one
#                 fullUserEvents.append(
#                     {'start_user_event_id': userEvent[0], 'start_time': userEvent[4], 'start_lat': userEvent[2], 'start_lng': userEvent[3]})
#             elif userEvent[1] == 0:
#                 if len(fullUserEvents) > 0:  # Idealy this should be end of previous one
#                     fullUserEvents[-1]['end_time'] = userEvent[4]
#                     fullUserEvents[-1]['end_lat'] = userEvent[2]
#                     fullUserEvents[-1]['end_lng'] = userEvent[3]
#                     fullUserEvents[-1]['end_user_event_id'] = userEvent[0]
#                 else:
#                     print('No start event for end event for now ignoring')
#             else:
#                 print('Unknown user event type', userEvent[1])
#         return fullUserEvents
#     except Exception as e:
#         print('exception in DB', e)
#     return []


# async def computeTravelDistances(user_device_id: int = -1):
#     # get points between staypoint times.
#     # add the point distances
#     # save format  startSt, end St, distance, startTime, EndTime
#     print("computeTravelDistances", user_device_id)
#     engine, conn = await dbConnect('location')
#     # first get on of userEvents by day

#     try:
#         user_device_ids = await getDeviceIds(engine, conn, user_device_id)
#         for (user_device_id,) in user_device_ids:
#             stmt = f''' select min(time_at) as st, max(time_at) as ed from main.user_event where
#                         user_device_id = {user_device_id}
#                     '''
#             dates = await executeStmt(engine, conn, stmt)
#             fullUserEvents = await getUserEvents(engine, conn, user_device_id, dates[0][0], dates[0][1])
#             # Per line of fullUserEvents, calculate distance
#           #  print('fullUserEvents', fullUserEvents)
#             for ix, fullUserEvent in enumerate(fullUserEvents):
#                 start_time = fullUserEvent["start_time"] if "start_time" in fullUserEvent else datetime(
#                     1970, 1, 1)
#                 end_time = fullUserEvent["end_time"] if "end_time" in fullUserEvent else datetime.now(
#                 )
#                 stmt = f''' select stay_point_id, lat, lng, arrived_at, left_at from location.stay_point where
#                             user_device_id = {user_device_id}
#                             and arrived_at >= timestamp '{start_time}'
#                             and left_at < timestamp '{end_time}'
#                             and processed = 0
#                             order by arrived_at ASC
#                         '''
#                 stay_points = await executeStmt(engine, conn, stmt)
#                 print('results = ', len(stay_points))
#                 if len(stay_points) == 0 :
#                     print('No raw locations to be processed')
#                     continue
#                 # Now calculate distances
#                 # Insert the user_event in stay_points start and end
#                 if ("start_time" in fullUserEvent) :
#                     stay_points.insert(
#                         0, (-1, fullUserEvent["start_lat"], fullUserEvent["start_lng"], fullUserEvent["start_time"], fullUserEvent["start_time"]))
#                 if ("end_time" in fullUserEvent) :
#                     stay_points.append(
#                         (-1, fullUserEvent["end_lat"], fullUserEvent["end_lng"], fullUserEvent["end_time"], fullUserEvent["end_time"]))

#                 for i in range(len(stay_points) - 1):
#                     startl = stay_points[i][4]
#                     endl = stay_points[i + 1][3]
#                     stmt = f''' select lat, lng, time_at from location.raw_location where
#                                 user_device_id = {user_device_id}
#                                 and time_at >= timestamp '{startl}'
#                                 and time_at <= timestamp '{endl}'
#                                 order by time_at ASC
#                             '''
#                     raw_locations = await executeStmt(engine, conn, stmt)
#                     print('results = ', len(raw_locations))
#                     distance = 0
#                     for ix in range(len(raw_locations) - 1):
#                         distance += getDistance(raw_locations[ix][0], raw_locations[ix][1],
#                                                 raw_locations[ix + 1][0], raw_locations[ix + 1][1])
#                     if distance == 0:
#                         continue
#                     route = {'user_device_id': user_device_id, 'start_lat': stay_points[i][1], 'start_lng': stay_points[i][2],
#                              'end_lat': stay_points[i + 1][1], 'end_lng': stay_points[i + 1][2],
#                              'start_at': startl, 'end_at': endl, 'distance': distance}
#                     # save the distance with upsert
#                     stmt = ''' insert into location.travel_route ( user_device_id,start_lat, start_lng, end_lat, end_lng, start_at, end_at, distance )
#                                                     values (:user_device_id,:start_lat, :start_lng, :end_lat, :end_lng, :start_at, :end_at, :distance )'''
#                     result = await conn.execute(text(stmt), [route])
#                     print('insert result', result)
#                     stmt = f''' update location.stay_point set processed = 1 where
#                                 user_device_id = {user_device_id}
#                                 and stay_point_id = {stay_points[i][0]}
#                             '''
#                     result = await conn.execute(text(stmt))
#                 # now set processed for user_event as well
#                 # stmt = f''' update main.user_event set processed = 1 where
#                 #             user_device_id = {user_device_id}
#                 #             and user_event_id in ({fullUserEvent["start_user_event_id"]}, {fullUserEvent["end_user_event_id"]})
#                 #         '''
#                 # result = await conn.execute(text(stmt))
#                 await conn.commit()
#     except Exception as e:
#         traceback.print_exc()
#         print('exception in DB', e)
#     finally:
#         await conn.close()
#     return 0
