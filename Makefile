SHELL=/bin/bash

# Below two lines allows us to accept extra arguments (by doing nothing when we get a job that doesn't match, rather than throwing an error)
%:
	@:

args = `arg="$(filter-out $@,$(MAKECMDGOALS))" && echo $${arg:-${1}}`

.PHONY:
.DEFAULT_GOAL: guide

compile-templates:
#	@IS_SERVER=True DB_TYPE=POSTGRES typeorm migration:generate -d ./db/datasources/serverconfig.js -o ./db/migrations/server/config/$(call args) -t true || true
	@PATH="$(PATH):./node_modules/.bin" handlebars queries/activity/*.hbs -f queries/query-templates.js -c handlebars/runtime

clean-install:
#	@IS_SERVER=True DB_TYPE=POSTGRES typeorm migration:generate -d ./db/datasources/serverconfig.js -o ./db/migrations/server/config/$(call args) -t true || true
	@rm -rf node_modules
	@export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && npm install

create-build:
#	@IS_SERVER=True DB_TYPE=POSTGRES typeorm migration:generate -d ./db/datasources/serverconfig.js -o ./db/migrations/server/config/$(call args) -t true || true
	@rm -rf build
	@grunt

publish-package:
#	export GITLAB_KRUSHAL_OC_PUBLISH_AUTH_TOKEN="********************" && echo $GITLAB_KRUSHAL_OC_PUBLISH_AUTH_TOKEN
	@export GITLAB_AUTH_TOKEN=${PUBLISH_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && cd build && npm publish && cd .. && export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN}
#	@export GITLAB_AUTH_TOKEN=${PUBLISH_KRUSHAL_OC_GITLAB_AUTH_TOKEN} && cd build && npm publish && cd .. && export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_OC_GITLAB_AUTH_TOKEN}
#	@npm publish
#	@cd .. 
#	@echo GITLAB_AUTH_TOKEN
#	@export GITLAB_AUTH_TOKEN=ABCD