SHELL=/bin/bash

# Below two lines allows us to accept extra arguments (by doing nothing when we get a job that doesn't match, rather than throwing an error)
%:
	@:

args = `arg="$(filter-out $@,$(MAKECMDGOALS))" && echo $${arg:-${1}}`
migrationServiceRegistryName=$$(jq -r .migrationServiceRegistry package.json)
migrationServicePackageName = $$(jq -r .migrationServiceName package.json)
migrationServiceVersion = $$(jq -r .migrationServiceVersion package.json)
VAR_ENV_JSON = $(shell cat .env | grep ENV_JSON= | cut -c 10-)

.PHONY:
.DEFAULT_GOAL: guide

install:
	@export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && npm install

clean-krushal-packages-and-install:
	@rm -rf node_modules/@krushal-it node_modules/@krushal-oc package-lock.json
	@export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && npm install

clean-install:
	@rm -rf node_modules package-lock.json
	@export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && npm install

create-server-main-postgres-migration:
	@PATH="$(PATH):./node_modules/.bin" && IS_SERVER=True DB_TYPE=POSTGRES typeorm migration:create -o ./db/migrations/server/main/$(call args) -t true || true

generate-server-main-postgres-migration:
	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON) IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:generate -d ./db/datasources/servermain.js -o ./db/migrations/server/main/$(call args) -t true || true

generate-server-main-sqlite-migration:
	@ENV_JSON=$(VAR_ENV_JSON) FORCE_LOAD_CONFIGURATION=True IS_SERVER=True DB_TYPE=SQLITE typeorm migration:generate -d ./db/datasources/servermain.js -o ./db/migrations/server/main/$(call args) -t true || true

#run-server-main-postgres-migration:
#	# @export $(cat .env) && IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/servermain.js
#	@PATH="$(PATH):./node_modules/.bin" && FORCE_LOAD_CONFIGURATION=True IS_SERVER=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/servermain.js


#run-server-main-postgres-migration:
#	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON) IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/servermain.js
run-server-main-postgres-migration-local:
	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON) IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/servermain.js	

run-server-main-postgres-migration-server:
	@PATH="$(PATH):./node_modules/.bin" && IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/servermain.js	

run-server-main-postgres-migration:
	# @export $(cat .env) && IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/servermain.js
	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON) FORCE_LOAD_CONFIGURATION=True IS_SERVER=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/servermain.js

run-server-main-sqlite-migration:
	@IS_SERVER=True DB_TYPE=SQLITE typeorm migration:run -d ./db/datasources/servermain.js


create-server-location-postgres-migration:
	@IS_SERVER=True DB_TYPE=POSTGRES typeorm migration:create -o ./db/migrations/server/location/$(call args) -t true || true

generate-server-location-postgres-migration:
	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON)  IS_SERVER=True DB_TYPE=POSTGRES FORCE_LOAD_CONFIGURATION=True  typeorm migration:generate -d ./db/datasources/serverlocation.js -o ./db/migrations/server/location/$(call args) -t true || true

run-server-location-postgres-migration:
	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON) IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/serverlocation.js


generate-server-config-postgres-migration:
#	@IS_SERVER=True DB_TYPE=POSTGRES  typeorm migration:generate -d ./db/datasources/serverconfig.js -o ./db/migrations/server/config/$(call args) -t true || true
	@ENV_JSON=$(VAR_ENV_JSON) IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:generate -d ./db/datasources/serverconfig.js -o ./db/migrations/server/config/$(call args) -t true || true

generate-server-config-sqlite-migration:
	@IS_SERVER=True DB_TYPE=SQLITE typeorm migration:generate -d ./db/datasources/serverconfig.js -o ./db/migrations/server/config/$(call args) -t true || true

run-server-config-postgres-migration-local:
	# @export $(cat .env) && IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/serverconfig.js
	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON) IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/serverconfig.js

run-server-config-postgres-migration-server:
	# @export $(cat .env) && IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/serverconfig.js
	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON) FORCE_LOAD_CONFIGURATION=True IS_SERVER=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/serverconfig.js

run-server-config-sqlite-migration:
	@IS_SERVER=True DB_TYPE=SQLITE typeorm migration:run -d ./db/datasources/serverconfig.js

create-server-common-migration:
	@IS_SERVER=True DB_TYPE=POSTGRES typeorm migration:create -o ./db/migrations/server/common/$(call args) -t true || true

run-server-common-migration-server:
	# @export $(cat .env) && IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/servercommon.js
#	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON) FORCE_LOAD_CONFIGURATION=True IS_SERVER=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/servercommon.js
	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON) IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/servercommon.js

run-server-common-migration1:
	IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/servercommon.js

generate-server-reporting-migration:
	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON) IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:generate -d ./db/datasources/serverreporting.js -o ./db/migrations/server/reporting/$(call args) -t true || true

create-server-reporting-postgres-migration:
	@PATH="$(PATH):./node_modules/.bin" && IS_SERVER=True DB_TYPE=POSTGRES typeorm migration:create -o ./db/migrations/server/reporting/$(call args) -t true || true

run-server-reporting-migration-server:
	# @export $(cat .env) && IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/serverreporting.js
	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON) FORCE_LOAD_CONFIGURATION=True IS_SERVER=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/serverreporting.js

run-server-reporting-postgres-migration-local:
	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON) IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES typeorm migration:run -d ./db/datasources/serverreporting.js

create-mobile-main-migration:
	@IS_SERVER=False DB_TYPE=SQLITE GENERATE_MOBILE_MIGRATION_ON_SERVER=True typeorm migration:create -o ./db/migrations/mobile/main/$(call args) -t true || true

generate-mobile-main-migration:
	@PATH="$(PATH):./node_modules/.bin" ENV_JSON=$(VAR_ENV_JSON) FORCE_LOAD_CONFIGURATION=True IS_SERVER=False DB_TYPE=SQLITE GENERATE_MOBILE_MIGRATION_ON_SERVER=True typeorm migration:generate -d ./db/datasources/mobilemain.js -o ./db/migrations/mobile/main/$(call args) -t true || true

run-mobile-main-migration:
	@PATH="$(PATH):./node_modules/.bin" ENV_JSON=$(VAR_ENV_JSON) FORCE_LOAD_CONFIGURATION=True IS_SERVER=False DB_TYPE=SQLITE GENERATE_MOBILE_MIGRATION_ON_SERVER=True typeorm migration:run -d ./db/datasources/mobilemain.js

generate-mobile-config-migration:
	@IS_SERVER=False DB_TYPE=SQLITE GENERATE_MOBILE_MIGRATION_ON_SERVER=True typeorm migration:generate -d ./db/datasources/mobileconfig.js -o ./db/migrations/mobile/config/$(call args) -t true || true

run-mobile-config-migration:
	@IS_SERVER=False DB_TYPE=SQLITE GENERATE_MOBILE_MIGRATION_ON_SERVER=True typeorm migration:run -d ./db/datasources/mobileconfig.js

regenerate-updated-at-triggers:
	@PATH="$(PATH):./node_modules/.bin" && ENV_JSON=$(VAR_ENV_JSON) IS_SERVER=True IS_SERVER=True FORCE_LOAD_CONFIGURATION=True DB_TYPE=POSTGRES npm run triggerHelper

migration-service-clean-build:
	@rm -rf migration-service-build && grunt --gruntfile MigrationServiceGruntfile.js

migration-service-docker-build:
	@cd migration-service-build && docker build --build-arg GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} -t $(migrationServiceRegistryName)/$(migrationServicePackageName):$(migrationServiceVersion) .

migration-service-docker-push:
	@cd migration-service-build && docker push $(migrationServiceRegistryName)/$(migrationServicePackageName):$(migrationServiceVersion)

package-clean-build:
	@rm -rf package-build && grunt --gruntfile PackageGruntfile.js

package-publish-package:
	@export GITLAB_AUTH_TOKEN=${PUBLISH_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && cd package-build && npm publish && cd .. && export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN}
