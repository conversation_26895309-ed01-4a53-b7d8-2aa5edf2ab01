SHELL=/bin/bash

# Below two lines allows us to accept extra arguments (by doing nothing when we get a job that doesn't match, rather than throwing an error)
%:
	@:

args = `arg="$(filter-out $@,$(MAKECMDGOALS))" && echo $${arg:-${1}}`

.PHONY:
.DEFAULT_GOAL: guide

create-build:
	@rm -rf build
	@grunt

publish-package:
	@export GITLAB_AUTH_TOKEN=${PUBLISH_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && cd build && npm publish && cd .. && export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN}
