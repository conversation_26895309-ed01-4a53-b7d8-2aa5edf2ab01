SHELL=/bin/bash

# Below two lines allows us to accept extra arguments (by doing nothing when we get a job that doesn't match, rather than throwing an error)
%:
	@:

args = `arg="$(filter-out $@,$(MAKECMDGOALS))" && echo $${arg:-${1}}`

.PHONY:
.DEFAULT_GOAL: guide

install:
	@export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && npm install --legacy-peer-deps

start:
	@npm start

clean-krushal-packages-and-install:
	@rm -rf node_modules/@krushal-it node_modules/@krushal-oc package-lock.json
	@export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && npm install --legacy-peer-deps

clean-install:
	@rm -rf node_modules package-lock.json
	@export GITLAB_AUTH_TOKEN=${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN} && npm install --legacy-peer-deps

push-env:
	@sh updateEnvJsonToDotEnv.sh
