.PHONY: help
help: ## Show this help
	@egrep -h '\s##\s' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

.PHONY: lint
lint:  ## Linter the code.
	isort app --check
	flake8 .
	mypy .

location_analytics_debug_build:
	@docker build -t location_analytics_debug -f ./Dockerfile ../krushal_infrastructure/docker/build/location_analytics_debug
location_analytics_debug_up:
	@docker-compose -f ../krushal_infrastructure/docker/build/local-docker-compose.yml up location_analytics_debug

location_analytics_up:
	@docker-compose -f ../krushal_infrastructure/docker/build/local-docker-compose.yml up -d location_analytics

location_analytics_debug_down:
	@docker-compose -f ../krushal_infrastructure/docker/build/local-docker-compose.yml rm -fsv  location_analytics_debug

location_analytics_down:
	@docker-compose -f ../krushal_infrastructure/docker/build/local-docker-compose.yml rm -fsv location_analytics

restart-and-show-logs:
	@docker restart local_analytics_debug
	@docker logs -f -n 100 local_analytics_debug
