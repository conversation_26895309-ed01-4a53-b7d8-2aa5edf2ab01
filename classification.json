{"CLASSIFICATION_CONFIGURATION": {"CUSTOMER_CLASSIFICATION": {"TABLE": "customer", "DB": "main", "CLASSIFICATION_TABLE": "customer_classification", "CLASSIFICATION_TABLE_PRIMARY_KEY": "customer_classification_id", "CLASSIFICATION_TABLE_ENTITY_COLUMN": "customer_id", "ATTRIBUTE_LIST": {"mastitis_occurrences_12_months": {"classifier_id": **********, "column": "value_int", "allow_multiple": false}, "mastitis_or_other_deaths_12_months": {"classifier_id": **********, "column": "value_int", "allow_multiple": false}, "customer_credit_limit": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "customer_credit_usage": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "account_payment_type": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": true}, "serviceable": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "pouring_dairy": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "is_farmer_looking_to_rear_a_female_calf": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "is_farmer_looking_to_buy_a_cow": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "total_land_for_foliages": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "farmer_available_feeds": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": true}, "current_no_of_female_calves": {"classifier_id": **********, "column": "value_int", "allow_multiple": false}, "current_no_of_milking_cows": {"classifier_id": 2000000445, "column": "value_int", "allow_multiple": false}, "current_no_of_dry_cows": {"classifier_id": 2000000444, "column": "value_int", "allow_multiple": false}, "c⁠urrent_total_solids": {"classifier_id": 2000000443, "column": "value_double", "allow_multiple": false}, "current_total_fat": {"classifier_id": 2000000442, "column": "value_double", "allow_multiple": false}, "current_solid_non_fat": {"classifier_id": 2000000441, "column": "value_double", "allow_multiple": false}, "current_lpd": {"classifier_id": 2000000440, "column": "value_double", "allow_multiple": false}, "current_lpd_as_of_date": {"classifier_id": 2000000466, "column": "value_date", "allow_multiple": false}, "current_solid_non_fat_as_of_date": {"classifier_id": 2000000467, "column": "value_date", "allow_multiple": false}, "current_total_fat_as_of_date": {"classifier_id": 2000000468, "column": "value_date", "allow_multiple": false}, "current_total_solids_as_of_date": {"classifier_id": 2000000469, "column": "value_date", "allow_multiple": false}, "jersey_dairy_id": {"classifier_id": 2000000439, "column": "value_string_256", "allow_multiple": false}, "farmer_dairy_location_1": {"classifier_id": 2000000004, "column": "value_json", "allow_multiple": false}, "type_of_dairy_farm_1": {"classifier_id": 2000000005, "column": "value_reference_id", "allow_multiple": false, "category_id": 10001100, "is_select": true}, "total_num_of_cow_1": {"classifier_id": 2000000006, "column": "value_int", "allow_multiple": false}, "total_num_of_cow_calves_1": {"classifier_id": 2000000007, "column": "value_int", "allow_multiple": false}, "total_num_of_buffalo_1": {"classifier_id": 2000000008, "column": "value_int", "allow_multiple": false}, "total_num_of_buff_calves_1": {"classifier_id": 2000000009, "column": "value_int", "allow_multiple": false}, "acreage_range_for_foliage_crops_1": {"classifier_id": 2000000010, "column": "value_reference_id", "allow_multiple": false, "category_id": 10001200, "is_select": true}, "other_acreage_range_for_foliage_crops_1": {"classifier_id": 2000000011, "column": "value_string_256", "allow_multiple": false}, "acreage_for_foliage_crops_1": {"classifier_id": 2000000012, "column": "value_double", "allow_multiple": false}, "foliage_crops_grown_1": {"classifier_id": 2000000013, "column": "value_reference_id", "allow_multiple": true, "category_id": 10001300, "is_select": true}, "other_foliage_crops_grown_1": {"classifier_id": 2000000014, "column": "value_string_256", "allow_multiple": false}, "num_of_lab_used_for_cow_farm_1": {"classifier_id": 2000000015, "column": "value_double", "allow_multiple": false}, "monthly_labour_cost_1": {"classifier_id": 2000000016, "column": "value_double", "allow_multiple": false}, "total_dairy_income_1": {"classifier_id": 2000000017, "column": "value_double", "allow_multiple": false}, "total_number_of_cow_purchased_in_last_3_years_1": {"classifier_id": 2000000018, "column": "value_int", "allow_multiple": false}, "total_number_of_cow_sold_in_last_3_years_1": {"classifier_id": 2000000019, "column": "value_int", "allow_multiple": false}, "total_number_of_female_calves_purchased_in_last_3_years_1": {"classifier_id": 2000000020, "column": "value_int", "allow_multiple": false}, "average_price_of_cow_purchased_in_last_3_years_1": {"classifier_id": 2000000021, "column": "value_double", "allow_multiple": false}, "average_price_of_cow_sold_in_last_3_years_1": {"classifier_id": 2000000022, "column": "value_double", "allow_multiple": false}, "average_price_of_female_calf_purchased_in_last_3_years_1": {"classifier_id": 2000000023, "column": "value_double", "allow_multiple": false}, "average_price_of_female_calves_sold_in_last_3_years_1": {"classifier_id": 2000000024, "column": "value_double", "allow_multiple": false}, "total_number_of_female_calves_sold_in_last_3_years_1": {"classifier_id": 2000000159, "column": "value_int", "allow_multiple": false}, "total_income_1": {"classifier_id": 2000000025, "column": "value_double", "allow_multiple": false}, "total_land_inc_foilage_1": {"classifier_id": 2000000026, "column": "value_double", "allow_multiple": false}, "total_number_of_aged_dependents_1": {"classifier_id": 2000000027, "column": "value_int", "allow_multiple": false}, "total_number_of_adults_in_household_1": {"classifier_id": 2000000028, "column": "value_int", "allow_multiple": false}, "total_number_of_children_in_household_1": {"classifier_id": 2000000029, "column": "value_int", "allow_multiple": false}, "assets_owned_1": {"classifier_id": 2000000030, "column": "value_reference_id", "allow_multiple": true, "category_id": 10003600, "is_select": true}, "current_loan_1": {"classifier_id": 2000000031, "column": "value_reference_id", "allow_multiple": true, "category_id": 10003700, "is_select": true}, "total_emi_per_month_1": {"classifier_id": 2000000032, "column": "value_double", "allow_multiple": false}, "farmer_state_1": {"classifier_id": 2000000052, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "farmer_district_1": {"classifier_id": 2000000053, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "farmer_taluka_1": {"classifier_id": 2000000054, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "farmer_village_1": {"classifier_id": 2000000055, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "farmer_address_1": {"classifier_id": 2000000056, "column": "value_json", "allow_multiple": false}, "aadhaar_1": {"classifier_id": 2000000003, "column": "value_string_256_encrypted", "allow_multiple": false}, "dob_1": {"classifier_id": 2000000095, "column": "value_date", "allow_multiple": false}, "age_1": {"classifier_id": 2000000096, "column": "value_double", "allow_multiple": false}, "farmer_foliage_available_1": {"classifier_id": 2000000169, "column": "value_reference_id", "allow_multiple": true, "category_id": 10001300}, "farmer_concentrates_available_1": {"classifier_id": 2000000170, "column": "value_reference_id", "allow_multiple": true, "category_id": 10013000}, "farmer_pan_1": {"classifier_id": 2000000225, "column": "value_string_256", "allow_multiple": false}, "customer_tnc_status": {"classifier_id": 2000000207, "column": "value_reference_id", "allow_multiple": false}, "number_of_adult_cows": {"classifier_id": 2000000006, "column": "value_int", "allow_multiple": false}, "number_of_adult_buffaloes": {"classifier_id": 2000000008, "column": "value_int", "allow_multiple": false}, "acres_of_land": {"classifier_id": 2000000026, "column": "value_double", "allow_multiple": false}, "crops_grown": {"classifier_id": 2000000257, "column": "value_reference_id", "allow_multiple": true, "is_select": true}, "alternate_mobile_number": {"classifier_id": 2000000453, "column": "value_string_256", "allow_multiple": false}, "cost_of_milk_production": {"classifier_id": 2000000480, "column": "value_double", "allow_multiple": false}, "cost_of_milk_production_as_of_date": {"classifier_id": 2000000481, "column": "value_date", "allow_multiple": false}}}, "ANIMAL_CLASSIFICATION": {"TABLE": "animal", "DB": "main", "CLASSIFICATION_TABLE": "animal_classification", "CLASSIFICATION_TABLE_PRIMARY_KEY": "animal_classification_id", "CLASSIFICATION_TABLE_ENTITY_COLUMN": "animal_id", "ATTRIBUTE_LIST": {"current_lpd": {"classifier_id": 2000000440, "column": "value_double", "allow_multiple": false}, "current_lpd_as_of_date": {"classifier_id": 2000000466, "column": "value_date", "allow_multiple": false}, "animal_gap_in_teeth_1": {"classifier_id": 2000000210, "category_id": 10007700, "column": "value_reference_id", "allow_multiple": false}, "animal_horns_exist_or_not_1": {"classifier_id": 2000000211, "category_id": 10007800, "column": "value_reference_id", "allow_multiple": false}, "animal_spread_of_patches_1": {"classifier_id": 2000000212, "category_id": 10005600, "column": "value_reference_id", "allow_multiple": false}, "animal_stretchability_of_udder_1": {"classifier_id": 2000000213, "category_id": 10005700, "column": "value_reference_id", "allow_multiple": false}, "animal_visbility_of_veins_1": {"classifier_id": 2000000214, "category_id": 10005800, "column": "value_reference_id", "allow_multiple": false}, "animal_additional_teats_1": {"classifier_id": **********, "category_id": 10008100, "column": "value_reference_id", "allow_multiple": false}, "animal_inactive_reason_1": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false, "category_id": 10020000}, "animal_inactive_date_1": {"classifier_id": **********, "column": "value_date", "allow_multiple": false}, "animal_inactive_reason_other_1": {"classifier_id": **********, "column": "value_string_256", "allow_multiple": false}, "healthcare_plan_subscription_date_1": {"classifier_id": **********, "column": "value_date", "allow_multiple": false}, "animal_name_1": {"classifier_id": **********, "column": "value_string_256", "allow_multiple": false}, "ear_tag_1": {"classifier_id": **********, "column": "value_string_256", "allow_multiple": false}, "animal_type": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false, "category_id": 10001400, "is_select": true}, "animal_breed_1": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false, "category_id": 10001500, "is_select": true}, "animal_other_breed_1": {"classifier_id": **********, "column": "value_string_256", "allow_multiple": false}, "animal_age_1": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "age_of_animal_as_of_1": {"classifier_id": **********, "column": "value_date", "allow_multiple": false}, "animal_dob_1": {"classifier_id": **********, "column": "value_date", "allow_multiple": false}, "animal_weight_1": {"classifier_id": 2000000041, "column": "value_double", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["animal_weight_1_updated_at"], "allow_multiple": false}, "animal_temperature_1": {"classifier_id": 2000000141, "column": "value_double", "allow_multiple": false}, "cattle_insurance_1": {"classifier_id": 2000000187, "column": "value_reference_id", "allow_multiple": false, "category_id": 10001050, "is_select": true}, "number_of_calvings_1": {"classifier_id": 2000000042, "column": "value_int", "allow_multiple": false, "is_select": true}, "milking_status_1": {"classifier_id": 2000000043, "column": "value_reference_id", "allow_multiple": false, "category_id": 10001600, "is_select": true}, "animal_stage_1": {"classifier_id": 2000000146, "column": "value_reference_id", "allow_multiple": false, "category_id": 10010000, "is_select": true}, "animal_milking_1": {"classifier_id": 2000000147, "column": "value_reference_id", "allow_multiple": false, "category_id": 10001050, "is_select": true}, "animal_preganancy_1": {"classifier_id": 2000000148, "column": "value_reference_id", "allow_multiple": false, "category_id": 10001050, "is_select": true}, "animal_number_of_permanent_teeth_1": {"classifier_id": 2000000154, "column": "value_double", "allow_multiple": false}, "max_lpd_of_animal_1": {"classifier_id": **********, "column": "value_double", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["max_lpd_of_animal_1_updated_at"], "allow_multiple": false}, "animal_average_lpd_1": {"classifier_id": **********, "column": "value_double", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["animal_average_lpd_1_updated_at"], "allow_multiple": false}, "animal_feed_recommendations": {"classifier_id": **********, "column": "value_json", "allow_multiple": false}, "animal_health_score": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "animal_locomotion_score_1": {"classifier_id": **********, "column": "value_reference_id", "category_id": 10006500, "other_load_columns": ["updated_at"], "other_load_column_aliases": ["animal_locomotion_score_1_updated_at"], "allow_multiple": false}, "animal_dung_score_1": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "animal_body_score_1": {"classifier_id": **********, "column": "value_double", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["animal_body_score_1_updated_at"], "allow_multiple": false}, "animal_body_back_view_condition_1": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "animal_body_side_view_condition_1": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "body_score_observation_tail_and_pin_bone": {"classifier_id": **********, "category_id": 10006600, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "body_score_observation_2": {"classifier_id": 2000000198, "column": "value_double", "allow_multiple": false}, "body_score_observation_back_bone": {"classifier_id": 2000000199, "category_id": 10006700, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "body_score_hip_and_pin_bone": {"classifier_id": 2000000200, "category_id": 10006800, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "animal_udder_score_1": {"classifier_id": 2000000145, "column": "value_double", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["animal_udder_score_1_updated_at"], "allow_multiple": false}, "animal_udder_hygene_score_1": {"classifier_id": 2000000155, "column": "value_double", "allow_multiple": false}, "animal_udder_depth_balance_score_1": {"classifier_id": 2000000156, "column": "value_double", "allow_multiple": false}, "animal_udder_cleft_score_1": {"classifier_id": 2000000157, "column": "value_double", "allow_multiple": false}, "animal_nose_condition_score_1": {"classifier_id": 2000000158, "column": "value_double", "allow_multiple": false}, "animal_foreign_body_1": {"classifier_id": **********, "category_id": 10008000, "column": "value_reference_id", "allow_multiple": false}, "animal_milk_fat_1": {"classifier_id": 2000000172, "column": "value_double", "allow_multiple": false}, "animal_sick_last_one_year_1": {"classifier_id": **********, "column": "value_int", "allow_multiple": false}, "temporal_entity_type_id_1": {"classifier_id": **********, "column": "value_reference_id", "category_id": 10009000, "allow_multiple": false}, "three_month_animal_illness_1": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": true, "category_id": 10001700, "is_select": true}, "other_three_month_animal_illness_1": {"classifier_id": **********, "column": "value_string_256", "allow_multiple": false}, "two_month_preventive_healthcare_1": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": true, "category_id": 10001800, "is_select": true}, "other_two_month_preventive_healthcare_1": {"classifier_id": **********, "column": "value_string_256", "allow_multiple": false}, "pregnancy_range_of_animal_1": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false, "category_id": 10001900, "is_select": true}, "insurance_status_of_animal_1": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false, "category_id": 10002000, "is_select": true}, "last_deworming_date_1": {"classifier_id": **********, "column": "value_date", "allow_multiple": false}, "number_of_month_since_last_deworming_1": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "last_dip_cups_replacement_date_1": {"classifier_id": **********, "column": "value_date", "allow_multiple": false}, "date_on_animal_went_dry": {"classifier_id": **********, "column": "value_date", "allow_multiple": false}, "dip_cup_status_1": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false, "category_id": 10004100, "is_select": true}, "last_tick_control_date_1": {"classifier_id": 2000000062, "column": "value_date", "allow_multiple": false}, "last_tick_control_status_1": {"classifier_id": 2000000061, "column": "value_reference_id", "allow_multiple": false, "category_id": 10003800, "is_select": true}, "last_calving_date_1": {"classifier_id": 2000000063, "column": "value_date", "allow_multiple": false}, "months_since_last_calving_1": {"classifier_id": 2000000064, "column": "value_int", "allow_multiple": false}, "month_since_calving_as_of_data_1": {"classifier_id": 2000000163, "column": "value_date", "allow_multiple": false}, "month_since_calving_as_of_date_1": {"classifier_id": 2000000163, "column": "value_date", "allow_multiple": false}, "last_ai_date_1": {"classifier_id": 2000000065, "column": "value_date", "allow_multiple": false}, "last_pregnancy_determination_date_1": {"classifier_id": 2000000066, "column": "value_date", "allow_multiple": false}, "number_of_months_pregnant_1": {"classifier_id": 2000000067, "column": "value_double", "allow_multiple": false, "is_select": true}, "months_pregnant_as_of_date": {"classifier_id": 2000000165, "column": "value_date", "allow_multiple": false}, "last_fmd_vaccination_date_range_1": {"classifier_id": 2000000068, "column": "value_reference_id", "allow_multiple": false, "category_id": 10004000, "is_select": true}, "last_fmd_vaccination_date_1": {"classifier_id": 2000000069, "column": "value_date", "allow_multiple": false}, "last_fmd_hs_bq_date_range_1": {"classifier_id": 2000000070, "column": "value_reference_id", "allow_multiple": false, "category_id": 10004000, "is_select": true}, "last_fmd_or_hs_or_bq_vaccination_date_1": {"classifier_id": 2000000071, "column": "value_date", "allow_multiple": false}, "brucellosis_vaccination_done_or_not_1": {"classifier_id": 2000000072, "column": "value_reference_id", "allow_multiple": false, "category_id": 10001050, "is_select": true}, "last_thileriosis_vaccination_date_1": {"classifier_id": 2000000073, "column": "value_date", "allow_multiple": false}, "theileriosis_vaccination_status_1": {"classifier_id": 2000000074, "column": "value_reference_id", "allow_multiple": false, "category_id": 10004000, "is_select": true}, "last_theileriosis_vaccination_date_1": {"classifier_id": 2000000075, "column": "value_date", "allow_multiple": false}, "anthrax_vaccination_status_1": {"classifier_id": 2000000076, "column": "value_reference_id", "allow_multiple": false, "category_id": 10003900, "is_select": true}, "pregnancy_month_1": {"classifier_id": 2000000085, "column": "value_int", "allow_multiple": false}, "subscription_plan_1": {"classifier_id": 2000000051, "column": "value_reference_id", "allow_multiple": false, "category_id": 10002400, "is_select": true}, "last_deworming_status_1": {"classifier_id": 2000000160, "column": "value_reference_id", "allow_multiple": false, "category_id": 10003800, "is_select": true}, "last_deworming_status_as_of_date_1": {"classifier_id": 2000000161, "column": "value_date", "allow_multiple": false}, "last_tick_control_status_as_of_date_1": {"classifier_id": 2000000162, "column": "value_date", "allow_multiple": false}, "month_since_last_calving_as_of_date_1": {"classifier_id": 2000000163, "column": "value_date", "allow_multiple": false}, "pregnancy_status_as_of_date_1": {"classifier_id": 2000000164, "column": "value_date", "allow_multiple": false}, "last_anthrax_date_1": {"classifier_id": 2000000171, "column": "value_date", "allow_multiple": false}, "number_of_months_pregnant_as_of_date_1": {"classifier_id": 2000000165, "column": "value_date", "allow_multiple": false}, "last_fmd_vaccination_date_range_as_of_date_1": {"classifier_id": 2000000166, "column": "value_date", "allow_multiple": false}, "last_fmd_hs_bq_vaccination_date_range_as_of_date_1": {"classifier_id": 2000000167, "column": "value_date", "allow_multiple": false}, "theilriosis_vaccination_status_as_of_date_1": {"classifier_id": 2000000168, "column": "value_date", "allow_multiple": false}, "animal_activation_date": {"classifier_id": 2000000177, "column": "value_date", "allow_multiple": false}, "animal_active_reason_1": {"classifier_id": 2000000178, "column": "value_reference_id", "allow_multiple": false, "category_id": 10060000}, "animal_active_reason_other_1": {"classifier_id": 2000000179, "column": "value_string_256", "allow_multiple": false}, "any_defects_in_animal_1": {"classifier_id": 2000000175, "column": "value_reference_id", "allow_multiple": false, "category_id": 10030000, "is_select": true}, "sales_registration_id_1": {"classifier_id": 2000000181, "column": "value_reference_uuid", "allow_multiple": false}, "sales_attribution_id_1": {"classifier_id": 2000000180, "column": "value_reference_uuid", "allow_multiple": false}, "udder_cleft": {"classifier_id": 2000000201, "category_id": 10006200, "column": "value_reference_id", "allow_multiple": false}, "udder_depth": {"classifier_id": 2000000202, "category_id": 10005900, "column": "value_reference_id", "allow_multiple": false}, "udder_balance": {"classifier_id": 2000000203, "category_id": 10006100, "column": "value_reference_id", "allow_multiple": false}, "udder_teat_placement": {"classifier_id": 2000000204, "category_id": 10006300, "column": "value_reference_id", "allow_multiple": false}, "udder_teat_length": {"classifier_id": 2000000205, "category_id": 10006400, "column": "value_reference_id", "allow_multiple": false}, "information_tracker_link": {"classifier_id": 2000000219, "column": "value_string_256", "allow_multiple": false}, "bs_tail_and_pin_bone_double": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "bs_tail_and_pin_bone_dropdown": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false, "other_load_columns": ["updated_at"], "other_load_column_aliases": ["bs_tail_and_pin_bone_dropdown_updated_at"], "category_id": 10006600, "is_select": true}, "bs_back_bone_double": {"classifier_id": 2000000199, "column": "value_double", "allow_multiple": false}, "bs_back_bone_dropdown": {"classifier_id": 2000000199, "column": "value_reference_id", "allow_multiple": false, "other_load_columns": ["updated_at"], "other_load_column_aliases": ["bs_back_bone_dropdown_updated_at"], "category_id": 10006700, "is_select": true}, "bs_hip_and_pin_bone_double": {"classifier_id": 2000000200, "column": "value_double", "allow_multiple": false}, "bs_hip_and_pin_bone_dropdown": {"classifier_id": 2000000200, "category_id": 10006800, "column": "value_reference_id", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["bs_hip_and_pin_bone_dropdown_updated_at"], "allow_multiple": false, "is_select": true}, "uts_udder_cleft_visibility_double": {"classifier_id": 2000000201, "column": "value_double", "allow_multiple": false}, "uts_udder_cleft_visibility_dropdown": {"classifier_id": 2000000201, "category_id": 10006200, "column": "value_reference_id", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["uts_udder_cleft_visibility_dropdown_updated_at"], "allow_multiple": false, "is_select": true}, "uts_udder_position_double": {"classifier_id": 2000000202, "column": "value_double", "allow_multiple": false}, "uts_udder_position_dropdown": {"classifier_id": 2000000202, "category_id": 10005900, "column": "value_reference_id", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["uts_udder_position_dropdown_updated_at"], "allow_multiple": false, "is_select": true}, "uts_udder_balance_double": {"classifier_id": 2000000203, "column": "value_double", "allow_multiple": false}, "uts_udder_balance_dropdown": {"classifier_id": 2000000203, "category_id": 10006100, "column": "value_reference_id", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["uts_udder_balance_dropdown_updated_at"], "allow_multiple": false, "is_select": true}, "uts_udder_teat_placement_double": {"classifier_id": 2000000204, "column": "value_double", "allow_multiple": false}, "uts_udder_teat_placement_dropdown": {"classifier_id": 2000000204, "category_id": 10006300, "column": "value_reference_id", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["uts_udder_teat_placement_dropdown_updated_at"], "allow_multiple": false, "is_select": true}, "uts_udder_teat_length_double": {"classifier_id": 2000000205, "column": "value_double", "allow_multiple": false}, "uts_udder_teat_length_dropdown": {"classifier_id": 2000000205, "category_id": 10006400, "column": "value_reference_id", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["uts_udder_teat_length_dropdown_updated_at"], "allow_multiple": false, "is_select": true}, "animal_locomotion_score_double": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "animal_locomotion_score_dropdown": {"classifier_id": **********, "column": "value_reference_id", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["animal_locomotion_score_dropdown_updated_at"], "category_id": 10006500, "allow_multiple": false, "is_select": true}, "ap_gap_in_teeth_double": {"classifier_id": 2000000210, "column": "value_double", "allow_multiple": false}, "ap_gap_in_teeth_dropdown": {"classifier_id": 2000000210, "category_id": 10007700, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "ap_horns_exist_or_not_double": {"classifier_id": 2000000211, "column": "value_double", "allow_multiple": false}, "ap_horns_exist_or_not_dropdown": {"classifier_id": 2000000211, "category_id": 10007800, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "ap_spread_of_patches_double": {"classifier_id": 2000000212, "column": "value_double", "allow_multiple": false}, "ap_spread_of_patches_dropdown": {"classifier_id": 2000000212, "category_id": 10005600, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "aphs_stretchability_of_udder_double": {"classifier_id": 2000000213, "column": "value_double", "allow_multiple": false}, "aphs_stretchability_of_udder_dropdown": {"classifier_id": 2000000213, "category_id": 10005700, "column": "value_reference_id", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["aphs_stretchability_of_udder_dropdown_updated_at"], "allow_multiple": false, "is_select": true}, "aphs_visibility_of_veins_double": {"classifier_id": 2000000214, "column": "value_double", "allow_multiple": false}, "aphs_visibility_of_veins_dropdown": {"classifier_id": 2000000214, "category_id": 10005800, "column": "value_reference_id", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["aphs_visibility_of_veins_dropdown_updated_at"], "allow_multiple": false, "is_select": true}, "aphs_additional_teats_double": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "aphs_additional_teats_dropdown": {"classifier_id": **********, "category_id": 10008100, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "hs_foreign_body_area_double": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "hs_foreign_body_area_dropdown": {"classifier_id": **********, "category_id": 10008000, "column": "value_reference_id", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["hs_foreign_body_area_dropdown_updated_at"], "allow_multiple": false, "is_select": true}, "cattle_exchange_price": {"classifier_id": **********, "column": "value_json", "allow_multiple": false}, "latest_health_score_verification_status": {"classifier_id": **********, "column": "value_reference_id", "other_load_columns": ["updated_at"], "other_load_column_aliases": ["latest_health_score_verification_status_updated_at"], "allow_multiple": false, "is_select": true}, "animal_average_lpd_as_of_date_1": {"classifier_id": **********, "column": "value_date", "allow_multiple": false}, "animal_milk_fat_as_of_date_1": {"classifier_id": **********, "column": "value_date", "allow_multiple": false}}}, "ANIMAL_DISEASE_CLASSIFICATION": {"TABLE": "animal_disease", "DB": "main", "CLASSIFICATION_TABLE": "animal_disease_classification", "CLASSIFICATION_TABLE_PRIMARY_KEY": "animal_disease_classification_id", "CLASSIFICATION_TABLE_ENTITY_COLUMN": "animal_disease_id", "ATTRIBUTE_LIST": {"animal_disease_type_1": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false, "category_id": 10001700, "is_select": true}, "animal_disease_date_1": {"classifier_id": **********, "column": "value_date", "allow_multiple": false}, "animal_disease_duration_1": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "animal_disease_medication_1": {"classifier_id": 2000000080, "column": "value_reference_id", "category_id": 10005000, "allow_multiple": true, "is_select": true}, "animal_disease_paravet_name_1": {"classifier_id": 2000000081, "column": "value_string_256", "allow_multiple": false}, "animal_disease_vet_name_1": {"classifier_id": 2000000082, "column": "value_string_256", "allow_multiple": false}}}, "CARE_CALENDAR_CLASSIFICATION": {"TABLE": "care_calendar", "DB": "main", "CLASSIFICATION_TABLE": "care_calendar_classification", "CLASSIFICATION_TABLE_PRIMARY_KEY": "care_calendar_classification_id", "CLASSIFICATION_TABLE_ENTITY_COLUMN": "care_calendar_id", "ATTRIBUTE_LIST": {"task_completion_location_1": {"classifier_id": **********, "column": "value_json", "allow_multiple": false}, "num_of_months_pregnant_2": {"classifier_id": **********, "column": "value_double", "allow_multiple": false}, "payment_details": {"classifier_id": **********, "column": "value_json", "allow_multiple": false}, "otp_verified": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false}, "otp": {"classifier_id": **********, "column": "value_int", "allow_multiple": false}, "account_payment_type": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false}, "invoice_generated": {"classifier_id": **********, "column": "value_reference_id", "allow_multiple": false}, "paid_amount": {"classifier_id": **********, "column": "value_int", "allow_multiple": false}, "number_of_animals": {"classifier_id": **********, "column": "value_int", "allow_multiple": false}, "number_of_calf": {"classifier_id": **********, "column": "value_int", "allow_multiple": false}, "date_of_visit": {"classifier_id": **********, "column": "value_date", "allow_multiple": false}, "housing_type": {"classifier_id": **********, "category_id": ********, "column": "value_reference_id", "allow_multiple": false}, "space_type": {"classifier_id": **********, "category_id": ********, "column": "value_reference_id", "allow_multiple": false}, "feeding_zone_type": {"classifier_id": **********, "category_id": ********, "column": "value_reference_id", "allow_multiple": false}, "resting_zone": {"classifier_id": 2000000287, "category_id": 10011900, "column": "value_reference_id", "allow_multiple": false}, "loffing_zone": {"classifier_id": 2000000288, "column": "value_reference_id", "allow_multiple": false}, "cow_grouping_done": {"classifier_id": 2000000289, "column": "value_reference_id", "allow_multiple": false}, "floor_condition": {"classifier_id": 2000000290, "category_id": 10012000, "column": "value_reference_id", "allow_multiple": false}, "water_stagnent_in_some_area": {"classifier_id": 2000000291, "column": "value_reference_id", "allow_multiple": false}, "any_existing_tree_or_plantation_done": {"classifier_id": 2000000292, "column": "value_reference_id", "allow_multiple": false}, "slope_of_total_area_maintained_for_draining_of_rainy_water": {"classifier_id": 2000000293, "column": "value_reference_id", "allow_multiple": false}, "nuisance_values_of_ectoparasites": {"classifier_id": 2000000294, "column": "value_reference_id", "allow_multiple": false}, "dirtiness_score": {"classifier_id": 2000000295, "category_id": 10012100, "column": "value_reference_id", "allow_multiple": false}, "dung_score": {"classifier_id": 2000000296, "category_id": 10012200, "column": "value_reference_id", "allow_multiple": false, "is_select": true}, "frequency_of_removing_dung_from_overall_area": {"classifier_id": 2000000297, "column": "value_int", "allow_multiple": false}, "manure_dung_storing_place_close_to_shed": {"classifier_id": 2000000298, "column": "value_reference_id", "allow_multiple": false}, "farm_management_form_completed_on": {"classifier_id": 2000000300, "column": "value_date", "allow_multiple": false}, "artificial_insemination_form_completed_on": {"classifier_id": 2000000271, "column": "value_date", "allow_multiple": false}, "clinical_mastitis_test_form_completed_on": {"classifier_id": 2000000272, "column": "value_date", "allow_multiple": false}, "pregnancy_detection_form_completed_on": {"classifier_id": 2000000273, "column": "value_date", "allow_multiple": false}, "farm_tick_control_spray_form_completed_on": {"classifier_id": 2000000274, "column": "value_date", "allow_multiple": false}, "ai_form_heat_date_and_time": {"classifier_id": 2000000131, "column": "value_date", "allow_multiple": false}, "ai_form_suitable_for_ai_based_on_heat": {"classifier_id": 2000000132, "column": "value_reference_id", "category_id": 10001050, "allow_multiple": false, "is_select": true}, "ai_form_suitable_for_ai_based_on_discharge": {"classifier_id": 2000000133, "column": "value_reference_id", "category_id": 10001050, "allow_multiple": false, "is_select": true}, "ai_form_company_of_semen": {"classifier_id": 2000000134, "column": "value_reference_id", "category_id": 10006000, "allow_multiple": false, "is_select": true}, "ai_form_name_of_bull": {"classifier_id": 2000000135, "column": "value_string_256", "allow_multiple": false}, "ai_form_id_of_bull": {"classifier_id": 2000000136, "column": "value_string_256", "allow_multiple": false}, "care_calendar_expiration_date": {"classifier_id": 2000000139, "column": "value_date", "allow_multiple": false}, "linked_care_calendar_task": {"classifier_id": 2000000126, "column": "value_reference_uuid", "allow_multiple": false}, "farmer_survey_data": {"classifier_id": 2000000138, "column": "value_json", "allow_multiple": false}, "medicine_1": {"classifier_id": 2000000104, "column": "value_reference_id", "allow_multiple": false, "category_id": 10005000, "is_select": true}, "medicine_1_quantity": {"classifier_id": 2000000105, "column": "value_double", "allow_multiple": false}, "medicine_2": {"classifier_id": 2000000106, "column": "value_reference_id", "allow_multiple": false, "category_id": 10005000, "is_select": true}, "medicine_2_quantity": {"classifier_id": 2000000107, "column": "value_double", "allow_multiple": false}, "medicine_3": {"classifier_id": 2000000108, "column": "value_reference_id", "allow_multiple": false, "category_id": 10005000, "is_select": true}, "medicine_3_quantity": {"classifier_id": 2000000109, "column": "value_double", "allow_multiple": false}, "medicine_4": {"classifier_id": 2000000110, "column": "value_reference_id", "allow_multiple": false, "category_id": 10005000, "is_select": true}, "medicine_4_quantity": {"classifier_id": 2000000111, "column": "value_double", "allow_multiple": false}, "medicine_5": {"classifier_id": 2000000112, "column": "value_reference_id", "allow_multiple": false, "category_id": 10005000, "is_select": true}, "medicine_5_quantity": {"classifier_id": 2000000113, "column": "value_double", "allow_multiple": false}, "medicine_6": {"classifier_id": 2000000114, "column": "value_reference_id", "allow_multiple": false, "category_id": 10005000, "is_select": true}, "medicine_6_quantity": {"classifier_id": 2000000115, "column": "value_double", "allow_multiple": false}, "medicine_7": {"classifier_id": 2000000116, "column": "value_reference_id", "allow_multiple": false, "category_id": 10005000, "is_select": true}, "medicine_7_quantity": {"classifier_id": 2000000117, "column": "value_double", "allow_multiple": false}, "medicine_8": {"classifier_id": 2000000118, "column": "value_reference_id", "allow_multiple": false, "category_id": 10005000, "is_select": true}, "medicine_8_quantity": {"classifier_id": 2000000119, "column": "value_double", "allow_multiple": false}, "medicine_9": {"classifier_id": 2000000120, "column": "value_reference_id", "allow_multiple": false, "category_id": 10005000, "is_select": true}, "medicine_9_quantity": {"classifier_id": 2000000121, "column": "value_double", "allow_multiple": false}, "medicine_10": {"classifier_id": 2000000122, "column": "value_reference_id", "allow_multiple": false, "category_id": 10005000, "is_select": true}, "medicine_10_quantity": {"classifier_id": 2000000123, "column": "value_double", "allow_multiple": false}, "ticket_1": {"classifier_id": 2000000125, "column": "value_string_256", "allow_multiple": false}, "vet_availablity_while_closing": {"classifier_id": 2000000191, "column": "value_reference_id", "category_id": 10001050, "allow_multiple": false, "is_select": true}, "lh_teat_cmt_score": {"classifier_id": 2000000193, "column": "value_int", "allow_multiple": false}, "rh_teat_cmt_score": {"classifier_id": 2000000194, "column": "value_int", "allow_multiple": false}, "lf_teat_cmt_score": {"classifier_id": 2000000195, "column": "value_int", "allow_multiple": false}, "rf_teat_cmt_score": {"classifier_id": 2000000196, "column": "value_int", "allow_multiple": false}, "is_cattle_pregnant": {"classifier_id": 2000000209, "column": "value_reference_id", "allow_multiple": false, "category_id": 10001050, "is_select": true}, "ai_form_company_name_of_semen_for_others": {"classifier_id": 2000000220, "column": "value_string_256", "allow_multiple": false}, "ai_task_completion_status": {"classifier_id": 2000000208, "column": "value_reference_id", "allow_multiple": false, "category_id": 10002000, "is_select": true}, "diagnosis_date": {"classifier_id": 2000000268, "column": "value_date", "allow_multiple": false}, "observation_date": {"classifier_id": 2000000269, "column": "value_date", "allow_multiple": false}, "prescription_publish_date": {"classifier_id": 2000000227, "column": "value_date", "allow_multiple": false}, "prescription_adminstered_date": {"classifier_id": 2000000228, "column": "value_date", "allow_multiple": false}, "service_request_type": {"classifier_id": 2000000243, "column": "value_reference_id", "allow_multiple": false, "category_id": 10009200}, "vet_cosultant_type": {"classifier_id": 2000000221, "column": "value_reference_id", "allow_multiple": false, "category_id": 10007500, "is_select": true}, "observation_category_configuration": {"classifier_id": 2000000426, "column": "value_json", "allow_multiple": false}, "observation_version": {"classifier_id": 2000000427, "column": "value_string_256", "allow_multiple": false}, "farmer_aflatoxin_flag": {"classifier_id": 2000000429, "column": "value_reference_id", "category_id": 10001050, "allow_multiple": false, "is_select": true}, "form_completion_date": {"classifier_id": 2000000421, "column": "value_date", "allow_multiple": false}, "saved_activity_form_data": {"classifier_id": 2000000437, "column": "value_json", "allow_multiple": false}, "task_service_cost": {"classifier_id": 2000000454, "column": "value_double", "allow_multiple": false}, "task_medicine_cost": {"classifier_id": 2000000455, "column": "value_double", "allow_multiple": false}, "heat_symptoms_mounting_behavior": {"classifier_id": 2000000456, "column": "value_reference_id", "category_id": 10001050, "allow_multiple": false, "is_select": true}, "vulva_lips_oedematous_and_pink": {"classifier_id": 2000000457, "column": "value_reference_id", "category_id": 10001050, "allow_multiple": false, "is_select": true}, "was_digital_ai_gun_used": {"classifier_id": 2000000458, "column": "value_reference_id", "category_id": 10001050, "allow_multiple": false, "is_select": true}, "cervix_normal": {"classifier_id": 2000000459, "column": "value_reference_id", "category_id": 10001050, "allow_multiple": false, "is_select": true}, "uterine_tone_normal_and_good": {"classifier_id": 2000000460, "column": "value_reference_id", "category_id": 10001050, "allow_multiple": false, "is_select": true}, "temperature": {"classifier_id": 2000000461, "column": "value_double", "allow_multiple": false, "is_select": true}, "discharges_clear_transparent_with_oily_viscosity": {"classifier_id": 2000000462, "column": "value_reference_id", "category_id": 10001050, "allow_multiple": false, "is_select": true}}}, "STAFF_CLASSIFICATION": {"TABLE": "staff", "DB": "main", "CLASSIFICATION_TABLE": "staff_classification", "CLASSIFICATION_TABLE_PRIMARY_KEY": "staff_classification_id", "CLASSIFICATION_TABLE_ENTITY_COLUMN": "staff_id", "ATTRIBUTE_LIST": {"staff_plan": {"classifier_id": 2000000241, "column": "value_reference_id", "allow_multiple": false}, "staff_plan_start_date": {"classifier_id": 2000000242, "column": "value_date", "allow_multiple": false}, "staff_pan": {"classifier_id": 2000000223, "column": "value_string_256", "allow_multiple": false}, "staff_aadhar": {"classifier_id": 2000000224, "column": "value_string_256", "allow_multiple": false}, "staff_app_access_groups_1": {"classifier_id": 2000000280, "column": "value_reference_id", "category_id": 10011500, "is_select": true, "allow_multiple": true}, "staff_vehicle_type_1": {"classifier_id": 2000000408, "column": "value_reference_id", "category_id": 10012400, "is_select": true, "allow_multiple": false}, "staff_feature_map": {"classifier_id": 2000000425, "column": "value_json", "allow_multiple": false}}}, "ASSET_CLASSIFICATION": {"TABLE": "asset", "DB": "main", "CLASSIFICATION_TABLE": "asset_classification", "CLASSIFICATION_TABLE_PRIMARY_KEY": "asset_classification_id", "CLASSIFICATION_TABLE_ENTITY_COLUMN": "asset_id", "ATTRIBUTE_LIST": {"acres_of_land": {"classifier_id": 2000000258, "column": "value_double", "allow_multiple": false}, "primary_crop": {"classifier_id": 2000000266, "column": "value_reference_id", "is_select": true, "allow_multiple": true}, "feed_generation_type": {"classifier_id": 2000000401, "category_id": 10015000, "column": "value_reference_id", "allow_multiple": false, "other_load_columns": ["updated_at"], "other_load_column_aliases": ["feed_generation_date"]}, "smart_ration_foliages": {"classifier_id": 2000000405, "category_id": 10001300, "column": "value_reference_id", "allow_multiple": true, "is_select": true}, "smart_ration_concentrates": {"classifier_id": 2000000406, "category_id": 10013000, "column": "value_reference_id", "allow_multiple": true, "is_select": true}, "smart_ration_other_nutrients": {"classifier_id": 2000000407, "category_id": 10013005, "column": "value_reference_id", "allow_multiple": true, "is_select": true}, "dm_min": {"classifier_id": 2000000414, "column": "value_double", "allow_multiple": false}, "dm_max": {"classifier_id": 2000000415, "column": "value_double", "allow_multiple": false}, "dcp": {"classifier_id": 2000000416, "column": "value_double", "allow_multiple": false}, "tdn": {"classifier_id": 2000000417, "column": "value_double", "allow_multiple": false}, "user_device_id": {"classifier_id": 2000000418, "column": "value_int", "allow_multiple": false}, "smart_ration_feasible_solution": {"classifier_id": 2000000419, "column": "value_reference_id", "category_id": 10001050, "allow_multiple": false}, "smart_ration_report_sent_to_customer": {"classifier_id": 2000000423, "column": "value_reference_id", "category_id": 10001050, "allow_multiple": false}}}, "MEDICINE_CLASSIFICATION": {"TABLE": "ref_medicine", "DB": "main", "CLASSIFICATION_TABLE": "ref_medicine_classification", "CLASSIFICATION_TABLE_PRIMARY_KEY": "medicine_classification_id", "CLASSIFICATION_TABLE_ENTITY_COLUMN": "medicine_id", "ATTRIBUTE_LIST": {"medicine_mrp": {"classifier_id": 2000000264, "column": "value_double", "allow_multiple": false}, "medicine_unit": {"classifier_id": 2000000244, "column": "value_reference_id", "is_select": true, "allow_multiple": false}, "medicine_form": {"classifier_id": 2000000245, "column": "value_reference_id", "is_select": true, "allow_multiple": false}, "medicine_pack_size": {"classifier_id": 2000000246, "column": "value_double", "allow_multiple": false}, "medicine_used_by_flag": {"classifier_id": 2000000247, "column": "value_reference_id", "is_select": true, "allow_multiple": false}, "medicine_zoho_item_id": {"classifier_id": 2000000251, "column": "value_string_256", "allow_multiple": false}, "is_krushal_medicine": {"classifier_id": 2000000267, "column": "value_reference_id", "is_select": true, "category_id": 10001050, "allow_multiple": false}, "medicine_usage_type": {"classifier_id": 2000000250, "column": "value_reference_id", "is_select": true, "category_id": 10003300, "allow_multiple": true}, "medicine_withdrawl_days": {"classifier_id": 2000000252, "column": "value_int", "allow_multiple": false}}}, "PARTNER_CLASSIFICATION": {"TABLE": "partner", "DB": "main", "CLASSIFICATION_TABLE": "partner_classification", "CLASSIFICATION_TABLE_PRIMARY_KEY": "partner_classification_id", "CLASSIFICATION_TABLE_ENTITY_COLUMN": "partner_id", "ATTRIBUTE_LIST": {"partner_primary_contact_person_name": {"classifier_id": **********, "column": "value_l10n", "allow_multiple": false}, "partner_address": {"classifier_id": **********, "column": "value_l10n", "allow_multiple": false}, "pharmacy_distribution_license_number": {"classifier_id": **********, "column": "value_string_256", "is_select": true, "allow_multiple": false}, "partner_gst": {"classifier_id": **********, "column": "value_string_256", "allow_multiple": false}, "partner_pan": {"classifier_id": **********, "column": "value_string_256", "allow_multiple": false}, "partner_gst_code": {"classifier_id": **********, "column": "value_int", "allow_multiple": false}, "partner_gst_state": {"classifier_id": **********, "column": "value_string_256", "allow_multiple": false}, "partner_invoice_sales_person": {"classifier_id": **********, "column": "value_l10n", "allow_multiple": false}, "partner_operational_village_id": {"classifier_id": **********, "column": "value_reference_id", "is_select": true, "allow_multiple": false}, "bco_location_position": {"classifier_id": **********, "column": "value_json", "allow_multiple": false}, "partner_zoho_id": {"classifier_id": **********, "column": "value_string_256", "allow_multiple": false}}}, "REQUISITION_CLASSIFICATION": {"TABLE": "requisition", "DB": "main", "CLASSIFICATION_TABLE": "requisition_classification", "CLASSIFICATION_TABLE_PRIMARY_KEY": "requisition_classification_id", "CLASSIFICATION_TABLE_ENTITY_COLUMN": "requisition_id", "ATTRIBUTE_LIST": {"pharmacy_invoice_number": {"classifier_id": 2000000278, "column": "value_string_256", "allow_multiple": false}, "zoho_bill_id": {"classifier_id": 2000000279, "column": "value_string_256", "is_select": true, "allow_multiple": false}}}, "ASSET_FEATURE_CLASSIFICATION": {"TABLE": "asset_feature", "DB": "main", "CLASSIFICATION_TABLE": "asset_feature_classification", "CLASSIFICATION_TABLE_PRIMARY_KEY": "asset_feature_classification_id", "CLASSIFICATION_TABLE_ENTITY_COLUMN": "asset_feature_id", "ATTRIBUTE_LIST": {"smart_ration_weight": {"classifier_id": 2000000402, "column": "value_double", "allow_multiple": false}, "smart_ration_cost": {"classifier_id": 2000000403, "column": "value_double", "allow_multiple": false}, "smart_ration_price_per_kg": {"classifier_id": 2000000404, "column": "value_double", "allow_multiple": false}}}, "REF_ACTIVITY_CLASSIFICATION": {"TABLE": "ref_activity", "DB": "main", "CLASSIFICATION_TABLE": "ref_activity_classification", "CLASSIFICATION_TABLE_PRIMARY_KEY": "activity_classification_id", "CLASSIFICATION_TABLE_ENTITY_COLUMN": "activity_id", "ATTRIBUTE_LIST": {"activity_form_config": {"classifier_id": 2000000424, "column": "value_json", "allow_multiple": false}}}}}