#This stage builds the code using node 16 image 
FROM krushaloc/node:*********-alpine as build-stage

ARG GITLAB_AUTH_TOKEN
ENV GITLAB_AUTH_TOKEN=$GITLAB_AUTH_TOKEN

WORKDIR /app/web_app

COPY .env .
COPY .npmrc .
COPY package.json .

RUN npm install --legacy-peer-deps

COPY . /app/web_app

RUN npm run build

#This stage creates an empty layer (scratch image) and copies files from build stage into root folder
#By enabling DOCKER_BUILDKIT=1 and running docker build with --output type=local, files will be moved to host machine

FROM scratch AS export-artifacts 
COPY --from=build-stage /app/web_app/build /
