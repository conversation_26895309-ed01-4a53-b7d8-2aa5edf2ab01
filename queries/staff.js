const { dbConnections } = require("@krushal-it/ah-orm");
const {  postProcessRecords } = require('@krushal-it/ah-orm');
const { ACTIVE } = require("../utils/constant");

const q_getStaffByCustomer = (options = {}) => {
    const { customer_id, staff_type } = options;
    const base_query = `
    SELECT 
    s.staff_id,
    s.staff_name_l10n,
    s.mobile_number,
    pd.document_id AS staff_document,
    qr.document_id AS ro_qr_code
    FROM main.staff AS s

    JOIN main.entity_geography as eg ON eg.entity_type_id = ${staff_type}
     AND eg.entity_uuid = s.staff_id
     AND eg.geography_type_id = 1000320004
     AND eg.active = 1000100001
    
    JOIN main.customer_classification AS cv ON cv.classifier_id = 2000000055
        AND cv.value_reference_id = eg.geography_id
        AND cv.customer_id = '${customer_id}'
        AND cv.active = 1000100001
    
    LEFT JOIN (
        SELECT entity_1_entity_uuid,max(created_at) as created_at
        FROM main.document
        WHERE document_type_id=1000260004  AND document.active = 1000100001
        GROUP BY entity_1_entity_uuid
        ) as pmdoc on pmdoc.entity_1_entity_uuid = s.staff_id
    
    LEFT JOIN main.document as pd ON pd.entity_1_entity_uuid = pmdoc.entity_1_entity_uuid 
        AND pd.entity_1_entity_uuid = s.staff_id 
        AND pd.created_at = pmdoc.created_at
        AND pd.active = 1000100001
        LEFT JOIN (
            SELECT entity_1_entity_uuid,max(created_at) as created_at
            FROM main.document
            WHERE document_type_id=1000260009  AND document.active = 1000100001
            GROUP BY entity_1_entity_uuid
            ) as qrdoc on qrdoc.entity_1_entity_uuid = s.staff_id
        
        LEFT JOIN main.document as qr ON qr.entity_1_entity_uuid = qrdoc.entity_1_entity_uuid 
            AND qr.entity_1_entity_uuid = s.staff_id 
            AND qr.created_at = qrdoc.created_at
            AND qr.active = 1000100001
        LIMIT 1
    `;
    return base_query;
};

const q_getNonGeoStaffByCustomer = (options = {}) => {
  const { customer_id, staff_type } = options;
  const base_query= `
    SELECT STAFF.STAFF_ID, STAFF.STAFF_NAME_L10N, STAFF.EMAIL_ADDRESS, STAFF.MOBILE_NUMBER, DOCUMENT.DOCUMENT_ID,  DOCUMENT.DOCUMENT_INFORMATION
    FROM MAIN.ENTITY_RELATIONSHIP ER
    LEFT JOIN MAIN.STAFF ON STAFF.STAFF_ID=ER.ENTITY_1_ENTITY_UUID
    LEFT JOIN MAIN.DOCUMENT ON DOCUMENT.ENTITY_1_TYPE_ID='${staff_type}'
    WHERE ER.ENTITY_RELATIONSHIP_TYPE_ID = 1000210010
      AND ER.ENTITY_2_ENTITY_UUID = '${customer_id}'
      AND ER.ACTIVE = 1000100001
    `

  return base_query
}
const getStaffList=async(options = {})=> {
    const manager = options.manager ? options.manager : dbConnections().main.manager;
    let params = [];
    let paramIndex = 1;
    // Joining staff type
    let base_query = `SELECT staff.*,ref_reference.reference_name AS staff_type_name,
    ref_reference.reference_name_l10n 
    FROM main.staff  JOIN main.ref_reference ON ref_reference.reference_id = staff.staff_type_id
    WHERE ref_reference.reference_category_id = 10002300`;
    // Filter staff_type
    if (options.f_staff_type) {
      base_query = `SELECT staff.* FROM (${base_query}) AS staff WHERE staff.staff_type_id = $${paramIndex}`;
      paramIndex++;
      params.push(options.f_staff_type);
    }
    let data = await manager.query(base_query, params);
    return data;
  }
//getting staffs based on staff type Ids
const getStaffListByTypeId = async(options = {})=> {
  //f_user_geo_filter , village_ids
  const manager = options.manager ? options.manager : dbConnections().main.manager;
  let staff_ids= []
  if(options.f_user_geo_filter  ) {
    let getStaffFromVillageQuery = await  manager.query(`
      select distinct entity_uuid from main.entity_geography where geography_id in (select geography_id from main.entity_geography where entity_uuid= '${options.user_id}') and active = ${ACTIVE}
    `)
    staff_ids = getStaffFromVillageQuery.map(entity => `'${entity.entity_uuid}'`)
  }

  let base_query = `SELECT staff.staff_id,staff.staff_type_id,staff.active,ref_reference.reference_name AS staff_type_name,
  staff.staff_name_l10n
  FROM main.staff  JOIN main.ref_reference ON ref_reference.reference_id = staff.staff_type_id
  WHERE ref_reference.reference_category_id = 10002300 and staff.active=1000100001`;
  // Filter staff_types
  if (options.f_user_geo_filter) {
    if(!staff_ids.length) return []
    base_query += ` AND staff.staff_id in (${staff_ids}) `
  }
  if (options.f_staff_type.length) {
    base_query = `SELECT staff.* FROM (${base_query}) AS staff WHERE staff.staff_type_id IN (${options.f_staff_type}) `;
  }

  let data = await manager.query(base_query);
  postProcessRecords(undefined, data, { json_columns: ['staff_name_l10n'] })
  return data;
}

module.exports = { q_getStaffByCustomer, q_getNonGeoStaffByCustomer, getStaffList, getStaffListByTypeId };