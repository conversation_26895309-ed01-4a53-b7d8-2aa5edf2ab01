const { configurationJSON } = require('@krushal-it/common-core')
const { dbConnections, preProcessRecords } = require('@krushal-it/ah-orm')
const { validateUUID } = require('@krushal-it/mobile-or-server-lib')
const uuid = require('uuid')

const q_getCardTotalROZO_v2 = (options = {}, days) => {
  const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
  const numberOfDaysToAddToCurrentDate = days || 0
  const staff_filter = `inner join main.entity_geography ueg on (
    (
      /*param here logging in users staff_id*/
      (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet */)
      or
      /*param here logging in users staff_id*/
      (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
      /*param here logging in users staff_id - */
      or
      (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as zo */)
      or
      (ueg.entity_type_id = 1000230003 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as vet */)
    )
    and
    ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
    and ueg.active = 1000100001
  )`

  base_query = `
  /* Left till today */
  select dba.care_category as care_category, count(dba.care_calendar_id) as count from (
  select cc.care_calendar_id, at_rr.activity_category, case when at_rr.activity_category = 1000270001 then 'Preventive' when at_rr.activity_category = 1000270002 then 'Critical' when at_rr.activity_category = 1000270003 then 'Critical' end as care_category
  from main.care_calendar cc
  inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
    and er.entity_relationship_type_id = 1000210004
  inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
    and ccl.classifier_id = 2000000055
  inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
    and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
    and eg.active = 1000100001
    ${options.user_type === 1000230002 ? '' : staff_filter}
  inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
  join main.ref_reference ar on ar.reference_id = at_rr.activity_category
  where cc.entity_type_id = 1000460002 /*animal*/
  /*param here for number of future days*/
  and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
  ${isPostgres ? ' and cc.activity_date::DATE <= current_date + ' + numberOfDaysToAddToCurrentDate + ' ' : " and cc.activity_date <= date('now', 'start of day', '+" + numberOfDaysToAddToCurrentDate + " day')"}
) dba
group by dba.care_category`
  return base_query
}

const q_getTotalFarmerROZO_v2 = (options = {}, days) => {
  const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
  const numberOfDaysToAddToCurrentDate = days || 0
  const staff_filter = `  inner join main.entity_geography ueg on (
        (
        /*param here logging in users staff_id*/
        (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet */)
        or
        /*param here logging in users staff_id*/
        (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
        /*param here logging in users staff_id - */
        or
        (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
        or
        (ueg.entity_type_id = 1000230003 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as vet */)
        )
        and
        ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
        and ueg.active = 1000100001
    )`
  let base_query = `
    select count(farmer_id) as today_farmers
    from (
    select er.entity_1_entity_uuid as farmer_id, count(cc.care_calendar_id) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
        and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
        and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
        and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
        and eg.active = 1000100001
        ${options.user_type === 1000230002 ? '' : staff_filter}
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    ${isPostgres ? ' and cc.activity_date::DATE <= current_date + ' + numberOfDaysToAddToCurrentDate + ' ' : " and cc.activity_date <= date('now', 'start of day', '+" + numberOfDaysToAddToCurrentDate + " day')"}
    group by er.entity_1_entity_uuid
) dbf`
  return base_query
}

const q_getROCardByGroupByParavet_v2 = (options = {}) => {
  const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
  const staff_filter = ` inner join main.entity_geography ueg on (
    (
      /*param here logging in users staff_id*/
      (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
      or
      (ueg.entity_type_id = 1000230003 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as vet */)
    )
    and
    ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
    and ueg.active = 1000100001
  )`
  base_query = `select s.staff_id,
  s.staff_name_l10n,
lss_doc.document_id,
lss_doc.document_information,
lss_doc.client_document_information,
dba.care_category,
task_range,
count 
from (
  select dbca.staff_id, dbca.task_range, dbca.care_category, count(dbca.care_calendar_id) as count
  from (
    select 'till_today' as task_range, cc.care_calendar_id, eg.entity_uuid as staff_id, 
    case when at_rr.activity_category = 1000270001 then 'Preventive' 
    when at_rr.activity_category = 1000270002 then 'Critical' 
    when at_rr.activity_category = 1000270003 then 'Critical' end as care_category
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
      and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
      and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
      and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
      and eg.active = 1000100001
--     left join main.staff s on s.staff_id = eg.entity_uuid and s.staff_type_id = 1000230004
       ${options.user_type === 1000230002 ? '' : staff_filter}
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    left join main.animal on cc.entity_uuid = animal.animal_id      
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    ${isPostgres ? ' and cc.activity_date::DATE <= current_date ' : " and cc.activity_date <= date('now', 'start of day') "}
    and animal.active = 1000100001

    union all
    
    select 'till_next_15_days' as task_range, cc.care_calendar_id, eg.entity_uuid as staff_id, 
	case when at_rr.activity_category = 1000270001 then 'Preventive' 
	  	 when at_rr.activity_category = 1000270002 then 'Critical' 
	  	 when at_rr.activity_category = 1000270003 then 'Critical' end as care_category
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
      and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
      and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
      and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
      and eg.active = 1000100001
--     left join main.staff s on s.staff_id = eg.entity_uuid and s.staff_type_id = 1000230004
       ${options.user_type === 1000230002 ? '' : staff_filter}
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    left join main.animal on cc.entity_uuid = animal.animal_id
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    ${isPostgres ? ' and cc.activity_date::DATE <= current_date + 15 ' : " and cc.activity_date <= date('now', 'start of day', '+15 day') "}
    and animal.active = 1000100001
  ) dbca
  group by dbca.staff_id, dbca.task_range, dbca.care_category
) dba
 left join main.staff s on s.staff_id = dba.staff_id
 left join (
        select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
        from main.document d
        where d.document_type_id = 1000260004
        group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
        ) as mdoc
        on mdoc.entity_1_entity_uuid = s.staff_id and mdoc.entity_1_type_id = 1000230004 
        left join main.document as lss_doc  
        on mdoc.entity_1_entity_uuid = lss_doc.entity_1_entity_uuid
        and lss_doc.entity_1_entity_uuid = s.staff_id
        and lss_doc.created_at = mdoc.max_created_at
`
  return base_query
}

const q_FarmerCountGroupByParavet_v2 = (options = {}) => {
  const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
  const staff_filter = `inner join main.entity_geography ueg on (
    (
    /*param here logging in users staff_id*/
    (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet */)
    or
    /*param here logging in users staff_id*/
    (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
    /*param here logging in users staff_id - */
    or
    (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
    or
    (ueg.entity_type_id = 1000230003 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as vet */)
    )
    and
    ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
    and ueg.active = 1000100001
)`
  let base_query = `select
    'total_left' as category,
    s.staff_id,
    s.staff_name_l10n,
    lss_doc.document_id, 
    lss_doc.document_information,
    lss_doc.client_document_information,
    count(farmer_id) as today_farmers
    from (
    select eg.entity_uuid as staff_id,er.entity_1_entity_uuid as farmer_id, count(cc.care_calendar_id)
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
        and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
        and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
        and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
        and eg.active = 1000100001
        ${options.user_type === 1000230002 ? '' : staff_filter}
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    left join main.animal on cc.entity_uuid = animal.animal_id      
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    ${isPostgres ? ' and cc.activity_date::DATE <= current_date + 0 ' : " and cc.activity_date <= date('now', 'start of day', '+1 day') "}
    and animal.active = 1000100001
    group by er.entity_1_entity_uuid,eg.entity_uuid
    ) dbf
    left join main.staff s on s.staff_id = dbf.staff_id
    left join (
            select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
            from main.document d
            where d.document_type_id = 1000260004
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            ) as mdoc
            on mdoc.entity_1_entity_uuid = s.staff_id and mdoc.entity_1_type_id = 1000230004 
            left join main.document as lss_doc  
            on mdoc.entity_1_entity_uuid = lss_doc.entity_1_entity_uuid
            and lss_doc.entity_1_entity_uuid = s.staff_id
            and lss_doc.created_at = mdoc.max_created_at
    group by s.staff_id,lss_doc.document_id

    union all

    select
    'total' as category,
    s.staff_id,
    s.staff_name_l10n,
    lss_doc.document_id, 
    lss_doc.document_information,
    lss_doc.client_document_information,
    count(farmer_id) as today_farmers
    from (
    select eg.entity_uuid as staff_id,er.entity_1_entity_uuid as farmer_id, count(cc.care_calendar_id) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
        and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
        and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
        and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
        and eg.active = 1000100001
        ${options.user_type === 1000230002 ? '' : staff_filter}

    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    left join main.animal on cc.entity_uuid = animal.animal_id      
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    --   and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    ${isPostgres ? ' and cc.activity_date::DATE <= current_date + 15 ' : " and cc.activity_date <= date('now', 'start of day', '+15 day') "}
    and animal.active = 1000100001
    group by er.entity_1_entity_uuid,eg.entity_uuid
    ) dbf
    left join main.staff s on s.staff_id = dbf.staff_id
    left join (
            select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
            from main.document d
            where d.document_type_id = 1000260004
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            ) as mdoc
            on mdoc.entity_1_entity_uuid = s.staff_id and mdoc.entity_1_type_id = 1000230004 
            left join main.document as lss_doc  
            on mdoc.entity_1_entity_uuid = lss_doc.entity_1_entity_uuid
            and lss_doc.entity_1_entity_uuid = s.staff_id
            and lss_doc.created_at = mdoc.max_created_at
        group by s.staff_id,lss_doc.document_id
    `
  return base_query
}

const q_getZOCardByGroupByRO_v2 = (options = {}) => {
  let base_query = `
  select s.staff_id,
  s.staff_name_l10n,
  lss_doc.document_id,
  dba.care_category,
  task_range,
  count 
  from (
    select dbca.staff_id, dbca.task_range, dbca.care_category, count(dbca.care_calendar_id) as count
    from (
      select 'till_today' as task_range, cc.care_calendar_id, ro_eg.entity_uuid as staff_id, 
      case when at_rr.activity_category = 1000270001 then 'Preventive' 
      when at_rr.activity_category = 1000270002 then 'Critical' 
      when at_rr.activity_category = 1000270003 then 'Critical' end as care_category
      from main.care_calendar cc
      inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
        and er.entity_relationship_type_id = 1000210004
      inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
        and ccl.classifier_id = 2000000055
    /*Joining Route officer*/  
    inner join main.entity_geography ro_eg on ro_eg.geography_id = ccl.value_reference_id
      and ro_eg.entity_type_id = 1000230001 and ro_eg.geography_type_id = 1000320004
      and ro_eg.active = 1000100001
    /*Joining Zonal officer*/
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
        and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230005
        and eg.active = 1000100001
      and eg.entity_uuid = '${options.user_id}' /* ZO id goes here */
      inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
      join main.ref_reference ar on ar.reference_id = at_rr.activity_category
      where cc.entity_type_id = 1000460002 /*animal*/
      /*param here for number of future days*/
      and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
      and cc.activity_date::DATE <= current_date + 0
    
      union all 
      
      select 'till_next_15_days' as task_range, cc.care_calendar_id, ro_eg.entity_uuid as staff_id, 
      case when at_rr.activity_category = 1000270001 then 'Preventive' 
      when at_rr.activity_category = 1000270002 then 'Critical' 
      when at_rr.activity_category = 1000270003 then 'Critical' end as care_category
      from main.care_calendar cc
      inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
        and er.entity_relationship_type_id = 1000210004
      inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
        and ccl.classifier_id = 2000000055
    /*Joining Route officer*/  
    inner join main.entity_geography ro_eg on ro_eg.geography_id = ccl.value_reference_id
      and ro_eg.entity_type_id = 1000230001 and ro_eg.geography_type_id = 1000320004
      and ro_eg.active = 1000100001
    /*Joining Zonal officer*/
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
        and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230005
        and eg.active = 1000100001
      and eg.entity_uuid = '${options.user_id}' /* ZO id goes here */
      inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
      join main.ref_reference ar on ar.reference_id = at_rr.activity_category
      where cc.entity_type_id = 1000460002 /*animal*/
      /*param here for number of future days*/
      and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
      and cc.activity_date::DATE <= current_date + 15
    ) as dbca
    group by dbca.staff_id, dbca.care_category, dbca.task_range) as dba
    left join main.staff s on s.staff_id = dba.staff_id
    left join (
          select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
          from main.document d
          where d.document_type_id = 1000260004
          group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          ) as mdoc
          on mdoc.entity_1_entity_uuid = s.staff_id and mdoc.entity_1_type_id = 1000230001
          left join main.document as lss_doc  
          on mdoc.entity_1_entity_uuid = lss_doc.entity_1_entity_uuid
          and lss_doc.entity_1_entity_uuid = s.staff_id
          and lss_doc.created_at = mdoc.max_created_at
    `
  return base_query
}

const q_FarmerCountGroupByRo_v2 = (options = {}) => {
  let base_query = `select
    'total_left' as category,
    s.staff_id,
    s.staff_name_l10n,
    lss_doc.document_id, 
    count(farmer_id) as today_farmers
    from (
    select ro_eg.entity_uuid as staff_id,er.entity_1_entity_uuid as farmer_id, count(cc.care_calendar_id) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
        and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
        and ccl.classifier_id = 2000000055
    inner join main.entity_geography ro_eg on ccl.value_reference_id = ro_eg.geography_id
        and ro_eg.geography_type_id = 1000320004 and ro_eg.entity_type_id = 1000230001
        and ro_eg.active = 1000100001
   	inner join main.entity_geography as eg on eg.geography_id = ro_eg.geography_id 
		and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230005
    and eg.active = 1000100001
		and eg.entity_uuid = '${options.user_id}'
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    and cc.activity_date::DATE <= current_date + 0 
    group by er.entity_1_entity_uuid,ro_eg.entity_uuid
    ) dbf
    left join main.staff s on s.staff_id = dbf.staff_id
    left join (
            select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
            from main.document d
            where d.document_type_id = 1000260004
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            ) as mdoc
            on mdoc.entity_1_entity_uuid = s.staff_id and mdoc.entity_1_type_id = 1000230001
            left join main.document as lss_doc  
            on mdoc.entity_1_entity_uuid = lss_doc.entity_1_entity_uuid
            and lss_doc.entity_1_entity_uuid = s.staff_id
            and lss_doc.created_at = mdoc.max_created_at
    group by s.staff_id,lss_doc.document_id
	
	union all
	
	select
    'total' as category,
    s.staff_id,
    s.staff_name_l10n,
    lss_doc.document_id, 
    count(farmer_id) as today_farmers
    from (
    select ro_eg.entity_uuid as staff_id,er.entity_1_entity_uuid as farmer_id, count(cc.care_calendar_id) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
        and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
        and ccl.classifier_id = 2000000055
    inner join main.entity_geography ro_eg on ccl.value_reference_id = ro_eg.geography_id
        and ro_eg.geography_type_id = 1000320004 and ro_eg.entity_type_id = 1000230001
        and ro_eg.active = 1000100001
   	inner join main.entity_geography as eg on eg.geography_id = ro_eg.geography_id 
		and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230005
    and eg.active = 1000100001
		and eg.entity_uuid = '${options.user_id}'
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.activity_date::DATE <= current_date + 0 
    group by er.entity_1_entity_uuid,ro_eg.entity_uuid
    ) dbf
    left join main.staff s on s.staff_id = dbf.staff_id
    left join (
            select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
            from main.document d
            where d.document_type_id = 1000260004
            group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
            ) as mdoc
            on mdoc.entity_1_entity_uuid = s.staff_id and mdoc.entity_1_type_id = 1000230001
            left join main.document as lss_doc  
            on mdoc.entity_1_entity_uuid = lss_doc.entity_1_entity_uuid
            and lss_doc.entity_1_entity_uuid = s.staff_id
            and lss_doc.created_at = mdoc.max_created_at
    group by s.staff_id,lss_doc.document_id`
  return base_query
}
//done
const q_getActivityById = (options = {}) => {
  const { activity_id, user_id, user_type } = options
  let authJoin = `JOIN main.entity_geography as ueg on ueg.entity_type_id = ${user_type} 
  and ueg.geography_type_id = 1000320004 and ueg.geography_id = vg.village_id 
  and ueg.active = 1000100001
  and ueg.entity_uuid = '${user_id}'`

  let base_query = `
  SELECT
  cc.activity_id,
  cc.entity_uuid,
  cc.entity_type_id,
  cc.activity_date,
  cc.care_calendar_id,
  cc.calendar_activity_status,
  tn.value_string_256 as ticket_number,
  acat.activity_name_l10n,
  acat.activity_category,
  a.animal_visual_id,
  etag.value_string_256 as ear_tag,
  atype.reference_name_l10n::text as animal_type_json,
 
  aname.value_string_256 as animal_name,
   astatus.reference_name_l10n as calendar_activity_status_name_json,
  c.customer_id,
  c.customer_name_l10n,
  c.customer_visual_id,
  c.mobile_number AS farmer_contact,
  vg.village_name_l10n,
  COALESCE(staff2.staff_id,staff.staff_id)AS staff_id,
  COALESCE(staff2.staff_name_l10n,staff.staff_name_l10n) as paravet_name,
  COALESCE(staff2.mobile_number,staff.mobile_number) as pravet_mobile_number,
  fd.document_id as farmer_document_id,
  COALESCE(pd2.document_id,pd.document_id) as paravet_document_id,
  ad.document_id as animal_document_id
  FROM main.care_calendar as cc
  LEFT JOIN main.care_calendar_classification as tn ON tn.care_calendar_id = cc.care_calendar_id and tn.classifier_id = 2000000125 and 
tn.active = 1000100001
  JOIN main.ref_reference as astatus on astatus.reference_id = cc.calendar_activity_status and astatus.active = 1000100001
  JOIN main.ref_activity as acat on acat.activity_id = cc.activity_id and acat.active = 1000100001
  JOIN main.animal as a on a.animal_id = cc.entity_uuid and cc.entity_type_id = 1000460002 and a.active = 1000100001
  LEFT JOIN main.animal_classification as ac_animal_type on ac_animal_type.animal_id = a.animal_id and ac_animal_type.classifier_id = 2000000035 and ac_animal_type.active = 1000100001
  JOIN main.ref_reference as atype on atype.reference_id = ac_animal_type.value_reference_id and atype.active = 1000100001
  LEFT JOIN main.animal_classification as etag on etag.animal_id = a.animal_id and etag.classifier_id = 2000000034 and etag.active = 1000100001
  LEFT JOIN main.animal_classification as aname on aname.animal_id = a.animal_id and aname.classifier_id = 2000000033
  JOIN main.entity_relationship as er on er.entity_relationship_type_id = 1000210004 and er.entity_2_entity_uuid = a.animal_id and er.active = 1000100001
  JOIN main.customer as c on c.customer_id = er.entity_1_entity_uuid and c.active = 1000100001
  JOIN main.customer_classification as cv on cv.classifier_id = 2000000055 and cv.customer_id = c.customer_id and cv.active = 1000100001
  JOIN main.ref_village as vg on vg.village_id = cv.value_reference_id
  LEFT JOIN main.entity_geography as eg on eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004 and eg.geography_id = vg.village_id and eg.active = 1000100001
  LEFT JOIN main.staff_activity AS sa ON sa.care_calendar = cc.care_calendar_id
  LEFT JOIN main.staff as staff2 on staff2.staff_id = sa.staff_id
  LEFT JOIN main.staff on staff.staff_id = eg.entity_uuid and staff.active = 1000100001
  LEFT JOIN (
    SELECT entity_1_entity_uuid,max(created_at) as created_at
    FROM main.document
    WHERE document_type_id=1000260003
    GROUP BY entity_1_entity_uuid
  ) as fmdoc on fmdoc.entity_1_entity_uuid = c.customer_id
  LEFT JOIN main.document as fd on fd.created_at = fmdoc.created_at and fd.entity_1_entity_uuid = c.customer_id and fd.entity_1_entity_uuid = fmdoc.entity_1_entity_uuid
  LEFT JOIN (
    SELECT entity_1_entity_uuid,max(created_at) as created_at
    FROM main.document
    WHERE document_type_id=1000260004
    GROUP BY entity_1_entity_uuid
  ) as pmdoc on pmdoc.entity_1_entity_uuid = staff.staff_id
  LEFT JOIN main.document as pd on pd.entity_1_entity_uuid = pmdoc.entity_1_entity_uuid and pd.entity_1_entity_uuid = staff.staff_id and pd.created_at = pmdoc.created_at
  LEFT JOIN (
    SELECT entity_1_entity_uuid,max(created_at) as created_at
    FROM main.document
    WHERE document_type_id=1000260004
    GROUP BY entity_1_entity_uuid
  ) as pmdoc2 on pmdoc2.entity_1_entity_uuid = sa.staff_id and sa.staff_id is not null
  LEFT JOIN main.document as pd2 on pd2.entity_1_entity_uuid = pmdoc2.entity_1_entity_uuid and pd2.entity_1_entity_uuid = sa.staff_id and pd2.created_at = pmdoc2.created_at
  LEFT JOIN (
    SELECT entity_1_entity_uuid,max(created_at) as created_at
    FROM main.document
    WHERE document_type_id=1000260001
    GROUP BY entity_1_entity_uuid
  ) as amdoc on amdoc.entity_1_entity_uuid  = a.animal_id
  LEFT JOIN main.document as ad on ad.entity_1_entity_uuid = amdoc.entity_1_entity_uuid and ad.created_at = amdoc.created_at and ad.entity_1_entity_uuid = a.animal_id
  WHERE 1 = 1
  AND cc.care_calendar_id = '${activity_id}' 
 
  


  union all

  SELECT
    cc.activity_id,
    cc.entity_uuid,
  cc.entity_type_id,
  cc.activity_date,

    cc.care_calendar_id,
    cc.calendar_activity_status,
     tn.value_string_256 as ticket_number,
     acat.activity_name_l10n,
  acat.activity_category,
    null as animal_visual_id,
    null as ear_tag,
    null as animal_type_json,
    
    null as animal_name,
    astatus.reference_name_l10n as calendar_activity_status_name_json,
    
  c.customer_id,
  c.customer_name_l10n,
  c.customer_visual_id,
  c.mobile_number AS farmer_contact,
  vg.village_name_l10n,
  COALESCE(staff2.staff_id,staff.staff_id) AS staff_id,
  COALESCE(staff2.staff_name_l10n,staff.staff_name_l10n) as paravet_name,
  COALESCE(staff2.mobile_number,staff.mobile_number) as pravet_mobile_number,
  fd.document_id as farmer_document_id,
  COALESCE(pd2.document_id,pd.document_id) as paravet_document_id,
  null as animal_document_id
   FROM main.care_calendar as cc
  LEFT JOIN main.care_calendar_classification as tn ON tn.care_calendar_id = cc.care_calendar_id and tn.classifier_id = 2000000125 and 
tn.active = 1000100001
  JOIN main.ref_reference as astatus on astatus.reference_id = cc.calendar_activity_status and astatus.active = 1000100001
  JOIN main.ref_activity as acat on acat.activity_id = cc.activity_id and acat.active = 1000100001

  JOIN main.customer as c on c.customer_id = cc.entity_uuid and cc.entity_type_id = 1000460001 and c.active = 1000100001
  JOIN main.customer_classification as cv on cv.classifier_id = 2000000055 and cv.customer_id = c.customer_id and cv.active = 1000100001
  JOIN main.ref_village as vg on vg.village_id = cv.value_reference_id
  LEFT JOIN main.entity_geography as eg on eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004 and eg.geography_id = vg.village_id and eg.active = 1000100001
  LEFT JOIN main.staff_activity AS sa ON sa.care_calendar = cc.care_calendar_id
  LEFT JOIN main.staff as staff2 on staff2.staff_id = sa.staff_id
  LEFT JOIN main.staff on staff.staff_id = eg.entity_uuid and staff.active = 1000100001
  LEFT JOIN (
    SELECT entity_1_entity_uuid,max(created_at) as created_at
    FROM main.document
    WHERE document_type_id=1000260003
    GROUP BY entity_1_entity_uuid
  ) as fmdoc on fmdoc.entity_1_entity_uuid = c.customer_id
  LEFT JOIN main.document as fd on fd.created_at = fmdoc.created_at and fd.entity_1_entity_uuid = c.customer_id and fd.entity_1_entity_uuid = fmdoc.entity_1_entity_uuid
  LEFT JOIN (
    SELECT entity_1_entity_uuid,max(created_at) as created_at
    FROM main.document
    WHERE document_type_id=1000260004
    GROUP BY entity_1_entity_uuid
  ) as pmdoc on pmdoc.entity_1_entity_uuid = staff.staff_id
  LEFT JOIN main.document as pd on pd.entity_1_entity_uuid = pmdoc.entity_1_entity_uuid and pd.entity_1_entity_uuid = staff.staff_id and pd.created_at = pmdoc.created_at
  LEFT JOIN (
    SELECT entity_1_entity_uuid,max(created_at) as created_at
    FROM main.document
    WHERE document_type_id=1000260004
    GROUP BY entity_1_entity_uuid
  ) as pmdoc2 on pmdoc2.entity_1_entity_uuid = sa.staff_id and sa.staff_id is not null
  LEFT JOIN main.document as pd2 on pd2.entity_1_entity_uuid = pmdoc2.entity_1_entity_uuid and pd2.entity_1_entity_uuid = sa.staff_id and pd2.created_at = pmdoc2.created_at
  ${user_type !== 1000230002 && user_type !== 1000220001 ? authJoin : ''}
  WHERE 1 = 1
  AND cc.care_calendar_id = '${activity_id}' LIMIT 1

  
  
  
  
  `
  return base_query
}
//done
const q_activityConfig = (options = {}) => {
  const base_query = `
  SELECT 
  atype.reference_name_l10n AS category_json,
  atype.reference_id AS category_parent,
  atype.reference_name,
  ra.activity_name_l10n,
  ra.activity_id
  FROM main.ref_reference as atype
  JOIN main.ref_activity ra ON atype.reference_id = ra.activity_category
    AND ra.active = 1000100001
  WHERE atype.reference_id IN (1000270001,1000270002,1000270003)`
  return base_query
}

const q_getCareCalendar = (options = {}) => {
  const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
  const { user_id, user_type } = options
  let authJoin = `JOIN main.entity_geography as ueg on ueg.entity_type_id = ${user_type} 
  and ueg.geography_type_id = 1000320004 and ueg.geography_id = vg.village_id 
  and ueg.active = 1000100001
  and ueg.entity_uuid = '${user_id}'`

  const base_query = `
  SELECT
    ${isPostgres ? 'a.animal_id::TEXT,' : 'a.animal_id,'}  
   c.customer_id,
   cc.activity_date,
   cc.activity_id,
   cc.entity_type_id,
   cc.care_calendar_id,
   cc.calendar_activity_status,
   null as expiry_date,
   etag.value_string_256 as ear_tag,
   aname.value_string_256 as animal_name,
   ${isPostgres ? 'ad.document_id::TEXT as animal_document_id,' : 'ad.document_id as animal_document_id,'}
   ra.activity_name_l10n,
   ra.activity_category,
   ac.reference_name_l10n as activity_category_json,
   lss.staff_id as paravet_id,
   lss.staff_name_l10n as paravet_name,
   lss.mobile_number as paravel_mobile_number,
   pd.document_id as paravet_document_id
   FROM main.care_calendar AS cc
   JOIN
   main.entity_relationship AS er
   ON
   er.entity_relationship_type_id = 1000210004
     AND
     er.entity_2_entity_uuid = cc.entity_uuid
     AND
     er.active = 1000100001
    
   JOIN main.customer AS c ON c.customer_id = er.entity_1_entity_uuid
     AND c.active = 1000100001

   JOIN main.animal as a on a.animal_id= cc.entity_uuid
     AND a.active = 1000100001
   LEFT JOIN main.animal_classification as etag  on etag.animal_id = a.animal_id
     AND etag.classifier_id = 2000000034
     AND etag.active = 1000100001
   LEFT JOIN main.animal_classification as aname on aname.animal_id = a.animal_id
     AND aname.classifier_id = 2000000033
     AND aname.active = 1000100001


   JOIN main.customer_classification as vc ON vc.classifier_id = 2000000055
     AND vc.customer_id = c.customer_id
     AND vc.active = 1000100001
   JOIN main.ref_village AS vg on vg.village_id = vc.value_reference_id
     AND vg.active = 1000100001

   JOIN main.ref_activity as ra on ra.activity_id = cc.activity_id
     AND ra.active = 1000100001
   JOIN main.ref_reference AS ac on ac.reference_id = ra.activity_category
     AND ac.active = 1000100001

   LEFT JOIN main.entity_geography AS pgeo on pgeo.geography_id = vg.village_id
     AND pgeo.entity_type_id = 1000230004
     AND pgeo.geography_type_id = 1000320004
     AND pgeo.active = 1000100001
   LEFT JOIN main.staff AS lss ON lss.staff_id = pgeo.entity_uuid
     AND  lss.active = 1000100001
   	LEFT JOIN main.document as pd 
		ON
			pd.document_id = (
        		SELECT 
					document_id
        		FROM 
					main.document
        		WHERE 
					document_type_id=1000260004 
					AND
					document.entity_1_entity_uuid  = lss.staff_id
				LIMIT 1
    	) 
	LEFT JOIN main.document as ad 
		ON
			ad.document_id = (
        		SELECT 
					document_id
        		FROM 
					main.document
        		WHERE 
					document_type_id=1000260001 
					AND
					document.entity_1_entity_uuid  = a.animal_id
				LIMIT 1
    	) 
   ${user_type !== 1000230002 && user_type !== 1000220001 ? authJoin : ''}
    WHERE 1 = 1
    ${user_type === 1000220001 ? `AND c.customer_id = '${user_id}'` : ''}
 union all

   SELECT
   NULL as animal_id,
   cc.entity_uuid as customer_id,
   cc.activity_date,
   cc.activity_id,
cc.entity_type_id,
   cc.care_calendar_id,
   cc.calendar_activity_status,
   ccc.value_date as expiry_date,
   null as ear_tag,
   null as animal_name,
   null as animal_document_id,
   ra.activity_name_l10n,
   ra.activity_category,
   ac.reference_name_l10n as activity_category_json,
   lss.staff_id as paravet_id,
   lss.staff_name_l10n as paravet_name,
   lss.mobile_number as paravel_mobile_number,
   pd.document_id as paravet_document_id
   FROM main.care_calendar AS cc
   JOIN main.customer_classification as vc
   ON vc.classifier_id = 2000000055
     AND vc.customer_id = cc.entity_uuid
     AND vc.active = 1000100001
     LEFT JOIN main.care_calendar_classification as ccc 
on cc.care_calendar_id = ccc.care_calendar_id
AND ccc.classifier_id = 2000000139
 AND ccc.active = 1000100001
   JOIN main.ref_village AS vg on vg.village_id = vc.value_reference_id
     AND vg.active = 1000100001
   JOIN main.ref_activity as ra on ra.activity_id = cc.activity_id
     AND ra.active = 1000100001
   JOIN main.ref_reference AS ac on ac.reference_id = ra.activity_category
     AND ac.active = 1000100001
   LEFT JOIN main.entity_geography AS pgeo on pgeo.geography_id = vg.village_id
     AND pgeo.entity_type_id = 1000230004
     AND pgeo.geography_type_id = 1000320004
     AND pgeo.active = 1000100001
   LEFT JOIN main.staff AS lss ON lss.staff_id = pgeo.entity_uuid
     AND  lss.active = 1000100001
     LEFT JOIN main.document as pd 
     ON
       pd.document_id = (
          SELECT 
            document_id
          FROM 
            main.document
          WHERE 
            document_type_id=1000260004 
            AND
            document.entity_1_entity_uuid  = lss.staff_id
            AND
            document.active = 1000100001
          LIMIT 1
       ) 
     ${user_type !== 1000230002 && user_type !== 1000220001 ? authJoin : ''}
     WHERE 1 = 1
     AND
     cc.entity_type_id = 1000460001
     ${user_type === 1000220001 ? `AND cc.entity_uuid = '${user_id}'` : ''}
    
  `
  return base_query
}
const insertStaffActivity = async (values, datasource) => {
  let mdatasource = datasource ? datasource : dbConnections().main.manager
  const entityStaffActivity = dbConnections().main.entities['staff_activity']
  const preProcessedValues = preProcessRecords(entityStaffActivity, values, entityStaffActivity.additionalAttributes)
  await mdatasource.createQueryBuilder().insert().into('staff_activity').values(preProcessedValues).execute()
}

const q_getPropagatedAnimals = (options = {}) => {
  const base_query = `
  SELECT
  a.animal_visual_id,
  etag.value_string_256 as eartag,
  atr.reference_name_l10n
  FROM
  main.care_calendar as cc
  JOIN main.care_calendar_classification as lnk ON
    cc.care_calendar_id = lnk.care_calendar_id
    AND lnk.classifier_id = 2000000126
    AND lnk.active = 1000100001
    AND lnk.value_reference_uuid = '${options.farmerCalendarId}'
    AND cc.entity_type_id = 1000460002

  JOIN main.animal as a ON
    a.animal_id  = cc.entity_uuid
    AND a.active = 1000100001

  JOIN main.animal_classification as etag ON
    etag.classifier_id = 2000000034
    AND etag.animal_id = a.animal_id
    AND etag.active = 1000100001

  JOIN main.animal_classification as atype ON
    atype.classifier_id = 2000000035
    AND atype.animal_id = a.animal_id
    AND atype.active = 1000100001

  JOIN main.ref_reference AS atr ON
   atr.reference_id = atype.value_reference_id
   AND atr.active = 1000100001
  `
  return base_query
}

const q_getLinkedTask = (parentID) => {
  // if (!uuid.validate(parentID)) {
  if (!validateUUID(parentID)) {
    throw new Error('parentID must be an uuid')
  }
  let base_query = `
  WITH child_tasks
    as (
    SELECT care_calendar_id FROM
      main.care_calendar_classification
      WHERE value_reference_uuid = '${parentID}'
      AND classifier_id = 2000000126
    )
  SELECT
      c.visit_schedule_time,
    c.care_calendar_id, 
    st.reference_id as activity_status_id,
    rat.activity_name_l10n as activity_name_json,
    rat.activity_id,
    st.reference_name_l10n as activity_status_json,
    c.activity_date,
    c.completion_date 
    FROM main.care_calendar as c
    JOIN main.ref_activity as rat ON
    rat.activity_id = c.activity_id
    JOIN main.ref_reference as st ON
    st.reference_id = c.calendar_activity_status
    WHERE c.care_calendar_id IN (
      SELECT care_calendar_id FROM child_tasks
    ) OR c.care_calendar_id = '${parentID}'
    ORDER BY c.activity_date 
  `
  return base_query
}
const getFormCalendar = async (options = {}) => {
  const { user_id } = options
  const manager = options.manager ? options.manager : dbConnections().main.manager
  let base_query = ` select cc.care_calendar_id,
  cc.activity_date,
  cc.calendar_activity_status,
  cc.activity_id,
  ccl.value_date as expiry_date,
  c.customer_id
  from main.care_calendar as cc
  join main.customer as c on c.customer_id = cc.entity_uuid
  and c.customer_id = '${user_id}'
  join
  main.care_calendar_classification as ccl on cc.care_calendar_id = ccl.care_calendar_id 
  and ccl.classifier_id =2000000139
  and ccl.active = 1000100001
  where current_date between cc.activity_date::Date and ccl.value_date::Date
  and cc.entity_type_id =1000460001 and cc.activity_id = 1000700001
  and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
  ${options.care_calendar_id ? `and cc.care_calendar_id='${options.care_calendar_id}'` : ''}
  `
  let calendarForm = await manager.query(base_query)
  return calendarForm
}

const q_taskListing = (user_type, user_id) => {
  if (!user_id || !user_type) throw new Error('user_id and user type are required')
  const base_query = `
      WITH VALID_VILLAGES AS
    (
    select 
      distinct (geography_id) as village_id
    from main.entity_geography
      left join main.staff
        on 
          entity_geography.entity_uuid = staff.staff_id
    where
      entity_geography.geography_type_id = 1000320004
      AND entity_geography.active = 1000100001
      AND
      staff.staff_type_id = ${user_type == 1000230002 ? '1000230004' : user_type}
      ${user_type != 1000230002 ? ` AND staff.staff_id  = '${user_id}'` : ''}
    )
    SELECT
        CUSTOMER.customer_id AS customer_id,
        COALESCE(
          CUSTOMER.customer_name_l10n ->> 'en',
          CUSTOMER.customer_name_l10n ->> 'ul'
        ) AS customer_name,
        CUSTOMER.mobile_number AS mobile_number,
        CUSTOMER_CLASSIFICATION.value_reference_id AS village_id,
        COALESCE(
          REF_VILLAGE.village_name_l10n ->> 'en',
          REF_VILLAGE.village_name_l10n ->> 'ul'
        ) AS village_name,
        ANIMAL.animal_id AS animal_id,
        ANIMAL.animal_visual_id AS animal_visual_id,
        REF_REFERENCE.reference_name AS animal_type,
        REF_ANIMAL_EAR_TAG.value_string_256 AS animal_ear_tag,
        COALESCE(STAFF_ACTIVITY.staff_id, STAFF.staff_id) AS paravet_id,
        COALESCE(
          COALESCE(STAFF_OVERRIDE.staff_name_l10n, STAFF.staff_name_l10n) ->> 'en',
          COALESCE(STAFF_OVERRIDE.staff_name_l10n, STAFF.staff_name_l10n) ->> 'ul'
        ) AS paravet_name,
        COALESCE(STAFF_OVERRIDE.mobile_number, STAFF.mobile_number) AS paravet_mobile_number,
        CARE_CALENDAR.care_calendar_id AS care_calendar_id,
        CARE_CALENDAR.activity_date::DATE AS activity_date,
        CARE_CALENDAR.user_task_completion_time::DATE AS completion_date,
        CARE_CALENDAR.activity_id AS activity_id,
        REF_ACTIVITY.activity_name AS activity_name,
        REF_ACTIVITY.activity_category AS activity_category_id,
        REF_ACTIVITY_CATEGORY.reference_name AS activity_category_name,
        CARE_CALENDAR.calendar_activity_status AS calendar_activity_status,
        REF_ACTIVITY_STATUS.reference_name AS activity_status,
        CARE_CALENDAR_CLASSIFICATION.value_string_256 AS complaint_number
    FROM 
        main.CARE_CALENDAR
          LEFT JOIN 
            main.CARE_CALENDAR_CLASSIFICATION
              ON
                CARE_CALENDAR.care_calendar_id = CARE_CALENDAR_CLASSIFICATION.care_calendar_id
                AND
                CARE_CALENDAR_CLASSIFICATION.classifier_id = 2000000125
          LEFT JOIN 
            main.REF_ACTIVITY
              ON
                CARE_CALENDAR.activity_id = REF_ACTIVITY.activity_id
          LEFT JOIN 
            main.REF_REFERENCE REF_ACTIVITY_STATUS
              ON
                REF_ACTIVITY_STATUS.reference_id = CARE_CALENDAR.calendar_activity_status
          LEFT JOIN 
            main.REF_REFERENCE REF_ACTIVITY_CATEGORY
              ON
                REF_ACTIVITY_CATEGORY.reference_id = REF_ACTIVITY.activity_category
          LEFT JOIN 
            main.ENTITY_RELATIONSHIP 
              ON 
                ENTITY_RELATIONSHIP.entity_2_entity_uuid = CARE_CALENDAR.entity_uuid
                AND
                ENTITY_RELATIONSHIP.entity_relationship_type_id = 1000210004
                AND
                CARE_CALENDAR.entity_type_id = 1000460002
          LEFT JOIN 
            main.CUSTOMER
              ON 
                CUSTOMER.customer_id = ENTITY_RELATIONSHIP.entity_1_entity_uuid
                OR (
                  CUSTOMER.customer_id = CARE_CALENDAR.entity_uuid
                  AND
                  CARE_CALENDAR.entity_type_id = 1000460001
                )	
          LEFT JOIN 
            main.CUSTOMER_CLASSIFICATION 
              ON 
                CUSTOMER_CLASSIFICATION.customer_id = CUSTOMER.customer_id
                AND
                CUSTOMER_CLASSIFICATION.classifier_id = 2000000055
          LEFT JOIN 
            main.REF_VILLAGE
              ON
                REF_VILLAGE.village_id = CUSTOMER_CLASSIFICATION.value_reference_id
          LEFT JOIN 
            main.REF_TALUK
              ON
                REF_TALUK.taluk_id = REF_VILLAGE.taluk_id
          LEFT JOIN 
            main.REF_DISTRICT
              ON
                REF_DISTRICT.district_id = REF_TALUK.district_id
          LEFT JOIN 
            main.REF_STATE
              ON
                REF_STATE.state_id = REF_DISTRICT.state_id
          LEFT JOIN 
            main.ENTITY_GEOGRAPHY 
              ON 
                ENTITY_GEOGRAPHY.geography_id = REF_VILLAGE.village_id
                AND
                ENTITY_GEOGRAPHY.geography_type_id = 1000320004 
                AND
                ENTITY_GEOGRAPHY.entity_type_id = 1000230004
                AND
                ENTITY_GEOGRAPHY.active = 1000100001
          LEFT JOIN 
            main.STAFF
              ON
                STAFF.staff_id = ENTITY_GEOGRAPHY.entity_uuid
          LEFT JOIN 
            main.STAFF_ACTIVITY
              ON
                STAFF_ACTIVITY.care_calendar = CARE_CALENDAR.care_calendar_id
          LEFT JOIN 
            main.STAFF STAFF_OVERRIDE
              ON
                STAFF_OVERRIDE.staff_id = STAFF_ACTIVITY.staff_id
          LEFT JOIN 
            main.ANIMAL
              ON
                ANIMAL.animal_id = ENTITY_RELATIONSHIP.entity_2_entity_uuid
          LEFT JOIN 
            main.ANIMAL_CLASSIFICATION REF_ANIMAL_TYPE 
              ON 
                REF_ANIMAL_TYPE.animal_id = ANIMAL.animal_id
                AND 
                REF_ANIMAL_TYPE.classifier_id = 2000000035
          LEFT JOIN 
            main.REF_REFERENCE 
              ON 
                REF_REFERENCE.reference_id = REF_ANIMAL_TYPE.value_reference_id
          LEFT JOIN 
            main.ANIMAL_CLASSIFICATION REF_ANIMAL_NAME
              ON
                REF_ANIMAL_NAME.animal_id = ANIMAL.animal_id
                AND
                REF_ANIMAL_NAME.classifier_id = 2000000033
            LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_EAR_TAG
              ON
                REF_ANIMAL_EAR_TAG.animal_id = ANIMAL.animal_id
                AND
                REF_ANIMAL_EAR_TAG.classifier_id = 2000000034
    WHERE
      CARE_CALENDAR.active = 1000100001
      AND
      CUSTOMER.active = 1000100001
      AND
      (
        ANIMAL.active is null 
        OR 
        ANIMAL.active = 1000100001
      )
      AND CUSTOMER_CLASSIFICATION.value_reference_id IN (
        SELECT village_id FROM VALID_VILLAGES
      )	
  `
  return base_query
}
module.exports = {
  q_getCardTotalROZO_v2,
  q_getTotalFarmerROZO_v2,
  q_getROCardByGroupByParavet_v2,
  q_FarmerCountGroupByParavet_v2,
  q_getZOCardByGroupByRO_v2,
  q_FarmerCountGroupByRo_v2,
  q_getActivityById,
  q_activityConfig,
  q_getCareCalendar,
  q_getPropagatedAnimals,
  insertStaffActivity,
  q_getLinkedTask,
  q_taskListing,
  getFormCalendar,
}
