const {ACTIVE } = require("../../utils/constant");
/**
 * Typeorm-placeholders:
 * Configure these using setParameters method of type-orm as per use
 * 1. user_id
 * 2. relation_type
 */
module.exports = `
SELECT rv.village_id AS id, rv.village_name_l10n AS name_l10n
FROM main.staff st 
LEFT JOIN  main.entity_relationship as er ON   er.entity_1_entity_uuid = st.staff_id 
and er.entity_relationship_type_id = :relation_type 
LEFT JOIN  main.customer_classification as cuc on cuc.customer_id = er.entity_2_entity_uuid
and cuc.active = ${ACTIVE} and classifier_id = 2000000055
LEFT JOIN  main.ref_village as rv on rv.village_id = cuc.value_reference_id
WHERE 1=1 {{#ifEquals user_type 1000230006}} and  st.staff_id= :user_id      {{/ifEquals}}

GROUP BY id
`;
