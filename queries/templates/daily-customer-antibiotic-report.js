const { VILLAGE_TYPE_ID, ACTIVE, VILLAGE_CLASSIFIER, FARMER_TO_ANIMAL } = require("../../utils/constant");
const { classifiers, staff_type, record_statuses } = require("../../ENUMS");

module.exports = `
WITH VALID_VILLAGES AS (
    SELECT 
        geography_id AS village_id
	FROM 
        main.entity_geography
	WHERE 
        geography_type_id = ${VILLAGE_TYPE_ID}
		AND 
        entity_uuid = :user_id
		AND 
        entity_type_id = :user_type
		AND 
        active = ${record_statuses.ACTIVE}
	GROUP BY
		geography_id
)
SELECT 
	ref_sdtv_view.taluk_name_l10n,
	ref_sdtv_view.village_name_l10n,
	staff.staff_name_l10n,
	staff.mobile_number as staff_mobile_number,	
	customer.customer_id,
	customer.customer_name_l10n,
	customer.mobile_number as customer_mobile_number,
	MAX(animal_classification.value_date) as withdrawl_end_date
FROM 
	main.animal_classification 
		left join main.entity_relationship
			on
				animal_classification.animal_id = entity_relationship.entity_2_entity_uuid
				and
				entity_relationship.entity_relationship_type_id = ${FARMER_TO_ANIMAL}
		left join main.customer
			on
				entity_relationship.entity_1_entity_uuid = customer.customer_id
		left join main.customer_classification
			on
				customer_classification.customer_id = customer.customer_id
				and
				customer_classification.classifier_id = ${VILLAGE_CLASSIFIER}
		left join main.entity_geography
			on
				customer_classification.value_reference_id = entity_geography.geography_id
				and
				entity_geography.entity_type_id = ${staff_type.BCO_ADMIN}
				and
				entity_geography.active = ${ACTIVE}
		left join main.staff
			on
				entity_geography.entity_uuid = staff.staff_id
		left join main.ref_sdtv_view
			on
				customer_classification.value_reference_id = ref_sdtv_view.village_id
WHERE 
	animal_classification.classifier_id = ${classifiers.ANIMAL_ANTIBIOTIC_FLAG}
	AND
	animal_classification.value_date BETWEEN :start_date AND :end_date
    AND 
        CUSTOMER_CLASSIFICATION.value_reference_id IN (
            SELECT village_id FROM VALID_VILLAGES
        )
GROUP BY
	ref_sdtv_view.taluk_name_l10n,
	ref_sdtv_view.village_name_l10n,
	staff.staff_name_l10n,
	staff.mobile_number,	
	customer.customer_id,
	customer.customer_name_l10n,
	customer.mobile_number
ORDER BY
	customer.created_at DESC
`;
