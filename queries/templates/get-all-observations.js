const { classifiers, record_statuses } = require('../../ENUMS.js')

module.exports = `

SELECT 
	care_calendar_classification.value_int as observation_category_id,
	obs_category_reference.reference_name_l10n as observation_category,
	care_calendar_classification.value_reference_id as observation_name_id,
	obs_reference.reference_name_l10n as observation_name,
	value_json as observation_val
FROM 
	main.care_calendar_classification 
		lEFT JOIN main.ref_reference as obs_category_reference
			ON
				obs_category_reference.reference_id = care_calendar_classification.value_int
		LEFT JOIN main.ref_reference as obs_reference
			ON
				obs_reference.reference_id = care_calendar_classification.value_reference_id
WHERE 
	care_calendar_id = :care_calendar_id
	AND
	classifier_id = ${classifiers.OBSERVATION_ANSWER} 
	AND 
	care_calendar_classification.active = ${record_statuses.ACTIVE}
ORDER BY 
	care_calendar_classification.value_int, 
	care_calendar_classification.value_reference_id

`;