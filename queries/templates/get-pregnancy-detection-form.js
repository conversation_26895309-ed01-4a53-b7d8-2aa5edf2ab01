const { record_statuses, classifiers } = require("../../ENUMS");

module.exports = `SELECT
    CARE_CALENDAR.care_calendar_id,
    IS_CATTLE_PREGNANT.VALUE_REFERENCE_ID AS is_cattle_pregnant,
    MONTHS_PREGNANT.VALUE_DOUBLE AS number_of_months_pregnant_2
FROM
MAIN.CARE_CALENDAR
    LEFT JOIN MAIN.CARE_CA<PERSON><PERSON>AR_CLASSIFICATION IS_CATTLE_PREGNANT ON IS_CATTLE_PREGNANT.CARE_CALENDAR_ID = CARE_CALENDAR.CARE_CALENDAR_ID
    AND IS_CATTLE_PREGNANT.CLASSIFIER_ID = ${classifiers.IS_CATTLE_PREGNANT}
    AND IS_CATTLE_PREGNANT.ACTIVE = ${record_statuses.ACTIVE}
    LEFT JOIN MAIN.CARE_CALENDAR_CLASSIFICATION MONTHS_PREGNANT ON MONTHS_PREGNANT.CARE_CALENDAR_ID = CARE_CALENDAR.CARE_CALENDAR_ID
    AND MONTHS_PREGNANT.CLASSIFIER_ID = ${classifiers.NUMBER_OF_MONTHS_PREGNANT_2}
    AND MONTHS_PREGNANT.ACTIVE = ${record_statuses.ACTIVE}
WHERE
    CARE_CALENDAR.CARE_CALENDAR_ID = :care_calendar
`