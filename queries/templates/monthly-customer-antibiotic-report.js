const { VILLAGE_TYPE_ID, VILLAGE_CLASSIFIER, FARMER_TO_ANIMAL } = require("../../utils/constant");
const { classifiers, staff_type, record_statuses } = require("../../ENUMS");

module.exports = `
WITH 
ALL_DATES AS (
	SELECT 
		dd::DATE as month_date
	FROM 
		generate_series(:start_date, :end_date, '1 day'::interval) dd
),
VALID_VILLAGES AS (
    SELECT 
		geography_id AS village_id
	FROM 
        main.entity_geography
	WHERE 
        geography_type_id = ${VILLAGE_TYPE_ID}
		AND 
        entity_uuid = :user_id
		AND 
        entity_type_id = :user_type
		AND 
        active = ${record_statuses.ACTIVE}
	GROUP BY
		geography_id
), 
ALL_CC AS (
	SELECT 
	ref_sdtv_view.taluk_name_l10n,
	ref_sdtv_view.village_name_l10n,
	staff.staff_name_l10n,
	staff.mobile_number as staff_mobile_number,
	customer.customer_id,
	customer.customer_name_l10n,
	customer.mobile_number as customer_mobile_number,
	animal_classification.value_date::DATE - animal_classification.value_int as  withdrawl_start_date,
	animal_classification.value_date::DATE as withdrawl_end_date
FROM 
	main.animal_classification 
		left join main.entity_relationship
			on
				animal_classification.animal_id = entity_relationship.entity_2_entity_uuid
				and
				entity_relationship.entity_relationship_type_id = ${FARMER_TO_ANIMAL}
		left join main.customer
			on
				entity_relationship.entity_1_entity_uuid = customer.customer_id
		left join main.customer_classification
			on
				customer_classification.customer_id = customer.customer_id
				and
				customer_classification.classifier_id = ${VILLAGE_CLASSIFIER}
		left join main.entity_geography
			on
				customer_classification.value_reference_id = entity_geography.geography_id
				and
				entity_geography.entity_type_id = ${staff_type.BCO_ADMIN}
				and
				entity_geography.active = ${record_statuses.ACTIVE}
		left join main.staff
			on
				entity_geography.entity_uuid = staff.staff_id
		left join main.ref_sdtv_view
			on
				customer_classification.value_reference_id = ref_sdtv_view.village_id		
WHERE 
	animal_classification.classifier_id = ${classifiers.ANIMAL_ANTIBIOTIC_FLAG}
	AND
	animal_classification.value_date >= :start_date
)
SELECT 
	ALL_CC.taluk_name_l10n,
	ALL_CC.village_name_l10n,
	ALL_CC.staff_name_l10n,
	ALL_CC.staff_mobile_number,
	ALL_CC.customer_id,
	ALL_CC.customer_name_l10n,
	ALL_CC.customer_mobile_number,
	COUNT (DISTINCT ALL_DATES.month_date) as total_number_of_withdrawl_days
FROM 
	ALL_DATES 
		LEFT JOIN ALL_CC
			ON
				ALL_DATES.month_date BETWEEN ALL_CC.withdrawl_start_date AND ALL_CC.withdrawl_end_date
WHERE
	ALL_CC.customer_id is not null
GROUP BY
	ALL_CC.taluk_name_l10n,
	ALL_CC.village_name_l10n,
	ALL_CC.staff_name_l10n,
	ALL_CC.staff_mobile_number,
	ALL_CC.customer_id,
	ALL_CC.customer_name_l10n,
	ALL_CC.customer_mobile_number		
`
;