const { classifiers, record_statuses, entity_type, relationship_types, activity_category, staff_activity_statuses } = require('../../ENUMS');

module.exports = `
  SELECT
    CUSTOMER.customer_id AS customer_id,
    CUSTOMER.customer_name_l10n AS customer_name,
    CUSTOMER.customer_visual_id AS customer_visual_id,
    CUSTOMER.mobile_number AS customer_mobile_number,
	  ALT_CONTACT_NUMBER.value_string_256 AS alternate_mobile_number,
    CUSTOMER_CLASSIFICATION.value_reference_id AS village_id,
    REF_VILLAGE.village_name_l10n AS village_name,
    ANIMAL.animal_id AS animal_id,
    ANIMAL.animal_visual_id AS animal_visual_id,
    REF_REFERENCE.reference_name_l10n AS animal_type_l10n,
    REF_ANIMAL_EAR_TAG.value_string_256 AS animal_ear_tag,
    CARE_CALENDAR.entity_type_id AS entity_type_id,
    CARE_CALENDAR.care_calendar_id AS care_calendar_id,
    CARE_CALENDAR.activity_date AS activity_date,
    CARE_CALENDAR.activity_id AS activity_id,
    REF_ACTIVITY.activity_category AS activity_category,
    REF_ACTIVITY.activity_name_l10n AS activity_name_l10n,
    CARE_CALENDAR.calendar_activity_status AS calendar_activity_status,
    REF_ACTIVITY_STATUS.reference_name_l10n AS activity_status_l10n,
    CARE_CALENDAR_CLASSIFICATION.value_string_256 AS sr_number
  FROM 
    main.CARE_CALENDAR
  LEFT JOIN main.CARE_CALENDAR_CLASSIFICATION
    ON
    CARE_CALENDAR.care_calendar_id = CARE_CALENDAR_CLASSIFICATION.care_calendar_id
    AND
    CARE_CALENDAR_CLASSIFICATION.classifier_id = ${classifiers.COMPLAINT_NUMBER}
    AND
    CARE_CALENDAR_CLASSIFICATION.active = ${record_statuses.ACTIVE}
  LEFT JOIN main.REF_ACTIVITY
    ON
    CARE_CALENDAR.activity_id = REF_ACTIVITY.activity_id
  LEFT JOIN main.REF_REFERENCE REF_ACTIVITY_STATUS
    ON
    REF_ACTIVITY_STATUS.reference_id = CARE_CALENDAR.calendar_activity_status
  LEFT JOIN main.ENTITY_RELATIONSHIP 
    ON 
    ENTITY_RELATIONSHIP.entity_2_entity_uuid = CARE_CALENDAR.entity_uuid
    AND
    ENTITY_RELATIONSHIP.entity_relationship_type_id = ${relationship_types.FARMER_TO_ANIMAL} 
    AND
    ENTITY_RELATIONSHIP.active = ${record_statuses.ACTIVE}
    AND
    CARE_CALENDAR.entity_type_id = ${entity_type.ANIMAL}
  LEFT JOIN main.CUSTOMER
    ON 
    CUSTOMER.customer_id = ENTITY_RELATIONSHIP.entity_1_entity_uuid
    OR (
        CUSTOMER.customer_id = CARE_CALENDAR.entity_uuid
        AND
        CARE_CALENDAR.entity_type_id = ${entity_type.CUSTOMER}
    )	
  LEFT JOIN main.CUSTOMER_CLASSIFICATION 
    ON 
    CUSTOMER_CLASSIFICATION.customer_id = CUSTOMER.customer_id
    AND
    CUSTOMER_CLASSIFICATION.classifier_id = ${classifiers.VILLAGE_FARMER_BELONGS_TO}
    AND
    CUSTOMER_CLASSIFICATION.active = ${record_statuses.ACTIVE}
  LEFT JOIN main.CUSTOMER_CLASSIFICATION ALT_CONTACT_NUMBER
    ON 
    ALT_CONTACT_NUMBER.customer_id = CUSTOMER.customer_id
    AND
    ALT_CONTACT_NUMBER.classifier_id = ${classifiers.CUSTOMER_ALTERNATE_CONTACT}
    AND
    ALT_CONTACT_NUMBER.active = ${record_statuses.ACTIVE}
  LEFT JOIN main.REF_VILLAGE
    ON
    REF_VILLAGE.village_id = CUSTOMER_CLASSIFICATION.value_reference_id
  LEFT JOIN main.ANIMAL
    ON
    ANIMAL.animal_id = ENTITY_RELATIONSHIP.entity_2_entity_uuid
  LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_TYPE
    ON
      REF_ANIMAL_TYPE.animal_id = ANIMAL.animal_id
      AND
      REF_ANIMAL_TYPE.classifier_id = ${classifiers.TYPE_OF_ANIMAL}  
      AND
      REF_ANIMAL_TYPE.active = ${record_statuses.ACTIVE}     
  LEFT JOIN main.REF_REFERENCE
    ON
    REF_REFERENCE.reference_id = REF_ANIMAL_TYPE.value_reference_id
  LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_EAR_TAG
    ON
    REF_ANIMAL_EAR_TAG.animal_id = ANIMAL.animal_id
    AND
    REF_ANIMAL_EAR_TAG.classifier_id = ${classifiers.EAR_TAG_NUMBER}
    AND
    REF_ANIMAL_EAR_TAG.active = ${record_statuses.ACTIVE}
  WHERE
    CARE_CALENDAR.entity_type_id IN (${entity_type.CUSTOMER}, ${entity_type.ANIMAL})
    AND
    CARE_CALENDAR.active = ${record_statuses.ACTIVE}
    AND
    (ANIMAL.active = ${record_statuses.ACTIVE} OR ANIMAL.active is NULL)

    AND
    CUSTOMER.active = ${record_statuses.ACTIVE}
    AND 
    (
      date(CARE_CALENDAR.activity_date) = :route_date
      OR
      date(CARE_CALENDAR.completion_date) = :route_date
    )  

    {{#if hide_closed_tickets }}
      AND CARE_CALENDAR.calendar_activity_status NOT IN (
        ${staff_activity_statuses.VISIT_ABANDONED}, 
        ${staff_activity_statuses.VISIT_CANCELLED}, 
        ${staff_activity_statuses.VISIT_COMPLETED}
      )
    {{/if}}

    {{#if customer_id }}
      AND CUSTOMER.customer_id = :customer_id
    {{/if}}

    {{#if animal_id }}
      AND ANIMAL.animal_id = :animal_id
    {{/if}}

  ORDER BY
  (
		CASE 
      WHEN 
        REF_ACTIVITY.activity_category IN (${activity_category.CURATIVE}, ${activity_category.REPRODUCTIVE}) 
      THEN 1
      ELSE 2
    END
	)    
    
`