const { VILLAGE_TYPE_ID, ACTIVE, VILLAGE_CLASSIFIER, BACK_OFFICE_PERSON } = require("../../utils/constant");

/**
 * Typeorm-placeholders:
 * Configure these using setParameters method of type-orm as per use
 * 1. user_type
 * 2. user_id
 * 3.f_farmer_name [required]
 */
module.exports = `
{{#ifEquals user_type ${BACK_OFFICE_PERSON}}}
SELECT customer_id AS id,customer_name_l10n as name_l10n
FROM main.customer
WHERE 
    UPPER(CUSTOMER.customer_name_l10n->>'ul') LIKE UPPER(:f_farmer_name) OR 
    UPPER(CUSTOMER.customer_name_l10n->>'mr') LIKE UPPER(:f_farmer_name) OR
    UPPER(CUSTOMER.customer_name_l10n->>'en') LIKE UPPER(:f_farmer_name) OR 
    UPPER(CUSTOMER.customer_name_l10n->>'hi') LIKE UPPER(:f_farmer_name)
{{else}}
WITH VALID_VILLAGES AS (
    SELECT DISTINCT(geography_id) AS village_id
	FROM main.entity_geography
	WHERE geography_type_id = ${VILLAGE_TYPE_ID}
		AND entity_uuid = :user_id
		AND entity_type_id = :user_type
		AND active = ${ACTIVE}
        )
SELECT c.customer_id AS id,c.customer_name_l10n AS name_l10n 
FROM main.customer AS c
JOIN main.customer_classification AS cv 
    ON cv.classifier_id = ${VILLAGE_CLASSIFIER}
    AND cv.customer_id = c.customer_id
    AND cv.active = ${ACTIVE}
    AND cv.value_reference_id in (SELECT * FROM VALID_VILLAGES)
WHERE 
    UPPER(c.customer_name_l10n->>'ul') LIKE UPPER(:f_farmer_name) OR 
    UPPER(c.customer_name_l10n->>'mr') LIKE UPPER(:f_farmer_name) OR
    UPPER(c.customer_name_l10n->>'en') LIKE UPPER(:f_farmer_name) OR 
    UPPER(c.customer_name_l10n->>'hi') LIKE UPPER(:f_farmer_name)
{{/ifEquals}}
LIMIT 10
`;