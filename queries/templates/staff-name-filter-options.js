/**
 * Typeorm placeholder
 * 1. f_staff_name
 */
module.exports = `
SELECT staff.staff_id as id , staff.staff_name_l10n as name_l10n
FROM main.staff 
WHERE 
    UPPER(staff.staff_name_l10n->>'ul') LIKE UPPER(:f_staff_name) OR 
    UPPER(staff.staff_name_l10n->>'mr') LIKE UPPER(:f_staff_name) OR
    UPPER(staff.staff_name_l10n->>'en') LIKE UPPER(:f_staff_name) OR 
    UPPER(staff.staff_name_l10n->>'hi') LIKE UPPER(:f_staff_name)
LIMIT 10
`;