const { VILLAGE_TYPE_ID, ACTIVE, PARAVET, BACK_OFFICE_PERSON } = require("../../utils/constant");
/**
 * Typeorm-placeholders:
 * Configure these using setParameters method of type-orm as per use
 * 1. user_type
 * 2. user_id
 */
module.exports = `
{{#ifEquals user_type ${BACK_OFFICE_PERSON}}}
	SELECT staff_id as id,staff_name_l10n as name_l10n FROM main.staff
	WHERE staff_type_id = ${PARAVET}
	AND active = ${ACTIVE}
{{else}}
WITH
VALID_VILLAGES AS (
	SELECT DISTINCT(geography_id) AS village_id
	FROM main.entity_geography
	WHERE geography_type_id = ${VILLAGE_TYPE_ID}
		AND entity_uuid = :user_id
		AND entity_type_id = :user_type
		AND active = ${ACTIVE}
		),
VALID_PARAVETS AS (
SELECT DISTINCT(entity_uuid) FROM main.entity_geography
	WHERE entity_type_id = ${PARAVET}
	AND active = ${ACTIVE}
	AND geography_id IN (SELECT * FROM VALID_VILLAGES))
SELECT staff_id as id,staff_name_l10n as name_l10n FROM main.staff 
	WHERE staff_id IN (SELECT * FROM VALID_PARAVETS)
	AND active = ${ACTIVE}
{{/ifEquals}}		
`