const { VILLAGE_CLASSIFIER, CONST_ID_TYPE_STAFF_BACKOFFICE, ACTIVE } = require("../../utils/constant");
/**
 * Typeorm-placeholders:
 * Configure these using setParameters method of type-orm as per use
 * 1. user_type
 * 2. user_id
 */
module.exports = `
SELECT rv.village_id AS id, rv.village_name_l10n AS name_l10n
FROM main.ref_village rv, (
  SELECT distinct value_reference_id as distinct_village_id
  FROM main.customer_classification cc
  WHERE cc.classifier_id = ${VILLAGE_CLASSIFIER}
  AND cc.active = ${ACTIVE}
) dc

{{#ifNotEquals user_type ${CONST_ID_TYPE_STAFF_BACKOFFICE}}}
, (
    SELECT geography_id
    FROM main.entity_geography
    WHERE entity_type_id = :user_type AND entity_uuid = :user_id
    AND active = ${ACTIVE}
    ) cv
{{/ifNotEquals}}

WHERE rv.village_id = dc.distinct_village_id 

{{#ifNotEquals user_type ${CONST_ID_TYPE_STAFF_BACKOFFICE}}}
AND rv.village_id = cv.geography_id
{{/ifNotEquals}}
`;
