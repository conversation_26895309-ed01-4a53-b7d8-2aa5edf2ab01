const { VILLAGE_TYPE_ID, ACTIVE, VILLAGE_CLASSIFIER, FARMER_TO_ANIMAL } = require("../../utils/constant");

module.exports = `
WITH VALID_VILLAGES AS (
    SELECT 
        DISTINCT(geography_id) AS village_id
	FROM 
        main.entity_geography
	WHERE 
        geography_type_id = ${VILLAGE_TYPE_ID}
		AND 
        entity_uuid = :user_id
		AND 
        entity_type_id = :user_type
		AND 
        active = ${ACTIVE}
)
SELECT 
	customer.customer_id,
	customer.customer_name_l10n,
	ref_sdtv_view.village_name_l10n,
	ref_sdtv_view.taluk_name_l10n,
	MAX(animal_classification.value_date) as withdrawl_end_date
FROM 
	main.animal_classification 
		left join main.entity_relationship
			on
				animal_classification.animal_id = entity_relationship.entity_2_entity_uuid
				and
				entity_relationship.entity_relationship_type_id = ${FARMER_TO_ANIMAL}
		left join main.customer
			on
				entity_relationship.entity_1_entity_uuid = customer.customer_id
		left join main.customer_classification
			on
				customer_classification.customer_id = customer.customer_id
				and
				customer_classification.classifier_id = ${VILLAGE_CLASSIFIER}
		left join main.ref_sdtv_view
			on
				customer_classification.value_reference_id = ref_sdtv_view.village_id
WHERE 
	animal_classification.classifier_id = 2000000428
	AND
	animal_classification.value_date >= CURRENT_DATE
    AND 
        CUSTOMER_CLASSIFICATION.value_reference_id IN (
            SELECT village_id FROM VALID_VILLAGES
        )
{{#if f_farmer_name}}
    AND (
        {{#ifEquals IS_POSTGRES 1}}
            UPPER(CUSTOMER.customer_name_l10n->>'ul') LIKE UPPER(:f_farmer_name) OR 
            UPPER(CUSTOMER.customer_name_l10n->>'mr') LIKE UPPER(:f_farmer_name) OR
            UPPER(CUSTOMER.customer_name_l10n->>'en') LIKE UPPER(:f_farmer_name) OR 
            UPPER(CUSTOMER.customer_name_l10n->>'hi') LIKE UPPER(:f_farmer_name)
        {{else}}
            UPPER(CUSTOMER.customer_name_l10n) LIKE UPPER(:f_farmer_name)
        {{/ifEquals}}
    )
{{/if}}        
GROUP BY
	customer.customer_id,
	customer.customer_name_l10n,
	ref_sdtv_view.village_name_l10n,
	ref_sdtv_view.taluk_name_l10n
ORDER BY
	customer.created_at DESC
`;
