const { TICKET_CLASSIFIER, ACTIVE, VILLAGE_TYPE_ID, VILLAGE_CLASSIFIER } = require("../../utils/constant");

/**
 * 
Template-variables:
Configure these using setParameters method of type-orm as per use
1. validVillage [required]
2. detailed [required]
3. dashboard [required]
4. f_task_id [optional]
5. farmerFilter [optional]

Typeorm-placeholders:
1. user_id [required]
2. user_type [required]
4. care_calendar_id [optional]
5. farmer_id [optional]
6. animal_id [optional]
 */

module.exports = `
WITH

{{#ifEquals validVillage 1}}
VALID_VILLAGES AS
(
SELECT 
	geography_id AS village_id
	FROM main.entity_geography
	LEFT JOIN main.staff
		ON 
		entity_geography.entity_uuid = staff.staff_id
	WHERE
		entity_geography.geography_id IS NOT NULL
		AND
		entity_geography.active = ${ACTIVE} 
		AND
		entity_geography.geography_type_id = ${VILLAGE_TYPE_ID}
		AND
		staff.staff_type_id = :user_type
		AND staff.staff_id = :user_id
	GROUP BY
		geography_id
	),
{{/ifEquals}}

	CC_ENTRIES AS (
		
		SELECT

		{{#ifEquals detailed 1}}
			CUSTOMER_CLASSIFICATION.value_reference_id AS village_id,
			REF_VILLAGE.village_name_l10n AS village_name_l10n,
			REF_TALUK.taluk_id AS taluk_id,
			REF_TALUK.taluk_name_l10n AS taluk_name,
			REF_DISTRICT.district_id AS district_id,
			REF_DISTRICT.district_name_l10n AS district_name,
			REF_STATE.state_id AS state_id,
			REF_STATE.state_name_l10n AS state_name,
			ANIMAL.active,
			ANIMAL.animal_visual_id AS animal_visual_id,
			REF_REFERENCE.reference_name AS animal_type,
			REF_REFERENCE.reference_name_l10n AS animal_type_json,			
			CARE_CALENDAR_CLASSIFICATION.value_string_256 AS ticket_number,
		{{/ifEquals}}
		
		ANIMAL.animal_id AS animal_id,
		CUSTOMER.customer_id AS customer_id,
		COALESCE(CARE_CALENDAR.completion_date, CARE_CALENDAR.activity_date) AS activity_date,
		CARE_CALENDAR.activity_id AS activity_id,
		CARE_CALENDAR.entity_type_id AS entity_type_id,
		CARE_CALENDAR.entity_uuid,
		CARE_CALENDAR.care_calendar_id AS care_calendar_id,
		CARE_CALENDAR.calendar_activity_status AS calendar_activity_status,
		REF_ACTIVITY_STATUS.reference_name AS activity_status,
		REF_ACTIVITY_STATUS.reference_name_l10n AS calendar_activity_status_name_json,
		REF_ANIMAL_EAR_TAG.value_string_256 AS ear_tag,
		REF_ANIMAL_NAME.value_string_256 AS animal_name,
		REF_ACTIVITY.activity_name AS activity_name,
		REF_ACTIVITY.activity_name_l10n AS activity_name_l10n,
		REF_ACTIVITY.activity_category AS activity_category,
		COALESCE(STAFF_ACTIVITY.staff_id, STAFF.staff_id) AS paravet_id,
		COALESCE(STAFF_OVERRIDE.staff_name_l10n, STAFF.staff_name_l10n) AS paravet_name,
		COALESCE(STAFF_OVERRIDE.mobile_number, STAFF.mobile_number) AS paravet_mobile_number,
		CUSTOMER.active,
		CUSTOMER.customer_name_l10n AS customer_name,
		CUSTOMER.customer_visual_id AS customer_visual_id,
		CUSTOMER.mobile_number AS farmer_contact		
	FROM 
		main.CARE_CALENDAR
			LEFT JOIN main.CARE_CALENDAR_CLASSIFICATION
				ON
					CARE_CALENDAR.care_calendar_id = CARE_CALENDAR_CLASSIFICATION.care_calendar_id
					AND
					CARE_CALENDAR_CLASSIFICATION.classifier_id = ${TICKET_CLASSIFIER}
                    AND 
                    CARE_CALENDAR_CLASSIFICATION.active = ${ACTIVE}
			LEFT JOIN main.REF_ACTIVITY
				ON
					CARE_CALENDAR.activity_id = REF_ACTIVITY.activity_id
			LEFT JOIN main.REF_REFERENCE REF_ACTIVITY_STATUS
				ON
					REF_ACTIVITY_STATUS.reference_id = CARE_CALENDAR.calendar_activity_status
			LEFT JOIN main.ENTITY_RELATIONSHIP 
				ON 
					ENTITY_RELATIONSHIP.entity_2_entity_uuid = CARE_CALENDAR.entity_uuid
					AND
					ENTITY_RELATIONSHIP.entity_relationship_type_id = 1000210004
					AND
					CARE_CALENDAR.entity_type_id = 1000460002
			LEFT JOIN main.CUSTOMER
				ON 
					CUSTOMER.customer_id = ENTITY_RELATIONSHIP.entity_1_entity_uuid
					OR (
						CUSTOMER.customer_id = CARE_CALENDAR.entity_uuid
						AND
						CARE_CALENDAR.entity_type_id = 1000460001
					)
			LEFT JOIN main.CUSTOMER_CLASSIFICATION
				ON
					CUSTOMER_CLASSIFICATION.customer_id = CUSTOMER.customer_id
				AND
					CUSTOMER_CLASSIFICATION.classifier_id = ${VILLAGE_CLASSIFIER}
			LEFT JOIN main.REF_VILLAGE
				ON
					REF_VILLAGE.village_id = CUSTOMER_CLASSIFICATION.value_reference_id	
			LEFT JOIN main.ENTITY_GEOGRAPHY 
				ON 
					ENTITY_GEOGRAPHY.geography_id = REF_VILLAGE.village_id
					AND
					ENTITY_GEOGRAPHY.geography_type_id = 1000320004 
					AND
					ENTITY_GEOGRAPHY.entity_type_id = 1000230004
					AND
					ENTITY_GEOGRAPHY.active = ${ACTIVE}
			LEFT JOIN main.STAFF
				ON
					STAFF.staff_id = ENTITY_GEOGRAPHY.entity_uuid
			LEFT JOIN main.STAFF_ACTIVITY
				ON
					STAFF_ACTIVITY.care_calendar = CARE_CALENDAR.care_calendar_id
			LEFT JOIN main.STAFF STAFF_OVERRIDE
				ON
					STAFF_OVERRIDE.staff_id = STAFF_ACTIVITY.staff_id
			LEFT JOIN main.ANIMAL
				ON
					ANIMAL.animal_id = ENTITY_RELATIONSHIP.entity_2_entity_uuid			
			LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_NAME
				ON
					REF_ANIMAL_NAME.animal_id = ANIMAL.animal_id
					AND
					REF_ANIMAL_NAME.classifier_id = 2000000033
			LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_EAR_TAG
				ON
					REF_ANIMAL_EAR_TAG.animal_id = ANIMAL.animal_id
					AND
					REF_ANIMAL_EAR_TAG.classifier_id = 2000000034
			LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_TYPE
				ON
					REF_ANIMAL_TYPE.animal_id = ANIMAL.animal_id
					AND
					REF_ANIMAL_TYPE.classifier_id = 2000000035        
						
			{{#ifEquals detailed 1 }}
				LEFT JOIN main.REF_TALUK
					ON
						REF_TALUK.taluk_id = REF_VILLAGE.taluk_id
				LEFT JOIN main.REF_DISTRICT
					ON
						REF_DISTRICT.district_id = REF_TALUK.district_id
				LEFT JOIN main.REF_STATE
					ON
						REF_STATE.state_id = REF_DISTRICT.state_id
				LEFT JOIN main.REF_REFERENCE
					ON
						REF_REFERENCE.reference_id = REF_ANIMAL_TYPE.value_reference_id			
			{{/ifEquals}}
            
	WHERE
		CARE_CALENDAR.active = 1000100001
		AND
		CARE_CALENDAR.entity_type_id IN (1000460001, 1000460002)
		AND
		CUSTOMER.active = 1000100001
		AND (
			ANIMAL.active is null 
			OR 
			ANIMAL.active = 1000100001
		)

		{{#ifEquals dashboard 1 }}
			{{#ifEquals IS_POSTGRES 1 }}
				AND CARE_CALENDAR.activity_date < current_date + 15
			{{else}}
				AND CARE_CALENDAR.activity_date < date('now', 'start of day', '+15 day')
			{{/ifEquals}}
		{{/ifEquals}}
		
		{{#if from }}
            AND CARE_CALENDAR.activity_date::DATE >= :from
        {{/if}}
        {{#if to}}
            AND CARE_CALENDAR.activity_date::DATE <= :to
        {{/if}}
		{{#ifEquals user_type 1000230004}}
		AND CARE_CALENDAR.activity_id != 1000700001
		{{/ifEquals}}
		{{#ifEquals user_type 1000220001}}
		AND CARE_CALENDAR.activity_id != 1000700002
		{{/ifEquals}}
				
		{{#ifEquals f_task_id 1}}
		AND CARE_CALENDAR.care_calendar_id = :care_calendar_id
		{{/ifEquals}}
		
		{{#ifEquals farmerFilter 1}}
		AND CUSTOMER.customer_id = :farmer_id
		{{/ifEquals}}
		
		{{#ifEquals animalFilter 1}}
		AND ANIMAL.animal_id = :animal_id
		{{/ifEquals}}

		{{#ifEquals validVillage 1}}
		AND CUSTOMER_CLASSIFICATION.value_reference_id IN ( SELECT village_id FROM VALID_VILLAGES)
			ORDER BY CUSTOMER_CLASSIFICATION.value_reference_id
		{{/ifEquals}}
)
{{#ifEquals thumbnail 1}}
,STAFF_DOCUMENT AS (
	SELECT
		entity_1_entity_uuid AS PARAVET_ID,
		MAX(document_id::TEXT) AS PARAVET_DOCUMENT_ID
	FROM
		main.document
			LEFT JOIN CC_ENTRIES
				ON entity_1_entity_uuid = CC_ENTRIES.paravet_id
	WHERE
	  	document_type_id = 1000260004 
		and 
		document.active = 1000100001
	GROUP BY
		entity_1_entity_uuid
),
ANIMAL_DOCUMENT AS (
	SELECT
		entity_1_entity_uuid AS ANIMAL_ID,
		MAX(document_id::TEXT) AS ANIMAL_DOCUMENT_ID
	FROM
		main.document
			LEFT JOIN CC_ENTRIES
				ON entity_1_entity_uuid = CC_ENTRIES.animal_id
	WHERE
	  	document_type_id = 1000260001 
		and 
		document.active = 1000100001
	GROUP BY
		entity_1_entity_uuid
)
{{/ifEquals}}
SELECT

{{#ifEquals thumbnail 1}}
	STAFF_DOCUMENT.PARAVET_DOCUMENT_ID AS paravet_document_id,
	ANIMAL_DOCUMENT.ANIMAL_DOCUMENT_ID AS animal_document_id,
{{/ifEquals}}
	CC_ENTRIES.*
FROM
	CC_ENTRIES
{{#ifEquals thumbnail 1}}
		LEFT JOIN STAFF_DOCUMENT
			ON
				CC_ENTRIES.paravet_id = STAFF_DOCUMENT.PARAVET_ID
		LEFT JOIN ANIMAL_DOCUMENT
			ON
				CC_ENTRIES.animal_id = ANIMAL_DOCUMENT.ANIMAL_ID
{{/ifEquals}}`;