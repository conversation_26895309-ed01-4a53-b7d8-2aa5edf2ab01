module.exports = {
    "route-plan": require("./route-plan"),
    "route-details": require("./route-details"),
    "activitybyid": require("./activitybyid"),
    "propagte-task-status": require("./propagte-task-status"),
    "propagatedanimals": require("./propagatedanimals"),
    "village-filter-options": require("./village-filter-options"),
    "paravet-filter-options": require("./paravet-filter-options"),
    "task": require("./task"),
    "taskv2": require("../../v2/queries/templates/activity/taskv2"),
    "customer-listing": require("./customer-listing"),
    "farmer-filter-options": require("./farmer-filter-options"),
    "staff-village-filter-options": require("./staff-village-filter-options"),
    "staff-name-filter-options": require("./staff-name-filter-options"),
    "check-active": require("./check-active"),
    "task-preview": require("./task-preview"),
    "farm-task-preview": require("./farm-task-preview"),
    "cattle-task-preview": require("./cattle-task-preview"),
    "animal-history": require("./animalHistoryById"),
    "activity-stats": require("./activity-stats"),
    "get-farmer-tasks": require("./get-farmer-tasks"),
    "get-ppu-staff-by-customer": require("./ppu-staff-by-customer"),
    "tasks-by-entity": require("./tasks-by-entity"),
    "route-plan-v2": require("./route-plan-v2"),
    "activity-stats-v2": require("./activity-stats-v2"),
    "village-filter-options-v2": require("./village-filter-options-v2"),
    "freelance-paravet-filter-options": require("./freelance-paravet-filter-options"),
    "calendar-summary": require("./calendar-summary"),
    "users-login-v2": require("../../v2/queries/templates/users.js/users"),
    "animals-v2": require("../../v2/queries/templates/animals/animals"),
    "health-record-summary": require("../../v2/queries/templates/summary/healthRecordSummary"),
    "medical-record-summary": require("../../v2/queries/templates/summary/medicalRecordSummary"),
    "customer-antibiotic-report": require("./customer-antibiotic-report"),
    "daily-cattle-rx-report": require("./daily-cattle-rx-report"),
    "daily-customer-antibiotic-report": require("./daily-customer-antibiotic-report"),
    "monthly-customer-antibiotic-report": require("./monthly-customer-antibiotic-report"),
    "animal-details-for-preventive-task": require("./animal-details-for-preventive-task"),
    "get-all-observations": require("./get-all-observations"),
    "get-pregnancy-detection-form": require("./get-pregnancy-detection-form")
};