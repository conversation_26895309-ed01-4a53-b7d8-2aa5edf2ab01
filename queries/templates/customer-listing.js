const { classifiers, record_statuses, relationship_types, geography_types, document_type, staff_type } = require('../../ENUMS')

module.exports = `
     WITH NOT_SUBSCRIBED AS (
        select 
            distinct customer.customer_id 
        from 
            main.customer
                left join main.entity_relationship as er 
                    on 
                        customer.customer_id = er.entity_1_entity_uuid
                        and 
                        er.entity_relationship_type_id = ${relationship_types.FARMER_TO_ANIMAL}
                left join main.animal_classification as ac 
                    on 
                        ac.animal_id = er.entity_2_entity_uuid
                        and 
                        ac.classifier_id = ${classifiers.HEALTHCARE_PLAN_SUBSCRIPTION_DATE}
                        and 
                        ac.value_date is null
        where 
            customer.active = ${record_statuses.ACTIVE}
     ),
     CUSTOMER_LISTING AS (
        SELECT
            customer.customer_id, 
            customer.customer_name_l10n, 
            customer.mobile_number,
            customer.customer_visual_id,
            customer.updated_at,
            customer.created_at,
            ALT_CONTACT.value_string_256 as alternate_mobile_number,
            REF_VILLAGE.village_name_l10n,
            REF_VILLAGE.village_id as village_id,
            REF_TALUK.taluk_id AS taluk_id,
            REF_TALUK.taluk_name_l10n,
            REF_DISTRICT.district_id AS district_id,
            REF_DISTRICT.district_name_l10n,
            REF_STATE.state_id AS state_id,
            REF_STATE.state_name_l10n,
            STAFF.staff_id as staff_id,
            STAFF.staff_name_l10n as paravet_name
        FROM		
            main.CUSTOMER 
                LEFT JOIN 
                    main.CUSTOMER_CLASSIFICATION 
                    ON 
                        CUSTOMER_CLASSIFICATION.customer_id = CUSTOMER.customer_id
                        AND 
                        CUSTOMER_CLASSIFICATION.classifier_id = ${classifiers.VILLAGE_FARMER_BELONGS_TO}
                LEFT JOIN 
                    main.CUSTOMER_CLASSIFICATION ALT_CONTACT
                    ON 
                        ALT_CONTACT.customer_id = CUSTOMER.customer_id
                        AND 
                        ALT_CONTACT.classifier_id = ${classifiers.CUSTOMER_ALTERNATE_CONTACT}
                LEFT JOIN 
                    main.REF_VILLAGE 
                    ON 
                        REF_VILLAGE.village_id = CUSTOMER_CLASSIFICATION.value_reference_id
                LEFT JOIN 
                    main.REF_TALUK 
                    ON 
                        REF_TALUK.taluk_id = REF_VILLAGE.taluk_id
                LEFT JOIN 
                    main.REF_DISTRICT 
                    ON 
                        REF_DISTRICT.district_id = REF_TALUK.district_id
                LEFT JOIN 
                    main.REF_STATE 
                    ON 
                        REF_STATE.state_id = REF_DISTRICT.state_id
                LEFT JOIN 
                    main.ENTITY_GEOGRAPHY 
                    ON 
                        ENTITY_GEOGRAPHY.geography_id = REF_VILLAGE.village_id
                        AND 
                        ENTITY_GEOGRAPHY.geography_type_id = ${geography_types.VILLAGE}
                        AND 
                        ENTITY_GEOGRAPHY.entity_type_id = ${staff_type.PARAVET}
                        AND 
                        ENTITY_GEOGRAPHY.ACTIVE = ${record_statuses.ACTIVE}
                LEFT JOIN 
                    main.STAFF 
                    ON 
                        STAFF.staff_id = ENTITY_GEOGRAPHY.entity_uuid
    WHERE
        customer.active = ${record_statuses.ACTIVE}
       
        {{#ifEquals IS_VILLAGE_FILTER_NEEDED 'Y'}}
        AND REF_VILLAGE.village_id IN (
            SELECT
                geography_id
            FROM
                main.entity_geography    
            WHERE
                geography_type_id = ${geography_types.VILLAGE}
                AND 
                entity_uuid = :user_id
                AND 
                entity_type_id = :user_type
                AND
                active = ${record_statuses.ACTIVE}
        )
        {{/ifEquals}}
        
        {{#if f_farmer_name}}
            AND (
                {{#ifEquals IS_POSTGRES 1}}
                UPPER(CUSTOMER.customer_name_l10n->>'ul') LIKE UPPER(:f_farmer_name) OR 
                UPPER(CUSTOMER.customer_name_l10n->>'mr') LIKE UPPER(:f_farmer_name) OR
                UPPER(CUSTOMER.customer_name_l10n->>'en') LIKE UPPER(:f_farmer_name) OR 
                UPPER(CUSTOMER.customer_name_l10n->>'hi') LIKE UPPER(:f_farmer_name)
                {{else}}
                UPPER(CUSTOMER.customer_name_l10n) LIKE UPPER(:f_farmer_name)
                {{/ifEquals}}
            )
        {{/if}}

        {{#if f_village}}
            AND REF_VILLAGE.village_id  IN (:...f_village)
        {{/if}}
        {{#if f_not_subscribed}}
            AND CUSTOMER.customer_id  IN (select * from NOT_SUBSCRIBED)
        {{/if}}
        {{#if f_not_assigned}}
        AND STAFF.staff_id is null
       {{/if}}
        {{#if f_village_id_array}}
            AND REF_VILLAGE.village_id  IN (:...f_village_id_array)
        {{/if}}

        {{#if f_farmer_village_text}}
            AND (
                {{#ifEquals IS_POSTGRES 1}}
                UPPER(REF_VILLAGE.village_name_l10n->>'ul') LIKE UPPER(:f_farmer_village_text) OR 
                UPPER(REF_VILLAGE.village_name_l10n->>'mr') LIKE UPPER(:f_farmer_village_text) OR
                UPPER(REF_VILLAGE.village_name_l10n->>'en') LIKE UPPER(:f_farmer_village_text) OR 
                UPPER(REF_VILLAGE.village_name_l10n->>'hi') LIKE UPPER(:f_farmer_village_text)
                {{else}}
                UPPER(REF_VILLAGE.village_name_l10n) LIKE UPPER(:f_farmer_village_text)
                {{/ifEquals}}
            )
        {{/if}}
        
        {{#if f_farmer_village_array}}
            AND (
                {{#ifEquals IS_POSTGRES 1}}
                REF_VILLAGE.village_name_l10n->>'ul' IN (:...f_farmer_village_array) OR 
                REF_VILLAGE.village_name_l10n->>'mr' IN (:...f_farmer_village_array) OR
                REF_VILLAGE.village_name_l10n->>'en' IN (:...f_farmer_village_array) OR 
                REF_VILLAGE.village_name_l10n->>'hi' IN (:...f_farmer_village_array)
                {{else}}
                REF_VILLAGE.village_name_l10n IN (:...f_farmer_village_array)
                {{/ifEquals}}
            )
        {{/if}}

        {{#if f_farmer_taluka_text}}
            AND (
                {{#ifEquals IS_POSTGRES 1}}
                UPPER(REF_TALUK.taluk_name_l10n->>'ul') LIKE UPPER(:f_farmer_taluka_text) OR 
                UPPER(REF_TALUK.taluk_name_l10n->>'mr') LIKE UPPER(:f_farmer_taluka_text) OR
                UPPER(REF_TALUK.taluk_name_l10n->>'en') LIKE UPPER(:f_farmer_taluka_text) OR 
                UPPER(REF_TALUK.taluk_name_l10n->>'hi') LIKE UPPER(:f_farmer_taluka_text)
                {{else}}
                UPPER(REF_TALUK.taluk_name_l10n) LIKE UPPER(:f_farmer_taluka_text)
                {{/ifEquals}}
            )
        {{/if}}
        
        {{#if f_farmer_taluka_array}}
            AND (
                {{#ifEquals IS_POSTGRES 1}}
                REF_TALUK.taluk_name_l10n->>'ul' IN (:...f_farmer_taluka_array) OR 
                REF_TALUK.taluk_name_l10n->>'mr' IN (:...f_farmer_taluka_array) OR
                REF_TALUK.taluk_name_l10n->>'en' IN (:...f_farmer_taluka_array) OR 
                REF_TALUK.taluk_name_l10n->>'hi' IN (:...f_farmer_taluka_array)
                {{else}}
                REF_TALUK.taluk_name_l10n IN (:...f_farmer_taluka_array)
                {{/ifEquals}}
            )
        {{/if}}
        
        {{#if f_farmer_visual_id}}
            AND (
                UPPER(customer.customer_visual_id) LIKE UPPER(:f_farmer_visual_id)
            )
        {{/if}}

        {{#if f_farmer_mobile}}
            AND (
                customer.mobile_number LIKE :f_farmer_mobile
            )
        {{/if}}

        {{#if f_paravet_name}}
            AND (
                UPPER(STAFF.staff_name_l10n->>'ul') LIKE UPPER(:f_paravet_name) OR 
                UPPER(STAFF.staff_name_l10n->>'mr') LIKE UPPER(:f_paravet_name) OR
                UPPER(STAFF.staff_name_l10n->>'en') LIKE UPPER(:f_paravet_name) OR 
                UPPER(STAFF.staff_name_l10n->>'hi') LIKE UPPER(:f_paravet_name)
            )
        {{/if}}
    ),
    ANIMAL_COUNT AS (
        
        SELECT 
            entity_relationship.entity_1_entity_uuid AS customer_id,
            COUNT(*) AS cattle_count
        FROM 
            main.entity_relationship
            JOIN main.animal as a 
                on 
                    entity_relationship.entity_2_entity_uuid = a.animal_id 
                    AND 
                    a.active = ${record_statuses.ACTIVE}
        WHERE 
            entity_relationship.entity_relationship_type_id = ${relationship_types.FARMER_TO_ANIMAL}
            AND
            entity_relationship.entity_1_entity_uuid IN (
                SELECT 
                    customer_id 
                FROM
                    CUSTOMER_LISTING
            )
        GROUP BY 
            entity_relationship.entity_1_entity_uuid
    ),
    THUMBNAIL_DOCUMENT AS (
        
        SELECT 
            document.entity_1_entity_uuid AS customer_id,
            {{#ifEquals IS_POSTGRES 1}}
            max(document.document_id::TEXT) AS document_id,
            max(document.document_information::TEXT) AS document_information,
            max(document.client_document_information::TEXT) AS client_document_information	
            {{else}}
            max(document.document_id) AS document_id,
            max(document.document_information) AS document_information,
            max(document.client_document_information) AS client_document_information	
            {{/ifEquals}}
        FROM 
            main.document 
        WHERE 
            document.document_type_id = ${document_type.CUSTOMER_THUMBNAIL}
            AND
            document.entity_1_entity_uuid IN (
                SELECT 
                    customer_id 
                FROM
                    CUSTOMER_LISTING
            )
            AND
            document.active = ${record_statuses.ACTIVE}
        GROUP BY
            document.entity_1_entity_uuid
    )
    SELECT 
        CUSTOMER_LISTING.customer_id, 
        CUSTOMER_LISTING.customer_name_l10n, 
        CUSTOMER_LISTING.mobile_number,
        CUSTOMER_LISTING.customer_visual_id,
        CUSTOMER_LISTING.updated_at,
        CUSTOMER_LISTING.village_name_l10n,
        CUSTOMER_LISTING.village_id,
        CUSTOMER_LISTING.taluk_id,
        CUSTOMER_LISTING.taluk_name_l10n,
        CUSTOMER_LISTING.staff_id,
        CUSTOMER_LISTING.paravet_name,
        CUSTOMER_LISTING.alternate_mobile_number,
        ANIMAL_COUNT.cattle_count,
        THUMBNAIL_DOCUMENT.document_id,
        THUMBNAIL_DOCUMENT.document_information,
        THUMBNAIL_DOCUMENT.client_document_information
    FROM CUSTOMER_LISTING
        LEFT JOIN 
            ANIMAL_COUNT
                ON
                    CUSTOMER_LISTING.customer_id = ANIMAL_COUNT.customer_id
        LEFT JOIN 
            THUMBNAIL_DOCUMENT
                ON
                    CUSTOMER_LISTING.customer_id = THUMBNAIL_DOCUMENT.customer_id
                    ORDER BY CUSTOMER_LISTING.created_at DESC
`
