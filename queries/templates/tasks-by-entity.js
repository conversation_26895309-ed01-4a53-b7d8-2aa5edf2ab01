const { TICKET_CLASSIFIER, ACTIVE, PPU_PARAVET_TO_FARMER, FARMER_TO_ANIMAL,ANIMAL_EAR_TAG_CLASSIFIER } = require("../../utils/constant");

module.exports = `
SELECT 
    CC.CARE_CALENDAR_ID,
    CCC.VALUE_STRING_256 AS SR_NUMBER,
    RA.ACTIVITY_ID,
    RA.ACTIVITY_NAME,
    RA.ACTIVITY_CATEGORY,
    RA.ACTIVITY_NAME_L10N,
    CC.ACTIVITY_DATE,
    CC.ACTIVE,
    CC.CALENDAR_ACTIVITY_STATUS,
    RR.REFERENCE_NAME,
    RR.REFERENCE_NAME_L10N,
    ER_CUSTOMER.ENTITY_2_ENTITY_UUID ANIMAL_ID,
    AC.VALUE_STRING_256 AS EAR_TAG,
    ANIMAL.ANIMAL_VISUAL_ID,
    ER_PARAVET.ENTITY_RELATIONSHIP_TYPE_ID,
    C.CUSTOMER_ID,
    C.CUSTOMER_VISUAL_ID,
    C.CUSTOMER_NAME_L10N AS CUSTOMER_NAME,
    C.MOBILE_NUMBER AS CUSTOMER_MOBILE,
    ANIMAL.ANIMAL_VISUAL_ID,
    ER_PARAVET.ENTITY_1_ENTITY_UUID AS PARAVET_ID,
    STAFF.STAFF_NAME_L10N AS PARAVET_NAME,
    STAFF.MOBILE_NUMBER as PARAVET_MOBILE_NUMBER
    FROM MAIN.CUSTOMER C
    LEFT JOIN MAIN.ENTITY_RELATIONSHIP ER_PARAVET ON ER_PARAVET.ENTITY_RELATIONSHIP_TYPE_ID=${PPU_PARAVET_TO_FARMER} AND ER_PARAVET.ENTITY_2_ENTITY_UUID=C.CUSTOMER_ID AND ER_PARAVET.ACTIVE=${ACTIVE}
    LEFT JOIN MAIN.ENTITY_RELATIONSHIP ER_CUSTOMER ON ER_CUSTOMER.ENTITY_RELATIONSHIP_TYPE_ID=${FARMER_TO_ANIMAL} AND ER_CUSTOMER.ENTITY_1_ENTITY_UUID=C.CUSTOMER_ID AND ER_CUSTOMER.ACTIVE=${ACTIVE}
    LEFT JOIN MAIN.STAFF ON STAFF.STAFF_ID=ER_PARAVET.ENTITY_1_ENTITY_UUID AND STAFF.ACTIVE=${ACTIVE}
    LEFT JOIN MAIN.ANIMAL ON ANIMAL.ANIMAL_ID=ER_CUSTOMER.ENTITY_2_ENTITY_UUID AND ANIMAL.ACTIVE=${ACTIVE}
    LEFT JOIN MAIN.ANIMAL_CLASSIFICATION AC ON ANIMAL.ANIMAL_ID=AC.ANIMAL_ID AND AC.CLASSIFIER_ID=${ANIMAL_EAR_TAG_CLASSIFIER}
    LEFT JOIN MAIN.CARE_CALENDAR CC ON CC.ENTITY_UUID=ANIMAL.ANIMAL_ID  AND CC.ACTIVE=${ACTIVE}
    LEFT JOIN MAIN.CARE_CALENDAR_CLASSIFICATION CCC ON CCC.CARE_CALENDAR_ID=CC.CARE_CALENDAR_ID AND CCC.CLASSIFIER_ID=${TICKET_CLASSIFIER}
    LEFT JOIN MAIN.REF_ACTIVITY RA ON CC.ACTIVITY_ID=RA.ACTIVITY_ID
    LEFT JOIN MAIN.REF_REFERENCE RR ON REFERENCE_ID=CC.CALENDAR_ACTIVITY_STATUS
    {{#if farmer_id}}
        WHERE C.CUSTOMER_ID= :farmer_id
    {{else}}
        WHERE ANIMAL.ANIMAL_ID= :animal_id
    {{/if}}
`