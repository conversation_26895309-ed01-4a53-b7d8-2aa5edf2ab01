const { VILLAGE_TYPE_ID, VILLAGE_CLASSIFIER, FARMER_TO_ANIMAL } = require("../../utils/constant");
const { classifiers, staff_type, record_statuses, entity_type, medication_categories } = require("../../ENUMS");

module.exports = `
WITH 
VALID_VILLAGES AS (
    SELECT 
		geography_id AS village_id
	FROM 
        main.entity_geography
	WHERE 
        geography_type_id = ${VILLAGE_TYPE_ID}
		AND 
        entity_uuid = :user_id
		AND 
        entity_type_id = :user_type
		AND 
        active = ${record_statuses.ACTIVE}
	GROUP BY
		geography_id
), 
ALL_CC AS (
	select 
		care_calendar_classification.care_calendar_id,
		care_calendar.entity_uuid
	from 
		main.care_calendar_classification 
			left join main.care_calendar
				on
					care_calendar_classification.care_calendar_id = care_calendar.care_calendar_id
	where 
		care_calendar_classification.classifier_id = ${classifiers.PRESCRIPTION_ADMINISTERED_AT} 
		and 
		care_calendar_classification.value_date::DATE = :start_date
		and
		care_calendar.entity_type_id = ${entity_type.ANIMAL}
	group by
		care_calendar_classification.care_calendar_id,
		care_calendar.entity_uuid
), 
ALL_CC_DIAGNOSIS AS (
	select 
		all_cc.care_calendar_id,
		care_calendar_classification.classifier_id,
		care_calendar_classification.value_reference_id,
		ref_diagnosis.reference_name_l10n,
		ROW_NUMBER() OVER(PARTITION BY all_cc.care_calendar_id) as rownum
	from 
		all_cc 
			left join main.care_calendar_classification
				on
					care_calendar_classification.care_calendar_id = ALL_CC.care_calendar_id
			left join main.ref_diagnosis
				on
					care_calendar_classification.value_reference_id = ref_diagnosis.reference_id
	where 
		care_calendar_classification.classifier_id = ${classifiers.DIAGNOSIS}
), 
ALL_CC_DIAGNOSIS_REPORT AS (
	select 
		ALL_CC_DIAGNOSIS.care_calendar_id,
		count(*) as diagnosis_count,
		jsonb_agg(case when ALL_CC_DIAGNOSIS.rownum = 1 then ALL_CC_DIAGNOSIS.reference_name_l10n end)->0 as diagnosis_1,
		jsonb_agg(case when ALL_CC_DIAGNOSIS.rownum = 2 then ALL_CC_DIAGNOSIS.reference_name_l10n end)->1 as diagnosis_2	
	from 
		ALL_CC_DIAGNOSIS
	group by
		ALL_CC_DIAGNOSIS.care_calendar_id,
		ALL_CC_DIAGNOSIS.classifier_id
),
ALL_CC_MEDICINES AS (
	select 
		all_cc.care_calendar_id,
		care_calendar_classification.classifier_id,
		care_calendar_classification.value_reference_id,
		ref_medicine.medicine_name_l10n as reference_name_l10n,
		care_calendar_classification.value_double,
		care_calendar_classification.value_json,
		CASE WHEN ref_medicine_classification.value_reference_id is not null THEN 'Y' ELSE 'N' END as is_antibiotic,
		ROW_NUMBER() OVER(PARTITION BY all_cc.care_calendar_id ORDER BY ref_medicine_classification.value_reference_id) as rownum
	from 
		all_cc 
			left join main.care_calendar_classification
				on
					care_calendar_classification.care_calendar_id = ALL_CC.care_calendar_id
			left join main.ref_medicine_classification
				on
					care_calendar_classification.value_reference_id = ref_medicine_classification.medicine_id
					and
					ref_medicine_classification.classifier_id = ${classifiers.MEDICINE_USAGE_TYPE}
					and
					ref_medicine_classification.value_reference_id = ${medication_categories.ANTIBIOTIC}
			left join main.ref_medicine
				on
					care_calendar_classification.value_reference_id = ref_medicine.medicine_id
	where 
		care_calendar_classification.classifier_id = ${classifiers.ACTIVITY_MEDICINES}
		and
		care_calendar_classification.value_json->>'status' = 'ADMINISTERED'
), 
ALL_CC_MEDICINES_REPORT AS (
	select 
		ALL_CC_MEDICINES.care_calendar_id,
		count(*) as medicine_count,
		jsonb_agg(case when ALL_CC_MEDICINES.rownum = 1 then ALL_CC_MEDICINES.reference_name_l10n end)->0 as medicine_1,
		max(case when ALL_CC_MEDICINES.rownum = 1 then ALL_CC_MEDICINES.is_antibiotic end) as medicine_1_antibiotic_status,	
		jsonb_agg(case when ALL_CC_MEDICINES.rownum = 2 then ALL_CC_MEDICINES.reference_name_l10n end)->1 as medicine_2,	
		max(case when ALL_CC_MEDICINES.rownum = 2 then ALL_CC_MEDICINES.is_antibiotic end) as medicine_2_antibiotic_status,	
		jsonb_agg(case when ALL_CC_MEDICINES.rownum = 3 then ALL_CC_MEDICINES.reference_name_l10n end)->2 as medicine_3,
		max(case when ALL_CC_MEDICINES.rownum = 3 then ALL_CC_MEDICINES.is_antibiotic end) as medicine_3_antibiotic_status	
	from 
		ALL_CC_MEDICINES
	group by
		ALL_CC_MEDICINES.care_calendar_id,
		ALL_CC_MEDICINES.classifier_id
)
SELECT 
	ref_sdtv_view.taluk_name_l10n,
	ref_sdtv_view.village_name_l10n,
	staff.staff_name_l10n,
	staff.mobile_number as staff_mobile_number,
	customer.customer_name_l10n,
	customer.mobile_number as customer_mobile_number,
	animal_classification.value_string_256 as ear_tag,
	ALL_CC.care_calendar_id,
	coalesce(ALL_CC_DIAGNOSIS_REPORT.diagnosis_count, 0) as diagnosis_count,
	ALL_CC_DIAGNOSIS_REPORT.diagnosis_1,
	ALL_CC_DIAGNOSIS_REPORT.diagnosis_2,
	coalesce(ALL_CC_MEDICINES_REPORT.medicine_count, 0) as medicine_count,
	ALL_CC_MEDICINES_REPORT.medicine_1,
	ALL_CC_MEDICINES_REPORT.medicine_1_antibiotic_status,
	ALL_CC_MEDICINES_REPORT.medicine_2,
	ALL_CC_MEDICINES_REPORT.medicine_2_antibiotic_status,	
	ALL_CC_MEDICINES_REPORT.medicine_3,
	ALL_CC_MEDICINES_REPORT.medicine_3_antibiotic_status
FROM 
	ALL_CC		
		LEFT JOIN ALL_CC_DIAGNOSIS_REPORT
			ON
				ALL_CC.care_calendar_id = ALL_CC_DIAGNOSIS_REPORT.care_calendar_id
		LEFT JOIN ALL_CC_MEDICINES_REPORT
			ON
				ALL_CC.care_calendar_id = ALL_CC_MEDICINES_REPORT.care_calendar_id
		LEFT JOIN main.animal_classification
			ON
				ALL_CC.entity_uuid = animal_classification.animal_id
				and
				animal_classification.classifier_id = ${classifiers.EAR_TAG_NUMBER}
			LEFT JOIN main.entity_relationship
			ON
				ALL_CC.entity_uuid = entity_relationship.entity_2_entity_uuid
				and
				entity_relationship.entity_relationship_type_id = ${FARMER_TO_ANIMAL}
		LEFT JOIN main.customer
			ON
				entity_relationship.entity_1_entity_uuid = customer.customer_id
		left join main.customer_classification
			on
				customer_classification.customer_id = customer.customer_id
				and
				customer_classification.classifier_id = ${VILLAGE_CLASSIFIER}
		left join main.entity_geography
			on
				customer_classification.value_reference_id = entity_geography.geography_id
				and
				entity_geography.entity_type_id = ${staff_type.BCO_ADMIN}
				and
				entity_geography.active = ${record_statuses.ACTIVE}
		left join main.staff
			on
				entity_geography.entity_uuid = staff.staff_id
		left join main.ref_sdtv_view
			on
				customer_classification.value_reference_id = ref_sdtv_view.village_id		
`
;