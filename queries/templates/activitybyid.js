module.exports = `{{!-- 
  Typeorm-placeholders:
  Configure these using setParameters method of type-orm as per use
  1. user_type [required]
  2. user_id [required]
  
  Template-variables:
  Pass these variables while invoking the template function
  1. activity_id [required]
   --}}
  
  SELECT
  cc.created_at,
  cc.updated_at,
    cc.activity_id,
    cc.entity_uuid,
    cc.entity_type_id,
    cc.activity_date AS activity_date,
    cc.completion_date as completion_date, 
    cc.care_calendar_id,
    cc.calendar_activity_status,
    cc.system_task_completion_time,
    cc.visit_schedule_time,
    cc.visit_creation_time,
    cc.user_task_completion_time,
    null as expiry_date,
    tn.value_string_256 as ticket_number,
    acat.activity_name_l10n,
    acat.activity_category,
    a.animal_visual_id,
    a.active as animal_active_status,
    etag.value_string_256 as ear_tag,
    {{#ifEquals IS_POSTGRES 1}}
    atype.reference_name_l10n::text as animal_type_json,
    {{else}}
    atype.reference_name_l10n as animal_type_json,
    {{/ifEquals}}
     aname.value_string_256 as animal_name,
     astatus.reference_name_l10n as calendar_activity_status_name_json,
    c.customer_id,
    c.customer_name_l10n,
    c.customer_visual_id,
    c.mobile_number AS farmer_contact,
    alt_mobile_number.value_string_256 as alternate_farmer_contact,
    vg.village_name_l10n,
    COALESCE(staff2.staff_type_id,staff.staff_type_id)AS staff_type_id,
    COALESCE(staff2.staff_id,staff.staff_id)AS staff_id,
    COALESCE(staff2.staff_name_l10n,staff.staff_name_l10n) as paravet_name,
    COALESCE(staff2.mobile_number,staff.mobile_number) as pravet_mobile_number,
    fd.document_id as farmer_document_id,
    COALESCE(pd2.document_id,pd.document_id) as paravet_document_id,
    COALESCE(vet_staff.staff_name_l10n,vet_staff.staff_name_l10n) as vet_name,
    vet_signature.document_information as vet_signature_doc_info,
    vet_signature.client_document_information as vet_signature_client_doc_info,
    ad.document_id as animal_document_id ,
    COALESCE(sr_value.reference_name,'Krushal') as service_request_type_name
    FROM main.care_calendar as cc
    LEFT JOIN main.care_calendar_classification as tn ON tn.care_calendar_id = cc.care_calendar_id and tn.classifier_id = 2000000125 and tn.active = {{ACTIVE}}
    LEFT JOIN main.care_calendar_classification as sr_type ON sr_type.care_calendar_id = cc.care_calendar_id and sr_type.classifier_id = 2000000243 and sr_type.active = {{ACTIVE}}
    LEFT JOIN main.ref_reference as sr_value ON sr_value.reference_id = sr_type.value_reference_id 
    JOIN main.ref_reference as astatus on astatus.reference_id = cc.calendar_activity_status and astatus.active = {{ACTIVE}}
    JOIN main.ref_activity as acat on acat.activity_id = cc.activity_id and acat.active = {{ACTIVE}}
    JOIN main.animal as a on a.animal_id = cc.entity_uuid and cc.entity_type_id = 1000460002 
    LEFT JOIN main.animal_classification as ac_animal_type on ac_animal_type.animal_id = a.animal_id and ac_animal_type.classifier_id = 2000000035 and ac_animal_type.active = {{ACTIVE}}
    JOIN main.ref_reference as atype on atype.reference_id = ac_animal_type.value_reference_id and atype.active = {{ACTIVE}}
    LEFT JOIN main.animal_classification as etag on etag.animal_id = a.animal_id and etag.classifier_id = 2000000034 and etag.active = {{ACTIVE}}
    LEFT JOIN main.animal_classification as aname on aname.animal_id = a.animal_id and aname.classifier_id = 2000000033
    JOIN main.entity_relationship as er on er.entity_relationship_type_id = 1000210004 and er.entity_2_entity_uuid = a.animal_id and er.active = {{ACTIVE}}
    JOIN main.customer as c on c.customer_id = er.entity_1_entity_uuid and c.active = {{ACTIVE}}
    JOIN main.customer_classification as cv on cv.classifier_id = 2000000055 and cv.customer_id = c.customer_id and cv.active = {{ACTIVE}}
    JOIN main.ref_village as vg on vg.village_id = cv.value_reference_id
    LEFT JOIN main.customer_classification as alt_mobile_number on alt_mobile_number.classifier_id = 2000000453 and alt_mobile_number.customer_id = c.customer_id and alt_mobile_number.active = {{ACTIVE}}
    LEFT JOIN main.entity_geography as eg on eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004 and eg.geography_id = vg.village_id and eg.active = {{ACTIVE}}
    LEFT JOIN main.staff_activity AS sa ON sa.care_calendar = cc.care_calendar_id
    LEFT JOIN main.staff as staff2 on staff2.staff_id = sa.staff_id
    LEFT JOIN main.staff on staff.staff_id = eg.entity_uuid and staff.active = {{ACTIVE}}
    LEFT JOIN main.entity_geography as vet_eg 
      on 
        vet_eg.geography_type_id = 1000320004 
        and vet_eg.entity_type_id = 1000230003
        and vet_eg.geography_id = vg.village_id 
        and vet_eg.active = {{ACTIVE}}
    LEFT JOIN main.staff vet_staff 
      on 
      vet_staff.staff_id = vet_eg.entity_uuid and vet_staff.active = {{ACTIVE}}
    LEFT JOIN main.document vet_signature
      on 
        vet_signature.entity_1_entity_uuid = vet_staff.staff_id
        and
        vet_signature.document_type_id = 1000260011
    LEFT JOIN (
      SELECT entity_1_entity_uuid,max(created_at) as created_at
      FROM main.document
      WHERE document_type_id=1000260003
      GROUP BY entity_1_entity_uuid
    ) as fmdoc on fmdoc.entity_1_entity_uuid = c.customer_id
    LEFT JOIN main.document as fd on fd.created_at = fmdoc.created_at and fd.entity_1_entity_uuid = c.customer_id and fd.entity_1_entity_uuid = fmdoc.entity_1_entity_uuid
    LEFT JOIN (
      SELECT entity_1_entity_uuid,max(created_at) as created_at
      FROM main.document
      WHERE document_type_id=1000260004
      GROUP BY entity_1_entity_uuid
    ) as pmdoc on pmdoc.entity_1_entity_uuid = staff.staff_id
    LEFT JOIN main.document as pd on pd.entity_1_entity_uuid = pmdoc.entity_1_entity_uuid and pd.entity_1_entity_uuid = staff.staff_id and pd.created_at = pmdoc.created_at
    LEFT JOIN (
      SELECT entity_1_entity_uuid,max(created_at) as created_at
      FROM main.document
      WHERE document_type_id=1000260004
      GROUP BY entity_1_entity_uuid
    ) as pmdoc2 on pmdoc2.entity_1_entity_uuid = sa.staff_id and sa.staff_id is not null
    LEFT JOIN main.document as pd2 on pd2.entity_1_entity_uuid = pmdoc2.entity_1_entity_uuid and pd2.entity_1_entity_uuid = sa.staff_id and pd2.created_at = pmdoc2.created_at
    LEFT JOIN (
      SELECT entity_1_entity_uuid,max(created_at) as created_at
      FROM main.document
      WHERE document_type_id=1000260001
      GROUP BY entity_1_entity_uuid
    ) as amdoc on amdoc.entity_1_entity_uuid  = a.animal_id
    LEFT JOIN main.document as ad on ad.entity_1_entity_uuid = amdoc.entity_1_entity_uuid and ad.created_at = amdoc.created_at and ad.entity_1_entity_uuid = a.animal_id
    WHERE 1 = 1
    {{#if calendar_id}}
       AND cc.care_calendar_id ='{{calendar_id}}'
    {{/if}}
   
   
    
  
  
    union all
  
    SELECT
    cc.created_at,
    cc.updated_at,
      cc.activity_id,
      cc.entity_uuid,
    cc.entity_type_id,
  cc.activity_date AS activity_date,
  cc.completion_date as completion_date, 
      cc.care_calendar_id,
      cc.calendar_activity_status,
      cc.system_task_completion_time,
      cc.visit_schedule_time,
      cc.visit_creation_time,
      cc.user_task_completion_time,
      ccc.value_date as expiry_date,
       tn.value_string_256 as ticket_number,
       acat.activity_name_l10n,
    acat.activity_category,
      null as animal_visual_id,
      null as animal_active_status,

      null as ear_tag,
      null as animal_type_json,
      
      null as animal_name,
      astatus.reference_name_l10n as calendar_activity_status_name_json,
      
    c.customer_id,
    c.customer_name_l10n,
    c.customer_visual_id,
    c.mobile_number AS farmer_contact,
    alt_mobile_number.value_string_256 as alternate_farmer_contact,
    vg.village_name_l10n,
    COALESCE(staff2.staff_type_id,staff.staff_type_id)AS staff_type_id,
    COALESCE(staff2.staff_id,staff.staff_id) AS staff_id,
    COALESCE(staff2.staff_name_l10n,staff.staff_name_l10n) as paravet_name,
    COALESCE(staff2.mobile_number,staff.mobile_number) as pravet_mobile_number,
    fd.document_id as farmer_document_id,
    COALESCE(pd2.document_id,pd.document_id) as paravet_document_id,
    COALESCE(vet_staff.staff_name_l10n,vet_staff.staff_name_l10n) as vet_name,
    vet_signature.document_information as vet_signature_doc_info,
    vet_signature.client_document_information as vet_signature_client_doc_info,    null as animal_document_id ,
    null as service_request_type
     FROM main.care_calendar as cc
    LEFT JOIN main.care_calendar_classification as tn ON tn.care_calendar_id = cc.care_calendar_id and tn.classifier_id = 2000000125 and 
  tn.active = {{ACTIVE}}
    JOIN main.ref_reference as astatus on astatus.reference_id = cc.calendar_activity_status and astatus.active = {{ACTIVE}}
    JOIN main.ref_activity as acat on acat.activity_id = cc.activity_id and acat.active = {{ACTIVE}}
  
    JOIN main.customer as c on c.customer_id = cc.entity_uuid and cc.entity_type_id = 1000460001 and c.active = {{ACTIVE}}
    JOIN main.customer_classification as cv on cv.classifier_id = 2000000055 and cv.customer_id = c.customer_id and cv.active = {{ACTIVE}}
    LEFT JOIN main.customer_classification as alt_mobile_number on alt_mobile_number.classifier_id = 2000000453 and alt_mobile_number.customer_id = c.customer_id and alt_mobile_number.active = {{ACTIVE}}
    LEFT JOIN main.care_calendar_classification as ccc 
    on cc.care_calendar_id = ccc.care_calendar_id
    AND ccc.classifier_id = {{EXPIRY_DATE_CLASSIFIER}}
     AND ccc.active = {{ACTIVE}}
    JOIN main.ref_village as vg on vg.village_id = cv.value_reference_id
    LEFT JOIN main.entity_geography as eg on eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004 and eg.geography_id = vg.village_id and eg.active = {{ACTIVE}}
    LEFT JOIN main.staff_activity AS sa ON sa.care_calendar = cc.care_calendar_id
    LEFT JOIN main.staff as staff2 on staff2.staff_id = sa.staff_id
    LEFT JOIN main.staff on staff.staff_id = eg.entity_uuid and staff.active = {{ACTIVE}}
    LEFT JOIN main.entity_geography as vet_eg 
    on 
      vet_eg.geography_type_id = 1000320004 
      and vet_eg.entity_type_id = 1000230003
      and vet_eg.geography_id = vg.village_id 
      and vet_eg.active = {{ACTIVE}}
  LEFT JOIN main.staff vet_staff 
    on 
    vet_staff.staff_id = vet_eg.entity_uuid and vet_staff.active = {{ACTIVE}}
  LEFT JOIN main.document vet_signature
    on 
      vet_signature.entity_1_entity_uuid = vet_staff.staff_id
      and
      vet_signature.document_type_id = 1000260011
  LEFT JOIN (
      SELECT entity_1_entity_uuid,max(created_at) as created_at
      FROM main.document
      WHERE document_type_id=1000260003
      GROUP BY entity_1_entity_uuid
    ) as fmdoc on fmdoc.entity_1_entity_uuid = c.customer_id
    LEFT JOIN main.document as fd on fd.created_at = fmdoc.created_at and fd.entity_1_entity_uuid = c.customer_id and fd.entity_1_entity_uuid = fmdoc.entity_1_entity_uuid
    LEFT JOIN (
      SELECT entity_1_entity_uuid,max(created_at) as created_at
      FROM main.document
      WHERE document_type_id=1000260004
      GROUP BY entity_1_entity_uuid
    ) as pmdoc on pmdoc.entity_1_entity_uuid = staff.staff_id
    LEFT JOIN main.document as pd on pd.entity_1_entity_uuid = pmdoc.entity_1_entity_uuid and pd.entity_1_entity_uuid = staff.staff_id and pd.created_at = pmdoc.created_at
    LEFT JOIN (
      SELECT entity_1_entity_uuid,max(created_at) as created_at
      FROM main.document
      WHERE document_type_id=1000260004
      GROUP BY entity_1_entity_uuid
    ) as pmdoc2 on pmdoc2.entity_1_entity_uuid = sa.staff_id and sa.staff_id is not null
    LEFT JOIN main.document as pd2 on pd2.entity_1_entity_uuid = pmdoc2.entity_1_entity_uuid and pd2.entity_1_entity_uuid = sa.staff_id and pd2.created_at = pmdoc2.created_at
  
    {{#existInSet user_type notUsersForvill}}
    
      {{else}}
      JOIN main.entity_geography as ueg on ueg.entity_type_id = :user_type
      and ueg.geography_type_id = 1000320004 and ueg.geography_id = vg.village_id 
      and ueg.entity_uuid = :user_id and ueg.active = 1000100001
  
  {{/existInSet}}
   
  
    WHERE 1 = 1
     {{#if calendar_id}}
       AND cc.care_calendar_id ='{{calendar_id}}'
    {{/if}} LIMIT 1`;