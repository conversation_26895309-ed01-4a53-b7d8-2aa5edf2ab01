const { CONST_ID_TYPE_STAFF_BACKOFFICE } = require("../../utils/constant");
/**
 * Typeorm-placeholders:
 * Configure these using setParameters method of type-orm as per use
 * 1. user_type
 */
module.exports = `
WITH STAFF_VILLAGES AS (
	SELECT DISTINCT(geography_id) as village_id from main.entity_geography 
	WHERE geography_type_id = 1000320004 and active = 1000100001
	)
SELECT 
village_id as id ,
village_name_l10n as name_l10n 
FROM main.ref_village 
    WHERE village_id in (SELECT village_id FROM STAFF_VILLAGES)
    {{#ifNotEquals user_type ${CONST_ID_TYPE_STAFF_BACKOFFICE}}}
        AND 1=0
    {{/ifNotEquals}}
`