const { 
    FARMER_TYPE_CALENDAR,
    CONST_ACTIVITY_ID_CMT,
    COMPLETED_ACTIVITY_STATUS,
    CANCELLED_ACTIVITY_STATUS,
    ABANDONED_ACTIVITY_STATUS,
    ACTIVE    
} = require("../../utils/constant");

module.exports = `

SELECT 
	care_calendar.care_calendar_id,
    {{#ifEquals IS_POSTGRES 1}}
	    care_calendar.activity_date::DATE,
    {{else}}
    care_calendar.activity_date,
    {{/ifEquals}}
	care_calendar.activity_id,
	ref_activity.activity_name
FROM 
	main.care_calendar
		LEFT JOIN main.ref_activity
			ON
				care_calendar.activity_id = ref_activity.activity_id
WHERE				
	care_calendar.entity_uuid = :user_id
	AND
	care_calendar.entity_type_id = ${FARMER_TYPE_CALENDAR}
	AND
	care_calendar.activity_id IN (${CONST_ACTIVITY_ID_CMT})
	AND
	care_calendar.calendar_activity_status NOT IN (${COMPLETED_ACTIVITY_STATUS}, ${CANCELLED_ACTIVITY_STATUS}, ${ABANDONED_ACTIVITY_STATUS})
	AND
	care_calendar.active = ${ACTIVE}
	AND
    {{#ifEquals IS_POSTGRES 1}}
	    care_calendar.activity_date BETWEEN CURRENT_DATE AND CURRENT_DATE + 14
    {{else}}
        care_calendar.activity_date BETWEEN date('now') AND date('now', 'start of day', '14 day')
    {{/ifEquals}}
ORDER BY
	care_calendar.activity_date

`