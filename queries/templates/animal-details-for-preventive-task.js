const { record_statuses, classifiers, staff_activity_statuses } = require('../../ENUMS.js')

module.exports = `

SELECT 
	animal.animal_id,
	animal.animal_visual_id,
	ear_tag_classification.value_string_256 as ear_tag,
	animal_type_classification.value_reference_id as animal_type_id,
	MAX(care_calendar.activity_date) as activity_last_done_on
FROM 
	main.animal
LEFT JOIN main.animal_classification as ear_tag_classification
	ON
		ear_tag_classification.animal_id = animal.animal_id
  		AND 
		ear_tag_classification.classifier_id = ${classifiers.EAR_TAG_NUMBER}
  		AND 
		ear_tag_classification.active = ${record_statuses.ACTIVE}
LEFT JOIN main.animal_classification as animal_type_classification
	ON
		animal_type_classification.animal_id = animal.animal_id
  		AND 
		animal_type_classification.classifier_id = ${classifiers.TYPE_OF_ANIMAL}
  		AND 
		animal_type_classification.active = ${record_statuses.ACTIVE}
LEFT JOIN main.care_calendar
	ON
		care_calendar.entity_uuid = animal.animal_id
		AND
		care_calendar.activity_id = :activity_id
		AND
		care_calendar.calendar_activity_status = ${staff_activity_statuses.VISIT_COMPLETED}	
WHERE
	animal.animal_id IN (:...animal_ids)
	AND
	animal.active = ${record_statuses.ACTIVE}
GROUP BY
	animal.animal_id,
	animal.animal_visual_id,
	ear_tag_classification.value_string_256,
	animal_type_classification.value_reference_id
`;