module.exports = `{{!-- 
    Typeorm-placeholders:
    Configure these using setParameters method of type-orm as per use
    1. user_type [required]
    2. user_id [required]
    3. f_farmer_name [optional]
    4. f_village [optional] 
    4a. f_village_id_array [optional] 
    5. activity_category [optional]
    6. f_activity_status [optional]
    7. start_date [optional]
    8. end_date [optional]
    9. f_paravet [optional]
    10. IS_POSTGRES [required]
    
    Template-variables:
    Pass these variables while invoking the template function
    1. user_type [required]
    2. f_farmer_name [optional]
    3. f_village [optional] 
    3a. f_village_id_array [optional] 
    4. activity_category [optional]
    5. f_activity_status [optional]
    6. start_date [optional]
    7. end_date [optional]
    8. f_paravet [optional]
    9. IS_POSTGRES [required]
    
        --}}
    WITH active_customers as (
        select 
            customer_id
        from
            main.customer
            left join main.entity_relationship
                on
                    entity_relationship.entity_1_entity_uuid = customer.customer_id
                    and
                    entity_relationship.entity_relationship_type_id = 1000210004
            left join main.animal
                on
                    entity_relationship.entity_2_entity_uuid = animal.animal_id
        where
            animal.active = 1000100001 
        group by
            customer_id
        having count(*) > 0
    ),
    
    ALL_CC AS (
      SELECT
        CUSTOMER.customer_id AS customer_id,
        CUSTOMER.customer_name_l10n AS customer_name,
        CUSTOMER.customer_visual_id AS customer_visual_id,
        CUSTOMER.mobile_number AS customer_mobile_number,
        CUSTOMER_CLASSIFICATION.value_reference_id AS village_id,
        REF_VILLAGE.village_name_l10n AS village_name,
        REF_TALUK.taluk_id AS taluk_id,
        REF_TALUK.taluk_name_l10n AS taluk_name,
        REF_DISTRICT.district_id AS district_id,
        REF_DISTRICT.district_name_l10n AS district_name,
        REF_STATE.state_id AS state_id,
        REF_STATE.state_name_l10n AS state_name,
        ANIMAL.animal_id AS animal_id,
        ANIMAL.animal_visual_id AS animal_visual_id,
        REF_REFERENCE.reference_name AS animal_type,
        REF_REFERENCE.reference_name_l10n AS animal_type_json,
        REF_ANIMAL_NAME.value_string_256 AS animal_name,
        REF_ANIMAL_EAR_TAG.value_string_256 AS animal_ear_tag,
        COALESCE(STAFF_ACTIVITY.staff_id, STAFF.staff_id) AS paravet_id,
        COALESCE(STAFF_OVERRIDE.staff_name_l10n, STAFF.staff_name_l10n) AS paravet_name,
        COALESCE(STAFF_OVERRIDE.mobile_number, STAFF.mobile_number) AS paravet_mobile_number,
        CARE_CALENDAR.care_calendar_id AS care_calendar_id,
        CARE_CALENDAR.activity_date AS visit_date,
        CARE_CALENDAR.activity_id AS activity_id,
        REF_ACTIVITY.activity_category AS activity_category,
        REF_ACTIVITY.activity_name AS activity_name,
        REF_ACTIVITY.activity_name_l10n AS activity_name_json,
        CARE_CALENDAR.calendar_activity_status AS activity_status_id,
        REF_ACTIVITY_STATUS.reference_name AS activity_status,
        REF_ACTIVITY_STATUS.reference_name_l10n AS activity_status_json,
        CARE_CALENDAR_CLASSIFICATION.value_string_256 AS activity_complaint_number,
        CASE 
            WHEN 
                REF_ACTIVITY.activity_category IN (1000270002, 1000270003) 
            THEN 1
            ELSE 
                -1
        END AS activity_priority
      FROM 
        main.CARE_CALENDAR
      LEFT JOIN main.CARE_CALENDAR_CLASSIFICATION
        ON
        CARE_CALENDAR.care_calendar_id = CARE_CALENDAR_CLASSIFICATION.care_calendar_id
        AND
        CARE_CALENDAR_CLASSIFICATION.classifier_id = 2000000125
      LEFT JOIN main.REF_ACTIVITY
        ON
        CARE_CALENDAR.activity_id = REF_ACTIVITY.activity_id
      LEFT JOIN main.REF_REFERENCE REF_ACTIVITY_STATUS
        ON
        REF_ACTIVITY_STATUS.reference_id = CARE_CALENDAR.calendar_activity_status
      LEFT JOIN main.ENTITY_RELATIONSHIP 
        ON 
        ENTITY_RELATIONSHIP.entity_2_entity_uuid = CARE_CALENDAR.entity_uuid
        AND
        ENTITY_RELATIONSHIP.entity_relationship_type_id = 1000210004
        AND
        CARE_CALENDAR.entity_type_id = 1000460002
      LEFT JOIN main.CUSTOMER
        ON 
        CUSTOMER.customer_id = ENTITY_RELATIONSHIP.entity_1_entity_uuid
        OR (
            CUSTOMER.customer_id = CARE_CALENDAR.entity_uuid
            AND
            CARE_CALENDAR.entity_type_id = 1000460001
        )	
      LEFT JOIN main.CUSTOMER_CLASSIFICATION 
        ON 
        CUSTOMER_CLASSIFICATION.customer_id = CUSTOMER.customer_id
        AND
        CUSTOMER_CLASSIFICATION.classifier_id = 2000000055
      LEFT JOIN main.REF_VILLAGE
        ON
        REF_VILLAGE.village_id = CUSTOMER_CLASSIFICATION.value_reference_id
      LEFT JOIN main.REF_TALUK
        ON
        REF_TALUK.taluk_id = REF_VILLAGE.taluk_id
      LEFT JOIN main.REF_DISTRICT
        ON
        REF_DISTRICT.district_id = REF_TALUK.district_id
      LEFT JOIN main.REF_STATE
        ON
        REF_STATE.state_id = REF_DISTRICT.state_id
      LEFT JOIN main.ENTITY_GEOGRAPHY 
        ON 
        ENTITY_GEOGRAPHY.geography_id = REF_VILLAGE.village_id
        AND
        ENTITY_GEOGRAPHY.geography_type_id = 1000320004 
        AND
        ENTITY_GEOGRAPHY.entity_type_id = 1000230004
        AND
        ENTITY_GEOGRAPHY.active = 1000100001
      LEFT JOIN main.STAFF
        ON
        STAFF.staff_id = ENTITY_GEOGRAPHY.entity_uuid
      LEFT JOIN main.STAFF_ACTIVITY
        ON
        STAFF_ACTIVITY.care_calendar = CARE_CALENDAR.care_calendar_id
      LEFT JOIN main.STAFF STAFF_OVERRIDE
        ON
        STAFF_OVERRIDE.staff_id = STAFF_ACTIVITY.staff_id
      LEFT JOIN main.ANIMAL
        ON
        ANIMAL.animal_id = ENTITY_RELATIONSHIP.entity_2_entity_uuid
      LEFT JOIN main.animal_classification REF_ANIMAL_TYPE
        ON
        REF_ANIMAL_TYPE.animal_id = ANIMAL.animal_id
        AND
        REF_ANIMAL_TYPE.classifier_id = 2000000035        
      LEFT JOIN main.REF_REFERENCE
        ON
        REF_REFERENCE.reference_id = REF_ANIMAL_TYPE.value_reference_id
      LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_NAME
        ON
        REF_ANIMAL_NAME.animal_id = ANIMAL.animal_id
        AND
        REF_ANIMAL_NAME.classifier_id = 2000000033
      LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_EAR_TAG
        ON
        REF_ANIMAL_EAR_TAG.animal_id = ANIMAL.animal_id
        AND
        REF_ANIMAL_EAR_TAG.classifier_id = 2000000034
      LEFT JOIN main.entity_relationship as er on er.entity_2_entity_uuid=CARE_CALENDAR.care_calendar_id and er.entity_relationship_type_id = :entity_relationship_type_id
      WHERE
        CARE_CALENDAR.entity_type_id IN (1000460001, 1000460002)
        AND
        CARE_CALENDAR.calendar_activity_status not in (1000300051, 1000300052)
        {{#ifEquals task_status "completed"}}
          AND CARE_CALENDAR.calendar_activity_status = 1000300050
        {{/ifEquals}}		
        {{#ifEquals task_status "opened"}}
        AND CARE_CALENDAR.calendar_activity_status != 1000300050
        {{/ifEquals}}	
        AND
        CUSTOMER.customer_id in (
          select customer_id from active_customers
        ) 
        AND
        (
        ANIMAL.active is null 
        OR 
        ANIMAL.active = 1000100001
        )
        AND er.entity_1_entity_uuid IN (:...user_ids)    
        {{#if f_farmer_name}}
        {{#ifEquals IS_POSTGRES 1}}
        AND (
          UPPER(CUSTOMER.customer_name_l10n->>'ul') LIKE :f_farmer_name
          OR
          UPPER(CUSTOMER.customer_name_l10n->>'en') LIKE :f_farmer_name
          OR
          UPPER(CUSTOMER.customer_name_l10n->>'mr') LIKE :f_farmer_name
          OR
          UPPER(CUSTOMER.customer_name_l10n->>'gj') LIKE :f_farmer_name
          OR
          UPPER(CUSTOMER.customer_name_l10n->>'hi') LIKE :f_farmer_name
        )
        {{else}}
        AND UPPER(CUSTOMER.customer_name_l10n) LIKE :f_farmer_name
        {{/ifEquals}}
        {{/if}}
    
        {{#if f_activity_category}}
        AND REF_ACTIVITY.activity_category IN (:...f_activity_category)
        {{/if}}
    
        {{#if start_date }}
        AND CARE_CALENDAR.activity_date >= :start_date
        {{/if}}
        
        {{#if end_date}}
        AND CARE_CALENDAR.activity_date <= :end_date
        {{/if}}
        
        {{#ifEquals IS_POSTGRES 1}}
        {{#bothNotExist start_date end_date}}
        AND CARE_CALENDAR.activity_date::DATE < current_date + 15
        {{/bothNotExist}}
        {{else}}
        {{#bothNotExist start_date end_date}}
        AND CARE_CALENDAR.activity_date < date('now', 'start of day', '+15 day')
        {{/bothNotExist}}
        {{/ifEquals}}
    
        {{#if f_activity_status}}
        AND CARE_CALENDAR.calendar_activity_status IN (:...f_activity_status)
        {{/if}}
    
        {{#if f_village}}
        AND CUSTOMER_CLASSIFICATION.value_reference_id IN (:...f_village)
        {{/if}}
    
        {{#if f_village_id_array}}
        AND CUSTOMER_CLASSIFICATION.value_reference_id IN (:...f_village_id_array)
        {{/if}}
    
    ) 
      
    select 
    ALL_CC.care_calendar_id,
         ALL_CC.animal_ear_tag,
         ALL_CC.customer_id,
         ALL_CC.customer_name,
         ALL_CC.customer_visual_id,
         ALL_CC.customer_mobile_number as customer_mobile_number,
         ALL_CC.village_name,
         ALL_CC.taluk_name,
         ALL_CC.visit_date,
         0 as animal_count,
         1 as activity_count,
         ALL_CC.activity_id AS first_cc_activity_id,
         ALL_CC.activity_status AS fcca_status_id,
         ccc_complaint.value_string_256 AS fcca_complaint_number,
         FCC_REF_ACTIVITY.activity_category AS fcca_activity_category_id,
         FCC_REF_ACTIVITY.activity_name AS fcca_name,
         FCC_REF_ACTIVITY.activity_name_l10n AS fcca_name_json,
         FCC_REF_ACTIVITY_STATUS.reference_name AS fcca_status,
         FCC_REF_ACTIVITY_STATUS.reference_name_l10n AS fcca_status_json,	
         ALL_CC.activity_id AS first_ncc_activity_id,
         ALL_CC.activity_status_id as calendar_activity_status,
         null AS fncca_status_id,
         null AS fncca_complaint_number,
         null AS fncca_activity_category_id,
         null fncca_name,
         null fncca_name_json,
         null AS fncca_status,
        null AS fncca_status_json
      FROM  ALL_CC  
      LEFT JOIN main.care_calendar_classification as ccc_complaint on ALL_CC.care_calendar_id=
      ccc_complaint.care_calendar_id and ccc_complaint.classifier_id = 2000000125
      LEFT JOIN main.REF_ACTIVITY FCC_REF_ACTIVITY
           ON
             ALL_CC.activity_id = FCC_REF_ACTIVITY.activity_id
        LEFT JOIN main.REF_REFERENCE FCC_REF_ACTIVITY_STATUS
           ON
             FCC_REF_ACTIVITY_STATUS.reference_id = ALL_CC.activity_status_id
    ORDER BY
      ALL_CC.visit_date
        `
    