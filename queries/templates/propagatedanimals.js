module.exports = `SELECT
  a.animal_visual_id,
  etag.value_string_256 as eartag,
  atr.reference_name_l10n
  FROM
  main.care_calendar as cc
  JOIN main.care_calendar_classification as lnk ON
    cc.care_calendar_id = lnk.care_calendar_id
    AND lnk.classifier_id = {{LINKED_CARE_CALENDAR_CLASSIFIER}}
    AND lnk.active = {{ACTIVE}}
    AND lnk.value_reference_uuid = '{{farmerCalendarId}}'
    AND cc.entity_type_id = {{ANIMAL_TYPE_CALENDAR}}

  JOIN main.animal as a ON
    a.animal_id  = cc.entity_uuid
    AND a.active = {{ACTIVE}}

  JOIN main.animal_classification as etag ON
    etag.classifier_id = {{ANIMAL_EAR_TAG_CLASSIFIER}}
    AND etag.animal_id = a.animal_id
    AND etag.active = {{ACTIVE}}

  JOIN main.animal_classification as atype ON
    atype.classifier_id = {{ANIMAL_TYPE_CLASSIFIER}}
    AND atype.animal_id = a.animal_id
    AND atype.active = {{ACTIVE}}

  JOIN main.ref_reference AS atr ON
   atr.reference_id = atype.value_reference_id
   AND atr.active = {{ACTIVE}}`