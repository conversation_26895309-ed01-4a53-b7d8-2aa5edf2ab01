const { classifiers, record_statuses, entity_type, relationship_types } = require('../../ENUMS');

module.exports = `
  
  SELECT
    cc.created_at,
    cc.updated_at,
    cc.activity_id,
    cc.entity_uuid,
    cc.entity_type_id,
    cc.activity_date AS activity_date,
    cc.completion_date as completion_date, 
    cc.care_calendar_id,
    cc.calendar_activity_status,
    cc.system_task_completion_time,
    cc.visit_schedule_time,
    cc.visit_creation_time,
    cc.user_task_completion_time,
    null as expiry_date,
    tn.value_string_256 as ticket_number,
    acat.activity_name_l10n,
    acat.activity_category,
    a.animal_visual_id,
    a.animal_id,
    a.active as animal_active_status,
    etag.value_string_256 as ear_tag,
    atype.reference_name_l10n as animal_type_json,
    aname.value_string_256 as animal_name,
    animal_subscription.value_reference_id as animal_subscription_plan,
    astatus.reference_name_l10n as calendar_activity_status_name_json,
    c.customer_id,
    c.customer_name_l10n,
    c.customer_visual_id,
    c.mobile_number AS farmer_contact,
    vg.village_name_l10n,
    COALESCE(sr_value.reference_name,'Krushal') as service_request_type_name,
    CASE 
      WHEN diagnosis.value_date IS NOT NULL THEN 'true' 
      ELSE 'false'
    END as diagnosis_completed,
    CASE 
      WHEN observation.value_date IS NOT NULL THEN 'true' 
      ELSE 'false'
    END as observation_completed,
    CASE 
      WHEN administered.value_date IS NOT NULL THEN 'true'
      ELSE 'false'
    END as administered_completed,
    CASE 
      WHEN published.value_date IS NOT NULL THEN 'true' 
      ELSE 'false'
    END as published_completed,
    CASE 
      WHEN form_completion_status.value_date IS NOT NULL THEN 'true'
      ELSE 'false' 
    END AS form_status,
    COALESCE(obs_version.value_string_256,'v2') as observation_version
  FROM main.care_calendar as cc
    LEFT JOIN 
      main.care_calendar_classification as obs_version 
        ON 
          obs_version.care_calendar_id =cc.care_calendar_id 
          and 
          obs_version.classifier_id = ${classifiers.OBSERVATION_VERSION}
          and 
          obs_version.active = ${record_statuses.ACTIVE} 
    LEFT JOIN 
      main.care_calendar_classification as tn 
        ON 
          tn.care_calendar_id = cc.care_calendar_id 
          and 
          tn.classifier_id = ${classifiers.COMPLAINT_NUMBER} 
          and 
          tn.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.care_calendar_classification as published 
        ON 
          published.care_calendar_id = cc.care_calendar_id 
          and 
          published.classifier_id = ${classifiers.PRESCRIPTION_PUBLISHED_AT} 
          and 
          published.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.care_calendar_classification as administered 
        ON 
          administered.care_calendar_id = cc.care_calendar_id 
          and 
          administered.classifier_id = ${classifiers.PRESCRIPTION_ADMINISTERED_AT} 
          and 
          administered.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.care_calendar_classification as diagnosis 
        ON 
          diagnosis.care_calendar_id = cc.care_calendar_id 
          and 
          diagnosis.classifier_id = ${classifiers.DIAGNOSIS_UPDATED_AT} 
          and 
          diagnosis.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.care_calendar_classification as observation 
        ON 
          observation.care_calendar_id = cc.care_calendar_id 
          and 
          observation.classifier_id = ${classifiers.OBSERVATION_UPDATED_AT} 
          and 
          observation.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.care_calendar_classification as form_completion_status 
        ON 
          form_completion_status.care_calendar_id = cc.care_calendar_id 
          and 
          form_completion_status.classifier_id = ${classifiers.ACTIVITY_FORM_COMPLETION} 
          and 
          form_completion_status.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.care_calendar_classification as sr_type 
        ON 
          sr_type.care_calendar_id = cc.care_calendar_id 
          and 
          sr_type.classifier_id = ${classifiers.SERVICE_REQUEST_TYPE}
          and 
          sr_type.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.ref_reference as sr_value 
        ON 
          sr_value.reference_id = sr_type.value_reference_id 
          and
          sr_value.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.ref_reference as astatus 
        ON 
          astatus.reference_id = cc.calendar_activity_status 
          and 
          astatus.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.ref_activity as acat 
        ON 
          acat.activity_id = cc.activity_id 
          and 
          acat.active = ${record_statuses.ACTIVE}
    JOIN 
      main.animal as a 
        on 
          a.animal_id = cc.entity_uuid 
          and 
          cc.entity_type_id = ${entity_type.ANIMAL} 
          and
          a.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.animal_classification as ac_animal_type 
        on 
          ac_animal_type.animal_id = a.animal_id 
          and 
          ac_animal_type.classifier_id = ${classifiers.TYPE_OF_ANIMAL} 
          and 
          ac_animal_type.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.ref_reference as atype 
        on 
          atype.reference_id = ac_animal_type.value_reference_id 
          and 
          atype.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.animal_classification as etag 
        on 
          etag.animal_id = a.animal_id 
          and 
          etag.classifier_id = ${classifiers.EAR_TAG_NUMBER} 
          and 
          etag.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.animal_classification as aname 
        on 
          aname.animal_id = a.animal_id 
          and 
          aname.classifier_id = ${classifiers.NAME_OF_ANIMAL}
          and
          aname.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.animal_classification as animal_subscription 
        on 
          animal_subscription.animal_id = a.animal_id 
          and 
          animal_subscription.classifier_id = ${classifiers.SUBSCRIPTION_PLAN}
          and
          animal_subscription.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.entity_relationship as er 
        on 
          er.entity_relationship_type_id = ${relationship_types.FARMER_TO_ANIMAL} 
          and 
          er.entity_2_entity_uuid = a.animal_id 
          and 
          er.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.customer as c 
        on 
          c.customer_id = er.entity_1_entity_uuid 
          and 
          c.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.customer_classification as cv 
        on 
          cv.classifier_id = ${classifiers.VILLAGE_FARMER_BELONGS_TO} 
          and 
          cv.customer_id = c.customer_id 
          and 
          cv.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.ref_village as vg 
        on 
          vg.village_id = cv.value_reference_id
  WHERE
    cc.care_calendar_id = '{{calendar_id}}'
    AND 
    cc.active = ${record_statuses.ACTIVE}
  
  UNION ALL
  
  SELECT
    cc.created_at,
    cc.updated_at,
    cc.activity_id,
    cc.entity_uuid,
    cc.entity_type_id,
    cc.activity_date AS activity_date,
    cc.completion_date as completion_date, 
    cc.care_calendar_id,
    cc.calendar_activity_status,
    cc.system_task_completion_time,
    cc.visit_schedule_time,
    cc.visit_creation_time,
    cc.user_task_completion_time,
    ccc.value_date as expiry_date,
    tn.value_string_256 as ticket_number,
    acat.activity_name_l10n,
    acat.activity_category,
    null as animal_visual_id,
    null as animal_id,
    null as animal_active_status,
    null as ear_tag,
    null as animal_type_json,
    null as animal_name,
    null as animal_subscription_plan,
    astatus.reference_name_l10n as calendar_activity_status_name_json, 
    c.customer_id,
    c.customer_name_l10n,
    c.customer_visual_id,
    c.mobile_number AS farmer_contact,
    vg.village_name_l10n,
    COALESCE(sr_value.reference_name,'Krushal') as service_request_type_name,
    CASE 
      WHEN diagnosis.value_date IS NOT NULL THEN 'true' 
      ELSE 'false'
    END as diagnosis_completed,
    CASE 
      WHEN observation.value_date IS NOT NULL THEN 'true' 
      ELSE 'false'
    END as observation_completed,
    CASE 
      WHEN administered.value_date IS NOT NULL THEN 'true'
      ELSE 'false'
    END as administered_completed,
    CASE 
      WHEN published.value_date IS NOT NULL THEN 'true' 
      ELSE 'false'
    END as published_completed,
    CASE 
      WHEN form_completion_status.value_date IS NOT NULL THEN 'true'
      ELSE 'false' 
    END AS form_status,
    COALESCE(obs_version.value_string_256,'v2') as observation_version
  FROM main.care_calendar as cc
    LEFT JOIN 
      main.care_calendar_classification as obs_version 
        ON 
          obs_version.care_calendar_id =cc.care_calendar_id 
          and 
          obs_version.classifier_id = ${classifiers.OBSERVATION_VERSION}
          and 
          obs_version.active = ${record_statuses.ACTIVE} 
    LEFT JOIN 
      main.care_calendar_classification as tn 
        ON 
          tn.care_calendar_id = cc.care_calendar_id 
          and 
          tn.classifier_id = ${classifiers.COMPLAINT_NUMBER} 
          and 
          tn.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.care_calendar_classification as published 
        ON 
          published.care_calendar_id = cc.care_calendar_id 
          and 
          published.classifier_id = ${classifiers.PRESCRIPTION_PUBLISHED_AT} 
          and 
          published.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.care_calendar_classification as administered 
        ON 
          administered.care_calendar_id = cc.care_calendar_id 
          and 
          administered.classifier_id = ${classifiers.PRESCRIPTION_ADMINISTERED_AT} 
          and 
          administered.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.care_calendar_classification as diagnosis 
        ON 
          diagnosis.care_calendar_id = cc.care_calendar_id 
          and 
          diagnosis.classifier_id = ${classifiers.DIAGNOSIS_UPDATED_AT} 
          and 
          diagnosis.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.care_calendar_classification as observation 
        ON 
          observation.care_calendar_id = cc.care_calendar_id 
          and 
          observation.classifier_id = ${classifiers.OBSERVATION_UPDATED_AT} 
          and 
          observation.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.care_calendar_classification as form_completion_status 
        ON 
          form_completion_status.care_calendar_id = cc.care_calendar_id 
          and 
          form_completion_status.classifier_id = ${classifiers.ACTIVITY_FORM_COMPLETION} 
          and 
          form_completion_status.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.care_calendar_classification as sr_type 
        ON 
          sr_type.care_calendar_id = cc.care_calendar_id 
          and 
          sr_type.classifier_id = ${classifiers.SERVICE_REQUEST_TYPE}
          and 
          sr_type.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.ref_reference as sr_value 
        ON 
          sr_value.reference_id = sr_type.value_reference_id 
          and
          sr_value.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.ref_reference as astatus 
        ON 
          astatus.reference_id = cc.calendar_activity_status 
          and 
          astatus.active = ${record_statuses.ACTIVE}
    LEFT JOIN 
      main.ref_activity as acat 
        ON 
          acat.activity_id = cc.activity_id 
          and 
          acat.active = ${record_statuses.ACTIVE}
    LEFT JOIN main.care_calendar_classification as ccc 
      on 
        cc.care_calendar_id = ccc.care_calendar_id
        AND 
        ccc.classifier_id = ${classifiers.CARE_CALENDAR_EXPIRATION_DATE}
        AND 
        ccc.active = ${record_statuses.ACTIVE}
    JOIN main.customer as c 
      on 
        c.customer_id = cc.entity_uuid 
        and 
        cc.entity_type_id = ${entity_type.CUSTOMER} 
        and 
        c.active = ${record_statuses.ACTIVE}
    LEFT JOIN main.customer_classification as cv 
      on 
        cv.customer_id = c.customer_id 
        and 
        cv.classifier_id = ${classifiers.VILLAGE_FARMER_BELONGS_TO} 
        and
        cv.active = ${record_statuses.ACTIVE}
    LEFT JOIN main.ref_village as vg 
      on 
        vg.village_id = cv.value_reference_id
WHERE
  cc.care_calendar_id = '{{calendar_id}}'
  AND 
  cc.active = ${record_statuses.ACTIVE}

LIMIT 1
`;