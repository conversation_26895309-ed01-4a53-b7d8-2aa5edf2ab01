const { 
    FARMER_TO_ANIMAL, 
    ANIMAL_TYPE_CALENDAR,
    COMPLETED_ACTIVITY_STATUS,
    CANCELLED_ACTIVITY_STATUS,
    ABANDONED_ACTIVITY_STATUS,
    ACTIVE, 
    VILLAGE_CLASSIFIER, 
    FARMER_TYPE_CALENDAR,
    VILLAGE_TYPE_ID, 
    PR<PERSON><PERSON>NTIVE,
    CURATIVE,
    REPRO<PERSON><PERSON>TIVE,
	DOCUMENT_STAFF_THUMBNAIL_TYPE,
	CONST_ID_TYPE_CUSTOMER_FARMER
} = require('../../utils/constant')

module.exports = `
WITH 
    ALL_CC AS
        (
            SELECT
	            care_calendar.care_calendar_id,
	            care_calendar.activity_id,
	            ref_activity.activity_category,
	            care_calendar.activity_date,
	            customer.customer_id
            FROM main.care_calendar
                INNER JOIN main.ref_activity 
		            ON 
                        ref_activity.activity_id = care_calendar.activity_id 
	            INNER JOIN main.animal 
		            ON 
                        care_calendar.entity_uuid = animal.animal_id
	            INNER JOIN main.entity_relationship 
		            ON 
			            entity_relationship.entity_2_entity_uuid = animal.animal_id
			            AND
			            entity_relationship.entity_relationship_type_id = ${FARMER_TO_ANIMAL}
	            INNER JOIN main.customer 
		            ON 
			            entity_relationship.entity_1_entity_uuid = customer.customer_id
            WHERE
	            care_calendar.entity_type_id = ${ANIMAL_TYPE_CALENDAR}
	            AND
	            care_calendar.calendar_activity_status NOT IN (${COMPLETED_ACTIVITY_STATUS}, ${CANCELLED_ACTIVITY_STATUS}, ${ABANDONED_ACTIVITY_STATUS})
	            AND
	            care_calendar.activity_date <= '{{end_date}}'
	            AND
	            care_calendar.active = ${ACTIVE}
	            AND
	            animal.active = ${ACTIVE}
	            AND
	            customer.active = ${ACTIVE}
				AND
				customer.customer_type_id = ${CONST_ID_TYPE_CUSTOMER_FARMER}		

            UNION ALL

            SELECT
	            care_calendar.care_calendar_id,
	            care_calendar.activity_id,	
	            ref_activity.activity_category,	
	            care_calendar.activity_date,
	            customer.customer_id
            FROM main.care_calendar
                INNER JOIN main.ref_activity 
		            ON 
                        ref_activity.activity_id = care_calendar.activity_id 	
	            INNER JOIN main.customer 
		            ON 
			            care_calendar.entity_uuid = customer.customer_id
            WHERE
	            care_calendar.entity_type_id = ${FARMER_TYPE_CALENDAR}
	            AND
	            care_calendar.calendar_activity_status NOT IN (${COMPLETED_ACTIVITY_STATUS}, ${CANCELLED_ACTIVITY_STATUS}, ${ABANDONED_ACTIVITY_STATUS})
	            AND
				care_calendar.activity_date <= '{{end_date}}'
	            AND
	            care_calendar.active = ${ACTIVE}
	            AND
	            customer.active = ${ACTIVE}
				AND
				customer.customer_type_id = ${CONST_ID_TYPE_CUSTOMER_FARMER}		
            GROUP BY
	            care_calendar.care_calendar_id,
	            care_calendar.activity_id,	
	            ref_activity.activity_category,		
	            care_calendar.activity_date,
	            customer.customer_id
        )
{{#if all_stats}}    		
SELECT
 	COUNT(
 		CASE 
			WHEN
 		  		activity_category in (${CURATIVE}, ${REPRODUCTIVE})
 		  		AND
				activity_date <= '{{start_date}}'
			THEN 
				care_calendar_id
 		END) AS critical_count,
 	COUNT(
 		CASE 
			WHEN
         		activity_category in (${CURATIVE}, ${REPRODUCTIVE})
         		AND
				activity_date <= '{{end_date}}'
  			THEN 
				care_calendar_id
 		END) AS critical_count_15,
 	COUNT(
 		CASE 
			WHEN
         		activity_category in (${PREVENTIVE})
         		AND
				activity_date <= '{{start_date}}'
   			THEN 
				care_calendar_id
 		END) AS preventive_count,
 	COUNT(
 		CASE 
			WHEN
         		activity_category in (${PREVENTIVE})
         		AND
		 		activity_date <= '{{end_date}}'
 			THEN 
				care_calendar_id
 		END) AS preventive_count_15,
 	COUNT(DISTINCT(
 		CASE 
			WHEN activity_date <= '{{start_date}}'
   			THEN all_cc.customer_id 
 		END)) AS farmer_count,
 	COUNT(DISTINCT(
 		CASE 
			WHEN activity_date <= '{{end_date}}'
 			THEN all_cc.customer_id 
 		END)) AS farmer_count_15	
FROM
 	all_cc
{{#if stats_by_user}}    
 		INNER JOIN main.customer_classification
 			ON
 				customer_classification.customer_id = all_cc.customer_id
 				AND
 				customer_classification.classifier_id = ${VILLAGE_CLASSIFIER}
 		INNER JOIN main.entity_geography
 			ON
 				entity_geography.geography_id = customer_classification.value_reference_id
 				AND
 				entity_geography.geography_type_id = ${VILLAGE_TYPE_ID}
 				AND
 				entity_geography.active = ${ACTIVE}
 WHERE
 	entity_geography.entity_uuid = :user_id
{{/if}}
{{/if}}

{{#if stats_by_staff_type}}    		

SELECT 
	staff_id, 
 	staff_name_l10n,
	document.document_id,
	document.document_information,
	document.client_document_information,
	COUNT(
		CASE 
			WHEN
				activity_category in (${CURATIVE}, ${REPRODUCTIVE})
			  	AND
			  	activity_date <= '{{start_date}}'
			THEN 
			  	care_calendar_id
		END) AS critical_count,
	COUNT(
		CASE 
			WHEN
				activity_category in (${CURATIVE}, ${REPRODUCTIVE})
				AND
				activity_date <= '{{end_date}}'
 			THEN 
				care_calendar_id
		END) AS critical_count_15,
	COUNT(
		CASE 
			WHEN 
				activity_category in (${PREVENTIVE})
				AND
				activity_date <= '{{start_date}}'
  			THEN 
				care_calendar_id
		END) AS preventive_count,
	COUNT(
		CASE 
			WHEN 
				activity_category in (${PREVENTIVE})
				AND
				activity_date <= '{{end_date}}'
			THEN 
				care_calendar_id
		END) AS preventive_count_15,
	COUNT(DISTINCT(
		CASE 
			WHEN activity_date <= '{{start_date}}'
  			THEN all_cc.customer_id 
		END)) AS farmer_count,
	COUNT(DISTINCT(
		CASE 
			WHEN activity_date <= '{{end_date}}'
			THEN all_cc.customer_id 
		END)) AS farmer_count_15	
FROM 
	main.staff
		LEFT JOIN main.document
			ON
				document.entity_1_entity_uuid = staff.staff_id
				AND
				document.document_type_id = ${DOCUMENT_STAFF_THUMBNAIL_TYPE}
				AND
				document.active = ${ACTIVE}
		INNER JOIN main.entity_geography
			ON
				entity_geography.entity_uuid = staff.staff_id
				AND
				entity_geography.geography_type_id = ${VILLAGE_TYPE_ID}
				AND
				entity_geography.active = ${ACTIVE}
		INNER JOIN main.customer_classification
			ON
				customer_classification.classifier_id = ${VILLAGE_CLASSIFIER}
				AND
				customer_classification.value_reference_id = entity_geography.geography_id
		INNER JOIN all_cc
			ON
				customer_classification.customer_id = all_cc.customer_id
		
{{#if stats_by_user}}    				
		INNER JOIN main.entity_geography user_geography
			ON
				user_geography.geography_id = customer_classification.value_reference_id
				AND
				user_geography.geography_type_id = ${VILLAGE_TYPE_ID}
				AND
				user_geography.active = ${ACTIVE}
{{/if}}
	WHERE
		staff.active = ${ACTIVE}
		AND
		staff.staff_type_id = :staff_type
{{#if stats_by_user}}    
		AND
		user_geography.entity_uuid = :user_id
{{/if}}		
	GROUP BY
 		staff_id, 
 		staff_name_l10n,
		document.document_id,
		document.document_information,
		document.client_document_information	
	ORDER BY
		critical_count DESC,
		critical_count_15 DESC		 
{{/if}}
`
