const {  
    ANIMAL_TYPE_CALENDAR,
    COMPLETED_ACTIVITY_STATUS,
    CANCELLED_ACTIVITY_STATUS,
    ABANDONED_ACTIVITY_STATUS,
    ACTIVE, 
    VILLAGE_CLASSIFIER, 
    ANIMAL_SUBSCRIPTION_DATE_CLASSIFIER,
    FARMER_TYPE_CALENDAR,
    PR<PERSON><PERSON><PERSON>IVE,
    CURATIVE,
    REPRO<PERSON><PERSON><PERSON><PERSON>,
	DOCUMENT_STAFF_THUMBNAIL_TYPE,
	PPU_PARAVET_TO_TASK,
	FARMER_TO_ANIMAL,
	CENTRALIZED_VET_TO_TASK,
} = require('../../utils/constant')

module.exports = `
WITH 
    ALL_CC AS
        (
            SELECT
	            care_calendar.care_calendar_id,
	            care_calendar.activity_id,
	            ref_activity.activity_category,
	            care_calendar.activity_date,
	            customer.customer_id as customer_id,
	            animal.animal_id	
            FROM main.care_calendar
                LEFT JOIN main.ref_activity 
		            ON 
                        ref_activity.activity_id = care_calendar.activity_id 
	            LEFT JOIN main.animal 
		            ON 
                        care_calendar.entity_uuid = animal.animal_id
	            LEFT JOIN main.entity_relationship 
		            ON 
			            entity_relationship.entity_2_entity_uuid = animal.animal_id
			            AND
			            entity_relationship.entity_relationship_type_id = ${FARMER_TO_ANIMAL}
	            LEFT JOIN main.customer 
		            ON 
			            entity_relationship.entity_1_entity_uuid = customer.customer_id
            WHERE
	            care_calendar.entity_type_id = ${ANIMAL_TYPE_CALENDAR}
	            AND
	            care_calendar.calendar_activity_status NOT IN (${COMPLETED_ACTIVITY_STATUS}, ${CANCELLED_ACTIVITY_STATUS}, ${ABANDONED_ACTIVITY_STATUS} )
	            AND
				{{#ifEquals IS_POSTGRES 1}}
	            care_calendar.activity_date <= current_timestamp + interval '{{number_of_days}} days'
				{{else}}
				care_calendar.activity_date <= date('now', 'start of day', '{{number_of_days}} day')
                {{/ifEquals}}
	            AND
	            care_calendar.active = ${ACTIVE}
	            AND
	            animal.active = ${ACTIVE}
	            AND
	            customer.active = ${ACTIVE}

            UNION ALL

            SELECT
	            care_calendar.care_calendar_id,
	            care_calendar.activity_id,	
	            ref_activity.activity_category,	
	            care_calendar.activity_date,
	            customer.customer_id as customer_id,
	            null as animal_id
            FROM main.care_calendar
                LEFT JOIN main.ref_activity 
		            ON 
                        ref_activity.activity_id = care_calendar.activity_id 	
	            LEFT JOIN main.customer 
		            ON 
			            care_calendar.entity_uuid = customer.customer_id
	            LEFT JOIN main.customer_classification
		            ON 
			            customer_classification.customer_id = customer.customer_id
			            AND
			            customer_classification.classifier_id = ${VILLAGE_CLASSIFIER}
	            LEFT JOIN main.entity_relationship 
		            ON 
			            entity_relationship.entity_1_entity_uuid = customer.customer_id
			            AND
			            entity_relationship.entity_relationship_type_id = ${FARMER_TO_ANIMAL}
	            LEFT JOIN main.animal 
		            ON 
			            entity_relationship.entity_2_entity_uuid = animal.animal_id
	            LEFT JOIN main.animal_classification 
		            ON
			            animal.animal_id = animal_classification.animal_id
			            AND
			            animal_classification.classifier_id = ${ANIMAL_SUBSCRIPTION_DATE_CLASSIFIER}	
            WHERE
	            care_calendar.entity_type_id = ${FARMER_TYPE_CALENDAR}
	            AND
	            care_calendar.calendar_activity_status NOT IN (${COMPLETED_ACTIVITY_STATUS}, ${CANCELLED_ACTIVITY_STATUS}, ${ABANDONED_ACTIVITY_STATUS} )
	            AND
				{{#ifEquals IS_POSTGRES 1}}
	            care_calendar.activity_date <= current_timestamp + interval '{{number_of_days}} days'
				{{else}}
				care_calendar.activity_date <= date('now', 'start of day', '{{number_of_days}} day')
                {{/ifEquals}}
	            AND
	            care_calendar.active = ${ACTIVE}
	            AND
	            customer.active = ${ACTIVE}
	            AND
	            animal_classification.animal_classification_id is not null
	            AND
	            animal.active = ${ACTIVE}
            GROUP BY
	            care_calendar.care_calendar_id,
	            care_calendar.activity_id,	
	            ref_activity.activity_category,		
	            care_calendar.activity_date,
	            customer.customer_id
        )
{{#if all_stats}}    		
SELECT
 	COUNT(
 		CASE WHEN
 		  	activity_category in (${CURATIVE},${REPRODUCTIVE})
 		  	AND
			   {{#ifEquals IS_POSTGRES 1}}
			   activity_date <= current_timestamp
			   {{else}}
			   activity_date <= date('now', 'start of day')
			   {{/ifEquals}}
			   THEN care_calendar_id
 	END) AS critical_count,
 	COUNT(
 		CASE WHEN
         activity_category in (${CURATIVE},${REPRODUCTIVE})
         AND
		 {{#ifEquals IS_POSTGRES 1}}
		 activity_date <= current_timestamp + interval '{{number_of_days}} days'
		 {{else}}
		 activity_date <= date('now', 'start of day', '{{number_of_days}} day')
		 {{/ifEquals}}
  THEN care_calendar_id
 	END) AS critical_count_15,
 	COUNT(
 		CASE WHEN
         activity_category in (${PREVENTIVE})
         AND
		 {{#ifEquals IS_POSTGRES 1}}
		 activity_date <= current_timestamp
		 {{else}}
		 activity_date <= date('now', 'start of day')
		 {{/ifEquals}}
   THEN care_calendar_id
 	END) AS preventive_count,
 	COUNT(
 		CASE WHEN
         activity_category in (${PREVENTIVE})
         AND
		 {{#ifEquals IS_POSTGRES 1}}
		 activity_date <= current_timestamp + interval '{{number_of_days}} days'
		 {{else}}
		 activity_date <= date('now', 'start of day', '{{number_of_days}} day')
		 {{/ifEquals}}
 		THEN care_calendar_id
 	END) AS preventive_count_15,
 	COUNT(DISTINCT(
 		CASE WHEN 
		 {{#ifEquals IS_POSTGRES 1}}
		 activity_date <= current_timestamp
		 {{else}}
		 activity_date <= date('now', 'start of day')
		 {{/ifEquals}}
   THEN all_cc.customer_id 
 	END)) AS farmer_count,
 	COUNT(DISTINCT(
 		CASE WHEN 
		 {{#ifEquals IS_POSTGRES 1}}
		 activity_date <= current_timestamp + interval '{{number_of_days}} days'
		 {{else}}
		 activity_date <= date('now', 'start of day', '{{number_of_days}} day')
		 {{/ifEquals}}
 		THEN all_cc.customer_id 
 	END)) AS farmer_count_15	
 FROM
 	all_cc
{{#if stats_by_user}}    
    LEFT JOIN main.entity_relationship as er_vet 
    on 
        er_vet.entity_relationship_type_id=${CENTRALIZED_VET_TO_TASK}
        AND 
        er_vet.entity_2_entity_uuid=all_cc.care_calendar_id
	LEFT JOIN main.staff ON staff.staff_id = er_vet.entity_1_entity_uuid
    WHERE
		staff.staff_id = :user_id AND staff.active=${ACTIVE}
{{/if}}
{{/if}}

{{#if stats_by_staff_type}}    		

SELECT 
	staff_id, 
 	staff_name_l10n,
	document.document_id,
	document.document_information,
	document.client_document_information,
	COUNT(
		CASE WHEN
			  activity_category in (${CURATIVE}, ${REPRODUCTIVE})
			  AND
			  {{#ifEquals IS_POSTGRES 1}}
			  activity_date <= current_timestamp
			  {{else}}
			  activity_date <= date('now', 'start of day')
			  {{/ifEquals}}
			  THEN care_calendar_id
	END) AS critical_count,
	COUNT(
		CASE WHEN
		activity_category in (${CURATIVE}, ${REPRODUCTIVE})
		AND
		{{#ifEquals IS_POSTGRES 1}}
		activity_date <= current_timestamp + interval '{{number_of_days}} days'
		{{else}}
		activity_date <= date('now', 'start of day', '{{number_of_days}} day')
		{{/ifEquals}}
 THEN care_calendar_id
	END) AS critical_count_15,
	COUNT(
		CASE WHEN
		activity_category in (${PREVENTIVE})
		AND
		{{#ifEquals IS_POSTGRES 1}}
		activity_date <= current_timestamp
		{{else}}
		activity_date <= date('now', 'start of day')
		{{/ifEquals}}
  THEN care_calendar_id
	END) AS preventive_count,
	COUNT(
		CASE WHEN
		activity_category in (${PREVENTIVE})
		AND
		{{#ifEquals IS_POSTGRES 1}}
		activity_date <= current_timestamp + interval '{{number_of_days}} days'
		{{else}}
		activity_date <= date('now', 'start of day', '{{number_of_days}} day')
		{{/ifEquals}}
		THEN care_calendar_id
	END) AS preventive_count_15,
	COUNT(DISTINCT(
		CASE WHEN 
		{{#ifEquals IS_POSTGRES 1}}
		activity_date <= current_timestamp
		{{else}}
		activity_date <= date('now', 'start of day')
		{{/ifEquals}}
  THEN all_cc.customer_id 
	END)) AS farmer_count,
	COUNT(DISTINCT(
		CASE WHEN 
		{{#ifEquals IS_POSTGRES 1}}
		activity_date <= current_timestamp + interval '{{number_of_days}} days'
		{{else}}
		activity_date <= date('now', 'start of day', '{{number_of_days}} day')
		{{/ifEquals}}
		THEN all_cc.customer_id 
	END)) AS farmer_count_15	
FROM 
	main.staff
		LEFT JOIN main.document
			ON
				document.entity_1_entity_uuid = staff.staff_id
				AND
				document.document_type_id = ${DOCUMENT_STAFF_THUMBNAIL_TYPE}
				AND
				document.active = ${ACTIVE}
        LEFT JOIN main.entity_relationship er_paravet
                ON 
                er_paravet.entity_relationship_type_id=${PPU_PARAVET_TO_TASK} and er_paravet.entity_1_entity_uuid= staff.staff_id
        LEFT JOIN all_cc
                ON
                er_paravet.entity_2_entity_uuid = all_cc.care_calendar_id
	WHERE
		staff.active = ${ACTIVE}
		AND
		staff.staff_type_id = {{staff_type}}
	
	GROUP BY
 		staff_id, 
 		staff_name_l10n,
		document.document_id,
		document.document_information,
		document.client_document_information	
	ORDER BY
		critical_count DESC,
		critical_count_15 DESC		 
{{/if}}
`