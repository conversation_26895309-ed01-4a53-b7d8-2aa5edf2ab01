module.exports = `
     select * from (
	
        select 
            'Preventive-NA' as service_request_number,
            ref_activity.activity_name,
            ref_activity.activity_name_l10n,
            care_calendar.activity_date as start_date,
            coalesce (care_calendar.completion_date, care_calendar.activity_date) as end_date
        from main.care_calendar
            left join main.ref_activity
                on
                    care_calendar.activity_id = ref_activity.activity_id
            left join main.animal
                on
                    care_calendar.entity_uuid = animal.animal_id			
        where
            ref_activity.activity_category in (
                1000270001
            )
            and
            care_calendar.entity_type_id = 1000460002
            and
            care_calendar.calendar_activity_status = 1000300050
            and
            animal.active = 1000100001
            and
            animal.animal_id = '{{animal_id}}'
            
        
        UNION ALL	
        
        select 
            care_calendar_classification.value_string_256 as service_request_number,
            ref_activity.activity_name,
            ref_activity.activity_name_l10n,
            min(coalesce (care_calendar.completion_date, care_calendar.activity_date)) as start_date,
            max(coalesce (care_calendar.completion_date, care_calendar.activity_date)) as end_date
        from main.care_calendar
            left join main.ref_activity
                on
                    care_calendar.activity_id = ref_activity.activity_id
            left join main.animal
                on
                    care_calendar.entity_uuid = animal.animal_id			
            left join main.care_calendar_classification
                on
                    care_calendar_classification.care_calendar_id = care_calendar.care_calendar_id
                    and
                    care_calendar_classification.classifier_id = 2000000125
        where
            ref_activity.activity_category in (
                1000270002,
                1000270003
            )
            and
            care_calendar.entity_type_id = 1000460002
            and
            care_calendar.calendar_activity_status = 1000300050
            and
            animal.active = 1000100001
            and
            animal.animal_id = '{{animal_id}}'
            and
            care_calendar_classification.value_string_256 is not null
         	and
         	care_calendar_classification.value_string_256 != '{{animal_id}}'
        group by
            care_calendar_classification.value_string_256,
            ref_activity.activity_id,
            ref_activity.activity_name	
        ) T1 order by
        T1.end_date desc
        `;