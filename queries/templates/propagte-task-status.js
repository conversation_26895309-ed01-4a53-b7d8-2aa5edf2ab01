module.exports = `{{!-- 
Typeorm-placeholders:
Configure these using setParameters method of type-orm as per use
1. status [required]
2. parent_id [required]
3. LINKED_CARE_CALENDAR_CLASSIFIER [required]
4. ACTIVE [required]
 --}}
UPDATE main.care_calendar 
SET calendar_activity_status = {{status}}
{{#ifEquals IS_POSTGRES 0}}
, data_sync_status = 1000101003
{{/ifEquals}}
WHERE main.care_calendar.care_calendar_id in (
SELECT care_calendar_id from main.care_calendar_classification as ccl
	WHERE ccl.classifier_id = {{LINKED_CARE_CALENDAR_CLASSIFIER}}
	AND ccl.active = {{ACTIVE}}
	AND ccl.value_reference_uuid = '{{parent_id}}'
)`;