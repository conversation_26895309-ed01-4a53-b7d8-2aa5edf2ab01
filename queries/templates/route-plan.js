module.exports = `{{!-- 
Typeorm-placeholders:
Configure these using setParameters method of type-orm as per use
1. user_type [required]
2. user_id [required]
3. f_farmer_name [optional]
4. f_village [optional] 
4a. f_village_id_array [optional] 
5. activity_category [optional]
6. f_activity_status [optional]
7. start_date [optional]
8. end_date [optional]
9. f_paravet [optional]
10. IS_POSTGRES [required]

Template-variables:
Pass these variables while invoking the template function
1. user_type [required]
2. f_farmer_name [optional]
3. f_village [optional] 
3a. f_village_id_array [optional] 
4. activity_category [optional]
5. f_activity_status [optional]
6. start_date [optional]
7. end_date [optional]
8. f_paravet [optional]
9. IS_POSTGRES [required]

    --}}
WITH VALID_VILLAGES AS
(
  SELECT 
    DISTINCT (geography_id) AS village_id
  FROM main.entity_geography
  LEFT JOIN main.staff
    ON 
      entity_geography.entity_uuid = staff.staff_id
  WHERE
    entity_geography.geography_type_id = 1000320004
    AND entity_geography.active = 1000100001
    {{#ifEquals user_type 1000230002}}
      AND staff.staff_type_id = 1000230004
    {{else}}
      AND staff.staff_type_id = :user_type  
      AND staff.staff_id = :user_id
    {{/ifEquals}}          

), active_customers as (
	select 
		customer_id
	from
		main.customer
		left join main.entity_relationship
			on
				entity_relationship.entity_1_entity_uuid = customer.customer_id
				and
				entity_relationship.entity_relationship_type_id = 1000210004
		left join main.animal
			on
				entity_relationship.entity_2_entity_uuid = animal.animal_id
	where
		animal.active = 1000100001 
		or
		customer.active = 1000100001
	group by
		customer_id
	having count(*) > 0
),

ALL_CC AS (
  SELECT
    CUSTOMER.customer_id AS customer_id,
    CUSTOMER.customer_name_l10n AS customer_name,
    CUSTOMER.customer_visual_id AS customer_visual_id,
    CUSTOMER.mobile_number AS customer_mobile_number,
    CUSTOMER_CLASSIFICATION.value_reference_id AS village_id,
    REF_VILLAGE.village_name_l10n AS village_name,
    REF_TALUK.taluk_id AS taluk_id,
    REF_TALUK.taluk_name_l10n AS taluk_name,
    REF_DISTRICT.district_id AS district_id,
    REF_DISTRICT.district_name_l10n AS district_name,
    REF_STATE.state_id AS state_id,
    REF_STATE.state_name_l10n AS state_name,
    ANIMAL.animal_id AS animal_id,
    ANIMAL.animal_visual_id AS animal_visual_id,
    REF_REFERENCE.reference_name AS animal_type,
    REF_REFERENCE.reference_name_l10n AS animal_type_json,
    REF_ANIMAL_NAME.value_string_256 AS animal_name,
    REF_ANIMAL_EAR_TAG.value_string_256 AS animal_ear_tag,
    STAFF.staff_id AS paravet_id,
    STAFF.staff_name_l10n AS paravet_name,
    STAFF.mobile_number AS paravet_mobile_number,
    CARE_CALENDAR.care_calendar_id AS care_calendar_id,
    CARE_CALENDAR.activity_date AS visit_date,
    CARE_CALENDAR.activity_id AS activity_id,
    REF_ACTIVITY.activity_category AS activity_category,
    REF_ACTIVITY.activity_name AS activity_name,
    REF_ACTIVITY.activity_name_l10n AS activity_name_json,
    CARE_CALENDAR.calendar_activity_status AS activity_status_id,
    REF_ACTIVITY_STATUS.reference_name AS activity_status,
    REF_ACTIVITY_STATUS.reference_name_l10n AS activity_status_json,
    CARE_CALENDAR_CLASSIFICATION.value_string_256 AS activity_complaint_number,
    CASE 
        WHEN 
            REF_ACTIVITY.activity_category IN (1000270002, 1000270003) 
        THEN 1
        ELSE 
            -1
    END AS activity_priority
  FROM 
    main.CARE_CALENDAR
  LEFT JOIN main.CARE_CALENDAR_CLASSIFICATION
    ON
    CARE_CALENDAR.care_calendar_id = CARE_CALENDAR_CLASSIFICATION.care_calendar_id
    AND
    CARE_CALENDAR_CLASSIFICATION.classifier_id = 2000000125
  LEFT JOIN main.REF_ACTIVITY
    ON
    CARE_CALENDAR.activity_id = REF_ACTIVITY.activity_id
  LEFT JOIN main.REF_REFERENCE REF_ACTIVITY_STATUS
    ON
    REF_ACTIVITY_STATUS.reference_id = CARE_CALENDAR.calendar_activity_status
  LEFT JOIN main.ENTITY_RELATIONSHIP 
    ON 
    ENTITY_RELATIONSHIP.entity_2_entity_uuid = CARE_CALENDAR.entity_uuid
    AND
    ENTITY_RELATIONSHIP.entity_relationship_type_id = 1000210004
    AND
    CARE_CALENDAR.entity_type_id = 1000460002
  LEFT JOIN main.CUSTOMER
    ON 
    CUSTOMER.customer_id = ENTITY_RELATIONSHIP.entity_1_entity_uuid
    OR (
        CUSTOMER.customer_id = CARE_CALENDAR.entity_uuid
        AND
        CARE_CALENDAR.entity_type_id = 1000460001
    )	
  LEFT JOIN main.CUSTOMER_CLASSIFICATION 
    ON 
    CUSTOMER_CLASSIFICATION.customer_id = CUSTOMER.customer_id
    AND
    CUSTOMER_CLASSIFICATION.classifier_id = 2000000055
  LEFT JOIN main.REF_VILLAGE
    ON
    REF_VILLAGE.village_id = CUSTOMER_CLASSIFICATION.value_reference_id
  LEFT JOIN main.REF_TALUK
    ON
    REF_TALUK.taluk_id = REF_VILLAGE.taluk_id
  LEFT JOIN main.REF_DISTRICT
    ON
    REF_DISTRICT.district_id = REF_TALUK.district_id
  LEFT JOIN main.REF_STATE
    ON
    REF_STATE.state_id = REF_DISTRICT.state_id
  LEFT JOIN main.ENTITY_GEOGRAPHY 
    ON 
    ENTITY_GEOGRAPHY.geography_id = REF_VILLAGE.village_id
    AND
    ENTITY_GEOGRAPHY.geography_type_id = 1000320004 
    AND
    ENTITY_GEOGRAPHY.entity_type_id = 1000230004
    AND
    ENTITY_GEOGRAPHY.active = 1000100001
  LEFT JOIN main.STAFF
    ON
    STAFF.staff_id = ENTITY_GEOGRAPHY.entity_uuid
  LEFT JOIN main.ANIMAL
    ON
    ANIMAL.animal_id = ENTITY_RELATIONSHIP.entity_2_entity_uuid
  LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_TYPE
    ON
      REF_ANIMAL_TYPE.animal_id = ANIMAL.animal_id
      AND
      REF_ANIMAL_TYPE.classifier_id = 2000000035        
  LEFT JOIN main.REF_REFERENCE
    ON
    REF_REFERENCE.reference_id = REF_ANIMAL_TYPE.value_reference_id
  LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_NAME
    ON
    REF_ANIMAL_NAME.animal_id = ANIMAL.animal_id
    AND
    REF_ANIMAL_NAME.classifier_id = 2000000033
  LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_EAR_TAG
    ON
    REF_ANIMAL_EAR_TAG.animal_id = ANIMAL.animal_id
    AND
    REF_ANIMAL_EAR_TAG.classifier_id = 2000000034
  WHERE
    CARE_CALENDAR.active = 1000100001
    AND
    CARE_CALENDAR.entity_type_id IN (1000460001, 1000460002)
    AND
    CARE_CALENDAR.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    AND
    CUSTOMER.customer_id in (
      select customer_id from active_customers
    ) 
    AND
    (
    ANIMAL.active is null 
    OR 
    ANIMAL.active = 1000100001
    )
    AND CUSTOMER_CLASSIFICATION.value_reference_id IN (
    SELECT village_id FROM VALID_VILLAGES
    )

    {{#if f_farmer_name}}
    {{#ifEquals IS_POSTGRES 1}}
    AND (
      EXISTS (
        SELECT 1
        FROM 
          jsonb_each_text(CUSTOMER.customer_name_l10n) AS customer_names_in_all_lang
        WHERE 
          customer_names_in_all_lang.value ILIKE :f_farmer_name
      )
      OR
      CARE_CALENDAR_CLASSIFICATION.value_string_256 ILIKE :f_farmer_name
      OR
      REF_ACTIVITY.activity_name ILIKE :f_farmer_name
    )
    {{else}}
    AND (
      UPPER(CUSTOMER.customer_name_l10n) LIKE :f_farmer_name
      OR
      CARE_CALENDAR_CLASSIFICATION.value_string_256 LIKE :f_farmer_name
      OR
      REF_ACTIVITY.activity_name LIKE :f_farmer_name
    ) 
    {{/ifEquals}}
    {{/if}}

    {{#if f_activity_category}}
    AND REF_ACTIVITY.activity_category IN (:...f_activity_category)
    {{/if}}

    {{#if start_date }}
    AND CARE_CALENDAR.activity_date >= :start_date
    {{/if}}
    
    {{#if end_date}}
    AND CARE_CALENDAR.activity_date <= :end_date
    {{/if}}
    
    {{#ifEquals IS_POSTGRES 1}}
    {{#bothNotExist start_date end_date}}
    AND CARE_CALENDAR.activity_date::DATE < current_date + 15
    {{/bothNotExist}}
    {{else}}
    {{#bothNotExist start_date end_date}}
    AND CARE_CALENDAR.activity_date < date('now', 'start of day', '+15 day')
    {{/bothNotExist}}
    {{/ifEquals}}

    {{#if f_activity_status}}
    AND CARE_CALENDAR.calendar_activity_status IN (:...f_activity_status)
    {{/if}}

    {{#if f_village}}
    AND CUSTOMER_CLASSIFICATION.value_reference_id IN (:...f_village)
    {{/if}}

    {{#if f_village_id_array}}
    AND CUSTOMER_CLASSIFICATION.value_reference_id IN (:...f_village_id_array)
    {{/if}}

    {{#if f_paravet}}
    AND STAFF.staff_id IN (:...f_paravet)
    {{/if}}

UNION ALL

SELECT
  CUSTOMER.customer_id AS customer_id,
  CUSTOMER.customer_name_l10n AS customer_name,
  CUSTOMER.customer_visual_id AS customer_visual_id,
  CUSTOMER.mobile_number AS customer_mobile_number,
  CUSTOMER_CLASSIFICATION.value_reference_id AS village_id,
  REF_VILLAGE.village_name_l10n AS village_name,
  REF_TALUK.taluk_id AS taluk_id,
  REF_TALUK.taluk_name_l10n AS taluk_name,
  REF_DISTRICT.district_id AS district_id,
  REF_DISTRICT.district_name_l10n AS district_name,
  REF_STATE.state_id AS state_id,
  REF_STATE.state_name_l10n AS state_name,
  ANIMAL.animal_id AS animal_id,
  ANIMAL.animal_visual_id AS animal_visual_id,
  REF_REFERENCE.reference_name AS animal_type,
  REF_REFERENCE.reference_name_l10n AS animal_type_json,
  REF_ANIMAL_NAME.value_string_256 AS animal_name,
  REF_ANIMAL_EAR_TAG.value_string_256 AS animal_ear_tag,
  STAFF.staff_id AS paravet_id,
  STAFF.staff_name_l10n AS paravet_name,
  STAFF.mobile_number AS paravet_mobile_number,
  CARE_CALENDAR.care_calendar_id AS care_calendar_id,
  CARE_CALENDAR.activity_date AS visit_date,
  CARE_CALENDAR.activity_id AS activity_id,
  REF_ACTIVITY.activity_category AS activity_category,
  REF_ACTIVITY.activity_name AS activity_name,
  REF_ACTIVITY.activity_name_l10n AS activity_name_json,
  CARE_CALENDAR.calendar_activity_status AS activity_status_id,
  REF_ACTIVITY_STATUS.reference_name AS activity_status,
  REF_ACTIVITY_STATUS.reference_name_l10n AS activity_status_json,
  CARE_CALENDAR_CLASSIFICATION.value_string_256 AS activity_complaint_number,
  CASE 
      WHEN 
          REF_ACTIVITY.activity_category IN (1000270002, 1000270003) 
      THEN 1
      ELSE 
          -1
  END AS activity_priority
FROM 
  main.STAFF_ACTIVITY      
    LEFT JOIN main.CARE_CALENDAR
    ON
      STAFF_ACTIVITY.care_calendar = CARE_CALENDAR.care_calendar_id
    LEFT JOIN main.CARE_CALENDAR_CLASSIFICATION
    ON
      CARE_CALENDAR.care_calendar_id = CARE_CALENDAR_CLASSIFICATION.care_calendar_id
      AND
      CARE_CALENDAR_CLASSIFICATION.classifier_id = 2000000125
    LEFT JOIN main.REF_ACTIVITY
    ON
      CARE_CALENDAR.activity_id = REF_ACTIVITY.activity_id
    LEFT JOIN main.REF_REFERENCE REF_ACTIVITY_STATUS
    ON
      REF_ACTIVITY_STATUS.reference_id = CARE_CALENDAR.calendar_activity_status
    LEFT JOIN main.ENTITY_RELATIONSHIP 
    ON 
      ENTITY_RELATIONSHIP.entity_2_entity_uuid = CARE_CALENDAR.entity_uuid
      AND
      ENTITY_RELATIONSHIP.entity_relationship_type_id = 1000210004
      AND
      CARE_CALENDAR.entity_type_id = 1000460002
    LEFT JOIN main.CUSTOMER
    ON 
      CUSTOMER.customer_id = ENTITY_RELATIONSHIP.entity_1_entity_uuid
      OR (
        CUSTOMER.customer_id = CARE_CALENDAR.entity_uuid
        AND
        CARE_CALENDAR.entity_type_id = 1000460001
      )	
    LEFT JOIN main.CUSTOMER_CLASSIFICATION 
    ON 
      CUSTOMER_CLASSIFICATION.customer_id = CUSTOMER.customer_id
      AND
      CUSTOMER_CLASSIFICATION.classifier_id = 2000000055
    LEFT JOIN main.REF_VILLAGE
    ON
      REF_VILLAGE.village_id = CUSTOMER_CLASSIFICATION.value_reference_id
    LEFT JOIN main.REF_TALUK
    ON
      REF_TALUK.taluk_id = REF_VILLAGE.taluk_id
    LEFT JOIN main.REF_DISTRICT
    ON
      REF_DISTRICT.district_id = REF_TALUK.district_id
    LEFT JOIN main.REF_STATE
    ON
      REF_STATE.state_id = REF_DISTRICT.state_id
    LEFT JOIN main.STAFF
    ON
      STAFF.staff_id = STAFF_ACTIVITY.staff_id
    LEFT JOIN main.ANIMAL
    ON
      ANIMAL.animal_id = ENTITY_RELATIONSHIP.entity_2_entity_uuid
    LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_TYPE
      ON
        REF_ANIMAL_TYPE.animal_id = ANIMAL.animal_id
        AND
        REF_ANIMAL_TYPE.classifier_id = 2000000035        
    LEFT JOIN main.REF_REFERENCE
      ON
        REF_REFERENCE.reference_id = REF_ANIMAL_TYPE.value_reference_id
    LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_NAME
    ON
      REF_ANIMAL_NAME.animal_id = ANIMAL.animal_id
      AND
      REF_ANIMAL_NAME.classifier_id = 2000000033
    LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_EAR_TAG
    ON
      REF_ANIMAL_EAR_TAG.animal_id = ANIMAL.animal_id
      AND
      REF_ANIMAL_EAR_TAG.classifier_id = 2000000034
WHERE
  STAFF_ACTIVITY.staff_id = :user_id
  AND
  CARE_CALENDAR.active = 1000100001
  AND
  CARE_CALENDAR.entity_type_id IN (1000460001, 1000460002)
  AND
  CARE_CALENDAR.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
  AND
  CUSTOMER.customer_id in (
    select customer_id from active_customers
  ) 
  AND
  (
    ANIMAL.active is null 
    OR 
    ANIMAL.active = 1000100001
  )

  {{#if f_farmer_name}}
  {{#ifEquals IS_POSTGRES 1}}
  AND (
    EXISTS (
      SELECT 1
      FROM 
        jsonb_each_text(CUSTOMER.customer_name_l10n) AS customer_names_in_all_lang
      WHERE 
        customer_names_in_all_lang.value ILIKE :f_farmer_name
    )
    OR
    CARE_CALENDAR_CLASSIFICATION.value_string_256 ILIKE :f_farmer_name
  )
  {{else}}
  AND (
    UPPER(CUSTOMER.customer_name_l10n) LIKE :f_farmer_name
    OR
    CARE_CALENDAR_CLASSIFICATION.value_string_256 LIKE :f_farmer_name
  ) 
  {{/ifEquals}}
  {{/if}}

{{#if f_activity_category}}
AND REF_ACTIVITY.activity_category IN (:...f_activity_category)
{{/if}}

{{#if start_date }}
AND CARE_CALENDAR.activity_date >= :start_date
{{/if}}

{{#if end_date}}
AND CARE_CALENDAR.activity_date <= :end_date
{{/if}}

{{#ifEquals IS_POSTGRES 1}}
{{#bothNotExist start_date end_date}}
AND CARE_CALENDAR.activity_date::DATE < current_date + 15
{{/bothNotExist}}
{{else}}
{{#bothNotExist start_date end_date}}
AND CARE_CALENDAR.activity_date < date('now', 'start of day', '+15 day')
{{/bothNotExist}}
{{/ifEquals}}

{{#if f_activity_status}}
AND CARE_CALENDAR.calendar_activity_status IN (:...f_activity_status)
{{/if}}

{{#if f_village}}
AND CUSTOMER_CLASSIFICATION.value_reference_id IN (:...f_village)
{{/if}}

{{#if f_village_id_array}}
AND CUSTOMER_CLASSIFICATION.value_reference_id IN (:...f_village_id_array)
{{/if}}

{{#if f_paravet}}
AND STAFF.staff_id IN (:...f_paravet)
{{/if}}

) 
  
SELECT 
  T1.customer_id,
  T1.customer_name,
  T1.customer_visual_id,
  T1.mobile_number,
  T1.village_name,
  T1.taluk_name,
  T1.visit_date,
  T1.animal_count,
  T1.cv_priority,
  T1.activity_count,
  FCC_CC.activity_id AS first_cc_activity_id,
  FCC_CC.calendar_activity_status AS fcca_status_id,
  FCC.value_string_256 AS fcca_complaint_number,
  FCC_REF_ACTIVITY.activity_category AS fcca_activity_category_id,
  FCC_REF_ACTIVITY.activity_name AS fcca_name,
  FCC_REF_ACTIVITY.activity_name_l10n AS fcca_name_json,
  FCC_REF_ACTIVITY_STATUS.reference_name AS fcca_status,
  FCC_REF_ACTIVITY_STATUS.reference_name_l10n AS fcca_status_json,	
  FNCC_CC.activity_id AS first_ncc_activity_id,
  FNCC_CC.calendar_activity_status AS fncca_status_id,
  FNCC.value_string_256 AS fncca_complaint_number,
  FNCC_REF_ACTIVITY.activity_category AS fncca_activity_category_id,
  FNCC_REF_ACTIVITY.activity_name AS fncca_name,
  FNCC_REF_ACTIVITY.activity_name_l10n AS fncca_name_json,
  FNCC_REF_ACTIVITY_STATUS.reference_name AS fncca_status,
  FNCC_REF_ACTIVITY_STATUS.reference_name_l10n AS fncca_status_json
FROM (
  SELECT
    customer_id,
    customer_name,
    customer_visual_id,
    customer_mobile_number AS mobile_number,
    village_name,
    taluk_name,
    visit_date,
    count(distinct(animal_id)) as animal_count,
    max(activity_priority) as cv_priority,
    count(activity_id) as activity_count
  FROM 
    ALL_CC
  GROUP BY
    customer_id,
    customer_name,
    customer_visual_id,
    customer_mobile_number,
    village_name,
    taluk_name,
    visit_date
  ORDER BY
    cv_priority DESC,
    visit_date,
    activity_count DESC		
) T1 LEFT JOIN (
  SELECT
    customer_id,
    customer_name,
    customer_visual_id,
    customer_mobile_number AS mobile_number,
    village_name,
    taluk_name,
    visit_date,
    {{#ifEquals IS_POSTGRES 1}}
    uuid(MAX(care_calendar_id::text)) AS first_cc_id
    {{else}}
    MAX(care_calendar_id) AS first_cc_id
    {{/ifEquals}}
  FROM
    ALL_CC
  WHERE
    activity_category IN (1000270002, 1000270003)
  GROUP BY
    customer_id,
    customer_name,
    customer_visual_id,
    customer_mobile_number,
    village_name,
    taluk_name,
    visit_date
  ORDER BY
    visit_date
) T2 ON 
    T1.customer_id = T2.customer_id
    AND
    T1.visit_date = T2.visit_date
LEFT JOIN (
  SELECT
    customer_id,
    customer_name,
    customer_visual_id,
    customer_mobile_number AS mobile_number,
    village_name,
    taluk_name,
    visit_date,
    {{#ifEquals IS_POSTGRES 1}}
    uuid(MAX(care_calendar_id::text)) AS first_ncc_id
    {{else}}
    MAX(care_calendar_id) AS first_ncc_id
    {{/ifEquals}}
  FROM
    ALL_CC
  WHERE
    activity_category NOT IN (1000270002, 1000270003)
  GROUP BY
    customer_id,
    customer_name,
    customer_visual_id,
    customer_mobile_number,
    village_name,
    taluk_name,
    visit_date
  ORDER BY
    visit_date
) T3 ON 	
  T1.customer_id = T3.customer_id
  AND
  T1.visit_date = T3.visit_date	

  LEFT JOIN main.CARE_CALENDAR_CLASSIFICATION FCC
    ON
      T2.first_cc_id = FCC.care_calendar_id
      AND
      FCC.classifier_id = 2000000125
  LEFT JOIN main.CARE_CALENDAR_CLASSIFICATION FNCC
    ON
      T3.first_ncc_id = FNCC.care_calendar_id
      AND
      FNCC.classifier_id = 2000000125
  LEFT JOIN main.CARE_CALENDAR FCC_CC
    ON
      T2.first_cc_id = FCC_CC.care_calendar_id
  LEFT JOIN main.CARE_CALENDAR FNCC_CC
    ON
      T3.first_ncc_id = FNCC_CC.care_calendar_id
  LEFT JOIN main.REF_ACTIVITY FCC_REF_ACTIVITY
    ON
      FCC_CC.activity_id = FCC_REF_ACTIVITY.activity_id
  LEFT JOIN main.REF_ACTIVITY FNCC_REF_ACTIVITY
    ON
      FNCC_CC.activity_id = FNCC_REF_ACTIVITY.activity_id
  LEFT JOIN main.REF_REFERENCE FCC_REF_ACTIVITY_STATUS
    ON
      FCC_REF_ACTIVITY_STATUS.reference_id = FCC_CC.calendar_activity_status	
  LEFT JOIN main.REF_REFERENCE FNCC_REF_ACTIVITY_STATUS
    ON
      FNCC_REF_ACTIVITY_STATUS.reference_id = FNCC_CC.calendar_activity_status	
ORDER BY
  T1.cv_priority DESC,
  T1.visit_date,
  T1.activity_count DESC
    `
