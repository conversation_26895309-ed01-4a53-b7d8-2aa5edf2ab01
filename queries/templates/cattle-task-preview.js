const { TICKET_CLASSIFIER, ACTIVE, VILLAGE_CLASSIFIER, VILLAGE_TYPE_ID, PARAVET, CONST_ID_TYPE_STAFF_RO, ANIMAL_EAR_TAG_CLASSIFIER } = require("../../utils/constant");

module.exports = `SELECT 

ticket.value_string_256 as ticket,
care_calendar.created_at as complaint_date_time,
customer.customer_name_l10n as farmer_name_l10n,
customer.mobile_number as farmer_mobile,
ear_tag.value_string_256 as animal_ear_tag,
animal.animal_visual_id as animal_visual_id,
customer.customer_visual_id as farmer_visual_id,
ref_village.village_name_l10n as farmer_village,
ref_geo.district_name_l10n as district_name_l10n,
ref_geo.taluk_name_l10n as taluk_name_l10n,
paravet.staff_name_l10n as paravet_name_l10n,
vet.staff_name_l10n as vet_name_l10n,
ro.staff_name_l10n as ro_name_l10n,
ref_activity.activity_name_l10n as activity_name_l10n

FROM main.care_calendar
LEFT JOIN main.care_calendar_classification as ticket
	ON ticket.care_calendar_id = care_calendar.care_calendar_id
	AND ticket.classifier_id = ${TICKET_CLASSIFIER}
	AND ticket.active = ${ACTIVE}
LEFT join main.ref_activity
	ON ref_activity.activity_id = care_calendar.activity_id
JOIN main.animal 
	ON animal.animal_id = care_calendar.entity_uuid
LEFT JOIN main.animal_classification AS ear_tag
	ON ear_tag.classifier_id = ${ANIMAL_EAR_TAG_CLASSIFIER}
	AND ear_tag.animal_id = animal.animal_id
	AND ear_tag.active = ${ACTIVE}
LEFT JOIN main.entity_relationship as er
	ON er.entity_relationship_type_id = 1000210004
	AND er.active = ${ACTIVE}
	AND er.entity_2_entity_uuid = animal.animal_id
JOIN main.customer
	ON customer.customer_id = er.entity_1_entity_uuid
LEFT JOIN main.customer_classification as village_classification 
	ON village_classification.customer_id = customer.customer_id
	AND village_classification.active = ${ACTIVE}
	AND village_classification.classifier_id = ${VILLAGE_CLASSIFIER}

LEFT JOIN main.ref_village ON
	ref_village.village_id = village_classification.value_reference_id
LEFT JOIN main.ref_sdtv_view as ref_geo
	ON ref_geo.village_id = ref_village.village_id
LEFT JOIN main.entity_geography as vet_geo
	ON vet_geo.entity_type_id = 1000230003
	AND vet_geo.active = ${ACTIVE}
	AND vet_geo.geography_type_id = ${VILLAGE_TYPE_ID}
	AND vet_geo.geography_id = ref_village.village_id

LEFT JOIN main.staff as vet
	ON vet.staff_id = vet_geo.entity_uuid

LEFT JOIN main.entity_geography as paravet_geo
	ON paravet_geo.entity_type_id = ${PARAVET}
	AND paravet_geo.active = ${ACTIVE}
	AND paravet_geo.geography_type_id = ${VILLAGE_TYPE_ID}
	AND paravet_geo.geography_id = ref_village.village_id
LEFT JOIN main.staff as paravet
	ON paravet.staff_id = paravet_geo.entity_uuid

LEFT JOIN main.entity_geography as ro_geo
	ON ro_geo.entity_type_id = ${CONST_ID_TYPE_STAFF_RO}
	AND ro_geo.active = ${ACTIVE}
	AND ro_geo.geography_type_id = ${VILLAGE_TYPE_ID}
	AND ro_geo.geography_id = ref_village.village_id
LEFT JOIN main.staff as ro
	ON ro.staff_id = ro_geo.entity_uuid
WHERE care_calendar.care_calendar_id = :care_calendar_id`;