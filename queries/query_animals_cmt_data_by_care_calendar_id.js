const { record_statuses, classifiers, entity_type } = require("../ENUMS");

function cmtQuery(data) {
  const {  care_calendar_id, calendar_activity_status, entity_type_id } = data
  if (calendar_activity_status && entity_type_id === entity_type.ANIMAL) {    
    return `
    select
      lh_teat_cmt_score.value_int as lh_teat_cmt_score,
      rh_teat_cmt_score.value_int as rh_teat_cmt_score, 
      lf_teat_cmt_score.value_int as lf_teat_cmt_score, 
      rf_teat_cmt_score.value_int as rf_teat_cmt_score,
      note.note,
      animal.animal_id as animal_id
    from main.care_calendar
      left join main.care_calendar_classification lh_teat_cmt_score on lh_teat_cmt_score.care_calendar_id = care_calendar.care_calendar_id and lh_teat_cmt_score.classifier_id = ${classifiers.LH_TEAT_CMT_SCORE} and lh_teat_cmt_score.active = ${record_statuses.ACTIVE}
      left join main.care_calendar_classification rh_teat_cmt_score on rh_teat_cmt_score.care_calendar_id = care_calendar.care_calendar_id and rh_teat_cmt_score.classifier_id = ${classifiers.RH_TEAT_CMT_SCORE} and rh_teat_cmt_score.active = ${record_statuses.ACTIVE}
      left join main.care_calendar_classification lf_teat_cmt_score on lf_teat_cmt_score.care_calendar_id = care_calendar.care_calendar_id and lf_teat_cmt_score.classifier_id = ${classifiers.LF_TEAT_CMT_SCORE} and lf_teat_cmt_score.active = ${record_statuses.ACTIVE}
      left join main.care_calendar_classification rf_teat_cmt_score on rf_teat_cmt_score.care_calendar_id = care_calendar.care_calendar_id and rf_teat_cmt_score.classifier_id = ${classifiers.RF_TEAT_CMT_SCORE} and rf_teat_cmt_score.active = ${record_statuses.ACTIVE}
      left join main.note on  note.entity_1_type_id=${entity_type.TASK} and note.entity_1_uuid =  care_calendar.care_calendar_id and note.active = ${record_statuses.ACTIVE}
      left join main.animal on animal.animal_id = care_calendar.entity_uuid and animal.active = ${record_statuses.ACTIVE}
      where care_calendar.care_calendar_id = '${care_calendar_id}' and care_calendar.active = ${record_statuses.ACTIVE}
    `
  }
  return `
      with tasks as (
        select CCC.care_calendar_id as care_calendar_id from main.care_calendar CC
        left join main.CARE_CALENDAR_CLASSIFICATION CCC on CCC.classifier_id= ${classifiers.LINKED_CARE_CALENDAR_TASK} and CCC.value_reference_uuid = CC.care_calendar_id
        where CC.care_calendar_id = '${care_calendar_id}' and CC.active = ${record_statuses.ACTIVE}
      ) 

      select
        lh_teat_cmt_score.value_int as lh_teat_cmt_score,
        rh_teat_cmt_score.value_int as rh_teat_cmt_score, 
        lf_teat_cmt_score.value_int as lf_teat_cmt_score, 
        rf_teat_cmt_score.value_int as rf_teat_cmt_score,
        note.note,
        animal.animal_id as animal_id
      from tasks
        left join main.care_calendar_classification lh_teat_cmt_score on lh_teat_cmt_score.care_calendar_id = tasks.care_calendar_id  and lh_teat_cmt_score.classifier_id = ${classifiers.LH_TEAT_CMT_SCORE} and lh_teat_cmt_score.active = ${record_statuses.ACTIVE}
        left join main.care_calendar_classification rh_teat_cmt_score on rh_teat_cmt_score.care_calendar_id = tasks.care_calendar_id  and rh_teat_cmt_score.classifier_id = ${classifiers.RH_TEAT_CMT_SCORE} and rh_teat_cmt_score.active = ${record_statuses.ACTIVE}
        left join main.care_calendar_classification lf_teat_cmt_score on lf_teat_cmt_score.care_calendar_id = tasks.care_calendar_id  and lf_teat_cmt_score.classifier_id = ${classifiers.LF_TEAT_CMT_SCORE} and lf_teat_cmt_score.active = ${record_statuses.ACTIVE}
        left join main.care_calendar_classification rf_teat_cmt_score on rf_teat_cmt_score.care_calendar_id = tasks.care_calendar_id  and rf_teat_cmt_score.classifier_id = ${classifiers.RF_TEAT_CMT_SCORE} and rf_teat_cmt_score.active = ${record_statuses.ACTIVE}
        left join main.care_calendar on care_calendar.entity_type_id= ${entity_type.ANIMAL} and care_calendar.care_calendar_id = tasks.care_calendar_id
        left join main.note on  note.entity_1_type_id=${entity_type.TASK} and note.entity_1_uuid =  tasks.care_calendar_id and note.active = ${record_statuses.ACTIVE}
        left join main.animal on animal.animal_id = care_calendar.entity_uuid and animal.active = ${record_statuses.ACTIVE}
        ` 
}

module.exports = {
  cmtQuery
}