const { configurationJSO<PERSON> } = require('@krushal-it/common-core')
const { dbConnections, preProcessRecords, postProcessRecords } = require('@krushal-it/ah-orm')
const { errorLog } = require('@krushal-it/mobile-or-server-lib')
const { getCompiledQuery } = require('./index.js')
const {
  RELATION_CUSTOMER,
  RELATION_CUSTOMER_CLASSIFICATION,
  SEQ_CUSTOMER_VISUAL_ID,
  RELATION_TALUKA,
  CONST_ID_CLASSIFIER_TALUKA,
  CONST_PADDING_VISUAL_ID_CUSTOMER,
  SCHEMA_MAIN,
  CONST_ID_TYPE_VILLAGE,
  BACK_OFFICE_PERSON,
  CENTRALIZED_VET
} = require('../utils/constant')

const insertToCustomer = async (user) => {
  const entity_customer = dbConnections().main.entities['customer']
  const preProcessedUser = preProcessRecords(entity_customer, user, entity_customer.additionalAttributes)
  let data = await dbConnections().main.manager.createQueryBuilder().insert().into(RELATION_CUSTOMER).values(preProcessedUser).execute()
  return data
}

const updateCustomerLocal = async (value, options = {}) => {
  if (!options.customer_id) throw new Error('customer_id is required')
  let manager = options.manager ? options.manager : dbConnections().main.manager
  const entity = dbConnections().main.entities['customer']
  const preProcessedValue = preProcessRecords(entity, value, { ...entity.additionalAttributes, id: options.customer_id })
  await manager.createQueryBuilder().update(RELATION_CUSTOMER).set(preProcessedValue).where(`${RELATION_CUSTOMER}_id = :id`, { id: options.customer_id }).execute()
  return
}
const getCustomerByPhoneNumber = async (mobile) => {
  const user = await dbConnections().main.repos[RELATION_CUSTOMER].createQueryBuilder().where(`${RELATION_CUSTOMER}.mobile_number = :mobile`, { mobile }).getOne()
  return user
}

const customerListing = async (options = {}) => {
  const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
  const { user_id, user_type } = options

  if (options.f_farmer_name) {
    options.f_farmer_name = '%' + options.f_farmer_name + '%'
  }

  if (options.f_farmer_village) {
    if (Array.isArray(options.f_farmer_village)) {
      options.f_farmer_village_array = options.f_farmer_village
    } else {
      options.f_farmer_village_text = '%' + options.f_farmer_village + '%'
    }
  }

  if (options.f_farmer_taluka) {
    if (Array.isArray(options.f_farmer_taluka)) {
      options.f_farmer_taluka_array = options.f_farmer_taluka
    } else {
      options.f_farmer_taluka_text = '%' + options.f_farmer_taluka + '%'
    }
  }

  if (options.f_farmer_visual_id) {
    options.f_farmer_visual_id = '%' + options.f_farmer_visual_id + '%'
  }

  if (options.f_farmer_mobile) {
    options.f_farmer_mobile = '%' + options.f_farmer_mobile + '%'
  }

  if (options.f_paravet_name) {
    options.f_paravet_name = '%' + options.f_paravet_name + '%'
  }

  const village_filters_not_required_for = [BACK_OFFICE_PERSON, CENTRALIZED_VET];
  let IS_VILLAGE_FILTER_NEEDED = 'Y';
  if(village_filters_not_required_for.includes(user_type)) {
    IS_VILLAGE_FILTER_NEEDED = 'N';
  }
  const queryParameters = { ...options, user_id, user_type }
  const templateParameters = { ...options, user_id, user_type, IS_POSTGRES: isPostgres, IS_VILLAGE_FILTER_NEEDED }

  let query = getCompiledQuery('customer-listing', templateParameters)
  const queryBuilder = await dbConnections().main.manager.createQueryBuilder().from(`(${query})`, 'customer-list').setParameters(queryParameters)

  let count = await queryBuilder.select('COUNT(*) as count').execute()
  count = parseInt(count[0].count)
  let total_page = 1
  if (!isNaN(options.page_number) && !isNaN(options.page_limit)) {
    let offset = options.page_limit * (options.page_number - 1)
    if (offset < 0) return { message: 'Invalid page number' }
    queryBuilder.skip(offset).take(options.page_limit)
    total_page = parseInt(count / options.page_limit)
    total_page += count % options.page_limit === 0 ? 0 : 1
  }

  const result = await queryBuilder.select('*').execute()
  postProcessRecords(undefined, result, { json_columns: ['customer_name_l10n', 'village_name_l10n', 'taluk_name_l10n', 'paravet_name', 'document_information', 'client_document_information'] })

  return { result, total_page }
}

const getTotalCustomers = async (options = {}) => {
  try {
    let query = global.datasource.getRepository(RELATION_CUSTOMER).createQueryBuilder()
    if (options.ids && Array.isArray(options.ids)) {
      query = query.where(`${RELATION_CUSTOMER}.customer_id IN (:...ids)`, { ids: options.ids })
    }
    let total = await query.getCount()
    return total
  } catch (error) {
    errorLog('error in getTotalCustomers:270', options, { message: error.message })
    return -1
  }
}
const getVisualIdSequence = async (options = {}) => {
  let manager = options.manager ? options.manager : global.datasource
  let val = await manager.createQueryBuilder().select(`NEXTVAL('${SEQ_CUSTOMER_VISUAL_ID}') AS val`).from(SEQ_CUSTOMER_VISUAL_ID).execute()
  return val[0].val
}
/**
 * Note: this function will not work if number of numeric digits in the visual id
 * exceeds CONST_PADDING_CUSTOMER_VISUAL_ID
 * @param {Number} taluka_id
 * @param {Object} options
 * @returns {Number}
 */
const getMaxVisualInTaluka = async (taluka_id, options = {}) => {
  try {
    let manager = options.manager ? options.manager : dbConnections().main.manager
    let paramIndex = 1
    let params = []
    let base_query = `SELECT MAX(${RELATION_CUSTOMER}.customer_visual_id) AS max_id
      FROM ${SCHEMA_MAIN}.${RELATION_CUSTOMER}
      JOIN ${SCHEMA_MAIN}.${RELATION_CUSTOMER_CLASSIFICATION}
      ON ${RELATION_CUSTOMER_CLASSIFICATION}.customer_id = ${RELATION_CUSTOMER}.customer_id
      AND ${RELATION_CUSTOMER_CLASSIFICATION}.classifier_id = ${CONST_ID_CLASSIFIER_TALUKA}
      JOIN ${SCHEMA_MAIN}.${RELATION_TALUKA}
      ON ${RELATION_TALUKA}.taluk_id = ${RELATION_CUSTOMER_CLASSIFICATION}.value_reference_id
      AND ${RELATION_TALUKA}.taluk_id = $${paramIndex}`
    paramIndex++
    params.push(taluka_id)
    let data = await manager.query(base_query, params)
    let max_id = data[0].max_id
    if (max_id === null) return [1, null]
    max_id = parseInt(max_id.substr(max_id.length - CONST_PADDING_VISUAL_ID_CUSTOMER)) + 1
    return [max_id, null]
  } catch (error) {
    errorLog('error in getMaxVisualInTaluka:307', options, { message: error.message })
    return [null, error.message]
  }
}
const addVisualId = async (options = {}) => {
  let manager = options.manager ? options.manager : global.datasource
  await manager.createQueryBuilder()
}

const q_getCustomerPrimaryTable = (options = {}) => {
  const { user_type, user_id } = options
  let geography_staff_inner_join =
    options.user_type != 1000230002 && options.user_type != 1000220001
      ? `join main.entity_geography ueg on
        ueg.entity_type_id = ${user_type} 
        and ueg.entity_uuid = '${user_id}'
        and ueg.geography_type_id = ${CONST_ID_TYPE_VILLAGE} 
        and ueg.geography_id = ccl_v.value_reference_id
        and ueg.active = 1000100001`
      : ''

  let base_query = `
    SELECT 
    customer.customer_id,
    customer.customer_type_id,
    customer.customer_visual_id,
    customer.customer_name_l10n,
    customer.mobile_number,
    customer.email_address,
    ad.document_id as customer_document_id,
    cv.village_name_l10n,
    cv.village_id as farmer_village_id,
    ct.taluk_id as farmer_taluk_id,
    cd.district_id as farmer_district_id,
    cs.state_id as farmer_state_id  
    FROM main.customer 
    JOIN main.customer_classification as ccl_v ON 
    ccl_v.customer_id = '${options.customer_id}'
    and ccl_v.classifier_id = 2000000055
    and ccl_v.active = 1000100001
    JOIN main.ref_village as cv on ccl_v.value_reference_id = cv.village_id
    JOIN main.ref_taluk as ct on cv.taluk_id = ct.taluk_id	
    JOIN main.ref_district as cd on cd.district_id = ct.district_id
    JOIN main.ref_state as cs on cd.state_id = cs.state_id
    LEFT JOIN (
      SELECT entity_1_entity_uuid,max(created_at) as created_at
      FROM main.document
      WHERE document_type_id=1000260003  
      GROUP BY entity_1_entity_uuid
  ) as amdoc on amdoc.entity_1_entity_uuid  = customer.customer_id
  LEFT JOIN main.document AS ad ON ad.entity_1_entity_uuid = amdoc.entity_1_entity_uuid 
      AND ad.created_at = amdoc.created_at 
      AND ad.entity_1_entity_uuid = customer.customer_id
    where customer.customer_id = '${options.customer_id}' AND customer.active = 1000100001;
    
    `
  return base_query
}

module.exports = {
  insertToCustomer,
  updateCustomerLocal,
  getCustomerByPhoneNumber,
  customerListing,
  getTotalCustomers,
  getVisualIdSequence,
  getMaxVisualInTaluka,
  addVisualId,
  q_getCustomerPrimaryTable,
}
