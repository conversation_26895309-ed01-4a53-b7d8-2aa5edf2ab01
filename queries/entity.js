//RELATIONSHIP QUERIES
const { dbConnections, preProcessRecords, postProcessRecords } = require("@krushal-it/ah-orm");
const { RELATION_RELATIONSHIP, SCHEMA_MAIN } = require("../utils/constant");
/**
 *
 * @param {int} entity_1_entity_id // eg customer_id
 * @param {int}  entity_2_entity_id // eg animal_id
 * @param {json} entity_information
 */


const insertToEntity = async (entity_1_entity_id, entity_2_entity_id, entity_relationship_type_id, entity_information) => {
    let value = {
        entity_1_entity_uuid: entity_1_entity_id,
        entity_2_entity_uuid: entity_2_entity_id,
        entity_relationship_type_id,
        entity_information,
    };
    const entity_entity = dbConnections().main.entities[RELATION_RELATIONSHIP];
    const preProcessedValue = preProcessRecords(entity_entity, value, entity_entity.additionalAttributes);
    let data = await dbConnections().main.manager.createQueryBuilder()
        .insert().into(RELATION_RELATIONSHIP).values(preProcessedValue).execute()
    return data;
};

/**
 * @deprecated Will be deleted
 *
 *
 */
const getentityRelation = async (options) => {
    let andFlag = false;
    let query = global.datasource.getRepository(RELATION_RELATIONSHIP).createQueryBuilder();
    if (Array.isArray(options.select) && options.select.length > 0) {
        query.select(options.select);
    }
    if (options.entity_relationship_type_id) {
        if (andFlag) {
            query.andWhere(`entity_relationship_type_id= :entity_relationship_type_id`,
                { entity_relationship_type_id: options.entity_relationship_type_id });
        } else
            query.where(`entity_relationship_type_id= :entity_relationship_type_id`,
                { entity_relationship_type_id: options.entity_relationship_type_id });
    }
    if (options.entity_2_entity_id) {
        if (andFlag)
            query.andWhere(`entity_2_entity_id= :entity_2_entity_id`,
                { entity_2_entity_id: options.entity_2_entity_id });
        else
            query.where(`entity_2_entity_id= :entity_2_entity_id`,
                { entity_2_entity_id: options.entity_2_entity_id, });
    }
    let data = await query.execute();
    return data;
};
const getentityRelation_v2 = async (options) => {
    let params = [];
    let paramIndex = 1;
    let base_query = `SELECT ${RELATION_RELATIONSHIP}.entity_1_entity_uuid FROM ${SCHEMA_MAIN}.${RELATION_RELATIONSHIP}
    WHERE ${RELATION_RELATIONSHIP}.entity_relationship_type_id = $${paramIndex}
    AND ${RELATION_RELATIONSHIP}.entity_2_entity_uuid = $${paramIndex + 1}`;
    paramIndex += 2;
    params.push(options.entity_relationship_type_id);
    params.push(options.entity_2_entity_uuid);
    const entityRelationshipResult = await dbConnections().main.manager.query(base_query, params)
    postProcessRecords(undefined, entityRelationshipResult, { json_columns: [] })
    return entityRelationshipResult
};

module.exports = { insertToEntity, getentityRelation, getentityRelation_v2 };
