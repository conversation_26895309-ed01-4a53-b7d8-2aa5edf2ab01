const handlebars = require("handlebars");
const dayjs = require('dayjs');
let queryIndex = require("./templates/index");

global.queryTemplates = null;

const loadHelpers = () => {
    try {
        handlebars.registerHelper('eq', function(a, b) {
            return a === b;
        });
    
        handlebars.registerHelper('gte', function(a, b) {
            return a >= b;
        });
        handlebars.registerHelper("ifEquals", (arg1, arg2, options) => {
            // if (arg1 === undefined || arg2 === undefined) throw new Error("iE arg1 and arg2 are required");
            return arg1 == arg2 ? options.fn(this) : options.inverse(this);
        });

        handlebars.registerHelper("bothExist", (arg1, arg2, options) => {
            return arg1 && arg2 ? options.fn(this) : options.inverse(this);
        });

        handlebars.registerHelper("bothNotExist", (arg1, arg2, options) => {
            return !arg1 && !arg2 ? options.fn(this) : options.inverse(this);
        });

        handlebars.registerHelper("existInSet", (arg1, arg2, options) => {
            if (arg1 === undefined) throw new Error("arg1 is required");
            if (!arg2) return options.inverse(this);
            if (!(arg2 instanceof Set)) throw new Error("arg2 must be an instance of Set");
            return arg2.has(arg1) ? options.fn(this) : options.inverse(this);
        });

        handlebars.registerHelper("ifNotEquals", (arg1, arg2, options) => {
            if (arg1 === undefined || arg2 === undefined) throw new Error("eNE arg1 and arg2 are required");
            return arg1 !== arg2 ? options.fn(this) : options.inverse(this);
        });
        handlebars.registerHelper('or', function () {
            const args = Array.prototype.slice.call(arguments);
            const options = args.pop();

            for (let i = 0; i < args.length; i++) {
                if (args[i]) {
                    return options.fn(this);
                }
            }

            return options.inverse(this);
        });

        handlebars.registerHelper('inc', function (value) {
            return parseInt(value) + 1;
        });

        handlebars.registerHelper('formatDate', function (date) {
            return date ? dayjs(date).format('DD MMM YYYY') : 'N/A';
        });


        handlebars.registerHelper('getColorClass', function (severities) {
            if (!severities || severities.length === 0) return '';
            const uniqueSeverities = [...new Set(severities.map(s => s.severity))].sort((a, b) => a - b);

            if (uniqueSeverities.length === 1) {
                return `color${uniqueSeverities[0]}`;
            }
            const colorClasses = uniqueSeverities.map(severity => `color${severity}`);
            return colorClasses.join(' ') + ' striped';
        });

    } catch (error) {
        console.log('bel q hbC lH 10a, error = ', error);
    }
};

const loadQueryTemplates = (options = {}) => {
    loadHelpers();
    if (options.custom_templates && Object.keys(options.custom_templates).length > 0) {
        queryIndex = { ...options.custom_templates, ...queryIndex };
    }
    global.queryTemplates = {};
    for (const [key, value] of Object.entries(queryIndex)) {
        global.queryTemplates[key] = handlebars.compile(value, { compat: true });
    }
};

const getTemplate = (name) => {
    // loadHelpers()
    if (!global.queryTemplates || global.queryTemplates === null)
        throw new Error("Template are not initialized, please invoke function:loadQueryTemplates first ");
    if (!global.queryTemplates[name])
        throw new Error(`Template with name ${name} does not exist`);
    return global.queryTemplates[name];
};

/**
 * 
 * @param {String} name Name of the template 
 * @param {Object} queryOptions parameters passed to the template
 * @returns {String}
 */
const getCompiledQuery = (name, queryOptions) => {
    return getTemplate(name)(queryOptions);
};

module.exports = { loadQueryTemplates, getCompiledQuery };