const { RELATION_INVENTORY_LEDGER,R<PERSON><PERSON>ION_REFERENCE,RELATION_WAREHOUSE_BLOCKQTY,RELATION_STAFF,SCHEMA_MAIN,ACTIVE,RELATION_REQUISITION_LINE_ITEMS,
    <PERSON><PERSON><PERSON><PERSON>SITION_DRAFT,RE<PERSON><PERSON>SITION_PENDING,REQUISITION_APPROVED_BY_APPROVER} = require('../utils/constant')
const { errorLog } = require('@krushal-it/mobile-or-server-lib')
const { dbConnections, preProcessRecords } = require('@krushal-it/ah-orm')
const { getDocumentJSONConfig } = require('../utils/document')
const { validateUUID } = require('@krushal-it/mobile-or-server-lib')
const { configurationJSON } = require('@krushal-it/common-core')
// const { validate } = require('uuid');
const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0;

const insertInventoryLedger = async (inventory_ledger,transaction) => {
    // return new Promise(async (resolve,reject)=>{
        try{
            const entity_inventory_ledger = dbConnections().main.entities[RELATION_INVENTORY_LEDGER]
            const preProcessedInventoryLedger = preProcessRecords(entity_inventory_ledger, inventory_ledger, entity_inventory_ledger.additionalAttributes)
            let manager = transaction ? transaction : dbConnections().main.manager
            let data = await manager
                .createQueryBuilder()
                .insert()
                .into(RELATION_INVENTORY_LEDGER)
                .values(preProcessedInventoryLedger)
                .execute()
            return data
            // resolve(data)
        }
        catch(e){
            // reject(e)
            throw e
        }
    // })
}

const updateInventoryLedger = async (value, options = {},transaction) => {
    // return new Promise(async (resolve,reject)=>{
        try{
            console.log('bel q c uR 1, value = ', value, ', options = ', options)
            if (!options.inventory_ledger_id) throw new Error('inventory_ledger_id is required')
            let manager = transaction ? transaction : dbConnections().main.manager
            const entity = dbConnections().main.entities[RELATION_INVENTORY_LEDGER]
            console.log('bel q c uR 2, entity.additionalAttributes = ', entity.additionalAttributes)
            const preProcessedValue = preProcessRecords(entity, value, { ...entity.additionalAttributes, id: options.inventory_ledger_id })
            console.log('bel q c uR 3, preProcessedValue = ', preProcessedValue)
            await manager.createQueryBuilder().update(RELATION_INVENTORY_LEDGER).set(preProcessedValue).where(`${RELATION_INVENTORY_LEDGER}_id = :id`, { id: options.inventory_ledger_id }).execute()
            // resolve(true)
            return true
        }
        catch(e){
            // reject(e)
            throw e
        }
    // })
  }

  const getInventoryKit = async(id,med)=>{
    let base_query=`SELECT
    subq.requisition_id,
    subq.requisitioner_type_id,
    subq.requisitioner_type_name_l10n,
    subq.requisitioner_uuid,
    subq.requisitioner_name_l10n,
    subq.medicine_id,
    subq.unit,
    subq.balance,
    subq.blocked_qty,
    subq.requisition_qty,
    subq.inserted_at
    FROM (
        SELECT
        il.requisition_id,
        il.warehouse_type_id AS requisitioner_type_id,
        rf2.reference_name_l10n AS requisitioner_type_name_l10n,
        il.warehouse_uuid AS requisitioner_uuid,
        s.staff_name_l10n AS requisitioner_name_l10n,
        il.medicine_id,
        il.unit,
        il.balance,
        whb.blocked_qty,
        rli.requisition_qty,
        il.inserted_at,
        ROW_NUMBER() OVER (PARTITION BY il.warehouse_uuid, il.medicine_id ORDER BY il.inserted_at DESC) AS rn
    FROM main.${RELATION_INVENTORY_LEDGER} il
        LEFT JOIN main.${RELATION_REFERENCE} rf2 ON rf2.reference_id = il.warehouse_type_id
        LEFT JOIN main.${RELATION_STAFF} s ON s.staff_id = il.warehouse_uuid
        LEFT JOIN (
            SELECT
            warehouse_uuid,
            medicine_id as wmed_id,
            SUM(blocked_qty-unblocked_qty) AS blocked_qty
            FROM main.${RELATION_WAREHOUSE_BLOCKQTY} 
            WHERE active=${ACTIVE} AND warehouse_uuid='${id}'`
            if(med) base_query+=` AND medicine_id in (${med})`
            base_query+=`GROUP BY warehouse_uuid, wmed_id
        ) AS whb ON whb.warehouse_uuid = il.warehouse_uuid AND whb.wmed_id = il.medicine_id
        LEFT JOIN (
            SELECT
                requisitioner_uuid,
                medicine_id AS med_id,
                SUM(quantity) AS requisition_qty
            FROM main.${RELATION_REQUISITION_LINE_ITEMS}
            WHERE (item_status_id=${REQUISITION_DRAFT} OR item_status_id=${REQUISITION_PENDING} OR item_status_id=${REQUISITION_APPROVED_BY_APPROVER}) AND active=${ACTIVE} AND requisitioner_uuid='${id}'`
            if(med) base_query+=` AND medicine_id in (${med})`
            base_query+=`GROUP BY requisitioner_uuid, med_id
        ) AS rli on il.warehouse_uuid= rli.requisitioner_uuid AND il.medicine_id = rli.med_id
    WHERE il.active = ${ACTIVE}
    AND il.warehouse_uuid='${id}'`
    if(med) base_query+=` AND il.medicine_id IN (${med})`    
    base_query+= `) as subq WHERE subq.rn = 1 ORDER BY subq.requisitioner_uuid, subq.medicine_id, subq.inserted_at DESC;` 
    return base_query
}

const getLatestInventoryForUUIDMedid = async(uuid,med_id,transaction)=>{
    // let latestinventory=await dbConnections().main.repos[RELATION_INVENTORY_LEDGER].createQueryBuilder()
    // .select()
    // .where(`${RELATION_INVENTORY_LEDGER}.warehouse_uuid = :uuid`, { uuid })
    // .andWhere(`${RELATION_INVENTORY_LEDGER}.medicine_id = :med_id`, { med_id })
    // .orderBy(`${RELATION_INVENTORY_LEDGER}.created_at = DESC`)
    // .limit(1)
    // return latestinventory
    let manager = transaction ? transaction : dbConnections().main.manager
    let latestinventoryquery = `SELECT * from main.${RELATION_INVENTORY_LEDGER} where warehouse_uuid='${uuid}' and medicine_id=${med_id} order by inserted_at desc limit 1`
    let latestinventory=await manager.query(latestinventoryquery)
    return latestinventory
}

const getMedInventoryKit = async(data)=>{
    let base_query=`SELECT DISTINCT ON (warehouse_uuid,medicine_id)
            il.warehouse_type_id AS staff_type_id,
            il.warehouse_uuid as staff_uuid,
            il.medicine_id,
            il.balance,
            s.staff_name_l10n,
            COALESCE(s.staff_name_l10n->>'en', s.staff_name_l10n->>'ul') as staff_name,
            rf2.reference_name_l10n AS staff_type_l10n
        FROM main.inventory_ledger il
        LEFT JOIN main.staff s ON s.staff_id = il.warehouse_uuid
        LEFT JOIN main.ref_reference rf2 ON rf2.reference_id = s.staff_type_id
        WHERE il.active=${ACTIVE}`
        if(data.med_id && data.staff_id) base_query+=` AND il.medicine_id=${data.med_id} AND il.warehouse_uuid='${data.staff_id}'`
        if(data.med_id && !data.staff_id) base_query+=` AND il.medicine_id=${data.med_id}`
        if(!data.med_id && data.staff_id) base_query+=` AND il.warehouse_uuid='${data.staff_id}'`
        if(data.medicine_id && data.medicine_id.length>0) base_query+=`AND il.medicine_id IN (${data.medicine_id})`
        if(data.staff_type_id && data.staff_type_id.length>0) base_query+=`AND il.warehouse_type_id IN (${data.staff_type_id})`
        if(isPostgres && data.staff_name) base_query+=`AND 
            (   UPPER(s1.staff_name_l10n->>'ul') LIKE '%${data.staff_name.toUpperCase()}%' OR 
                UPPER(s1.staff_name_l10n->>'mr') LIKE '%${data.staff_name.toUpperCase()}%' OR
                UPPER(s1.staff_name_l10n->>'en') LIKE '%${data.staff_name.toUpperCase()}%' OR 
                UPPER(s1.staff_name_l10n->>'hi') LIKE '%${data.staff_name.toUpperCase()}%'
            )`
        if(!isPostgres && data.staff_name) base_query+=`AND UPPER(s1.staff_name) LIKE '%${data.staff_name.toUpperCase()}%'` 
        base_query+=` ORDER BY il.warehouse_uuid,il.medicine_id, il.inserted_at DESC;`
    return base_query
}

const getInventoryLedgerQuery = async(data,transaction)=>{
    try{
        let base_query=`
        SELECT  il.inventory_ledger_id,
            il.warehouse_type_id,
            rf1.reference_name_l10n AS warehouse_type_l10n,
            il.warehouse_uuid,
            s1.staff_name_l10n as warehouse_name_l10n,
            COALESCE(s1.staff_name_l10n->>'en', s1.staff_name_l10n->>'ul') as warehouse_name,
            il.inserted_at,
            il.updated_at,
            il.medicine_id,
            il.unit,
            il.credit_qty,
            il.debit_qty,
            il.balance,
            med.medicine_name_l10n
        FROM main.inventory_ledger AS il
            LEFT JOIN main.ref_reference AS rf1 ON il.warehouse_type_id = rf1.reference_id
            LEFT JOIN main.staff AS s1 ON il.warehouse_uuid = s1.staff_id
            LEFT JOIN main.ref_medicine AS med on il.medicine_id = med.medicine_id
        WHERE
            il.active = ${ACTIVE} AND (il.credit_qty>0 OR il.debit_qty>0)`
            if(data.il_inventory_ledger_id) base_query+=`AND il.inventory_ledger_id='${data.il_inventory_ledger_id}'`
            if(data.il_warehouse_uuid && data.il_warehouse_uuid.length>0) base_query+=`AND il.warehouse_uuid IN ('${data.il_warehouse_uuid}')`           
            if(data.il_warehouse_type && data.il_warehouse_type.length>0) base_query+=`AND il.warehouse_type_id IN (${data.il_warehouse_type})`
            if(data.il_medicine_id && data.il_medicine_id.length>0) base_query+=`AND il.medicine_id IN (${data.il_medicine_id})`  
            if(data.il_created_at_from) base_query+=`AND DATE(il.updated_at) >='${data.il_created_at_from}'`
            if(data.il_created_at_to) base_query+=`AND DATE(il.updated_at) <='${data.il_created_at_to}'`
            if(isPostgres && data.il_warehouse_name) base_query+=`AND 
            (   UPPER(s1.staff_name_l10n->>'ul') LIKE '%${data.il_warehouse_name.toUpperCase()}%' OR 
                UPPER(s1.staff_name_l10n->>'mr') LIKE '%${data.il_warehouse_name.toUpperCase()}%' OR
                UPPER(s1.staff_name_l10n->>'en') LIKE '%${data.il_warehouse_name.toUpperCase()}%' OR 
                UPPER(s1.staff_name_l10n->>'hi') LIKE '%${data.il_warehouse_name.toUpperCase()}%'
            )`
            if(!isPostgres && data.il_warehouse_name) base_query+=`AND UPPER(s1.staff_name) LIKE '%${data.il_warehouse_name.toUpperCase()}%'`
            if(isPostgres && data.il_medicine_name) base_query+=`AND 
            (   UPPER(med.medicine_name_l10n->>'ul') LIKE '%${data.il_medicine_name.toUpperCase()}%' OR 
                UPPER(med.medicine_name_l10n->>'mr') LIKE '%${data.il_medicine_name.toUpperCase()}%' OR
                UPPER(med.medicine_name_l10n->>'en') LIKE '%${data.il_medicine_name.toUpperCase()}%' OR 
                UPPER(med.medicine_name_l10n->>'hi') LIKE '%${data.il_medicine_name.toUpperCase()}%'
            )`
            if(!isPostgres && data.il_medicine_name) base_query+=`AND UPPER(med.medicine_name) LIKE '%${data.il_medicine_name.toUpperCase()}%'`
        base_query+=` ORDER BY il.updated_at DESC`
        let manager = transaction ? transaction : dbConnections().main.manager
        let requisitionlist = await manager.query(base_query)
        // resolve(requisitionlist)
        return requisitionlist
    }
    catch(e){
        // reject(e)
        throw e
    }
}

// rf1.reference_name_l10n AS medicine_name_l10n,
// rf1.reference_information AS medicine_information,
// LEFT JOIN main.${RELATION_REFERENCE} rf1 ON rf1.reference_id = il.medicine_id


module.exports= {insertInventoryLedger,updateInventoryLedger,getInventoryKit,getLatestInventoryForUUIDMedid,getMedInventoryKit,getInventoryLedgerQuery}
