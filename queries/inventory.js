const { ACTIVE, CONST_PRESCRTIPTION_PROCESS_STATUS, CONST_PRESCRTIPTION_MEDICINE_DELIVERY_STATUS, CONST_PRESCRTIPTION_INVOICE_STATUS ,RELATION_REQUISITION, R<PERSON><PERSON><PERSON>SITION_DRAFT, RELATION_STAFF} = require('../utils/constant')
const { dbConnections } = require('@krushal-it/ah-orm')
const { addRequisition } = require('../services/requisition/controller')
const { configurationJSON } = require('@krushal-it/common-core')
const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
const OpenRequisitionForRequisitioner= async(id,params,transaction)=>{
    // return new Promise(async (resolve,reject)=>{
      try{
        let manager = transaction ? transaction : dbConnections().main.manager
        // await manager.transaction(async (transactionalEntityManager) => {
          console.log(id)
          let openquery=`SELECT requisition_id,requisitioner_uuid,status_id FROM main.${RELATION_REQUISITION} where requisitioner_uuid='${id}' AND status_id=${REQUISITION_DRAFT} AND active=${ACTIVE}`
          let openreqlist= await manager.query(openquery)
          console.log('openreqlist==>1',openreqlist)
          if(!openreqlist || openreqlist.length==0){
            console.log('called inside openreq')
            let staffquery=`SELECT staff_id,staff_type_id FROM main.${RELATION_STAFF} where staff_id='${id}'`
            let staff=await manager.query(staffquery)
            console.log('staff===>',staff)
            let req= await addRequisition({
              requisitioner_type_id:staff[0].staff_type_id,
              requisitioner_uuid:staff[0].staff_id,
              status_id:REQUISITION_DRAFT
            },params,transaction)
            openreqlist=await manager.query(openquery)
          }
          // resolve(openreqlist)
          return openreqlist
        // })
      }
      catch(e){
        console.log('OpenRequisitionForRequisitioner',e)
        // reject(e)
        throw e
      }
    // })
  }
const allRequisition = async (data, transaction) => {
  try {
    let dt = isPostgres ? 'NOW()' : 'CURRENT_TIMESTAMP'
    let base_query = `
            SELECT  r.requisition_id,
                r.requisitioner_type_id,
                rf2.reference_name_l10n AS requisitioner_type_l10n,
                r.requisitioner_uuid,
                s1.staff_name_l10n as requisitioner_name_l10n,
                r.approver_type_id,
                rf3.reference_name_l10n AS approver_type_l10n,
                r.approver_uuid,
                s2.staff_name_l10n as approver_name_l10n,
                r.fullfiller_type_id,
                rf4.reference_name_l10n AS fullfiller_type_l10n,
                r.fullfiller_uuid,
                COALESCE(p.partner_name_l10n,s3.staff_name_l10n) AS fullfiller_name_l10n,
                r.line_items_count,
                r.status_id,
                rf1.reference_name_l10n AS status_l10n,
                r.created_at,
                r.inserted_at,
                r.updated_at,
                pps.status_id AS prescription_process_status_id,
                rf6.reference_name_l10n AS prescription_process_status_l10n,
                pmds.status_id AS prescription_medicine_delivery_status_id,
                rf7.reference_name_l10n AS prescription_medicine_delivery_status_l10n,
                pis.status_id AS prescription_invoice_status_id,
                rf8.reference_name_l10n AS prescription_invoice_status_l10n
            FROM main.requisition AS r

                LEFT JOIN main.ref_reference AS rf1 ON rf1.reference_id = r.status_id
                LEFT JOIN main.ref_reference AS rf2 ON r.requisitioner_type_id = rf2.reference_id
                LEFT JOIN main.staff AS s1 ON r.requisitioner_uuid = s1.staff_id
                LEFT JOIN main.ref_reference AS rf3 ON r.approver_type_id = rf3.reference_id
                LEFT JOIN main.staff AS s2 ON r.approver_uuid = s2.staff_id
                LEFT JOIN main.ref_reference AS rf4 ON r.fullfiller_type_id = rf4.reference_id
                LEFT JOIN main.partner AS p ON r.fullfiller_uuid = p.partner_id
                LEFT JOIN main.staff AS s3 ON r.fullfiller_uuid = s3.staff_id
                LEFT JOIN 
                (
                    SELECT status_id,entity_1_entity_uuid
                    FROM main.entity_status
                    WHERE status_category_id=${CONST_PRESCRTIPTION_PROCESS_STATUS} AND start_date<=${dt} AND end_date>=${dt}
                ) AS pps on pps.entity_1_entity_uuid=r.requisition_id
                LEFT JOIN main.ref_reference AS rf6 ON rf6.reference_id = pps.status_id
                LEFT JOIN 
                (
                    SELECT status_id,entity_1_entity_uuid
                    FROM main.entity_status
                    WHERE status_category_id=${CONST_PRESCRTIPTION_MEDICINE_DELIVERY_STATUS} AND start_date<=${dt} AND end_date>=${dt}
                ) AS pmds on pmds.entity_1_entity_uuid=r.requisition_id
                LEFT JOIN main.ref_reference AS rf7 ON rf7.reference_id = pmds.status_id
                LEFT JOIN 
                (
                    SELECT status_id,entity_1_entity_uuid
                    FROM main.entity_status
                    WHERE status_category_id=${CONST_PRESCRTIPTION_INVOICE_STATUS} AND start_date<=${dt} AND end_date>=${dt}
                ) AS pis on pis.entity_1_entity_uuid=r.requisition_id
                LEFT JOIN main.ref_reference AS rf8 ON rf8.reference_id = pis.status_id
            WHERE
                r.active = ${ACTIVE} `
    if (data.r_id) base_query += ` AND r.requisition_id = '${data.r_id}' `
    if (data.r_requisitioner && data.r_requisitioner.length > 0) base_query += ` AND r.requisitioner_uuid IN ('${data.r_requisitioner}') `
    if (data.r_status && data.r_status.length > 0) base_query += ` AND r.status_id IN (${data.r_status}) `
    if (data.r_fullfiller && data.r_fullfiller.length > 0) base_query += ` AND r.fullfiller_uuid IN ('${data.r_fullfiller}') `
    if (data.r_approver && data.r_approver.length > 0) base_query += ` AND r.approver_uuid IN ('${data.r_approver}') `
    if (data.r_requisitioner_type && data.r_requisitioner_type.length > 0) base_query += ` AND r.requisitioner_type_id IN (${data.r_requisitioner_type}) `
    if (data.r_approver_type && data.r_approver_type.length > 0) base_query += ` AND r.approver_type_id IN (${data.r_approver_type}) `
    if (data.r_fullfiller_type && data.r_fullfiller_type.length > 0) base_query += ` AND r.fullfiller_type_id IN (${data.r_fullfiller_type}) `
    if (data.r_created_at_from) base_query += ` AND DATE(r.created_at) >='${data.r_created_at_from}' `
    if (data.r_created_at_to) base_query += ` AND DATE(r.created_at) <='${data.r_created_at_to}' `
    if (data.r_inserted_at_from) base_query += ` AND DATE(r.inserted_at) >='${data.r_inserted_at_from}' `
    if (data.r_inserted_at_to) base_query += ` AND DATE(r.inserted_at) <='${data.r_inserted_at_to}' `
    if (data.r_updated_at_from) base_query += ` AND DATE(r.updated_at) >='${data.r_updated_at_from}' `
    if (data.r_updated_at_to) base_query += ` AND DATE(r.updated_at) <='${data.r_updated_at_to}' `
    if (isPostgres && data.r_requisitioner_name)
      base_query += ` AND 
                (   UPPER(s1.staff_name_l10n->>'ul') LIKE '%${data.r_requisitioner_name.toUpperCase()}%' OR 
                    UPPER(s1.staff_name_l10n->>'mr') LIKE '%${data.r_requisitioner_name.toUpperCase()}%' OR
                    UPPER(s1.staff_name_l10n->>'en') LIKE '%${data.r_requisitioner_name.toUpperCase()}%' OR 
                    UPPER(s1.staff_name_l10n->>'hi') LIKE '%${data.r_requisitioner_name.toUpperCase()}%'
                ) `
    if (!isPostgres && data.r_requisitioner_name) base_query += ` AND UPPER(s1.staff_name) LIKE '%${data.r_requisitioner_name.toUpperCase()}%' `
    if (isPostgres && data.r_approver_name)
      base_query += ` AND 
                (   UPPER(s2.staff_name_l10n->>'ul') LIKE '%${data.r_approver_name.toUpperCase()}%' OR 
                    UPPER(s2.staff_name_l10n->>'mr') LIKE '%${data.r_approver_name.toUpperCase()}%' OR
                    UPPER(s2.staff_name_l10n->>'en') LIKE '%${data.r_approver_name.toUpperCase()}%' OR 
                    UPPER(s2.staff_name_l10n->>'hi') LIKE '%${data.r_approver_name.toUpperCase()}%'
                ) `
    if (!isPostgres && data.r_approver_name) base_query += ` AND UPPER(s2.staff_name_l10n) LIKE '%${data.r_approver_name.toUpperCase()}%' `
    if (isPostgres && data.r_fullfiller_name)
      base_query += ` AND 
                (   UPPER(p.partner_name_l10n->>'ul') LIKE '%${data.r_fullfiller_name.toUpperCase()}%' OR 
                    UPPER(p.partner_name_l10n->>'mr') LIKE '%${data.r_fullfiller_name.toUpperCase()}%' OR
                    UPPER(p.partner_name_l10n->>'en') LIKE '%${data.r_fullfiller_name.toUpperCase()}%' OR 
                    UPPER(p.partner_name_l10n->>'hi') LIKE '%${data.r_fullfiller_name.toUpperCase()}%'
                ) OR
                (   UPPER(s3.staff_name_l10n->>'ul') LIKE '%${data.r_fullfiller_name.toUpperCase()}%' OR 
                    UPPER(s3.staff_name_l10n->>'mr') LIKE '%${data.r_fullfiller_name.toUpperCase()}%' OR
                    UPPER(s3.staff_name_l10n->>'en') LIKE '%${data.r_fullfiller_name.toUpperCase()}%' OR 
                    UPPER(s3.staff_name_l10n->>'hi') LIKE '%${data.r_fullfiller_name.toUpperCase()}%'
                ) `
    if (!isPostgres && data.r_fullfiller_name) base_query += ` AND UPPER(s3.staff_name_l10n) LIKE '%${data.r_fullfiller_name.toUpperCase()}%' `
    if (data.r_pps_id && data.r_pps_id.length > 0) base_query += ` AND pps.status_id IN (${data.r_pps_id}) `
    if (data.r_pmds_id && data.r_pmds_id.length > 0) base_query += ` AND pmds.status_id IN (${data.r_pmds_id}) `
    if (data.r_pis_id && data.r_pis_id.length > 0) base_query += ` AND pis.status_id IN (${data.r_pis_id}) `
    base_query += ` ORDER BY r.updated_at DESC `
    let manager = transaction ? transaction : dbConnections().main.manager
    let requisitionlist = await manager.query(base_query)
    // resolve(requisitionlist)
    return requisitionlist
  } catch (e) {
    // reject(e)
    throw e
  }
  // })
}

const getUsersRequisition = async (data , user_id, transaction = null) => {
 try {
    let dt = isPostgres ? 'NOW()' : 'CURRENT_TIMESTAMP'
    let base_query = `
            SELECT  r.requisition_id,
                r.requisitioner_type_id,
                rf2.reference_name_l10n AS requisitioner_type_l10n,
                r.requisitioner_uuid,
                s1.staff_name_l10n as requisitioner_name_l10n,
                r.approver_type_id,
                rf3.reference_name_l10n AS approver_type_l10n,
                r.approver_uuid,
                s2.staff_name_l10n as approver_name_l10n,
                r.fullfiller_type_id,
                rf4.reference_name_l10n AS fullfiller_type_l10n,
                r.fullfiller_uuid,
                COALESCE(p.partner_name_l10n,s3.staff_name_l10n) AS fullfiller_name_l10n,
                r.line_items_count,
                r.status_id,
                rf1.reference_name_l10n AS status_l10n,
                r.created_at,
                r.inserted_at,
                r.updated_at,
                pps.status_id AS prescription_process_status_id,
                rf6.reference_name_l10n AS prescription_process_status_l10n,
                pmds.status_id AS prescription_medicine_delivery_status_id,
                rf7.reference_name_l10n AS prescription_medicine_delivery_status_l10n,
                pis.status_id AS prescription_invoice_status_id,
                rf8.reference_name_l10n AS prescription_invoice_status_l10n
            FROM main.requisition AS r

                LEFT JOIN main.ref_reference AS rf1 ON rf1.reference_id = r.status_id
                LEFT JOIN main.ref_reference AS rf2 ON r.requisitioner_type_id = rf2.reference_id
                LEFT JOIN main.staff AS s1 ON r.requisitioner_uuid = s1.staff_id
                LEFT JOIN main.ref_reference AS rf3 ON r.approver_type_id = rf3.reference_id
                LEFT JOIN main.staff AS s2 ON r.approver_uuid = s2.staff_id
                LEFT JOIN main.ref_reference AS rf4 ON r.fullfiller_type_id = rf4.reference_id
                LEFT JOIN main.partner AS p ON r.fullfiller_uuid = p.partner_id
                LEFT JOIN main.staff AS s3 ON r.fullfiller_uuid = s3.staff_id
                LEFT JOIN 
                (
                    SELECT status_id,entity_1_entity_uuid
                    FROM main.entity_status
                    WHERE status_category_id=${CONST_PRESCRTIPTION_PROCESS_STATUS} AND start_date<=${dt} AND end_date>=${dt}
                ) AS pps on pps.entity_1_entity_uuid=r.requisition_id
                LEFT JOIN main.ref_reference AS rf6 ON rf6.reference_id = pps.status_id
                LEFT JOIN 
                (
                    SELECT status_id,entity_1_entity_uuid
                    FROM main.entity_status
                    WHERE status_category_id=${CONST_PRESCRTIPTION_MEDICINE_DELIVERY_STATUS} AND start_date<=${dt} AND end_date>=${dt}
                ) AS pmds on pmds.entity_1_entity_uuid=r.requisition_id
                LEFT JOIN main.ref_reference AS rf7 ON rf7.reference_id = pmds.status_id
                LEFT JOIN 
                (
                    SELECT status_id,entity_1_entity_uuid
                    FROM main.entity_status
                    WHERE status_category_id=${CONST_PRESCRTIPTION_INVOICE_STATUS} AND start_date<=${dt} AND end_date>=${dt}
                ) AS pis on pis.entity_1_entity_uuid=r.requisition_id
                LEFT JOIN main.ref_reference AS rf8 ON rf8.reference_id = pis.status_id
            WHERE
                r.active = ${ACTIVE} `
    base_query += ` AND r.requisitioner_uuid = '${user_id}'`
    if (data.r_status && data.r_status.length > 0) base_query += ` AND r.status_id IN (${data.r_status}) `
    base_query += ` ORDER BY r.updated_at DESC `
    let manager = transaction ? transaction : dbConnections().main.manager
    let requisitionData = await manager.query(base_query)
    return requisitionData
 } catch (error) {  
    throw new Error("went wrong while getting the data")
 }
}

const getRequisitionById = async (requisition_id) => {
    try {
        let dt = isPostgres ? 'NOW()' : 'CURRENT_TIMESTAMP'
        let base_query = `
                SELECT  r.requisition_id,
                    r.requisitioner_type_id,
                    rf2.reference_name_l10n AS requisitioner_type_l10n,
                    r.requisitioner_uuid,
                    s1.staff_name_l10n as requisitioner_name_l10n,
                    r.approver_type_id,
                    rf3.reference_name_l10n AS approver_type_l10n,
                    r.approver_uuid,
                    s2.staff_name_l10n as approver_name_l10n,
                    r.fullfiller_type_id,
                    rf4.reference_name_l10n AS fullfiller_type_l10n,
                    r.fullfiller_uuid,
                    COALESCE(p.partner_name_l10n,s3.staff_name_l10n) AS fullfiller_name_l10n,
                    r.line_items_count,
                    r.status_id,
                    rf1.reference_name_l10n AS status_l10n,
                    r.created_at,
                    r.inserted_at,
                    r.updated_at,
                    pps.status_id AS prescription_process_status_id,
                    rf6.reference_name_l10n AS prescription_process_status_l10n,
                    pmds.status_id AS prescription_medicine_delivery_status_id,
                    rf7.reference_name_l10n AS prescription_medicine_delivery_status_l10n,
                    pis.status_id AS prescription_invoice_status_id,
                    rf8.reference_name_l10n AS prescription_invoice_status_l10n
                FROM main.requisition AS r
    
                    LEFT JOIN main.ref_reference AS rf1 ON rf1.reference_id = r.status_id
                    LEFT JOIN main.ref_reference AS rf2 ON r.requisitioner_type_id = rf2.reference_id
                    LEFT JOIN main.staff AS s1 ON r.requisitioner_uuid = s1.staff_id
                    LEFT JOIN main.ref_reference AS rf3 ON r.approver_type_id = rf3.reference_id
                    LEFT JOIN main.staff AS s2 ON r.approver_uuid = s2.staff_id
                    LEFT JOIN main.ref_reference AS rf4 ON r.fullfiller_type_id = rf4.reference_id
                    LEFT JOIN main.partner AS p ON r.fullfiller_uuid = p.partner_id
                    LEFT JOIN main.staff AS s3 ON r.fullfiller_uuid = s3.staff_id
                        LEFT JOIN 
                    (
                        SELECT status_id,entity_1_entity_uuid
                        FROM main.entity_status
                        WHERE status_category_id=${CONST_PRESCRTIPTION_PROCESS_STATUS} AND start_date<=${dt} AND end_date>=${dt}
                    ) AS pps on pps.entity_1_entity_uuid=r.requisition_id
                    LEFT JOIN main.ref_reference AS rf6 ON rf6.reference_id = pps.status_id
                    LEFT JOIN 
                    (
                        SELECT status_id,entity_1_entity_uuid
                        FROM main.entity_status
                        WHERE status_category_id=${CONST_PRESCRTIPTION_MEDICINE_DELIVERY_STATUS} AND start_date<=${dt} AND end_date>=${dt}
                    ) AS pmds on pmds.entity_1_entity_uuid=r.requisition_id
                    LEFT JOIN main.ref_reference AS rf7 ON rf7.reference_id = pmds.status_id
                    LEFT JOIN 
                    (
                        SELECT status_id,entity_1_entity_uuid
                        FROM main.entity_status
                        WHERE status_category_id=${CONST_PRESCRTIPTION_INVOICE_STATUS} AND start_date<=${dt} AND end_date>=${dt}
                    ) AS pis on pis.entity_1_entity_uuid=r.requisition_id
                    LEFT JOIN main.ref_reference AS rf8 ON rf8.reference_id = pis.status_id
                WHERE
                    r.active = ${ACTIVE} `
        base_query += ` AND r.requisition_id = '${requisition_id}'`
        base_query += ` ORDER BY r.updated_at DESC `
        let manager = dbConnections().main.manager
        let requisitionData = await manager.query(base_query)
        return requisitionData
     } catch (error) {  
        throw new Error("went wrong while getting the data")
     }
}

const loadRequisitionLineItem = async (requisition_id) => {
    let base_query = `
        select  requisition_line_items_id,
        medicine_id,
        unit,
        quantity,
        item_status_id from main.requisition_line_items where requisition_id = '${requisition_id}' and active =${ACTIVE}    
    `
    let manager = dbConnections().main.manager
    let requisitionData = await manager.query(base_query)
    return requisitionData
}

module.exports = { allRequisition , OpenRequisitionForRequisitioner , getUsersRequisition , getRequisitionById ,loadRequisitionLineItem}