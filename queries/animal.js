const { RELATION_ANIMAL, RELATION_RELATIONSHIP, RELATION_ANIMAL_DISEASE, RELATION_DISEASE_CLASSIFICATION, RELATION_DOC, RELATION_REFERENCE, RELATION_ANIMAL_CLASSIFICATION, SCHEMA_MAIN } = require('../utils/constant')
const { errorLog } = require('@krushal-it/mobile-or-server-lib')
const { dbConnections, preProcessRecords } = require('@krushal-it/ah-orm')
const { getDocumentJSONConfig } = require('../utils/document')
const { validateUUID } = require('@krushal-it/mobile-or-server-lib')
const { configurationJSON } = require('@krushal-it/common-core')
const ENUMS = require('../ENUMS')
// const { validate } = require('uuid');

const getAnimalByFarmersId = async () => {
  let paramIndex = 1
  let params = []
  let classificationDTypes = dtype.map((col) => `${RELATION_ANIMAL_CLASSIFICATION}.${col}`)
  classificationDTypes = [...classificationDTypes, `${RELATION_ANIMAL_CLASSIFICATION}.classifier_id`].join()

  let entityInnerQuery = `FROM ${SCHEMA_MAIN}.${RELATION_RELATIONSHIP}`

  let classificationInnerQuery = `SELECT * FROM ${SCHEMA_MAIN}.${RELATION_ANIMAL_CLASSIFICATION}`
  if (Array.isArray(options.customer_id) && options.customer_id.length > 0) {
    entityInnerQuery +=
      ' ' +
      `WHERE ${RELATION_RELATIONSHIP}.entity_1_entity_uuid  = ANY($${paramIndex}) 
    AND ${RELATION_RELATIONSHIP}.entity_relationship_type_id = **********`
    paramIndex++

    params.push(options.customer_id)
  }

  // calculating row count before pagination
  let count = await dbConnections().main.manager.query(`SELECT COUNT(*) as count ${entityInnerQuery}`, params)
  count = count[0].count
  entityInnerQuery = `SELECT * ${entityInnerQuery}`

  // Setting Pagination: Page limit and offset will be applied at animal level
  if (typeof options.limit === 'number' && typeof options.offset === 'number') {
    entityInnerQuery += ' ' + `LIMIT $${paramIndex}`
    params.push(options.limit)
    paramIndex++
    entityInnerQuery += ' ' + `OFFSET $${paramIndex}`
    params.push(options.offset)
    paramIndex++
  }

  // Picking selected classification befor JOIN to narrow down the results
  if (options.classificationIds) {
    classificationInnerQuery += ' ' + `WHERE ${RELATION_ANIMAL_CLASSIFICATION}.classifier_id = ANY($${paramIndex}) `
    paramIndex++
    params.push(options.classificationIds)
  }
  try {
    let base_query = `SELECT *, ${RELATION_RELATIONSHIP}.entity_2_entity_uuid,${RELATION_RELATIONSHIP}.entity_1_entity_id,${RELATION_RELATIONSHIP}.created_at,${RELATION_RELATIONSHIP}.updated_at,${classificationDTypes}
    
        FROM (${entityInnerQuery}) as ${RELATION_RELATIONSHIP} LEFT JOIN (${classificationInnerQuery}) as ${RELATION_ANIMAL_CLASSIFICATION} ON ${RELATION_RELATIONSHIP}.entity_2_entity_uuid = ${RELATION_ANIMAL_CLASSIFICATION}.animal_id`

    if (options.image === 1) {
      const documentTypes = await getDocumentJSONConfig()
      entityInnerQuery = `SELECT ${RELATION_RELATIONSHIP}.*,${RELATION_DOC}.document_information,${RELATION_DOC}.document_id FROM (${entityInnerQuery}) AS ${RELATION_RELATIONSHIP} LEFT JOIN ${SCHEMA_MAIN}.${RELATION_DOC} ON ${RELATION_RELATIONSHIP}.entity_2_entity_uuid = ${RELATION_DOC}.entity_1_entity_uuid
      WHERE ${RELATION_DOC}.document_type_id = ${documentTypes['animal_thumbnail_1'].ref_reference_reference_id} OR ${RELATION_DOC}.document_type_id = ${documentTypes['animal_photo_any_angle_1'].ref_reference_reference_id} OR (${RELATION_RELATIONSHIP}.entity_2_entity_uuid IS NOT NULL AND ${RELATION_DOC}.document_id IS NULL)`
      // Modifying base query
      base_query = `SELECT *, ${RELATION_RELATIONSHIP}.document_information,${RELATION_RELATIONSHIP}.document_id,${RELATION_RELATIONSHIP}.entity_2_entity_id,${RELATION_RELATIONSHIP}.entity_1_entity_id,${RELATION_RELATIONSHIP}.created_at,${RELATION_RELATIONSHIP}.updated_at,${classificationDTypes} 
      FROM (${entityInnerQuery}) as ${RELATION_RELATIONSHIP} LEFT JOIN (${classificationInnerQuery}) as ${RELATION_ANIMAL_CLASSIFICATION} ON ${RELATION_RELATIONSHIP}.entity_2_entity_uuid = ${RELATION_ANIMAL_CLASSIFICATION}.animal_id WHERE ${RELATION_ANIMAL_CLASSIFICATION}.animal_id IS NOT NULL`
    }

    base_query = `SELECT ${RELATION_RELATIONSHIP}.*,${RELATION_ANIMAL}.animal_visual_id FROM (${base_query}) AS ${RELATION_RELATIONSHIP}
        JOIN ${SCHEMA_MAIN}.${RELATION_ANIMAL} ON ${RELATION_ANIMAL}.animal_id = ${RELATION_RELATIONSHIP}.animal_id`
    let result = await dbConnections().main.manager.query(base_query, params)
    return { count, result }
  } catch (error) {
    errorLog('error in getAnimalByFarmersId:84', params, { message: error.message })
    return null
  }
}

const q_queryGetAnimalById = (options = {}) => {
  // if (!validate(options.animal_id)) throw new Error("Invalid UUID:animal_id");
  if (!validateUUID(options.animal_id)) throw new Error('Invalid UUID:animal_id')
  let base_query = `(SELECT * FROM main.animal WHERE animal_id =  '${options.animal_id}') AS animal`
  let projection = ['animal.*']
  if (options.classificationJson) {
    for (const classifier of Object.values(options.classificationJson)) {
      if (classifier.cardinality === 'one') {
        base_query += `
        LEFT JOIN main.animal_classification AS ${classifier.field_name}
        ON ${classifier.field_name}.animal_id = animal.animal_id
        AND ${classifier.field_name}.classifier_id = ${classifier.reference_id}`
        projection.push(`${classifier.field_name}.${classifier.type_of_data} AS ${classifier.field_name}`)
      } else if (classifier.cardinality === 'many') {
        let innerClassification = `SELECT 
         array_agg(${classifier.type_of_data}) AS ${classifier.type_of_data},
         animal_id
         FROM main.animal_classification AS ac
         WHERE ac.classifier_id = ${classifier.reference_id}
         GROUP BY ac.animal_id`
        base_query += `
         LEFT JOIN (${innerClassification}) AS ${classifier.field_name}
         ON ${classifier.field_name}.animal_id = animal.animal_id`
        projection.push(`${classifier.field_name}.${classifier.type_of_data} AS ${classifier.field_name}`)
      }
    }
  }
  // Join owner
  base_query += `
      JOIN main.entity_relationship AS er ON 
      er.entity_relationship_type_id = ********** AND er.entity_2_entity_uuid = '${options.animal_id}'
      JOIN main.customer ON customer.customer_id = er.entity_1_entity_uuid
      `
  projection.push('customer.customer_name_l10n')
  //join media
  base_query = `SELECT ${projection} FROM ${base_query}`
  return base_query
}

const insertToAnimal = async (animal) => {
  const entity_animal = dbConnections().main.entities[RELATION_ANIMAL]
  const preProcessedAnimal = preProcessRecords(entity_animal, animal, entity_animal.additionalAttributes)
  let data = await dbConnections()
    .main.manager.createQueryBuilder()
    .insert()
    .into(RELATION_ANIMAL)
    .values(preProcessedAnimal)
    // .returning(`animal_id`)
    .execute()
  return data
}

const insertToEntity = async (entity_1_entity_id, entity_2_entity_id, entity_relationship_type_id, entity_information) => {
  let value = {
    entity_1_entity_uuid: entity_1_entity_id,
    entity_2_entity_uuid: entity_2_entity_id,
    entity_relationship_type_id,
    entity_information,
  }
  const entity_entity = dbConnections().main.entities[RELATION_RELATIONSHIP]
  const preProcessedValue = preProcessRecords(entity_entity, value, entity_entity.additionalAttributes)
  let data = await dbConnections().main.manager.createQueryBuilder().insert().into(RELATION_RELATIONSHIP).values(preProcessedValue).execute()
  return data
}
const getDiseasseByAnimalId = async (options = {}) => {
  if (!options.animal_id) throw new Error('aniaml_id is required')
  const { datasource, animal_id } = options
  let mdatasource = datasource ? datasource : dbConnections().main.manager
  let params = []
  let paramIndex = 1

  // Base disease query
  let base_query = `SELECT ${RELATION_ANIMAL_DISEASE}.animal_disease_id,${RELATION_ANIMAL_DISEASE}.disease_id,${RELATION_ANIMAL_DISEASE}.disease_date
  FROM ${SCHEMA_MAIN}.${RELATION_ANIMAL_DISEASE} WHERE ${RELATION_ANIMAL_DISEASE}.animal_id = $${paramIndex} AND ${RELATION_ANIMAL_DISEASE}.active = **********`
  paramIndex++
  params.push(animal_id)

  if (options.classifiers === 1) {
    //Join disease name
    base_query = `SELECT ${RELATION_ANIMAL_DISEASE}.*,${RELATION_REFERENCE}.reference_name AS animal_disease_type_1,
    ${RELATION_REFERENCE}.reference_name_l10n AS animal_disease_type_1_json
    FROM (${base_query}) AS ${RELATION_ANIMAL_DISEASE}  JOIN ${SCHEMA_MAIN}.${RELATION_REFERENCE} ON ${RELATION_ANIMAL_DISEASE}.disease_id = ${RELATION_REFERENCE}.reference_id`

    // Joining classifiers
    let types = []
    for (let typeIndex = 0; typeIndex < dtype.length; typeIndex++) types.push(`${RELATION_DISEASE_CLASSIFICATION}.${dtype[typeIndex]}`)
    types = types.join(',')

    base_query = `SELECT ${RELATION_ANIMAL_DISEASE}.*,${RELATION_DISEASE_CLASSIFICATION}.classifier_id,${types}
    FROM (${base_query}) AS ${RELATION_ANIMAL_DISEASE} LEFT JOIN ${SCHEMA_MAIN}.${RELATION_DISEASE_CLASSIFICATION}
    ON ${RELATION_ANIMAL_DISEASE}.animal_disease_id = ${RELATION_DISEASE_CLASSIFICATION}.animal_disease_id`
  }

  return await mdatasource.query(base_query, params)
}
const deleteDiseasesClassifiers = async (options = {}) => {
  const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
  let { disease_id } = options
  let manager = options.datasource ? options.datasource : global.datasource
  const valueToBeUpdated = { active: 1000100002 }
  if (!isPostgres) {
    valueToBeUpdated['data_sync_status'] = 1000101003
  }
  if (typeof disease_id === 'string') {
    disease_id = [disease_id]
  }
  await manager
    .createQueryBuilder()
    .update(RELATION_DISEASE_CLASSIFICATION)
    .set(valueToBeUpdated)
    // .delete()
    // .from(RELATION_DISEASE_CLASSIFICATION)
    .where(`${RELATION_DISEASE_CLASSIFICATION}.animal_disease_id IN (:...disease_id)`, {
      disease_id,
    })
    .execute()
}

const deleteDisease = async (options = {}) => {
  const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
  let { disease_id } = options
  let manager = options.datasource ? options.datasource : global.datasource
  const valueToBeUpdated = { active: 1000100002 }
  if (!isPostgres) {
    valueToBeUpdated['data_sync_status'] = 1000101003
  }
  if (typeof disease_id === 'string') {
    disease_id = [disease_id]
  }
  await manager
    .createQueryBuilder()
    // .delete()
    // .from(RELATION_ANIMAL_DISEASE)
    .update(RELATION_ANIMAL_DISEASE)
    .set(valueToBeUpdated)
    .where(`${RELATION_ANIMAL_DISEASE}.animal_disease_id IN (:...disease_id)`, {
      disease_id,
    })
    .execute()
}

const q_getAnimalPrimaryById = (options = {}) => {
  const { animal_id } = options
  let base_query = `
    select animal_visual_id,animal_type,animal_id , active from main.animal
    where animal.animal_id = '${animal_id}' `
  return base_query
}

const q_getAnimalByFarmersId = (options = {}) => {
  const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
  //animal_id,name, eartag, animal_type, breed, subscription type, subscribtion date, animal profile id
  let { user_id, user_type, customer_id } = options

  customer_id = user_type === 1000220001 ? user_id : customer_id

  let authJoin = `JOIN main.entity_geography as ueg on ueg.entity_type_id = ${user_type} 
        and ueg.geography_type_id = ********** and ueg.geography_id = vc.value_reference_id
        and ueg.active = **********
        and ueg.entity_uuid = '${user_id}'`

  let base_query = `
 
    SELECT 
    a.animal_id,
    a.animal_visual_id,
    a.active,
    etag.value_string_256 as ear_tag_1,
    aname.value_string_256 as animal_name_1,
    atype.reference_name_l10n as animal_type,
    ab.reference_name_l10n as animal_breed_1,
    sd.value_date as healthcare_plan_subscription_date_1,
    stype.reference_name_l10n as subscription_plan_1,
    ad.document_id as animal_document_id,
    ad.document_information, ad.client_document_information ,
    healthScore.value_double as health_score,
    healthScore.updated_at as health_score_updated_at
    FROM main.animal as a
    
    JOIN main.entity_relationship AS er ON er.entity_relationship_type_id = ********** 
        AND er.entity_1_entity_uuid = '${customer_id}' 
        AND er.entity_2_entity_uuid = a.animal_id 
        AND er.active = **********
    
    LEFT JOIN main.animal_classification AS etag ON etag.classifier_id = ********** 
        AND etag.animal_id = a.animal_id 
        AND etag.active = **********
   LEFT JOIN main.animal_classification AS aname ON aname.classifier_id = ********** 
        AND aname.animal_id = a.animal_id 
        AND aname.active = **********
    
    LEFT JOIN main.animal_classification AS atypec ON atypec.classifier_id = **********
        AND atypec.animal_id = a.animal_id
        AND atypec.active = **********
    LEFT JOIN main.ref_reference as atype ON atypec.value_reference_id = atype.reference_id
        AND atype.active = **********
    
    LEFT JOIN main.animal_classification AS abc ON abc.classifier_id = 2000000036
        AND abc.animal_id = a.animal_id
        AND abc.active = **********
    LEFT JOIN main.ref_reference as ab ON ab.reference_id = abc.value_reference_id
        AND ab.active = **********
    
    LEFT JOIN main.animal_classification AS sd ON sd.classifier_id = **********
        AND sd.animal_id = a.animal_id 
        AND sd.active = **********
    
    LEFT JOIN main.animal_classification AS stypec ON stypec.classifier_id = **********
        AND stypec.animal_id = a.animal_id
        AND stypec.active = **********

    LEFT JOIN main.animal_classification AS  healthScore ON healthScore.classifier_id = **********
      AND healthScore.animal_id = a.animal_id
      AND healthScore.active = **********
    
    LEFT JOIN main.ref_reference AS stype ON stype.reference_id = stypec.value_reference_id
        AND stypec.active = **********
    
    LEFT JOIN main.customer_classification as vc ON vc.customer_id = '${customer_id}'
        AND vc.classifier_id = **********
        AND vc.active = **********
    LEFT JOIN (
      SELECT ad.entity_1_entity_uuid, ${isPostgres ? 'min(document_id::text)' : 'min(document_id)'} as document_id
      FROM main.document ad, (
        SELECT entity_1_entity_uuid, max(created_at) as created_at
        FROM main.document
        WHERE document_type_id=**********
        GROUP BY entity_1_entity_uuid
      ) admxca 
      WHERE ad.entity_1_entity_uuid = admxca.entity_1_entity_uuid and ad.created_at = admxca.created_at and ad.document_type_id=**********
      GROUP BY ad.entity_1_entity_uuid
    ) as amdoc on amdoc.entity_1_entity_uuid  = a.animal_id
    LEFT JOIN main.document AS ad ON ad.document_id = ${isPostgres ? 'amdoc.document_id::uuid ' : 'amdoc.document_id '}
    WHERE ${user_type !== 1000230002 ? 'a.active=********** AND ' : ''} 1=1
    ORDER BY a.animal_visual_id
    `
  return base_query
}

const q_animalsWithM1orM2Activity = (options = {}) => {
  let base_query = `
        SELECT 
        ccm1.activity_date AS dtcbm1,
        a.animal_id,
        er.entity_1_entity_uuid as customer_id
        FROM main.animal AS a
    
        /*JOINING FARMER*/
        JOIN main.entity_relationship as er ON
          er.entity_relationship_type_id = ********** 
          AND er.entity_2_entity_uuid = a.animal_id
          AND er.active = **********
  	
	      LEFT JOIN main.care_calendar as ccm1 ON 
		      ccm1.activity_id in( 1000180001,1000180012 )
		      AND ccm1.entity_type_id = 1000460002 
          AND ccm1.entity_uuid = a.animal_id
  `
  return base_query
}
const q_animalForFarmActivity = (options = {}) => {
  const base_query = `
            SELECT 
              a.animal_id,
              a.animal_visual_id,
              atrs.reference_name AS animal_breed,
              acs.reference_name AS stage,
              dob.value_date AS animal_dob,
              ec_pregnancy.value_date AS number_of_months_pregnant_as_of_date,
              ec_pregnancy_count.value_double AS number_of_months_pregnant,
              ec_calvings.value_int AS months_of_calvings,
              ec_calvings_count.value_int AS no_of_calvings,
              ec_ear_tag.value_string_256 AS ear_tag,
              atr.reference_id AS animal_type_id,
              atr.reference_name_l10n AS animal_type_l10n,
              er.entity_1_entity_uuid AS customer_id,
              ad.document_id
          FROM 
              main.animal AS a

          -- JOINING FARMER
          JOIN 
              main.entity_relationship AS er ON
              er.entity_relationship_type_id = ${ENUMS.relationship_types.FARMER_TO_ANIMAL} 
              AND er.entity_2_entity_uuid = a.animal_id
              AND er.active = ${ENUMS.record_statuses.ACTIVE}

          -- JOINING CLASSIFICATIONS
          LEFT JOIN 
              main.animal_classification AS ec_ear_tag ON
              ec_ear_tag.animal_id = a.animal_id
              AND ec_ear_tag.classifier_id = ${ENUMS.classifiers.EAR_TAG_NUMBER}
              AND ec_ear_tag.active = ${ENUMS.record_statuses.ACTIVE}

          LEFT JOIN 
              main.animal_classification AS ec_pregnancy ON
              ec_pregnancy.animal_id = a.animal_id
              AND ec_pregnancy.classifier_id = ${ENUMS.classifiers.NUMBER_OF_MONTHS_PREGNANT_AS_OF_DATE}
              AND ec_pregnancy.active = ${ENUMS.record_statuses.ACTIVE}

          LEFT JOIN 
              main.animal_classification AS ec_calvings ON
              ec_calvings.animal_id = a.animal_id
              AND ec_calvings.classifier_id = ${ENUMS.classifiers.MONTHS_SINCE_LAST_CALVING}
              AND ec_calvings.active = ${ENUMS.record_statuses.ACTIVE}

          LEFT JOIN 
              main.animal_classification AS ec_pregnancy_count ON
              ec_pregnancy_count.animal_id = a.animal_id
              AND ec_pregnancy_count.classifier_id = ${ENUMS.classifiers.NUMBER_OF_MONTHS_PREGNANT}
              AND ec_pregnancy_count.active = ${ENUMS.record_statuses.ACTIVE}

          LEFT JOIN 
              main.animal_classification AS ec_calvings_count ON
              ec_calvings_count.animal_id = a.animal_id
              AND ec_calvings_count.classifier_id = ${ENUMS.classifiers.NUMBER_OF_CALVINGS}
              AND ec_calvings_count.active = ${ENUMS.record_statuses.ACTIVE}

          LEFT JOIN 
              main.animal_classification AS dob ON
              dob.animal_id = a.animal_id
              AND dob.classifier_id = ${ENUMS.classifiers.DOB_OF_ANIMAL}
              AND dob.active = ${ENUMS.record_statuses.ACTIVE}

          LEFT JOIN 
              main.animal_classification AS at ON
              at.animal_id = a.animal_id
              AND at.classifier_id = ${ENUMS.classifiers.TYPE_OF_ANIMAL}
              AND at.active = ${ENUMS.record_statuses.ACTIVE}
          LEFT JOIN 
              main.ref_reference AS atr ON
              atr.reference_id = at.value_reference_id
              AND atr.active = ${ENUMS.record_statuses.ACTIVE}

          LEFT JOIN 
              main.animal_classification AS acc ON
              acc.animal_id = a.animal_id
              AND acc.classifier_id = ${ENUMS.classifiers.ANIMAL_STAGE}
              AND acc.active = ${ENUMS.record_statuses.ACTIVE}
          LEFT JOIN 
              main.ref_reference AS acs ON
              acs.reference_id = acc.value_reference_id
              AND acs.active = ${ENUMS.record_statuses.ACTIVE}

          LEFT JOIN 
              main.animal_classification AS at2 ON
              at2.animal_id = a.animal_id
              AND at2.classifier_id = ${ENUMS.classifiers.BREED_OF_ANIMAL}
              AND at2.active = ${ENUMS.record_statuses.ACTIVE}
          LEFT JOIN 
              main.ref_reference AS atrs ON
              atrs.reference_id = at2.value_reference_id
              AND atrs.active = ${ENUMS.record_statuses.ACTIVE}

          JOIN 
              main.animal_classification AS sub ON
              sub.animal_id = a.animal_id
              AND sub.classifier_id = ${ENUMS.classifiers.HEALTHCARE_PLAN_SUBSCRIPTION_DATE}
              AND sub.active = ${ENUMS.record_statuses.ACTIVE}

          -- JOINING THUMBNAIL
          LEFT JOIN (
              SELECT 
                  d.entity_1_type_id, 
                  d.entity_1_entity_uuid, 
                  d.document_type_id, 
                  max(created_at) AS max_created_at
              FROM 
                  main.document d
              WHERE 
                  d.document_type_id = ${ENUMS.document_activities.ANIMAL_THUMBNAIL_PHOTO}
              GROUP BY 
                  d.entity_1_type_id, 
                  d.entity_1_entity_uuid, 
                  d.document_type_id
          ) AS mad ON
              mad.entity_1_entity_uuid = a.animal_id 
              AND mad.entity_1_type_id = **********

          LEFT JOIN 
              main.document AS ad ON
              mad.entity_1_entity_uuid = ad.entity_1_entity_uuid
              AND mad.max_created_at = ad.created_at
              AND ad.document_type_id = ${ENUMS.document_activities.ANIMAL_THUMBNAIL_PHOTO}

          WHERE 
              a.active = ${ENUMS.record_statuses.ACTIVE}
          `
  return base_query
}
module.exports = {
  getAnimalByFarmersId,
  insertToAnimal,
  insertToEntity,
  getDiseasseByAnimalId,
  deleteDiseasesClassifiers,
  deleteDisease,
  q_getAnimalByFarmersId,
  q_getAnimalPrimaryById,
  q_animalForFarmActivity,
  q_animalsWithM1orM2Activity,
}
