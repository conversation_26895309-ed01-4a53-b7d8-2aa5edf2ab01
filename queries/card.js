const { getDBConnections, preProcessRecords, postProcessRecords, dbConnections } = require('@krushal-it/ah-orm')
const getCardOfPV = async (options = {}) => {
  let f_activity_category = options.f_activity_category || [1000270001, 1000270002, 1000270003]

  f_activity_category = Array.isArray(f_activity_category) ? f_activity_category : [f_activity_category]

  const manager = options.manager || dbConnections().main.manager
  let queryToday = `select count(care_calendar_id) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
      and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
      and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
      and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
      and eg.active = 1000100001
    inner join main.entity_geography ueg on (
      (
        (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = '${options.user_id}')
        or
        (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = '${options.user_id}')
        or
        (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = '${options.user_id}')
      )
      and
      ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
      and ueg.active = 1000100001
    )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (${f_activity_category})
    where cc.entity_type_id = 1000460002 
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    and cc.activity_date::DATE = current_date`
  let today = await manager.query(queryToday, [])
  let base_query2 = `select count(care_calendar_id) as count
      from main.care_calendar cc
      inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
	    and er.entity_relationship_type_id = 1000210004
      inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
	    and ccl.classifier_id = 2000000055
      inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
	    and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
      and eg.active = 1000100001
      inner join main.entity_geography ueg on (
        (
          (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = '${options.user_id}')
          or
          (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = '${options.user_id}')
          or
          (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = '${options.user_id}')
        )
        and
        ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
        and ueg.active = 1000100001
        )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (${f_activity_category})
    where cc.entity_type_id = 1000460002 
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    and cc.activity_date::DATE < current_date + 15`
  let total = await manager.query(base_query2, [])
  let t = total[0]
  return { today_count: today[0], total_count: t }
}

const getTotalZORO = async (options = {}) => {
  let base_query1 = `-- Left column
-- Total in 15 days 
select 
'total' as type,
at_rr.activity_category as activity_category_id,
ar.reference_name_l10n as activity_name_l10n,
count(*) as count
from main.care_calendar cc
inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
	and er.entity_relationship_type_id = 1000210004
inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
	and ccl.classifier_id = 2000000055
inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
	and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
  and eg.active = 1000100001
inner join main.entity_geography ueg on (
	(
		/*param here logging in users staff_id*/
		(ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet */)
		or
		/*param here logging in users staff_id*/
		(ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
		/*param here logging in users staff_id - */
		or
		(ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
	)
	and
	ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
  and ueg.geography_type_id = 1000100001
)
inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
join main.ref_reference ar on ar.reference_id = at_rr.activity_category
where cc.entity_type_id = 1000460002 /*animal*/
/*param here for number of future days*/
and cc.activity_date::DATE <= current_date+15
group by at_rr.activity_category,ar.reference_name_l10n

union all

-- Right Column
-- Total today left
select 
'total_left_today' as type,
at_rr.activity_category as activity_category_id,
ar.reference_name_l10n as activity_name_l10n,
count(*) as count
from main.care_calendar cc
inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
	and er.entity_relationship_type_id = 1000210004
inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
	and ccl.classifier_id = 2000000055
inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
	and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
  and eg.active = 1000100001
inner join main.entity_geography ueg on (
	(
		/*param here logging in users staff_id*/
		(ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet */)
		or
		/*param here logging in users staff_id*/
		(ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
		/*param here logging in users staff_id - */
		or
		(ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
	)
	and
	ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
  and ueg.active = 1000100001
)
inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
join main.ref_reference ar on ar.reference_id = at_rr.activity_category
where cc.entity_type_id = 1000460002 /*animal*/
/*param here for number of future days*/
and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
and cc.activity_date::DATE = current_date
group by at_rr.activity_category,ar.reference_name_l10n;`
  let rawData = await dbConnections().main.manager.query(base_query1)

  let base_query2 = `
    -- left column
    -- Total farmer in 15 days
    select 'total_farmer' as type,sum(t.single_farmer) from
    (select 
    er.entity_1_entity_uuid,
    least(count(*),1) as single_farmer
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
        and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
        and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
        and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
        and eg.active = 1000100001
    inner join main.entity_geography ueg on (
        (
            /*param here logging in users staff_id*/
            (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet */)
            or
            /*param here logging in users staff_id*/
            (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
            /*param here logging in users staff_id - */
            or
            (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
        )
        and
        ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
        and ueg.geography_type_id = 1000100001
    )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.activity_date::DATE = current_date 
    group by er.entity_1_entity_uuid) as t

    union all

    select 'total_farmer_left_today' as type,sum(t.single_farmer) from
    (select 
    er.entity_1_entity_uuid,
    least(count(*),1) as single_farmer
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
        and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
        and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
        and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
        and eg.active = 1000100001
    inner join main.entity_geography ueg on (
        (
            /*param here logging in users staff_id*/
            (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet */)
            or
            /*param here logging in users staff_id*/
            (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
            /*param here logging in users staff_id - */
            or
            (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
        )
        and
        ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
        and ueg.active = 1000100001
    )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    where cc.entity_type_id = 1000460002 /*animal*/
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    /*param here for number of future days*/
    and cc.activity_date::DATE = current_date 
    group by er.entity_1_entity_uuid
    ) as t`
  let rawfarmerCount = await dbConnections().main.manager.query(base_query2)

  let formateData = {
    1000270001: { total: 0, total_left_today: 0 },
    1000270002: { total: 0, total_left_today: 0 },
    1000270003: { total: 0, total_left_today: 0 },
  }
  for (let i = 0; i < rawData.length; i++) {
    let type = rawData[i].type
    let category = rawData[i].activity_category_id
    let count = rawData[i].count
    if (formateData[category]) {
      formateData[category][type] = count
    }
  }
  let formatedFarmerCount = {
    total_farmer: 0,
    total_farmer_left_today: 0,
  }
  for (let i = 0; i < rawfarmerCount.length; i++) {
    let type = rawfarmerCount[i].type
    if (formatedFarmerCount[type] !== undefined || formatedFarmerCount[type] !== null) {
      formatedFarmerCount[type] = rawfarmerCount[i].sum || 0
    }
  }

  return { data: { ...formateData, ...formatedFarmerCount } }
}
const getCardOfFarmer = async (options = {}) => {
  const manager = options.manager || dbConnections().main.manager
  let queryToday = `select count(care_calendar_id) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
      and er.entity_relationship_type_id = 1000210004
	  and er.entity_1_entity_uuid = '${options.customer_id}'
    where cc.entity_type_id = 1000460002
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    and cc.activity_date::DATE = current_date`
  let today = await manager.query(queryToday, [])
  let queryTotal = `select count(care_calendar_id) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
      and er.entity_relationship_type_id = 1000210004
	  and er.entity_1_entity_uuid = '88463acc-b369-11ed-9fd7-0f3379a8f0fd'
    where cc.entity_type_id = 1000460002
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    and cc.activity_date::DATE < current_date + 15`
  let total = await manager.query(queryTotal, [])
  return { today_count: today, total_count: total }
}
const getROcardsByParavet = async (options = {}) => {
  let base_query1 = `
    -- Total in 15 days 
    select
    'total' as type,
    t.*,
    lss_doc.document_id as lss_doc,
    staff.staff_name_l10n
    from (select 
    eg.entity_uuid,
    at_rr.activity_category,
    count(*) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
    and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
    and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
    and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
    and eg.active = 1000100001
    inner join main.entity_geography ueg on (
    (
        /*param here logging in users staff_id*/
        (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet */)
        or
        /*param here logging in users staff_id*/
        (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
        /*param here logging in users staff_id - */
        or
        (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
        or
        (ueg.entity_type_id = 1000230003 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as vet */)
    )
    and
    ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
    and ueg.active = 1000100001
    )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.activity_date::DATE <= current_date + 15
    group by eg.entity_uuid,at_rr.activity_category) as t
    left join (
    select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
    from main.document d
    where d.document_type_id = 1000260004
    group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
    ) as mdoc
    on mdoc.entity_1_entity_uuid = t.entity_uuid and mdoc.entity_1_type_id = 1000230004 
    left join main.document as lss_doc on mdoc.entity_1_entity_uuid = lss_doc.entity_1_entity_uuid 
    and lss_doc.created_at = mdoc.max_created_at
    and lss_doc.entity_1_entity_uuid = t.entity_uuid
    join main.staff on staff.staff_id = t.entity_uuid and staff.staff_type_id = 1000230004

    union all

    -- total left today
    select
    'total_left_today' as type,
    t.*,
    lss_doc.document_id as lss_doc,
    staff.staff_name_l10n
    from (select 
    eg.entity_uuid,
    at_rr.activity_category,
    count(*) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
    and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
    and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
    and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
    and eg.active = 1000100001
    inner join main.entity_geography ueg on (
    (
        /*param here logging in users staff_id*/
        (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet */)
        or
        /*param here logging in users staff_id*/
        (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
        /*param here logging in users staff_id - */
        or
        (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
        or
        (ueg.entity_type_id = 1000230003 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as vet */)
    )
    and
    ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
    and ueg.active = 1000100001
    )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.activity_date::DATE = current_date
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    group by eg.entity_uuid,at_rr.activity_category) as t
    left join (
    select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
    from main.document d
    where d.document_type_id = 1000260004
    group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
    ) as mdoc
    on mdoc.entity_1_entity_uuid = t.entity_uuid and mdoc.entity_1_type_id = 1000230004 
    left join main.document as lss_doc  
    on mdoc.entity_1_entity_uuid = lss_doc.entity_1_entity_uuid
    and lss_doc.entity_1_entity_uuid = t.entity_uuid
    and lss_doc.created_at = mdoc.max_created_at
    join main.staff on staff.staff_id = t.entity_uuid`
  let base_query2 = `
    select
    'total_farmer' as type,
    t.entity_uuid,
    lss_doc.document_id as lss_doc,
    staff.staff_name_l10n,
    sum(cnt) as count
    from (
    select 
    eg.entity_uuid,
    1 cnt
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
        and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
        and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
        and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
        and eg.active = 1000100001
    inner join main.entity_geography ueg on (
        (
            /*param here logging in users staff_id*/
            (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet */)
            or
            /*param here logging in users staff_id*/
            (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
            /*param here logging in users staff_id - */
            or
            (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
            or
            (ueg.entity_type_id = 1000230003 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as vet */)
        )
        and
        ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
        and ueg.active = 1000100001
    )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.activity_date::DATE <= current_date+15
    group by eg.entity_uuid,er.entity_1_entity_uuid
        ) as t
    left join (
        select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
        from main.document d
        where d.document_type_id = 1000260004
        group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
        ) as mdoc
    on mdoc.entity_1_entity_uuid = t.entity_uuid and mdoc.entity_1_type_id = 1000230004 
    left join main.document as lss_doc  
    on mdoc.entity_1_entity_uuid = lss_doc.entity_1_entity_uuid
    and lss_doc.entity_1_entity_uuid = t.entity_uuid
    and lss_doc.created_at = mdoc.max_created_at
    join main.staff on staff.staff_id = t.entity_uuid
    group by t.entity_uuid,lss_doc.document_id,staff.staff_name_l10n

    union all 

    select
    'total_farmer_left_today' as type,
    t.entity_uuid,
    lss_doc.document_id as lss_doc,
    staff.staff_name_l10n,
    sum(cnt) as count
    from (
    select 
    eg.entity_uuid,
    1 cnt
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
        and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
        and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
        and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
        and eg.active = 1000100001
    inner join main.entity_geography ueg on (
        (
            /*param here logging in users staff_id*/
            (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet */)
            or
            /*param here logging in users staff_id*/
            (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
            /*param here logging in users staff_id - */
            or
            (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
            or
            (ueg.entity_type_id = 1000230003 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as vet */)
        )
        and
        ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
        and ueg.active = 1000100001
    )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.activity_date::DATE = current_date
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    group by eg.entity_uuid,er.entity_1_entity_uuid
        ) as t
    left join (
        select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
        from main.document d
        where d.document_type_id = 1000260004
        group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
        ) as mdoc
    on mdoc.entity_1_entity_uuid = t.entity_uuid and mdoc.entity_1_type_id = 1000230004 
    left join main.document as lss_doc  
    on mdoc.entity_1_entity_uuid = lss_doc.entity_1_entity_uuid
    and lss_doc.entity_1_entity_uuid = t.entity_uuid
    and lss_doc.created_at = mdoc.max_created_at
    join main.staff on staff.staff_id = t.entity_uuid
    group by t.entity_uuid,lss_doc.document_id,staff.staff_name_l10n
`

  let rawData = await dbConnections().main.manager.query(base_query1)
  let rawFarmerCount = await dbConnections().main.manager.query(base_query2)

  let formatedFarmerCount = {}
  for (let i = 0; i < rawFarmerCount.length; i++) {
    let lss_id = rawFarmerCount[i].entity_uuid
    let { type, count } = rawFarmerCount[i]
    if (!formatedFarmerCount[lss_id]) formatedFarmerCount[lss_id] = {}
    formatedFarmerCount[lss_id][type] = count
  }

  let formatedData = []
  for (let i = 0; i < rawData.length; i++) {
    let lss_id = rawData[i].entity_uuid
    let { staff_name_l10n, type, activity_category, count, lss_doc } = rawData[i]
    let existingEntry = formatedData.find((entry) => entry.entity_uuid === lss_id)
    if (!existingEntry) {
      let entry = {
        entity_uuid: lss_id,
        staff_name_l10n,
        lss_doc,
        total: {
          1000270001: 0,
          1000270002: 0,
          1000270003: 0,
        },
        total_left_today: {
          1000270001: 0,
          1000270002: 0,
          1000270003: 0,
        },
        farmer_count: formatedFarmerCount[lss_id] || {
          total_farmer_left_today: 0,
          total_farmer: 0,
        },
      }
      formatedData.push(entry)
      existingEntry = entry
    }
    existingEntry[type][activity_category] = count
  }

  return { data: formatedData }
}

const getZoCardByRo = async (options = {}) => {
  let base_query = `select
    'total' as type,
    t.*,
    lss_doc.document_id as lss_doc,
    staff.staff_name_l10n
    from (select
    eg.entity_uuid,
    at_rr.activity_category,
    count(*) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
    and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
    and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
    and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230001
    and eg.active = 1000100001
    inner join main.entity_geography ueg on (
    (
        /*param here logging in users staff_id*/
        (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet 
*/)
        or
        /*param here logging in users staff_id*/
        (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)  
        /*param here logging in users staff_id - */
        or
        (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)  
    )
    and
    ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
    and ueg.active = 1000100001
    )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.activity_date::DATE <= current_date + 15
    group by eg.entity_uuid,at_rr.activity_category) as t
    left join (
    select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
    from main.document d
    where d.document_type_id = 1000260004
    group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
    ) as mdoc
    on mdoc.entity_1_entity_uuid = t.entity_uuid and mdoc.entity_1_type_id = 1000230001
    left join main.document as lss_doc on mdoc.entity_1_entity_uuid = lss_doc.entity_1_entity_uuid
    and lss_doc.created_at = mdoc.max_created_at
    and lss_doc.entity_1_entity_uuid = t.entity_uuid
    join main.staff on staff.staff_id = t.entity_uuid and staff.staff_type_id = 1000230001

    union all

    -- total left today
    select
    'total_left_today' as type,
    t.*,
    lss_doc.document_id as lss_doc,
    staff.staff_name_l10n
    from (select
    eg.entity_uuid,
    at_rr.activity_category,
    count(*) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
    and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
    and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
    and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230001
    and eg.active = 1000100001
    inner join main.entity_geography ueg on (
    (
        /*param here logging in users staff_id*/
        (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet 
*/)
        or
        /*param here logging in users staff_id*/
        (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)  
        /*param here logging in users staff_id - */
        or
        (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)  
    )
    and
    ueg.geography_type_id = 1000320001 and ueg.geography_id = eg.geography_id
    and ueg.active = 1000100001
    )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.activity_date::DATE = current_date
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    group by eg.entity_uuid,at_rr.activity_category) as t
    left join (
    select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
    from main.document d
    where d.document_type_id = 1000260004
    group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
    ) as mdoc
    on mdoc.entity_1_entity_uuid = t.entity_uuid and mdoc.entity_1_type_id = 1000230001
    left join main.document as lss_doc
    on mdoc.entity_1_entity_uuid = lss_doc.entity_1_entity_uuid
    and lss_doc.entity_1_entity_uuid = t.entity_uuid
    and lss_doc.created_at = mdoc.max_created_at
    join main.staff on staff.staff_id = t.entity_uuid`
  let base_query2 = ` select
    'total_farmer_left_today' as type,
    t.entity_uuid,
    lss_doc.document_id as lss_doc,
    staff.staff_name_l10n,
    sum(cnt) as count
    from (
    select
    eg.entity_uuid,
    1 cnt
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
        and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
        and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
        and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
        and eg.active = 1000100001
    inner join main.entity_geography ueg on (
        (
            /*param here logging in users staff_id*/
            (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as paravet */)
            or
            /*param here logging in users staff_id*/
            (ueg.entity_type_id = 1000230001 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
            /*param here logging in users staff_id - */
            or
            (ueg.entity_type_id = 1000230005 and ueg.entity_uuid = /*5*/ '${options.user_id}' /*staff_id as ro */)
        )
        and
        ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
        and ueg.active = 1000100001
    )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001, 1000270002, 1000270003)
    join main.ref_reference ar on ar.reference_id = at_rr.activity_category
    where cc.entity_type_id = 1000460002 /*animal*/
    /*param here for number of future days*/
    and cc.activity_date::DATE = current_date
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    group by eg.entity_uuid,er.entity_1_entity_uuid
        ) as t
    left join (
        select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
        from main.document d
        where d.document_type_id = 1000260004
        group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
        ) as mdoc
    on mdoc.entity_1_entity_uuid = t.entity_uuid and mdoc.entity_1_type_id = 1000230001
    left join main.document as lss_doc
    on mdoc.entity_1_entity_uuid = lss_doc.entity_1_entity_uuid
    and lss_doc.entity_1_entity_uuid = t.entity_uuid
    and lss_doc.created_at = mdoc.max_created_at
    join main.staff on staff.staff_id = t.entity_uuid
    group by t.entity_uuid,lss_doc.document_id,staff.staff_name_l10n`
  let rawData = await dbConnections().main.manager.query(base_query)
  let rawFarmerCount = await dbConnections().main.manager.query(base_query2)
  let formatedFarmerCount = {}
  for (let i = 0; i < rawFarmerCount.length; i++) {
    let lss_id = rawFarmerCount[i].entity_uuid
    let { type, count } = rawFarmerCount[i]
    if (!formatedFarmerCount[lss_id]) formatedFarmerCount[lss_id] = {}
    formatedFarmerCount[lss_id][type] = count
  }

  let formatedData = []
  for (let i = 0; i < rawData.length; i++) {
    let lss_id = rawData[i].entity_uuid
    let { staff_name_l10n, type, activity_category, count, lss_doc } = rawData[i]
    let existingEntry = formatedData.find((entry) => entry.entity_uuid === lss_id)
    if (!existingEntry) {
      let entry = {
        entity_uuid: lss_id,
        staff_name_l10n,
        lss_doc,
        total: {
          1000270001: 0,
          1000270002: 0,
          1000270003: 0,
        },
        total_left_today: {
          1000270001: 0,
          1000270002: 0,
          1000270003: 0,
        },
        farmer_count: formatedFarmerCount[lss_id] || {
          total_farmer_left_today: 0,
          total_farmer: 0,
        },
      }
      formatedData.push(entry)
      existingEntry = entry
    }
    existingEntry[type][activity_category] = count
  }

  return { data: formatedData }
}
module.exports = {
  getCardOfPV,
  getTotalZORO,
  getCardOfFarmer,
  getROcardsByParavet,
  getZoCardByRo,
}
