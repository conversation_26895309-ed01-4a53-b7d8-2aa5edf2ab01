const {
  RELATION_STATE,
  R<PERSON><PERSON><PERSON>_DISTRICT,
  <PERSON><PERSON><PERSON><PERSON>_TALUKA,
  RELATION_VILLAGE,
  LOCATION_TYPE_DISTRICT,
  LOCATION_TYPE_STATE,
  LOCATION_TYPE_TALUKA,
  LOCATION_TYPE_VILLAGE } = require('../utils/constant');
const { dbConnections } = require('@krushal-it/ah-orm');
const LOCATIONS = new Set([RELATION_DISTRICT, RELATION_STATE, RELATION_TALUKA, RELATION_VILLAGE]);
const {errorLog} = require('@krushal-it/mobile-or-server-lib')

const getStates = async (options = {}) => {
  try {
    let query = dbConnections().main.repos[RELATION_STATE].createQueryBuilder();
    if (typeof options.name_start_with === 'string') {
      query.where(`LOWER(state_name_l10n->>'ul') LIKE :name`,
        { name: `%${options.name_start_with}%`.toLowerCase(), });
      query.orWhere(`LOWER(state_name_l10n->>'en') LIKE :name`,
        { name: `%${options.name_start_with}%`.toLowerCase() });
      query.orWhere(`LOWER(state_name_l10n->>'mr') LIKE :name`,
        { name: `%${options.name_start_with}%`.toLowerCase() });
    }
    let data = await query.getMany();
    return { result: true, data };
  } catch (error) {
    errorLog("error in getStates:28",params,{message:error.message})
    return { result: false, message: error.message };
  }
};
const getDistrictByStateId = async (options = {}) => {
  try {
    if (!options.state_id) throw new Error('state_id require in options');
    let query = dbConnections().main.repos[RELATION_DISTRICT].createQueryBuilder();
    if (Array.isArray(options.select) && options.select.length > 0) {
      for (let i = 0; i < options.select.length; i++) {
        options.select[i] = `${RELATION_DISTRICT}.${options.select[i]}`;
      }
      query.select(options.select);
    }
    query.where(`${RELATION_DISTRICT}.state_id = :state_id`, {
      state_id: options.state_id,
    });
    let data = await query.getMany();
    return { result: true, data };
  } catch (error) {
    errorLog("error in getDistrictByStateId:48",params,{message:error.message})
    return { result: false, error: error.message };
  }
};
const getTalukByDistrictId = async (options = {}) => {
  try {
    if (!options.district_id) throw new Error('district_id require in options');
    let query = dbConnections().main.repos[RELATION_TALUKA].createQueryBuilder();
    query.where(`district_id = :district_id`, { district_id: options.district_id });
    let data = await query.getMany();
    return { result: true, data };
  } catch (error) {
    errorLog("error in getTalukByDistrictId:60",params,{message:error.message})
    return { result: false, error: error.message };
  }
};
const getVillageByTalukaId = async (options) => {
  try {
    if (!options.taluka_id) throw new Error('taluka_id require in options');
    let query = dbConnections().main.repos[RELATION_VILLAGE].createQueryBuilder();
    if (Array.isArray(options.select) && options.select.length > 0) {
      for (let i = 0; i < options.select.length; i++) {
        options.select[i] = `${RELATION_VILLAGE}.${options.select[i]}`;
      }
      query.select(options.select);
    }
    query.where(`${RELATION_VILLAGE}.taluk_id = :taluka_id`,
      { taluka_id: options.taluka_id });
    let data = await query.getMany();
    return { result: true, data };
  } catch (error) {
    errorLog("error in getVillageByTalukaId:79",params,{message:error.message})
    return { result: false, error: error.message };
  }
};

const getLocationEntityById = async (entity, ids, datasource) => {
  try {
    const entity_name_map = {};
    entity_name_map[RELATION_DISTRICT] = LOCATION_TYPE_DISTRICT;
    entity_name_map[RELATION_STATE] = LOCATION_TYPE_STATE;
    entity_name_map[RELATION_TALUKA] = LOCATION_TYPE_TALUKA;
    entity_name_map[RELATION_VILLAGE] = LOCATION_TYPE_VILLAGE;

    if (!LOCATIONS.has(entity))
      throw new Error(`${entity} is not a valid location entity`);
    if (!ids || ids.length <= 0) throw new Error("require ids");
    if (!Array.isArray(ids)) ids = [ids];
    const COLUMNS = [
      `${entity_name_map[entity]}_id`,
      `${entity_name_map[entity]}_name_l10n`,
      `${entity_name_map[entity]}_short_code`
    ];
    let mdatasource = datasource ? datasource : dbConnections().main.manager;
    let query = mdatasource.getRepository(entity).createQueryBuilder();
    query.select(COLUMNS);
    query.where(`${entity_name_map[entity]}_id IN (:...ids)`, { ids });
    let data = await query.execute();
    return data;
  } catch (error) {
    errorLog("error in getVillageByTalukaId:108",params,{message:error.message})
    return [];
  }
};
module.exports = {
  getStates,
  getDistrictByStateId,
  getTalukByDistrictId,
  getVillageByTalukaId,
  getLocationEntityById
};
