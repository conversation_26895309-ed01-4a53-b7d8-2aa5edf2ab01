const { RELATION_WAREHOUSE_BLOCKQTY, SCHEMA_MAIN,ACTIVE } = require('../utils/constant')
const { errorLog } = require('@krushal-it/mobile-or-server-lib')
const { dbConnections, preProcessRecords } = require('@krushal-it/ah-orm')
const { getDocumentJSONConfig } = require('../utils/document')
const { validateUUID } = require('@krushal-it/mobile-or-server-lib')
const { configurationJSON } = require('@krushal-it/common-core')
// const { validate } = require('uuid');

const insertToWarehouseBlockQty = async (options,transaction) => {
  // return new Promise(async (resolve,reject)=>{
    try{
      const wh_block = dbConnections().main.entities['warehouse_blockqty']
      const preProcessedInsertValue = preProcessRecords(wh_block, options, wh_block.additionalAttributes)
      let manager = transaction ? transaction : dbConnections().main.manager
      let data = await manager.createQueryBuilder().insert().into(RELATION_WAREHOUSE_BLOCKQTY).values(preProcessedInsertValue)/*.returning(`warehouse_blockqty_id`)*/.execute()      // resolve(data)
      return data
    }
    catch(e){
        // reject(e)
        throw e
    }
  // })
}
  
  const updateWarehouseBlockQty = async (value, options = {},transaction) => {
    // return new Promise(async (resolve,reject)=>{
      try{
        console.log('bel q c uWB 1, value = ', value, ', options = ', options)
        if (!options.warehouse_blockqty_id) throw new Error('warehouse_blockqty_id is required')
        let manager = transaction ? transaction : dbConnections().main.manager
        const entity = dbConnections().main.entities['warehouse_blockqty']
        console.log('bel q c uWB 2, entity.additionalAttributes = ', entity.additionalAttributes)
        const preProcessedValue = preProcessRecords(entity, value, { ...entity.additionalAttributes, id: options.warehouse_blockqty_id })
        console.log('bel q c uWB 3, preProcessedValue = ', preProcessedValue)
        await manager.createQueryBuilder().update(RELATION_WAREHOUSE_BLOCKQTY).set(preProcessedValue).where(`${RELATION_WAREHOUSE_BLOCKQTY}_id = :id`, { id: options.warehouse_blockqty_id }).execute()
        // resolve(true)
        return true
      }
      catch(e){
          // reject(e)
          throw e
      }
    // })
  }

  const getBlockQtyByUUID = async(uuid)=>{
    let blockqtylistquery=`SELECT * FROM main.${RELATION_WAREHOUSE_BLOCKQT} WHERE warehouse_uuid=${uuid} AND active=${ACTIVE}`
    // let  blockqtylist = await dbConnections().main.repos[RELATION_WAREHOUSE_BLOCKQTY].createQueryBuilder().where(`${RELATION_WAREHOUSE_BLOCKQTY}.warehouse_uuid = :uuid`, { uuid }).andWhere(`${RELATION_WAREHOUSE_BLOCKQTY}.active = :act`, { act:ACTIVE }).execute()
    let blockqtylist=await dbConnections().main.manager.query(blockqtylistquery)
    return blockqtylist
  }

  const getBlockQtyByMedAndWareUUID = async(medid,uuid)=>{
    // const blockqty = await dbConnections().main.repos[RELATION_WAREHOUSE_BLOCKQTY].createQueryBuilder().where(`${RELATION_WAREHOUSE_BLOCKQTY}.warehouse_uuid = :uuid`, { uuid }).andWhere(`${RELATION_WAREHOUSE_BLOCKQTY}.medicine_id = :medid`, { medid }).andWhere(`${RELATION_WAREHOUSE_BLOCKQTY}.active = :act`, { act:ACTIVE }).getOne()
    let blockqtyquery =`SELECT * FROM main.${RELATION_WAREHOUSE_BLOCKQTY} WHERE warehouse_uuid='${uuid}' AND medicine_id=${medid} AND active=${ACTIVE}`
    let blockqty=await dbConnections().main.manager.query(blockqtyquery)
    return blockqty
  }

  const getMedBySourceID=async(source_uuid,transaction)=>{
    let manager = transaction ? transaction:dbConnections().main.manager
    let medsourceidquery =`SELECT * FROM main.${RELATION_WAREHOUSE_BLOCKQTY} WHERE source_uuid='${source_uuid}' AND active=${ACTIVE}`
    let medsourceidlist=await manager.query(medsourceidquery)
    return medsourceidlist
  }

module.exports= {insertToWarehouseBlockQty,updateWarehouseBlockQty,getBlockQtyByUUID,getBlockQtyByMedAndWareUUID,getMedBySourceID}