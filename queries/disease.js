
const {
  getDiseasseByAnimalId,
  deleteDisease,
  deleteDiseasesClassifiers,
} = require('./animal')
const {
  preProcessRecords,
  postProcessRecords,
  getDBConnections,
  dbConnections,
} = require('@krushal-it/ah-orm')
const {infoLog} = require('@krushal-it/mobile-or-server-lib')
const { RELATION_ANIMAL_DISEASE } = require('../utils/constant')
const {errorLog} = require('@krushal-it/mobile-or-server-lib')

const insertToDisease = async (diseases, entityManager) => {
  let mdatasource = entityManager ? entityManager : dbConnections().main.manager
  const entity_disease = dbConnections().main.entities[RELATION_ANIMAL_DISEASE]
  const preProcessedDiseases = preProcessRecords(
    entity_disease,
    diseases,
    entity_disease.additionalAttributes
  )
  let data = await mdatasource
    .createQueryBuilder()
    .insert()
    .into(RELATION_ANIMAL_DISEASE)
    .values(preProcessedDiseases)
    // .returning('animal_disease_id')
    .execute()
  return data
}

const deleteDiseasess = async (animal_id, options = {}) => {
  const datasource = options.datasource ? options.datasource : global.datasource
  const diseases = await getDiseasseByAnimalId({ datasource, animal_id })
  let disease_ids = []
  for (let diseaseindex = 0; diseaseindex < diseases.length; diseaseindex++) {
    disease_ids.push(diseases[diseaseindex].animal_disease_id)
  }
  if (disease_ids.length === 0) return
  await deleteDiseasesClassifiers({ disease_id: disease_ids, datasource })
  await deleteDisease({ disease_id: disease_ids, datasource })
  return
}

module.exports = { deleteDiseasess, insertToDisease }
