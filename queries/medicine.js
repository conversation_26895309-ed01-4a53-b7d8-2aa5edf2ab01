const { errorLog } = require('@krushal-it/mobile-or-server-lib')
const { dbConnections, preProcessRecords } = require('@krushal-it/ah-orm')
const { getDocumentJSONConfig } = require('../utils/document')
const { validateUUID } = require('@krushal-it/mobile-or-server-lib')
const { configurationJSON } = require('@krushal-it/common-core')
// const { validate } = require('uuid');
const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0;

const getMedicinesQuery = async(medicines,transaction)=>{
    try{
        let base_query=`SELECT *
        FROM main.ref_medicine m
        LEFT JOIN main.ref_medicine_classification mc1 ON m.medicine_id=mc1.medicine_id
        LEFT JOIN main.ref_reference rf ON rf.reference_id=mc1.value_reference_id
        WHERE m.active=1000100001 AND mc1.active=1000100001 `
        if(medicines && medicines.length>0) base_query+=` AND m.medicine_id IN (${medicines}) `
        base_query+=` ORDER BY m.medicine_id`
        let manager = transaction ? transaction : dbConnections().main.manager
        let data= await manager.query(base_query)
        return data
    }
    catch(e){
        throw e
    }
}

module.exports = { getMedicinesQuery }