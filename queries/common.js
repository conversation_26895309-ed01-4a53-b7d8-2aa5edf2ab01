const { RELATION_NOTE } = require('../utils/constant')
const {
  preProcessRecords,
  postProcessRecords,
  getDBConnections,
  dbConnections,
} = require('@krushal-it/ah-orm')

const q_getParavetByfarmerId = (options = {}) => {
  const baseQuery = `
    SELECT 
    staff_name_l10n,
    staff.mobile_number,
    staff.staff_id as p_id,
    staff_doc.document_id,
    staff_doc.document_information,
    staff_doc.document_name
    FROM (
    SELECT * FROM main.customer_classification
    WHERE  customer_classification.classifier_id = 2000000055
    AND customer_classification.customer_id = '${options.customer_id}'
    ) 
    as cc_village
    JOIN main.entity_geography AS e_gr
    ON e_gr.geography_id = cc_village.value_reference_id
    AND e_gr.geography_type_id  = 1000320004
    AND e_gr.entity_type_id = 1000230004
    AND e_gr.active = 1000100001
    LEFT JOIN main.staff
    ON staff.staff_id = e_gr.entity_uuid
    LEFT JOIN (SELECT DISTINCT ON (entity_1_entity_uuid) * FROM main.document 
    WHERE document.document_type_id = 1000260004 
    ORDER BY entity_1_entity_uuid,created_at DESC) as staff_doc
    ON staff_doc.entity_1_entity_uuid = staff.staff_id`
  return baseQuery
}
const q_getROByFarmerId = (options = {}) => {
  const baseQuery = `
    SELECT staff.staff_name_l10n,staff.mobile_number,staff.email_address,document.document_id 
    AS RO_QR_Code,innerImageDoc.document_id as RO_Thumbnail
    FROM main.customer_classification 
    JOIN main.entity_geography
    ON entity_geography.geography_id = customer_classification.value_reference_id
    AND customer_classification.classifier_id = 2000000055
    AND customer_classification.customer_id = '${options.customer_id}'
    AND entity_geography.geography_type_id = 1000320004
    AND entity_geography.entity_type_id = 1000230001
    AND entity_geography.active = 1000100001
    JOIN main.staff
    ON staff.staff_id = entity_geography.entity_uuid
    JOIN main.document
    ON staff.staff_id = document.entity_1_entity_uuid
    AND document.document_type_id = 1000260009 
    LEFT JOIN (SELECT DISTINCT ON (entity_1_entity_uuid, created_at) entity_1_entity_uuid, created_at, document_id FROM main.document 
    WHERE document.document_type_id = 1000260004 ORDER BY entity_1_entity_uuid, created_at DESC) AS innerImageDoc
    ON innerImageDoc.entity_1_entity_uuid = staff.staff_id ORDER BY document.created_at desc LIMIT 1`
  return baseQuery
}
const NoteQueries = async (values, options = {}) => {
  let manager = options.manager ? options.manager : dbConnections().main.manager
  const entity_note = dbConnections().main.entities[RELATION_NOTE]
  const preProcessedValues = preProcessRecords(entity_note, values, entity_note.additionalAttributes)
  
  return await manager
    .createQueryBuilder()
    .insert()
    .into(RELATION_NOTE)
    .values(preProcessedValues)
    // .returning('note_id')
    .execute()
}

module.exports = { q_getParavetByfarmerId, q_getROByFarmerId, NoteQueries }
