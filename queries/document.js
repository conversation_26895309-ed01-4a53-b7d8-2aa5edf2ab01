const { configurationJSON } = require("@krushal-it/common-core");
const { dbConnections, preProcessRecords, postProcessRecords } = require("@krushal-it/ah-orm");
const entityTypeIds = { farmer: 1000220001, animal: 1000220002, staff: 1000230004 };
const { validateUUID } = require('@krushal-it/mobile-or-server-lib');

const { RELATION_DOC, RELATION_REFERENCE, ENTITY_TYPE_ANIMAL, RELATION_DUMP_DOC } = require("../utils/constant");
const {errorLog} = require('@krushal-it/mobile-or-server-lib')

const insertToDocument = async (value) => {
    const isPostgres = (configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1) ? true : false
    const entity_document = dbConnections().main.entities[RELATION_DOC];
    const preProcessedValue = preProcessRecords(entity_document, value, entity_document.additionalAttributes)
    let qb = await dbConnections().main.manager.createQueryBuilder()
        .insert().into(RELATION_DOC).values(preProcessedValue)
    if (isPostgres) {
        qb = qb.returning("document_id")
    }
    let data = qb.execute()
    if (isPostgres) {
        return data
    } else {
        if (data.identifiers) {
            data = data.identifiers[0].document_id
        }
        return data
    }
};

const insertDumpedDoc = async (value, options = {}) => {
    const entity_dump_document = dbConnections().main.entities[RELATION_DUMP_DOC];
    const preProcessedValue = preProcessRecords(entity_dump_document, value, entity_dump_document.additionalAttributes);
    await global.datasource.createQueryBuilder().insert().into(RELATION_DUMP_DOC).values(preProcessedValue).execute();
    return;
};

const getImagesByAnimalAId = async (animal,document_type_id = false) => {
    try {
        const projection = [
            "document_id",
            "document_information",
            "client_document_information",
            "document_name",
            "document_type_id",
            "updated_at",
            "active"
        ];
        let where_condition= {
            id: animal,
            entity_id: entityTypeIds[ENTITY_TYPE_ANIMAL],
            active: 1000100001
        }
        let additionalWhere = '';
        if(document_type_id && document_type_id.length){
                additionalWhere += 'AND document_type_id IN (:...document_type_id)'
                where_condition['document_type_id'] = document_type_id
        }
        const animall = await dbConnections()
            .main.manager.getRepository(RELATION_DOC)
            .createQueryBuilder().where(`entity_1_entity_uuid=:id AND entity_1_type_id = :entity_id AND active = :active  ${additionalWhere}`,where_condition).select(projection).execute()
        postProcessRecords(undefined, animall, { json_columns: ['document_information', 'client_document_information'] });
        return animall;
    } catch (error) {
        errorLog("error in getImagesByAnimalAId:28",params,{message:error.message})
        throw error;
    }
};

const getDocumentById = async (document_id, options = {}) => {
    let manager = options.datasource ? options.datasource : dbConnections().main.manager;
    let queryBuilder = manager.createQueryBuilder();
    // if (!uuid.validate(document_id)) return null;
    if (!validateUUID(document_id)) return null;
    let data = await queryBuilder.from(RELATION_DOC)
        .where("document_id= :document_id", { document_id }).execute();
    if (data.length > 0) return data[0].document_information.url;
    return null;
};

const getDocumentReferences = async (options = {}, datasource) => {
    try {
        const doc_categoryParent = 10002600;
        let mdatasource = datasource ? datasource : dbConnections().main.repos[RELATION_REFERENCE];
        let query = mdatasource.createQueryBuilder()
            .where(
                `${RELATION_REFERENCE}.active = 1000100001 AND ${RELATION_REFERENCE}.reference_category_id = :parent`,
                { parent: doc_categoryParent });
        let data = await query.execute();
        return data;
    } catch (error) {
        errorLog("error in getDocumentReferences:63",params,{message:error.message})
        return [];
    }
};

/**
 *
 * @param {Object} options
 * Options: entity_id(user id), entity_type_id(user type id), document_type_id(type of document)
 * @returns
 */
const getDocuments = async (options = {}) => {
    try {
        const { entity_id, entity_type_id, document_type_id } = options;
        let query = dbConnections().main.repos[RELATION_DOC].createQueryBuilder()
            .where("entity_1_entity_uuid= :entity_id AND entity_1_type_id = :entity_type_id AND document_type_id = :document_type_id",
                { entity_id, entity_type_id, document_type_id }).orderBy("created_at", "DESC");
        if (options.limit) query.limit(options.limit);
        const document = await query.execute()
        postProcessRecords(undefined, document, { json_columns: ['document_document_name_l10n', 'document_document_information', 'document_client_document_information', 'document_document_meta_data_information'] });
        return document;
    } catch (error) {
        errorLog("error in getDocuments:84",params,{message:error.message})
        throw error;
    }
};

const deleteDocumentById = async (document_id, options = {}) => {
    const manager = options.datasource ? options.datasource : global.datasource;
    const queryBuilder = manager.createQueryBuilder();
    return await queryBuilder.delete().from(RELATION_DOC)
        .where("document_id= :document_id", { document_id }).execute();
};

const getImagesByFarmerId = async (farmerId) => {
    try {
      const projection = [
        'document_id',
        'document_information',
        'client_document_information',
        'document_name',
        'document_type_id',
      ]
      const farmerr = await dbConnections()
        .main.repos[RELATION_DOC].createQueryBuilder()
        .where('entity_1_entity_uuid=:id AND entity_1_type_id = :entity_id ', {
          id: farmerId,
          entity_id: 1000220001,
        })
        .select(projection)
        .execute()
      postProcessRecords(undefined, farmerr, { json_columns: ['document_information', 'client_document_information'] })
      return farmerr
    } catch (error) {
      errorLog("error in getImagesByFarmerId:69",params,{message:error.message})
      return {message:error.message}
    }
  }
  
  const getDocumentsByRequisitionId = async (requisition) => {
    try {
      const projection = [
        'document_id',
        'document_information',
        'document_name',
        'document_type_id',
      ]
      const farmerr = await dbConnections()
        .main.repos[RELATION_DOC].createQueryBuilder()
        .where('entity_1_entity_uuid=:id AND entity_1_type_id = :entity_id ', {
          id: requisition,
          entity_id: 1000460006,
        })
        .select(projection)
        .execute()
      return farmerr
    } catch (error) {
      errorLog("error in getDocumentsByRequisitionId",params,{message:error.message})
      return {message:error.message}
    }
  }
  

module.exports = {
    insertToDocument,
    insertDumpedDoc,
    getImagesByAnimalAId,
    getDocumentById,
    getDocumentReferences,
    getDocuments,
    deleteDocumentById,
    getImagesByFarmerId,
    getDocumentsByRequisitionId
};
