const { ACTIVE, ANIMAL_EAR_TAG_CLASSIFIER, TICKET_CLASSIFIER, FARMER_TO_ANIMAL } = require("../utils/constant");

const task_infromation_by_task_id=
`
SELECT
    coalesce(c1.customer_name_l10n, c2.customer_name_l10n) AS customer_name,
    coalesce(c1.customer_id, c2.customer_id) AS customer_id,
    ac.value_string_256 AS ear_tag,
    rf.reference_name AS task_name,
    ccc.value_string_256 AS ticket_number,
    cc.entity_type_id,
    cc.activity_id,
    cc.activity_date AS task_date,
    cc.visit_schedule_time
FROM main.care_calendar AS cc
    LEFT JOIN main.care_calendar_classification AS ccc
        ON ccc.care_calendar_id = cc.care_calendar_id AND ccc.classifier_id = ${TICKET_CLASSIFIER} AND ccc.active = ${ACTIVE}	

    LEFT JOIN main.ref_reference rf 
        ON rf.reference_id=cc.activity_id

    LEFT JOIN main.animal_classification AS ac
        ON ac.animal_id = cc.entity_uuid AND ac.classifier_id = ${ANIMAL_EAR_TAG_CLASSIFIER} AND ac.active = ${ACTIVE}

    LEFT JOIN main.entity_relationship er
        ON er.entity_relationship_type_id = ${FARMER_TO_ANIMAL} AND er.entity_2_entity_uuid = cc.entity_uuid AND er.active = ${ACTIVE}

    LEFT JOIN main.customer c1 
        ON c1.customer_id=cc.entity_uuid

    LEFT JOIN main.customer c2 
        ON c2.customer_id=er.entity_1_entity_uuid

WHERE cc.care_calendar_id =:care_calendar_id
`
module.exports = {task_infromation_by_task_id}