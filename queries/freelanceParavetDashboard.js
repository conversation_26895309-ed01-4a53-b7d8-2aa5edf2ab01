const { CONST_STATUS_ACTIVITY_COMPLETED, PREVENTIVE, CANCELLED_ACTIVITY_STATUS, ANIMAL_TYPE_CALENDAR, ABANDONED_ACTIVITY_STATUS, PPU_PARAVET_TO_FARMER, FARMER_TO_ANIMAL, ACTIVE } = require("../utils/constant");
module.exports = `
select COUNT(
    CASE WHEN
    care_calendar.calendar_activity_status not in (${CONST_STATUS_ACTIVITY_COMPLETED},${CANCELLED_ACTIVITY_STATUS},${ABANDONED_ACTIVITY_STATUS}) 
  THEN care_calendar_id
  END) AS open,
    current_timestamp + interval '15 days' as end_date,
    current_timestamp - interval '5 days' as start_date
  from main.staff
    left join main.entity_relationship as paravet_customer
        on
            paravet_customer.entity_relationship_type_id=${PPU_PARAVET_TO_FARMER} 
                and
            paravet_customer.entity_1_entity_uuid=staff.staff_id 
    left join main.entity_relationship as customer_animal
        on
            customer_animal.entity_relationship_type_id=${FARMER_TO_ANIMAL} 
                and
            customer_animal.entity_1_entity_uuid=paravet_customer.entity_2_entity_uuid
    left join main.animal
        on 
            animal.animal_id = customer_animal.entity_2_entity_uuid
                and
            animal.active=${ACTIVE}
    left join main.care_calendar
        on
            care_calendar.entity_type_id=${ANIMAL_TYPE_CALENDAR} 
                and 
            care_calendar.entity_uuid=animal.animal_id
                and
            care_calendar.active = ${ACTIVE}
    left join main.ref_activity ra 
        on 
            ra.activity_id = care_calendar.activity_id
                and
            ra.active = ${ACTIVE}
  where
    care_calendar.activity_date <= current_timestamp + interval '15 days'
        and
    care_calendar.activity_date >= current_timestamp - interval '5 days'
         and
    ra.activity_category = ${PREVENTIVE} 
        and
    staff.staff_id= :paravet_id
`;