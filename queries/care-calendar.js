const { configurationJSON } = require('@krushal-it/common-core')
const { dbConnections, preProcessRecords, postProcessRecords } = require('@krushal-it/ah-orm')
const moment = require('moment')
const { RELATION_STAFF_ACTIVITY, RELATION_CALENDAR, CONST_ID_TYPE_VILLAGE, BACK_OFFICE_PERSON, PARAVET, ACTIVE } = require('../utils/constant')
const { q_getCardTotalROZO_v2, q_getTotalFarmerROZO_v2, q_getROCardByGroupByParavet_v2, q_FarmerCountGroupByParavet_v2, q_getZOCardByGroupByRO_v2, q_FarmerCountGroupByRo_v2, insertStaffActivity } = require('./activity')
const { saveClassificationData, getClassificationConfiguration, loadClassificationData } = require('../services/common/classification.helper')
const { getCompiledQuery } = require('./index.js')
const { loadMeds } = require('../services/common/common.class.js')
const { classifiers, record_statuses } = require('../ENUMS.js')

class CareCalendarQueries {
  static async insertcalendarEntries(values, options = {}) {
    const entityCareCalendar = dbConnections().main.entities[RELATION_CALENDAR]
    const preProcessedValues = preProcessRecords(entityCareCalendar, values, entityCareCalendar.additionalAttributes)
    const mdatasource = options.datasource ? options.datasource : dbConnections().main.manager
    let ids = await mdatasource
      .createQueryBuilder()
      .insert()
      .into(RELATION_CALENDAR)
      .values(preProcessedValues)
      .execute()

    return ids
  }

  static async updateEntryStatus(id, data, token, transaction) {

    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
    const updateable = ['calendar_activity_status', 'completion_date', 'user_task_completion_time', 'system_task_completion_time', 'activity_completed_by']
    let newValues = {}

    for (const key of updateable) {
      if (data.hasOwnProperty(key)) {
        newValues[key] = data[key]
      }
    }

    if (Object.keys(newValues).length > 0) {
      if (!isPostgres) {
        newValues['data_sync_status'] = 1000101003
      }
      await transaction.getRepository("care_calendar").update({ care_calendar_id: id }, newValues);
    }

    let dataKeyList = Object.keys(data)
    const classificationConfig = getClassificationConfiguration()['CARE_CALENDAR_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
    let upsertData = {}
    for (let datakeyIndex = 0; datakeyIndex < dataKeyList.length; datakeyIndex++) {
      let key = dataKeyList[datakeyIndex]
      if (classificationConfig[key]) {
        upsertData[key] = data[key]
      }
    }
    await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', id, upsertData)
  }

  static async updateStaffActivity(value, options) {
    
    const { transaction } = options
    if (!transaction) throw new Error('options must have a transaction object')

    let { care_calendar } = options
    if (!care_calendar) 
      throw new Error('care_calendar id is required')
    care_calendar = Array.isArray(care_calendar) ? care_calendar : [care_calendar]

    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false

    let valid_care_calendar_data = await transaction
      .createQueryBuilder()
      .select('care_calendar_id,activity_id,entity_type_id,calendar_activity_status')
      .from('care_calendar')
      .where('care_calendar_id IN (:...care_calendar_id)', { care_calendar_id: care_calendar })
      .andWhere('calendar_activity_status NOT IN (:...status)', { status: [1000300050, 1000300051, 1000300052] })
      .execute()
    
    if (valid_care_calendar_data.length == 0) 
      throw new Error('no care calendar')
    const valid_ids = valid_care_calendar_data.map((el) => el.care_calendar_id)

    if (valid_ids.length > 0) {

      let existingData = await transaction.createQueryBuilder().select('care_calendar').from(RELATION_STAFF_ACTIVITY).where('care_calendar IN (:...care_calendar)', { care_calendar: valid_ids }).execute()
      let existingCareCalendars = new Set(existingData.map((data) => data.care_calendar))
      let newCareCalendars = []

      for (let careCalendarIndex = 0; careCalendarIndex < care_calendar.length; careCalendarIndex++) {
        if (!existingCareCalendars.has(valid_care_calendar_data[careCalendarIndex].care_calendar_id)) {
          newCareCalendars.push(valid_care_calendar_data[careCalendarIndex])
        }
      }

      if (existingData.length > 0) {
        const toBeUpdatedValue = { ...value }
        if (!isPostgres) {
          toBeUpdatedValue['data_sync_status'] = 1000101003
        }
        await transaction
          .createQueryBuilder()
          .update(RELATION_STAFF_ACTIVITY)
          .set(toBeUpdatedValue)
          .where('care_calendar IN (:...care_calendar)', { care_calendar: Array.from(existingCareCalendars) })
          .execute()
      }
      
      if (newCareCalendars.length > 0) {
        let values = []

        for (const data of newCareCalendars) {
          let newValue = {
            ...value,
            care_calendar: data.care_calendar_id,
            activity_id: data.activity_id,
            entity_type_id: data.entity_type_id,
          }
          values.push(newValue)
        }
        await insertStaffActivity(values, transaction)
      }
    }
  }

  static async getRoutePlan_v3(options = {}) {
    const notUsersForvill = new Set([1000230002, 1000230004])
    const { user_id, user_type } = options
    if (!user_id || !user_type) throw new Error('user_id and user_type are required in the options')
    const manager = options.manager || dbConnections().main.manager
    let innerQueryWithVill = `
        WITH VALID_VILLAGES AS
        (
        select 
            distinct (geography_id) as village_id
        from main.entity_geography
            left join main.staff
                on 
                    entity_geography.entity_uuid = staff.staff_id
        where
            entity_geography.geography_type_id = 1000320004
            AND
            entity_geography.active = 1000100001
            AND
            staff.staff_type_id = ${user_type == 1000230002 ? '1000230004' : user_type}
            ${user_type != 1000230002 ? ` AND staff.staff_id  = '${user_id}'` : ''}
        )`
    let innerQueryWithcc = `
            SELECT
                CUSTOMER.customer_id AS customer_id,
		        CUSTOMER.active AS is_customer_active,
                CUSTOMER.customer_name_l10n AS customer_name,
                CUSTOMER.customer_visual_id AS customer_visual_id,
                CUSTOMER.mobile_number AS customer_mobile_number,
                CUSTOMER_CLASSIFICATION.value_reference_id AS village_id,
                REF_VILLAGE.village_name_l10n AS village_name,
                REF_TALUK.taluk_id AS taluk_id,
                REF_TALUK.taluk_name_l10n AS taluk_name,
                REF_DISTRICT.district_id AS district_id,
                REF_DISTRICT.district_name_l10n AS district_name,
                REF_STATE.state_id AS state_id,
                REF_STATE.state_name_l10n AS state_name,
                ANIMAL.animal_id AS animal_id,
		        ANIMAL.active AS is_animal_active,
                ANIMAL.animal_visual_id AS animal_visual_id,
                REF_REFERENCE.reference_name AS animal_type,
                REF_REFERENCE.reference_name_l10n AS animal_type_json,
                REF_ANIMAL_NAME.value_string_256 AS animal_name,
                REF_ANIMAL_EAR_TAG.value_string_256 AS animal_ear_tag,
                COALESCE(
			        STAFF_ACTIVITY.staff_id, 
			        STAFF.staff_id
		        ) AS paravet_id,
                COALESCE(
                    STAFF_OVERRIDE.staff_name_l10n,
                    STAFF.staff_name_l10n
                ) AS paravet_name,
                COALESCE(
                    STAFF_OVERRIDE.mobile_number,
                    STAFF.mobile_number
                ) AS paravet_mobile_number,
                CARE_CALENDAR.care_calendar_id AS care_calendar_id,
                CARE_CALENDAR.activity_date AS visit_date,
                CARE_CALENDAR.activity_id AS activity_id,
                REF_ACTIVITY.activity_category AS activity_category,
                REF_ACTIVITY.activity_name AS activity_name,
                REF_ACTIVITY.activity_name_l10n AS activity_name_json,
                CARE_CALENDAR.calendar_activity_status AS activity_status_id,
                REF_ACTIVITY_STATUS.reference_name AS activity_status,
                REF_ACTIVITY_STATUS.reference_name_l10n AS activity_status_json,
                CARE_CALENDAR_CLASSIFICATION.value_string_256 AS activity_complaint_number,
                CASE
                    WHEN REF_ACTIVITY.activity_category IN (1000270002, 1000270003) THEN 1
                    ELSE -1
                END AS activity_priority,
                ROW_NUMBER() OVER (
                    PARTITION BY CUSTOMER.customer_id,
                    CARE_CALENDAR.activity_date :: DATE,
                    CASE
                        WHEN REF_ACTIVITY.activity_category IN (1000270002, 1000270003) THEN 1
                        ELSE -1
                    END
                    ORDER BY
                        CARE_CALENDAR.calendar_activity_status DESC
                ) AS ACTIVITY_ROW_NUM
            FROM
                main.CARE_CALENDAR
                LEFT JOIN 
			main.CARE_CALENDAR_CLASSIFICATION 
				ON 
					CARE_CALENDAR.care_calendar_id = CARE_CALENDAR_CLASSIFICATION.care_calendar_id
                			AND 
					CARE_CALENDAR_CLASSIFICATION.classifier_id = 2000000125
                LEFT JOIN 
			main.REF_ACTIVITY 
				ON 
					CARE_CALENDAR.activity_id = REF_ACTIVITY.activity_id
                LEFT JOIN 
			main.REF_REFERENCE REF_ACTIVITY_STATUS 
				ON 
					REF_ACTIVITY_STATUS.reference_id = CARE_CALENDAR.calendar_activity_status
                LEFT JOIN 
			main.ENTITY_RELATIONSHIP 
				ON 
					ENTITY_RELATIONSHIP.entity_2_entity_uuid = CARE_CALENDAR.entity_uuid
                			AND 
					ENTITY_RELATIONSHIP.entity_relationship_type_id = 1000210004
                			AND 
					CARE_CALENDAR.entity_type_id = 1000460002
                LEFT JOIN 
			main.CUSTOMER 
				ON 
					CUSTOMER.customer_id = ENTITY_RELATIONSHIP.entity_1_entity_uuid
                			OR 
					(
                    				CUSTOMER.customer_id = CARE_CALENDAR.entity_uuid
                    				AND 
						CARE_CALENDAR.entity_type_id = 1000460001
                			)
                LEFT JOIN 
			main.CUSTOMER_CLASSIFICATION 
				ON 
					CUSTOMER_CLASSIFICATION.customer_id = CUSTOMER.customer_id
                			AND 
					CUSTOMER_CLASSIFICATION.classifier_id = 2000000055
                LEFT JOIN 
			main.REF_VILLAGE 
				ON 
					REF_VILLAGE.village_id = CUSTOMER_CLASSIFICATION.value_reference_id
                LEFT JOIN 
			main.REF_TALUK 
				ON 
					REF_TALUK.taluk_id = REF_VILLAGE.taluk_id
                LEFT JOIN 
			main.REF_DISTRICT 
				ON 
					REF_DISTRICT.district_id = REF_TALUK.district_id
                LEFT JOIN 
			main.REF_STATE 
				ON 
					REF_STATE.state_id = REF_DISTRICT.state_id
                LEFT JOIN 
			main.ENTITY_GEOGRAPHY 
				ON 
					ENTITY_GEOGRAPHY.geography_id = REF_VILLAGE.village_id
                			AND 
					ENTITY_GEOGRAPHY.geography_type_id = 1000320004
                			AND 
					ENTITY_GEOGRAPHY.entity_type_id = 1000230004
                      AND 
					ENTITY_GEOGRAPHY.active = 1000100001
                LEFT JOIN 
			main.STAFF 
				ON 
					STAFF.staff_id = ENTITY_GEOGRAPHY.entity_uuid
                LEFT JOIN 
			main.STAFF_ACTIVITY 
				ON 
					STAFF_ACTIVITY.care_calendar = CARE_CALENDAR.care_calendar_id
                LEFT JOIN 
			main.STAFF STAFF_OVERRIDE 
				ON 
					STAFF_OVERRIDE.staff_id = STAFF_ACTIVITY.staff_id
                LEFT JOIN 
			main.ANIMAL 
				ON 
					ANIMAL.animal_id = ENTITY_RELATIONSHIP.entity_2_entity_uuid
                LEFT JOIN 
            main.ANIMAL_CLASSIFICATION REF_ANIMAL_TYPE 
                ON 
                    REF_ANIMAL_TYPE.animal_id = ANIMAL.animal_id
                    AND 
                    REF_ANIMAL_TYPE.classifier_id = 2000000035
                LEFT JOIN 
            main.REF_REFERENCE 
                ON 
                    REF_REFERENCE.reference_id = REF_ANIMAL_TYPE.value_reference_id
                LEFT JOIN 
			main.ANIMAL_CLASSIFICATION REF_ANIMAL_NAME 
				ON 
					REF_ANIMAL_NAME.animal_id = ANIMAL.animal_id
                			AND 
					REF_ANIMAL_NAME.classifier_id = 2000000033
                LEFT JOIN 
			main.ANIMAL_CLASSIFICATION REF_ANIMAL_EAR_TAG 
				ON 
					REF_ANIMAL_EAR_TAG.animal_id = ANIMAL.animal_id
                			AND 
					REF_ANIMAL_EAR_TAG.classifier_id = 2000000034
            WHERE
                CARE_CALENDAR.entity_type_id IN (1000460001, 1000460002)
                AND 
		        CARE_CALENDAR.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
                ${!notUsersForvill.has(user_type) ? 'AND CUSTOMER_CLASSIFICATION.value_reference_id IN (SELECT village_id FROM VALID_VILLAGES) ' : ''}               
        `
    let paramIndex = 1
    let params = []
    if (options.f_farmer_name) {
      innerQueryWithcc += ` AND (
                UPPER(CUSTOMER.customer_name_l10n->>'ul') LIKE UPPER($${paramIndex++}) OR 
                UPPER(CUSTOMER.customer_name_l10n->>'mr') LIKE UPPER($${paramIndex++}) OR
                UPPER(CUSTOMER.customer_name_l10n->>'en') LIKE UPPER($${paramIndex++}) OR 
                UPPER(CUSTOMER.customer_name_l10n->>'hi') LIKE UPPER($${paramIndex++}))`
      params.push('%' + options.f_farmer_name.toUpperCase() + '%')
      params.push('%' + options.f_farmer_name.toUpperCase() + '%')
      params.push('%' + options.f_farmer_name.toUpperCase() + '%')
      params.push('%' + options.f_farmer_name.toUpperCase() + '%')
    }
    if (options.f_village) {
      let villages = Array.isArray(options.f_village) ? options.f_village : [options.f_village]
      if (villages.length > 0) {
        let villageFilter = `
                    UPPER(REF_VILLAGE.village_name_l10n->>'mr') LIKE ANY ($${paramIndex++}) OR 
                    UPPER(REF_VILLAGE.village_name_l10n->>'en') LIKE ANY ($${paramIndex++}) OR 
                    UPPER(REF_VILLAGE.village_name_l10n->>'hi') LIKE ANY ($${paramIndex++}) OR 
                    UPPER(REF_VILLAGE.village_name_l10n->>'ul') LIKE ANY ($${paramIndex++})`
        params.push(villages.map((village) => `%${village.toUpperCase()}%`))
        params.push(villages.map((village) => `%${village.toUpperCase()}%`))
        params.push(villages.map((village) => `%${village.toUpperCase()}%`))
        params.push(villages.map((village) => `%${village.toUpperCase()}%`))
        innerQueryWithcc += ` AND (${villageFilter})`
      }
    }
    if (options.f_activity_category) {
      options.f_activity_category = Array.isArray(options.f_activity_category) ? options.f_activity_category : [options.f_activity_category]
      innerQueryWithcc += ` AND REF_ACTIVITY.activity_category  = ANY($${paramIndex++})`
      params.push(options.f_activity_category)
    }
    if (options.f_activity_status) {
      options.f_activity_status = Array.isArray(options.f_activity_status) ? options.f_activity_status : [options.f_activity_status]
      innerQueryWithcc += ` AND CARE_CALENDAR.calendar_activity_status  = ANY($${paramIndex++})`
      params.push(options.f_activity_status)
    }
    if (!options.start_date && !options.end_date) {
      innerQueryWithcc += ' AND CARE_CALENDAR.activity_date::DATE < current_date + 15'
    }
    if (options.start_date) {
      innerQueryWithcc += ` AND CARE_CALENDAR.activity_date::DATE >= $${paramIndex++}`
      params.push(options.start_date)
    }
    if (options.end_date) {
      innerQueryWithcc += ` AND CARE_CALENDAR.activity_date::DATE <= $${paramIndex++}`
      params.push(options.end_date)
    }
    if (user_type == 1000230004) {
      innerQueryWithcc += ` AND COALESCE(STAFF_ACTIVITY.staff_id, STAFF.staff_id) = $${paramIndex++}`
      params.push(user_id)
    }
    if (options.f_paravet) {
      options.f_paravet = Array.isArray(options.f_paravet) ? options.f_paravet : [options.f_paravet]
      innerQueryWithcc += ` AND COALESCE(STAFF_ACTIVITY.staff_id, STAFF.staff_id) = ANY($${paramIndex++})`
      params.push(options.f_paravet)
    }
    let innerQuery = `${innerQueryWithVill},ALL_CC AS (${innerQueryWithcc})`
    let outterQuery = `
            SELECT
            customer_id,
            customer_name,
            customer_visual_id,
            customer_mobile_number AS mobile_number,
            village_name,
            taluk_name,
            visit_date,
            count(distinct(animal_id)) as animal_count,
            max(activity_priority) as cv_priority,
            count(activity_id) as activity_count,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_id
                END
            ) AS first_cc_activity_id,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_category
                END
            ) AS fcca_activity_category_id,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_name
                END
            ) AS fcca_name,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_name_json::text
                END
            )::jsonb AS fcca_name_json,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_status_id
                END
            ) AS fcca_status_id,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_status
                END
            ) AS fcca_status,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_status_json::text
                END
            )::jsonb AS fcca_status_json,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_complaint_number
                END
            ) AS fcca_complaint_number,	


            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_id
                END
            ) AS second_cc_activity_id,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_category
                END
            ) AS scca_activity_category_id,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_name
                END
            ) AS scca_name,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_name_json::text
                END
            )::jsonb AS scca_name_json,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_status_id
                END
            ) AS scca_status_id,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_status
                END
            ) AS scca_status,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_status_json::text
                END
            )::jsonb AS scca_status_json,
            MAX(CASE 
                WHEN activity_category IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_complaint_number
                END
            ) AS scca_complaint_number,	
            
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_id
                END
            ) AS first_ncc_activity_id,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_category
                END
            ) AS fncca_activity_category_id,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_name
                END
            ) AS fncca_name,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_name_json::text
                END
            )::jsonb AS fncca_name_json,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_status_id
                END
            ) AS fncca_status_id,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_status
                END
            ) AS fncca_status,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_status_json::text
                END
            )::jsonb AS fncca_status_json,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 1
                THEN activity_complaint_number
                END
            ) AS fncca_complaint_number,	
            
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_id
                END
            ) AS second_ncc_activity_id,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_category
                END
            ) AS sncca_activity_category_id,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_name
                END
            ) AS sncca_name,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_name_json::text
                END
            )::jsonb AS sncca_name_json,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_status_id
                END
            ) AS sncca_status_id,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_status
                END
            ) AS sncca_status,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_status_json::text
                END
            )::jsonb AS sncca_status_json,
            MAX(CASE 
                WHEN activity_category NOT IN (1000270002, 1000270003) AND ACTIVITY_ROW_NUM = 2
                THEN activity_complaint_number
                END
            ) AS sncca_complaint_number	
            
        FROM 
            ALL_CC
        WHERE
	        is_customer_active = 1000100001
	        AND
	        (
		        is_animal_active is null 
		        OR 
		        is_animal_active = 1000100001
	        )	    
        GROUP BY
            customer_id,
            customer_name,
            customer_visual_id,
            customer_mobile_number,
            village_name,
            taluk_name,
            visit_date
        ORDER BY
            cv_priority DESC,
            visit_date,
            activity_count DESC	
        `
    let base_query = innerQuery + outterQuery
    let count = await manager.query(`SELECT COUNT(*) as count FROM (${base_query}) AS c`, params)
    count = parseInt(count[0].count)
    let total_page = 1
    if (!isNaN(options.page_number) && !isNaN(options.page_limit)) {
      let offset = options.page_limit * (options.page_number - 1)
      if (offset < 0) return { message: 'Invalid page number' }
      base_query = `${base_query} LIMIT $${paramIndex++} OFFSET $${paramIndex++}`
      params.push(options.page_limit)
      params.push(offset)

      total_page = parseInt(count / options.page_limit)
      total_page += count % options.page_limit === 0 ? 0 : 1
    }
    let pvQuery = `select staff_name_l10n,staff_id from main.staff WHERE staff_type_id = 1000230004 AND active = 1000100001`
    let routePlan = await manager.query(base_query, params)
    let paravet = await manager.query(pvQuery, [])

    return { routePlan, paravets: paravet }
  }

  static async getCardOfRO(options = {}) {
    const manager = options.manager || dbConnections().main.manager
    let base_query = `SELECT * FROM (select
      'critical' as category,
      lssgeo.entity_uuid as lss_id,
      count(*) as total,
      count(*) filter (where cc.calendar_activity_status NOT  IN (1000300050, 1000300051, 1000300052)) as pending   
      -- 	select cc.care_calendar_id 
      from ( select * from main.staff
      where staff_id = '${options.user_id}') as staff
      join main.entity_geography as rogeo on rogeo.entity_uuid = staff.staff_id
      and rogeo.entity_type_id = 1000230001 and rogeo.geography_type_id = 1000320004
      and rogeo.active = 1000100001
      join main.entity_geography as lssgeo on lssgeo.entity_type_id = 1000230004 and lssgeo.geography_type_id = 1000320004
      and lssgeo.active = 1000100001
      and lssgeo.geography_id = rogeo.geography_id
      join main.customer_classification as vcc on
      vcc.value_reference_id = rogeo.geography_id
      and vcc.classifier_id = 2000000055
      join main.entity_relationship as faer on
      faer.entity_relationship_type_id = 1000210004 and faer.entity_1_entity_uuid = vcc.customer_id
      join main.care_calendar cc on cc.entity_uuid = faer.entity_2_entity_uuid
      join main.ref_activity  as ra on ra.activity_id = cc.activity_id
      where cc.activity_date::date = current_date 
      and ra.activity_category in (1000270002,1000270003)
      group by lssgeo.entity_uuid
      
      union all
      
      select
      'preventive' as category,
      lssgeo.entity_uuid as lss_id,
      count(*)  as  total,
      count(*) filter (where cc.calendar_activity_status NOT IN (1000300050, 1000300051, 1000300052)) as pending
      from ( select * from main.staff
      where staff_id = '${options.user_id}') as staff
      join main.entity_geography as rogeo on rogeo.entity_uuid = staff.staff_id
      and rogeo.entity_type_id = 1000230001 and rogeo.geography_type_id = 1000320004
      and rogeo.active = 1000100001
      join main.entity_geography as lssgeo on lssgeo.entity_type_id = 1000230004 and lssgeo.geography_type_id = 1000320004
      and lssgeo.active = 1000100001
      and lssgeo.geography_id = rogeo.geography_id
      join main.customer_classification as vcc on
      vcc.value_reference_id = rogeo.geography_id
      and vcc.classifier_id = 2000000055
      join main.entity_relationship as faer on
      faer.entity_relationship_type_id = 1000210004 and faer.entity_1_entity_uuid = vcc.customer_id
      join main.care_calendar cc on cc.entity_uuid = faer.entity_2_entity_uuid
      join main.ref_activity  as ra on ra.activity_id = cc.activity_id
      where cc.activity_date::date = current_date 
      and ra.activity_category = 1000270001
      group by lssgeo.entity_uuid
      
      union all
      
      select  
      'farmer' as category,
      entity_uuid as lss_id,
      sum(one) as total,
      sum(pending) as pending from
      (
      select lssgeo.entity_uuid,faer.entity_1_entity_uuid,
      1 as one,
      least(count(*)  filter (where cc.calendar_activity_status  NOT IN (1000300050, 1000300051, 1000300052)),1) as pending
      from (select * from main.staff
      where staff_id = '${options.user_id}') as staff
      join main.entity_geography as rogeo on rogeo.entity_uuid = staff.staff_id
      and rogeo.entity_type_id = 1000230001 and rogeo.geography_type_id = 1000320004
      and rogeo.active = 1000100001
      join main.entity_geography as lssgeo on lssgeo.entity_type_id = 1000230004 and lssgeo.geography_type_id = 1000320004
      and lssgeo.active = 1000100001
      and lssgeo.geography_id = rogeo.geography_id
      join main.customer_classification as vcc on
      vcc.value_reference_id = rogeo.geography_id
      and vcc.classifier_id = 2000000055
      join main.entity_relationship as faer on
      faer.entity_relationship_type_id = 1000210004 and faer.entity_1_entity_uuid = vcc.customer_id
      join main.care_calendar cc on cc.entity_uuid = faer.entity_2_entity_uuid
      where cc.activity_date::date = current_date 
      group by lssgeo.entity_uuid,faer.entity_1_entity_uuid order by lssgeo.entity_uuid,faer.entity_1_entity_uuid) as t
      group by entity_uuid) as t order by t.lss_id`
    return await manager.query(base_query, [])
  }

  static async getCardOfPV(options = {}) {
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1
    let f_activity_category = options.f_activity_category || [1000270001, 1000270002]

    f_activity_category = Array.isArray(f_activity_category) ? f_activity_category : [f_activity_category]

    const manager = options.manager || dbConnections().main.manager
    let queryToday = `select 'preventive' as category, count(care_calendar_id) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
      and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
      and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
      and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
      and eg.active = 1000100001
    inner join main.entity_geography ueg on (
      (
        (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = '${options.user_id}')
      )
      and
      ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
      and ueg.active = 1000100001
    )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001)
    where cc.entity_type_id = 1000460002 
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    ${isPostgres ? 'and cc.activity_date::DATE <= current_date' : "and cc.activity_date <= date('now', 'start of day', '+1 day')"}
    
    union all
	select 'curative' as category, count(care_calendar_id) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
      and er.entity_relationship_type_id = 1000210004
    inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
      and ccl.classifier_id = 2000000055
    inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
      and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
      and eg.active = 1000100001
    inner join main.entity_geography ueg on (
      (
        (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = '${options.user_id}')
      )
      and
      ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
      and ueg.active = 1000100001
    )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270002, 1000270003)
    where cc.entity_type_id = 1000460002 
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    ${isPostgres ? 'and cc.activity_date::DATE <= current_date' : "and cc.activity_date <= date('now', 'start of day', '+1 day')"}
    `
    let today = await manager.query(queryToday, [])

    let base_query2 = `select 'preventive' as category, count(care_calendar_id) as count
      from main.care_calendar cc
      inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
	    and er.entity_relationship_type_id = 1000210004
      inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
	    and ccl.classifier_id = 2000000055
      inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
	    and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
      and eg.active = 1000100001
      inner join main.entity_geography ueg on (
        (
          (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = '${options.user_id}')
        )
        and
        ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
        and ueg.active = 1000100001
      )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270001)
    where cc.entity_type_id = 1000460002 
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    ${isPostgres ? 'and cc.activity_date::DATE <= current_date' : "and cc.activity_date <= date('now', 'start of day', '+15 day')"}
    union all
    select 'curative' as category, count(care_calendar_id) as count
      from main.care_calendar cc
      inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
	    and er.entity_relationship_type_id = 1000210004
      inner join main.customer_classification ccl on er.entity_1_entity_uuid = ccl.customer_id
	    and ccl.classifier_id = 2000000055
      inner join main.entity_geography eg on ccl.value_reference_id = eg.geography_id
	    and eg.geography_type_id = 1000320004 and eg.entity_type_id = 1000230004
      and eg.active = 1000100001
      inner join main.entity_geography ueg on (
        (
          (ueg.entity_type_id = 1000230004 and ueg.entity_uuid = '${options.user_id}')
        )
        and
        ueg.geography_type_id = 1000320004 and ueg.geography_id = eg.geography_id
        and ueg.active = 1000100001
        )
    inner join main.ref_activity at_rr on at_rr.activity_id = cc.activity_id and at_rr.activity_category in (1000270002, 1000270003)
    where cc.entity_type_id = 1000460002 
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    ${isPostgres ? 'and cc.activity_date::DATE <= current_date' : "and cc.activity_date <= date('now', 'start of day', '+15 day')"}
    `
    let total = await manager.query(base_query2, [])
    const countsByCategory = {}

    today.forEach((count) => {
      const category = count.category
      const todayCount = count.count
      countsByCategory[category] = { todayCount }
    })

    total.forEach((count) => {
      const category = count.category
      const totalCount = count.count
      countsByCategory[category].totalCount = totalCount
    })
    let formatedResponse = []
    for (const [key, val] of Object.entries(countsByCategory)) {
      formatedResponse.push({ ...val, category: key == 'preventive' ? 1000270001 : 1000270002 })
    }
    return { data: formatedResponse }
  }

  static async getCardOfFarmer(options = {}) {
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
    const manager = options.manager || dbConnections().main.manager
    let queryToday = `select count(care_calendar_id) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
      and er.entity_relationship_type_id = 1000210004
	  and er.entity_1_entity_uuid = '${options.customer_id}'
    where cc.entity_type_id = 1000460002
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    ${isPostgres ? 'and cc.activity_date::DATE <= current_date' : "and cc.activity_date <= date('now', 'start of day', '+1 day')"}`
    let today = await manager.query(queryToday, [])
    let queryTotal = `select count(care_calendar_id) as count
    from main.care_calendar cc
    inner join main.entity_relationship er on cc.entity_uuid = er.entity_2_entity_uuid
      and er.entity_relationship_type_id = 1000210004
	  and er.entity_1_entity_uuid = '${options.customer_id}'
    where cc.entity_type_id = 1000460002
    and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052)
    ${isPostgres ? 'and cc.activity_date::DATE <= current_date + 15' : "and cc.activity_date <= date('now', 'start of day', '+15 day')"}`
    let total = await manager.query(queryTotal, [])
    return { today_count: today, total_count: total }
  }

  static async getTotalOfZO(options = {}) {
    const manager = options.manager || dbConnections().main.manager
    let base_query = `select
    'critical' as category,
    count(*) as total,
    count(*) filter (where cc.calendar_activity_status   NOT IN (1000300050, 1000300051, 1000300052)) as pending   
    from ( select * from main.staff
    where staff_id = '${options.user_id}') as staff
    join main.entity_geography as zogeo on zogeo.entity_uuid = staff.staff_id
    and zogeo.entity_type_id = 1000230005 and zogeo.geography_type_id = 1000320004
    and zogeo.active = 1000100001
    join main.customer_classification as vcc on
    vcc.value_reference_id = zogeo.geography_id
    and vcc.classifier_id = 2000000055
    join main.entity_relationship as faer on
    faer.entity_relationship_type_id = 1000210004 and faer.entity_1_entity_uuid = vcc.customer_id
    join main.care_calendar cc on cc.entity_uuid = faer.entity_2_entity_uuid
    join main.ref_activity  as ra on ra.activity_id = cc.activity_id
    where cc.activity_date::date = current_date 
    and ra.activity_category in (1000270002,1000270003)
    
    union all 
    
    select
    'preventive' as preventive,
    count(*) as total,
    count(*) filter (where cc.calendar_activity_status   NOT IN (1000300050, 1000300051, 1000300052)) as pending   
    from ( select * from main.staff
    where staff_id = '${options.user_id}') as staff
    join main.entity_geography as zogeo on zogeo.entity_uuid = staff.staff_id
    and zogeo.entity_type_id = 1000230005 and zogeo.geography_type_id = 1000320004
    and zogeo.active = 1000100001
    join main.customer_classification as vcc on
    vcc.value_reference_id = zogeo.geography_id
    and vcc.classifier_id = 2000000055
    join main.entity_relationship as faer on
    faer.entity_relationship_type_id = 1000210004 and faer.entity_1_entity_uuid = vcc.customer_id
    join main.care_calendar cc on cc.entity_uuid = faer.entity_2_entity_uuid
    join main.ref_activity  as ra on ra.activity_id = cc.activity_id
    where cc.activity_date::date = current_date 
    and ra.activity_category = 1000270001
    
    union all
    
    select  
    'farmer' as category,
    sum(one) as total,
    sum(pending) as pending from
    (select 
    1 as one,
    least(count(*)  filter (where cc.calendar_activity_status  NOT IN (1000300050, 1000300051, 1000300052)),1) as pending
    from (select * from main.staff
    where staff_id = '${options.user_id}') as staff
    join main.entity_geography as zogeo on zogeo.entity_uuid = staff.staff_id
    and zogeo.entity_type_id = 1000230005 and zogeo.geography_type_id = 1000320004
    and zogeo.active = 1000100001
    join main.customer_classification as vcc on
    vcc.value_reference_id = zogeo.geography_id
    and vcc.classifier_id = 2000000055
    join main.entity_relationship as faer on
    faer.entity_relationship_type_id = 1000210004 and faer.entity_1_entity_uuid = vcc.customer_id
    join main.care_calendar cc on cc.entity_uuid = faer.entity_2_entity_uuid
    where cc.activity_date::date = current_date
    group by vcc.customer_id order by customer_id
    ) as t`
    return await manager.query(base_query, [])
  }
  static async getTotalOfRO(options = {}) {
    const manager = options.manager || dbConnections().main.manager
    let base_query = `
      select
      'critical' as category,
      -- lssgeo.entity_uuid as lss_id,
      count(*) as total,
      count(*) filter (where cc.calendar_activity_status   NOT IN (1000300050, 1000300051, 1000300052)) as pending   
      from ( select * from main.staff
      where staff_id = '${options.user_id}' ) as staff
      join main.entity_geography as rogeo on rogeo.entity_uuid = staff.staff_id
      and rogeo.entity_type_id = 1000230001 and rogeo.geography_type_id = 1000320004
      and rogeo.active = 1000100001
      join main.customer_classification as vcc on
      vcc.value_reference_id = rogeo.geography_id
      and vcc.classifier_id = 2000000055
      join main.entity_relationship as faer on
      faer.entity_relationship_type_id = 1000210004 and faer.entity_1_entity_uuid = vcc.customer_id
      join main.care_calendar cc on cc.entity_uuid = faer.entity_2_entity_uuid
      join main.ref_activity  as ra on ra.activity_id = cc.activity_id
      where cc.activity_date::date = current_date 
      and ra.activity_category in (1000270002,1000270003)
      
      union all 
      
      select
      'preventive' as preventive,
      count(*) as total,
      count(*) filter (where cc.calendar_activity_status   NOT IN (1000300050, 1000300051, 1000300052)) as pending   
      from ( select * from main.staff
      where staff_id = '${options.user_id}') as staff
      join main.entity_geography as rogeo on rogeo.entity_uuid = staff.staff_id
      and rogeo.entity_type_id = 1000230001 and rogeo.geography_type_id = 1000320004
      and rogeo.active = 1000100001
      join main.customer_classification as vcc on
      vcc.value_reference_id = rogeo.geography_id
      and vcc.classifier_id = 2000000055
      join main.entity_relationship as faer on
      faer.entity_relationship_type_id = 1000210004 and faer.entity_1_entity_uuid = vcc.customer_id
      join main.care_calendar cc on cc.entity_uuid = faer.entity_2_entity_uuid
      join main.ref_activity  as ra on ra.activity_id = cc.activity_id
      where cc.activity_date::date = current_date 
      and ra.activity_category = 1000270001
      
      union all
      
      select  
      'farmer' as category,
      sum(one) as total,
      sum(pending) as pending from
      (
        
        
      select 
      1 as one,
      least(count(*)  filter (where cc.calendar_activity_status  NOT IN (1000300050, 1000300051, 1000300052)),1) as pending
      from (select * from main.staff
      where staff_id = '${options.user_id}') as staff
      join main.entity_geography as rogeo on rogeo.entity_uuid = staff.staff_id
      and rogeo.entity_type_id = 1000230001 and rogeo.geography_type_id = 1000320004
      and rogeo.active = 1000100001
      join main.customer_classification as vcc on
      vcc.value_reference_id = rogeo.geography_id
      and vcc.classifier_id = 2000000055
      join main.entity_relationship as faer on
      faer.entity_relationship_type_id = 1000210004 and faer.entity_1_entity_uuid = vcc.customer_id
      join main.care_calendar cc on cc.entity_uuid = faer.entity_2_entity_uuid
      where cc.activity_date::date = current_date
      group by vcc.customer_id order by customer_id
      ) as t`
    return await manager.query(base_query, [])
  }
  static async getRoutePlanDetails(options = {}) {
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
    if (!options.customer_id && !options.route_date) return []
    let base_query = `
        select cc.care_calendar_id,
          ${isPostgres ? 'coalesce(cc.completion_date, cc.activity_date)::DATE as activity_date' : 'coalesce(cc.completion_date, cc.activity_date) as activity_date'},
          cc.entity_uuid as animal_id,
          cc.activity_id, an_ra.activity_name,
          sr_no.value_string_256 as sr_number, 
          an_ra.activity_name_l10n,
          cc.calendar_activity_status,
          as_mr.reference_name as activity_status,
          as_mr.reference_name_l10n as activity_status_l10n,
          a.animal_visual_id,
          et_ac.value_string_256 as animal_ear_tag,
          atd.document_id as animal_thumbnail_document_id,
          atd.document_information as animal_thumbnail_document_information,
          atd.client_document_information as animal_thumbnail_client_document_information
        from main.care_calendar cc
        inner join (
          select animal_id, min(animal_priority)
          from (
            select cc.entity_uuid as animal_id, 1 as animal_priority
            from main.entity_relationship er, main.care_calendar cc, main.ref_activity an_ra
            /* params for query - put customer id  */
            where er.entity_1_entity_uuid = '${options.customer_id}' and er.entity_relationship_type_id = 1000210004
            and er.entity_2_entity_uuid = cc.entity_uuid and cc.entity_type_id = 1000460002
            /* and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052) */
            /* params for query - put visit date here  */
            ${isPostgres ? 'and coalesce(cc.completion_date, cc.activity_date)::DATE ' : " and date(coalesce(cc.completion_date, cc.activity_date), 'start of day')"} = '${moment(options.route_date).format('YYYY-MM-DD')}'
            and cc.activity_id = an_ra.activity_id and an_ra.activity_category in (10001700, 10004700)
            
            union all
            
            select cc.entity_uuid as animal_id, 2 as animal_priority
            from main.entity_relationship er, main.care_calendar cc, main.ref_activity an_ra
            /* params for query - put customer id  */
            where er.entity_1_entity_uuid = '${options.customer_id}' and er.entity_relationship_type_id = 1000210004
            and er.entity_2_entity_uuid = cc.entity_uuid and cc.entity_type_id = 1000460002
            /* and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052) */
            /* params for query - put visit date here  */
            ${isPostgres ? 'and coalesce(cc.completion_date, cc.activity_date)::DATE ' : " and date(coalesce(cc.completion_date, cc.activity_date), 'start of day')"} = '${moment(options.route_date).format('YYYY-MM-DD')}'
            and cc.activity_id = an_ra.activity_id and an_ra.activity_category not in (10001700, 10004700)
          ) a
          group by animal_id
          order by min(animal_priority) asc
        ) pa on pa.animal_id = cc.entity_uuid
        inner join main.animal a on a.animal_id = cc.entity_uuid
        inner join main.ref_activity an_ra on an_ra.activity_id = cc.activity_id
        inner join main.ref_reference as_mr on as_mr.reference_id = cc.calendar_activity_status
        left join main.animal_classification et_ac on et_ac.animal_id = a.animal_id and et_ac.classifier_id = 2000000034
        left join (
          select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, d.document_id, d.document_information, d.client_document_information
          from main.document d, (
						select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id,
              ${isPostgres ? ' max(document_id::text) max_document_id ' : ' max(document_id) max_document_id '}
						from main.document d, (
							select d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id, max(created_at) max_created_at
							from main.document d
							where d.document_type_id = 1000260001
							group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
						) mcad
						where d.entity_1_type_id = mcad.entity_1_type_id and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid and d.document_type_id = mcad.document_type_id
						and d.created_at = mcad.max_created_at
						group by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
          ) mdid
          where d.document_id = ${isPostgres ? ' mdid.max_document_id::uuid ' : ' mdid.max_document_id '}
          /*and d.entity_1_type_id = mdid.entity_1_type_id 
          and d.entity_1_entity_uuid = mdid.entity_1_entity_uuid 
          and d.document_type_id = mdid.document_type_id*/
          order by d.entity_1_type_id, d.entity_1_entity_uuid, d.document_type_id
        ) atd on atd.entity_1_type_id = 1000220002 and atd.entity_1_entity_uuid = a.animal_id
          and atd.document_type_id = 1000260001
        left join main.care_calendar_classification sr_no
          on sr_no.care_calendar_id = cc.care_calendar_id and sr_no.classifier_id = ${classifiers.COMPLAINT_NUMBER} and sr_no.active = ${record_statuses.ACTIVE}
        where cc.entity_type_id = 1000460002
        /* and cc.calendar_activity_status not in (1000300050, 1000300051, 1000300052) */
        /* params for query - put visit date here  */
        ${options.animal_id?'and cc.entity_uuid ='+ `'${options.animal_id}'`:''}
        ${isPostgres ? 'and coalesce(cc.completion_date, cc.activity_date)::DATE ' : " and date(coalesce(cc.completion_date, cc.activity_date), 'start of day')"} = '${moment(options.route_date).format('YYYY-MM-DD')}'
      `
    let data = await dbConnections().main.manager.query(base_query, [])
    postProcessRecords(undefined, data, { json_columns: ['animal_thumbnail_document_information', 'activity_name_l10n', 'activity_status_l10n', 'animal_thumbnail_client_document_information'] })
    return data
  }

  static async taskListingQuery(options = {}) {
    let manager = dbConnections().main.manager
    let paramIndex = 1
    let params = []
    let staff_filter = `
    inner join main.entity_geography eg on eg.geography_id = rv.village_id 
    and eg.geography_type_id = 1000320004
    and eg.active = 1000100001
    /* use logged in user's staff id below */
    and eg.entity_uuid = '${options.user_id}'`

    let base_query = `
   
     select * from (
      select cc.care_calendar_id, 
     cc.activity_date,cc.entity_type_id,
      ra.activity_category as activity_category_id,
     coalesce(ra.activity_name_l10n->>'en',
          ra.activity_name_l10n->>'ul')        
      as activity_name,
      ac.reference_name as activity_catefory_name,
      cc.activity_id, tn_ccc.value_string_256 as complaint_number,
      cc.calendar_activity_status, as_rr.reference_name as activity_status,
     null as animal_ear_tag,
      coalesce(c.customer_name_l10n->>'ul', c.customer_name_l10n->>'en') as farmer_name, 
     c.mobile_number,
  rv.village_id, rv.village_name_l10n->>'en' as village_name, 
     cc.updated_at
      from main.care_calendar cc
      inner join main.ref_activity as ra on ra.activity_id = cc.activity_id
      inner join main.ref_reference as ac on ra.activity_category = ac.reference_id
      inner join main.ref_reference as_rr on as_rr.reference_id = cc.calendar_activity_status
      left join main.care_calendar_classification tn_ccc on tn_ccc.care_calendar_id = cc.care_calendar_id
      and tn_ccc.classifier_id = 2000000125
      inner join main.customer c on c.customer_id = cc.entity_uuid
     
      
      
      inner join main.customer_classification fv_cc on fv_cc.customer_id = c.customer_id and fv_cc.classifier_id = 2000000055  
      inner join main.ref_village rv on rv.village_id = fv_cc.value_reference_id
      /** optional below is for non back end user  start **/
      ${options.user_type === 1000230002 ? '' : staff_filter}
      /** optional below is for non back end user  en **/
      
      
    where cc.entity_type_id = 1000460001
   
  
    union all
    
   
      select cc.care_calendar_id, cc.activity_date,cc.entity_type_id,
      ra.activity_category as activity_category_id, coalesce(ra.activity_name_l10n->>'en',ra.activity_name_l10n->>'ul')        
      as activity_name,
      ac.reference_name as activity_catefory_name,
      cc.activity_id, tn_ccc.value_string_256 as complaint_number,
      cc.calendar_activity_status, as_rr.reference_name as activity_status, et_ac.value_string_256 as animal_ear_tag,
      coalesce(c.customer_name_l10n->>'ul', c.customer_name_l10n->>'en') as farmer_name, c.mobile_number,
      rv.village_id, rv.village_name_l10n->>'en' as village_name, cc.updated_at
      from main.care_calendar cc
      inner join main.ref_activity as ra on ra.activity_id = cc.activity_id
      inner join main.ref_reference as ac on ra.activity_category = ac.reference_id
      inner join main.ref_reference as_rr on as_rr.reference_id = cc.calendar_activity_status
      left join main.care_calendar_classification tn_ccc on tn_ccc.care_calendar_id = cc.care_calendar_id
      and tn_ccc.classifier_id = 2000000125
      inner join main.animal a on a.animal_id = cc.entity_uuid
      left join main.animal_classification et_ac on et_ac.animal_id = a.animal_id and et_ac.classifier_id = 2000000034
      inner join main.entity_relationship er on er.entity_2_entity_uuid = a.animal_id
        and er.entity_relationship_type_id = 1000210004
      inner join main.customer c on c.customer_id = er.entity_1_entity_uuid
      inner join main.customer_classification fv_cc on fv_cc.customer_id = c.customer_id and fv_cc.classifier_id = 2000000055  
      inner join main.ref_village rv on rv.village_id = fv_cc.value_reference_id
      /** optional below is for non back end user  start **/
  
      ${options.user_type === 1000230002 ? '' : staff_filter}
      /** optional below is for non back end user  en **/
   
    where cc.entity_type_id = 1000460002
    ) as tl
    where 1 = 1
    
   
    
    
    `

    if (options.f_date_from) {
      base_query += `AND  tl.activity_date::DATE >= $${paramIndex++} `
      params.push(options.f_date_from)
    }
    if (options.f_date_to) {
      base_query += `AND  tl.activity_date::DATE <= $${paramIndex++} `
      params.push(options.f_date_to)
    }
    //TODO: test
    if (options.f_activity_category) {
      options.f_activity_category = Array.isArray(options.f_activity_category) ? options.f_activity_category : [options.f_activity_category]
      base_query += `AND  tl.activity_category_id = ANY ($${paramIndex++}) `
      params.push(options.f_activity_category)
    }
    if (options.f_activity_status) {
      options.f_activity_status = Array.isArray(options.f_activity_status) ? options.f_activity_status : [options.f_activity_status]
      base_query += `AND  tl.calendar_activity_status = ANY ($${paramIndex++}) `
      params.push(options.f_activity_status)
    }
    if (options.f_activity_name) {
      base_query += `AND upper(activity_name) like upper($${paramIndex++})`
      params.push(`%${options.f_activity_name}%`)
    }
    if (options.f_complaint_number) {
      base_query += `AND upper(tl.complaint_number) like upper($${paramIndex++})`
      params.push(`%${options.f_complaint_number}%`)
    }
    if (options.f_ear_tag) {
      base_query += `AND upper(tl.animal_ear_tag) like upper($${paramIndex++})`
      params.push(`%${options.f_ear_tag}%`)
    }
    if (options.f_farmer_name) {
      base_query += `AND upper(tl.farmer_name) like upper($${paramIndex++})`
      params.push(`%${options.f_farmer_name}%`)
    }
    if (options.f_farmer_mobile) {
      base_query += `AND upper(tl.mobile_number) like upper($${paramIndex++})`
      params.push(`%${options.f_farmer_mobile}%`)
    }
    if (options.f_village) {
      base_query += `AND upper(tl.village_name) like upper($${paramIndex++})`
      params.push(`%${options.f_village}%`)
    }
    base_query += ' order by updated_at'

    let count = await manager.query(`SELECT COUNT(*) FROM (${base_query}) AS c`, params)
    count = parseInt(count[0].count)
    let total_page = 1
    if (!isNaN(options.page_number) && !isNaN(options.page_limit)) {
      let offset = options.page_limit * (options.page_number - 1)
      if (offset < 0) return { message: 'Invalid page number' }
      base_query = `${base_query} LIMIT $${paramIndex++} OFFSET $${paramIndex++}`
      params.push(options.page_limit)
      params.push(offset)
      total_page = parseInt(count / options.page_limit)
      total_page += count % options.page_limit === 0 ? 0 : 1
    }
    let data = await manager.query(base_query, params)
    return { data, total_page }
  }

  static async getTotalZORO_v2(options = {}) {
    let queryTaskTillToday = q_getCardTotalROZO_v2(options, 0)
    let queryTaskTilllNext = q_getCardTotalROZO_v2(options, 15)
    let queryFarmerTillToday = q_getTotalFarmerROZO_v2(options, 0)
    let queryFarmerTillNext = q_getTotalFarmerROZO_v2(options, 15)
    let critical = { today: 0, till_next: 0, category: [1000270002, 1000270003] }
    let preventive = { today: 0, till_next: 0, category: 1000270001 }
    let farmers = { today: 0, till_next: 0 }
    let dataTaskToday = await dbConnections().main.manager.query(queryTaskTillToday)
    for (let i = 0; i < dataTaskToday.length; i++) {
      if (dataTaskToday[i].care_category === 'Critical') critical.today = dataTaskToday[i].count
      else if (dataTaskToday[i].care_category === 'Preventive') preventive.today = dataTaskToday[i].count
    }
    let dataTaskNext = await dbConnections().main.manager.query(queryTaskTilllNext)
    for (let i = 0; i < dataTaskNext.length; i++) {
      if (dataTaskNext[i].care_category === 'Critical') critical.till_next = dataTaskNext[i].count
      else if (dataTaskNext[i].care_category === 'Preventive') preventive.till_next = dataTaskNext[i].count
    }
    let dataFarmerToday = await dbConnections().main.manager.query(queryFarmerTillToday)
    let dataFarmerNext = await dbConnections().main.manager.query(queryFarmerTillNext)

    for (let i = 0; i < dataFarmerToday.length; i++) {
      farmers.today = dataFarmerToday[i].today_farmers
    }

    for (let i = 0; i < dataFarmerNext.length; i++) {
      farmers.till_next = dataFarmerNext[i].today_farmers
    }

    return { critical, preventive, farmers }
  }

  static async getROcardsByParavet_v2(options = {}) {
    let query = q_getROCardByGroupByParavet_v2(options)
    let queryFarmer = q_FarmerCountGroupByParavet_v2(options)
    let data = await dbConnections().main.manager.query(query)
    postProcessRecords(undefined, data, { json_columns: ['staff_name_l10n', 'document_information', 'client_document_information'] })
    let dataFarmer = await dbConnections().main.manager.query(queryFarmer)
    postProcessRecords(undefined, dataFarmer, { json_columns: ['staff_name_l10n', 'document_information', 'client_document_information'] })

    let formatedObject = {}
    for (let i = 0; i < data.length; i++) {
      if (!formatedObject[data[i].staff_id]) {
        formatedObject[data[i].staff_id] = {
          staff_name_l10n: data[i].staff_name_l10n,
          staff_id: data[i].staff_id,
          document_id: data[i].document_id,
          document_information: data[i].document_information,
          client_document_information: data[i].client_document_information,
          Preventive: { till_next: 0, today: 0, category: 1000270001 },
          Critical: { till_next: 0, today: 0, category: [1000270002, 1000270003] },
          Farmer: { today: 0, till_next: 0 },
        }
      }
      formatedObject[data[i].staff_id][data[i].care_category][data[i].task_range] = data[i].count
    }
    for (let i = 0; i < dataFarmer.length; i++) {
      if (!formatedObject[dataFarmer[i].staff_id]) {
        formatedObject[dataFarmer[i].staff_id] = {
          staff_name_l10n: dataFarmer[i].staff_name_l10n,
          staff_id: dataFarmer[i].staff_id,
          document_id: dataFarmer[i].document_id,
          document_information: dataFarmer[i].document_information,
          client_document_information: dataFarmer[i].client_document_information,
          Preventive: { till_next: 0, today: 0, category: 1000270001 },
          Critical: { till_next: 0, today: 0, category: [1000270002, 1000270003] },
          Farmer: { today: 0, till_next: 0 },
        }
      }
      formatedObject[dataFarmer[i].staff_id]['Farmer'][dataFarmer[i].category] = dataFarmer[i].today_farmers
    }
    return Object.values(formatedObject)
  }

  static async getZoCardByRo_v2(options = {}) {
    let queryTasks = q_getZOCardByGroupByRO_v2(options)
    let queryFarmers = q_FarmerCountGroupByRo_v2(options)
    let dataTask = await dbConnections().main.manager.query(queryTasks)
    let dataFarmer = await dbConnections().main.manager.query(queryFarmers)
    let formatedObject = {}
    for (let i = 0; i < dataTask.length; i++) {
      if (!formatedObject[dataTask[i].staff_id]) {
        formatedObject[dataTask[i].staff_id] = {
          staff_name_l10n: dataTask[i].staff_name_l10n,
          staff_id: dataTask[i].staff_id,
          document_id: dataTask[i].document_id,
          Preventive: { till_next: 0, today: 0, category: 1000270001 },
          Critical: { till_next: 0, today: 0, category: [1000270002, 1000270003] },
          Farmer: { today: 0, till_next: 0 },
        }
      }
      formatedObject[dataTask[i].staff_id][dataTask[i].care_category][dataTask[i].task_range] = dataTask[i].count
    }
    for (let i = 0; i < dataFarmer.length; i++) {
      if (!formatedObject[dataFarmer[i].staff_id]) {
        formatedObject[dataFarmer[i].staff_id] = {
          staff_name_l10n: dataFarmer[i].staff_name_l10n,
          staff_id: dataFarmer[i].staff_id,
          document_id: dataFarmer[i].document_id,
          Preventive: { till_next: 0, today: 0, category: 1000270001 },
          Critical: { till_next: 0, today: 0, category: [1000270002, 1000270003] },
          Farmer: { today: 0, till_next: 0 },
        }
      }
      formatedObject[dataFarmer[i].staff_id]['Farmer'][dataFarmer[i].category] = dataFarmer[i].today_farmers
    }
    return Object.values(formatedObject)
  }
  static async farmerRoutePlanDetail(options = {}) {
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
    if (!options.customer_id && !options.route_date) return []

    let base_query = `select 
        c.customer_id,
        c.customer_name_l10n,
        c.customer_visual_id,
        rv.village_name_l10n,
        ccl.value_reference_id as village_id,
        cc.care_calendar_id,
        entity_type_id,
        cc.activity_id,
        sr_no.value_string_256 as sr_number, 
        activity_date,
        calendar_activity_status,
        mr.reference_name_l10n as activity_status,
        ra.activity_name_l10n as activity_name,
        cd.document_id,
        cd.client_document_information,
        cd.document_information
        from main.care_calendar as cc
        join main.customer as c on c.customer_id = cc.entity_uuid
        and c.customer_id = '${options.customer_id}'
        and c.active = 1000100001
        join main.customer_classification as ccl on ccl.customer_id = c.customer_id
        and ccl.classifier_id = 2000000055
        and ccl.active = 1000100001
        join main.ref_village as rv on rv.village_id = ccl.value_reference_id
         and rv.active = 1000100001
        join main.ref_reference as mr on mr.reference_id = cc.calendar_activity_status
        and mr.active = 1000100001
        join main.ref_activity as ra on ra.activity_id = cc.activity_id
        and ra.active = 1000100001
        
        left join main.care_calendar_classification sr_no
          on sr_no.care_calendar_id = cc.care_calendar_id and sr_no.classifier_id = ${classifiers.COMPLAINT_NUMBER} and sr_no.active = ${record_statuses.ACTIVE}

          left join (
          select d.entity_1_entity_uuid, d.document_id, d.document_information, d.client_document_information
          from main.document d, (
            select d.entity_1_entity_uuid, ${isPostgres ? ' max(d.document_id::text) ' : ' max(d.document_id) '} max_document_id
            from main.document d, (
              select entity_1_entity_uuid, max(created_at) created_at
              from main.document d
              where d.entity_1_type_id = 1000220001
              and d.document_type_id = 1000260003
              and d.active = 1000100001
              group by entity_1_entity_uuid
            ) mcad
            where d.document_type_id = 1000260003 and d.active = 1000100001 
            and d.entity_1_entity_uuid = mcad.entity_1_entity_uuid and d.created_at = mcad.created_at
            group by d.entity_1_entity_uuid
          ) mdid
          where d.document_id = ${isPostgres ? ' mdid.max_document_id::uuid ' : ' mdid.max_document_id '}
        ) cd on cd.entity_1_entity_uuid = c.customer_id
        where entity_type_id = 1000460001
        ${isPostgres ? 'and cc.activity_date::DATE' : " and date(cc.activity_date, 'start of day')"}
        = '${options.route_date}'
        `
    let data = await dbConnections().main.manager.query(base_query, [])
    postProcessRecords(undefined, data, { json_columns: ['customer_name_l10n', 'village_name_l10n', 'activity_status', 'activity_name', 'client_document_information', 'document_information'] })
    return data
  }

  static async getActivityStats(options = {}) {

    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
    const { user_id, user_type } = options

    const all_stats = 1;
    let stats_by_user;
    if (user_type !== BACK_OFFICE_PERSON) {
      stats_by_user = true;
    }

    const number_of_days = 15;

    const queryParameters = { ...options, user_id, user_type }
    const templateParameters = {
      ...options,
      user_id,
      user_type,
      all_stats,
      stats_by_user,
      number_of_days,
      start_date: moment().format('YYYY-MM-DD'),
      end_date: moment().add(15, 'days').format('YYYY-MM-DD'),
      IS_POSTGRES: isPostgres
    }

    let activityStatsQuery = getCompiledQuery('activity-stats', templateParameters);
    const activityStatsQueryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${activityStatsQuery})`, 'activity_stats').setParameters(queryParameters);
    const activityStatsResult = await activityStatsQueryBuilder.execute();

    let Critical = { today: 0, till_next: 0, category: [1000270002, 1000270003] }
    let Preventive = { today: 0, till_next: 0, category: 1000270001 }
    let Farmer = { today: 0, till_next: 0 }

    if (activityStatsResult.length > 0) {

      Critical.today = activityStatsResult[0]?.critical_count ?? 0
      Critical.till_next = activityStatsResult[0]?.critical_count_15 ?? 0

      Preventive.today = activityStatsResult[0]?.preventive_count ?? 0
      Preventive.till_next = activityStatsResult[0]?.preventive_count_15 ?? 0

      Farmer.today = activityStatsResult[0]?.farmer_count ?? 0
      Farmer.till_next = activityStatsResult[0]?.farmer_count_15 ?? 0
    }
    return { Critical, Preventive, Farmer }
  }

  static async getActivityStatsByStaffType(options = {}) {

    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
    const { user_id, user_type, staff_type } = options

    const stats_by_staff_type = 1;
    let stats_by_user;
    if (user_type !== BACK_OFFICE_PERSON) {
      stats_by_user = true;
    }

    const number_of_days = 15;

    const queryParameters = { ...options, user_id, user_type, staff_type }
    const templateParameters = {
      ...options,
      user_id,
      user_type,
      stats_by_staff_type,
      stats_by_user,
      number_of_days,
      start_date: moment().format('YYYY-MM-DD'),
      end_date: moment().add(15, 'days').format('YYYY-MM-DD'),
      IS_POSTGRES: isPostgres
    }

    let activityStatsQuery = getCompiledQuery('activity-stats', templateParameters);
    const activityStatsQueryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${activityStatsQuery})`, 'activity_stats').setParameters(queryParameters);
    const activityStatsResult = await activityStatsQueryBuilder.execute();

    postProcessRecords(undefined, activityStatsResult, { json_columns: ['staff_name_l10n', 'document_information', 'client_document_information'] })

    let activityStatsResponse = [];
    for (let i = 0; i < activityStatsResult.length; i++) {
      let responseItem = {};
      responseItem.staff_id = activityStatsResult[i].staff_id;
      responseItem.staff_name_l10n = activityStatsResult[i].staff_name_l10n;
      responseItem.document_id = activityStatsResult[i].document_id;
      responseItem.document_information = activityStatsResult[i].document_information;
      responseItem.client_document_information = activityStatsResult[i].client_document_information;
      responseItem.Preventive = { till_next: activityStatsResult[i]?.preventive_count_15 ?? 0, today: activityStatsResult[i]?.preventive_count ?? 0, category: 1000270001 };
      responseItem.Critical = { till_next: activityStatsResult[i]?.critical_count_15 ?? 0, today: activityStatsResult[i]?.critical_count ?? 0, category: [1000270002, 1000270003] };
      responseItem.Farmer = { till_next: activityStatsResult[i]?.farmer_count_15 ?? 0, today: activityStatsResult[i]?.farmer_count ?? 0  };

      activityStatsResponse.push(responseItem);
    }

    return activityStatsResponse;
  }
}

class CareCalendarActivityQueries {
  
  static async getObservationV3(calendar_id) {
    
    if (!calendar_id) return []
    
    let obs_query = 
    `
    SELECT
      care_calendar_classification.value_reference_id as obs_id,
      care_calendar_classification.value_json as obs_val,
      CASE 
        WHEN care_calendar_classification.value_reference_id IN (1010000029,1030000029) THEN 'Weight'
        WHEN care_calendar_classification.value_reference_id IN (1010000004,1030000004) THEN 'Temperature'
      END AS obs_name
    FROM
      main.care_calendar_classification      
    WHERE
      care_calendar_classification.care_calendar_id = '${calendar_id}'
      AND
      care_calendar_classification.classifier_id = 2000000277    
      AND
      care_calendar_classification.value_reference_id in (1010000004, 1010000029 ,1030000029, 1030000004)
      AND
      care_calendar_classification.active = 1000100001    
    `
    let observationData = await dbConnections().main.manager.query(obs_query, [])

    let observationResponse = [];

    postProcessRecords(undefined, observationData, { json_columns: ['obs_val'] })

    for (const observation of observationData) {
      let answer = observation?.obs_val?.answer || observation?.obs_val?.data?.answers
      if ((Array.isArray(answer))
        && answer.length) {

        const observationItem = { ...observation };
        observationItem['obs_val'] = answer[0];
        observationResponse.push(observationItem);
      }
    }

    return observationResponse
  }

  static async getObservation(calendar_id) {
    if (!calendar_id) return []
    //add case if future , we use other than value_reference_id
    let obs_query = `select  care_calendar_classification.value_reference_id as obs_id,
    care_calendar_classification.value_double as obs_val,
    ref_reference.reference_name as obs_name   
    from    main.care_calendar_classification      
    left join main.ref_reference       
    on care_calendar_classification.value_reference_id = ref_reference.reference_id
    where  care_calendar_classification.care_calendar_id = '${calendar_id}'   
    and    care_calendar_classification.classifier_id = 2000000182    
    and    care_calendar_classification.active = 1000100001  
    `
    let obs = await dbConnections().main.manager.query(obs_query, [])
    return obs
  }
  static async getDiagnosis(calendar_id) {
    if (!calendar_id) return []
    //add case if future , we use other than value_reference_id
    let diag_query = `select     care_calendar_classification.value_reference_id as diag_id,
    ref_diagnosis.name as diag_name   
    from    main.care_calendar_classification      
    left join main.ref_diagnosis      
    on care_calendar_classification.value_reference_id = ref_diagnosis.reference_id
    where  care_calendar_classification.care_calendar_id = '${calendar_id}'   
    and    care_calendar_classification.classifier_id = 2000000184    
    and    care_calendar_classification.active = 1000100001 
    `;
    let response_to_treatment_query = `select  care_calendar_classification.value_reference_id as rtt_id,
    ref_reference.reference_name as rtt_name   
    from    main.care_calendar_classification      
    left join main.ref_reference       
    on care_calendar_classification.value_reference_id = ref_reference.reference_id
    where  care_calendar_classification.care_calendar_id = '${calendar_id}'   
    and    care_calendar_classification.classifier_id = 2000000183    
    and    care_calendar_classification.active = 1000100001`
    let diagnosis = await dbConnections().main.manager.query(diag_query, [])
    let response_to_treatment = await dbConnections().main.manager.query(response_to_treatment_query, [])

    return { diagnosis, response_to_treatment }
  }
  static async getPrescription(calendar_id) {
    if (!calendar_id) return []
    //add case if future , we use other than value_reference_id
    let prescribed_info_query = `
      SELECT
        care_calendar_classification.value_reference_id as prescription_id,
        care_calendar_classification.care_calendar_classification_id,
        care_calendar_classification.value_double as prescription_value,
        care_calendar_classification.value_json as prescription_details,
        ref_medicine.medicine_name as prescription_name
      FROM
        main.care_calendar_classification      
          left join main.ref_medicine       
            on care_calendar_classification.value_reference_id = ref_medicine.medicine_id
      WHERE  
        care_calendar_classification.care_calendar_id = '${calendar_id}'   
        AND    
        care_calendar_classification.classifier_id = 2000000185    
        AND    
        care_calendar_classification.active = 1000100001
    `;

    let prescription = await dbConnections().main.manager.query(prescribed_info_query, [])

    postProcessRecords(undefined, prescription, { json_columns: ['prescription_details'] })

    const prescribedMedicines = await loadMeds(prescription.map(item => item.prescription_id));

    let routeQuery = `
      SELECT 
        ref_reference.reference_id, 
        ref_reference.reference_name
      FROM 
        main.ref_reference
      WHERE 
        ref_reference.reference_category_id = 10090000 
        AND 
        ref_reference.active = 1000100001
    `;
    let route = await dbConnections().main.manager.query(routeQuery, []);

    const routeReferenceMap = new Map(route.map(option => [option.reference_id, option.reference_name]));

    let total_price = 0;
    const prescriptionsWithRouteNames = prescription.map(presc => {

      if (!presc.prescription_details) return presc;
      
      const unit= presc.prescription_details.unit ? presc.prescription_details.unit : '';
      const status= presc.prescription_details.status ? presc.prescription_details.status : '';
      const frequency= presc.prescription_details.frequency ? presc.prescription_details.frequency : '';
      const days = presc.prescription_details.days ? presc.prescription_details.days : 1;
      const existMrp = parseInt(presc.prescription_details.mrp) >=0 ? presc.prescription_details.mrp : 0;
      
      let mrp = existMrp
      let frequency_count = 1;
      let route_name = presc.prescription_details.route ? routeReferenceMap.get(presc.prescription_details.route) : '';
      
      if (frequency === 'BID') {
        frequency_count = 2
      } else if (frequency === 'TID') {
        frequency_count = 3
      }

      const prescribedMedicinesClassification = prescribedMedicines[presc.prescription_id];

      if (prescribedMedicinesClassification) {
        let actual_mrp = prescribedMedicinesClassification.medicine_mrp ? prescribedMedicinesClassification.medicine_mrp : 0;
        if(existMrp) {
          actual_mrp =existMrp
        }
        const pack_size = prescribedMedicinesClassification.medicine_pack_size ? prescribedMedicinesClassification.medicine_pack_size : 0;
        const quantity = presc.prescription_value ? presc.prescription_value : 0;

        if (pack_size != 0) {
          mrp = (actual_mrp / pack_size) * quantity;
        }
        mrp = mrp * days * frequency_count
      }

      total_price = total_price + mrp;

        return {
          ...presc,
          prescription_details: {
            unit,
            status,
            frequency,
            route_name: route_name,
            mrp,
            days
          },
        };
      });
    
      let checkFreelanceTaskQuery =
      ` 
        select 
          sr_type.value_reference_id 
        FROM 
          main.care_calendar_classification as sr_type 
        WHERE  
          sr_type.care_calendar_id = '${calendar_id}' 
          AND 
          sr_type.classifier_id = 2000000243 
          AND 
          sr_type.active = ${ACTIVE}
      `;
  
      let checkFreelanceTask = await dbConnections().main.manager.query(checkFreelanceTaskQuery,[])

      let vetConsultationFee = checkFreelanceTask.length ? 0 : await this.getVetConsultationFee(calendar_id) ;

      total_price = parseInt(total_price) + parseInt(vetConsultationFee); 
    
      return {prescriptionsWithRouteNames, total_price: parseInt(total_price), vet_consultant_cost: vetConsultationFee};
  }
  static async notes(calendar_id, note_type) {
    let notes_query = `select note.note from main.note where entity_1_uuid= '${calendar_id}' and active = 1000100001 and note_type_id= ${note_type} order by created_at desc limit 1`
    let notes = await dbConnections().main.manager.query(notes_query, [])
    notes = postProcessRecords(undefined, notes, { json_columns: ['note'] })
    return notes
  }

  static async getVetConsultationFee(care_calendar_id) {
    let priceQuery =`select value_double as price from main.care_calendar_classification
    where classifier_id=2000000221 
    and active=1000100001
    and care_calendar_id='${care_calendar_id}' limit 1`
    let price = await dbConnections().main.manager.query(priceQuery,[])
    price = postProcessRecords(undefined ,price[0] , {})
    return price ? price.price : 0;
  }
}

module.exports = { CareCalendarQueries, CareCalendarActivityQueries }
