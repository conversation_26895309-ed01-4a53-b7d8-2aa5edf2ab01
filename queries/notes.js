const { configurationJSON } = require("@krushal-it/common-core");
const { dbConnections, preProcessRecords, postProcessRecords } = require("@krushal-it/ah-orm");
const { RELATION_NOTE } = require("../utils/constant");
const {infoLog} = require('@krushal-it/mobile-or-server-lib')

const insertNote = async (values, options = {}) => {
  const isPostgres = (configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1) ? true : false
  console.log('bel q n iN 1, values = ', values)
  let manager = options.manager ? options.manager : dbConnections().main.manager;
  const entity_note = dbConnections().main.entities[RELATION_NOTE];
  const preProcessedValues = preProcessRecords(entity_note, values, entity_note.additionalAttributes);
  console.log('bel q n iN 1a, preProcessedValues = ', preProcessedValues)
  infoLog(preProcessedValues);
  console.log('bel q n iN 2')
  let insertedValueQB = manager
      .createQueryBuilder()
      .insert()
      .into(RELATION_NOTE)
      .values(preProcessedValues)
  if (isPostgres) {
    insertedValueQB = insertedValueQB.returning("note_id")
  }
  let insertedValueReturn = await insertedValueQB.execute();
  console.log('bel q n iN 3, insertedValueReturn = ', insertedValueReturn)
  
  if (isPostgres) {
    return insertedValueReturn
  } else {
    if (insertedValueReturn.identifiers) {
      insertedValueReturn = insertedValueReturn.identifiers[0].note_id
    }
    return insertedValueReturn
  }
};

const getNote = async (options = {}) => {
  const { note_type_id, entity_1_type_id, entity_1_uuid } = options;
  const manager = options.manager ? options.manager : dbConnections().main.manager;
  let base_query_parent = `
    WITH child_tasks
    as (
    SELECT care_calendar_id FROM
      main.care_calendar_classification
      WHERE value_reference_uuid = '${entity_1_uuid}'
      AND classifier_id = 2000000126
    )
    SELECT 
    n.note_id,  
    n.created_at, 
    n.note_type_id, 
    n.note_time, n.entity_1_type_id, n.creator_type_id,
    n.note, n.creator_uuid, n.entity_1_uuid, s.staff_name_l10n as creator_name,
      nt_rr.reference_name as note_type, nt_rr.reference_name_l10n as note_type_json,
      net_rr.reference_name as entity_1_type_name, net_rr.reference_name_l10n as entity_1_type_name_json
    from main.note n
    inner join main.ref_reference nt_rr on n.note_type_id = nt_rr.reference_id
    inner join main.ref_reference net_rr on n.note_type_id = net_rr.reference_id
    left join main.staff s on n.creator_uuid = s.staff_id
    WHERE (n.entity_1_uuid IN (
      SELECT care_calendar_id FROM child_tasks
    ) OR n.entity_1_uuid = '${entity_1_uuid}')
    AND n.note_type_id = ${note_type_id} AND n.entity_1_type_id = ${entity_1_type_id} 
    ORDER BY n.created_at DESC`;
  let parentNote = await manager.query(base_query_parent)
  postProcessRecords(undefined, parentNote, { json_columns: ['note', 'creator_name', 'note_type_json', 'entity_1_type_name_json'] });
  return parentNote

};



module.exports = { insertNote, getNote};
