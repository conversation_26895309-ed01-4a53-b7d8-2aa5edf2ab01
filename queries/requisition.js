const { RELATION_REQUISITION,R<PERSON><PERSON>ION_REQUISITION_CLASSIFCATION,REL<PERSON>ION_REQUISITION_LINE_ITEMS,SCHEMA_MAIN, ACTIVE,
    REQUISITION_PENDING,REQUISITION_APPROVED_BY_APPROVER,R<PERSON><PERSON>ION_REFERENCE,RELATION_ENTITY_STATUS,
    CONST_PRESCRTIPTION_PROCESS_STATUS,CONST_PRESCRTIPTION_MEDICINE_DELIVERY_STATUS,CONST_PRESCRTIPTION_INVOICE_STATUS} = require('../utils/constant')
const { errorLog } = require('@krushal-it/mobile-or-server-lib')
const { dbConnections, preProcessRecords } = require('@krushal-it/ah-orm')
const { getDocumentJSONConfig } = require('../utils/document')
const { validateUUID } = require('@krushal-it/mobile-or-server-lib')
const { configurationJSON } = require('@krushal-it/common-core')
const ENUMS = require('../ENUMS')
// const { validate } = require('uuid');
const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0;

const insertRequisition = async (requisition,transaction) => {
//   return new Promise(async (resolve,reject)=>{
        try{
        console.log('insertrequisition==>',requisition)
        requisition['inserted_at'] = new Date().toISOString()
        const entity_requisition = dbConnections().main.entities[RELATION_REQUISITION]
        const preProcessedRequisition = preProcessRecords(entity_requisition, requisition, entity_requisition.additionalAttributes)
        console.log('requsition===>1',preProcessedRequisition)
        let manager = transaction ? transaction : dbConnections().main.manager
        let data = await manager
            .createQueryBuilder()
            .insert()
            .into(RELATION_REQUISITION)
            .values(preProcessedRequisition)
            .execute()
        console.log('requisition query===>2',data)
        if(!isPostgres) return data.identifiers[0].requisition_id
        return (JSON.parse(JSON.stringify(data))).raw[0].requisition_id
        // resolve((JSON.parse(JSON.stringify(data))).raw[0].requisition_id)
    }
    catch(e){
        // reject(e)
        throw e
    }
//   })
}

const updateRequisition = async (value, options = {},transaction) => {
    // return new Promise(async (resolve,reject)=>{
        try{
        console.log('bel q c uR 1, value = ', value, ', options = ', options)
        if (!options.requisition_id) throw new Error('requisition_id is required')
        let manager = transaction ? transaction : dbConnections().main.manager
        const entity = dbConnections().main.entities[RELATION_REQUISITION]
        console.log('bel q c uR 2, entity.additionalAttributes = ', entity.additionalAttributes)
        const preProcessedValue = preProcessRecords(entity, value, { ...entity.additionalAttributes, id: options.requisition_id })
        console.log('bel q c uR 3, preProcessedValue = ', preProcessedValue)
        await manager.createQueryBuilder().update(RELATION_REQUISITION).set(preProcessedValue).where(`${RELATION_REQUISITION}_id = :id`, { id: options.requisition_id }).execute()
        // resolve(true)
        return true
    }
    catch(e){
            // reject(e)
            throw e
        }
    // })
}

const insertRequisitionClassification = async (requisition_classification,transaction) => {
    //   return new Promise(async (resolve,reject)=>{
            try{
            console.log('insertrequisition==>',requisition_classification)
            const entity_requisition_classification = dbConnections().main.entities[RELATION_REQUISITION_CLASSIFCATION]
            const preProcessedRequisitionClassification = preProcessRecords(entity_requisition_classification, requisition_classification, entity_requisition_classification.additionalAttributes)
            console.log('requsition===>1',preProcessedRequisitionClassification)
            let manager = transaction ? transaction : dbConnections().main.manager
            let data = await manager
                .createQueryBuilder()
                .insert()
                .into(RELATION_REQUISITION_CLASSIFCATION)
                .values(preProcessedRequisitionClassification)
                .execute()
            console.log('requisition query===>2',data)
            if(!isPostgres) return data.identifiers[0].requisition_classification_id
            return (JSON.parse(JSON.stringify(data))).raw[0].requisition_classification_id
            // resolve((JSON.parse(JSON.stringify(data))).raw[0].requisition_id)
        }
        catch(e){
            // reject(e)
            throw e
        }
    //   })
}
    
const updateRequisitionClassification = async (value, options = {},transaction) => {
    // return new Promise(async (resolve,reject)=>{
        try{
        console.log('bel q c uR 1, value = ', value, ', options = ', options)
        if (!options.requisition_classification_id) throw new Error('requisition_classification_id is required')
        let manager = transaction ? transaction : dbConnections().main.manager
        const reqclassificationentity = dbConnections().main.entities[RELATION_REQUISITION_CLASSIFCATION]
        console.log('bel q c uR 2, reqclassificationentity.additionalAttributes = ', reqclassificationentity.additionalAttributes)
        const preProcessedValue = preProcessRecords(reqclassificationentity, value, { ...reqclassificationentity.additionalAttributes, id: options.requisition_classification_id })
        console.log('bel q c uR 3, preProcessedValue = ', preProcessedValue)
        await manager.createQueryBuilder().update(RELATION_REQUISITION_CLASSIFCATION).set(preProcessedValue).where(`${RELATION_REQUISITION_CLASSIFCATION}_id = :id`, { id: options.requisition_id }).execute()
        // resolve(true)
        return true
    }
    catch(e){
            // reject(e)
            throw e
        }
    // })
}

const getClassificationByRequisition = async(uuid)=>{
    // return new Promise(async (resolve,reject)=>{
        try{
        let reqclassificationquery = `SELECT * FROM main.${RELATION_REQUISITION_CLASSIFCATION} where requisition_id='${uuid}' AND active=${ACTIVE}`
        let requisitionclassification = await dbConnections().main.manager.query(reqclassificationquery)
        // resolve(requisitionclassification)
        return requisitionclassification
        }
        catch(e){
            // reject(e)
            throw e
        }
    // })
}

const insertRequisitionLineItems = async (requisition_line_items,transaction) => {
    // return new Promise(async (resolve,reject)=>{
        try{
            console.log('insertRequisitionLineItems',requisition_line_items)
            const entity_requisition_line_items = dbConnections().main.entities[RELATION_REQUISITION_LINE_ITEMS]
            const preProcessedRequisitionLineItems = preProcessRecords(entity_requisition_line_items, requisition_line_items, entity_requisition_line_items.additionalAttributes)
            console.log('req line items===>3',preProcessedRequisitionLineItems)
            let manager = transaction ? transaction : dbConnections().main.manager
            let data = await manager
                .createQueryBuilder()
                .insert()
                .into(RELATION_REQUISITION_LINE_ITEMS)
                .values(preProcessedRequisitionLineItems)
                .execute()
            console.log('after insert reqline items==>4',data)
            if(!isPostgres) return data.identifiers[0].requisition_line_items_id
            return (JSON.parse(JSON.stringify(data))).raw[0].requisition_line_items_id
            // resolve((JSON.parse(JSON.stringify(data))).raw[0].requisition_line_items_id)
        }
        catch(e){
            // reject(e)
            throw e
        }
    // })
}

const updateRequisitionLineItems = async (value, options = {},transaction) => {
    // return new Promise(async (resolve,reject)=>{
        try{
            console.log('bel q c uRLI 1, value = ', value, ', options = ', options)
            if (!options.requisition_line_items_id) throw new Error('requisition_line_items_id is required')
            let manager = transaction ? transaction : dbConnections().main.manager
            const lineitemsentity = dbConnections().main.entities[RELATION_REQUISITION_LINE_ITEMS]
            console.log('bel q c uRLI 2, lineitemsentity.additionalAttributes = ', lineitemsentity.additionalAttributes)
            const preProcessedRequisitionLineItemsValue = preProcessRecords(lineitemsentity, value, { ...lineitemsentity.additionalAttributes, id: options.requisition_line_items_id })
            console.log('bel q c uRLI 3, preProcessedValue = ', preProcessedRequisitionLineItemsValue)
            await manager.createQueryBuilder().update(RELATION_REQUISITION_LINE_ITEMS).set(preProcessedRequisitionLineItemsValue).where(`${RELATION_REQUISITION_LINE_ITEMS}_id = :id`, { id: options.requisition_line_items_id }).execute()
            // resolve(true)
            return true
        }
        catch(e){
            // reject(e)
            throw e
        }
    // })
}

const getLineItemsByRequisition = async(uuid)=>{
    // return new Promise(async (resolve,reject)=>{
        try{
        // const requisitionlineitems = await dbConnections().main.repos[RELATION_REQUISITION_LINE_ITEMS].createQueryBuilder().where(`${RELATION_REQUISITION_LINE_ITEMS}.requisition_id = :uuid`, { uuid }).andWhere(`${RELATION_REQUISITION_LINE_ITEMS}.active = :act`, { act:ACTIVE }).execute()
        let reqlineitemquery = `SELECT * FROM main.${RELATION_REQUISITION_LINE_ITEMS} where requisition_id='${uuid}' AND active=${ACTIVE}`
        let requisitionlineitems = await dbConnections().main.manager.query(reqlineitemquery)
        // resolve(requisitionlineitems)
        return requisitionlineitems
        }
        catch(e){
            // reject(e)
            throw e
        }
    // })
}

const getRequisitionLineItemsForRequisitioner = async(uuid)=>{
    // return new Promise(async (resolve,reject)=>{
        try{
            let base_query=`
            SELECT 
                rli.requisitioner_uuid,
                rli.medicine_id,
                rli.unit,
                rf1.reference_name_l10n AS medicine_name_l10n, 
                SUM(rli.quantity) AS quantity
            FROM main.${RELATION_REQUISITION_LINE_ITEMS} rli
                LEFT JOIN main.${RELATION_REFERENCE} rf1 ON rf1.reference_id = rli.medicine_id
            WHERE item_status_id=${REQUISITION_PENDING} OR item_status_id=${REQUISITION_APPROVED_BY_APPROVER} AND active=${ACTIVE}`
            let lineitems = await dbConnections().main.manager.query(base_query)
            // resolve(lineitems)
            return lineitems
        }
        catch(e){
            // reject(e)
            throw e
        }
    // })
}

const getAllRequisition = async(data,transaction)=>{
    // return new Promise(async (resolve,reject)=>{
        try{
            let dt = isPostgres ? "NOW()" : "CURRENT_TIMESTAMP"
            let base_query=`
            SELECT  r.requisition_id,
                r.requisitioner_type_id,
                rf2.reference_name_l10n AS requisitioner_type_l10n,
                r.requisitioner_uuid,
                s1.staff_name_l10n as requisitioner_name_l10n,
                r.approver_type_id,
                rf3.reference_name_l10n AS approver_type_l10n,
                r.approver_uuid,
                s2.staff_name_l10n as approver_name_l10n,
                r.fullfiller_type_id,
                rf4.reference_name_l10n AS fullfiller_type_l10n,
                r.fullfiller_uuid,
                COALESCE(p.partner_name_l10n,s3.staff_name_l10n) AS fullfiller_name_l10n,
                r.line_items_count,
                r.status_id,
                rf1.reference_name_l10n AS status_l10n,
                r.created_at,
                r.inserted_at,
                r.updated_at,
                rl.requisition_line_items_id,
                rl.medicine_id,
                rl.unit,
                rl.quantity,
                rl.item_status_id,
                rf5.reference_name_l10n AS item_status_l10n,
                pps.status_id AS prescription_process_status_id,
                rf6.reference_name_l10n AS prescription_process_status_l10n,
                pmds.status_id AS prescription_medicine_delivery_status_id,
                rf7.reference_name_l10n AS prescription_medicine_delivery_status_l10n,
                pis.status_id AS prescription_invoice_status_id,
                rf8.reference_name_l10n AS prescription_invoice_status_l10n
            FROM main.requisition AS r
                LEFT JOIN main.requisition_line_items AS rl ON rl.requisition_id = r.requisition_id AND rl.active = ${ACTIVE}
                LEFT JOIN main.ref_reference AS rf1 ON rf1.reference_id = r.status_id
                LEFT JOIN main.ref_reference AS rf2 ON r.requisitioner_type_id = rf2.reference_id
                LEFT JOIN main.staff AS s1 ON r.requisitioner_uuid = s1.staff_id
                LEFT JOIN main.ref_reference AS rf3 ON r.approver_type_id = rf3.reference_id
                LEFT JOIN main.staff AS s2 ON r.approver_uuid = s2.staff_id
                LEFT JOIN main.ref_reference AS rf4 ON r.fullfiller_type_id = rf4.reference_id
                LEFT JOIN main.partner AS p ON r.fullfiller_uuid = p.partner_id
                LEFT JOIN main.staff AS s3 ON r.fullfiller_uuid = s3.staff_id
                LEFT JOIN main.ref_reference AS rf5 ON rf5.reference_id = rl.item_status_id
                LEFT JOIN 
                (
                    SELECT status_id,entity_1_entity_uuid
                    FROM main.entity_status
                    WHERE status_category_id=${CONST_PRESCRTIPTION_PROCESS_STATUS} AND start_date<=${dt} AND end_date>=${dt}
                ) AS pps on pps.entity_1_entity_uuid=r.requisition_id
                LEFT JOIN main.ref_reference AS rf6 ON rf6.reference_id = pps.status_id
                LEFT JOIN 
                (
                    SELECT status_id,entity_1_entity_uuid
                    FROM main.entity_status
                    WHERE status_category_id=${CONST_PRESCRTIPTION_MEDICINE_DELIVERY_STATUS} AND start_date<=${dt} AND end_date>=${dt}
                ) AS pmds on pmds.entity_1_entity_uuid=r.requisition_id
                LEFT JOIN main.ref_reference AS rf7 ON rf7.reference_id = pmds.status_id
                LEFT JOIN 
                (
                    SELECT status_id,entity_1_entity_uuid
                    FROM main.entity_status
                    WHERE status_category_id=${CONST_PRESCRTIPTION_INVOICE_STATUS} AND start_date<=${dt} AND end_date>=${dt}
                ) AS pis on pis.entity_1_entity_uuid=r.requisition_id
                LEFT JOIN main.ref_reference AS rf8 ON rf8.reference_id = pis.status_id
            WHERE
                r.active = ${ACTIVE} `
                if(data.r_id) base_query+=` AND r.requisition_id = '${data.r_id}' `
                if(data.r_requisitioner && data.r_requisitioner.length>0) base_query+=` AND r.requisitioner_uuid IN ('${data.r_requisitioner}') `           
                if(data.r_status && data.r_status.length>0) base_query+=` AND r.status_id IN (${data.r_status}) `           
                if(data.r_fullfiller && data.r_fullfiller.length>0) base_query+=` AND r.fullfiller_uuid IN ('${data.r_fullfiller}') `          
                if(data.r_approver && data.r_approver.length>0) base_query+=` AND r.approver_uuid IN ('${data.r_approver}') `
                if(data.r_requisitioner_type && data.r_requisitioner_type.length>0) base_query+=` AND r.requisitioner_type_id IN (${data.r_requisitioner_type}) ` 
                if(data.r_approver_type && data.r_approver_type.length>0) base_query+=` AND r.approver_type_id IN (${data.r_approver_type}) `
                if(data.r_fullfiller_type && data.r_fullfiller_type.length>0) base_query+=` AND r.fullfiller_type_id IN (${data.r_fullfiller_type}) `
                if(data.r_created_at_from) base_query+=` AND DATE(r.created_at) >='${data.r_created_at_from}' `
                if(data.r_created_at_to) base_query+=` AND DATE(r.created_at) <='${data.r_created_at_to}' `
                if(data.r_inserted_at_from) base_query+=` AND DATE(r.inserted_at) >='${data.r_inserted_at_from}' `
                if(data.r_inserted_at_to) base_query+=` AND DATE(r.inserted_at) <='${data.r_inserted_at_to}' `
                if(data.r_updated_at_from) base_query+=` AND DATE(r.updated_at) >='${data.r_updated_at_from}' `
                if(data.r_updated_at_to) base_query+=` AND DATE(r.updated_at) <='${data.r_updated_at_to}' `
                if(isPostgres && data.r_requisitioner_name) base_query+=` AND 
                (   UPPER(s1.staff_name_l10n->>'ul') LIKE '%${data.r_requisitioner_name.toUpperCase()}%' OR 
                    UPPER(s1.staff_name_l10n->>'mr') LIKE '%${data.r_requisitioner_name.toUpperCase()}%' OR
                    UPPER(s1.staff_name_l10n->>'en') LIKE '%${data.r_requisitioner_name.toUpperCase()}%' OR 
                    UPPER(s1.staff_name_l10n->>'hi') LIKE '%${data.r_requisitioner_name.toUpperCase()}%'
                ) `
                if(!isPostgres && data.r_requisitioner_name) base_query+=` AND UPPER(s1.staff_name) LIKE '%${data.r_requisitioner_name.toUpperCase()}%' ` 
                if(isPostgres && data.r_approver_name) base_query+=` AND 
                (   UPPER(s2.staff_name_l10n->>'ul') LIKE '%${data.r_approver_name.toUpperCase()}%' OR 
                    UPPER(s2.staff_name_l10n->>'mr') LIKE '%${data.r_approver_name.toUpperCase()}%' OR
                    UPPER(s2.staff_name_l10n->>'en') LIKE '%${data.r_approver_name.toUpperCase()}%' OR 
                    UPPER(s2.staff_name_l10n->>'hi') LIKE '%${data.r_approver_name.toUpperCase()}%'
                ) `
                if(!isPostgres && data.r_approver_name) base_query+=` AND UPPER(s2.staff_name_l10n) LIKE '%${data.r_approver_name.toUpperCase()}%' ` 
                if(isPostgres && data.r_fullfiller_name) base_query+=` AND 
                (   UPPER(p.partner_name_l10n->>'ul') LIKE '%${data.r_fullfiller_name.toUpperCase()}%' OR 
                    UPPER(p.partner_name_l10n->>'mr') LIKE '%${data.r_fullfiller_name.toUpperCase()}%' OR
                    UPPER(p.partner_name_l10n->>'en') LIKE '%${data.r_fullfiller_name.toUpperCase()}%' OR 
                    UPPER(p.partner_name_l10n->>'hi') LIKE '%${data.r_fullfiller_name.toUpperCase()}%'
                ) OR
                (   UPPER(s3.staff_name_l10n->>'ul') LIKE '%${data.r_fullfiller_name.toUpperCase()}%' OR 
                    UPPER(s3.staff_name_l10n->>'mr') LIKE '%${data.r_fullfiller_name.toUpperCase()}%' OR
                    UPPER(s3.staff_name_l10n->>'en') LIKE '%${data.r_fullfiller_name.toUpperCase()}%' OR 
                    UPPER(s3.staff_name_l10n->>'hi') LIKE '%${data.r_fullfiller_name.toUpperCase()}%'
                ) `
                if(!isPostgres && data.r_fullfiller_name) base_query+=` AND UPPER(s3.staff_name_l10n) LIKE '%${data.r_fullfiller_name.toUpperCase()}%' `  
                if(data.r_pps_id && data.r_pps_id.length>0) base_query+=` AND pps.status_id IN (${data.r_pps_id}) `
                if(data.r_pmds_id && data.r_pmds_id.length>0) base_query+=` AND pmds.status_id IN (${data.r_pmds_id}) `  
                if(data.r_pis_id && data.r_pis_id.length>0) base_query+=` AND pis.status_id IN (${data.r_pis_id}) `      
            base_query+=` ORDER BY r.updated_at DESC `
            let manager = transaction ? transaction : dbConnections().main.manager
            let requisitionlist = await manager.query(base_query)
            // resolve(requisitionlist)
            return requisitionlist
        }
        catch(e){
            // reject(e)
            throw e
        }
    // })
}

const insertRequisitionEntityStatus = async(entity_status,transaction)=>{
    // return new Promise(async (resolve,reject)=>{
        try{
            console.log('insert_requisition_entity_status==>',entity_status)
            const entity_requisition_status = dbConnections().main.entities[RELATION_ENTITY_STATUS]
            console.log('entity_requisition_status',entity_requisition_status.additionalAttributes)
            const preProcessedRequisitionEntityStatus = preProcessRecords(entity_requisition_status, entity_status, entity_requisition_status.additionalAttributes)
            console.log('requsition_entity_status===>1',preProcessedRequisitionEntityStatus)
            let manager = transaction ? transaction : dbConnections().main.manager
            let data = await manager
                .createQueryBuilder()
                .insert()
                .into(RELATION_ENTITY_STATUS)
                .values(preProcessedRequisitionEntityStatus)
                .execute()
            console.log('entity_status query===>2',data)
            // resolve((JSON.parse(JSON.stringify(data))).raw[0].entity_status_uuid)
            if(!isPostgres) return data.identifiers[0].entity_status_uuid
            return (JSON.parse(JSON.stringify(data))).raw[0].entity_status_uuid
        }
        catch(e){
            // reject(e)
            throw e
        }
    // })
}

const updateEntityStatusToAdjustEndTimeForRequisitionStatus= async(estatus,transaction)=>{
    // return new Promise(async (resolve,reject)=>{
        try{
        let base_query_old= `
        WITH ordered_rows AS (
            SELECT
              start_date,
			  entity_status_uuid,
			  entity_1_entity_uuid,
              ROW_NUMBER() OVER (PARTITION BY entity_1_entity_uuid ORDER BY status_id DESC) AS row_num
            FROM main.entity_status
            WHERE status_category_id=${estatus.status_category_id} and entity_1_entity_uuid='${estatus.entity_1_entity_uuid}'
          )
          UPDATE main.entity_status es
          SET end_date = (
            SELECT start_date
            FROM ordered_rows
            WHERE row_num = 1
          )
          WHERE EXISTS (
            SELECT o.entity_1_entity_uuid
            FROM ordered_rows o
            WHERE o.row_num = 2 and es.entity_status_uuid=o.entity_status_uuid
          )`
          let current_date= new Date() 

          let base_query = `
          UPDATE main.entity_status 
            SET end_date = '${current_date.toISOString()}'
            WHERE entity_status_uuid = (select entity_status_uuid from main.entity_status where status_category_id=${estatus.status_category_id} and entity_1_entity_uuid='${estatus.entity_1_entity_uuid}' limit 1)
          `
        let manager = transaction ? transaction : dbConnections().main.manager
        let data= await manager.query(base_query)
        // resolve(data)
        return data
    }
    catch(e){
        // reject(e)
        throw e
    }
// })
}

const invoiceRequisition = async (id,transaction)=>{
    try{
        let dt = isPostgres ? "NOW()" : "CURRENT_TIMESTAMP"
        let base_query=`
        SELECT  r.requisition_id,
            r.requisitioner_type_id,
            rf2.reference_name_l10n AS requisitioner_type_l10n,
            r.requisitioner_uuid,
            s1.staff_name_l10n as requisitioner_name_l10n,
            r.approver_type_id,
            rf3.reference_name_l10n AS approver_type_l10n,
            r.approver_uuid,
            s2.staff_name_l10n as approver_name_l10n,
            r.fullfiller_type_id,
            rf4.reference_name_l10n AS fullfiller_type_l10n,
            r.fullfiller_uuid,
            p.partner_name_l10n AS fullfiller_name_l10n,
            p.mobile_number AS fullfiller_mobile_number,
            r.line_items_count,
            r.status_id,
            rf1.reference_name_l10n AS status_l10n,
            r.created_at,
            r.updated_at,
            rl.requisition_line_items_id,
            rl.medicine_id,
            rl.unit,
            rl.quantity,
            rl.item_status_id,
			pc.classifier_id,
			pc.value_string_256,
			pc.value_int,
     
            pc.value_string_2000,
            pc.value_reference_id,
            pc.value_double,
            pc.value_date,
            pc.value_json,
            pc.value_l10n,
            pc_gst.value_string_256 AS partner_gst
        FROM main.requisition AS r
            LEFT JOIN main.requisition_line_items AS rl ON rl.requisition_id = r.requisition_id AND rl.active = ${ACTIVE}
            LEFT JOIN main.ref_reference AS rf1 ON rf1.reference_id = r.status_id
            LEFT JOIN main.ref_reference AS rf2 ON r.requisitioner_type_id = rf2.reference_id
            LEFT JOIN main.staff AS s1 ON r.requisitioner_uuid = s1.staff_id
            LEFT JOIN main.ref_reference AS rf3 ON r.approver_type_id = rf3.reference_id
            LEFT JOIN main.staff AS s2 ON r.approver_uuid = s2.staff_id
            LEFT JOIN main.ref_reference AS rf4 ON r.fullfiller_type_id = rf4.reference_id
            LEFT JOIN main.partner AS p ON r.fullfiller_uuid = p.partner_id
            LEFT JOIN main.partner_classification pc ON r.fullfiller_uuid = pc.partner_id
            LEFT JOIN main.partner_classification pc_gst ON pc_gst.partner_id = p.partner_id
                AND pc_gst.classifier_id = ${ENUMS.classifiers.PARTNER_GST} 
                AND pc_gst.active = ${ENUMS.record_statuses.ACTIVE}
        WHERE
            r.active = ${ACTIVE} AND r.requisition_id = '${id}' order by rl.medicine_id`
        let manager = transaction ? transaction : dbConnections().main.manager
        let data= await manager.query(base_query)
        return data
    }
    catch(e){
        throw e
    }
}

const getRequisitionClassificationQuery = async(r_id,transaction)=>{
    try{
        let base_query=`SELECT *
        FROM main.requisition_classification rc
        LEFT JOIN main.ref_reference rf ON rf.reference_id=rc.value_reference_id
        WHERE rc.active=1000100001`
        if(r_id) base_query+=` AND rc.requisition_id='${r_id}' `
        let manager = transaction ? transaction : dbConnections().main.manager
        let data= await manager.query(base_query)
        return data
    }
    catch(e){
        throw e
    }
}

// m.reference_name_l10n AS medicine_name_l10n,
// m.reference_information AS medicine_information,

// LEFT JOIN main.ref_reference AS m ON rl.medicine_id = m.reference_id


module.exports= {
    insertRequisition,
    updateRequisition,
    insertRequisitionClassification,
    updateRequisitionClassification,
    getClassificationByRequisition,
    insertRequisitionLineItems,
    updateRequisitionLineItems,
    getLineItemsByRequisition,
    getAllRequisition,
    getRequisitionLineItemsForRequisitioner,
    insertRequisitionEntityStatus,
    updateEntityStatusToAdjustEndTimeForRequisitionStatus,
    invoiceRequisition,
    getRequisitionClassificationQuery
}