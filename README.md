# Information

We are Making one Docker Image for Web App And Paravet App

# Step 1

In order To make docker image There should be two directory mobile_app and web_app and put this docker file at the top level also nginx file and ssl certificate file should be there at the top level

# Step 2

In order to build image you can use docker build -t imagename dockerfile path

# Step 3

in order to run docker image you can use docker run -d -p machine port:docker port imageId
