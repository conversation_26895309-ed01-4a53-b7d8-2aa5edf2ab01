# krushal_ssl_certificates

Generate Certificate every 3 months from https://gethttpsforfree.com/

File Description

* account.key (Dont Change) - key used for generating new certificates every 3 months 
    - Generate public key everytime using below command
    - openssl rsa -in account.key -pubout
* csr.req (Dont Change) - Use this file as-is for generating new certificates every 3 months 
* domain.key (Dont Change) - Use this file to genarate new csr if required. Not required mostly.
* signed.cert.chain - This is the file which is genrerated every 3 months and the server has to be updated with new file



Generate Certificate every 3 months using certbot

* install certbot (if not available)
* Run this command - sudo certbot certonly --manual --preferred-challenges dns -d krushal.in -d *.krushal.in
* certbot should generate a TXT record
* deploy above DNS TXT record under the name - _acme-challenge on squarespace
* verify the TXT record here - https://toolbox.googleapps.com/apps/dig/#TXT/_acme-challenge.krushal.in.
* Press ENTER
* Sample output of the above steps

Successfully received certificate.
Certificate is saved at: /etc/letsencrypt/live/krushal.in/fullchain.pem
Key is saved at:         /etc/letsencrypt/live/krushal.in/privkey.pem (This is the same as domain.key)
This certificate expires on 2024-10-31.
These files will be updated when the certificate renews.
