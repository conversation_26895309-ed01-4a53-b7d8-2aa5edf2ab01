Instructions

############# 
1. First setup Python 3.10.10 via pyenv ref https://realpython.com/intro-to-pyenv/
   a. sudo apt-get install -y make build-essential libssl-dev zlib1g-dev \
libbz2-dev libreadline-dev libsqlite3-dev wget curl llvm libncurses5-dev \
libncursesw5-dev xz-utils tk-dev libffi-dev liblzma-dev python-openssl
    b. curl https://pyenv.run | bash
    c. add the following to ~/.bashrc:

        export PATH="$HOME/.pyenv/bin:$PATH"
        eval "$(pyenv init -)"
    ** Note: We are going to use virutal enviornment management via poetry ** 
    d. pyenv install 3.10.10
    e. pyenv global 3.10.10  (When no local env is set this version of python is used)

2. Install poetry ref: https://python-poetry.org/docs/ 
    a. curl -sSL https://install.python-poetry.org | python3 -
    b. add following to .bashrc
    export PATH="$PATH:/home/<USER>/.local/bin"

#####################
Following per project the above is needed once on your machine, following instruction for the project if there is already pyproject.yml exists (this is similar to package.json)

1. cd ~/krushal/repos/local_analytics
2. sudo apt-get install libpq-dev # for postgres client files on machine
2. poetry install
3. poetry shell #This goes into virtual env if not this will create one
###### 
For createing new project with poetry run 
1. poetry init (in target directory) 
   
In vscode in command pallete python select interpreter to :   with local_analytics in it

To run on terminal
# docker-compose up

To debug in vscode
docker-compose -f docker.compose.debug.yml up 
in vscode Run-> Start Debugging

Debug setup based on https://github.com/Kludex/fastapi-docker-debug  & https://scheele.hashnode.dev/build-a-dockerized-fastapi-application-with-poetry-and-gunicorn