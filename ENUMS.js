module.exports = Object.freeze({
  preventive_healthcare_activities: {
    FARM_MANAGEMENT: **********,
    LSD_VACCINATION: **********,
    FARM_TICK_CONTROL_SPRAYING: **********,
    DEWORMING_AND_TICK_CONTROL_BOLUS_B1: **********,
    FMD_VACCINATION: **********,
    FMD_HS_BQ_VACCINATION: **********,
    THEILERIOSIS_VACCINATION: **********,
    TICK_CONTROL: **********,
    DIP_CUP_SOLUTION_USES: **********,
    FARM_AND_COW_SPRAYING: **********,
    BRUCELLOSIS: **********,
    NONE: **********,
    RUMEN_MAGNET: **********,
    DEWORMING_AND_TICK_CONTROL_BOLUS_B2: **********,
    CMT: **********,
  },
  reproductive_activities: {
    AI_PENDING: **********,
    <PERSON>ISCHARGE_PROBLEM: **********,
    PREGNANCY_DETECTION: **********,
    CALVING: **********,
    ABORTION: **********,
    DISKOTIA: **********,
    PREMATURE_BIRTH: **********,
    OTHERS: **********,
    NONE: **********,
  },
  document_activities: {
    ANIMAL_THUMBNAIL_PHOTO: **********,
  },
  staff_activity_statuses: {
    NEW_CASE_VISIT_PENDING: 1000300001,
    FOLLOW_UP_PENDING_1ST: 1000300002,
    FOLLOW_UP_PENDING_2ND: 1000300003,
    FOLLOW_UP_PENDING_3RD: 1000300004,
    FOLLOW_UP_PENDING_4TH: 1000300005,
    FOLLOW_UP_PENDING_5TH: 1000300006,
    FOLLOW_UP_PENDING_6TH: 1000300007,
    FOLLOW_UP_PENDING_7TH: 1000300008,
    FOLLOW_UP_PENDING_8TH: 1000300009,
    FOLLOW_UP_PENDING_9TH: 1000300010,
    FOLLOW_UP_PENDING_10TH: 1000300011,
    FOLLOW_UP_PENDING_11TH: 1000300012,
    FOLLOW_UP_PENDING_12TH: 1000300013,
    FOLLOW_UP_PENDING_13TH: 1000300014,
    FOLLOW_UP_PENDING_14TH: 1000300015,
    FOLLOW_UP_PENDING_15TH: 1000300016,
    VISIT_COMPLETED: 1000300050,
    VISIT_CANCELLED: 1000300051,
    VISIT_ABANDONED: 1000300052,
  },
  record_statuses: {
    ACTIVE: 1000100001,
    INACTIVE: 1000100002,
  },
  classifiers: {
    OBSERVATION_DATE: 2000000261,
    ACTIVITY_FORM_COMPLETION: 2000000421,
    ANY_DEFECTS_SEEN_IN_ANIMAL: 2000000175,
    ANIMAL_ACTIVATION_DATE: 2000000177,
    ANIMAL_ACTIVATION_REASON: 2000000178,
    ANIMAL_ACTIVATION_REASON_OTHER: 2000000179,
    SALES_ATTRIBUTION_STAFF_ID: 2000000180,
    SALES_REGISTRATION_STAFF_ID: 2000000181,
    OBSERVATION_AND_SYMPTOMS: 2000000182,
    RESPONSE_TO_TREATMENT: 2000000183,
    DIAGNOSIS: 2000000184,
    ACTIVITY_MEDICINES: 2000000185,
    FOLLOW_UP_TASK_ID: 2000000186,
    GAP_IN_TEETH: 2000000210,
    HORNS_EXIST: 2000000211,
    SPREAD_OF_PATCHES: 2000000212,
    STRETCHABILITY_OF_UDDER: 2000000213,
    VISIBILITY_OF_VEINS: 2000000214,
    ADDITIONAL_TEAT: 2000000215,
    SERVICE_REQUEST_TYPE: 2000000243,
    SLAB_FOR_STAFF: 2000000240,
    AI_FORM_ZOHO_FORM_DATA_TEMPORARY: 2000000130,
    STAFF_ONLINE_OVERRIDE_FLAG_YES_OR_NO: 2000000137,
    ANIMAL_FOREIGN_BODY_PRESENCE_AREA: 2000000140,
    ANIMAL_BODY_TEMPARATURE: 2000000141,
    ANIMAL_LOCOMOTION_SCORE: 2000000142,
    ANIMAL_DUNG_SCORE: 2000000143,
    ANIMAL_BODY_SCORE: 2000000144,
    ANIMAL_UDDER_SCORE: 2000000145,
    ANIMAL_STAGE: 2000000146,
    ANIMAL_GIVING_MILK_OR_NOT: **********,
    ANIMAL_PREGANANT_OR_NOT: **********,
    TEMPORAL_ENTITY_TYPE_ID: **********,
    ANIMAL_NUMBER_OF_TEETH: **********,
    ANIMAL_UDDER_HYGENE_SCORE: **********,
    ANIMAL_UDDER_DEPTH_BALANCE_SCORE: **********,
    ANIMAL_UDDER_CLEFT_SCORE: **********,
    ANIMAL_NOSE_CONDITION_SCORE: **********,
    FARMER_FOLIAGE_AVAILABLE: **********,
    FARMER_CONCENTRATE_AVAILABLE: **********,
    ANIMAL_MILK_FAT: **********,
    ANIMAL_FEED_RECOMMENDATIONS: **********,
    ANIMAL_HEALTH_SCORE: **********,
    KRUSHAL_PLAN_SUGGESTION_FROM_FIELD: **********,
    ANIMAL_SICK_LAST_ONE_YEAR: **********,
    ANIMAL_BODY_BACK_VIEW_CONDITION: **********,
    ANIMAL_BODY_SIDE_VIEW_CONDITION: **********,
    VET_AVAILABLITY_ON_ACTIVITY_COMPLETION: **********,
    CATTLE_MILKING_DATA: **********,
    LH_TEAT_CMT_SCORE: **********,
    RH_TEAT_CMT_SCORE: **********,
    LF_TEAT_CMT_SCORE: **********,
    RF_TEAT_CMT_SCORE: **********,
    BODY_SCORE_OBSERVATION_1: **********,
    BODY_SCORE_OBSERVATION_2: **********,
    BODY_SCORE_OBSERVATION_3: **********,
    BODY_SCORE_OBSERVATION_4: **********,
    UDDER_CLEFT: **********,
    UDDER_DEPTH: **********,
    UDDER_BALANCE: **********,
    UDDER_TEAT_PLACEMENT: **********,
    UDDER_TEAT_LENGTH: **********,
    COMPRESSED_DATABASE_SYNC: **********,
    CUSTOMER_TERMS_AND_CONDITION_STATUS: **********,
    IS_CATTLE_PREGNANT: **********,
    STAFF_AADHAR: **********,
    CUSTOMER_PAN: **********,
    STAFF_PAN: **********,
    CATTLE_INSURANCE_REQUIRED: 2000000187,
    OTHER_COMPANY_NAME_FOR_SEMEN: 2000000220,
    ANIMAL_INACTIVE_REASON: 2000000150,
    ANIMAL_INACTIVE_DATE: 2000000151,
    ANIMAL_INACTIVE_REASON_OTHER: 2000000153,
    AI_FORM_HEAT_DATE_AND_TIME: 2000000131,
    AI_FORM_SUITABLE_FOR_AI_BASED_ON_HEAT: 2000000132,
    FARMER_AGE_OF_DATE: 2000000097,
    ANIMAL_FORM_ENTRY_DATE_TEMPORARY: 2000000098,
    LAST_DEWORMING_STATUS_AS_OF_DATE: 2000000161,
    AI_FORM_SUITABLE_FOR_AI_BASED_ON_DISCHARGE: 2000000133,
    AI_FORM_COMPANY_OF_SEMEN: 2000000134,
    AI_FORM_NAME_OF_BULL: 2000000135,
    AI_FORM_ID_OF_BULL: 2000000136,
    FARMER_DATA: 2000000138,
    TOTAL_NUMBER_OF_FEMALE_CALVES_BUFFALO_AND_COW_PURCHASED_IN_LAST_3_YEARS: 2000000020,
    ANIMAL_DISEASE_DATE: 2000000078,
    AVERAGE_PRICE_OF_FEMALE_CALF_PURCHASED: 2000000023,
    LAST_DEWORMING_DATE: 2000000058,
    ANIMAL_DISEASE_TYPE: 2000000077,
    LINKED_CARE_CALENDAR_TASK: 2000000126,
    OTHER_ASSETS_OWNED: 2000000030,
    LAST_DEWORMING_STATUS: 2000000160,
    DISTRICT_TALUKA_OF_FARMER: 2000000001,
    VILLAGE_OF_FARMER: 2000000002,
    CARE_CALENDAR_EXPIRATION_DATE: 2000000139,
    COMPLAINT_DATE: 2000000127,
    COMPLAINT_FOLLOW_UP_DATE: 2000000128,
    COMPLAINT_STATUS: 2000000129,
    LAST_TICK_CONTROL_STATUS_AS_OF_DATE: 2000000162,
    MONTHS_SINCE_LAST_CALVING_AS_OF_DATE: 2000000163,
    PREGNANCY_STATUS_AS_OF_DATE: 2000000164,
    NUMBER_OF_MONTHS_PREGNANT_AS_OF_DATE: 2000000165,
    LAST_FMD_VACCINATION_DATE_RANGE_AS_OF_DATE: 2000000166,
    LAST_FMD_HS_BQ_VACCINATION_DATE_RANGE_AS_OF_DATE: 2000000167,
    THEILERIOSIS_VACCINATION_STATUS_AS_OF_DATE: 2000000168,
    LAST_ANTHRAX_DATE: 2000000171,
    MEDICINE_MRP: 2000000264,
    ANIMAL_ILLNESS: 2000000086,
    OTHER_ANIMAL_ILLENESS: 2000000087,
    CUSTOMER_AGE: 2000000088,
    CUSTOMER_GENDER: 2000000089,
    FARMER_FORM_ENTRY_DATE_TEMPORARY: 2000000090,
    FARMER_FORM_ENTRY_IP_ADDRESS_TEMPORARY: 2000000091,
    FARMER_FORM_ENTRY_DATE_IN_FORM_TEMPORARY: 2000000092,
    FARMER_FORM_ENTRY_RO_NAME_TEMPORARY: 2000000093,
    FARMER_FORM_ENTRY_LSS_NAME_TEMPORARY: 2000000094,
    FARMER_DOB: 2000000095,
    FARMER_AGE: 2000000096,
    ANIMAL_FORM_ENTRY_IP_ADDRESS_TEMPORARY: 2000000099,
    ANIMAL_FORM_ENTRY_DATE_IN_FORM_TEMPORARY: 2000000100,
    ANIMAL_FORM_ENTRY_RO_NAME_TEMPORARY: 2000000101,
    ANIMAL_FORM_ENTRY_LSS_NAME_TEMPORARY: 2000000102,
    ANIMAL_AVERAGE_LPD: 2000000103,
    COMPLAINT_NUMBER: 2000000125,
    AADHAAR: 2000000003,
    FARMER_DAIRY_FARM_LOCATION: 2000000004,
    TYPE_OF_DAIRY_FARM: 2000000005,
    TOTAL_NO_OF_COWS: 2000000006,
    TOTAL_NO_OF_COW_CALVES: 2000000007,
    TOTAL_NO_OF_BUFFALOES: 2000000008,
    TOTAL_NO_OF_BUFFALO_CALVES: 2000000009,
    ACREAGE_RANGE_FOR_FOLIAGE_CROPS: 2000000010,
    OTHER_ACREAGE_RANGE_FOR_FOLIAGE_CROPS: 2000000011,
    ACREAGE_FOR_FOLIAGE_CROPS: 2000000012,
    FOLIAGE_CROPS_GROWN: 2000000013,
    OTHER_FOLIAGE_CROPS_GROWN: 2000000014,
    NUMBER_OF_LABOURERS_USED_FOR_DAIRY: 2000000015,
    MONTHLY_LABOUR_COST: 2000000016,
    TOTAL_DAIRY_INCOME: 2000000017,
    LAST_PREGNANCY_DETERMINATION_DATE: 2000000066,
    LAST_FMD_VACCINATION_STATUS: 2000000068,
    ANIMAL_DISEASE_DURATION: 2000000079,
    TOTAL_NUMBER_OF_COWS_BOUGHT_IN_LAST_3_YEARS: 2000000018,
    TOTAL_NUMBER_OF_COWS_SOLD_IN_LAST_3_YEARS: 2000000019,
    AVERAGE_PRICE_OF_COW_BOUGHT_SOLD: 2000000021,
    AVERAGE_PRICE_OF_BUFFALO_BOUGHT_SOLD: 2000000022,
    TOTAL_INCOME_INCLUDING_DAIRY_FARM_OTHERS: 2000000025,
    TOTAL_LAND_INCLUDING_FOLIAGE_CROP: 2000000026,
    TOTAL_NUMBER_OF_AGED_DEPENDENTS_IN_HOUSEHOLD: 2000000027,
    TOTAL_NUMBER_OF_ADULTS_IN_HOUSEHOLD: 2000000028,
    TOTAL_NUMBER_OF_CHILDREN_IN_HOUSEHOLD: 2000000029,
    TOTAL_EMI_PER_MONTH: 2000000032,
    NAME_OF_ANIMAL: **********,
    EAR_TAG_NUMBER: **********,
    TYPE_OF_ANIMAL: **********,
    BREED_OF_ANIMAL: **********,
    OTHER_BREED_OF_ANIMAL: **********,
    AGE_OF_ANIMAL: **********,
    AGE_OF_ANIMAL_AS_OF: **********,
    DOB_OF_ANIMAL: **********,
    WEIGHT_OF_ANIMAL: **********,
    NUMBER_OF_CALVINGS: **********,
    MILKING_STATUS_OF_ANIMAL: **********,
    MAX_LPD_OF_ANIMAL_NOT_AVERAGE: **********,
    ANIMAL_ILLNESSES_3_MONTH: **********,
    OTHER_3_MONTH_ANIMAL_ILLNESSES: **********,
    PREVENTIVE_HEALTHCARE_2_MONTH: **********,
    OTHER_2_MONTH_PREVENTIVE_HEALTHCARE: **********,
    PREGNANCY_RANGE_OF_ANIMAL: **********,
    INSURANCE_STATUS_OF_ANIMAL: **********,
    SUBSCRIPTION_PLAN: **********,
    STATE_FARMER_BELONGS_TO: **********,
    DISTRICT_FARMER_BELONGS_TO: **********,
    TALUK_FARMER_BELONGS_TO: **********,
    VILLAGE_FARMER_BELONGS_TO: **********,
    ADDRESS_OF_FARMER: **********,
    LAST_CALVING_DATE: **********,
    LAST_AI_DATE: **********,
    NUMBER_OF_MONTHS_PREGNANT: **********,
    LAST_DIP_CUPS_REPLACEMENT_DATE: **********,
    DIP_CUPS_STATUS: **********,
    TICK_CONTROL_STATUS: **********,
    LAST_TICK_CONTROL_DATE: **********,
    MONTHS_SINCE_LAST_CALVING: **********,
    AVERAGE_PRICE_OF_FEMALE_CALF_SOLD: **********,
    DEWORMING_STATUS: **********,
    ANIMAL_DISEASE_MEDICATION: **********,
    ANIMAL_DISEASE_PARAVET_NAME: **********,
    ANIMAL_DISEASE_VET_NAME: **********,
    ANIMAL_DISEASE_PRESCRIPTION_IMAGE: **********,
    ANIMAL_DISEASE_OTHER_IMAGES: **********,
    CC_ACTIVITY_MEDICINE_1: 2000000104,
    CC_ACTIVITY_MEDICINE_1_QUANTITY: 2000000105,
    CC_ACTIVITY_MEDICINE_2: 2000000106,
    CC_ACTIVITY_MEDICINE_2_QUANTITY: 2000000107,
    CC_ACTIVITY_MEDICINE_3: 2000000108,
    CC_ACTIVITY_MEDICINE_3_QUANTITY: 2000000109,
    CC_ACTIVITY_MEDICINE_4: 2000000110,
    CC_ACTIVITY_MEDICINE_4_QUANTITY: 2000000111,
    CC_ACTIVITY_MEDICINE_5: **********,
    CC_ACTIVITY_MEDICINE_5_QUANTITY: **********,
    CC_ACTIVITY_MEDICINE_6: **********,
    CC_ACTIVITY_MEDICINE_6_QUANTITY: **********,
    CC_ACTIVITY_MEDICINE_7: **********,
    CC_ACTIVITY_MEDICINE_7_QUANTITY: **********,
    CC_ACTIVITY_MEDICINE_8: **********,
    CC_ACTIVITY_MEDICINE_8_QUANTITY: **********,
    CC_ACTIVITY_MEDICINE_9: **********,
    CC_ACTIVITY_MEDICINE_9_QUANTITY: **********,
    CC_ACTIVITY_MEDICINE_10: **********,
    CC_ACTIVITY_MEDICINE_10_QUANTITY: **********,
    HEALTHCARE_PLAN_SUBSCRIPTION_DATE: **********,
    TOTAL_NUMBER_OF_FEMALE_CALVES_SOLD_IN_LAST_3_YEARS: **********,
    LAST_FMD_HS_BQ_VACCINATION_DATE: **********,
    BRUCELLOSIS_VACCINATION_STATUS: **********,
    BRUCELLOSIS_VACCINATION_DATE: **********,
    THILERIOSIS_VACCINATION_STATUS: **********,
    THILERIOSIS_VACCINATION_DATE: **********,
    ANTHRAX_VACCINATION_STATUS: **********,
    CURRENT_LOANS: **********,
    LAST_FMD_VACCINATION_DATE: **********,
    LAST_FMD_HS_BQ_VACCINATION_STATUS: **********,
    VISIT_CONSULTANT_TYPE: **********,
    PRESCRIPTION_PUBLISHED_AT: **********,
    PRESCRIPTION_ADMINISTERED_AT: **********,
    AI_TASK_COMPLETION_STATUS: **********,
    STAFF_PLAN: **********,
    STAFF_PLAN_START_DATE: **********,
    ARTIFICIAL_INSEMINATION_FORM_COMPLETED_ON: **********,
    CLINICAL_MASTITIS_TEST_FORM_COMPLETED_ON: **********,
    PREGNANCY_DETECTION_FORM_COMPLETED_ON: **********,
    FARM_TICK_CONTROL_SPRAY_FORM_COMPLETED_ON: **********,
    OBSERVATION_ANSWER: 2000000277,
    NUMBER_OF_ANIMALS: 2000000282,
    NUMBER_OF_CALF: 2000000283,
    DATE_OF_VISIT: 2000000284,
    HOUSING_TYPE: 2000000285,
    SPACE_TYPE: 2000000299,
    FEEDING_ZONE_TYPE: 2000000286,
    RESTING_ZONE: 2000000287,
    LOFFING_ZONE: 2000000288,
    COW_GROUPING_DONE: 2000000289,
    FLOOR_CONDITION: 2000000290,
    WATER_STAGNENT_IN_SOME_AREA: 2000000291,
    ANY_EXISTING_TREE_OR_PLANTATION_DONE: **********,
    SLOPE_OF_TOTAL_AREA_MAINTAINED_FOR_DRAINING_OF_RAINY_WATER: **********,
    NUISANCE_VALUES_OF_ECTOPARASITES: **********,
    DIRTINESS_SCORE: **********,
    DUNG_SCORE: **********,
    FREQUENCY_OF_REMOVING_DUNG_FROM_OVERALL_AREA: **********,
    MANURE_DUNG_STORING_PLACE_CLOSE_TO_SHED: **********,
    ANIMAL_ANTIBIOTIC_FLAG: **********,
    MEDICINE_USAGE_TYPE: **********,
    MEDICINE_FLAGGED: **********,
    ACTIVITY_FORM_DATA: **********,
    ACCOUNT_PAYMENT_TYPE: **********,
    INVOICE_GENERATED: **********,
    OTP_VERIFIED: **********,
    OTP: **********,
    PARTNER_GST: **********,
    CUSTOMER_ALTERNATE_CONTACT: **********,
    TASK_SERVICE_COST_PAYMENT_DETAILS: **********,
    TASK_MEDICINE_COST_PAYMENT_DETAILS: **********,
    TASK_PAYMENT_DETAILS: **********,
    CUSTOMER_CREDIT_LIMIT: **********,
    CUSTOMER_CREDIT_USAGE: **********,
    CUSTOMER_POURING_TO_DAIRY: **********,
    NUMBER_OF_MONTHS_PREGNANT_2: **********,
    DIAGNOSIS_UPDATED_AT: **********,
    OBSERVATION_UPDATED_AT: **********,
    OBSERVATION_VERSION: **********,
  },
  entity_type: {
    CUSTOMER: **********,
    ANIMAL: **********,
    STAFF: **********,
    TASK: **********,
    CUSTOMER_LEGACY: **********,
    ANIMAL_LEGACY: **********,
    TASK_LEGACY: **********,
  },
  relationship_types: {
    ROUTE_OFFICER_TO_PARAVET: **********,
    PARAVET_TO_FARMER: **********,
    BACKOFFICE_PERSON_TO_PARAVET: **********,
    FARMER_TO_ANIMAL: 1000210004,
    SNOOZE_SELLABLE_ANIMAL: 1000210005,
    SNOOZE_SELLABLE_FARMER: 1000210006,
    PPU_PARAVET_TO_FARMER: 1000210010,
    PPU_PARAVET_TO_TASK: 1000210011,
    CENTRALIZED_VET_TO_TASK: 1000210012,
  },
  yes_no_values: {
    YES: 1000105001,
    NO: 1000105002,
  },
  note_type: {
    PRESCRIPTION_ADVISORY_NOTE: 1000440005,
    OBSERVATION_NOTES: 1000440006,
    TASK_ACTIVITY_NOTE: 1000440001,
    ANIMAL_NOTE: 1000440002,
    FARMER_NOTE: 1000440003,
    IMAGE: 1000440004,
    STAFF_TIMELINE: 1000440007,
    CUSTOMER_SERVICE_TASK_INSTRUCTIONS: 1000440015,
    SNOOZE_CUSTOMER_SERVICE_TASK_RELATED_COMMENT: 1000440016,
    UPDATE_DATA_CUSTOMER_SERVICE_TASK_RELATED_COMMENT: 1000440017,
  },
  freelancer_slab_for_sr_ticket: {
    SLABS_RATE: 1000910001,
  },
  transaction_status: {
    CONFIRMED: 1000880001,
    BLOCKED: 1000880002,
    CANCELLED: 1000880003,
  },
  staff_subscription_plans: {
    FREELANCE_DEFAULT_PLAN: 1000860001,
  },
  object_management: {
    file_object_key: {
      NAME: 'file_object_key',
      MAX_COUNT: 10,
    },
    LIMIT_SIZE: 20000000 /* in bytes (near to 20MB) */,
    allowed_file_types: ['mp4', 'mov', 'avi', 'wmv', 'webm', 'mkv', '3gp', 'x-flv', 'flv', 'x-matroska', '3gpp', 'pdf', 'jpg', 'jpeg', 'png', 'gif', 'blob', 'video', 'image', 'application'],
    storage: {
      MEMORY: 'memory',
      DISK: 'disk',
    },
  },
  aws_s3_entities: {
    task: 'task',
    animal: 'animal',
    customer: 'customer',
    staff: 'staff',
  },
  observation_question: {
    DOCUMENT: 1030000090,
    QUESTION_CATEGOEY: 1001280001,
    CATTLE_WEIGHT: 1030000029
  },

  document_type: {
    CUSTOMER_THUMBNAIL: 1000260003,
    ACTIVITY_PRESCRIPTION_PDF:1000260015,
    SYSTEM_GENERATED_WITH_FARMER_FOLIAGE_ONLY_SMART_RATION_REPORT: 1000260023,
    SYSTEM_GENERATED_SMART_RATION_REPORT:1000260024,
    OBSERVATION: 1000260025,
    SERVICE_DOCUMENT: 1000260026,
    FARM_MANAGEMENT_DOCUMENT: 1000260027,
    FARM_MANAGEMENT_PDF: 1000260028,
    ANIMAL_THUMBNAIL_1:**********,
    ANIMAL_EARTAG_1:1000260012,
    FOREIGN_BODY_AREA:1000260013,
    REFERENCE_DOCUMENTS:1000260014,
    CUSTOMER_TERMS_AND_CONDITION_PDF:1000260017,
    FARMER_DAILY_DAIRY_RECIEPT:1000260018,
    ANIMAL_CLASSIFICATION_DOCUMENTS:1000260016,
    REQUISITION_INVOICE:1000260021,
    OBSERVATION_MEDIA:1000260025,
    ANIMAL_PHOTO_ANY_ANGLE_1:1000260002,
    DAIRY_FARM_PHOTO_1:1000260005,
    FARM_PHOTO_1:1000260008,
    FARMER_AADHAAR_PHOTO_1:1000260006,
    FARMER_PHOTO_1:1000260007,
    RO_QR_CODE_1:1000260009,
    TASK_ACTIVITY_PHOTO_1:1000260010,
    STAFF_THUMBNAIL_PHOTO_1:1000260004,
    VET_SIGNATURE_PHOTO_1:10********,
  },

  staff_type: {
    ROUTE_OFFICER: **********,
    BACK_OFFICE: **********,
    VET: **********,
    PARAVET: **********,
    ZONAL_OFFICER: **********,
    PPU_PARAVET: **********,
    CENTRAL_VET: **********,
    BCO_ADMIN: **********,
  },
  medication_categories: {
    ANTIBIOTIC: **********,
  },
  geography_types: {
    VILLAGE: **********,
    DISTRICT: **********,
    TALUK: **********,
    STATE: **********,
  },
  activity_configuration: {
    activity_cost_map: **********,
  },
  account_payment_type: {
    DEDUCTION_AT_SOURCE: **********,
    SELF_PAY: **********,
    PAY_IN_FULL: **********,
    PAID_BY_PROVIDER_WITH_CREDIT_LIMIT: **********,
    PAID_BY_PROVIDER: **********,
  },
  subscription_plan: {
    TN_RETAIL_PLAN_1: **********,
    SHREEJA_PLAN: **********,
    G_AND_G_ANIMAL_CARE: **********,
    AP_RETAIL_PLAN: **********,
  },
  activity_category: {
    PREVENTIVE: **********,
    CURATIVE: **********,
    REPRODUCTIVE: **********,
  },
  payer: {
    CUSTOMER: **********,
    SHREEJA: **********,
  },
  data_map: {
    DEFAULT_PAYMENT_MAPPING_BY_PLAN: **********,
    SUBSCRIPTION_PLAN_MAPPING: **********,
    PREVENTIVE_CALENDAR_CONFIG: **********,
    SR_PAYMENT_DETAILS_RULES: **********,
    PREVENTIVE_CALENDAR_RULES: **********,
  },
  document_sync_statuses: {
    DOCUMENT_SYNCED:**********,
    CLIENT_DOCUMENTS_MODIFIED:**********,
    SERVER_DOCUMENTS_MODIFIED:**********,
    SERVER_DOCUMENTS_MODIFIED_AND_CLIENT_DOCUMENT_EXISTS:**********,
  },
  farm_management_question_key: {
    CURRENT: "current",
    TARGET: "target",
    TARGET_DATE: "target_date",
    NOTES: "notes",
    MEDIA: "image",
    ADVICE: "advices",
    REFERENCEMEDIA: "reference_media"
  },
  farm_management_question_type: {
    MEDIA: "media",
    ADVANCED_SELECT: "advanced_select",
    ADVANCED_MULTI_SELECT: "advanced_multi_select",
    UNIT: "unit"
  },
  farm_management_frequency: {
    ONE_TIME: 'one-time',
  },
  moment_keys: {
    days: 'days',
    month: 'month',
    DATE_FORMAT: 'YYYY-MM-DD',
  },
  farm_management_priority_grouping: {
    priority_1: 1,
    priority_2: 2,
  },
  farm_management_frequency_grouping_name: {
    VISIT_PRIORITY: 'Visit Priority',
    ADDITIONAL: 'Additional',
  },
  farm_management_frequency_grouping_name_l10n: {
    VISIT_PRIORITY: { en: 'Visit Priority' , 'ta': 'முன்னுரிமையைப் பார்வையிடவும்' },
    ADDITIONAL: { en: 'Additional' ,'ta':'கூடுதல்' },
  },
  farm_management_question: {
    LPD: '6e5b94a3-0896-4557-a185-640fef752376' ,
    FAT: '7e747b70-73f5-4e72-b6be-aa6682abe9ce' ,
    SNF: '42e4671e-af48-46db-bbb0-8dd405c57f89' ,
    Cost_of_Milk_Production: 'b7abaf63-6d71-4dec-84f2-7617b31f07a9'
  }
})
