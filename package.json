{"name": "krushal-farmer-app", "version": "2.4.0", "private": true, "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@krushal-it/ah-router": "git+https://ghrokrushal:<EMAIL>/krushal-it/ah-router#release/2.1.0", "@krushal-it/common-core": "2.0.0", "@mui/icons-material": "^5.10.16", "@mui/material": "^5.10.14", "@mui/styled-engine-sc": "^5.10.14", "@mui/x-date-pickers": "5.0.20", "@mui/x-date-pickers-pro": "5.0.13", "@reduxjs/toolkit": "^1.9.5", "antd": "^5.9.0", "bootstrap": "^5.2.2", "bootstrap-switch-button-react": "^1.2.0", "date-fns": "^2.29.3", "dayjs": "^1.11.7", "firebase": "^9.17.2", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lodash.debounce": "^4.0.8", "material-react-table": "^1.3.16", "moment": "^2.29.4", "multiselect-react-dropdown": "^2.0.25", "pdf-viewer-reactjs": "^2.2.3", "pouchdb-browser": "8.0.1", "pure-react-carousel": "^1.30.1", "query-string": "^8.1.0", "react": "^18.2.0", "react-autocomplete": "^1.8.1", "react-big-calendar": "^1.5.2", "react-bootstrap": "^2.5.0", "react-datepicker": "^4.8.0", "react-dom": "^18.2.0", "react-elastic-carousel": "^0.11.5", "react-google-translate": "^1.0.11", "react-image-file-resizer": "^0.4.8", "react-images-uploading": "^3.1.7", "react-images-viewer": "^1.7.1", "react-infinite-scroll-component": "^6.1.0", "react-moment": "^1.1.2", "react-pdf": "^7.3.3", "react-redux": "^8.1.2", "react-router-dom": "^6.4.2", "react-scripts": "5.0.1", "react-signature-canvas": "^1.0.6", "react-simple-image-viewer": "^1.2.2", "react-toastify": "^9.1.1", "styled-components": "^5.3.6", "sweetalert2": "^11.7.28", "web-push": "^3.6.3", "web-vitals": "^2.1.4"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0"}, "scripts": {"package3-@syncfusion/ej2-react-calendars": "^20.3.56", "package4-@syncfusion/ej2-react-schedule": "^20.3.57", "start": "react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "build_dev": "CI=false npm run build", "prod": "node server.js", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}