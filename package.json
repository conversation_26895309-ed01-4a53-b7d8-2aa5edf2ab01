{"name": "@krushal-it/ah-orm", "version": "3.0.0", "migrationServiceRegistry": "registry.gitlab.com/krushal-it/**********************", "migrationServiceName": "ah_orm_migration_service", "migrationServiceVersion": "1.0.1", "description": "ORM for Animal Health", "publishConfig": {"@krushal-it:registry": "https://gitlab.com/api/v4/projects/41140174/packages/npm/"}, "scripts": {"start": "IS_SERVER=True DB_TYPE=POSTGRES node index.js", "initialize": "IS_SERVER=True DB_TYPE=POSTGRES node initializeConnections.js", "triggerHelper": "node triggerHelper.js"}, "main": "index.js", "author": "<PERSON><PERSON><PERSON>", "dependencies": {"pg": "^8.8.0", "reflect-metadata": "^0.1.13", "sqlite": "^4.1.2", "sqlite3": "^5.1.4", "typeorm": "^0.3.11"}, "peerDependencies": {"@krushal-it/common-core": "*", "axios": "*", "uuid": "*"}, "devDependencies": {"@types/node": "^18.11.17", "grunt-contrib-copy": "^1.0.0", "grunt-terser": "^2.0.0"}}