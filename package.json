{"name": "krushal", "version": "4.4.0", "description": "", "serviceRegistry": "registry.gitlab.com/krushal-it/**********************", "serviceName": "back-end", "serviceVersion": "4.4.0", "main": "server.js", "scripts": {"dev": "node --inspect=0.0.0.0 server.js", "prod": "node --inspect=0.0.0.0 server.js", "start": "node --inspect=0.0.0.0 server.js", "test": "echo \"Error: no test specified\" && exit 1", "migration:generate_dev": "typeorm migration:generate -d  ./database_utils/connection/config/dev_typeorm_config.js -o ./database_utils/migrations/migration -t true", "migration:up_dev": "typeorm migration:run -d ./database_utils/connection/config/dev_typeorm_config.js", "migration:down_dev": "typeorm migration:revert -d ./database_utils/connection/config/dev_typeorm_config.js", "ignore-package-1": "\"@krushal-oc/common-core\": \"github:krushal-oc/common-core#develop\"", "alternate-way-for-using-packages": "@krushal-oc/common-core\": \"npm:@krushal-it/common-core@1.0.0\"", "ignore-for-now-package@krushal-it/common-core": "1.0.0"}, "author": "", "license": "ISC", "dependencies": {"@feathersjs/errors": "^4.5.15", "@feathersjs/express": "^4.5.15", "@feathersjs/feathers": "^4.5.15", "@feathersjs/socketio": "^5.0.14", "@google-cloud/translate": "^7.0.3", "@krushal-it/ah-orm": "git+https://ghrokrushal:<EMAIL>/krushal-it/ah-orm.git#release/base", "@krushal-it/back-end-lib": "git+https://ghrokrushal:<EMAIL>/krushal-it/back-end-lib.git#release/base", "@krushal-it/common-core": "^2.0.2", "@krushal-it/krushal-pdf-generator": "git+https://ghrokrushal:<EMAIL>/krushal-it/krushal-pdf-generator.git#minor_updates", "@krushal-it/mobile-or-server-lib": "npm:@krushal-it/server-lib@^3.0.0", "aws-sdk": "^2.1238.0", "axios": "^1.3.2", "concat-stream": "2.0.0", "cors": "^2.8.5", "docxtemplater": "3.44.0", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "fcm-node": "^1.6.1", "feathers-validations": "^1.0.1", "feathers-validator": "^1.0.2", "firebase-admin": "^11.3.0", "form-data": "^4.0.0", "fs": "^0.0.1-security", "googleapis": "131.0.0", "grunt": "^1.6.1", "grunt-contrib-clean": "^2.0.1", "grunt-contrib-uglify": "^5.2.2", "grunt-json-minification": "^0.2.1", "handlebars": "4.7.8", "javascript-lp-solver": "^0.4.24", "json-rules-engine": "^6.5.0", "json2csv": "^6.0.0-alpha.2", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.39", "multer": "^1.4.5-lts.1", "nanoid": "3.3.4", "node-cron": "^3.0.2", "nodemailer": "^6.9.13", "pg": "^8.8.0", "pizzip": "3.1.6", "puppeteer": "19.11.1", "reflect-metadata": "^0.1.13", "socket.io": "^4.7.3", "sqlite": "^4.1.2", "sqlite3": "^5.1.4", "stream": "0.0.2", "translate-google": "^1.5.0", "typeorm": "^0.3.11", "uuid": "^9.0.0", "ws": "^8.16.0"}, "devDependencies": {"@types/node": "^18.11.17", "eslint": "^8.37.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.7.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.32.2", "grunt-contrib-copy": "^1.0.0", "grunt-terser": "^2.0.0", "nanoid": "^3.3.7", "prettier": "1.19.1"}}