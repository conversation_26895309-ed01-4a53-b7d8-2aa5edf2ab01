{"name": "my-app", "version": "2.7.1", "private": true, "dependencies": {"@ant-design/icons": "5.2.6", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fortawesome/fontawesome-free": "^6.4.2", "@hookform/resolvers": "^3.9.0", "@json-schema-spec/json-pointer": "^0.1.2", "@krushal-it/common-core": "2.0.2", "@krushal-it/krushal-hook-form": "^2.1.9", "@mui/icons-material": "^5.10.16", "@mui/lab": "^5.0.0-alpha.144", "@mui/material": "^5.14.9", "@mui/x-date-pickers-pro": "^5.0.13", "@reduxjs/toolkit": "^1.9.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "ajv": "^8.16.0", "ajv-keywords": "^5.1.0", "antd": "^5.8.0", "antd-style": "^3.7.1", "axios": "^1.2.0", "bootstrap": "^5.2.2", "brace": "^0.11.1", "date-fns": "^2.29.3", "dayjs": "^1.11.10", "exceljs": "4.4.0", "express": "^4.18.2", "firebase": "^9.14.0", "fuse.js": "^7.0.0", "gh-pages": "^3.2.3", "krushal-hook-form": "git+https://ghrokrushal:<EMAIL>/krushal-it/krushal-hook-form#refactoring-bundling", "leaflet": "^1.7.1", "leaflet-draw": "^1.0.4", "leaflet-fullscreen": "^1.0.2", "lodash": "^4.17.21", "material-react-table": "^1.3.16", "moment": "^2.29.4", "pdf-viewer-reactjs": "^2.2.3", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-big-calendar": "^1.5.2", "react-bootstrap": "^2.6.0", "react-datepicker": "^4.16.0", "react-dom": "^18.2.0", "react-highlight-words": "^0.20.0", "react-hook-form": "^7.49.2", "react-image-file-resizer": "^0.4.8", "react-images-viewer": "^1.7.1", "react-leaflet": "^3.2.0", "react-moment": "^1.1.2", "react-pdf": "^7.5.1", "react-quill": "^2.0.0", "react-redux": "^8.1.2", "react-responsive": "^9.0.2", "react-router-dom": "6.18.0", "react-scripts": "^5.0.1", "react-simple-image-viewer": "^1.2.2", "react-toastify": "^9.1.1", "react-use": "^17.4.0", "reactjs-popup": "^2.0.6", "sass": "^1.35.1", "web-vitals": "^2.1.4", "zod": "^3.23.8"}, "devDependencies": {"@hookform/devtools": "^4.3.1", "@types/lodash": "^4.17.5", "@types/react-highlight-words": "^0.20.0", "eslint": "^8.41.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.7.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.32.2", "typescript": "^5.3.3"}, "browser": {"zlib": false, "stream": false}, "scripts": {"FROM DEPENDENCIES - @krushal-it/common-core": "2.0.0", "start": "react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "build_dev": "CI=false npm run build", "server": "node server.js", "lint": "eslint .", "ignore-pdf-viewer-reactjs": "^2.2.3"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}