{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1d091519", "metadata": {}, "outputs": [], "source": ["import folium\n", "from sqlalchemy import create_engine\n", "from sqlalchemy import text\n", "from IPython.display import display"]}, {"cell_type": "code", "execution_count": 2, "id": "86bcdb51", "metadata": {"scrolled": true}, "outputs": [], "source": ["engine = create_engine('postgresql+psycopg2://krushallocal:krushallocal@localhost:5432/krushallocal')\n", "engine.execution_options(schema_translate_map = { None: 'location' } )\n", "conn = engine.connect()"]}, {"cell_type": "code", "execution_count": 5, "id": "525d797c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(39.977256777777775, 116.32896233333334, datetime.datetime(2008, 10, 23, 6, 1, 24)), (39.977256777777775, 116.32896233333334, datetime.datetime(2008, 10, 23, 6, 1, 24)), (40.014436086956515, 116.30585956521738, datetime.datetime(2008, 10, 23, 11, 9, 10)), (40.014436086956515, 116.30585956521738, datetime.datetime(2008, 10, 23, 11, 9, 10)), (40.01364757068064, 116.30663940837665, datetime.datetime(2008, 10, 23, 11, 49, 13))] 5\n", "(39.978311, 116.327259, datetime.datetime(2008, 10, 23, 6, 1, 24)) 632 5\n"]}], "source": ["#first read stay points first 10 \n", "#then display on the raw points starting time to end time all epoints with blue\n", "stmt = f''' select lat, lng, arrived_at from location.stay_point where \n", "            user_device_id = '001' \n", "            order by arrived_at ASC\n", "            limit 5\n", "        '''\n", "results = conn.execute(text(stmt))\n", "results = results.fetchall()\n", "stay_points = [ r for r in results ]\n", "print (stay_points, len(stay_points))\n", "#now plot on the map all stay point with \n", "starti = stay_points[0][2].strftime('%Y-%m-%d %H:%M:%S')\n", "endi = stay_points[-1][2].strftime('%Y-%m-%d %H:%M:%S')\n", "            \n", "stmt = f''' select lat, lng, time_at from location.raw_location where \n", "            user_device_id = '001' \n", "            and time_at >= '{starti}'\n", "            and time_at <='{endi}'\n", "            order by time_at ASC\n", "        '''\n", "results = conn.execute(text(stmt))\n", "results = results.fetchall()\n", "raw_points = [ r for r in results ]\n", "print (raw_points[0], len(raw_points), len(stay_points))\n", "#now plot on the map all stay point with \n"]}, {"cell_type": "code", "execution_count": 4, "id": "7b1a0528", "metadata": {}, "outputs": [{"data": {"text/html": ["<div style=\"width:100%;\"><div style=\"position:relative;width:100%;height:0;padding-bottom:60%;\"><span style=\"color:#565656\">Make this Notebook Trusted to load map: File -> Trust Notebook</span><iframe srcdoc=\"&lt;!DOCTYPE html&gt;\n", "&lt;html&gt;\n", "&lt;head&gt;\n", "    \n", "    &lt;meta http-equiv=&quot;content-type&quot; content=&quot;text/html; charset=UTF-8&quot; /&gt;\n", "    \n", "        &lt;script&gt;\n", "            L_NO_TOUCH = false;\n", "            L_DISABLE_3D = false;\n", "        &lt;/script&gt;\n", "    \n", "    &lt;style&gt;html, body {width: 100%;height: 100%;margin: 0;padding: 0;}&lt;/style&gt;\n", "    &lt;style&gt;#map {position:absolute;top:0;bottom:0;right:0;left:0;}&lt;/style&gt;\n", "    &lt;script src=&quot;https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js&quot;&gt;&lt;/script&gt;\n", "    &lt;script src=&quot;https://code.jquery.com/jquery-1.12.4.min.js&quot;&gt;&lt;/script&gt;\n", "    &lt;script src=&quot;https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js&quot;&gt;&lt;/script&gt;\n", "    &lt;script src=&quot;https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js&quot;&gt;&lt;/script&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap.min.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css&quot;/&gt;\n", "    \n", "            &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width,\n", "                initial-scale=1.0, maximum-scale=1.0, user-scalable=no&quot; /&gt;\n", "            &lt;style&gt;\n", "                #map_95cf2023ee8a6c9b2bd9ad66177155c1 {\n", "                    position: relative;\n", "                    width: 100.0%;\n", "                    height: 100.0%;\n", "                    left: 0.0%;\n", "                    top: 0.0%;\n", "                }\n", "                .leaflet-container { font-size: 1rem; }\n", "            &lt;/style&gt;\n", "        \n", "&lt;/head&gt;\n", "&lt;body&gt;\n", "    \n", "    \n", "            &lt;div class=&quot;folium-map&quot; id=&quot;map_95cf2023ee8a6c9b2bd9ad66177155c1&quot; &gt;&lt;/div&gt;\n", "        \n", "&lt;/body&gt;\n", "&lt;script&gt;\n", "    \n", "    \n", "            var map_95cf2023ee8a6c9b2bd9ad66177155c1 = L.map(\n", "                &quot;map_95cf2023ee8a6c9b2bd9ad66177155c1&quot;,\n", "                {\n", "                    center: [39.977256777777775, 116.32896233333334],\n", "                    crs: L.CRS.EPSG3857,\n", "                    zoom: 10,\n", "                    zoomControl: true,\n", "                    preferCanvas: false,\n", "                }\n", "            );\n", "\n", "            \n", "\n", "        \n", "    \n", "            var tile_layer_f8d50c169b6dc2e87065be87defce53d = L.tileLayer(\n", "                &quot;https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png&quot;,\n", "                {&quot;attribution&quot;: &quot;Data by \\u0026copy; \\u003ca target=\\&quot;_blank\\&quot; href=\\&quot;http://openstreetmap.org\\&quot;\\u003eOpenStreetMap\\u003c/a\\u003e, under \\u003ca target=\\&quot;_blank\\&quot; href=\\&quot;http://www.openstreetmap.org/copyright\\&quot;\\u003eODbL\\u003c/a\\u003e.&quot;, &quot;detectRetina&quot;: false, &quot;maxNativeZoom&quot;: 18, &quot;maxZoom&quot;: 18, &quot;minZoom&quot;: 0, &quot;noWrap&quot;: false, &quot;opacity&quot;: 1, &quot;subdomains&quot;: &quot;abc&quot;, &quot;tms&quot;: false}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "            var circle_marker_9d09529739f78f1253928cfb2519e7b2 = <PERSON><PERSON>circle<PERSON>(\n", "                [39.978311, 116.327259],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_28a29ce327cf092327f9d78b1033d7f6 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_ed0d7d8b6c13236798daf85896eb3e84 = $(`&lt;div id=&quot;html_ed0d7d8b6c13236798daf85896eb3e84&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_28a29ce327cf092327f9d78b1033d7f6.setContent(html_ed0d7d8b6c13236798daf85896eb3e84);\n", "            \n", "        \n", "\n", "        circle_marker_9d09529739f78f1253928cfb2519e7b2.bindPopup(popup_28a29ce327cf092327f9d78b1033d7f6)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_4104b46497e4bc781a7a4e0bd3d2c39b = <PERSON>.circleMarker(\n", "                [39.977586, 116.326918],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_aa6f82bdff62305ee09a5203eb3d42ac = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_c28797aaf447c78313f4643f45de6768 = $(`&lt;div id=&quot;html_c28797aaf447c78313f4643f45de6768&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_aa6f82bdff62305ee09a5203eb3d42ac.setContent(html_c28797aaf447c78313f4643f45de6768);\n", "            \n", "        \n", "\n", "        circle_marker_4104b46497e4bc781a7a4e0bd3d2c39b.bindPopup(popup_aa6f82bdff62305ee09a5203eb3d42ac)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_8790c3205e2343f05d3a996e00127150 = <PERSON>.circleMarker(\n", "                [39.978093, 116.327214],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_e384314643c35a2cfbe2edfedbd95fc8 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_73cc30cb59c21324942a667885bc167f = $(`&lt;div id=&quot;html_73cc30cb59c21324942a667885bc167f&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_e384314643c35a2cfbe2edfedbd95fc8.setContent(html_73cc30cb59c21324942a667885bc167f);\n", "            \n", "        \n", "\n", "        circle_marker_8790c3205e2343f05d3a996e00127150.bindPopup(popup_e384314643c35a2cfbe2edfedbd95fc8)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_f72ef5c695410f29c15a24bbce031a98 = <PERSON>.circleMarker(\n", "                [39.978971, 116.327242],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_61c9ec585d86c23b5ce675c1a90432d4 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_c6ad10c45b6c6305a685ce29434fe5aa = $(`&lt;div id=&quot;html_c6ad10c45b6c6305a685ce29434fe5aa&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_61c9ec585d86c23b5ce675c1a90432d4.setContent(html_c6ad10c45b6c6305a685ce29434fe5aa);\n", "            \n", "        \n", "\n", "        circle_marker_f72ef5c695410f29c15a24bbce031a98.bindPopup(popup_61c9ec585d86c23b5ce675c1a90432d4)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_9d62aa078f785b5032d45d9513c312ac = <PERSON>.circleMarker(\n", "                [39.979629, 116.327197],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_015d4632d7831d5d2839c5596dc4b29b = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_d30e3ac3f4f215b8e3c9b460f9c20990 = $(`&lt;div id=&quot;html_d30e3ac3f4f215b8e3c9b460f9c20990&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_015d4632d7831d5d2839c5596dc4b29b.setContent(html_d30e3ac3f4f215b8e3c9b460f9c20990);\n", "            \n", "        \n", "\n", "        circle_marker_9d62aa078f785b5032d45d9513c312ac.bindPopup(popup_015d4632d7831d5d2839c5596dc4b29b)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_6ea15197894276f81c3bc24e7a7f3573 = <PERSON>.circle<PERSON>(\n", "                [39.980522, 116.326976],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_0321b8f6f191f6960ea7e9fd9427a8c1 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_eb66fb173af7e93733ef32312db2104c = $(`&lt;div id=&quot;html_eb66fb173af7e93733ef32312db2104c&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_0321b8f6f191f6960ea7e9fd9427a8c1.setContent(html_eb66fb173af7e93733ef32312db2104c);\n", "            \n", "        \n", "\n", "        circle_marker_6ea15197894276f81c3bc24e7a7f3573.bindPopup(popup_0321b8f6f191f6960ea7e9fd9427a8c1)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_19107169aa30ccfe603caf37a5d343b1 = <PERSON><PERSON>circle<PERSON>(\n", "                [39.982175, 116.326752],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_d12feaf3f18128752fec4342246c7239 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_74ec19c0381aca8c829d32146f01f555 = $(`&lt;div id=&quot;html_74ec19c0381aca8c829d32146f01f555&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_d12feaf3f18128752fec4342246c7239.setContent(html_74ec19c0381aca8c829d32146f01f555);\n", "            \n", "        \n", "\n", "        circle_marker_19107169aa30ccfe603caf37a5d343b1.bindPopup(popup_d12feaf3f18128752fec4342246c7239)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_ecd0724279de1c344e840836a3a7401a = <PERSON>.circleMarker(\n", "                [39.98336, 116.326707],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_c810dcbe54199eedf016767be0c158ce = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_738b53b47a58ea9904c24321ba138cb3 = $(`&lt;div id=&quot;html_738b53b47a58ea9904c24321ba138cb3&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_c810dcbe54199eedf016767be0c158ce.setContent(html_738b53b47a58ea9904c24321ba138cb3);\n", "            \n", "        \n", "\n", "        circle_marker_ecd0724279de1c344e840836a3a7401a.bindPopup(popup_c810dcbe54199eedf016767be0c158ce)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_979376d61060c5f8b9c9cb9b2736b2eb = <PERSON>.circle<PERSON>arker(\n", "                [39.984354, 116.326715],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_3c6d4e08a69ae437f7a0f0ea010b963e = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_20fadd8d185b235278b7482f39321387 = $(`&lt;div id=&quot;html_20fadd8d185b235278b7482f39321387&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_3c6d4e08a69ae437f7a0f0ea010b963e.setContent(html_20fadd8d185b235278b7482f39321387);\n", "            \n", "        \n", "\n", "        circle_marker_979376d61060c5f8b9c9cb9b2736b2eb.bindPopup(popup_3c6d4e08a69ae437f7a0f0ea010b963e)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_58037c724456bda0dc417b0704b59062 = <PERSON><PERSON>circle<PERSON>(\n", "                [39.985454, 116.32661],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_5bd706f642a4161a76f6de7f35997976 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_8f67d0e4fad4c1609480e3d6dd83f7f5 = $(`&lt;div id=&quot;html_8f67d0e4fad4c1609480e3d6dd83f7f5&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_5bd706f642a4161a76f6de7f35997976.setContent(html_8f67d0e4fad4c1609480e3d6dd83f7f5);\n", "            \n", "        \n", "\n", "        circle_marker_58037c724456bda0dc417b0704b59062.bindPopup(popup_5bd706f642a4161a76f6de7f35997976)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_f296bae38ea73e16f3645dee55bcfae2 = <PERSON><PERSON>circle<PERSON>(\n", "                [39.986409, 116.326442],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_f31fb2fbd24ac961e542f2ddfefa065f = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_b6fc394782ab35b436d8948809620208 = $(`&lt;div id=&quot;html_b6fc394782ab35b436d8948809620208&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_f31fb2fbd24ac961e542f2ddfefa065f.setContent(html_b6fc394782ab35b436d8948809620208);\n", "            \n", "        \n", "\n", "        circle_marker_f296bae38ea73e16f3645dee55bcfae2.bindPopup(popup_f31fb2fbd24ac961e542f2ddfefa065f)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_da72e2a54016785bb87a5dbd36ed5095 = <PERSON>.circleMarker(\n", "                [39.987398, 116.326519],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_451c000868d4c55ca828eaf951978265 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_a72f2d66359a7afcefe165ed627d2607 = $(`&lt;div id=&quot;html_a72f2d66359a7afcefe165ed627d2607&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_451c000868d4c55ca828eaf951978265.setContent(html_a72f2d66359a7afcefe165ed627d2607);\n", "            \n", "        \n", "\n", "        circle_marker_da72e2a54016785bb87a5dbd36ed5095.bindPopup(popup_451c000868d4c55ca828eaf951978265)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_15bf488f8669aeadcb4042aea01157e5 = <PERSON><PERSON>circle<PERSON>(\n", "                [39.988512, 116.326463],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_acf51e5879f9c0f7b686fce7da32d76a = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_afaa8a63c761292509b98f38e353ab3b = $(`&lt;div id=&quot;html_afaa8a63c761292509b98f38e353ab3b&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_acf51e5879f9c0f7b686fce7da32d76a.setContent(html_afaa8a63c761292509b98f38e353ab3b);\n", "            \n", "        \n", "\n", "        circle_marker_15bf488f8669aeadcb4042aea01157e5.bindPopup(popup_acf51e5879f9c0f7b686fce7da32d76a)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_cc114b0f480088be9f26dc5e95b06d7e = <PERSON>.circleMarker(\n", "                [39.989539, 116.326552],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_fe3e428807f8f58247338bec5b9f2ef0 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_cc8bc80e68486e54d97e5f1c2f19e677 = $(`&lt;div id=&quot;html_cc8bc80e68486e54d97e5f1c2f19e677&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_fe3e428807f8f58247338bec5b9f2ef0.setContent(html_cc8bc80e68486e54d97e5f1c2f19e677);\n", "            \n", "        \n", "\n", "        circle_marker_cc114b0f480088be9f26dc5e95b06d7e.bindPopup(popup_fe3e428807f8f58247338bec5b9f2ef0)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_0a2d240bc6e2d3b3441376d763312a31 = <PERSON><PERSON>circle<PERSON>(\n", "                [39.990497, 116.32642],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_0b83afadcb83d7a6b6fdf62507935b5a = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_e95e983bfb80341b9050b8b5619c7bc7 = $(`&lt;div id=&quot;html_e95e983bfb80341b9050b8b5619c7bc7&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_0b83afadcb83d7a6b6fdf62507935b5a.setContent(html_e95e983bfb80341b9050b8b5619c7bc7);\n", "            \n", "        \n", "\n", "        circle_marker_0a2d240bc6e2d3b3441376d763312a31.bindPopup(popup_0b83afadcb83d7a6b6fdf62507935b5a)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_54419b4c3f84e5909c7eb3c52bdd37c9 = <PERSON>.circle<PERSON>er(\n", "                [39.991152, 116.326348],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_f30725c8140c891846098f4d69a3c0fb = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_bdaeec1791d2977b440158d1bca78094 = $(`&lt;div id=&quot;html_bdaeec1791d2977b440158d1bca78094&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_f30725c8140c891846098f4d69a3c0fb.setContent(html_bdaeec1791d2977b440158d1bca78094);\n", "            \n", "        \n", "\n", "        circle_marker_54419b4c3f84e5909c7eb3c52bdd37c9.bindPopup(popup_f30725c8140c891846098f4d69a3c0fb)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_f2a904f0b27ab580fb35e9da40623cc4 = <PERSON><PERSON>circle<PERSON>arker(\n", "                [39.991574, 116.326394],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_e4eec5915885a23619a8067121a2ae03 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_9504e616e0ee5b9d78c50364b2aec972 = $(`&lt;div id=&quot;html_9504e616e0ee5b9d78c50364b2aec972&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_e4eec5915885a23619a8067121a2ae03.setContent(html_9504e616e0ee5b9d78c50364b2aec972);\n", "            \n", "        \n", "\n", "        circle_marker_f2a904f0b27ab580fb35e9da40623cc4.bindPopup(popup_e4eec5915885a23619a8067121a2ae03)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_9646ffa76de533c36e2577e045d79021 = <PERSON><PERSON>circle<PERSON>(\n", "                [39.992483, 116.32635],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_7efbe0bc4f55cbfd5ed4512eac9e6e3f = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_7b485d0753f15a9893622c30fac7d1d1 = $(`&lt;div id=&quot;html_7b485d0753f15a9893622c30fac7d1d1&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_7efbe0bc4f55cbfd5ed4512eac9e6e3f.setContent(html_7b485d0753f15a9893622c30fac7d1d1);\n", "            \n", "        \n", "\n", "        circle_marker_9646ffa76de533c36e2577e045d79021.bindPopup(popup_7efbe0bc4f55cbfd5ed4512eac9e6e3f)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_5b002f09d75c7655f0868c0cc1660203 = L.circleMarker(\n", "                [39.993332, 116.326481],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_815b440a3a9dc39542991d2a14067312 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_900e747d9c9fbc6378bf72dd9e39e885 = $(`&lt;div id=&quot;html_900e747d9c9fbc6378bf72dd9e39e885&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_815b440a3a9dc39542991d2a14067312.setContent(html_900e747d9c9fbc6378bf72dd9e39e885);\n", "            \n", "        \n", "\n", "        circle_marker_5b002f09d75c7655f0868c0cc1660203.bindPopup(popup_815b440a3a9dc39542991d2a14067312)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_b3daf00878bd6a57570f036045912a7c = <PERSON>.circleMarker(\n", "                [39.994398, 116.326473],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_0b512fb9e1b4ff53760a6763b01a0d8b = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_80736e60f90f2aa4e65ec2a6249192a9 = $(`&lt;div id=&quot;html_80736e60f90f2aa4e65ec2a6249192a9&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_0b512fb9e1b4ff53760a6763b01a0d8b.setContent(html_80736e60f90f2aa4e65ec2a6249192a9);\n", "            \n", "        \n", "\n", "        circle_marker_b3daf00878bd6a57570f036045912a7c.bindPopup(popup_0b512fb9e1b4ff53760a6763b01a0d8b)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_16fae5a79d0f06b960d5092562c2ccd3 = <PERSON>.circleMarker(\n", "                [39.995183, 116.32649],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_6d7815428fc06a5a6066207c2ed52053 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_f47840f620e54e4eab3a24e65be01e7c = $(`&lt;div id=&quot;html_f47840f620e54e4eab3a24e65be01e7c&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_6d7815428fc06a5a6066207c2ed52053.setContent(html_f47840f620e54e4eab3a24e65be01e7c);\n", "            \n", "        \n", "\n", "        circle_marker_16fae5a79d0f06b960d5092562c2ccd3.bindPopup(popup_6d7815428fc06a5a6066207c2ed52053)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_c1605ab9992acf2e1b0ae44e0fb9ed25 = <PERSON><PERSON>circle<PERSON>er(\n", "                [39.995959, 116.326688],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_a112b6a5fd50cc2bf923c764c829069e = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_a793312742248ca18ae5fc754fc8a8ab = $(`&lt;div id=&quot;html_a793312742248ca18ae5fc754fc8a8ab&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_a112b6a5fd50cc2bf923c764c829069e.setContent(html_a793312742248ca18ae5fc754fc8a8ab);\n", "            \n", "        \n", "\n", "        circle_marker_c1605ab9992acf2e1b0ae44e0fb9ed25.bindPopup(popup_a112b6a5fd50cc2bf923c764c829069e)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_f3d8aedf6d109d98de7dead0affb53f5 = <PERSON>.circle<PERSON>er(\n", "                [39.997015, 116.326629],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_7f8268f453018a83c1740b50f15c0586 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_fe3160541a7951a486836d19d39a6ff1 = $(`&lt;div id=&quot;html_fe3160541a7951a486836d19d39a6ff1&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_7f8268f453018a83c1740b50f15c0586.setContent(html_fe3160541a7951a486836d19d39a6ff1);\n", "            \n", "        \n", "\n", "        circle_marker_f3d8aedf6d109d98de7dead0affb53f5.bindPopup(popup_7f8268f453018a83c1740b50f15c0586)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_e56f7cfae6ee22834cdc3a9a8b557856 = <PERSON>.circleMarker(\n", "                [39.998039, 116.326615],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_7baa39c8c562017943e9cd28a051402a = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_f29171e715ce109e53c438933cd30ad0 = $(`&lt;div id=&quot;html_f29171e715ce109e53c438933cd30ad0&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_7baa39c8c562017943e9cd28a051402a.setContent(html_f29171e715ce109e53c438933cd30ad0);\n", "            \n", "        \n", "\n", "        circle_marker_e56f7cfae6ee22834cdc3a9a8b557856.bindPopup(popup_7baa39c8c562017943e9cd28a051402a)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_00a711245f950c3659b3cc0891b0d006 = <PERSON>.circleMarker(\n", "                [39.999337, 116.326513],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_1c4c4a89f754b8a320e6e6de86f79c4c = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_fd1b6605a8bb034bd1b90cffbcde6769 = $(`&lt;div id=&quot;html_fd1b6605a8bb034bd1b90cffbcde6769&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_1c4c4a89f754b8a320e6e6de86f79c4c.setContent(html_fd1b6605a8bb034bd1b90cffbcde6769);\n", "            \n", "        \n", "\n", "        circle_marker_00a711245f950c3659b3cc0891b0d006.bindPopup(popup_1c4c4a89f754b8a320e6e6de86f79c4c)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_754cb755938f3b62b9c247c3bc73916a = <PERSON>.circle<PERSON>arker(\n", "                [39.999808, 116.325582],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_d907ff35730f9cd8aee77293bec28d53 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_7653c1b1ac11fb234830ea7709fbd6a6 = $(`&lt;div id=&quot;html_7653c1b1ac11fb234830ea7709fbd6a6&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_d907ff35730f9cd8aee77293bec28d53.setContent(html_7653c1b1ac11fb234830ea7709fbd6a6);\n", "            \n", "        \n", "\n", "        circle_marker_754cb755938f3b62b9c247c3bc73916a.bindPopup(popup_d907ff35730f9cd8aee77293bec28d53)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_ca86b844178dea2d7848add8647b289d = <PERSON><PERSON>circle<PERSON>(\n", "                [39.999815, 116.324254],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_9c468a6ad0ca2891e4c27af887e2d9e1 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_14e24168e8a4557ec52b3ab9454e14e6 = $(`&lt;div id=&quot;html_14e24168e8a4557ec52b3ab9454e14e6&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_9c468a6ad0ca2891e4c27af887e2d9e1.setContent(html_14e24168e8a4557ec52b3ab9454e14e6);\n", "            \n", "        \n", "\n", "        circle_marker_ca86b844178dea2d7848add8647b289d.bindPopup(popup_9c468a6ad0ca2891e4c27af887e2d9e1)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_82e176da0ba57ba822e2990640d7e141 = <PERSON><PERSON>circle<PERSON>(\n", "                [39.999799, 116.322821],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_bad1baac3c385a98060fd84ffcbf2814 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_ad28a69b5a0016292bb11a7df0a95f55 = $(`&lt;div id=&quot;html_ad28a69b5a0016292bb11a7df0a95f55&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_bad1baac3c385a98060fd84ffcbf2814.setContent(html_ad28a69b5a0016292bb11a7df0a95f55);\n", "            \n", "        \n", "\n", "        circle_marker_82e176da0ba57ba822e2990640d7e141.bindPopup(popup_bad1baac3c385a98060fd84ffcbf2814)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_52284931d1c77ae32abb20f2849155de = <PERSON><PERSON>circle<PERSON>(\n", "                [40.000379, 116.321901],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_411f1a8765e604750eb8e1f9d8e46bf1 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_ce36e3b1d9d296ac46b2a1eb6e059e29 = $(`&lt;div id=&quot;html_ce36e3b1d9d296ac46b2a1eb6e059e29&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_411f1a8765e604750eb8e1f9d8e46bf1.setContent(html_ce36e3b1d9d296ac46b2a1eb6e059e29);\n", "            \n", "        \n", "\n", "        circle_marker_52284931d1c77ae32abb20f2849155de.bindPopup(popup_411f1a8765e604750eb8e1f9d8e46bf1)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_0d2f9431e6f8e13f1367fe2bab572e19 = <PERSON>.circle<PERSON>arker(\n", "                [40.001418, 116.321696],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_fa7e000e3eb6018aa4f8112d88c7de85 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_e266c874c3e17af4d1c1b97fbc74dbf6 = $(`&lt;div id=&quot;html_e266c874c3e17af4d1c1b97fbc74dbf6&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_fa7e000e3eb6018aa4f8112d88c7de85.setContent(html_e266c874c3e17af4d1c1b97fbc74dbf6);\n", "            \n", "        \n", "\n", "        circle_marker_0d2f9431e6f8e13f1367fe2bab572e19.bindPopup(popup_fa7e000e3eb6018aa4f8112d88c7de85)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_aedaa352d4eeb12010d84db0149f07a4 = <PERSON>.circleMarker(\n", "                [40.002515, 116.321562],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_2eb2bb89db836fa78228612d5ff46262 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_b8dec7b7be51530d70655f9320f4d38b = $(`&lt;div id=&quot;html_b8dec7b7be51530d70655f9320f4d38b&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_2eb2bb89db836fa78228612d5ff46262.setContent(html_b8dec7b7be51530d70655f9320f4d38b);\n", "            \n", "        \n", "\n", "        circle_marker_aedaa352d4eeb12010d84db0149f07a4.bindPopup(popup_2eb2bb89db836fa78228612d5ff46262)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_9fccb7690ecafd165b557b26b8a9a564 = <PERSON>.circle<PERSON>arker(\n", "                [40.003572, 116.321455],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_d78c65a99140c16afd85a39b4c3f44dc = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_b15d7a0139d96af7bd1bed4c4179fa70 = $(`&lt;div id=&quot;html_b15d7a0139d96af7bd1bed4c4179fa70&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_d78c65a99140c16afd85a39b4c3f44dc.setContent(html_b15d7a0139d96af7bd1bed4c4179fa70);\n", "            \n", "        \n", "\n", "        circle_marker_9fccb7690ecafd165b557b26b8a9a564.bindPopup(popup_d78c65a99140c16afd85a39b4c3f44dc)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_25aaa0b85fc7df3d94adfb4f5cedf65f = <PERSON>.circle<PERSON>arker(\n", "                [40.004571, 116.32134],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_4946e7c4aaf384212cb0c6572a8f335f = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_fac07774df0c20449fc8fd344286209d = $(`&lt;div id=&quot;html_fac07774df0c20449fc8fd344286209d&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_4946e7c4aaf384212cb0c6572a8f335f.setContent(html_fac07774df0c20449fc8fd344286209d);\n", "            \n", "        \n", "\n", "        circle_marker_25aaa0b85fc7df3d94adfb4f5cedf65f.bindPopup(popup_4946e7c4aaf384212cb0c6572a8f335f)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_84be2f34647add2389eb3f90f512e24f = <PERSON><PERSON>circle<PERSON>(\n", "                [40.004827, 116.320101],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_34cbb0d88cec45ed6c9b3609417cf131 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_a9b603dd4449ae35c62749271822bca8 = $(`&lt;div id=&quot;html_a9b603dd4449ae35c62749271822bca8&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_34cbb0d88cec45ed6c9b3609417cf131.setContent(html_a9b603dd4449ae35c62749271822bca8);\n", "            \n", "        \n", "\n", "        circle_marker_84be2f34647add2389eb3f90f512e24f.bindPopup(popup_34cbb0d88cec45ed6c9b3609417cf131)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_e85009508a87cd646df19b865d956f46 = <PERSON>.circle<PERSON>er(\n", "                [40.004936, 116.318786],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_ddf18a7342a27ce5572881ad4b77d963 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_844ad389c6457a874d0c89be5d39345c = $(`&lt;div id=&quot;html_844ad389c6457a874d0c89be5d39345c&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_ddf18a7342a27ce5572881ad4b77d963.setContent(html_844ad389c6457a874d0c89be5d39345c);\n", "            \n", "        \n", "\n", "        circle_marker_e85009508a87cd646df19b865d956f46.bindPopup(popup_ddf18a7342a27ce5572881ad4b77d963)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_6c567e749042a9f55e2c16d230fad6db = <PERSON>.circle<PERSON>er(\n", "                [40.005336, 116.317808],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_59d4298c9d9d5bb1044650f3df968d30 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_c235cae48a15e70a13a9e6894e1ce248 = $(`&lt;div id=&quot;html_c235cae48a15e70a13a9e6894e1ce248&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_59d4298c9d9d5bb1044650f3df968d30.setContent(html_c235cae48a15e70a13a9e6894e1ce248);\n", "            \n", "        \n", "\n", "        circle_marker_6c567e749042a9f55e2c16d230fad6db.bindPopup(popup_59d4298c9d9d5bb1044650f3df968d30)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_ee2a7514bf22cdd9e7c9f56248bcef84 = L.circleMarker(\n", "                [40.006436, 116.317701],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_8100b1bef020cb1742d07ec21086f0b8 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_033946d222eb0427ee0cfa981ee2c156 = $(`&lt;div id=&quot;html_033946d222eb0427ee0cfa981ee2c156&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_8100b1bef020cb1742d07ec21086f0b8.setContent(html_033946d222eb0427ee0cfa981ee2c156);\n", "            \n", "        \n", "\n", "        circle_marker_ee2a7514bf22cdd9e7c9f56248bcef84.bindPopup(popup_8100b1bef020cb1742d07ec21086f0b8)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_a2f400a7304570264c63cff603656616 = <PERSON><PERSON>circle<PERSON>(\n", "                [40.00742, 116.317659],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_2bc3a86a6d9ac1ff036dc3ef59a92401 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_cc652da1854e1f80e249fdcb4c1d8728 = $(`&lt;div id=&quot;html_cc652da1854e1f80e249fdcb4c1d8728&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_2bc3a86a6d9ac1ff036dc3ef59a92401.setContent(html_cc652da1854e1f80e249fdcb4c1d8728);\n", "            \n", "        \n", "\n", "        circle_marker_a2f400a7304570264c63cff603656616.bindPopup(popup_2bc3a86a6d9ac1ff036dc3ef59a92401)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_5a029aa76131e76cc52366fe358bd536 = <PERSON><PERSON>circle<PERSON>(\n", "                [40.008565, 116.317695],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_7924c9a08125985b065ff7c2fe166f02 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_a38c51524c2491e94d4222abd5f8cf2c = $(`&lt;div id=&quot;html_a38c51524c2491e94d4222abd5f8cf2c&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_7924c9a08125985b065ff7c2fe166f02.setContent(html_a38c51524c2491e94d4222abd5f8cf2c);\n", "            \n", "        \n", "\n", "        circle_marker_5a029aa76131e76cc52366fe358bd536.bindPopup(popup_7924c9a08125985b065ff7c2fe166f02)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_aabb25640cd18f8819ff0fab7fdb1298 = <PERSON>.circle<PERSON>arker(\n", "                [40.008804, 116.316652],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_729b83118e7e97674cd003bc32ae9398 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_e8980d0881c1783e6108654b26a63df9 = $(`&lt;div id=&quot;html_e8980d0881c1783e6108654b26a63df9&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_729b83118e7e97674cd003bc32ae9398.setContent(html_e8980d0881c1783e6108654b26a63df9);\n", "            \n", "        \n", "\n", "        circle_marker_aabb25640cd18f8819ff0fab7fdb1298.bindPopup(popup_729b83118e7e97674cd003bc32ae9398)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_4d6f1030c5ae6f255e8c1f4df4a05130 = <PERSON>.circle<PERSON>arker(\n", "                [40.009559, 116.31591],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_32a81eb0854d3d9988d6a6e111ccba2a = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_80c8323ee016ce1bc7ac490b8700ee87 = $(`&lt;div id=&quot;html_80c8323ee016ce1bc7ac490b8700ee87&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_32a81eb0854d3d9988d6a6e111ccba2a.setContent(html_80c8323ee016ce1bc7ac490b8700ee87);\n", "            \n", "        \n", "\n", "        circle_marker_4d6f1030c5ae6f255e8c1f4df4a05130.bindPopup(popup_32a81eb0854d3d9988d6a6e111ccba2a)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_189a9ae6de8798b782209741ed2b11f3 = <PERSON>.circle<PERSON>arker(\n", "                [40.009983, 116.314836],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_4f4922357e7c685a2ea97c3d6343893f = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_b07448f99c9428c9448edd28b341cf70 = $(`&lt;div id=&quot;html_b07448f99c9428c9448edd28b341cf70&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_4f4922357e7c685a2ea97c3d6343893f.setContent(html_b07448f99c9428c9448edd28b341cf70);\n", "            \n", "        \n", "\n", "        circle_marker_189a9ae6de8798b782209741ed2b11f3.bindPopup(popup_4f4922357e7c685a2ea97c3d6343893f)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_c98d29f65c7869cda58b77f32be741ff = L.circleMarker(\n", "                [40.009802, 116.313247],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_40520a0f1ac98ca4f6d9ad1eff14a702 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_accda0475e7f9bfcc514847ccd583085 = $(`&lt;div id=&quot;html_accda0475e7f9bfcc514847ccd583085&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_40520a0f1ac98ca4f6d9ad1eff14a702.setContent(html_accda0475e7f9bfcc514847ccd583085);\n", "            \n", "        \n", "\n", "        circle_marker_c98d29f65c7869cda58b77f32be741ff.bindPopup(popup_40520a0f1ac98ca4f6d9ad1eff14a702)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_88f09fd8cd5004aa689850f65047c592 = <PERSON>.circle<PERSON>(\n", "                [40.009904, 116.312617],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_13a204afcb83923a7918f3b054489960 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_8e4177fdf8f1786bb4f30b469aa796a4 = $(`&lt;div id=&quot;html_8e4177fdf8f1786bb4f30b469aa796a4&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_13a204afcb83923a7918f3b054489960.setContent(html_8e4177fdf8f1786bb4f30b469aa796a4);\n", "            \n", "        \n", "\n", "        circle_marker_88f09fd8cd5004aa689850f65047c592.bindPopup(popup_13a204afcb83923a7918f3b054489960)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_990ede595adeb4a27ac38d4d7f4353b6 = <PERSON><PERSON>circle<PERSON>(\n", "                [40.010882, 116.312587],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_e61f5e26c8146fd4c6fe44550de8c538 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_513e1249155e4f384e099c1d7944a93e = $(`&lt;div id=&quot;html_513e1249155e4f384e099c1d7944a93e&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_e61f5e26c8146fd4c6fe44550de8c538.setContent(html_513e1249155e4f384e099c1d7944a93e);\n", "            \n", "        \n", "\n", "        circle_marker_990ede595adeb4a27ac38d4d7f4353b6.bindPopup(popup_e61f5e26c8146fd4c6fe44550de8c538)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_49fc6d9d849f229972247dba8087fbe4 = <PERSON>.circleMarker(\n", "                [40.011313, 116.312443],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_b9027439e0a1fe3c8270ea27f8e64b33 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_e113dc18f56ecd606a635d8b6037166d = $(`&lt;div id=&quot;html_e113dc18f56ecd606a635d8b6037166d&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_b9027439e0a1fe3c8270ea27f8e64b33.setContent(html_e113dc18f56ecd606a635d8b6037166d);\n", "            \n", "        \n", "\n", "        circle_marker_49fc6d9d849f229972247dba8087fbe4.bindPopup(popup_b9027439e0a1fe3c8270ea27f8e64b33)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_f817803a1a00168155ca7e319d22e492 = <PERSON>.circleMarker(\n", "                [40.011729, 116.311952],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_47e7ba6863556afa2be6c03f3ccf5d69 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_aca86e3740acafe6e8cb814ff5f08d3d = $(`&lt;div id=&quot;html_aca86e3740acafe6e8cb814ff5f08d3d&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_47e7ba6863556afa2be6c03f3ccf5d69.setContent(html_aca86e3740acafe6e8cb814ff5f08d3d);\n", "            \n", "        \n", "\n", "        circle_marker_f817803a1a00168155ca7e319d22e492.bindPopup(popup_47e7ba6863556afa2be6c03f3ccf5d69)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_7a89586df6633b49a5dc6830cc8874e5 = <PERSON>.circleMarker(\n", "                [40.012736, 116.311887],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_cbe2e678a03b09b542f83eec56b2d9c2 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_4bac247356033460a64ad657fdff0d28 = $(`&lt;div id=&quot;html_4bac247356033460a64ad657fdff0d28&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_cbe2e678a03b09b542f83eec56b2d9c2.setContent(html_4bac247356033460a64ad657fdff0d28);\n", "            \n", "        \n", "\n", "        circle_marker_7a89586df6633b49a5dc6830cc8874e5.bindPopup(popup_cbe2e678a03b09b542f83eec56b2d9c2)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_a1f7da138d73fd6ccfdb15fbba145741 = <PERSON>.circle<PERSON>(\n", "                [40.013827, 116.311729],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_ef4561f4e24db94f3d8d2a59b91ddcd5 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_fa9700132ee72842da91f9864ea35cd9 = $(`&lt;div id=&quot;html_fa9700132ee72842da91f9864ea35cd9&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_ef4561f4e24db94f3d8d2a59b91ddcd5.setContent(html_fa9700132ee72842da91f9864ea35cd9);\n", "            \n", "        \n", "\n", "        circle_marker_a1f7da138d73fd6ccfdb15fbba145741.bindPopup(popup_ef4561f4e24db94f3d8d2a59b91ddcd5)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_95c15c610168f9f9f94a7c66058113e3 = <PERSON>.circleMarker(\n", "                [40.014826, 116.311333],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_511d48aa5f72d086b37e322c5952c13c = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_169f70b490c1492329021f1154810cb0 = $(`&lt;div id=&quot;html_169f70b490c1492329021f1154810cb0&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_511d48aa5f72d086b37e322c5952c13c.setContent(html_169f70b490c1492329021f1154810cb0);\n", "            \n", "        \n", "\n", "        circle_marker_95c15c610168f9f9f94a7c66058113e3.bindPopup(popup_511d48aa5f72d086b37e322c5952c13c)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_5b1d3fddebea8a3059f0109c2e81ff6f = <PERSON>.circle<PERSON>arker(\n", "                [40.015778, 116.310762],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_b589e32f873095adea1be623f095f3b6 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_db90fd3d1931b97e042d05111ed576de = $(`&lt;div id=&quot;html_db90fd3d1931b97e042d05111ed576de&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_b589e32f873095adea1be623f095f3b6.setContent(html_db90fd3d1931b97e042d05111ed576de);\n", "            \n", "        \n", "\n", "        circle_marker_5b1d3fddebea8a3059f0109c2e81ff6f.bindPopup(popup_b589e32f873095adea1be623f095f3b6)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_f098aee21229c5db09afe9fda2bcee39 = <PERSON><PERSON>circle<PERSON>arker(\n", "                [40.016569, 116.309919],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_5a4dfe31f94d17f381ab0e395d64e0f9 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_3846e72d702fea1e71827628b7093024 = $(`&lt;div id=&quot;html_3846e72d702fea1e71827628b7093024&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_5a4dfe31f94d17f381ab0e395d64e0f9.setContent(html_3846e72d702fea1e71827628b7093024);\n", "            \n", "        \n", "\n", "        circle_marker_f098aee21229c5db09afe9fda2bcee39.bindPopup(popup_5a4dfe31f94d17f381ab0e395d64e0f9)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_76f18203584a81510c130be8e7ad311b = <PERSON><PERSON>circle<PERSON>(\n", "                [40.016383, 116.308486],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_9b4e12b551c7430c250b508805d95369 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_2b9fea29d90b8142f74074ee29c2f3c5 = $(`&lt;div id=&quot;html_2b9fea29d90b8142f74074ee29c2f3c5&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_9b4e12b551c7430c250b508805d95369.setContent(html_2b9fea29d90b8142f74074ee29c2f3c5);\n", "            \n", "        \n", "\n", "        circle_marker_76f18203584a81510c130be8e7ad311b.bindPopup(popup_9b4e12b551c7430c250b508805d95369)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_4b01baebcd2b30e4aa6f78a1cac6720b = <PERSON><PERSON>circle<PERSON>arker(\n", "                [40.016123, 116.307134],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_73b52256f89989abe373eaded35237db = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_1ab8a05e23b49a652257e9f0fee54f67 = $(`&lt;div id=&quot;html_1ab8a05e23b49a652257e9f0fee54f67&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_73b52256f89989abe373eaded35237db.setContent(html_1ab8a05e23b49a652257e9f0fee54f67);\n", "            \n", "        \n", "\n", "        circle_marker_4b01baebcd2b30e4aa6f78a1cac6720b.bindPopup(popup_73b52256f89989abe373eaded35237db)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_75481c619399437454b9d437ebf5ad12 = <PERSON><PERSON>circle<PERSON>(\n", "                [40.015948, 116.306097],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_a7cb8574ac0be6a2f27e24e01f7b8ee2 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_4bbd18a83f274e1148a1b430b01c24ab = $(`&lt;div id=&quot;html_4bbd18a83f274e1148a1b430b01c24ab&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_a7cb8574ac0be6a2f27e24e01f7b8ee2.setContent(html_4bbd18a83f274e1148a1b430b01c24ab);\n", "            \n", "        \n", "\n", "        circle_marker_75481c619399437454b9d437ebf5ad12.bindPopup(popup_a7cb8574ac0be6a2f27e24e01f7b8ee2)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_f5e8863cf851093a7a38ff9c357aae77 = <PERSON>.circleMarker(\n", "                [40.01596, 116.30612],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_51facfe0f165545339cd683f9baa10fc = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_de940833423b7eedec97fb13793e7944 = $(`&lt;div id=&quot;html_de940833423b7eedec97fb13793e7944&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_51facfe0f165545339cd683f9baa10fc.setContent(html_de940833423b7eedec97fb13793e7944);\n", "            \n", "        \n", "\n", "        circle_marker_f5e8863cf851093a7a38ff9c357aae77.bindPopup(popup_51facfe0f165545339cd683f9baa10fc)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_539669f45f8eb468bde29be6fab02b6c = <PERSON>.circle<PERSON>arker(\n", "                [40.015881, 116.305646],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_26ddeb8fee5f4db0f457a902330bb299 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_627fe540b68fa2a3020a4c8fd15a381d = $(`&lt;div id=&quot;html_627fe540b68fa2a3020a4c8fd15a381d&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_26ddeb8fee5f4db0f457a902330bb299.setContent(html_627fe540b68fa2a3020a4c8fd15a381d);\n", "            \n", "        \n", "\n", "        circle_marker_539669f45f8eb468bde29be6fab02b6c.bindPopup(popup_26ddeb8fee5f4db0f457a902330bb299)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_68929e71bc2793bf85afd6fafacd9d95 = <PERSON><PERSON>circle<PERSON>(\n", "                [40.015984, 116.306517],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_28ac14844224e8f2b3c4863ada0abeee = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_e35081c2716a461c373328191446f22b = $(`&lt;div id=&quot;html_e35081c2716a461c373328191446f22b&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_28ac14844224e8f2b3c4863ada0abeee.setContent(html_e35081c2716a461c373328191446f22b);\n", "            \n", "        \n", "\n", "        circle_marker_68929e71bc2793bf85afd6fafacd9d95.bindPopup(popup_28ac14844224e8f2b3c4863ada0abeee)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_d73b72484a37fcb74a11b256114b6b37 = <PERSON>.circle<PERSON>(\n", "                [40.015993, 116.306583],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_4a4c01b35416e971a3e1f296209adc47 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_10cc3d58e64380300ec0f1cfe82ec3f4 = $(`&lt;div id=&quot;html_10cc3d58e64380300ec0f1cfe82ec3f4&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_4a4c01b35416e971a3e1f296209adc47.setContent(html_10cc3d58e64380300ec0f1cfe82ec3f4);\n", "            \n", "        \n", "\n", "        circle_marker_d73b72484a37fcb74a11b256114b6b37.bindPopup(popup_4a4c01b35416e971a3e1f296209adc47)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_0afb48b9543d839d4034d5a61318990a = <PERSON><PERSON>circle<PERSON>(\n", "                [40.015996, 116.306591],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_f6a39579c9d1b015e2c5b0f22426134a = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_9ae4973879440b73654752edccf92c99 = $(`&lt;div id=&quot;html_9ae4973879440b73654752edccf92c99&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_f6a39579c9d1b015e2c5b0f22426134a.setContent(html_9ae4973879440b73654752edccf92c99);\n", "            \n", "        \n", "\n", "        circle_marker_0afb48b9543d839d4034d5a61318990a.bindPopup(popup_f6a39579c9d1b015e2c5b0f22426134a)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_bf8b04e7cdd98fc2f8f02cad67301123 = L.circleMarker(\n", "                [40.015943, 116.30607],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_37c518b5fc165a4595b42732c3564e0d = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_8b09bda842adc3e41e4ec9b4155659d8 = $(`&lt;div id=&quot;html_8b09bda842adc3e41e4ec9b4155659d8&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_37c518b5fc165a4595b42732c3564e0d.setContent(html_8b09bda842adc3e41e4ec9b4155659d8);\n", "            \n", "        \n", "\n", "        circle_marker_bf8b04e7cdd98fc2f8f02cad67301123.bindPopup(popup_37c518b5fc165a4595b42732c3564e0d)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_a25eb35e9fd2dc1266910e442aa330ff = <PERSON><PERSON>circle<PERSON>(\n", "                [40.015234, 116.305487],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_fc319b1fea309d1457404d87223f6ecb = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_d7973593a6ec1d824e9940aa8f82c39b = $(`&lt;div id=&quot;html_d7973593a6ec1d824e9940aa8f82c39b&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_fc319b1fea309d1457404d87223f6ecb.setContent(html_d7973593a6ec1d824e9940aa8f82c39b);\n", "            \n", "        \n", "\n", "        circle_marker_a25eb35e9fd2dc1266910e442aa330ff.bindPopup(popup_fc319b1fea309d1457404d87223f6ecb)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_fa3110754d98a35f37e346a7302504e8 = <PERSON>.circleMarker(\n", "                [40.014175, 116.305708],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_c0056e87e6f2342d44b94b520297de5b = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_f537ae75d92cfa4563523280169af539 = $(`&lt;div id=&quot;html_f537ae75d92cfa4563523280169af539&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_c0056e87e6f2342d44b94b520297de5b.setContent(html_f537ae75d92cfa4563523280169af539);\n", "            \n", "        \n", "\n", "        circle_marker_fa3110754d98a35f37e346a7302504e8.bindPopup(popup_c0056e87e6f2342d44b94b520297de5b)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_66154780268ce748218ce316b5bfaa3a = <PERSON><PERSON>circle<PERSON>(\n", "                [40.01354, 116.306489],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#fc2403&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;##fc2403&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 1, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_ecddded149f29b8d02da50652c41bb82 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_c83cda2a7d7bb15cc1c0da53168e4df7 = $(`&lt;div id=&quot;html_c83cda2a7d7bb15cc1c0da53168e4df7&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_ecddded149f29b8d02da50652c41bb82.setContent(html_c83cda2a7d7bb15cc1c0da53168e4df7);\n", "            \n", "        \n", "\n", "        circle_marker_66154780268ce748218ce316b5bfaa3a.bindPopup(popup_ecddded149f29b8d02da50652c41bb82)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_52beba30c87cd5743fb7c3d429f036c1 = <PERSON><PERSON>circle<PERSON>(\n", "                [39.977256777777775, 116.32896233333334],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#0b03fc&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;#0b03fc&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 5, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_eefdde48813e3799bc81badc27212e75 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_453eb7e16cc51f283b32c0a7eda93bbf = $(`&lt;div id=&quot;html_453eb7e16cc51f283b32c0a7eda93bbf&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_eefdde48813e3799bc81badc27212e75.setContent(html_453eb7e16cc51f283b32c0a7eda93bbf);\n", "            \n", "        \n", "\n", "        circle_marker_52beba30c87cd5743fb7c3d429f036c1.bindPopup(popup_eefdde48813e3799bc81badc27212e75)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_deea0aa905dad447c4941e8394c9c502 = <PERSON><PERSON>circle<PERSON>(\n", "                [39.977256777777775, 116.32896233333334],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#0b03fc&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;#0b03fc&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 5, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_64eab7e5ac89305e01f7db5de44aa938 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_a680be83659a00ada35a675c75178c20 = $(`&lt;div id=&quot;html_a680be83659a00ada35a675c75178c20&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_64eab7e5ac89305e01f7db5de44aa938.setContent(html_a680be83659a00ada35a675c75178c20);\n", "            \n", "        \n", "\n", "        circle_marker_deea0aa905dad447c4941e8394c9c502.bindPopup(popup_64eab7e5ac89305e01f7db5de44aa938)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_e8e53ac92fdfec065836e9a39e291602 = <PERSON>.circleMarker(\n", "                [40.014436086956515, 116.30585956521738],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#0b03fc&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;#0b03fc&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 5, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_5c1a6e2d2e797459ab19262cd860b510 = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_ec91e720a324bee544d98daa6d08bf37 = $(`&lt;div id=&quot;html_ec91e720a324bee544d98daa6d08bf37&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_5c1a6e2d2e797459ab19262cd860b510.setContent(html_ec91e720a324bee544d98daa6d08bf37);\n", "            \n", "        \n", "\n", "        circle_marker_e8e53ac92fdfec065836e9a39e291602.bindPopup(popup_5c1a6e2d2e797459ab19262cd860b510)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_43ac49495256891912488c9b661e3b1f = <PERSON>.circle<PERSON>er(\n", "                [40.014436086956515, 116.30585956521738],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#0b03fc&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;#0b03fc&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 5, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_25f0038a1c552027b91d1650aa02785b = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_8f0835d06b4abd7a0f391db513264462 = $(`&lt;div id=&quot;html_8f0835d06b4abd7a0f391db513264462&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_25f0038a1c552027b91d1650aa02785b.setContent(html_8f0835d06b4abd7a0f391db513264462);\n", "            \n", "        \n", "\n", "        circle_marker_43ac49495256891912488c9b661e3b1f.bindPopup(popup_25f0038a1c552027b91d1650aa02785b)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            var circle_marker_2e2743cdcd7234e84d907535caf1f4f1 = <PERSON><PERSON>circle<PERSON>(\n", "                [40.01364757068064, 116.30663940837665],\n", "                {&quot;bubblingMouseEvents&quot;: true, &quot;color&quot;: &quot;#0b03fc&quot;, &quot;dashArray&quot;: null, &quot;dashOffset&quot;: null, &quot;fill&quot;: true, &quot;fillColor&quot;: &quot;#0b03fc&quot;, &quot;fillOpacity&quot;: 0.2, &quot;fillRule&quot;: &quot;evenodd&quot;, &quot;lineCap&quot;: &quot;round&quot;, &quot;lineJoin&quot;: &quot;round&quot;, &quot;opacity&quot;: 1.0, &quot;radius&quot;: 5, &quot;stroke&quot;: true, &quot;weight&quot;: 3}\n", "            ).addTo(map_95cf2023ee8a6c9b2bd9ad66177155c1);\n", "        \n", "    \n", "        var popup_e3420dbb0a6fd10505ce18dd62e6ea8c = L.popup({&quot;maxWidth&quot;: &quot;100%&quot;});\n", "\n", "        \n", "            \n", "                var html_7b2f7d3f585a05481d9e9a3fa76d70a7 = $(`&lt;div id=&quot;html_7b2f7d3f585a05481d9e9a3fa76d70a7&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;Laurelhurst Park&lt;/div&gt;`)[0];\n", "                popup_e3420dbb0a6fd10505ce18dd62e6ea8c.setContent(html_7b2f7d3f585a05481d9e9a3fa76d70a7);\n", "            \n", "        \n", "\n", "        circle_marker_2e2743cdcd7234e84d907535caf1f4f1.bindPopup(popup_e3420dbb0a6fd10505ce18dd62e6ea8c)\n", "        ;\n", "\n", "        \n", "    \n", "&lt;/script&gt;\n", "&lt;/html&gt;\" style=\"position:absolute;width:100%;height:100%;left:0;top:0;border:none !important;\" allowfullscreen webkitallowfullscreen mozallowfullscreen></iframe></div></div>"], "text/plain": ["<folium.folium.Map at 0x7fc18e0b9960>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["m = folium.Map(location=[stay_points[0][0],stay_points[0][1]])\n", "#now display all stay points on map \n", "for ix, loc in enumerate(raw_points) :\n", "    if ix % 10 != 0 :\n", "        continue\n", "    folium.CircleMarker(\n", "        location=[loc[0], loc[1]],\n", "        radius=1,\n", "        popup=\"Laurelhurst Park\",\n", "        color=\"#fc2403\",\n", "        fill=False,\n", "        fill_color=\"##fc2403\",\n", "    ).add_to(m)\n", "    \n", "for ix, stLoc in enumerate(stay_points) :\n", "    folium.CircleMarker(\n", "        location=[stLoc[0], stLoc[1]],\n", "        radius=5,\n", "        popup=\"Laurelhurst Park\",\n", "        color=\"#0b03fc\",\n", "        fill=False,\n", "        fill_color=\"#0b03fc\",\n", "    ).add_to(m)\n", "\n", "    \n", "\n", "display(m)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}, "vscode": {"interpreter": {"hash": "9b6d718a46f7edd5f9401521f8b58b73ea196822719533cdca87ba2c4919b031"}}}, "nbformat": 4, "nbformat_minor": 5}