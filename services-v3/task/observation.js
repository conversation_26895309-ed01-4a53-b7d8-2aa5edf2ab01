const { staff_activity_statuses, classifiers, record_statuses, yes_no_values } = require("../../ENUMS.js");
const { saveClassificationData, loadClassificationData } = require('../../services/common/classification.helper.js')
const moment = require("moment");
const { dbConnections, preProcessRecords, postProcessRecords } = require('@krushal-it/ah-orm');
const { In } = require("typeorm");
const { OBSERVATION_DATE_CLASSIFIER } = require('../../utils/constant.js')

module.exports = observationManager = {
    /*
        read the received array of observation objects and insert question_id into value_reference_id, whole observation object itself to value_json 
        if care_calendar_classification is found in the object, do the update otherwise a new insert.

        each obervation object looks like below
        
        {
            "type": "multiple-choice",
            "options": {
              "Abnormality of prehension": {},
              "abnormality in rumination": {},
              "abnormality in swallowing": {},
              "abnormality in mastication": {},
              "abnormality in eructation / cud dropping": {}
            },
            "question": "How is appetite or not feeding at all?" 
            "answer" :["Abnormality of prehension","abnormality in rumination"]
            "care-calendar-classification-id": "1938e17f-1c3f-45eb-8c88-01dda26e8643" 
            "question_id": "1010000001",
        }


        OBERVATION-MODEL
        {
            value_reference_id: observation.question_id,
            value_json: observation,
            care_calendar_id: uuid,
            care-calendar-classification-id: observation."1938e17f-1c3f-45eb-8c88-01dda26e8643" 
        }
        
    */


    async save(care_calendar_id, data, token) {
        const { observations } = data

        /*  
            validating request-body data structure
        */
        if (!Array.isArray(observations) && observations.length === 0) {
            throw new Error('Missing data! observations is undefined, invalid structure or empty')
        }

        /* 
            recreating new Observation_model confirmed objects from the received observation objects
        */
        const new_observations = observations.map((observation) => Observation_model(observation, care_calendar_id))


        const result = await dbConnections().main.manager.transaction(async (transactionalEntityManager) => {

            const entity_care_calendar_classification = dbConnections().main.entities['care_calendar_classification']

            /*
                const existing_observations = await getObservations(care_calendar_id, question_id, transactionalEntityManager)
            */

            /*
                finds all the existing observation not in new_observations for deactivation because those observations are invalid now.
                Also adds the active key with value of inactive to every observation object. 
            */

            /*
            const observations_for_deactivation = findObservationsForDeactivation(new_observations, existing_observations)

            preProcessRecords(entity_care_calendar_classification, observations_for_deactivation, entity_care_calendar_classification.additionalAttributes)
            const deactivated_observations = await transactionalEntityManager.getRepository('care_calendar_classification')
                .save(observations_for_deactivation)
            
                const deacti_obser_mapped_observation_model = deactivated_observations.map(obr => Observation_model(obr))
            
            */

            /*
                updating and inserting observations 
            */
            const processed_new_observations = preProcessRecords(entity_care_calendar_classification, new_observations, { json_columns: ['value_json'] })

            const updated_or_inserted_observations = await transactionalEntityManager.getRepository('care_calendar_classification')
                .save(processed_new_observations)
            const upd_or_inser_mapped_observation_model = updated_or_inserted_observations.map(obr => Observation_model(obr))

            const care_calendar_classification_entry = preProcessRecords(dbConnections().main.entities["care_calendar_classification"],
                {
                    classifier_id: OBSERVATION_DATE_CLASSIFIER,
                    value_date: new Date().toJSON(),
                    last_modifying_user_id: token.user_id,
                    care_calendar_id: care_calendar_id
                }
                , { json_columns: [] });

            await transactionalEntityManager.getRepository('care_calendar_classification').save(care_calendar_classification_entry)

            await saveClassificationData(transactionalEntityManager, 'CARE_CALENDAR_CLASSIFICATION', care_calendar_id, { observation_version: 'v1'})

            return {
                saved_observation: postProcessRecords(undefined, upd_or_inser_mapped_observation_model, { json_columns: ['value_json'] })
            }
        })

        return {
            result
        }
    },

    /*

    */
    async get(care_calendar_id, question_ids) {
        // validation of care calendar id
        if (!(care_calendar_id && question_ids)) throw new Error("Missing care-calendar-id or question-id.")

        const result = await dbConnections().main.manager.createQueryBuilder().from('care_calendar_classification')
            .where({
                care_calendar_id,
                classifier_id: classifiers.OBSERVATION_ANSWER,
                active: record_statuses.ACTIVE,
                value_reference_id: In(question_ids)
            })
            .select('value_reference_id', 'question_id')
            .addSelect('care_calendar_id')
            .addSelect('care_calendar_classification_id')
            .addSelect('value_json')
            .execute()

        return {
            result: postProcessRecords(undefined, result, { json_columns: ['value_json'] })
        }
    }
}

function Observation_model(data, care_calendar_id) {

    const model_confirmed = {
        care_calendar_classification_id: data.care_calendar_classification_id ? data.care_calendar_classification_id : undefined,
        care_calendar_id: care_calendar_id ? care_calendar_id : data.care_calendar_id,
        [data.question_id ? 'value_reference_id' : 'question_id']: data.value_reference_id ? data.value_reference_id : data.question_id,
        value_json: data.value_json,
        classifier_id: classifiers.OBSERVATION_ANSWER,
    }

    return model_confirmed

}

function findObservationsForDeactivation(newObservations, existingObservations) {
    const deactivate_observations = []
    existingObservations.forEach(existing_observation => {
        const result = newObservations.every(new_observation => new_observation.care_calendar_classification_id !== existing_observation.care_calendar_classification_id)
        if (result) {
            delete existing_observation.value_json
            existing_observation['active'] = record_statuses.INACTIVE
            deactivate_observations.push(existing_observation)
        }
    });

    return deactivate_observations
}

async function getObservations(care_calendar_id, question_ids, transactionalEntityManager) {
    const observations = await transactionalEntityManager.createQueryBuilder()
        .from('care_calendar_classification')
        .where({
            care_calendar_id: care_calendar_id,
            classifier_id: classifiers.OBSERVATION_ANSWER,
            active: record_statuses.ACTIVE,
            value_reference_id: In(question_ids)
        }).select(['care_calendar_classification_id', 'care_calendar_id', 'value_reference_id', 'value_json'])
        .execute()

    return observations
}

