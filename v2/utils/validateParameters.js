/**Function to validate non falsy values */
function validateValues(values) {

    for (const key in values) {
        if (Object.hasOwnProperty.call(values, key)) {
            const value = values[key];
            if (!isValid(value)) {
                return { result: false, message: `Invalid ${value} value for key ${key}` }
            }
        }
    }
    return { result: true, message: `All valid values` }

}

/**Function to check valid page Number*/
function validatePage(page) {
    if (page <= 0) return { result: false, message: `Invalid ${page} value for Page No.` }
    return { result: true, message: `Valid Page No.` }

}

/** Function to validate date format in yyyy-mm-dd */
function validateDates(dates) {
    if (dates?.from > dates?.to) {
        return { result: false, message: `Invalid date range, from > to` }
    }
    for (const key in dates) {
        if (Object.hasOwnProperty.call(dates, key)) {
            const date = dates[key];
            if (!isValidDate(date)) {
                return { result: false, message: `Invalid ${date} value/format(yyyy-mm-dd) for key ${key}` }
            }
        }
    }
    return { result: true, message: `All valid dates` }
}

/**===============================================UTIL FUNCTIONS=========================================================== */

/**Util function to validate correct date format */
function isValidDate(date) {
    const dateRegex = /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/;
    if (!dateRegex.test(date)) {
        return false
    }

    const [year, month, day] = date.split('-').map(Number);

    // Check if month is within the range of 1 to 12
    if (month < 1 || month > 12) {
        return false;
    }

    // Check if day is within the range of 1 to 31
    if (day < 1 || day > 31) {
        return false
    }

    return true
}

/**Util function to validate non-falsy values */
function isValid(value) {
    return value !== null && value !== undefined && value !== '';
}



module.exports = { validateValues, validatePage, validateDates }

