const { dbConnections, getDBConnections, postProcessRecords, preProcessRecords } = require("@krushal-it/ah-orm");
const response = require('../../utils/responseTemplate');
const { getCompiledQuery } = require("../../../queries");
const { q_getAnimalByFarmersId } = require('../../../queries/animal')
const { getClassificationConfiguration } = require("../../../services/common/classification.helper");
const { In, Between } = require("typeorm");
const moment = require('moment');
const { ACTIVE, INACTIVE } = require('../../../utils/constant');
const { RETURN_CODE } = require("../../utils/constants");
const _ = require('lodash');



const getAnimalDetails = async (options) => {
  try {
    const { page_number, page_limit, search, filter, customer_id } = options
    if (!page_number || page_number < 1) {
      return response(RETURN_CODE.NO_DATA, 400, "Invalid Page Number", [])
    }
    let filterType = filter[0]?.filter_key
    let filterValue = []
    filter.forEach(item => {
      filterValue.push(item?.filter_value)
    })
    const attribute_list = await getClassificationConfiguration()["ANIMAL_CLASSIFICATION"]["ATTRIBUTE_LIST"]


    const data = attribute_list[filterType]?.classifier_id
    let filterCondition = (filterValue && filterValue?.length > 0) ? `(${filterValue?.join(',')})` : '';

    const queryParams = {
      page_number: page_number,
      page_limit: page_limit,
      filter: filterCondition,
      customer_id: customer_id,
      search: search,
      type: data
    }
    const countParam = {
      page_number: '',
      page_limit: '',
      filter: filterCondition,
      customer_id: customer_id,
      search: search,
      type: data
    }

    const dbConnections = getDBConnections();

    const query = getCompiledQuery('animals-v2', countParam)
    const countQuery = `SELECT COUNT(*) as count FROM (${query}) as t`
    const countRes = await dbConnections.main.manager.query(countQuery)

    const dataQuery = getCompiledQuery('animals-v2', queryParams)
    const animalDetails = await dbConnections.main.manager.query(dataQuery)
    if (animalDetails.length > 0) {
      return response(RETURN_CODE.SUCCESS, 200, "Animals Details Fetched Successfully", { data: animalDetails, totalCount: countRes[0].count })
    }
    else return response(RETURN_CODE.NO_DATA, 204, "Animals List Empty", { data: [], totalCount: 0 })
  } catch (error) {
    return response(RETURN_CODE.FAILED, 400, error.message, { data: [], totalCount: 0 })
  }

}

const getAnimalCategoryList = async (ref_id) => {
  try {
    if (!ref_id || ref_id === '') {
      throw new Error("Invalid Reference id")
    }
    const query = `Select reference_name from main.ref_reference where reference_id = ${ref_id}`
    const dbConnections = getDBConnections();
    const type = await dbConnections.main.manager.query(query);
    if (type.length === 0) {
      return response(RETURN_CODE.NO_DATA, 200, "No Category Found On for Given ref_id", {})
    }
    let filterType = ''
    if (type[0].reference_name == 'Type of Animal') {
      filterType = 'animal_type'
    }
    if (type[0].reference_name == 'Breed of Animal') {
      filterType = 'animal_breed_1'
    }
    const attribute_list = await getClassificationConfiguration()["ANIMAL_CLASSIFICATION"]["ATTRIBUTE_LIST"]

    const categories = []
    const data = attribute_list[filterType].category_options
    data.forEach(element => {
      const template = {
        filter_key: filterType,
        filter_type: element.reference_id,
        display_name: element.reference_name_l10n,
      }
      categories.push(template)
    });
    return response(RETURN_CODE.SUCCESS, 200, "Category Details Fetched", categories)
  } catch (error) {
    return response(RETURN_CODE.FAILED, 400, error.message, {})
  }

}


{/*
    Milk Log Section
*/}


/*
  Method: getAnimalsByFarmerId
  Params: options - pageLimit, pageNumber
  Response: Array if animalList with totalPages
*/

// generate all animals by farmer ids

const getAnimalsByFarmerId = async (options) => {
  let { pageLimit, pageNumber } = options
  if (pageNumber <= 0) return { data: [], totalPages: 0, messsage: 'Invalid page number' }
  let pageOffset = parseInt((pageNumber - 1) * pageLimit)
  const queryAnimalByFarmer = q_getAnimalByFarmersId(options)
  const countQuery = `SELECT COUNT(*) as count FROM (${queryAnimalByFarmer}) as t`
  const countRes = await dbConnections().main.manager.query(countQuery)
  const { count } = countRes[0]
  let totalPages = parseInt(count / parseInt(pageLimit))
  totalPages += parseInt(count % parseInt(pageLimit)) > 0 ? 1 : 0
  if (pageNumber > totalPages) {
    return { data: [], totalPages, messsage: `Max allowed page number is ${totalPages}` }
  }
  if (!pageLimit && !pageNumber) {
    pageLimit = count
    pageOffset = 0
  }
  /*
  Query: 

  */
  const animalList = await dbConnections().main.manager.createQueryBuilder().select('*').from(`(${queryAnimalByFarmer})`, 'animals').skip(pageOffset).take(pageLimit).execute()
  postProcessRecords(undefined, animalList, { json_columns: ['animal_breed_1', 'subscription_plan_1', 'document_information', 'client_document_information'] })
  return { data: animalList, totalPages }
}

const getAnimalMilkingToBeDeactivated = (milkingDetail, existingAnimalByDate) => {
  let convertedMilkingData = convertMilkingpayloadToClassiifer(milkingDetail);
  const result = convertedMilkingData.map(item1 => {
    const matchingItem = existingAnimalByDate.find(item2 =>
      item1.animal_id === item2.animal_id
    );
    if (matchingItem && matchingItem.value_json && !_.isEqual(matchingItem.value_json, item1.value_json)) {
      return matchingItem ? matchingItem : null;
    }
    return null;
  });
  return result.filter((animal_classification) => animal_classification !== null);
}
const convertMilkingpayloadToClassiifer = (milkingDetail) => {
  const convertedData = [];

  // Loop through each milkingDetail entry
  milkingDetail.forEach((detail) => {
    const { cattle_id, cattle_milking_log } = detail;

    // Create an object for each entry in the desired format
    let entry = {
      value_json: cattle_milking_log,
      animal_id: cattle_id,
      classifier_id: 2000000192
    };

    // Push the object into the convertedData array
    convertedData.push(entry);
  });
  return convertedData
}


const getMilkDetailsLog = async (data) => {
  let { milking_date, milkingDetail } = data
  const animalIds = milkingDetail.map((animal) => animal.cattle_id)
  //get existing milking status for catlle Ids For following Date
  const existingAnimalByDate = await getExistingAnimalsByIds(animalIds, milking_date);
  //existing animal Ids
  const existingAnimalIds = existingAnimalByDate.map((animal) => animal.animal_id);
  //get existing animal By ids 
  const animals_to_beDeactivated = getAnimalMilkingToBeDeactivated(milkingDetail, existingAnimalByDate);
  // const animals_to_be_excluded_while_inserting = 
  const deactive_animal_classification_id = animals_to_beDeactivated.map((animal) => animal.animal_classification_id)
  //deactivate the ids
  const deactive_animal_ids = animals_to_beDeactivated.map((animal) => animal.animal_id)

  const idsToBeExludedForInsertAndDelete = existingAnimalIds.filter(item => !deactive_animal_ids.includes(item));
  //remove data to be exluded in payload while inserting
  milkingDetail = milkingDetail.filter(item => {
    if (!idsToBeExludedForInsertAndDelete.includes(item.cattle_id) && item.cattle_milking_log !== null) {
      return item
    }
  });
  const response = await dbConnections().main.manager.transaction(async (transaction) => {
    const deactivateAnimalIds = await deactivateAnimalMilk(transaction, deactive_animal_classification_id);
    const animalMilkClassification = await insertToAnimalMilkClassification(transaction, milkingDetail, milking_date);
    return animalMilkClassification
  });


  return response;
}

// generate exisiting animals by animal ids

const getExistingAnimalsByIds = async (AnimalIds, date) => {
  try {
    let animal_milk = await dbConnections().main.repos["animal_classification"].find({
      where: { active: ACTIVE, classifier_id: 2000000192, value_date: date, animal_id: In(AnimalIds) }
    });
    animal_milk = postProcessRecords(undefined, animal_milk, { json_columns: ["value_date"] });
    return animal_milk;
  } catch (error) {
    return error
  }
}


// deactivation of animal milk belonging to a specified animal

const deactivateAnimalMilk = async (transaction, animal_classification_id) => {
  try {
    let inactiveAnimalMilk = {
      active: INACTIVE
    }
    //add pre process record
    inactiveAnimalMilk = preProcessRecords(dbConnections().main.entities["animal_classification"], inactiveAnimalMilk, null);
    if (inactiveAnimalMilk) delete inactiveAnimalMilk.animal_classification_id
    const result = await transaction.getRepository("animal_classification").update({
      animal_classification_id: In(animal_classification_id),
      classifier_id: 2000000192,
      active: ACTIVE
    }, inactiveAnimalMilk);
    return result
  }
  catch (error) {
    return error
  }
}


// insertion to Animal Milk Classification table

const insertToAnimalMilkClassification = async (transaction, milkingDetail, milking_date) => {
  try {
    const data = [];

    // Loop through each milkingDetail entry
    milkingDetail.forEach((detail) => {
      const { cattle_id, cattle_milking_log } = detail;

      // Create an object for each entry in the desired format
      let entry = {
        value_json: cattle_milking_log,
        animal_id: cattle_id,
        value_date: milking_date,
        classifier_id: 2000000192
      };
      entry = preProcessRecords(dbConnections().main.entities["animal_classification"], entry, { json_columns: ["value_json"] });
      // Push the object into the data array
      data.push(entry);

    });

    await transaction.save("animal_classification", data);

    const groupedLogs = {};

    data.forEach((log) => {
      if (!groupedLogs[log.value_date]) {
        groupedLogs[log.value_date] = [];
      }
      groupedLogs[log.value_date].push(log);
      delete log.value_date
    });

    const resultantLogs = Object.keys(groupedLogs).map((date) => ({
      value_date: date,
      logs: groupedLogs[date],
    }));

    return resultantLogs
  } catch (error) {
    return error
  }
}


//Calculation of Milk Data on the basis of complete month

const calculateMonthlyMilkDetails = async (animalIds, fromDate, toDate) => {

  try {

    let startDate = moment(fromDate).clone().startOf('month');
    let endDate = moment(toDate).clone().endOf('month');


    if (endDate.diff(startDate, 'days') > 30) {
      startDate = fromDate
      endDate = toDate
    } else {
      startDate = startDate.format('YYYY-MM-DD');
      endDate = endDate.format('YYYY-MM-DD')
    }

    let details = await dbConnections().main.repos["animal_classification"].find({
      where: {
        active: ACTIVE,
        classifier_id: 2000000192,
        value_date: Between(startDate, endDate),
        animal_id: In(animalIds)
      },
      select: {
        value_date: true,
        value_json: true,
        animal_id: true
      }
    })

    let totalLitres = 0;

    details.forEach((item) => {
      totalLitres = parseFloat(totalLitres) + parseFloat(item.value_json.morning ? item.value_json.morning : 0) + parseFloat(item.value_json.evening ? item.value_json.evening : 0)

    })

    /*
     a. Get all the animals
     b. Get the current months date w.r.t animal
     c. Average caculation of an animal between the date trange
     d. Average calcilation is (sum of all milk collected/numberofDays)
    */
    let calculations = {};
    let estimate = 0;

    calculations['total_milk'] = totalLitres
    fromDate = moment(fromDate);
    toDate = moment(toDate)
    const currentDate = moment();

    if (toDate.isSame(currentDate, 'month')) {
      let daysCompleted = toDate.diff(fromDate, 'days');

      const average_milk = parseFloat(totalLitres / (daysCompleted + 1));

      calculations['average_milk'] = average_milk;

      const remaining_days = toDate.daysInMonth() - moment().date();

      estimate = average_milk * remaining_days

    } else {
      calculations['average_milk'] = totalLitres / (toDate.diff(fromDate, 'days') + 1)
    }

    calculations['estimate'] = estimate

    if (Object.values(calculations).includes(null)) throw error;

    return calculations

  } catch (error) {
    return error;
  }

}

// Fetching all milk logs


const fetchAllLogs = async (animalIds) => {
  let animal_milk_details = await dbConnections().main.repos["animal_classification"].find({
    where: {
      active: ACTIVE,
      classifier_id: 2000000192,
      animal_id: In(animalIds)
    },
    order: { value_date: 'ASC' },
    select: {
      value_date: true,
      value_json: true,
      animal_id: true
    }
  })

  animal_milk_details = postProcessRecords(undefined, animal_milk_details, { json_columns: ["value_date"] });

  let result = animal_milk_details.reduce((acc, item) => {

    let date = new moment(item.value_date).format('YYYY-MM-DD');

    const existingEntry = acc.find(entry => entry.date === date)

    const value_json = {
      morning_quantity: item.value_json.morning,
      evening_quantity: item.value_json.evening
    }

    // Prepare the cattle data
    const cattleData = {
      animal_id: item.animal_id,
      value_json: value_json
    };


    if (existingEntry) {
      // If an entry with the same date exists, add cattle data to it
      existingEntry.cattle.push(cattleData);
    } else {
      // If no entry with the same date exists, create a new entry
      acc.push({
        date,
        milking_date: moment(date).format('YYYY-MM-DD'),
        cattle: [cattleData]
      });
    }

    return acc;

  }, []);

  result = result.map((item) => {

    delete item.date;

    let status = "Pending";

    if (item.cattle.length === animalIds.length) {
      let morning_len = item.cattle.filter((cattle) => cattle.value_json.morning_quantity !== undefined).length
      let evening_len = item.cattle.filter((cattle) => cattle.value_json.evening_quantity !== undefined).length
      if (morning_len === animalIds.length && evening_len === animalIds.length) {
        status = "Completed"
      }
    }
    return { status, ...item }

  })

  return { animal_milk: result }
}

const getMilkLog = async (animalIds, fromDate, toDate, singleAnimal = false) => {
  try {
    let animal_milk = await dbConnections().main.repos["animal_classification"].find({
      where: {
        active: ACTIVE,
        classifier_id: 2000000192,
        value_date: Between(fromDate, toDate),
        animal_id: In(animalIds)
      },
      order: { value_date: 'ASC' },
      select: {
        value_date: true,
        value_json: true,
        animal_id: true
      }
    });

    animal_milk = postProcessRecords(undefined, animal_milk, { json_columns: ["value_date"] });

    let result = animal_milk.reduce((acc, item) => {
      // Extract date without time  
      // let date = new Date(item.value_date).toISOString().split('T')[0];

      let date = new moment(item.value_date).format('YYYY-MM-DD');

      const existingEntry = acc.find(entry => entry.date === date)

      const value_json = {
        morning_quantity: item.value_json.morning,
        evening_quantity: item.value_json.evening
      }

      // Prepare the cattle data
      const cattleData = {
        animal_id: item.animal_id,
        value_json: value_json
      };


      if (existingEntry && !singleAnimal) {
        // If an entry with the same date exists, add cattle data to it
        existingEntry.cattle.push(cattleData);
      } else {
        // If no entry with the same date exists, create a new entry

        acc.push({
          date,
          milking_date: moment(date).format('YYYY-MM-DD'),
          cattle: !singleAnimal ? [cattleData] : value_json
        });
      }

      return acc;

    }, []);

    result = result.map((item) => {

      delete item.date;

      let status = "Pending";

      if (singleAnimal && item.cattle.morning_quantity !== undefined && item.cattle.evening_quantity !== undefined) {
        status = "Completed"
      }

      if (!singleAnimal && item.cattle.length === animalIds.length) {
        let moning_len = item.cattle.filter((cattle) => cattle.value_json.morning_quantity !== undefined).length
        let evening_len = item.cattle.filter((cattle) => cattle.value_json.evening_quantity !== undefined).length
        if (moning_len === animalIds.length && evening_len === animalIds.length) {
          status = "Completed"
        }
      }
      return { status, ...item }

    })

    const calculations = await calculateMonthlyMilkDetails(animalIds, fromDate, toDate);

    if (calculations === null) throw error;

    return { calculations, animal_milk: result }
  } catch (error) {
    return response(-101, 400, error.message, {})
  }
}




module.exports = {
  getAnimalDetails,
  getAnimalCategoryList,
  getAnimalsByFarmerId,
  fetchAllLogs,
  getMilkLog,
  getMilkDetailsLog,
  getExistingAnimalsByIds,
  deactivateAnimalMilk,
  insertToAnimalMilkClassification
}