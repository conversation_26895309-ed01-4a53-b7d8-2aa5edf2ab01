const moment = require('moment');
const { dbConnections } = require('@krushal-it/ah-orm')
const response = require('../../utils/responseTemplate')
const {
  getAnimalDetails,
  getAnimalCategoryList,
  fetchAllLogs,
  getMilkLog,
  getAnimalsByFarmerId,
  getMilkDetailsLog,
  getExistingAnimalsByIds,
  deactivateAnimalMilk,
  insertToAnimalMilkClassification
} = require('./animal.controller')
const _ = require('lodash');
const { RETURN_CODE } = require('../../utils/constants');

class AnimalServicesV2 {
  async create(data, params) {
    try {
      let { search, filter } = data
      const { page_number, page_limit } = params.query
      const { customer_id } = params.headers.token
      const options = {
        search, filter, page_number, page_limit, customer_id
      }
      const animalsDetails = await getAnimalDetails(options)

      return animalsDetails
    } catch (error) {
      return response(RETURN_CODE.FAILED, 400, error.message, { data: [], totalCount: 0 })
    }

  }
}

class AnimalCategoryV2 {
  async find(params) {
    try {
      const { ref_id } = params.query
      const categoryList = await getAnimalCategoryList(ref_id)
      return categoryList
    } catch (error) {
      return response(RETURN_CODE.FAILED, 400, error.message, {})
    }
  }
}


class AnimalMilkingV2 {

  async updateOrCreateMilkLog(data) {

    try {
      if (Array.isArray(data)) {

        const resultLog = await Promise.all(
          data.map(async (item) => getMilkDetailsLog(item))
        )

        return resultLog;

      }
      return await getMilkDetailsLog(data)

    } catch (error) {
      return response(-101, 400, error.message, {})
    }
  }

  async getMilkLogByFarmerId(farmer_id, fromDate, toDate) {
    try {
      let query = { user_id: farmer_id, user_type: 1000220001 };
      const animals = await getAnimalsByFarmerId(query);
      const animalIds = animals.data.map((animal) => animal.animal_id)

      let loggedMilking;

      if (fromDate === '0000-00-00' && toDate === '0000-00-00') {
        loggedMilking = await fetchAllLogs(animalIds)
      } else {
        const fromDateStr = moment(fromDate).format('YYYY-MM-DD');
        const toDateStr = moment(toDate).format('YYYY-MM-DD');

        loggedMilking = await getMilkLog(animalIds, fromDateStr, toDateStr)
      }
      return loggedMilking
    } catch (error) {
      return response(-101, 400, error.message, [])
    }
  }

  async getMilkLogByAnimalId(animal_id, fromDate, toDate) {
    try {
      const fromDateStr = moment(fromDate).format('YYYY-MM-DD')
      const toDateStr = moment(toDate).format('YYYY-MM-DD');
      if (fromDateStr == 'Invalid date' || toDateStr == 'Invalid date') throw new Error('Invalid Dates Given')


      const loggedMilking = await getMilkLog([animal_id], fromDateStr, toDateStr, true)
      return loggedMilking;

      // return {...loggedMilking}
    } catch (error) {
      return response(-101, 400, error.message, [])
    }

  }

}
module.exports = { AnimalServicesV2, AnimalCategoryV2, AnimalMilkingV2 }