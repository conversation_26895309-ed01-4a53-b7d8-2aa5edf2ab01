
const { ID_TYPES_STAFF, ID_TYPES_CUSTOMER, RETURN_CODE } = require('../../utils/constants')
const response = require('../../utils/responseTemplate')
const { getLoginDetails, updateLocation, signOutUser, signOutFromBackOffice } = require('./users.controller')

class UsersLogin {
    /**
    * Function: creates user device registration based on data and parameters.
    * @param {Object} data - Object containing device and customer details for registering user device.
    * @param {Object} params - Object containing query parameters and headers, including customer token.
    */
    async create(data, params) {
        const token = params.headers.token
        if (!ID_TYPES_STAFF[token?.user_type] && !ID_TYPES_CUSTOMER[token?.user_type]) {
            return response(RETURN_CODE.FAILED, 401, 'Unauthorized Access', {})
        }
        try {
            const response = await getLoginDetails(data, token)
            return response
        } catch (error) {
            return response(RETURN_CODE.FAILED, 400, error.message, {})
        }
    }

    /**
    * Function: updates user device registration based on data and parameters.
    * @param {Object} data - Object containing device and customer details with lat-long details.
    * @param {Object} params - Object containing query parameters and headers, including customer token.
    */
    async updateLatLong(data, params) {
        try {
            const response = await updateLocation(data, params)
            return response
        } catch (error) {
            return response(RETURN_CODE.FAILED, 400, error.message, [])
        }
    }

    /**
    * Function: logout user based on data and parameters.
    * @param {Object} data - Object containing device and customer details.
    * @param {Object} params - Object containing query parameters and headers, including customer token.
    */
    async userSignOut(data, params) {
        try {
            const response = await signOutUser(data, params)
            return response
        } catch (error) {
            return response(RETURN_CODE.FAILED, 400, error.message, [])
        }
    }

    /**
    * Function: logout user based on mobile_number.
    * @param {Object} data - Object containing device and customer details.
    * @param {Object} params - Object containing query parameters and headers, including customer token.
    */
    async userSignOutFromOffice(data, params) {
        try {
            const token = params.headers.token
            if (ID_TYPES_STAFF[token?.user_type]?.type !== 'back_office_person') {
                return response(RETURN_CODE.FAILED, 401, 'Unauthorized Access', {})
            }
            const result = await signOutFromBackOffice(data, token?.user_type)
            return result
        } catch (error) {
            return response(RETURN_CODE.FAILED, 400, error.message, [])
        }
    }

}

module.exports = { UsersLogin }
