const { getDBConnections, preProcessRecords } = require("@krushal-it/ah-orm")
const response = require('../../utils/responseTemplate')
const { getCompiledQuery } = require("../../../queries/handlebarConfig")
const { ID_TYPES_CUSTOMER, ID_TYPES_STAFF, CLASSIFIER_ID, ENTITY_TYPE, VALUE_REFERENCE_ID, ACTIVE, RETURN_CODE } = require('../../utils/constants')
const { validateValues } = require("../../utils/validateParameters")

/*
  Method: getLoginDetails
  Params: data-{user device details}, token,
  Response: Object of user details
*/

const getLoginDetails = async (data, token) => {
    try {

        /**Valid values check */
        const validValues = validateValues({ token: token, phone_number: token?.mobile, instance_id: data?.instance_id, device_id: data?.device_id, uid: data?.uid, device_information: data?.device_information })
        if (!validValues.result) {
            return response(RETURN_CODE.FAILED, 400, validValues.message, {})
        }

        const dbConnections = getDBConnections()
        const phone_number = String(token.mobile)
        let queryOptions = { phone_number: phone_number, classifier_id: null }

        if (ID_TYPES_CUSTOMER[token.user_type]) {
            queryOptions.classifier_id = CLASSIFIER_ID.FARMER_VILLAGE_ID
        } else if (ID_TYPES_STAFF[token.user_type]) {
            queryOptions.classifier_id = CLASSIFIER_ID.STAFF_ONLINE
        }
        const query = getCompiledQuery("users-login-v2", queryOptions)

        const phoneMatchingResults = await dbConnections.main.manager.query(query)
        if (phoneMatchingResults.length > 0) {

            let entityType = null
            let userId = ''
            let userTypeId = ''
            let user_name_l10n = '';
            let user_type = ''

            /** Determine the user entity type, ID, and related information based on the token's user type. */
            if (ID_TYPES_CUSTOMER[token.user_type]) {
                entityType = ENTITY_TYPE.FARMER
                userId = phoneMatchingResults[0].customer_id
                userTypeId = phoneMatchingResults[0].customer_type_id
                user_name_l10n = phoneMatchingResults[0].customer_name_l10n
                user_type = phoneMatchingResults[0].customer_type_l10n
            } else if (ID_TYPES_STAFF[token.user_type]) {
                entityType = ENTITY_TYPE.STAFF
                userId = phoneMatchingResults[0].staff_id
                userTypeId = phoneMatchingResults[0].staff_type_id
                user_name_l10n = phoneMatchingResults[0].staff_name_l10n
                user_type = phoneMatchingResults[0].staff_type_l10n
            }

            const offlineUserypeIds = [1000230001, 1000230004, 1000230003]
            let offlineUser = offlineUserypeIds.includes(userTypeId)
            if (offlineUser && phoneMatchingResults[0].value_reference_id && phoneMatchingResults[0].value_reference_id === VALUE_REFERENCE_ID.YES) {
                offlineUser = false
            }
            let compress_db_sync = false
            if (phoneMatchingResults[0].compress_db_sync && phoneMatchingResults[0].compress_db_sync === VALUE_REFERENCE_ID.YES) {
                compress_db_sync = true
            }

            const userDeviceRepo = dbConnections.main.repos.user_device
            const matchingUserDevices = await userDeviceRepo.find({
                where: {
                    entity_type_id: entityType,
                    entity_uuid: userId,
                    // instance_id: data.instance_id,
                    active: ACTIVE.YES,
                },
            })

            // Check for existing user devices with different device_id
            if (matchingUserDevices.length > 0 && data.device_id !== matchingUserDevices[0].device_id) {
                const userDeviceId = matchingUserDevices[0].user_device_id
                const userDeviceResult = {
                    user_device_id: userDeviceId,
                    entity_type_id: entityType,
                    offline_user: offlineUser,
                    user_type_id: userTypeId,
                    user_id: userId,
                    entity_uuid: userId,
                    user_name_l10n: user_name_l10n,
                    user_type: user_type,
                    user_mobile_number: phoneMatchingResults[0].mobile_number,
                    user_document_information: phoneMatchingResults[0].document_information,
                    compress_db_sync: compress_db_sync
                }

                return response(RETURN_CODE.ALTERNATE_LOGIN, 401, "Alternate Login", userDeviceResult)
            }
            // Check for existing user devices with same device_id
            if (matchingUserDevices.length > 0) {
                const userDeviceId = matchingUserDevices[0].user_device_id

                try {
                    await dbConnections.main.manager.transaction(async (transaction) => {
                        transaction.getRepository('user_device').update(
                            {
                                user_device_id: userDeviceId,
                            },
                            {
                                device_information: data.device_information,
                            }
                        )
                    })
                } catch (error) {
                    console.log('error in updating device information', error)
                }

                const userDeviceResult = {
                    user_device_id: userDeviceId,
                    entity_type_id: entityType,
                    offline_user: offlineUser,
                    user_type_id: userTypeId,
                    user_id: userId,
                    entity_uuid: userId,
                    user_name_l10n: user_name_l10n,
                    user_type: user_type,
                    user_mobile_number: phoneMatchingResults[0].mobile_number,
                    user_document_information: phoneMatchingResults[0].document_information,
                    compress_db_sync: compress_db_sync
                }
                return response(RETURN_CODE.SUCCESS, 200, 'Existing Login', userDeviceResult)
            } else {
                // Create a new device id if no matching device found
                const userDeviceCreationResult = await dbConnections.main.manager.transaction(async (transactionalEntityManager) => {
                    const userDeviceData = {
                        entity_type_id: entityType,
                        entity_uuid: userId,
                        uid: data.uid,
                        instance_id: data.instance_id,
                        device_id: data.device_id,
                        device_information: data.device_information,
                    }
                    const userDeviceEntity = dbConnections.main.entities['user_device']
                    const preProcessedUserDeviceData = preProcessRecords(userDeviceEntity, userDeviceData, userDeviceEntity.additionalAttributes)
                    const createdUserDeviceRecord = await transactionalEntityManager.createQueryBuilder().insert().into(userDeviceEntity).values(preProcessedUserDeviceData).execute()
                    const insertedUserDeviceId = createdUserDeviceRecord.identifiers[0].user_device_id
                    const userDeviceResult = {
                        user_device_id: insertedUserDeviceId,
                        entity_type_id: entityType,
                        offline_user: offlineUser,
                        user_type_id: userTypeId,
                        user_id: userId,
                        entity_uuid: userId,
                        user_name_l10n: user_name_l10n,
                        user_type: user_type,
                        user_mobile_number: phoneMatchingResults[0].mobile_number,
                        user_document_information: phoneMatchingResults[0].document_information,
                        compress_db_sync: compress_db_sync

                    }
                    return response(RETURN_CODE.SUCCESS, 200, 'Created a new device id', userDeviceResult)
                })
                return userDeviceCreationResult
            }
        } else {
            return response(RETURN_CODE.NO_DATA, 401, 'Mobile number does not exist in the system', {})
        }
    } catch (error) {
        console.log(error)
        return response(RETURN_CODE.FAILED, 400, error.message, {})
    }
}

/*
  Method: updateLocation
  Params: data-{user device details}, params,
  Response: Object of user details
*/
const updateLocation = async (data, params) => {
    try {
        const validValues = validateValues({ user_device_id: data?.user_device_id, customer_id: data?.customer_id, latitude: data?.lat, longitude: data?.long })
        if (!validValues.result) {
            return response(RETURN_CODE.FAILED, 400, validValues.message, [])
        }
        const dbConnections = getDBConnections()
        const entityType = ENTITY_TYPE.FARMER
        const user_id = data?.customer_id

        const userDeviceRepo = dbConnections.main.repos.user_device
        const matchingUserDevices = await userDeviceRepo.find({
            where: {
                entity_type_id: entityType,
                entity_uuid: user_id,
                user_device_id: data?.user_device_id,
                active: ACTIVE.YES,
            },
        })

        if (matchingUserDevices.length === 0) return response(RETURN_CODE.FAILED, 200, 'user not found', [])

        const lat_long = {
            lat: data?.lat,
            long: data?.long
        }
        const updateData = { ...matchingUserDevices[0].device_information, lat_long }


        const userDeviceId = matchingUserDevices[0].user_device_id

        try {
            await dbConnections.main.manager.transaction(async (transaction) => {
                transaction.getRepository('user_device').update(
                    {
                        user_device_id: userDeviceId,
                    },
                    {
                        device_information: updateData
                    }
                )
            })
        } catch (error) {
            return response(RETURN_CODE.FAILED, 400, error.message, [])
        }
        const device = await userDeviceRepo.find({
            where: {
                entity_type_id: entityType,
                entity_uuid: user_id,
                user_device_id: data?.user_device_id,
                active: ACTIVE.YES,
            },
        })

        return response(RETURN_CODE.SUCCESS, 200, 'Lat long update Successful', device)
    } catch (error) {
        return response(RETURN_CODE.FAILED, 400, error.message, [])
    }
}

/*
  Method: signOutUser
  Params: data-{user device details}, params,
  Response: Object of user details
*/
const signOutUser = async (data, params) => {
    try {
        const validValues = validateValues({ user_device_id: data?.user_device_id, customer_id: data?.customer_id })
        if (!validValues.result) {
            return response(RETURN_CODE.FAILED, 400, validValues.message, [])
        }
        const dbConnections = getDBConnections()
        const entityType = ENTITY_TYPE.FARMER
        const user_id = data?.customer_id

        const userDeviceRepo = dbConnections.main.repos.user_device
        const matchingUserDevices = await userDeviceRepo.find({
            where: {
                entity_type_id: entityType,
                entity_uuid: user_id,
                user_device_id: data?.user_device_id,
                active: ACTIVE.YES,
            },
        })

        if (matchingUserDevices.length === 0) return response(RETURN_CODE.FAILED, 200, 'user not found', [])

        const userDeviceId = matchingUserDevices[0].user_device_id

        try {
            await dbConnections.main.manager.transaction(async (transaction) => {
                transaction.getRepository('user_device').update(
                    {
                        user_device_id: userDeviceId,
                    },
                    {
                        active: ACTIVE.NO
                    }
                )
            })
        } catch (error) {
            return response(RETURN_CODE.FAILED, 400, error.message, [])
        }
        const device = await userDeviceRepo.find({
            where: {
                entity_type_id: entityType,
                entity_uuid: user_id,
                user_device_id: data?.user_device_id,
                active: ACTIVE.NO,
            },
        })

        return response(RETURN_CODE.SUCCESS, 200, 'Signed Out Successfully', device)
    } catch (error) {
        return response(RETURN_CODE.FAILED, 400, error.message, [])
    }
}

/*
  Method: signOutFromBackOffice
  Params: data-{user mobile number}, params,
  Response: Object of user details
*/
const signOutFromBackOffice = async (data, user_type) => {
    try {
        const validValues = validateValues({ mobile_number: data?.mobile_number, user_type: user_type })
        if (!validValues.result) {
            return response(RETURN_CODE.FAILED, 400, validValues.message, [])
        }
        const dbConnections = getDBConnections()

        const customerIdQuery = `Select customer_id, mobile_number From main.customer where mobile_number = '${data?.mobile_number}'`

        const customer = await dbConnections.main.manager.query(customerIdQuery)

        if (customer.length > 0) {
            const customer_id = customer[0].customer_id

            try {
                await dbConnections.main.manager.transaction(async (transaction) => {
                    transaction.getRepository('user_device').update(
                        {
                            entity_uuid: customer_id,
                            active: ACTIVE.YES
                        },
                        {
                            active: ACTIVE.NO
                        }
                    )
                })
                const userDeviceRepo = dbConnections.main.repos.user_device

                const device = await userDeviceRepo.find({
                    where: {
                        entity_uuid: customer_id,
                        active: ACTIVE.NO,
                    },
                })

                return response(RETURN_CODE.SUCCESS, 200, 'Signed Out Successfully', device)
            } catch (error) {
                return response(RETURN_CODE.FAILED, 400, error.message, [])
            }
        } else {
            response(RETURN_CODE.NO_DATA, 404, "User not Found", [])
        }
    }
    catch (error) {
        return response(RETURN_CODE.FAILED, 400, error.message, [])
    }
}

module.exports = { getLoginDetails, updateLocation, signOutUser, signOutFromBackOffice }
