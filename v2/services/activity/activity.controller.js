const { configurationJSON } = require('@krushal-it/common-core');
const { dbConnections, getDBConnections } = require('@krushal-it/ah-orm');
const moment = require('moment');
const { validateUUID } = require('@krushal-it/mobile-or-server-lib');
const { getCompiledQuery, loadQueryTemplates } = require('../../../queries/handlebarConfig.js');
const requiredQuery = require("../../queries/templates/activity/taskv2.js")
const response = require("../../utils/responseTemplate.js")



const {
  REFERENCE_ID
} = require('../../queries/templates/activity/constants.js');
const { RETURN_CODE } = require('../../utils/constants.js');
const { validateValues, validatePage, validateDates } = require('../../utils/validateParameters.js');
const { loadClassificationData } = require('../../../services/common/classification.helper.js');
const { q_getLinkedTask } = require('../../../queries/activity.js');



const getTasksV2 = async (data, token, pageNo, options = {}) => {


  try {
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0;
    const validQueryType = new Set(['farmer', 'animal']);
    const USER_BACK_OFFICE = REFERENCE_ID.STAFF_BACKOFFICE;
    const USER_FARMER = REFERENCE_ID.FARMER;

    const { user_id, user_type } = token;

    if (!token || !user_id || !user_type) {
      throw new Error('Invalid token object(unresilved or null)');
    }

    if (!data) {
      throw new Error('invalid request data');
    }

    if (user_type !== USER_FARMER && (!data.farmer_id || !validateUUID(data.farmer_id)) && options.querType === 'farmer') {
      // farmer ID is always required
      throw new Error('A valid farmer_id is required');
    }

    if (!validQueryType.has(options.querType)) {
      throw new Error('Missing/Invalid querType in options');
    }

    if (options.querType === 'farmer' && !validateUUID(data.farmer_id) && user_type !== USER_FARMER) {
      throw new Error('valid farmer id(uuid) is required');
    }

    const { from, to } = data;

    const current_date = moment().format('YYYY-MM-DD');

    const fromDate = moment(from);
    const toDate = moment(to);

    const fromDif = fromDate.diff(current_date, 'days');
    const toDif = toDate.diff(current_date, 'days');

    if (fromDate > toDate)
      throw new Error('From date should not exceed To date')

    if (!fromDate.isValid() || !toDate.isValid())
      throw new Error('Invalid input')

    if (fromDif > 365 || fromDif < -365)
      throw new Error('From date must be within one year from now');

    if (toDif > 365 || toDif < -365)
      throw new Error('To date must be within one year from now');

    const templateParams = {
      // valid village filter is disabled for backoffice person and farmer
      validVillage: user_type == USER_BACK_OFFICE || user_type == USER_FARMER ? 0 : 1,
      thumbnail: 0 | data.thumbnail,
      user_type,
      IS_POSTGRES: isPostgres,
    };
    const queryParams = {
      user_id,
      user_type,
      from,
      to,
    };

    if (user_type == USER_FARMER) {
      templateParams.farmerFilter = 1;
      templateParams.dashboard = data.dashboard | 0;
      templateParams.from = 1;
      templateParams.to = 1;
      queryParams.farmer_id = user_id;
      queryParams.from = data.from || null;
      queryParams.to = data.to || null;
    }

    if (options.querType === 'animal') {
      templateParams.farmerFilter = 1;
      templateParams.animalFilter = 1;
      templateParams.dashboard = data.dashboard | 0;
      templateParams.from = 1;
      templateParams.to = 1;
      queryParams.farmer_id = user_id;
      queryParams.animal_id = data.animal_id;
      queryParams.from = data.from || null;
      queryParams.to = data.to || null;
    }
    loadQueryTemplates({
      custom_templates: {
        "countQuery": `SELECT COUNT(*) AS TOTAL_COUNT FROM ( ${requiredQuery} ) AS TOTAL_COUNT`
      }
    })
    const countRows = getCompiledQuery('countQuery', templateParams)
    const totalRows = await dbConnections().main.manager.createQueryBuilder().from(`(${countRows})`, 'TOTAL_COUNT').setParameters(queryParams).execute();
    const { total_count } = totalRows[0];

    templateParams.pageNumber = pageNo ? 1 : 0;
    queryParams.pageNumber = pageNo || 0;

    const taskQuery2 = getCompiledQuery('taskv2', templateParams);
    const list = await dbConnections().main.manager.createQueryBuilder().from(`(${taskQuery2})`).setParameters(queryParams).execute();
    return response(RETURN_CODE.SUCCESS, 200, "success", { list: list, totalCount: total_count })
  } catch (error) {
    return response(RETURN_CODE.FAILED, 400, error.message, { list: [], totalCount: 0 })
  }
};

/*
Method: getTaskHistoryV2
Params: pageNo, animal_id , optional:(from, to)
Response: Array of animal activity history with total pages
*/
const getTaskHistoryV2 = async (pageNo, animal_id, from, to) => {

  /**Valid values check */
  const validValues = validateValues({ pageNo: pageNo, animal_id: animal_id })
  if (!validValues.result) {
    return response(RETURN_CODE.FAILED, 400, validValues.message, { list: [], totalCount: 0 })
  }

  /**Valid page check (page > 0)  */
  const validPage = validatePage(pageNo)
  if (!validPage.result) {
    return response(RETURN_CODE.FAILED, 400, validPage.message, { list: [], totalCount: 0 })
  }
  try {
    const templateParams = {
      animalFilter: 1,
      history: 1

    };
    const queryParams = {
      animal_id: animal_id
    };
    if (from && to) {
      templateParams.from = from
      templateParams.to = to
      queryParams.from = from
      queryParams.to = to
    }

    const dBConnection = getDBConnections();
    templateParams.pageNumber = 0;

    /**Get Total count*/
    const query = getCompiledQuery('taskv2', templateParams)
    const countQuery = `SELECT COUNT(*) as count FROM (${query}) as t`
    const count = await dBConnection.main.manager.createQueryBuilder().from(`(${countQuery})`).setParameters(queryParams).execute();

    templateParams.pageNumber = pageNo ? 1 : 0;
    queryParams.pageNumber = pageNo || 0;

    /**Get animal history tasks */
    const taskQuery2 = getCompiledQuery('taskv2', templateParams);
    const list = await dBConnection.main.manager.createQueryBuilder().from(`(${taskQuery2})`).setParameters(queryParams).execute();

    return response(RETURN_CODE.SUCCESS, 200, "History Fetched Successfully", { list: list, totalCount: count[0].count })
  } catch (error) {
    return response(RETURN_CODE.FAILED, 400, error.message, { list: [], totalCount: 0 })
  }
}

/*
Method: getAllTaskDetailsV2
Params: data, pageNo, token
Response: Array of animal activity list between a range of date with total pages
*/
const getAllTaskDetailsV2 = async (data, pageNo, token) => {
  try {
    const { from, to } = data;
    const { user_id } = token

    /**Valid values check */
    const validValues = validateValues({ from: from, to: to, user_id: user_id })
    if (!validValues.result) {
      return response(RETURN_CODE.FAILED, 400, validValues.message, { list: [], totalCount: 0 })
    }

    /**Valid date and format check (yyyy-mm-dd) */
    const validDates = validateDates({ from: from, to: to })
    if (!validDates.result) {
      return response(RETURN_CODE.FAILED, 400, validDates.message, { list: [], totalCount: 0 })
    }

    const templateParams = {
      farmerFilter: 1,
      allTask: 1,
      from: from,
      to: to
    }
    const queryParams = {
      farmer_id: user_id,
      from: from,
      to: to
    }
    const dBConnection = getDBConnections();
    templateParams.pageNumber = 0;

    /**Get Total count*/
    const query = getCompiledQuery('taskv2', templateParams)
    const countQuery = `SELECT COUNT(*) as count FROM (${query}) as t`
    const count = await dBConnection.main.manager.createQueryBuilder().from(`(${countQuery})`).setParameters(queryParams).execute();

    templateParams.pageNumber = pageNo ? 1 : 0;
    queryParams.pageNumber = pageNo || 0;

    /**Get animal tasks by farmer ID */
    const taskQuery2 = getCompiledQuery('taskv2', templateParams);
    const list = await dBConnection.main.manager.createQueryBuilder().from(`(${taskQuery2})`).setParameters(queryParams).execute();
    return response(RETURN_CODE.SUCCESS, 200, "All Data Fetched", { list: list, totalCount: count[0].count })

  } catch (error) {
    return response(RETURN_CODE.FAILED, 400, error.message, { list: [], totalCount: 0 })
  }
}

/*
Method: getSummaryDetailsV2
Params: data, params
Response: Array of single object with summary flags
*/
const getSummaryDetailsV2 = async (data, params) => {
  try {
    const { care_calendar_id, animal_id } = data;
    const validValues = validateValues({ care_calendar_id: care_calendar_id, animal_id: animal_id })
    if (!validValues.result) {
      return response(RETURN_CODE.FAILED, 400, validValues.message, [])
    }
    const dbConnections = getDBConnections();

    /*
     * diagnosis/prescription/observation summary query
     * get diagnosis/prescription/observation summary by flags
     */
    const queryParams = {
      care_calendar_id: care_calendar_id
    }
    const query = getCompiledQuery('medical-record-summary', queryParams)
    const summary = await dbConnections.main.manager.query(query);

    /*
     * health records summary query
     * get health records summary by service request Number
     */
    const healthParams = {
      animal_id: animal_id
    }
    const healthQuery = getCompiledQuery('health-record-summary', healthParams)
    const health = await dbConnections.main.manager.query(healthQuery);

    /*
     * case history summary query
     * get case history summary by activity count
     */
    let linkedTask = await loadClassificationData('CARE_CALENDAR_CLASSIFICATION', care_calendar_id, ['linked_care_calendar_task']);
    let parentId = care_calendar_id;
    if (linkedTask.linked_care_calendar_task) {
      parentId = linkedTask.linked_care_calendar_task;
    }
    let baseQuery = q_getLinkedTask(parentId);

    const countQuery = `SELECT COUNT(*) as count FROM (${baseQuery}) as t`
    let caseHistory = await dbConnections.main.manager.query(countQuery);


    /**Create flags based on case-history and health records outputs */
    if (caseHistory.length > 0 && caseHistory[0]?.count > 0) {
      summary[0].case_history = "true"
    } else {
      summary[0].case_history = "false"
    }

    if (health.length > 0) {
      summary[0].health_record = "true"
    } else {
      summary[0].health_record = "false"

    }
    return response(RETURN_CODE.SUCCESS, 200, "Summary Details fetched Succesfully", summary)
  } catch (error) {
    return response(RETURN_CODE.FAILED, 400, error.message, [])
  }

}
module.exports = { getTasksV2, getTaskHistoryV2, getAllTaskDetailsV2, getSummaryDetailsV2 }