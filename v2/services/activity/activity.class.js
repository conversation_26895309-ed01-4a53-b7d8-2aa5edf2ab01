const response = require("../../utils/responseTemplate.js")

const { getTasksV2, getTaskHistoryV2, getAllTaskDetailsV2, getSummaryDetailsV2 } = require("./activity.controller.js")

class LibTaskV2 {
  async getFarmerLevelTaskV2(data, params) {
    try {
      const { token } = params.headers;
      const { pageNo } = params.query;
      const result = await getTasksV2(data, token, pageNo || 0, { querType: "farmer" })
      return result
    } catch (error) {
      return response(RETURN_CODE.FAILED, 400, error.message, { list: list, totalCount: 0 })
    }
  }

  async getAnimalLevelTaskV2(data, params) {
    try {
      const { token } = params.headers;
      const { pageNo } = params.query;
      const result = await getTasksV2(data, token, pageNo || 0, { querType: "animal" })
      return result
    } catch (error) {
      return response(RETURN_CODE.FAILED, 400, error.message, { list: list, totalCount: 0 })
    }
  }
  /**
   * Function: Gets animal history tasks based on parameters.
   * Retrieves animal history task details with pagination and filters, considering the context of a specific animal.
   * @param {Object} data - Object containing from and to parameters for creating hstory records.
   * @param {Object} params - Object containing query parameters and headers, including pageNo. and animal id to get data.
   */

  async getAnimalTaskHistoryV2(data, params) {
    try {
      const { pageNo, animal_id } = params.query;
      const { from, to } = data
      const result = await getTaskHistoryV2(pageNo, animal_id, from, to)
      return result
    } catch (error) {
      return response(RETURN_CODE.FAILED, 400, error.message, { list: list, totalCount: 0 })
    }
  }

  /**
   * Function: Gets animal tasks based on parameters.
   * Retrieves animal task details with pagination and filters, considering the context of a specific customer.
   * @param {Object} data - Object containing from and to parameters for creating activity details.
   * @param {Object} params - Object containing query parameters and headers, including pageNo. and token to get data.
   */
  async getAllAnimalsTaskV2(data, params) {
    try {
      const { pageNo } = params?.query
      const { token } = params?.headers
      const result = await getAllTaskDetailsV2(data, pageNo, token)
      return result
    } catch (error) {
      return response(RETURN_CODE.FAILED, 400, error.message, { list: [], totalCount: 0 })
    }
  }

  /**
  * Function: Gets summary for activity details.
  * Retrieves animal task details summary, considering the context of a specific animal.
  * @param {Object} data - Object containing care_calendar_id and to animal_id for creating activity summary.
  * @param {Object} params - Object containing query parameters and headers, including token.
  */
  async getActivitySummaryV2(data, params) {
    try {
      const result = await getSummaryDetailsV2(data, params)
      return result
    } catch (error) {
      return response(RETURN_CODE.FAILED, 400, error.message, [])
    }
  }
}


module.exports = {
  LibTaskV2
};
