
const {
    CLA<PERSON><PERSON>IER_ID, REFERENCE_ID
} = require("./constants");

module.exports = `
 
    SELECT
        ANIMAL.animal_id AS animal_id,
        COALESCE(CARE_CALENDAR.activity_date,CARE_CALENDAR.completion_date) AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata' AS activity_date,
        CARE_CALENDAR.visit_schedule_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata' AS schedule_time,
        CARE_CALENDAR.activity_id AS activity_id,
        CARE_CALENDAR.entity_type_id AS entity_type_id,
        CARE_CALENDAR.entity_uuid,
        CARE_CALENDAR.care_calendar_id AS care_calendar_id,
        CARE_CALENDAR.calendar_activity_status AS calendar_activity_status,
        REF_ACTIVITY_STATUS.reference_name AS activity_status,
        REF_ACTIVITY_STATUS.reference_name_l10n AS calendar_activity_status_name_json,
        REF_ANIMAL_EAR_TAG.value_string_256 AS ear_tag,
        REF_ANIMAL_NAME.value_string_256 AS animal_name,
        REF_ACTIVITY.activity_name AS activity_name,
        REF_ACTIVITY.activity_name_l10n AS activity_name_l10n,
        REF_ACTIVITY.activity_category AS activity_category,
        REF_ACTIVITY_CATEGORY_NAME.reference_name_l10n as activity_category_name,
        COALESCE(STAFF_ACTIVITY.staff_id, STAFF.staff_id) AS paravet_id, 
        COALESCE(STAFF_OVERRIDE.staff_name_l10n, STAFF.staff_name_l10n) AS paravet_name,
        COALESCE(STAFF_OVERRIDE.mobile_number, STAFF.mobile_number) AS paravet_mobile_number,
        ad.document_id as animal_document_id,
        ad.document_information, 
        ad.client_document_information
        
    FROM
        main.CARE_CALENDAR
            LEFT JOIN main.CARE_CALENDAR_CLASSIFICATION
                ON
                    CARE_CALENDAR.care_calendar_id = CARE_CALENDAR_CLASSIFICATION.care_calendar_id
                    AND
                    CARE_CALENDAR_CLASSIFICATION.classifier_id = ${CLASSIFIER_ID.COMPLAINT}
                    AND
                    CARE_CALENDAR_CLASSIFICATION.active = ${REFERENCE_ID.ACTIVE}
            LEFT JOIN main.REF_ACTIVITY
                ON
                    CARE_CALENDAR.activity_id = REF_ACTIVITY.activity_id
            LEFT JOIN main.REF_REFERENCE REF_ACTIVITY_STATUS
                ON
                    REF_ACTIVITY_STATUS.reference_id = CARE_CALENDAR.calendar_activity_status
            LEFT JOIN main.REF_REFERENCE REF_ACTIVITY_CATEGORY_NAME
            ON
                REF_ACTIVITY_CATEGORY_NAME.reference_id = REF_ACTIVITY.activity_category
            LEFT JOIN main.ENTITY_RELATIONSHIP
                ON
                    ENTITY_RELATIONSHIP.entity_2_entity_uuid = CARE_CALENDAR.entity_uuid
                    AND
                    ENTITY_RELATIONSHIP.entity_relationship_type_id = ${REFERENCE_ID.FARMER_TO_ANIMAL}
                    AND
                    CARE_CALENDAR.entity_type_id = ${REFERENCE_ID.ANIMAL}
            LEFT JOIN main.CUSTOMER
                ON
                    CUSTOMER.customer_id = ENTITY_RELATIONSHIP.entity_1_entity_uuid
                    OR (
                        CUSTOMER.customer_id = CARE_CALENDAR.entity_uuid
                        AND
                        CARE_CALENDAR.entity_type_id = ${REFERENCE_ID.CUSTOMER}
                    )
            LEFT JOIN main.CUSTOMER_CLASSIFICATION
                ON
                    CUSTOMER_CLASSIFICATION.customer_id = CUSTOMER.customer_id
                AND
                    CUSTOMER_CLASSIFICATION.classifier_id = ${CLASSIFIER_ID.FARMER_VILLAGE}
            LEFT JOIN main.REF_VILLAGE
                ON
                    REF_VILLAGE.village_id = CUSTOMER_CLASSIFICATION.value_reference_id
            LEFT JOIN main.ENTITY_GEOGRAPHY
                ON
                    ENTITY_GEOGRAPHY.geography_id = REF_VILLAGE.village_id
                    AND
                    ENTITY_GEOGRAPHY.geography_type_id = ${REFERENCE_ID.VILLAGE}
                    AND
                    ENTITY_GEOGRAPHY.entity_type_id = ${REFERENCE_ID.STAFF_PARAVET}
                    AND
                    ENTITY_GEOGRAPHY.active = ${REFERENCE_ID.ACTIVE}
            LEFT JOIN main.STAFF
                ON
                    STAFF.staff_id = ENTITY_GEOGRAPHY.entity_uuid
            LEFT JOIN main.STAFF_ACTIVITY
                ON
                    STAFF_ACTIVITY.care_calendar = CARE_CALENDAR.care_calendar_id
            LEFT JOIN main.STAFF STAFF_OVERRIDE
                ON
                    STAFF_OVERRIDE.staff_id = STAFF_ACTIVITY.staff_id
            LEFT JOIN main.ANIMAL
                ON
                    ANIMAL.animal_id = ENTITY_RELATIONSHIP.entity_2_entity_uuid
            
            LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_NAME
                ON
                    REF_ANIMAL_NAME.animal_id = ANIMAL.animal_id
                    AND
                    REF_ANIMAL_NAME.classifier_id = ${CLASSIFIER_ID.ANIMAL_NAME}
            LEFT JOIN main.ANIMAL_CLASSIFICATION REF_ANIMAL_EAR_TAG
                ON
                    REF_ANIMAL_EAR_TAG.animal_id = ANIMAL.animal_id
                    AND
                    REF_ANIMAL_EAR_TAG.classifier_id = ${CLASSIFIER_ID.ANIMAL_EAR_TAG}
            LEFT JOIN (
                SELECT ad.entity_1_entity_uuid, min(document_id::text) as document_id
                FROM main.document ad, (
                    SELECT entity_1_entity_uuid, max(created_at) as created_at
                    FROM main.document
                    WHERE document_type_id=${REFERENCE_ID.ANIMAL_THUMBNAIL_PHOTO}
                    GROUP BY entity_1_entity_uuid
                ) admxca 
                WHERE ad.entity_1_entity_uuid = admxca.entity_1_entity_uuid and ad.created_at = admxca.created_at and ad.document_type_id=${REFERENCE_ID.ANIMAL_THUMBNAIL_PHOTO}
                GROUP BY ad.entity_1_entity_uuid
                ) as amdoc on amdoc.entity_1_entity_uuid  = ANIMAL.animal_id
            LEFT JOIN main.document AS ad ON ad.document_id = amdoc.document_id::uuid
        WHERE
            CARE_CALENDAR.entity_type_id IN (${REFERENCE_ID.CUSTOMER}, ${REFERENCE_ID.ANIMAL})
            AND
            CUSTOMER.active = ${REFERENCE_ID.ACTIVE}
            AND (
                ANIMAL.active is null
                OR
                ANIMAL.active = ${REFERENCE_ID.ACTIVE}
            )
            AND CARE_CALENDAR.activity_id != ${REFERENCE_ID.PAREVET_FARM_DATA_COLLECTION} AND CARE_CALENDAR.activity_id != ${REFERENCE_ID.FARM_MANAGEMENT}

            {{#ifEquals farmerFilter 1}}
                AND CUSTOMER.customer_id = :farmer_id
            {{/ifEquals}}

            {{#ifEquals animalFilter 1}}
                AND ANIMAL.animal_id = :animal_id
            {{/ifEquals}}
 
            {{#bothExist from to}}
                AND CARE_CALENDAR.activity_date::DATE >= :from
                AND CARE_CALENDAR.activity_date::DATE <= :to
            {{/bothExist}}

            {{#ifEquals history 1}}
                AND REF_ACTIVITY_STATUS.reference_id IN (${REFERENCE_ID.VISIT_COMPLETED}, ${REFERENCE_ID.VISIT_CANCELLED}, ${REFERENCE_ID.VISIT_ABONDANED}) 
            {{else}}
                {{#ifEquals allTask 1}}
                    AND REF_ACTIVITY_STATUS.reference_id IN ${REFERENCE_ID.ALL_TASK}
                {{else}}
                    AND REF_ACTIVITY_STATUS.reference_id = ${REFERENCE_ID.NEW_CASE_VISIT_PENDING}
                {{/ifEquals}}
            {{/ifEquals}}
                


            AND CARE_CALENDAR.visit_schedule_time IS NOT NULL
            ORDER BY CARE_CALENDAR.visit_schedule_time

            {{#ifEquals pageNumber 1}}
                LIMIT 10 OFFSET (:pageNumber - 1) * 10
            {{/ifEquals}}
 
`;