const { CUSTOMER_ADDRESS_CLASSIFIER_ID, ACTIVE, COMPRESS_DB_SYNC_ID, STAFF_THUMBNAIL_PHOTO_ID, STAFF_ONLINE_CLASSIFIER_ID } = require("./constants");

module.exports = `
{{#ifEquals classifier_id ${CUSTOMER_ADDRESS_CLASSIFIER_ID}}}
    SELECT
      c.customer_id,
      c.customer_type_id,
      cc.value_reference_id,
      c.mobile_number,
      c.customer_name_l10n,
      rr_st.reference_name_l10n AS customer_type_l10n,
      sd.document_information
    FROM
      main.customer c
      INNER JOIN main.ref_reference rr_st ON c.customer_type_id = rr_st.reference_id
      LEFT JOIN main.customer_classification cc ON cc.customer_id = c.customer_id AND cc.classifier_id = ${CUSTOMER_ADDRESS_CLASSIFIER_ID} AND cc.active = ${ACTIVE}
      LEFT JOIN main.document sd ON sd.entity_1_entity_uuid = c.customer_id
    WHERE
      c.mobile_number = '{{phone_number}}'
      AND c.active = ${ACTIVE}
  
{{/ifEquals}}
{{#ifEquals classifier_id ${STAFF_ONLINE_CLASSIFIER_ID}}}
    SELECT 
      s.staff_id, 
      s.staff_type_id, 
      sc.value_reference_id, 
      sccom.value_reference_id AS compress_db_sync, 
      s.mobile_number, 
      s.staff_name_l10n, 
      rr_st.reference_name_l10n AS staff_type_l10n, 
      sd.document_information 
    FROM 
      main.staff s 
      INNER JOIN main.ref_reference rr_st ON s.staff_type_id = rr_st.reference_id 
      LEFT JOIN main.staff_classification sc ON sc.staff_id = s.staff_id AND sc.classifier_id = ${STAFF_ONLINE_CLASSIFIER_ID} AND sc.active = ${ACTIVE} 
      LEFT JOIN main.staff_classification sccom ON sccom.staff_id = s.staff_id AND sccom.classifier_id = ${COMPRESS_DB_SYNC_ID} 
      LEFT JOIN main.document sd ON sd.document_type_id = ${STAFF_THUMBNAIL_PHOTO_ID} AND sd.entity_1_entity_uuid = s.staff_id 
    WHERE 
      s.mobile_number = '{{phone_number}}'
      AND s.active = ${ACTIVE}
{{/ifEquals}}
`;
