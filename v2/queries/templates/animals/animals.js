const { ACTIVE, EAR_TAG_CLASSIFIER_ID, ANIMAL_TYPE_CLASSIFIER_ID, ANIMAL_NAME_CLASSIFIER_ID, ANIMAL_BREED_CLASSIFIER_ID, ANIMAL_AGE_CLASSIFIER_ID, ANIMAL_AVERAGE_LPD, FARMER_TO_ANIMAL_RELATIONSHIP, ANIMAL_THUMBNAIL_ID } = require("./constants");

module.exports = `SELECT 
a.animal_id,
a.animal_visual_id,
a.active,
etag.value_string_256 as ear_tag_1,
aname.value_string_256 as animal_name_1,
age.value_double as age,
avgmilk.value_double as average_milk,
atype.reference_name_l10n as animal_type,
ab.reference_name_l10n as animal_breed_1,
ad.document_id as animal_document_id,
ad.document_information, 
ad.client_document_information,
healthScore.value_double as health_score,
bodyScore.value_double as body_score,
udderScore.value_double as udder_score,
healthScore.updated_at as health_score_updated_at

FROM main.animal as a

JOIN main.entity_relationship AS er ON er.entity_relationship_type_id = ${FARMER_TO_ANIMAL_RELATIONSHIP} 
    AND er.entity_1_entity_uuid = '{{customer_id}}' 
    AND er.entity_2_entity_uuid = a.animal_id 
    AND er.active = ${ACTIVE}

Left JOIN main.animal_classification AS etag ON etag.classifier_id = ${EAR_TAG_CLASSIFIER_ID} 
    AND etag.animal_id = a.animal_id 
    AND etag.active = ${ACTIVE}
LEFT JOIN main.animal_classification AS aname ON aname.classifier_id = ${ANIMAL_NAME_CLASSIFIER_ID} 
    AND aname.animal_id = a.animal_id 
    AND aname.active = ${ACTIVE}

LEFT JOIN main.animal_classification AS atypec ON atypec.classifier_id = ${ANIMAL_TYPE_CLASSIFIER_ID}
    AND atypec.animal_id = a.animal_id
    AND atypec.active = ${ACTIVE}
LEFT JOIN main.ref_reference as atype ON atype.reference_id = atypec.value_reference_id
    AND atype.active = ${ACTIVE} 

LEFT JOIN main.animal_classification AS abc ON abc.classifier_id = ${ANIMAL_BREED_CLASSIFIER_ID}
    AND abc.animal_id = a.animal_id
    AND abc.active = ${ACTIVE}
LEFT JOIN main.animal_classification AS age ON age.classifier_id = ${ANIMAL_AGE_CLASSIFIER_ID}
    AND age.animal_id = a.animal_id
    AND age.active = ${ACTIVE}
LEFT JOIN main.animal_classification AS avgmilk ON avgmilk.classifier_id = ${ANIMAL_AVERAGE_LPD}
    AND avgmilk.animal_id = a.animal_id
    AND avgmilk.active = ${ACTIVE}
LEFT JOIN main.animal_classification AS healthScore ON healthScore.animal_id = a.animal_id
    AND healthScore.classifier_id =  **********
    AND healthScore.active = ${ACTIVE}
LEFT JOIN main.animal_classification AS bodyScore ON bodyScore.animal_id = a.animal_id
    AND bodyScore.classifier_id =  **********
    AND bodyScore.active = ${ACTIVE}
LEFT JOIN main.animal_classification AS udderScore ON udderScore.animal_id = a.animal_id
    AND udderScore.classifier_id =  **********
    AND udderScore.active = ${ACTIVE}
LEFT JOIN main.ref_reference as ab ON ab.reference_id = abc.value_reference_id
    AND ab.active = ${ACTIVE}
LEFT JOIN (
    SELECT ad.entity_1_entity_uuid, min(document_id::text) as document_id
    FROM main.document ad, (
        SELECT entity_1_entity_uuid, max(created_at) as created_at
        FROM main.document
        WHERE document_type_id=${ANIMAL_THUMBNAIL_ID}
        GROUP BY entity_1_entity_uuid
    ) admxca 
    WHERE ad.entity_1_entity_uuid = admxca.entity_1_entity_uuid and ad.created_at = admxca.created_at and ad.document_type_id=${ANIMAL_THUMBNAIL_ID}
    GROUP BY ad.entity_1_entity_uuid
    ) as amdoc on amdoc.entity_1_entity_uuid  = a.animal_id
LEFT JOIN main.document AS ad ON ad.document_id = amdoc.document_id::uuid

WHERE a.active=${ACTIVE} 
AND (
    a.animal_visual_id ILIKE '%{{search}}%' 
    OR etag.value_string_256 ILIKE '%{{search}}%' 
    OR atype.reference_name ILIKE '%{{search}}%'
)
AND (
    {{#if filter}}
    {{#ifEquals type ${ANIMAL_TYPE_CLASSIFIER_ID}}}
        atype.reference_id IN {{filter}}
    {{/ifEquals}}
    {{#ifEquals type ${ANIMAL_BREED_CLASSIFIER_ID}}}
        ab.reference_id IN {{filter}}
    {{/ifEquals}}
    {{else}}
        1=1
    {{/if}}
)
ORDER BY a.animal_visual_id
{{#if page_number}}
    LIMIT {{page_limit}}
    OFFSET ({{page_number}} - 1) * {{page_limit}}
{{/if}}`;