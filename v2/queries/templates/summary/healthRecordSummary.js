const { CLASS<PERSON>IER_ID, REFERENCE_ID } = require("./constants")

module.exports = `SELECT
care_calendar_classification.value_string_256 as service_request_number
FROM
main.care_calendar
LEFT JOIN main.ref_activity ON care_calendar.activity_id = ref_activity.activity_id
LEFT JOIN main.animal ON care_calendar.entity_uuid = animal.animal_id
LEFT JOIN main.care_calendar_classification ON care_calendar_classification.care_calendar_id = care_calendar.care_calendar_id
    AND care_calendar_classification.classifier_id = ${CLASSIFIER_ID.COMPLAINT_NUMBER}
WHERE
ref_activity.activity_category IN (${REFERENCE_ID.PREVENTIVE}, ${REFERENCE_ID.CURATIVE},${REFERENCE_ID.REPRODUCTIVE})
AND care_calendar.entity_type_id = ${REFERENCE_ID.ENTITY_ANIMAL}
AND care_calendar.calendar_activity_status = ${REFERENCE_ID.VISIT_COMPLETED}
AND animal.active = ${REFERENCE_ID.ACTIVE}
AND animal.animal_id = '{{animal_id}}'
AND care_calendar_classification.value_string_256 IS NOT NULL
AND care_calendar_classification.value_string_256 != '{{animal_id}}'
`