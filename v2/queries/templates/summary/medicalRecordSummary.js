const { REFERENCE_ID, CLASSIFIER_ID } = require("./constants")

module.exports = `SELECT cc.entity_uuid,
CASE WHEN diagnosis.value_date   IS NOT  NULL  then 'true' 
ELSE
  'false'
end as diagnosis_completed,
CASE WHEN observation.value_date   IS NOT  NULL  then 'true' 
ELSE
  'false'
end as observation_completed,
CASE WHEN administered.value_date   IS NOT  NULL  then 'true' 
ELSE
  'false'
end as administered_completed,
CASE 
  WHEN administered.value_date   IS NOT  NULL  then 'false' 
  WHEN published.value_date   IS NOT  NULL  then 'true' 
ELSE
  'false'
end as published_completed
FROM main.care_calendar as cc
LEFT JOIN main.care_calendar_classification as published ON published.care_calendar_id = cc.care_calendar_id and published.classifier_id = ${CLASSIFIER_ID.PRESCRIPTION_PUBLISHED} and 
published.active = ${REFERENCE_ID.ACTIVE}
LEFT JOIN main.care_calendar_classification as administered ON administered.care_calendar_id = cc.care_calendar_id and administered.classifier_id = ${CLASSIFIER_ID.PRESCRIPTION_ADMINISTERED} and 
administered.active = ${REFERENCE_ID.ACTIVE}
LEFT JOIN main.care_calendar_classification as diagnosis ON diagnosis.care_calendar_id = cc.care_calendar_id and diagnosis.classifier_id = ${CLASSIFIER_ID.DIAGNOSIS} and 
diagnosis.active = ${REFERENCE_ID.ACTIVE}
LEFT JOIN main.care_calendar_classification as observation ON observation.care_calendar_id = cc.care_calendar_id and observation.classifier_id = ${CLASSIFIER_ID.OBSERVATION} and 
observation.active = ${REFERENCE_ID.ACTIVE}
where cc.care_calendar_id = '{{care_calendar_id}}'
and cc.ACTIVE = ${REFERENCE_ID.ACTIVE}
`