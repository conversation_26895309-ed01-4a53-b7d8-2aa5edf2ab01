const { RELATION_FREELANCE_PARAVET_TO_ANIMAL, ANIMAL_EAR_TAG_CLASSIFIER, FARMER_TO_ANIMAL, ANIMAL_NAME_CLASSIFIER, ANIMAL_TYPE_CLASSIFIER, BREED_OF_ANIMAL, ANIMAL_SUBSCRIPTION_DATE_CLASSIFIER, ANIMAL_SUBSCRIPTION_PLAN_CLASSIFIER, ANIMAL_HEALTH_SCORE, ACTIVE, ANIMAL_THUMBNAIL_PHOTO } = require('../utils/constant')

const { FARMER, PPU_FARMER } = require('../utils/constant')
const { dbConnections } = require('@krushal-it/ah-orm')

async function getAnimalsByFarmerID(options = {}) {
  const { customer_id } = options

  const query = `
    SELECT 
	ER.ENTITY_1_ENTITY_UUID as customer_id,
	A.animal_id,
    A.animal_visual_id,
    ER_FREE_LANCER_TO_FARMER.ENTITY_1_ENTITY_UUID as associated_paravet_id,
    ER_FREE_LANCER_TO_FARMER.ENTITY_1_TYPE_ID as associated_paravet_type_id,
	ETAG.VALUE_STRING_256 AS EAR_TAG_1,
	ANAME.VALUE_STRING_256 AS ANIMAL_NAME_1,
	ATYPE.REFERENCE_NAME_L10N AS ANIMAL_TYPE,
	AB.REFERENCE_NAME_L10N AS ANIMAL_BREED_1,
	SD.VALUE_DATE AS HEALTHCARE_PLAN_SUBSCRIPTION_DATE_1,
	STYPE.REFERENCE_NAME_L10N AS SUBSCRIPTION_PLAN_1,
	AD.DOCUMENT_ID AS ANIMAL_DOCUMENT_ID,
	AD.DOCUMENT_INFORMATION,
	AD.CLIENT_DOCUMENT_INFORMATION,
	HEALTHSCORE.VALUE_DOUBLE AS HEALTH_SCORE,
	HEALTHSCORE.UPDATED_AT AS HEALTH_SCORE_UPDATED_AT
        FROM MAIN.ANIMAL A
        LEFT JOIN MAIN.ENTITY_RELATIONSHIP AS ER ON ER.ENTITY_RELATIONSHIP_TYPE_ID = ${FARMER_TO_ANIMAL}
        AND ER.ENTITY_2_ENTITY_UUID = A.ANIMAL_ID
        LEFT JOIN MAIN.ENTITY_RELATIONSHIP AS ER_FREE_LANCER_TO_FARMER ON ER_FREE_LANCER_TO_FARMER.ENTITY_RELATIONSHIP_TYPE_ID = ${RELATION_FREELANCE_PARAVET_TO_ANIMAL}
        AND ER_FREE_LANCER_TO_FARMER.ENTITY_2_ENTITY_UUID = A.ANIMAL_ID
        AND ER_FREE_LANCER_TO_FARMER.ACTIVE = ${ACTIVE}
        LEFT JOIN MAIN.ANIMAL_CLASSIFICATION AS ETAG ON ETAG.CLASSIFIER_ID = ${ANIMAL_EAR_TAG_CLASSIFIER}
        AND ETAG.ANIMAL_ID = A.ANIMAL_ID
        AND ETAG.ACTIVE = ${ACTIVE}
        LEFT JOIN MAIN.ANIMAL_CLASSIFICATION AS ANAME ON ANAME.CLASSIFIER_ID = ${ANIMAL_NAME_CLASSIFIER}
        AND ANAME.ANIMAL_ID = A.ANIMAL_ID
        AND ANAME.ACTIVE = ${ACTIVE}
        LEFT JOIN MAIN.ANIMAL_CLASSIFICATION AS ATYPEC ON ATYPEC.CLASSIFIER_ID = ${ANIMAL_TYPE_CLASSIFIER}
        AND ATYPEC.ANIMAL_ID = A.ANIMAL_ID
        AND ATYPEC.ACTIVE = ${ACTIVE}
        LEFT JOIN MAIN.REF_REFERENCE AS ATYPE ON ATYPEC.VALUE_REFERENCE_ID = ATYPE.REFERENCE_ID
        AND ATYPE.ACTIVE = ${ACTIVE}
        LEFT JOIN MAIN.ANIMAL_CLASSIFICATION AS ABC ON ABC.CLASSIFIER_ID = ${BREED_OF_ANIMAL}
        AND ABC.ANIMAL_ID = A.ANIMAL_ID
        AND ABC.ACTIVE = ${ACTIVE}
        LEFT JOIN MAIN.REF_REFERENCE AS AB ON AB.REFERENCE_ID = ABC.VALUE_REFERENCE_ID
        AND AB.ACTIVE = ${ACTIVE}
        LEFT JOIN MAIN.ANIMAL_CLASSIFICATION AS SD ON SD.CLASSIFIER_ID = ${ANIMAL_SUBSCRIPTION_DATE_CLASSIFIER}
        AND SD.ANIMAL_ID = A.ANIMAL_ID
        AND SD.ACTIVE = ${ACTIVE}
        LEFT JOIN MAIN.ANIMAL_CLASSIFICATION AS STYPEC ON STYPEC.CLASSIFIER_ID = ${ANIMAL_SUBSCRIPTION_PLAN_CLASSIFIER}
        AND STYPEC.ANIMAL_ID = A.ANIMAL_ID
        AND STYPEC.ACTIVE = ${ACTIVE}
        LEFT JOIN MAIN.ANIMAL_CLASSIFICATION AS HEALTHSCORE ON HEALTHSCORE.CLASSIFIER_ID = ${ANIMAL_HEALTH_SCORE}
        AND HEALTHSCORE.ANIMAL_ID = A.ANIMAL_ID
        AND HEALTHSCORE.ACTIVE = ${ACTIVE}
        LEFT JOIN MAIN.REF_REFERENCE AS STYPE ON STYPE.REFERENCE_ID = STYPEC.VALUE_REFERENCE_ID
        AND STYPEC.ACTIVE = ${ACTIVE}
        LEFT JOIN
            (SELECT AD.ENTITY_1_ENTITY_UUID,
                    MIN(DOCUMENT_ID::text) AS DOCUMENT_ID
                FROM MAIN.DOCUMENT AD,
                    (SELECT ENTITY_1_ENTITY_UUID,
                            MAX(CREATED_AT) AS CREATED_AT
                        FROM MAIN.DOCUMENT
                        WHERE DOCUMENT_TYPE_ID = ${ANIMAL_THUMBNAIL_PHOTO}
                        GROUP BY ENTITY_1_ENTITY_UUID) ADMXCA
                WHERE AD.ENTITY_1_ENTITY_UUID = ADMXCA.ENTITY_1_ENTITY_UUID
                    AND AD.CREATED_AT = ADMXCA.CREATED_AT
                    AND AD.DOCUMENT_TYPE_ID = ${ANIMAL_THUMBNAIL_PHOTO}
                GROUP BY AD.ENTITY_1_ENTITY_UUID) AS AMDOC ON AMDOC.ENTITY_1_ENTITY_UUID = A.ANIMAL_ID
        LEFT JOIN MAIN.DOCUMENT AS AD ON AD.DOCUMENT_ID = AMDOC.DOCUMENT_ID::UUID
        WHERE ER.ENTITY_1_ENTITY_UUID = :customer_id`

  const queryBuilder = await dbConnections().main.manager.createQueryBuilder().from(`(${query})`).setParameters({ customer_id })
  const result = queryBuilder.execute()
  return result
}

function isAFarmer(user_type) {
  return [FARMER, PPU_FARMER].find((farmer) => farmer === user_type) == undefined ? false : true
}

module.exports = {
  getAnimalsByFarmerID,
}
