const { CLASSIFIER_FREELANCE_SLAB_SR_TICKET, PAY_PER_USE_PARAVET, TRANSACTION_DEBIT, TRANSACTION_CONFIRMED, FREELANCE_DEFAULT_PLAN, FREELANCE_SLAB } = require('../utils/constant')
const { saveClassificationData } = require('../services/common/classification.helper')
module.exports = function (transaction) {
  this.enrollToFreelancingPlan = async function enrollToFreelancingPlan(staff_info) {
    const { staff_type_id, staff_id, subscription_plan_start_date, staff_plan } = staff_info

    if (staff_type_id !== PAY_PER_USE_PARAVET) return new Error(`Cannot enroll staff of type "${CONST_TYPES_STAFF[staff_type_id]}" to free-lancing plan`)

    if (!subscription_plan_start_date || !staff_plan || !staff_id) 
      return new Error(`One or many required fields (staff_id, subscription_plan_start_date, staff_plan) are missing`)

    const { slabs } = await getSlabsRate()
    const relative_new_slabs_rate = await calculateNewSlabsRate(subscription_plan_start_date, slabs)

    //cannot insert multiple values using 'saveClassificationData' so had to insert it manually
    transaction.getRepository('staff_classification').insert({
      staff_id: staff_id,
      classifier_id: CLASSIFIER_FREELANCE_SLAB_SR_TICKET,
      value_json: { slabs: relative_new_slabs_rate },
      value_date: getMonthsLastDate(subscription_plan_start_date),
    })

    const staffClassification = {
      staff_plan: staff_plan,
      staff_plan_start_date: subscription_plan_start_date,
    }
    await saveClassificationData(transaction, 'STAFF_CLASSIFICATION', staff_id, staffClassification)

    const { plan_amount , plan_name} = await getPlanAmount(staff_plan)
    await transaction.getRepository('transaction_ledger').insert({
      staff_uuid: staff_id,
      amount: plan_amount,
      account_id: "KRUSHAL", 
      transaction_type: TRANSACTION_DEBIT,
      transaction_category: FREELANCE_DEFAULT_PLAN,
      transaction_date: new Date(),
      transaction_status: TRANSACTION_CONFIRMED,
      transaction_description :  { notes : plan_name }
    })

    return { staff_id, staff_type_id, plan_amount, slabs_rate: relative_new_slabs_rate }
  }
  async function getSlabsRate() {
    const { reference_information } = await transaction.createQueryBuilder().select('reference_information').from('ref_reference', 't1').where('t1.reference_id = :id', { id: FREELANCE_SLAB }).getRawOne()
    return reference_information
  }

  function calculateNewSlabsRate(date, slabs) {
    const start_date = new Date(date)
    const last_date_of_month = new Date(start_date.getFullYear(), start_date.getMonth() + 1, 0).getDate()
    const remaining_days = last_date_of_month - start_date.getDate() + 1
    const transfrom = (value) => Math.round((remaining_days / last_date_of_month) * value)
    return slabs.map((slab) => ({ start: transfrom(slab.start), end: slab.end ? transfrom(slab.end) : undefined, price: slab.price }))
  }

  async function getPlanAmount(staff_plan) {
    const result = await transaction.createQueryBuilder().select(['reference_information','reference_name']).from('ref_reference', 't1').where('t1.reference_id = :plan_reference_id', { plan_reference_id: staff_plan }).getRawOne()
    let plan_amount = result && result.reference_information && result.reference_information.price ? result.reference_information.price : 0
    let plan_name = result && result.reference_name ? result.reference_name : ''
    return { plan_amount , plan_name} 
  }

  function getMonthsLastDate(dateString) {
    const date = new Date(dateString)
    const last_date_of_month = new Date(date.getFullYear(), date.getMonth() + 1, 0)
    const year = last_date_of_month.toLocaleString('default', { year: 'numeric' })
    const month = last_date_of_month.toLocaleString('default', {
      month: '2-digit',
    })
    const day = last_date_of_month.toLocaleString('default', { day: '2-digit' })
    return [year, month, day].join('-')
  }
}
