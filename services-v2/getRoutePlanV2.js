const {
    CONST_ID_TYPE_STAFF_PARAVET,
    CONST_ID_TYPE_STAFF_BACKOFFICE,
    REPRODUCTIVE,
    CURATIVE,
    TICKET_CLASSIFIER,
    FARMER_TO_ANIMAL,
    VILLAGE_CLASSIFIER,
    VILLAGE_TYPE_ID,
    PARAVET,
    ANIMAL_NAME_CLASSIFIER,
    ANIMAL_TYPE_CLASSIFIER,
    ANIMAL_EAR_TAG_CLASSIFIER,
    FARMER_TYPE_CALENDAR,
    ANIMAL_TYPE_CALENDAR,
    COMPLETED_ACTIVITY_STATUS,
    CANCELLED_ACTIVITY_STATUS,
    ABANDONED_ACTIVITY_STATUS,
    ACTIVE,
    PPU_PARAVET_TO_TASK,
    CENTRALIZED_VET,
    CENTRALIZED_VET_TO_TASK
  } = require('../utils/constant');
  const { getCompiledQuery } = require('../queries/index.js');
  const { dbConnections, postProcessRecords } = require('@krushal-it/ah-orm');
  const { configurationJSON, post } = require('@krushal-it/common-core');
  
  const getRoutePlanV2 = async (data, user_id, user_type) => {
    try {
      const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0;
  
      if (!user_id || !user_type) throw new Error('user_id and user_type are required');
  
      const notUsersForvill = new Set([CONST_ID_TYPE_STAFF_BACKOFFICE, CONST_ID_TYPE_STAFF_PARAVET]);
      if (data.f_farmer_name) {
        data.f_farmer_name = `%${data.f_farmer_name.toUpperCase()}%`;
      }

      //for completed and opened task
      if (data.task_status) {
        if (Array.isArray(data.task_status) && data.task_status.length == 1) {
            data.task_status = data.task_status[0]
        }
        if (Array.isArray(data.task_status) && data.task_status.length !=1){
          delete data.task_status
        }
      }
      let entity_relationship_type_id = PPU_PARAVET_TO_TASK
      let user_ids = [user_id]
      if( user_type === CENTRALIZED_VET ) {
        if ( data.f_paravet && data.f_paravet.length ) {
          user_ids = data.f_paravet
        }
        else{
          entity_relationship_type_id = CENTRALIZED_VET_TO_TASK
        }
      }
      const queryParameters = { ...data, user_ids, user_type ,entity_relationship_type_id};
      const templateParameters = {
        ...data,
        user_ids,
        user_type,
        notUsersForvill,
        REPRODUCTIVE,
        CURATIVE,
        TICKET_CLASSIFIER,
        FARMER_TO_ANIMAL,
        FARMER_TYPE_CALENDAR,
        VILLAGE_CLASSIFIER,
        VILLAGE_TYPE_ID,
        PARAVET,
        ANIMAL_NAME_CLASSIFIER,
        ANIMAL_TYPE_CLASSIFIER,
        ANIMAL_EAR_TAG_CLASSIFIER,
        ANIMAL_TYPE_CALENDAR,
        COMPLETED_ACTIVITY_STATUS,
        CANCELLED_ACTIVITY_STATUS,
        ABANDONED_ACTIVITY_STATUS,
        ACTIVE,
        IS_POSTGRES: isPostgres,
        entity_relationship_type_id
      };
  

      let query = getCompiledQuery('route-plan-v2', templateParameters);
      const queryBuilder = await dbConnections().main.manager.createQueryBuilder().from(`(${query})`, 't1').setParameters(queryParameters);
      let count = await queryBuilder.select('COUNT(*) as count').execute();
      count = parseInt(count[0].count);
      let total_page = 1;
      if (!isNaN(data.page_number) && !isNaN(data.page_limit)) {
        let offset = data.page_limit * (data.page_number - 1);
        if (offset < 0) return { message: 'Invalid page number' };
        queryBuilder.skip(offset).take(data.page_limit);
        total_page = parseInt(count / data.page_limit);
        total_page += count % data.page_limit === 0 ? 0 : 1;
      }
  
      const routePlan = await queryBuilder.select('*').execute();
      postProcessRecords(undefined, routePlan, { json_columns: ['customer_name', 'fcca_name_json', 'fcca_status_json', 'fncca_name_json', 'fncca_status_json', 'taluk_name', 'village_name'] });
      return { routePlan };
    } catch (error) {
        return new Error(`error in getRoutePlan'  ${ {...data, user_type, user_id} }, ${ {message: error} }`);
    }
};
  
module.exports = {
    getRoutePlanV2
}