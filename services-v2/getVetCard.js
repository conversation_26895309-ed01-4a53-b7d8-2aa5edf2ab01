const { getCompiledQuery } = require('../queries/index')
const { dbConnections, preProcessRecords, postProcessRecords } = require('@krushal-it/ah-orm')
const { configurationJSON } = require('@krushal-it/common-core')
const {  BACK_OFFICE_PERSON } = require('../utils/constant')

async function getVetCard(options = {}) {

    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
  const {
    user_id,
    user_type,
    staff_type,
    all_stats,
    stats_by_user,
    stats_by_staff_type,
    number_of_days } = options


    const queryParameters = { ...options, user_id, user_type, staff_type }
    const templateParameters = {
      ...options,
      user_id,
      user_type,
      all_stats,
      stats_by_staff_type,
      stats_by_user,
      number_of_days,
      IS_POSTGRES: isPostgres
    }

  let activityStatsQuery = getCompiledQuery('activity-stats-v2', templateParameters);
    const activityStatsQueryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${activityStatsQuery})`, 'activity_stats').setParameters(queryParameters);
    const activityStatsResult = await activityStatsQueryBuilder.execute();

    postProcessRecords(undefined, activityStatsResult, { json_columns: ['staff_name_l10n', 'document_information', 'client_document_information'] })

    let activityStatsResponse = [];
    for (let i = 0; i < activityStatsResult.length; i++) {
      let responseItem = {};
      responseItem.staff_id = activityStatsResult[i].staff_id;
      responseItem.staff_name_l10n = activityStatsResult[i].staff_name_l10n;
      responseItem.document_id = activityStatsResult[i].document_id;
      responseItem.document_information = activityStatsResult[i].document_information;
      responseItem.client_document_information = activityStatsResult[i].client_document_information;
      responseItem.Preventive = { till_next: activityStatsResult[i]?.preventive_count_15 ?? 0, today: activityStatsResult[i]?.preventive_count ?? 0, category: 1000270001 };
      responseItem.Critical = { till_next: activityStatsResult[i]?.critical_count_15 ?? 0, today: activityStatsResult[i]?.critical_count ?? 0, category: [1000270002, 1000270003] };
      responseItem.Farmer = { till_next: activityStatsResult[i]?.farmer_count_15 ?? 0, today: activityStatsResult[i]?.farmer_count ?? 0  };

      activityStatsResponse.push(responseItem);
    }

    return activityStatsResponse;
}
  
module.exports = { getVetCard }