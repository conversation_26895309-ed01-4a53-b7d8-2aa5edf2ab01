const { dbConnections,postProcessRecords } = require("@krushal-it/ah-orm")
const { record_statuses } = require("../../ENUMS")

async function getActivities() {
    let ref_activity = dbConnections()
      .main.manager.getRepository("ref_activity")
      .createQueryBuilder()
      .select("activity_id, activity_name, activity_name_l10n, activity_category, rf.reference_name as activity_category_name, rf.reference_name_l10n as activity_category_name_l10n, activity_information")
      .leftJoin("ref_reference", "rf", "rf.reference_id = activity_category")
      .orderBy("ref_activity.updated_at", "DESC")
  
    let result = await ref_activity.where("ref_activity.active = :active", { active: record_statuses.ACTIVE }).execute()

    return {
      count: result.length,
      report:postProcessRecords(undefined, result, { json_columns: ['activity_category_name_l10n', 'activity_name_l10n'] }),
      return_code: 0
    }
}
module.exports = {getActivities}