const { dbConnections } = require('@krushal-it/ah-orm');
const { getCompiledQuery,loadQueryTemplates } = require('../queries/index')
const { task_infromation_by_task_id } = require('../queries/tasks')

async function getTasks(options, filters) {

        const { farmer_id, animal_id } = options;
        const query = getCompiledQuery('tasks-by-entity', { farmer_id, animal_id });
        
        const queryBuilder = await dbConnections().main.manager.createQueryBuilder()
            .from(`(${query})`).setParameters({ farmer_id, animal_id });
       
        const result = queryBuilder.execute();
        return result;
    
}

async function get_task_infromation_by_id(care_calendar_id) {

    const result = await dbConnections().main.manager.createQueryBuilder()
        .from(`(${task_infromation_by_task_id})`)
        .setParameters({ care_calendar_id })
        .execute();
    return result;
    
}

module.exports = {
    getTasks,
    get_task_infromation_by_id
};

