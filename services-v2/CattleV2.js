const { FREELANCE_CARE_CATTLE_DEFAULT, PAY_PER_USE_PARAVET, RELATION_FREELANCE_PARAVET_TO_ANIMAL, ANIMAL_TYPE_CALENDAR, FARMER_TO_ANIMAL, FARMER_TYPE_CALENDAR, ACTIVE } = require('../utils/constant')
const { dbConnections } = require('@krushal-it/ah-orm')
const { generateAnimalVisualId } = require('../utils/animal')
const { saveClassificationData } = require('../services/common/classification.helper')
const moment = require('moment');

// This function is missing use of CONSTANTS, need to do it
module.exports = function () {
  this.registerCattleV2 = async function registerCattleV2(data) {
    if (!data.customer_id || !data.paravet_id) 
      throw new Error('Either or both customer_id and paravet_id is missing')

    //feilds necessary for registring a cattle.
    const mandatory_feilds = ['animal_type']

    const cattle_visual_id = await generateAnimalVisualId(data.customer_id)
    const new_animal = {
      animal_visual_id: cattle_visual_id,
    }

    for (let index = 0; index < mandatory_feilds.length; index++) {
      const key = mandatory_feilds[index]
      if (data[key] == undefined) {
        throw new Error(`Invalid payload! mandatory field "${key}" is not present.`)
      }
      const value = data[key]
      new_animal[key] = value[0]
    }

    // if ear_tag is null, will generate from animal_visual_id
    if (data['ear_tag_1'] == undefined) {
      data['ear_tag_1'] = cattle_visual_id
    }

    const created_animal_id = await dbConnections().main.manager.transaction(async (transaction) => {
      let query_response = await transaction.createQueryBuilder().insert().into('animal').values(new_animal).returning('animal_id').execute()
      const animal_id = query_response.identifiers[0].animal_id
      let entity_relationship_freelance_paravet_animal = {
        entity_relationship_type_id: RELATION_FREELANCE_PARAVET_TO_ANIMAL,
        entity_1_type_id: PAY_PER_USE_PARAVET,
        entity_2_type_id: ANIMAL_TYPE_CALENDAR,
        entity_1_entity_uuid: data.paravet_id,
        entity_2_entity_uuid: animal_id,
      }

      let entity_relationship_farmer_animal = {
        entity_relationship_type_id: FARMER_TO_ANIMAL,
        entity_1_entity_id: FARMER_TYPE_CALENDAR,
        entity_2_entity_id: ANIMAL_TYPE_CALENDAR,
        entity_1_entity_uuid: data.customer_id,
        entity_2_entity_uuid: animal_id,
      }

      const animalClassification = {
        subscription_plan_1: FREELANCE_CARE_CATTLE_DEFAULT,
        healthcare_plan_subscription_date_1: moment().format('YYYY-MM-DD'),
        ...data,
      }

      await saveClassificationData(transaction, 'ANIMAL_CLASSIFICATION', animal_id, animalClassification)
      await transaction.getRepository('entity_relationship').insert([entity_relationship_farmer_animal, entity_relationship_freelance_paravet_animal])

      return animal_id
    })

    return { key: created_animal_id }
  }

  this.enrollCattle = async function enrollCattle(data) {
    const { paravet_id, animal_id } = data
    if (paravet_id == undefined && animal_id == undefined) {
      throw new Error('Either one or both of the necessary feilds are missing (paravet_id, animal_id)')
    }

    return await dbConnections()
      .main.manager.createQueryBuilder()
      .insert()
      .into('entity_relationship')
      .values({
        entity_relationship_type_id: RELATION_FREELANCE_PARAVET_TO_ANIMAL,
        entity_1_type_id: PAY_PER_USE_PARAVET,
        entity_2_type_id: ANIMAL_TYPE_CALENDAR,
        entity_1_entity_uuid: paravet_id,
        entity_2_entity_uuid: animal_id,
        active: ACTIVE,
      })
      .returning('entity_relationship_id')
      .execute()
  }
}
