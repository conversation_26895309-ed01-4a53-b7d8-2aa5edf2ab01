// const { st } = require("translate-google/languages");
const { CONST_ID_CLASSIFIER_TALUKA, CONST_ID_CLASSIFIER_VILLAGE, CONST_ID_CLASSIFIER_DISTRICT, CONST_ID_CLASSIFIER_STATE, ACTIVE, PPU_PARAVET_TO_FARMER } = require("../utils/constant");

const { dbConnections } = require('@krushal-it/ah-orm');

async function getFarmerInfoByMobileNumber(mobile_number) {
	const query = `SELECT CUSTOMER.CUSTOMER_ID,
	CUSTOMER.CUSTOMER_NAME_L10N,
	CUSTOMER.CUSTOMER_ID,
	CUSTOMER.CUSTOMER_VISUAL_ID,
	CUSTOMER.MOBILE_NUMBER,
	REF_VILLAGE.VILLAGE_NAME_L10N,
	REF_TALUK.TALUK_NAME_L10N,
	REF_DISTRICT.DISTRICT_NAME_L10N,
	REF_STATE.STATE_NAME_L10N
FROM MAIN.CUSTOMER
LEFT JOIN MAIN.CUSTOMER_CLASSIFICATION CC_VILLAGE ON CUSTOMER.CUSTOMER_ID = CC_VILLAGE.CUSTOMER_ID
AND CC_VILLAGE.CLASSIFIER_ID = ${CONST_ID_CLASSIFIER_VILLAGE} AND CC_VILLAGE.ACTIVE = ${ACTIVE}
LEFT JOIN MAIN.REF_VILLAGE ON REF_VILLAGE.VILLAGE_ID = CC_VILLAGE.VALUE_REFERENCE_ID
LEFT JOIN MAIN.CUSTOMER_CLASSIFICATION CC_TALUK ON CUSTOMER.CUSTOMER_ID = CC_TALUK.CUSTOMER_ID
AND CC_TALUK.CLASSIFIER_ID = ${CONST_ID_CLASSIFIER_TALUKA} AND CC_TALUK.ACTIVE = ${ACTIVE}
LEFT JOIN MAIN.REF_TALUK ON REF_TALUK.TALUK_ID = CC_TALUK.VALUE_REFERENCE_ID AND REF_TALUK.ACTIVE= ${ACTIVE}
LEFT JOIN MAIN.CUSTOMER_CLASSIFICATION CC_DISTRICT ON CUSTOMER.CUSTOMER_ID = CC_DISTRICT.CUSTOMER_ID 
AND CC_DISTRICT.CLASSIFIER_ID = ${CONST_ID_CLASSIFIER_DISTRICT} AND CC_DISTRICT.ACTIVE= ${ACTIVE}
LEFT JOIN MAIN.REF_DISTRICT ON REF_DISTRICT.DISTRICT_ID = CC_DISTRICT.VALUE_REFERENCE_ID AND REF_DISTRICT.ACTIVE= ${ACTIVE}
LEFT JOIN MAIN.CUSTOMER_CLASSIFICATION CC_STATE ON CUSTOMER.CUSTOMER_ID = CC_STATE.CUSTOMER_ID
AND CC_STATE.ACTIVE = ${ACTIVE} AND CC_STATE.CLASSIFIER_ID = ${CONST_ID_CLASSIFIER_STATE}
LEFT JOIN MAIN.REF_STATE ON REF_STATE.STATE_ID = CC_STATE.VALUE_REFERENCE_ID AND REF_STATE.ACTIVE=${ACTIVE}
WHERE CUSTOMER.MOBILE_NUMBER = :mobile_number
LIMIT 1`
	const result = await dbConnections().main.manager.createQueryBuilder()
		.from(`(${query})`).setParameters({ mobile_number }).execute();

	return result;
}

const checkFreelanceCustomerRelationAlreadyExist = async (staff_id, customerId) => {
	try {
		const result = await dbConnections().main.repos["entity_relationship"].find({
			where: { entity_1_entity_uuid: staff_id, entity_2_entity_uuid: customerId, entity_relationship_type_id: PPU_PARAVET_TO_FARMER, active: ACTIVE },
			select: { entity_relationship_id: true }
		});
		return result
	} catch (error) {
		throw new Error("something went wrong")
	}
}

module.exports = {
	getFarmerInfoByMobileNumber,
	checkFreelanceCustomerRelationAlreadyExist
}