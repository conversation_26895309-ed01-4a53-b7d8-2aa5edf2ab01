const { dbConnections } = require('@krushal-it/ah-orm');
const freelannceParavetDashboard = require("../queries/freelanceParavetDashboard")

async function getParavetPreventiveDashboard(data) {
    const { paravet_id } = data;
    const result = await dbConnections().main.manager.createQueryBuilder()
        .from(`(${freelannceParavetDashboard})`).setParameters({ paravet_id }).execute();
    
    return result;
}

module.exports = {
    getParavetPreventiveDashboard
}
