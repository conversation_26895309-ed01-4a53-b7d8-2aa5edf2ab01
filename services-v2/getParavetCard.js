const { CONST_STATUS_ACTIVITY_COMPLETED, REPRODUCTIVE, CURATIVE, PREVENTIVE, CANCELLED_ACTIVITY_STATUS, ABANDONED_ACTIVITY_STATUS, PPU_PARAVET_TO_TASK, ACTIVE } = require("../utils/constant");
const { dbConnections } = require('@krushal-it/ah-orm');

async function getParavetCard(options) {
    const { paravet_id } = options;
    const query =
        `select
        'tasks' as category,
        count(*) as total,
        count(*) filter (where cc.calendar_activity_status NOT IN (${CONST_STATUS_ACTIVITY_COMPLETED}, ${CANCELLED_ACTIVITY_STATUS}, ${ABANDONED_ACTIVITY_STATUS})) as pending   
        from  main.staff as staff
        left join main.entity_relationship pvet_er on pvet_er.entity_1_entity_uuid=staff.staff_id and pvet_er.entity_relationship_type_id = ${PPU_PARAVET_TO_TASK} and pvet_er.active=${ACTIVE}
        join main.care_calendar cc on cc.care_calendar_id = pvet_er.entity_2_entity_uuid
        where staff.staff_id= :paravet_id`
    
    const queryBuilder = await dbConnections().main.manager.createQueryBuilder()
        .from(`(${query})`).setParameters({ paravet_id });
    const result = queryBuilder.execute();
    return result;
}

module.exports = {
    getParavetCard
}
