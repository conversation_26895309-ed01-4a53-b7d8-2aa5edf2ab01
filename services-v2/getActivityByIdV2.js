const {
  FARMER_TO_ANIMAL,
  DOCUMENT_CUSTOMER_THUMBNAIL_TYPE,
  ACTIVE, ANIMAL_NAME_CLASSIFIER,
  ANIMAL_EAR_TAG_CLASSIFIER,
  DOCUMENT_STAFF_THUMBNAIL_TYPE,
  VET_SIGNATURE,
  ANIMAL_THUMBNAIL_PHOTO,
  TICKET_CLASSIFIER,
  CONST_ID_CLASSIFIER_VILLAGE,
  CENTRALIZED_VET_TO_TASK,
  PPU_PARAVET_TO_TASK
} = require("../utils/constant");

const {  dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm');

async function getActivityByIdV2(options) {
  const { care_calender_id } = options;
  const query = `
    SELECT
  cc.created_at,
  cc.updated_at,
    cc.activity_id,
    cc.entity_uuid,
    cc.entity_type_id,
    cc.activity_date AS activity_date,
    cc.completion_date as completion_date, 
    cc.care_calendar_id,
    cc.calendar_activity_status,
    cc.system_task_completion_time,
    cc.visit_schedule_time,
    cc.visit_creation_time,
    cc.user_task_completion_time,
    null as expiry_date,
    tn.value_string_256 as ticket_number,
    acat.activity_name_l10n,
    acat.activity_category,
    a.animal_visual_id,
    etag.value_string_256 as ear_tag,
    atype.reference_name_l10n::text as animal_type_json,
    atype.reference_name_l10n as animal_type_json,
     aname.value_string_256 as animal_name,
     astatus.reference_name_l10n as calendar_activity_status_name_json,
    c.customer_id,
    c.customer_name_l10n,
    c.customer_visual_id,
    c.mobile_number AS farmer_contact,
    vg.village_name_l10n,
    COALESCE(paravet_staff.staff_id)AS staff_id,
    COALESCE(paravet_staff.staff_name_l10n) as paravet_name,
    COALESCE(paravet_staff.mobile_number) as pravet_mobile_number,
    fd.document_id as farmer_document_id,
    COALESCE(pd2.document_id,pd.document_id) as paravet_document_id,
    COALESCE(vet_staff.staff_name_l10n,vet_staff.staff_name_l10n) as vet_name,
    vet_signature.document_information as vet_signature_doc_info,
    vet_signature.client_document_information as vet_signature_client_doc_info,
    ad.document_id as animal_document_id
    FROM main.care_calendar as cc
    LEFT JOIN main.care_calendar_classification as tn ON tn.care_calendar_id = cc.care_calendar_id and tn.classifier_id = ${TICKET_CLASSIFIER} and tn.active = ${ACTIVE}
    LEFT JOIN main.ref_reference as astatus on astatus.reference_id = cc.calendar_activity_status and astatus.active = ${ACTIVE}
    LEFT JOIN main.ref_activity as acat on acat.activity_id = cc.activity_id and acat.active = ${ACTIVE}
    LEFT JOIN main.animal as a on a.animal_id = cc.entity_uuid and a.active = ${ACTIVE}
    LEFT JOIN main.animal_classification as ac_animal_type on ac_animal_type.animal_id = a.animal_id and ac_animal_type.classifier_id = 2000000035 and ac_animal_type.active = ${ACTIVE}
    LEFT JOIN main.ref_reference as atype on atype.reference_id = ac_animal_type.value_reference_id and atype.active = ${ACTIVE}
    LEFT JOIN main.animal_classification as etag on etag.animal_id = a.animal_id and etag.classifier_id = ${ANIMAL_EAR_TAG_CLASSIFIER} and etag.active =${ACTIVE}
    LEFT JOIN main.animal_classification as aname on aname.animal_id = a.animal_id and aname.classifier_id = ${ANIMAL_NAME_CLASSIFIER}
    LEFT JOIN main.entity_relationship as er on er.entity_relationship_type_id = ${FARMER_TO_ANIMAL} and er.entity_2_entity_uuid = a.animal_id and er.active = ${ACTIVE}
    LEFT JOIN main.customer as c on  c.customer_id = er.entity_1_entity_uuid and c.active = ${ACTIVE}
    LEFT JOIN main.customer_classification as cv on cv.classifier_id = ${CONST_ID_CLASSIFIER_VILLAGE} and cv.customer_id = c.customer_id and cv.active =${ACTIVE}
    LEFT JOIN main.ref_village as vg on vg.village_id = cv.value_reference_id
    LEFT JOIN main.entity_relationship as paravet_er on paravet_er.entity_relationship_type_id = ${PPU_PARAVET_TO_TASK} and paravet_er.entity_2_entity_uuid = cc.care_calendar_id
    LEFT JOIN main.staff as paravet_staff on paravet_staff.staff_id = paravet_er.entity_1_entity_uuid and paravet_staff.active = ${ACTIVE}
    LEFT JOIN main.entity_relationship as vet_er on vet_er.entity_relationship_type_id=${CENTRALIZED_VET_TO_TASK} and vet_er.entity_2_entity_uuid = cc.care_calendar_id
    LEFT JOIN main.staff vet_staff on vet_staff.staff_id = vet_er.entity_1_entity_uuid and vet_staff.active = ${ACTIVE}
    LEFT JOIN main.document vet_signature on vet_signature.entity_1_entity_uuid = vet_staff.staff_id and vet_signature.document_type_id = ${VET_SIGNATURE}
    LEFT JOIN (
      SELECT entity_1_entity_uuid,max(created_at) as created_at
      FROM main.document
      WHERE document_type_id=${DOCUMENT_CUSTOMER_THUMBNAIL_TYPE}
      GROUP BY entity_1_entity_uuid
    ) as fmdoc on fmdoc.entity_1_entity_uuid = c.customer_id
    LEFT JOIN main.document as fd on fd.created_at = fmdoc.created_at and fd.entity_1_entity_uuid = c.customer_id and fd.entity_1_entity_uuid = fmdoc.entity_1_entity_uuid
    LEFT JOIN (
      SELECT entity_1_entity_uuid,max(created_at) as created_at
      FROM main.document
      WHERE document_type_id=${DOCUMENT_STAFF_THUMBNAIL_TYPE}
      GROUP BY entity_1_entity_uuid
    ) as pmdoc on pmdoc.entity_1_entity_uuid = paravet_staff.staff_id
    LEFT JOIN main.document as pd on pd.entity_1_entity_uuid = pmdoc.entity_1_entity_uuid and pd.entity_1_entity_uuid = paravet_staff.staff_id and pd.created_at = pmdoc.created_at
    LEFT JOIN (
      SELECT entity_1_entity_uuid,max(created_at) as created_at
      FROM main.document
      WHERE document_type_id=${DOCUMENT_STAFF_THUMBNAIL_TYPE}
      GROUP BY entity_1_entity_uuid
    ) as pmdoc2 on pmdoc2.entity_1_entity_uuid = paravet_staff.staff_id
    LEFT JOIN main.document as pd2 on pd2.entity_1_entity_uuid = pmdoc2.entity_1_entity_uuid and pd2.entity_1_entity_uuid =  paravet_staff.staff_id and pd2.created_at = pmdoc2.created_at
    LEFT JOIN (
      SELECT entity_1_entity_uuid,max(created_at) as created_at
      FROM main.document
      WHERE document_type_id=${ANIMAL_THUMBNAIL_PHOTO}
      GROUP BY entity_1_entity_uuid
    ) as amdoc on amdoc.entity_1_entity_uuid  = a.animal_id
    LEFT JOIN main.document as ad on ad.entity_1_entity_uuid = amdoc.entity_1_entity_uuid and ad.created_at = amdoc.created_at and ad.entity_1_entity_uuid = a.animal_id
    WHERE cc.care_calendar_id = :care_calender_id
    `
  const queryBuilder = await dbConnections().main.manager.createQueryBuilder()
    .from(`(${query})`).setParameters({ care_calender_id });
  const result = queryBuilder.execute();
  return result;
}

module.exports = {
  getActivityByIdV2
}