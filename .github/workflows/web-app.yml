name: mobile-app CI 

on:
  push:
    branches: [release/requirement-19]
  pull_request:
    branches: [release/requirement-19]

jobs:
  build:
    runs-on: self-hosted

    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}
      - run: 
          npm i --legacy-peer-deps
      - run: cp /home/<USER>/etc/environment/mobile.env .env
      - run:
          npm run build_dev
      - run:
          pm2 stop web-app 
      - run:
          pm2 start web-app 
      - run:
          pm2 save
      - run:
          sudo service nginx restart
