# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Node.js CI

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
    - uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - run: npm install --force
    - run: npm run build_dev
    - name: ssh deploy on aws ec2
      uses: actions/checkout@v3
      env:
        SSH_PRIVATE_KEY: ${{ secrets.DEPLOY_KEY }}
        REMOTE_HOST: ${{ secrets.DEPLOY_HOST }}
        REMOTE_USER: ${{ secrets.DEPLOY_USER }}
        REMOTE_PORT: ${{ secrets.DEPLOY_PORT }}
        SOURCE: "build/"
        TARGET: "/home/<USER>/"

    - run: npm run server