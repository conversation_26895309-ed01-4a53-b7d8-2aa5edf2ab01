const { extractBasedOnLanguage } = require("@krushal-it/common-core")
const { defaultTable } = require("./service-document")
const kurshal_image = require("../utils/image")

function formattedTimeStamp(timestamp) {
  const date = new Date(timestamp)
  const options = {
    day: "2-digit",
    month: "short",
    year: "numeric",
    hour: "numeric",
    minute: "numeric",
    hour12: true,
    timeZone: "UTC"
  }

  const formattedDate = date.toLocaleString("en-GB", options).replace(/(\d+)(st|nd|rd|th),/, "$1$2")
  return formattedDate
}

const convertGMTDateToISTDate = date => {
  let istOffset = 5.5
  const givenDate = new Date(date)
  const gmtDate = new Date(givenDate.getTime() + istOffset * 60 * 60 * 1000)

  return gmtDate
}

/**
 * @param {{ payment_detail_break_up: any; activity_id: any; }} activityDetails
 * @param {any} userLanguage
 * @param {any} imageUrl
 * @param {any} KrushalLogo
 */
function getPrescriptionHTML(activityDetails, userLanguage, imageUrl, total_savings = "") {
  const { payment_detail_break_up, activity_id } = activityDetails

  const noticeHtmlTable = defaultTable(payment_detail_break_up)

  return baseHTML({ activityDetails, userLanguage, imageUrl, total_savings, noticeHtmlTable })
}

function baseHTML({ activityDetails, userLanguage, imageUrl, total_savings, noticeHtmlTable }) {
  return `<!DOCTYPE html>
  <html xml:lang="en" lang="en">
  
  <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
      <title>Prescription</title>
      <style type="text/css">
          body{
            font-family: "Noto Sans", sans-serif;
          }
          @page {
            size: A4;
            margin: 0;
            margin-top: 20px;
            margin-bottom: 20px;
          }

          @page :first {
          
              margin-top: 0%;
          }
  
          .prescription .observation {
              page-break-inside: avoid
          }
  
          .prescription tr {
              page-break-inside: avoid;
              page-break-after: auto
          }
          .animal_table {
            width: 100%;
            margin-top: 50px;
            border: 1px solid black;
        }

        .animal_table>table {
            border-collapse: collapse;
            width: calc(100%);
            margin: 0 auto;
        }

        .animal_table>table>tbody>tr>td {
            width: calc(100%/3);
            padding-left: 15px;
        }

        .stamp {
            width: 200px;
            height: 200px;
        }

      </style>
  </head>
  
  <body>
      <div class="prescription" style="margin-right:0pt;margin-left:0pt">
          
      <header style='display:flex; justify-content:center; align-items:center; height:fit-content; margin-top:10px;' ><img src="data:image/png;base64,${kurshal_image[userLanguage]}" height="40px" /> </header>
          
          <table style="border-collapse: collapse; width:100%; margin:5px 0 20px 0!important">
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                      ${extractBasedOnLanguage(language.templateLabels.CustomerComplaintID, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.ticket_number ? activityDetails.ticket_number : ""}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(language.templateLabels.Date, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.activity_date && activityDetails.activity_date.length > 0 ? formattedTimeStamp(convertGMTDateToISTDate(activityDetails.activity_date)) : ""}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(language.templateLabels.TimeOfComplaint, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      16:19
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(language.templateLabels.FarmerName, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.customer_name_l10n ? (extractBasedOnLanguage(activityDetails.customer_name_l10n, userLanguage) ? extractBasedOnLanguage(activityDetails.customer_name_l10n, userLanguage) : "") : ""}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(language.templateLabels.FarmerPhoneNo, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.farmer_contact ? activityDetails.farmer_contact : ""}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(language.templateLabels.FarmerID, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.customer_visual_id ? activityDetails.customer_visual_id : ""}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(language.templateLabels.AnimalName, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.animal_name ? activityDetails.animal_name : ""}
                  </td>
              </tr>
  
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(language.templateLabels.AnimalTagNo, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.ear_tag ? activityDetails.ear_tag : ""}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(language.templateLabels.AnimalReferenceNumber, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.animal_visual_id ? activityDetails.animal_visual_id : ""}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(language.templateLabels.village, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.village_name_l10n ? (extractBasedOnLanguage(activityDetails.village_name_l10n, userLanguage) ? extractBasedOnLanguage(activityDetails.village_name_l10n, userLanguage) : "") : ""}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(language.templateLabels.DrOrLSS, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.paravet_name ? (extractBasedOnLanguage(activityDetails.paravet_name, userLanguage) ? extractBasedOnLanguage(activityDetails.paravet_name, userLanguage) : "") : ""}
                  </td>
              </tr>
             
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                  ${extractBasedOnLanguage(language.templateLabels.VeterinaryOfficer, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.vet_name ? (extractBasedOnLanguage(activityDetails.vet_name, userLanguage) ? extractBasedOnLanguage(activityDetails.vet_name, userLanguage) : "") : ""}
                  </td>
              </tr>
              
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                  ${extractBasedOnLanguage(language.templateLabels.ActivityOrComplaint, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.activity_name_l10n ? (extractBasedOnLanguage(activityDetails.activity_name_l10n, userLanguage) ? extractBasedOnLanguage(activityDetails.activity_name_l10n, userLanguage) : "") : ""}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(language.templateLabels.FollowUpScheduledOn, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.followUpDateTime ? formattedTimeStamp(convertGMTDateToISTDate(activityDetails.followUpDateTime)) : ""}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(language.templateLabels.ResponseToTreatment, userLanguage)}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${activityDetails.response_to_treatment && activityDetails.response_to_treatment.length > 0 ? activityDetails.response_to_treatment[0].rtt_name : ""}
                  </td>
              </tr>
  
          </table>



          <table style="border-collapse: collapse; width:100%; margin:20px 0 20px 0;">
            <tr>
                <th style='border: thin solid black;text-align:center;padding:5px; padding-left:10px; font-size:17px;background-color:#512DA8;color:white'>${extractBasedOnLanguage(language.templateLabels.CaseDescription, userLanguage)}</th>
            </tr>
                 ${
                   activityDetails.observationNotes && activityDetails.observationNotes.length > 0
                     ? `

                 ${activityDetails.observationNotes
                   .map((data, index) => {
                     return `<tr style="">
                     <td  style='border: thin solid black;padding:5px; padding-left:10px;'>

                         ${data.note ? (extractBasedOnLanguage(data.note, userLanguage) ? extractBasedOnLanguage(data.note, userLanguage) : "") : ""}

                     </td>
                 </tr>`
                   })
                   .join("")}
                 `
                     : `<tr>
                          <td  style='border: thin solid black;padding:5px; padding-left:10px;'>  &#20;  </td>
                        </tr>`
                 }
          </table>

  
  
          <table style="border-collapse: collapse; width:100%; margin:20px 0 20px 0;">
              <tr>
                <th colspan='3' style='border: thin solid black;text-align:center;padding:5px; padding-left:10px; font-size:17px;background-color:#512DA8;color:white'> ${extractBasedOnLanguage(language.templateLabels.Observation, userLanguage)} </th>
              </tr>
              <tr>
                  <th style='border: thin solid black;  text-align:left;padding:5px; padding-left:10px;width:10%;background-color:#FF5722; color:white;' >  ${extractBasedOnLanguage(language.templateLabels.SerialNumber, userLanguage)}  </th>
                  <th style='border: thin solid black; width:50%; text-align:left;padding:5px; padding-left:10px;background-color:#FF5722; color:white;' >  ${extractBasedOnLanguage(language.templateLabels.Name, userLanguage)}    </th>
                  <th style='border: thin solid black; text-align:left;padding:5px; padding-left:10px;background-color:#FF5722; color:white;' > ${extractBasedOnLanguage(language.templateLabels.Value, userLanguage)}</th>
              </tr>
              ${
                activityDetails.observation && activityDetails.observation.length > 0
                  ? activityDetails.observation
                      .map((data, index) => {
                        return `<tr>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'> ${index + 1} </td>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'> ${data.obs_name ? data.obs_name : ""} </td>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'> ${data.obs_val ? data.obs_val : ""} </td>
              </tr>`
                      })
                      .join(" ")
                  : `<tr>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>&#20;</td>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>&#20;</td>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>&#20;</td>
                  </tr>`
              }
          </table>
  



  
          <table style="border-collapse: collapse; width:100%; margin:20px 0 20px 0;">
              <tr>
                <th colspan='2'style='border: thin solid black;text-align:center;padding:5px; padding-left:10px; font-size:17px;background-color:#512DA8;color:white'> ${extractBasedOnLanguage(language.templateLabels.Diagnosis, userLanguage)} </th>
              </tr>
              <tr>
                <th style='border: thin solid black;text-align:center;padding:5px; padding-left:10px; width:10%;background-color:#FF5722; color:white;'>${extractBasedOnLanguage(language.templateLabels.SerialNumber, userLanguage)}  </th>
                <th style='border: thin solid black;text-align:center;padding:5px; padding-left:10px;background-color:#FF5722; color:white;'>${extractBasedOnLanguage(language.templateLabels.Name, userLanguage)} </th>
              </tr>
              ${
                activityDetails.diagnosis && activityDetails.diagnosis.length > 0
                  ? activityDetails.diagnosis
                      .map((data, index) => {
                        return `
              <tr>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>  ${index + 1}                            </td>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>  ${data.diag_name ? data.diag_name : ""} </td>
              </tr>
              `
                      })
                      .join(" ")
                  : `<tr>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'> &#20; </td>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'> &#20; </td>
                  </tr>`
              }
  
          </table>




          <table style="border-collapse: collapse; width:100%; margin:20px 0 20px 0;">
              <tr>
                <th colspan='7'style='border: thin solid black;text-align:center;padding:5px; padding-left:10px; font-size:17px; background-color:#512DA8;color:white'>${extractBasedOnLanguage(language.templateLabels.Prescription, userLanguage)} </th>
              </tr>
              <tr>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px; padding-left:10px;background-color:#FF5722; color:white;width:10%'>${extractBasedOnLanguage(language.templateLabels.SerialNumber, userLanguage)} </th>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px; padding-left:10px;background-color:#FF5722; color:white;width:30%'>${extractBasedOnLanguage(language.templateLabels.MedicineName, userLanguage)}</th>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px;padding-left:10px;background-color:#FF5722; color:white;'>${extractBasedOnLanguage(language.templateLabels.Dose, userLanguage)}</th>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px;padding-left:10px;background-color:#FF5722; color:white;'>${extractBasedOnLanguage(language.templateLabels.Unit, userLanguage)}</th>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px;padding-left:10px;background-color:#FF5722; color:white;'>${extractBasedOnLanguage(language.templateLabels.Frequnecy, userLanguage)}</th>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px; padding-left:10px;width:20%;background-color:#FF5722; color:white;'> ${extractBasedOnLanguage(language.templateLabels.Route, userLanguage)} </th>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px;padding-left:10px;background-color:#FF5722; color:white;'>${extractBasedOnLanguage(language.templateLabels.Price, userLanguage)}</th>
              </tr>
              ${
                activityDetails.prescription && activityDetails.prescription.length > 0
                  ? activityDetails.prescription
                      .map((data, index) => {
                        return `
                    <tr>
                      <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>${index + 1}</td>
                      <td style='font-size:13px;text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>${data.prescription_name ? data.prescription_name : ""}</td>
                      <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>${data.prescription_value ? parseFloat(data.prescription_value).toFixed(2) : "-"}</td>
                      <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>${data.prescription_details ? (data.prescription_details.unit ? data.prescription_details.unit : "-") : "-"}</td>
                      <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>${data.prescription_details ? (data.prescription_details.frequency ? data.prescription_details.frequency : "-") : "-"}</td>
                      <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>${data.prescription_details ? (data.prescription_details.route_name ? data.prescription_details.route_name : "-") : "-"}</td>
                      <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>&#x20B9;${data.prescription_details ? (data.prescription_details.mrp ? parseFloat(data.prescription_details.mrp).toFixed(2) : "0") : "0"}</td>
                    </tr>
                  `
                      })
                      .join("")
                  : ""
              }

             <tr>
                <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;background-color:white' colspan="5">${extractBasedOnLanguage(language.templateLabels.VetConsultationFee, userLanguage)}</td>
                <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;' colspan="2">&#x20B9;${activityDetails?.vet_consultant_cost === undefined || activityDetails?.vet_consultant_cost === null || activityDetails?.vet_consultant_cost === "" ? 0 : activityDetails?.vet_consultant_cost}</td>
            </tr>
            <tr>
              <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;' colspan="5">${extractBasedOnLanguage(language.templateLabels.TotalPrice, userLanguage)}</td>
              <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;' colspan="2">&#x20B9;${activityDetails.total_price}</td>
            </tr>

          </table>



          <table style="border-collapse: collapse; width:100%; break-inside:avoid; margin:20px 0 20px 0;">
            <tr>
              <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px; padding-left:10px;background-color:#512DA8;color:white; color:white'>${extractBasedOnLanguage(language.templateLabels.VetAdvisoryNotes, userLanguage)}</th>
            </tr>
            ${
              activityDetails.prescriptionNotes && activityDetails.prescriptionNotes.length > 0
                ? activityDetails.prescriptionNotes.map((data, index) => {
                    return `<tr><td style='border: thin solid black;padding:5px; padding-left:10px;'>${data.note ? (extractBasedOnLanguage(data.note, userLanguage) ? extractBasedOnLanguage(data.note, userLanguage) : "") : ""}</td></tr>`
                  })
                : `<tr><td style='border: thin solid black;padding:5px; padding-left:10px;'>&#20;</td></tr>`
            }
          </table>

    <div class="animal_table">
        <table>
            <thead>
                <tr>
                    <td style="font-size: 17px;text-transform: capitalize; text-align:center;background-color: #512DA8;color: white;">
                        important notice
                    </td>
                </tr>
            </thead>
            <tbody>
                ${noticeHtmlTable}
            </tbody>
        </table>
    </div>

    <div  style="display: block;break-inside: avoid;margin-bottom:5pt;margin-top: 5pt;margin-right:10pt; display: flex; justify-content: flex-end; align-items: center;">
      ${imageUrl ? `<img src="data:image/png;base64,${imageUrl}" alt="Signature picture" width="150" height="150">` : ""}
    </div>

  </div>
  ${total_savings}
  </body>
  
</html>
  `
}

const admin_stamp = `<div class="stamp">
      <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
        <circle
          cx="100"
          cy="100"
          r="75"
          fill="none"
          stroke="red"
          stroke-width="2"
        />
        <path
          id="textPath"
          d="M 100,100 m -65,0 a 65,65 0 1,1 130,0 a 65,65 0 1,1 -130,0"
          fill="none"
          stroke="none"
        />
        <text
          fill="red"
          font-size="10"
          font-weight="bold"
          letter-spacing="2"
          transform="rotate(30, 100, 100)"
        >
          <textPath href="#textPath" startOffset="20%" text-anchor="middle">
            Administration
          </textPath>
        </text>
      </svg>
    </div>`

var language = {
  templateLabels: {
    CustomerComplaintID: {
      en: "Customer Complaint ID",
      mr: "ग्राहक तक्रार आयडी",
      tn: "வாடிக்கையாளர் புகார் அடையாளம்",
      hi: "ग्राहक शिकायत आईडी"
    },
    Date: {
      en: "Date",
      mr: "तारीख",
      tn: "தேதி",
      hi: "तारीख"
    },
    TimeOfComplaint: {
      en: "Time of Complaint",
      mr: "तक्रारीची वेळ",
      tn: "புகாரின் நேரம்",
      hi: "शिकायत का समय"
    },
    FarmerName: {
      en: "Farmer Name",
      mr: "शेतकऱ्याचे नाव",
      tn: "விவசாயி பெயர்",
      hi: "किसान का नाम"
    },
    FarmerPhoneNo: {
      en: "Farmer Phone No",
      mr: "शेतकऱ्याचा फोन नंबर",
      tn: "விவசாயி தொலைபேசி எண்",
      hi: "किसान फ़ोन नं"
    },
    FarmerID: {
      en: "Farmer ID",
      mr: "शेतकरी आयडी",
      tn: "விவசாயி அடையாளம்",
      hi: "किसान आईडी"
    },
    AnimalName: {
      en: "Animal Name",
      mr: "प्राण्याचे नाव",
      tn: "விலங்கு பெயர்",
      hi: "पशु का नाम"
    },
    AnimalTagNo: {
      en: "Animal Tag No",
      mr: "प्राणी टॅग क्रमांक",
      tn: "விலங்கு குறி எண்",
      hi: "पशु टैग नं"
    },
    AnimalReferenceNumber: {
      en: "Animal Reference Number",
      mr: "प्राणी संदर्भ क्रमांक",
      tn: "விலங்கு குறிப்பு எண்",
      hi: "पशु संदर्भ संख्या"
    },
    village: {
      en: "Village",
      mr: "गाव",
      tn: "கிராமம்",
      hi: "गाँव"
    },
    DrOrLSS: {
      en: "Dr./LSS",
      mr: "सहाय्यक पशुवैद्य",
      tn: "துணை கால்நடை மருத்துவர்",
      hi: "डॉ./एलएसएस"
    },
    VeterinaryOfficer: {
      en: "Veterinary Officer",
      mr: "पशुवैद्यकीय अधिकारी",
      tn: "கால்நடை மருத்துவர்",
      hi: "पशु चिकित्सा अधिकारी"
    },
    ActivityOrComplaint: {
      en: "Activity / Complaint",
      mr: "क्रियाकलाप / तक्रार",
      tn: "நடவடிக்கை / புகார்",
      hi: "गतिविधि/शिकायत"
    },
    FollowUpScheduledOn: {
      en: "Follow up Scheduled On",
      mr: "अनुसूचित पाठपुरावा",
      tn: "பின்தொடர்தல் திட்டமிடப்பட்ட தேதி",
      hi: "शेड्यूल ऑन का पालन करें"
    },
    ResponseToTreatment: {
      en: "Response To Treatment",
      mr: "उपचारांना प्रतिसाद",
      tn: "சிகிச்சைக்கான பதில்",
      hi: "उपचार के प्रति प्रतिक्रिया"
    },
    CaseDescription: {
      en: "Case Description",
      mr: "केस वर्णन",
      tn: "நோய் விளக்கம்",
      hi: "केस विवरण"
    },
    Observation: {
      en: "Observation",
      mr: "निरीक्षण",
      tn: "கவனிப்பு",
      hi: "अवलोकन"
    },
    SerialNumber: {
      en: "S.No",
      mr: "अनुक्रमांक",
      tn: "வரிசை எண்",
      hi: "क्र.सं."
    },
    Name: {
      en: "Name",
      mr: "नाव",
      tn: "பெயர்",
      hi: "नाम"
    },
    Value: {
      en: "Value",
      mr: "मूल्य",
      tn: "மதிப்பு",
      hi: "कीमत"
    },
    Diagnosis: {
      en: "DIAGNOSIS",
      mr: "निदान",
      tn: "கண்டறியப்பட்ட நோய்கள்",
      hi: "निदान"
    },
    Prescription: {
      en: "PRESCRIPTION",
      mr: "प्रिस्क्रिप्शन",
      tn: "மருந்துச்சீட்டு",
      hi: "नुस्खा"
    },
    MedicineName: {
      en: "Medicine Name",
      mr: "औषधाचे नाव",
      tn: "மருந்தின் பெயர்",
      hi: "दवा का नाम"
    },
    Dose: {
      en: "Dose",
      mr: "डोस",
      tn: "அளவு",
      hi: "खुराक"
    },
    Unit: {
      en: "Unit",
      mr: "युनिट",
      tn: "அலகு",
      hi: "इकाई"
    },
    Price: {
      en: "Price",
      mr: "किंमत",
      tn: "விலை",
      hi: "कीमत"
    },
    Frequnecy: {
      en: "Frequnecy",
      mr: "वारंवारता",
      tn: "ஒரு நாளுக்கான அளவு",
      hi: "आवृत्ति"
    },
    Route: {
      en: "Route",
      mr: "मार्ग",
      tn: "மருந்து செலுத்தும் முறை",
      hi: "मार्ग"
    },
    VetConsultationFee: {
      en: "Vet Consultation",
      mr: "पशुवैद्यकीय सल्लामसलत शुल्क",
      tn: "கால்நடை ஆலோசனை கட்டணம்",
      hi: "पशुचिकित्सक परामर्श"
    },
    TotalPrice: {
      en: "Total Price",
      mr: "एकूण किंमत",
      tn: "மொத்த விலை",
      hi: "कुल कीमत"
    },
    VetAdvisoryNotes: {
      en: "Vet Advisory Notes",
      mr: "पशुवैद्य सल्लागार नोट्स",
      tn: "கால்நடை ஆலோசனை குறிப்புகள்",
      hi: "पशुचिकित्सक सलाहकार नोट्स"
    }
  }
}

module.exports = {
  getPrescriptionHTML,
  convertGMTDateToISTDate,
  formattedTimeStamp,
  baseHTML
}
