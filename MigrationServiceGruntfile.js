module.exports = function(grunt) {
  grunt.initConfig({
    terser: {
      target: {
        files: [{
          expand: true,
          src: ['**/*.js', '!db/migrations/**', '!node_modules/**', '!*Gruntfile.js', '!package-build/**', '!migration-service-build/**'],
          dest: 'migration-service-build'
        }]
      }
    },
    copy: {
      files: {
        src: ['package.json', '!**/*.js', 'db/migrations/**', '!node_modules/**', 'Makefile', '.npm*', 'Dockerfile*', '.gitignore*', '!*Gruntfile.js', '!package-build/**', '!migration-service-build/**'],
        dest: 'migration-service-build',    // destination folder
        expand: true      // required when using cwd
      }
    }
  });
  grunt.loadNpmTasks('grunt-terser');
  grunt.loadNpmTasks('grunt-contrib-copy');
  grunt.registerTask('default', ['copy', 'terser']);
}