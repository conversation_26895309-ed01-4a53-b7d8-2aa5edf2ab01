const { LibAnimalService, LibUpdateAnimalActiveStatus, LibUpsertAnimalService, LibAnimalListFarmTaskService, LibAnimalHealthRecord, LibAnimalMilking } = require('./services/animal/animal.class')
const { LibCommonServices, References2Lib, FilterService, ReferencesLib, loadMeds ,References3Lib} = require('./services/common/common.class')
const { generateVisualId } = require('./utils/customer')
const { LibHealthScore } = require('./services/healthScore/healthScore.class')
const { LibAnimalFeeds } = require('./services/animalFeeds/animalFeeds.class')
const { LibCustomerService, LibCustomerListing, LibUpsertCustomer, CustomerLib, CustomerListLib, FlaggedCustomers } = require('./services/customer/customer.class')

const { getCustomerList_v3, registerCustomer, getUpcomingFarmerTasks } = require('./services/customer/controller.js')

const { GetStaffService, GetStaffTypeIdsService } = require('./services/staff/get-staff.service')
const { DistrictListLib, TalukListLib, VillageListLib, GeoArraysLib } = require('./services/geo/geo.class')
const { LibLocationService } = require('./services/location/location.class')

const { LibDocumentService, DocumentListLib, DocumentLib } = require('./services/document/document.class')

const { addDocumentEntry, makeDocumentInactive, getDocumentsByEnityUUID } = require('./services/document/controller.js')

const { LibActivityService, LibTaskList, LibTask, LibCardService, LibChildCards, LibRoutePlanDetailService, LibRoutePlanService, LibLinkedTaskService, LibActivityObservationService, LibActivityDiagnosisService, LibActivityPrescriptionService, ActivityPreviewService, LibRouteDetailService } = require('./services/activity_routeplan/activity.class')
const { LibReferenceService } = require('./services/references/reference.class')
const { loadClassificationConfiguration, getClassificationConfiguration, loadClassificationData, loadClassificationDataWithClassifierIds, saveClassificationData, saveClassifications } = require('./services/common/classification.helper')

const { loadQueryTemplates, getTemplate, getCompiledQuery } = require('./queries/index')
const ActivityFormControl = require("./services/ActivityFormManagement")
const updateActivity_V2 = require("./services/activity_routeplan/updateActivityV2.js")
const {observationManager} = require('./services-v3/task/index.js')
const { LibRequisitionService} = require('./services/requisition/requisition.class')
const {LibInventoryLedgerService} = require('./services/inventory_ledger/inventory_ledger.class')
const { LibWarehouseService } = require('./services/warehouse/warehouse.class')
const { LibMedicineService } = require('./services/medicine/medicine.class')


const { getTasks, get_task_infromation_by_id } = require('./services-v2/tasks')
const { getAnimalsByFarmerID } = require('./services-v2/animalbyfarmerid.js')
const { getActivityByIdV2 } = require('./services-v2/getActivityByIdV2.js')
const { getParavetCard } = require('./services-v2/getParavetCard.js')
const { getRoutePlanV2 } = require('./services-v2/getRoutePlanV2.js')
const { getVetCard } = require('./services-v2/getVetCard.js')
const { ActivityPrescriptionUpdateServiceV2, ActivityObservationUpdateSeviceV2, ActivityDiagnosisUpdateServiceV2, activitySummaryV2 } = require('./services/activity_routeplan/activityV2.class.js')
const { ActivityAdditionalMedicineServiceV2 } = require('./services/activity_routeplan/additionalMeds.class.js')
const FreelanceStaffService = require('./services-v2/freelanceStaffService.js')
const CattleV2 = require('./services-v2/CattleV2.js')

const { LibTaskV2 } = require('./v2/services/activity/activity.class')
const { UsersLogin } = require('./v2/services/users/users.service')
const { AnimalServicesV2, AnimalCategoryV2, AnimalMilkingV2 } = require('./v2/services/animals/animal.service')
const { getParavetPreventiveDashboard } = require('./services-v2/preventiveDashboards.js')
const { getFarmerInfoByMobileNumber, checkFreelanceCustomerRelationAlreadyExist } = require('./services-v2/getFarmerInfoByMobileNumber.js')
const { LibAsset } = require('./services/asset/asset.class')
const { LibAssetFeature } = require('./services/asset_feature/asset_feature.class')
const { LibSmartFeed } = require('./services/animalFeeds/smartFeed.class.js')
const { getUniqueObjectKeyWithExt, getNanoId, getObjectInfo } = require("./utils/object-helpers/index.js")
const { verifyEntity } = require("./utils/object-helpers/filters/index.js")
const {getActivities} = require('./services-v2/activity-management/controller.js')

const { ObservationServiceV2 ,ObservationCategoryService } = require ('./services/observationV2/controller.js')
const { ObservationNotes } = require('./services/observationV2/observationNotes')
const { configureInventoryV2 } = require('./services/inventory/inventory.class.js')
const { getServiceDocumentData } = require('./services/serviceDocument/controller.js')
const {
  verifyOtp,
  getOtp
} = require('./utils/otp.js')

const observationServiceInstance = require('./services/task-observations/observationService.js')
const { CareCalendarQueries, CareCalendarActivityQueries } = require('./queries/care-calendar.js')
const { FarmManagement } = require("./services/ActivityFormManagement/farmManagement/farm_management_v2.js")
const { FarmManagementComplete } = require("./services/ActivityFormManagement/farmManagement/complete.js")
const referenceRepositoryInstance = require('./repositories/referenceRepository')
const animalRepositoryInstance = require('./repositories/animalRepository')
const customerRepositoryInstance = require('./repositories/customerRepository')
const { FarmManagementReport } = require("./services/ActivityFormManagement/farmManagement/report.js")
const { FarmManagementWeb } = require("./services/ActivityFormManagement/farmManagement/admin.js")
const { FarmManagementConfig } = require("./services/ActivityFormManagement/farmManagement/config.js")
module.exports = {
  ReferencesLib,
  References2Lib,
  References3Lib,
  loadClassificationConfiguration,
  getClassificationConfiguration,
  loadClassificationData,
  loadClassificationDataWithClassifierIds,
  saveClassificationData,
  DistrictListLib,
  TalukListLib,
  VillageListLib,
  GeoArraysLib,
  LibAnimalService,
  LibUpdateAnimalActiveStatus,
  LibUpsertAnimalService,
  LibCustomerService,
  LibCustomerListing,
  LibUpsertCustomer,
  CustomerListLib,
  CustomerLib,
  LibDocumentService,
  DocumentListLib,
  DocumentLib,
  LibCommonServices,
  LibLocationService,
  LibActivityService,
  LibTaskList,
  LibTask,
  LibTaskV2,
  LibCardService,
  LibChildCards,
  LibRoutePlanDetailService,
  LibLinkedTaskService,
  LibRoutePlanService,
  LibReferenceService,
  LibAnimalListFarmTaskService,
  loadQueryTemplates,
  getTemplate,
  getCompiledQuery,
  GetStaffService,
  generateVisualId,
  FilterService,
  GetStaffTypeIdsService,
  LibActivityObservationService,
  LibActivityDiagnosisService,
  LibActivityPrescriptionService,
  ActivityPreviewService,
  LibHealthScore,
  LibAnimalHealthRecord,
  LibAnimalMilking,
  LibAnimalFeeds,
  getCustomerList_v3,
  registerCustomer,
  getUpcomingFarmerTasks,
  addDocumentEntry,
  makeDocumentInactive,
  getDocumentsByEnityUUID,
  getTasks,
  get_task_infromation_by_id,
  getAnimalsByFarmerID,
  getActivityByIdV2,
  getParavetCard,
  getRoutePlanV2,
  getVetCard,
  ActivityPrescriptionUpdateServiceV2,
  FreelanceStaffService,
  CattleV2,
  getParavetPreventiveDashboard,
  getFarmerInfoByMobileNumber,
  checkFreelanceCustomerRelationAlreadyExist,
  loadMeds,
  ActivityObservationUpdateSeviceV2,
  ActivityDiagnosisUpdateServiceV2,
  ActivityFormControl,
  updateActivity_V2,
  activitySummaryV2,
  ActivityAdditionalMedicineServiceV2,
  observationManager,
  UsersLogin,
  AnimalServicesV2,
  AnimalCategoryV2,
  AnimalMilkingV2,
  LibAsset,
  LibAssetFeature,
  LibSmartFeed,
  getUniqueObjectKeyWithExt,
  getNanoId,
  verifyEntity,
  getObjectInfo,
  getActivities,
  LibRequisitionService,
  LibInventoryLedgerService,
  LibWarehouseService,
  LibMedicineService,
  saveClassifications,
  LibRouteDetailService,
  ObservationServiceV2,
  ObservationCategoryService,
  ObservationNotes,
  FlaggedCustomers,
  configureInventoryV2,
  verifyOtp,
  getOtp,
  getServiceDocumentData,
  observationServiceInstance,
  CareCalendarQueries,
  CareCalendarActivityQueries,
  FarmManagement,
  FarmManagementComplete,
  referenceRepositoryInstance,
  animalRepositoryInstance,
  customerRepositoryInstance,
  FarmManagementReport,
  FarmManagementWeb,
  FarmManagementConfig
}
