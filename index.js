// const dotEnv = require('dotenv')
// dotEnv.config()

/*const dbModels = require('./db/models')
const postGresDB = require('./server.config')
const sqliteDB = require('./mobile.config')

postGresDB.entities = [...dbModels.main.models] */
/*sqliteDB.entities = [ new EntitySchema(student) 
] */

// console.log('process.env = ', process.env)

/*
  "host": "localhost", 
  "port": 5432, 
  "username": "krushallocal", 
  "password": "krushallocal", 
  "database": "krushallocal",
*/
// const { loadConfiguration, configurationJSON } = require('@krushal-it/common-core')
// loadConfiguration(process.env.ENV_JSON)
const {
  dbConnections,
  initializeAllRepositories,
  getDBConnections,
} = require("./utils/connectionHelper");
const {
  regenerateUpdatedAtTriggers,
} = require("./utils/regenerateUpdatedAtTriggers");
const { optionallyAddPrimaryKeyToRecords, preProcessRecords, postProcessRecords, postProcessSelectQueryResults } = require("./utils/ormHelper");
module.exports = {
  dbConnections,
  initializeAllRepositories,
  getDBConnections,
  regenerateUpdatedAtTriggers,
  optionallyAddPrimaryKeyToRecords,
  preProcessRecords,
  postProcessRecords,
  postProcessSelectQueryResults
};
