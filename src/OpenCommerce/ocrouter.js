const { /* get,  */put, /* post,  */configurationJSON, KrushalError } = require('@krushal-it/common-core')
const { post, get } = require('../routerhelper')
const getSellableAnimalsReportOld = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gSAR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/sellable-animals-report-old`
    console.log('r gSAR  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSAR 10, error')
    console.log('r gSAR 10a, error = ', error)
    throw error
  }
}

const getFarmersWithNoLocationRecordedReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gFWNLRR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-with-no-location-recorded`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/customer-list`
    console.log('r gFWNLRR 2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWNLRR 10, error')
    console.log('r gFWNLRR 10a, error = ', error)
    throw error
  }
}

const getFarmersWithNoLocationRecordedReportFilterData = async (headers, configuration) => {
  try {
    console.log('r gFWLRRFD 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-with-no-location-recorded`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/customer-list-filter`
    console.log('r gFWLRRFD  2, url = ', url)
    const response = await post(url, headers, configuration)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRRFD 10, error')
    console.log('r gFWLRRFD 10a, error = ', error)
    throw error
  }
}

const getFarmersWithLocationRecordedReport = async (headers, configuration) => {
  try {
    console.log('r gFWLRR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-with-location-recorded`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/customer-list`
    console.log('r gFWLRR  2, url = ', url)
    const response = await post(url, headers, configuration)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRR 10, error')
    console.log('r gFWLRR 10a, error = ', error)
    throw error
  }
}

const getFarmersWithLocationRecordedReportFilterData = async (headers) => {
  try {
    console.log('r gFWLRRFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-with-location-recorded`
    console.log('r gFWLRRFD  2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRRFD 10, error')
    console.log('r gFWLRRFD 10a, error = ', error)
    throw error
  }
}

const getDataForPotentiallySellableAnimalsReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gPSAWLR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/data-for-potentially-sellable-animals`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/animal-list`
    console.log('r gPSAWLR  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gPSAWLR 10, error')
    console.log('r gPSAWLR 10a, error = ', error)
    throw error
  }
}

const getDataForPotentiallySellableAnimalsReportFilterData = async (headers) => {
  try {
    console.log('r gFWLRRFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/data-for-potentially-sellable-animals`
    console.log('r gFWLRRFD  2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRRFD 10, error')
    console.log('r gFWLRRFD 10a, error = ', error)
    throw error
  }
}

const getPotentiallySellableAnimalsReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gPSAWLR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/potentially-sellable-animals`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/animal-list`
    console.log('r gPSAWLR  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gPSAWLR 10, error')
    console.log('r gPSAWLR 10a, error = ', error)
    throw error
  }
}

const getPotentiallySellableAnimalsReportFilterData = async (headers) => {
  try {
    console.log('r gFWLRRFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/potentially-sellable-animals`
    console.log('r gFWLRRFD  2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRRFD 10, error')
    console.log('r gFWLRRFD 10a, error = ', error)
    throw error
  }
}

const snoozeForSellableAnimals = async (headers, animalWithSnoozeInformation) => {
  try {
    console.log('r sFSA 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/snooze-for-sellable-animals`
    console.log('r sFSA  2, url = ', url)
    const response = await post(url, headers, animalWithSnoozeInformation)
    return response
  } catch (error) {
    console.log('r sFSA 10, error')
    console.log('r sFSA 10a, error = ', error)
    throw error
  }
}

const unsnoozeSellableAnimals = async (headers, toBeSnoozeAnimalInformation) => {
  try {
    console.log('r sFSA 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/unsnooze-sellable-animals`
    console.log('r sFSA  2, url = ', url)
    const response = await post(url, headers, toBeSnoozeAnimalInformation)
    return response
  } catch (error) {
    console.log('r sFSA 10, error')
    console.log('r sFSA 10a, error = ', error)
    throw error
  }
}

const updateSellableAnimalStatus = async (headers, animalUpdateStatusData) => {
  try {
    console.log('r uSAS 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/update-animal-selling-status`
    console.log('r uSAS  2, url = ', url)
    const response = await post(url, headers, animalUpdateStatusData)
    return response
  } catch (error) {
    console.log('r uSAS 10, error')
    console.log('r uSAS 10a, error = ', error)
    throw error
  }
}

const markAnimalsAsSellable = async (headers, animalsData) => {
  try {
    console.log('r mAAS 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/mark-animals-as-sellable`
    console.log('r mAAS  2, url = ', url)
    const response = await post(url, headers, animalsData)
    return response
  } catch (error) {
    console.log('r mAAS 10, error')
    console.log('r mAAS 10a, error = ', error)
    throw error
  }
}

const getSellableAnimalsReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gSAR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/sellable-animals`
    console.log('r gSAR  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSAR 10, error')
    console.log('r gSAR 10a, error = ', error)
    throw error
  }
}

const getSellableAnimalsReportFilterData = async (headers) => {
  try {
    console.log('r gSARFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/sellable-animals`
    console.log('r gSARFD  2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSARFD 10, error')
    console.log('r gSARFD 10a, error = ', error)
    throw error
  }
}

const getItemDetailByItemListingId = async (headers, itemListingInformation) => {
  try {
    console.log('r gIDBILI 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/item-detail-by-item-listing-id`
    console.log('r gIDBILI  2, url = ', url)
    const response = await post(url, headers, itemListingInformation)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gIDBILI 10, error')
    console.log('r gIDBILI 10a, error = ', error)
    throw error
  }
}

const getPotentialBuyerFarmersForAnimalReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gSAR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/potential-buyer-farmers-for-animal`
    console.log('r gSAR  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSAR 10, error')
    console.log('r gSAR 10a, error = ', error)
    throw error
  }
}

const getPotentialBuyerFarmersForAnimalReportFilterData = async (headers) => {
  try {
    console.log('r gSARFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/potential-buyer-farmers-for-animal`
    console.log('r gSARFD  2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSARFD 10, error')
    console.log('r gSARFD 10a, error = ', error)
    throw error
  }
}

const snoozeForBuyerFarmers = async (headers, buyerFarmerWithAnimalAndSnoozeInformation) => {
  try {
    console.log('r sFSA 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/snooze-for-buyer-farmers`
    console.log('r sFSA  2, url = ', url)
    const response = await post(url, headers, buyerFarmerWithAnimalAndSnoozeInformation)
    return response
  } catch (error) {
    console.log('r sFSA 10, error')
    console.log('r sFSA 10a, error = ', error)
    throw error
  }
}

const updateBuyerFarmerStatus = async (headers, farmerUpdateStatusData) => {
  try {
    console.log('r uSAS 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/update-buying-farmer-status`
    console.log('r uSAS  2, url = ', url)
    const response = await post(url, headers, farmerUpdateStatusData)
    return response
  } catch (error) {
    console.log('r uSAS 10, error')
    console.log('r uSAS 10a, error = ', error)
    throw error
  }
}

const getFarmersWhoMayWantToBuyAnimalsReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gFWMWTBAR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-who-may-want-to-buy-animals`
    console.log('r gFWMWTBAR 2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWMWTBAR 10, error')
    console.log('r gFWMWTBAR 10a, error = ', error)
    throw error
  }
}

const getFarmersWhoMayWantToBuyAnimalsReportFilterData = async (headers) => {
  try {
    console.log('r gFWMWTBARFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-who-may-want-to-buy-animals`
    console.log('r gFWMWTBARFD 2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWMWTBARFD 10, error')
    console.log('r gFWMWTBARFD 10a, error = ', error)
    throw error
  }
}

const createOrderForAnimalSale = async (headers, data) => {
  try {
    console.log('r cOFAS 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/create-order-for-animal-sale`
    console.log('r cOFAS 2, url = ', url)
    const response = await post(url, headers, data)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r cOFAS 10, error')
    console.log('r cOFAS 10a, error = ', error)
    throw error
  }
}

const getAnimalExchangeOrdersReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gSAR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/animal-exchange-orders`
    console.log('r gSAR  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSAR 10, error')
    console.log('r gSAR 10a, error = ', error)
    throw error
  }
}

const getAnimalExchangeOrdersReportFilterData = async (headers) => {
  try {
    console.log('r gSARFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/animal-exchange-orders`
    console.log('r gSARFD  2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSARFD 10, error')
    console.log('r gSARFD 10a, error = ', error)
    throw error
  }
}

const updateOrderFarmerStatus = async (headers, farmerUpdateStatusData) => {
  try {
    console.log('r uOFS 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/update-order-farmer-status`
    console.log('r uOFS  2, url = ', url)
    const response = await post(url, headers, farmerUpdateStatusData)
    return response
  } catch (error) {
    console.log('r uOFS 10, error')
    console.log('r uOFS 10a, error = ', error)
    throw error
  }
}

const getDocuments = async (headers, documentQueryInformation) => {
  try {
    console.log('r uOFS 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/get-documents`
    console.log('r uOFS  2, url = ', url)
    const response = await post(url, headers, documentQueryInformation)
    return response
  } catch (error) {
    console.log('r uOFS 10, error')
    console.log('r uOFS 10a, error = ', error)
    throw error
  }
}

const uploadFileToS3 = async (headers, formData) => {
  try {
    console.log('r uFTS3 1')
    const url =
      configurationJSON().SERVICE_END_POINTS.BACK_END +
      '/v2/file'
    const response = await post(url, headers, formData)
    console.log('r uFTS3 2, response = ', response)
    return response
  } catch (error) {
    console.log('r uFTS3 10, error')
    console.log('r uFTS3 10a, error = ', error)
    throw error
  }
}

const saveDocuments = async (headers, documents) => {
  try {
    console.log('r sD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/save-documents`
    console.log('r sD  2, url = ', url)
    const response = await post(url, headers, documents)
    return response
  } catch (error) {
    console.log('r sD 10, error')
    console.log('r sD 10a, error = ', error)
    throw error
  }
}

const getComments = async (headers, commentConfiguration) => {
  try {
    console.log('r gC 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/get-comments`
    console.log('r gC  2, url = ', url)
    const response = await post(url, headers, commentConfiguration)
    return response
  } catch (error) {
    console.log('r gC 10, error')
    console.log('r gC 10a, error = ', error)
    throw error
  }
}
const getFarmersReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gFWLRR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-with-location-recorded`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + '/customer-listingantd'
    console.log('r gFWLRR  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRR 10, error')
    console.log('r gFWLRR 10a, error = ', error)
    throw error
  }
}

// farmers who are not contacted and snooze is not there

const getManureOCFarmersReport = async (headers, customerFilterConfiguration) => {
  try {
    console.log('r gMOCFR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/manure-farmers`
    console.log('r gMOCFR 2, url = ', url)
    const response = await post(url, headers, customerFilterConfiguration)
    return response
  } catch (error) {
    console.log('r gMOCFR 10, error')
    console.log('r gMOCFR 10a, error = ', error)
    throw error
  }
}

const getManureOCFarmersReportReportFilterData = async (headers) => {
  try {
    console.log('r gMOCFRFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/manure-farmers`
    console.log('r gMOCFRFD  2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gMOCFRFD 10, error')
    console.log('r gMOCFRFD 10a, error = ', error)
    throw error
  }
}

// not interested, we snooze them for a year
const snoozeManureOCFarmers = async (headers, customerFilterConfiguration) => {
  try {
    console.log('r gMONCF 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/manure-snooze-farmers`
    console.log('r gC  2, url = ', url)
    const response = await post(url, headers, customerFilterConfiguration)
    return response
  } catch (error) {
    console.log('r gC 10, error')
    console.log('r gC 10a, error = ', error)
    throw error
  }
}

const getDocumentDetails = async (headers, documentId) => {
  try {
    console.log('r gDD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/document-information/${documentId}`
    console.log('r gDD 2, url = ', url)
    const response = await get(url, headers)
    return response
  } catch (error) {
    console.log('r gDD 10, error')
    console.log('r gDD 10a, error = ', error)
    throw error
  }
}

const getFarmersFilterData = async (headers) => {
  try {
    console.log('r gFWLRRFD 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-with-location-recorded`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + '/customer-listingantd'
    console.log('r gFWLRRFD  2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRRFD 10, error')
    console.log('r gFWLRRFD 10a, error = ', error)
    throw error
  }
}

// view farmer detailed information
const loadReferencesForReferenceCategories = async (headers, referenceCategoriesRelatedData) => {
  try {
    console.log('r lRFRC 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/load-references-for-reference-categories`
    console.log('r lRFRC  2, url = ', url)
    const response = await post(url, headers, referenceCategoriesRelatedData)
    return response
  } catch (error) {
    console.log('r lRFRC 10, error')
    console.log('r lRFRC 10a, error = ', error)
    throw error
  }
}

const calculateHealthScore = async (headers, animalData) => {
  try {
    console.log('r cHS 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/healthScore/recalculate`
    console.log('r cHS  2, url = ', url)
    const response = await post(url, headers, animalData)
    return response
  } catch (error) {
    console.log('r cHS 10, error')
    console.log('r cHS 10a, error = ', error)
    throw error
  }
}

const generatePriceForAnimal = async (headers, animalData) => {
  try {
    console.log('r gPFA 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/generate-cattle-exchange-price`
    console.log('r gPFA  2, url = ', url)
    const response = await post(url, headers, animalData)
    return response
  } catch (error) {
    console.log('r gPFA 10, error')
    console.log('r gPFA 10a, error = ', error)
    throw error
  }
}

export  {
  getSellableAnimalsReportOld,
  getFarmersWithNoLocationRecordedReport, getFarmersWithNoLocationRecordedReportFilterData,
  getFarmersWithLocationRecordedReport, getFarmersWithLocationRecordedReportFilterData,
  getDataForPotentiallySellableAnimalsReport, getDataForPotentiallySellableAnimalsReportFilterData,
  getPotentiallySellableAnimalsReport, getPotentiallySellableAnimalsReportFilterData,
  snoozeForSellableAnimals, unsnoozeSellableAnimals, updateSellableAnimalStatus, markAnimalsAsSellable,
  getSellableAnimalsReport, getSellableAnimalsReportFilterData,
  getItemDetailByItemListingId, getPotentialBuyerFarmersForAnimalReport, getPotentialBuyerFarmersForAnimalReportFilterData,
  snoozeForBuyerFarmers, updateBuyerFarmerStatus,
  getFarmersWhoMayWantToBuyAnimalsReport, getFarmersWhoMayWantToBuyAnimalsReportFilterData,
  createOrderForAnimalSale, getAnimalExchangeOrdersReport, getAnimalExchangeOrdersReportFilterData,
  updateOrderFarmerStatus, getDocuments, uploadFileToS3, saveDocuments,
  getComments,
  getManureOCFarmersReport, getManureOCFarmersReportReportFilterData, snoozeManureOCFarmers,
  getDocumentDetails, getFarmersReport, getFarmersFilterData,
  loadReferencesForReferenceCategories, calculateHealthScore, generatePriceForAnimal
}