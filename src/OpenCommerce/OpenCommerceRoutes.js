import {
  Routes,
  Route,
} from "react-router-dom";

import Sidebar from "../Components/Sidebar/Sidebar";


import OpenCommerce from "./Components/OpenCommerce";
import FarmersNoLocationRecorded  from "./Components/CattleExchange/FarmersNoLocationRecorded";
import FarmersLocationRecorded  from "./Components/CattleExchange/FarmersLocationRecorded";
import DataCollectionOfPotentiallySellableAnimals  from "./Components/CattleExchange/DataCollectionOfPotentiallySellableAnimals";
import PotentiallySellableAnimals  from "./Components/CattleExchange/PotentiallySellableAnimals";
import AnimalsThatFarmersMayWantToSell  from "./Components/CattleExchange/AnimalsThatFarmersMayWantToSell";
import SellableAnimals from "./Components/CattleExchange/SellableAnimals";
import PotentiallyBuyerFarmersForAnimal from "./Components/CattleExchange/PotentiallyBuyerFarmersForAnimal";
import FarmersWhoMayWantToBuy from "./Components/CattleExchange/FarmersWhoMayWantToBuy";
import AnimalExchangeOrders from "./Components/CattleExchange/AnimalExchangeOrders";
import ManureOCFarmersNotContacted from "./Components/Manure/ManureOCFarmersNotContacted";
import ManureOCFarmersNotInterested from "./Components/Manure/ManureOCFarmersNotInterested";
import ManureOCFarmersInterested from "./Components/Manure/ManureOCFarmersInterested";
// import OCFarmerDetails from "./Components/Entity/OCFarmerDetails";
// import OCAnimalDetails from "./Components/Entity/OCAnimalDetails";

const OpenCommerceRoutes = () => {
  return (
    <Routes>
      <Route path="oc" element={<Sidebar content={<OpenCommerce />} />} />
        <Route path="oc/farmer-no-location-recorded" element={<Sidebar content={<FarmersNoLocationRecorded />} />} />
        <Route path="oc/farmer-location-recorded" element={<Sidebar content={<FarmersLocationRecorded />} />} />
        <Route path="oc/data-collection-of-potentially-sellable-animals" element={<Sidebar content={<DataCollectionOfPotentiallySellableAnimals />} />} />
        <Route path="oc/animals-without-healthscore" element={<Sidebar content={<SellableAnimals />} />} />
        <Route path="oc/potentially-sellable-animals" element={<Sidebar content={<PotentiallySellableAnimals />} />} />
        <Route path="oc/animals-that-farmers-may-want-to-sell" element={<Sidebar content={<AnimalsThatFarmersMayWantToSell />} />} />
        <Route path="oc/sellable-animals" element={<Sidebar content={<SellableAnimals />} />} />
        <Route path="oc/potential-buyer-farmers-for-animal" element={<Sidebar content={<PotentiallyBuyerFarmersForAnimal />} />} />
        <Route path="oc/farmers-who-may-want-to-buy" element={<Sidebar content={<FarmersWhoMayWantToBuy />} />} />
        <Route path="oc/animal-exchange-orders" element={<Sidebar content={<AnimalExchangeOrders />} />} />
        <Route path="oc/manure-to-be-contacted-customers" element={<Sidebar content={<ManureOCFarmersNotContacted />} />} />
        <Route path="oc/manure-not-interested-customers" element={<Sidebar content={<ManureOCFarmersNotInterested />} />} />
        <Route path="oc/manure-interested-customers" element={<Sidebar content={<ManureOCFarmersInterested />} />} />
    </Routes>
  )
}

export default OpenCommerceRoutes