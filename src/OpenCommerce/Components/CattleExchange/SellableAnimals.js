import { Space, Button, Modal } from "antd";

import { useState } from "react"
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { useSelector } from "react-redux"

import {
  KrushalOCForm,
  assignDataToFormState,
} from "../../../Components/Common/KrushalOCForm";

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../../Components/user/authorize')

const { getSellableAnimalsReport, getSellableAnimalsReportFilterData } = require('../../../router')

const { createURLQueryStringFromPageParams, OCTable, createPageParamsFromURLString, MediaCarouselModalButton, CommentViewerModalButton } = require('../../../Components/Common/AntTableHelper')

const loadReport = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Sellable Animals Report',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getSellableAnimalsReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadFilters = async (userToken) => {
  try {
    const headers = {
      useCase: 'Get Sellable Animals Report Filter Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getSellableAnimalsReportFilterData(headers)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const tableConfiguration = {
  tableNavigationUrl: '../oc/sellable-animals',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
  },
  rowKey: 'oc_item_listing_uuid',
  columns: [
    {
      title: "Item Listing Id",
      key: "oc_item_listing_uuid",
    },
    {
      title: "Tracker Link",
      key: "animal_information_tracker_link"
    },
    {
      title: "Minimum Price",
      key: "minimum_price",
    },
    {
      title: "Maximum Price",
      key: "maximum_price",
    },
    {
      title: "Sellable Till",
      key: "sellable_till",
    },
    {
      title: "Farmer Name",
      key: "customer_name",
    },
    {
      title: "Farmer Mobile Number",
      key: "mobile_number",
    },
    {
      title: "Taluk",
      key: "taluk_name",
    },
    {
      title: "Village",
      key: "village_name",
    },
    {
      title: "With Krushal From",
      key: "with_krushal_from_date",
    },
    {
      title: "With Krushal For Months",
      dataIndex: "with_krushal_from_date",
      key: 'with_krushal_for_months'
      // dataIndex: "with_krushal_for_months",
    },
    {
      title: "Latest LPD",
      key: "latest_lpd",
    },
    {
      title: "Latest LPD Date",
      key: "latest_lpd_date",
    },
    {
      title: "Number of Calvings",
      key: "number_of_calvings",
    },
    {
      title: "Number of Months Pregnant",
      key: "number_of_months_pregnant",
    },
    {
      title: "Health Score",
      key: "health_score",
    },
    {
      title: "Body Score",
      key: "body_score",
    },
    {
      title: "Udder and Teat Score",
      key: "udder_and_teat_score",
    },
    {
      title: "Mastitis Score",
      key: "mastitis_score",
    },
    {
      title: "Preventive Health Score",
      key: "mastitis_score",
    },
  ],
  columnConfiguration: {
    animal_information_tracker_link: {
      headingText: 'Tracker Link',
      formatType: 'hyperLink',
    },
    sellable_till: {
      headingText: 'Sellable Till',
      enableFilter: true,
      type: 'dayjs_date_till',
      // defaultFilteredValue: [dayjs()],
      formatType: 'date',
      format: 'DD-MMM-YYYY'
    },
    customer_id: {
      headingText: 'Customer Id',
      allowFilterInPageParams: true,
      paramSearchType: 'array',
      paramSearchDataType: 'string'
    },
    animal_ear_tag: {
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    customer_name: {
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    mobile_number: {
      headingText: 'Mobile Number',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    taluk_name: {
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      dataType: 'int',
      altColumn: 'taluk_id'
    },
    village_name: {
      headingText: 'Village',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
    },
    bdm: {
      headingText: 'BDM',
      enableSort: true,
      sortColumnType: 'string',
    },
    with_krushal_from_date: {
      headingText: 'With Krushal From Date',
      formatType: 'date',
      format: 'MMM-YYYY',
      enableSort: true,
      sortColumnType: 'date',
      defaultSort: 'ascend'
    },
    with_krushal_for_months: {
      headingText: 'With Krushal From Months',
      formatType: 'months_since',
    },
    health_score: {
      type: 'number_range',
      headingText: 'Health Score',
      format: {decimalPlaces: 1},
      enableFilter: true,
      formatType: 'double',
      allowFilterInPageParams: true,
      allowSettingFilterFromPageParams: true,
      paramSearchType: 'number_range',
    },
  }
}

const updateStatusDialogFormDefinition = {
  forceNewLineAfterControl: true,
  defaultClasses: 'mb-3',
  formControls: {
    animalId: {
      type: "text",
      label: "Animal Id",
      readOnly: 1,
    },
    animalEarTag: {
      type: "text",
      label: "Animal Ear Tag",
      readOnly: 1,
    },
    farmerId: {
      type: "text",
      label: "Farmer Id",
      readOnly: 1,
    },
    farmerName: {
      type: "text",
      label: "Farmer Name",
      readOnly: 1,
    },
    farmerMobile: {
      type: "text",
      label: "Farmer Mobile",
      readOnly: 1,
    },
    sellingAnimalCurrentStatusId: {
      type: "text",
      label: "Seller Animal Selling Current Status",
      readOnly: 1,
    },
    sellingAnimalCurrentStatus: {
      type: "text",
      label: "Seller Animal Selling Current Status",
      readOnly: 1,
    },
    sellingAnimalNextStatus: {
      type: "single-select-l10n",
      label: "Animal Seller Selling Process Status",
      errorMessage: "Choosing the status the animal needs to be updated to",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Choose Yes or No",
      validations: [{ type: "Mandatory" }],
    },
    updateStatusComments: {
      type: "text",
      label: "Comments",
      validations: [{ type: "Mandatory" }],
      placeHolderText: "Enter Any Comments",
    },
  },
  visibleFormControls: [
    "animalId",
    "animalEarTag",
    "farmerId",
    "farmerName",
    "farmerMobile",
    "sellingAnimalCurrentStatusId",
    "sellingAnimalCurrentStatus",
    "sellingAnimalNextStatus",
    "updateStatusComments",
  ],
  elementToDataMapping: {
    animalId: "animal_id",
    animalEarTag: "animal_ear_tag",
    farmerId: "customer_id",
    farmerName: "customer_name",
    farmerMobile: "mobile_number",
    sellingAnimalCurrentStatusId: "selling_animal_current_status_id",
    sellingAnimalCurrentStatus: "selling_animal_current_status",
    sellingAnimalNextStatus: "selling_animal_next_status",
    updateStatusComments: "update_status_comments",
  },
  dataToElementMapping: {
    animal_id: "animalId",
    animalEarTag: "animalEarTag",
    customer_id: "farmerId",
    customer_name: "farmerName",
    mobile_number: "farmerMobile",
    selling_animal_current_status_id: "sellingAnimalCurrentStatusId",
    selling_animal_current_status: "sellingAnimalCurrentStatus",
    selling_animal_next_status: "sellingAnimalNextStatus",
    update_status_comments: "updateStatusComments",
  },
}

const updateStatusDialogFormInitialState = {
  animalId: {},
  animalEarTag: {},
  farmerId: {},
  farmerName: {},
  farmerMobile: {},
  sellingAnimalCurrentStatusId: {},
  sellingAnimalCurrentStatus: {},
  sellingAnimalNextStatus: {},
  updateStatusComments: {},
}

const itemDocumentsMediaCarouselInitialConfiguration = {
  completeInformation: false,
  showLatestOnlyFlag: true,
  enableLatestOnlyFlag: true,
  latestOnlyFlag: false,
  enablePreview: true,
  enableDownload: true,
  enableViewInBrowser: true,
  enableUploadOfFiles: false,
  uploadFolder: 'ah/animal',
  documentTypesForFilter: [
    {label:'Animal Thumbnail Photo', value:1000260001},
    {label:'Animal Photo Any Angle', value:1000260002},
    {label:'Animal Eartag Photo', value:1000260012},
    {label:'Foreign Body Area', value:1000260013},
    {label:'Animal classification documents', value:1000260016},
  ],
  document_query_information : {
    entity_name: 'animal'
  },
  base_entity_information: {
    entity_1_type_id: 1000220005,
    // entity_1_entity_uuid: Array.isArray(selectedRowKeys) && selectedRowKeys.length > 0 ? selectedRowKeys[0] : undefined
  },
  styleContainer: {
    mediaCardStyle: { display: 'inline-block', padding: '0px', margin: '0px', width: '225px', height: 'auto' },
    mediaCardBodyStyle: {padding: '12px'},
    mediaTagWrapperDivStyle: {position: 'relative', width: 'auto'},
    mediaTagWrapperOnHoverDivStyle: undefined,
    mediaTagPreviewOverlayStyle: undefined,
    mediaTagPreviewOverlayOnHoverStyle: undefined,
    mediaTagStyle: { maxWidth: '200px', height: 'auto', position: 'relative'},
  },
}
const itemCommentsModalDialogInitialConfiguration = {
  title: 'Item Comments',
  note_query_configuration: {
    note_type_id: [
      1000440010
    ],
    entity_1_type_id: 1000220005
  }
}

const SellableAnimals = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    tableConfiguration.enableTableExport = true
    tableConfiguration.enableTableExportFromCloud = true
  }

  const [reportData, setReportData] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  // const tableRef = useRef()

  const postInitialLoadCB = () => {
    assignDataToFormState(
      updateStatusDialogFormDefinition,
      {
        selling_animal_next_status_list: [
          // {reference_name_l10n:{ul:'Farmer Contacted, Follow up Call Required'},reference_id: 1000490001},
          // {reference_name_l10n:{ul:'Farmer Contacted, Detailed Process Explanation Required'},reference_id: 1000490002},
          // {reference_name_l10n:{ul:'Process Explained, Deposit Not Collected'},reference_id: 1000490003},
          // {reference_name_l10n:{ul:'Deposit Collected, Latest Photos Not Collected'},reference_id: 1000490004},
          // {reference_name_l10n:{ul:'Photos Collected, Other Process Not Completed'},reference_id: 1000490005},
          // {reference_name_l10n:{ul:'Animal Put Up For Sale'},reference_id: 1000490006},
          {reference_name_l10n:'Farmer Contacted, Follow up Call Required',reference_id: 1000490001},
          {reference_name_l10n:'Farmer Contacted, Detailed Process Explanation Required',reference_id: 1000490002},
          /* {reference_name_l10n:'Process Explained, Deposit Not Collected',reference_id: 1000490003},
          {reference_name_l10n:'Deposit Collected, Latest Photos Not Collected',reference_id: 1000490004},
          {reference_name_l10n:'Photos Collected, Other Process Not Completed',reference_id: 1000490005},
          {reference_name_l10n:'Animal Put Up For Sale',reference_id: 1000490006}, */
      ],
        // farmer_level_snooze_choice_list: [{label:'Yes', id: 1}, {label:'No', id: 2}],
      },
      updateStatusDialogFormState,
      setUpdateStatusDialogFormState
    )
  }

  const loadInitialFilterDataCB = (additionalParams) => {
    return loadFilters(userToken, additionalParams)
  }

  const loadReportCB = (reportQueryParams, additionalParams) => {
    return loadReport(userToken, reportQueryParams, additionalParams)
  }

  const [updateStatusModalDialogVisibility, setUpdateStatusModalDialogVisibility] = useState(false)
  const [updateStatusDialogFormState, setUpdateStatusDialogFormState] = useState(updateStatusDialogFormInitialState)

  const [itemDocumentsMediaCarouselConfiguration, setItemDocumentsMediaCarouselConfiguration] = useState(itemDocumentsMediaCarouselInitialConfiguration)
  const handleViewItemDocuments = () => {
    if (Array.isArray(selectedRowKeys) && selectedRowKeys.length > 0) {
      const updatedConfiguration = {
        ...itemDocumentsMediaCarouselConfiguration,
        completeInformation: true,
        base_entity_information: {
          ...itemDocumentsMediaCarouselConfiguration.base_entity_information,
          entity_uuid: Array.isArray(selectedRowKeys) && selectedRowKeys.length > 0 ? selectedRowKeys[0] : undefined
        }
      }
      setItemDocumentsMediaCarouselConfiguration(updatedConfiguration)
      return true
    } else {
      return false
    }
  }

  const onUpdateStatusModalOK = async () => {
  }

  const onUpdateStatusModalCancel = () => {
    setUpdateStatusModalDialogVisibility(false)
  }

  const enableButton = (selectedRowKeys, reportData, buttonType) => {
    let returnValue = true
    if (selectedRowKeys.length === 1) {
      const selectedRow = reportData.filter((object) => {
        return (object['oc_item_listing_uuid'] === selectedRowKeys[0])
      })
      if (selectedRow.length > 0) {
        // if (selectedRow[0].listing_status !== 'Listed Animal') {
          if (['POTENTIALFARMERS', 'STATUSUPDATE', 'SNOOZE', 'ITEMDOCUMENTS', 'COMMENTS'].includes(buttonType)) {
            returnValue = false
          }
        // }
      }
      
    }
    return returnValue
  }

  const handleUpdateStatusOfAnimalClick = () => {
    // this is one at a time
    // update state for animal id, ear tag, seller farmer name, seller farmer mobile
    console.log('PSAWL hSC 1, selectedRowKeys = ', selectedRowKeys)
    const selectedRow = reportData.filter((object) => {
      return (object['oc_item_listing_uuid'] === selectedRowKeys[0])
    })
    console.log('PSAWL hSC 2, selectedRow = ', selectedRow)
    assignDataToFormState(
      updateStatusDialogFormDefinition,
      selectedRow[0],
      updateStatusDialogFormState,
      setUpdateStatusDialogFormState
    )

    // launch popup
    // based on krushalocform
    //    pick up status
    //    pick up comments
    // on save, callback
    // call api
    console.log('PSAWL hSC 1')
    setUpdateStatusModalDialogVisibility(true)
  }

  const handleViewPotentialFarmersClick = () => {
    const filterParams = { 
      oc_item_listing_uuid: selectedRowKeys,
      // health_score: [ [4, 5] ] 
    }; // Example query parameters
    const urlQueryString = createURLQueryStringFromPageParams(undefined, filterParams, {})
    console.log('SA hVPFC 1, urlQueryString = ', urlQueryString)
    navigate({
      pathname: '/oc/potential-buyer-farmers-for-animal',
      search: `?${urlQueryString}`
    })
  }

  const [itemCommentModalDialogConfiguration, setItemCommentModalDialogConfiguration] = useState(itemCommentsModalDialogInitialConfiguration)
  const handleViewSellableItemCommentsClick = () => {
    const updatedConfiguration = {
      ...itemCommentModalDialogConfiguration,
      note_query_configuration: {
        ...itemCommentModalDialogConfiguration.note_query_configuration,
        entity_1_uuid: selectedRowKeys[0]
      }
    }
    setItemCommentModalDialogConfiguration(updatedConfiguration)
  }

  /* const backToQueueListLinkUIViewItemDocumentsFarmerUpdateStatusViewPotentialFarmersViewItemCommentsButtons = () => {
    return (
      <>
        <Link to="/dashboard/oc">Back to Queue List</Link>
        <MediaCarouselModalButton
          buttonTitle="View Item Documents"
          disabled={enableButton(selectedRowKeys, reportData, 'ITEMDOCUMENTS')}
          onClickHandler={handleViewItemDocuments}
          modalDialogTitle="Item Documents"
          configuration={itemDocumentsMediaCarouselConfiguration}
          />
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateStatusOfAnimalClick}>Update Status Of Animal</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'POTENTIALFARMERS')} onClick={handleViewPotentialFarmersClick}>View Potential Farmers</Button>
        <CommentViewerModalButton
          buttonTitle="View Item Comments"
          disabled={enableButton(selectedRowKeys, reportData, 'COMMENTS')}
          onClickHandler={handleViewSellableItemCommentsClick}
          configuration={itemCommentModalDialogConfiguration}
          />
      </>
    )
  } */

  /* const backToQueueListLinkUI = () => {
    return (
      <Link to="/dashboard/oc">Back to Queue List</Link>
    )
  }

  const viewItemDocumentsFarmerUpdateStatusViewPotentialFarmersViewItemCommentsButtons = () => {
    return (
      <>
      <MediaCarouselModalButton
        buttonTitle="View Item Documents"
        disabled={enableButton(selectedRowKeys, reportData, 'ITEMDOCUMENTS')}
        onClickHandler={handleViewItemDocuments}
        modalDialogTitle="Item Documents"
        configuration={itemDocumentsMediaCarouselConfiguration}
        />
      <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateStatusOfAnimalClick}>Update Status Of Animal</Button>
      <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'POTENTIALFARMERS')} onClick={handleViewPotentialFarmersClick}>View Potential Farmers</Button>
      <CommentViewerModalButton
        buttonTitle="View Item Comments"
        disabled={enableButton(selectedRowKeys, reportData, 'COMMENTS')}
        onClickHandler={handleViewSellableItemCommentsClick}
        configuration={itemCommentModalDialogConfiguration}
        />
      </>
    )
  } */
  
  return (
    <>
      <Modal
        title="Update Status"
        open={updateStatusModalDialogVisibility}
        onOk={onUpdateStatusModalOK}
        onCancel={onUpdateStatusModalCancel}
        >
          <>
            <KrushalOCForm
              formState={updateStatusDialogFormState}
              setFormState={setUpdateStatusDialogFormState}
              formDefinition={updateStatusDialogFormDefinition}
            />
          </>
      </Modal>
      <Space style={{marginTop:'8px', marginBottom:'8px'}}>
        <Link to="/oc">Back to Queue List</Link>
        <MediaCarouselModalButton
          buttonTitle="View Item Documents"
          disabled={enableButton(selectedRowKeys, reportData, 'ITEMDOCUMENTS')}
          onClickHandler={handleViewItemDocuments}
          modalDialogTitle="Item Documents"
          configuration={itemDocumentsMediaCarouselConfiguration}
          />
        {/* <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateStatusOfAnimalClick}>Update Status Of Animal</Button> */}
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'POTENTIALFARMERS')} onClick={handleViewPotentialFarmersClick}>View Potential Farmers</Button>
        <CommentViewerModalButton
          buttonTitle="View Item Comments"
          disabled={enableButton(selectedRowKeys, reportData, 'COMMENTS')}
          onClickHandler={handleViewSellableItemCommentsClick}
          configuration={itemCommentModalDialogConfiguration}
          />
      </Space>
      <OCTable
        // ref={tableRef}
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={tableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        postInitialLoadCallback={postInitialLoadCB}
        loadReportCallback={loadReportCB}
        loadInitialFilterDataCallback={loadInitialFilterDataCB}
        parentPageParams={pageParams}
        // showTableHeader={true}
        // omniSearchRowEnabled={true}
        // omniSearchRowBeginning={backToQueueListLinkUI}
        // omniSearchRowMiddle={viewItemDocumentsFarmerUpdateStatusViewPotentialFarmersViewItemCommentsButtons}
        // actionsRow={backToQueueListLinkUIViewItemDocumentsFarmerUpdateStatusViewPotentialFarmersViewItemCommentsButtons}
      />
    </>
  );
}

// export default SellableAnimals
export default withAuthorization(SellableAnimals, [])