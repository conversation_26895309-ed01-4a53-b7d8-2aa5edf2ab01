import { Space, Button, Modal, Checkbox } from "antd";
import { ReloadOutlined } from '@ant-design/icons';

import { useRef, useState, createRef } from "react"
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import {useSelector} from "react-redux"
import ToastersService from "../../../Services/toasters.service";

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  validateFormElements2,
  assignValuesFromControlReferencesToFormState,
  extractChangedValues,
} from "../../../Components/Common/KrushalOCForm";

const moment = require("moment");

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../../Components/user/authorize')

const { updateSellableAnimalStatus, markAnimalsAsSellable, getAnimalDataReport, /* getPotentiallySellableAnimalsReport,  */getAnimalDataReportsPageLevelFilterData, /* getPotentiallySellableAnimalsReportFilterData,  */snoozeForSellableAnimals, unsnoozeSellableAnimals } = require('../../../router')

const { OCTable, createPageParamsFromURLString, MediaCarouselModalButton, CommentViewerModalButton } = require('../../../Components/Common/AntTableHelper')

const loadReport = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Potentially Sellable Animals Report',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    queryParams['animal_selling_intent_already_expressed'] = true
    queryParams['check_for_animals_marked_for_sale'] = true
    const response = await getAnimalDataReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadFilters = async (userToken, selectedColumns, additionalParams) => {
  try {
    const headers = {
      useCase: 'Get Potentially Sellable Animals Report Filter Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getAnimalDataReportsPageLevelFilterData(headers, {selectedColumns: selectedColumns, searchConfiguration: tableConfiguration})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const tableConfiguration = {
  tableNavigationUrl: '../oc/animals-that-farmers-may-want-to-sell',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  enableSplittingOfServerData: true, // use this only if you have made changes to the backend
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'animal_id',
  columns: [
    {
      title: "Animal Id",
      key: "animal_id",
    },
    {
      title: "Tracker Link",
      key: "animal_information_tracker_link"
    },
    {
      title: 'Snoozed or Not',
      key: 'snooze_status',
    },
    {
      title: 'Selling Animal Current Status',
      key: 'selling_animal_current_status'
    },
    {
      title: 'Listed or Not',
      key: 'listing_status',
    },
    {
      title: "Animal Ear Tag",
      key: "animal_ear_tag",
    },
    {
      title: "Farmer Location Recorded Status",
      key: "farmer_location_recorded_status",
    },
    {
      title: "Location Updated Timestamp",
      key: "location_updated_timestamp",
    },
    {
      title: "Farmer Name",
      key: "customer_name",
    },
    {
      title: "Farmer Mobile Number",
      key: "mobile_number",
    },
    {
      title: "Taluk",
      key: "taluk_name",
    },
    {
      title: "Village",
      key: "village_name",
    },
    {
      title: "BDM",
      key: "bdm",
    },
    {
      title: "BDM Mobile",
      key: "bdm_mobile",
    },
    {
      title: "With Krushal From",
      key: "with_krushal_from_date",
    },
    {
      title: "With Krushal For Months",
      key: "with_krushal_for_months",
    },
    {
      title: "Latest LPD",
      key: "latest_lpd",
    },
    {
      title: "Latest LPD Date",
      key: "latest_lpd_date",
    },
    {
      title: "Maximum LPD",
      key: "maximum_lpd",
    },
    {
      title: "Maximum LPD Date",
      key: "maximum_lpd_date",
    },
    {
      title: "Number of Calvings",
      key: "number_of_calvings",
    },
    {
      title: "Number of Months Pregnant",
      key: "number_of_months_pregnant",
    },
    {
      title: "Health Score",
      key: "health_score",
    },
    {
      title: "Body Score",
      key: "body_score",
    },
    {
      title: "Udder and Teat Score",
      key: "udder_and_teat_score",
    },
    {
      title: "Minimum Price",
      key: "minimum_selling_price",
    },
    {
      title: "Maximum Price",
      key: "maximum_selling_price",
    },
  ],
  columnConfiguration: {
    animal_information_tracker_link: {
      headingText: 'Tracker Link',
      formatType: 'hyperLink',
    },
    snooze_status: {
      headingText: 'Snooze Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'Snoozed', value: 'Snoozed' },
        { text: 'Not Snoozed', value: 'Not Snoozed' },
      ],
      defaultFilteredValue: ['Not Snoozed'],
      enableSort: true,
      sortColumnType: 'string',
    },
    selling_animal_current_status: {
      headingText: 'Selling Animal Current Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      filters: [
        // { text: 'Not Spoken To', value: 1000490000 },
        // { text: 'Farmer Contacted, Follow up Call Required', value: 1000490001 },
        { text: 'Farmer Contacted, Detailed Process Explanation Required', value: 1000490002 },
        { text: 'Process Explained, Deposit Not Collected', value: 1000490003 },
        { text: 'Deposit Collected, Latest Photos Not Collected', value: 1000490004 },
        { text: 'Photos Collected, Other Process Not Completed', value: 1000490005 },
        { text: 'Animal Put Up For Sale', value: 1000490006 },
      ],
      altColumn: 'selling_animal_current_status_id'
    },
    listing_status: {
      headingText: 'Listing Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'Listed Animal', value: 'Listed Animal' },
        { text: 'Not Listed Animal', value: 'Not Listed Animal' },
      ],
      enableSort: true,
      sortColumnType: 'string',
    },
    farmer_location_recorded_status: {
      headingText: 'Farmer Location Recorded Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'Location Recorded', value: 'Location Recorded' },
        { text: 'Location Not Recorded', value: 'Location Not Recorded' },
      ],
      defaultFilteredValue: ['Location Recorded'],
      enableSort: true,
      sortColumnType: 'string',
    },
    location_updated_timestamp: {
      headingText: 'Location Updated Timestsamp',
      enableFilter: true,
      type: 'dayjs_range',
      formatType: 'date',
      format: 'h:mm a, DD-MMM-YYYY',
      enableSort: true,
      sortColumnType: 'date',
      defaultSort: 'descend'
    },
    customer_id: {
      headingText: 'Customer Id',
      allowFilterInPageParams: true,
      paramSearchType: 'array',
      paramSearchDataType: 'string'
    },
    animal_ear_tag: {
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    customer_name: {
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    mobile_number: {
      headingText: 'Mobile Number',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    taluk_name: {
      enableFilter: true,
      type: 'default_array',
      // dataType: 'string',
      dataType: 'int',
      altColumn: 'taluk_id',
      updateFilterDataDuringFilterCall: true,
    },
    village_name: {
      headingText: 'Village',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      updateFilterDataDuringReportCall: true,
    },
    bdm: {
      headingText: 'BDM',
      enableSort: true,
      sortColumnType: 'string',
    },
    with_krushal_from_date: {
      headingText: 'With Krushal From Date',
      formatType: 'date',
      format: 'MMM-YYYY',
      enableSort: true,
      sortColumnType: 'date',
      defaultSort: 'ascend'
    },
    health_score: {
      type: 'number_range',
      headingText: 'Health Score',
      format: {decimalPlaces: 1},
      enableFilter: true,
      formatType: 'double',
      allowFilterInPageParams: true,
      allowSettingFilterFromPageParams: true,
      paramSearchType: 'number_range',
    },
    minimum_selling_price: {
      type: 'number_range',
      headingText: 'Minimum Selling Price',
      enableFilter: true,
      formatType: 'int',
      enableSort: true,
      sortColumnType: 'double',
    },
  }
}

const snoozeDialogFormDefinition = {
  forceNewLineAfterControl: true,
  defaultClasses: 'mb-3',
  formControls: {
    animalId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Id",
      readOnly: 1,
    },
    animalEarTag: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Ear Tag",
      readOnly: 1,
    },
    farmerId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Id",
      readOnly: 1,
    },
    farmerName: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Name",
      readOnly: 1,
    },
    farmerMobile: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Mobile",
      readOnly: 1,
    },
    snoozeTillDate: {
      ocFormRef: createRef(),
      type: "date-time_2",
      label: "Snooze Till",
      placeHolderText: "Enter Date",
      errorMessage: "Snooze Till Date is Mandatory!",
      validations: [{ type: "Mandatory" }, {type: "MinimumDate", value: new Date()}],
    },
    farmerLevelSnoozeChoice: {
      ocFormRef: createRef(),
      type: "single-select_2",
      label: "Snooze At Farmer Level*",
      errorMessage: "Choosing if Farmer Needs to be Snoozed or Not Is Mandatory",
      labelKeyInList: "reference_name",
      valueKeyInList: "reference_id",
      placeHolderText: "Choose Yes or No",
      validations: [{ type: "Mandatory" }],
    },
    snoozeComments: {
      ocFormRef: createRef(),
      type: "text-area_2",
      label: "Comments",
      validations: [{ type: "Mandatory" }],
      placeHolderText: "Enter Any Comments",
    },
  },
  visibleFormControls: [
    "animalId",
    "animalEarTag",
    "farmerId",
    "farmerName",
    "farmerMobile",
    "snoozeTillDate",
    "farmerLevelSnoozeChoice",
    "snoozeComments",
  ],
  elementToDataMapping: {
    animalId: "animal_id",
    animalEarTag: "animal_ear_tag",
    farmerId: "customer_id",
    farmerName: "customer_name",
    farmerMobile: "mobile_number",
    snoozeTillDate: "snooze_till_date",
    farmerLevelSnoozeChoice: "farmer_level_snooze_choice",
    snoozeComments: "snooze_comments",
  },
  dataToElementMapping: {
    animal_id: "animalId",
    animalEarTag: "animalEarTag",
    customer_id: "farmerId",
    customer_name: "farmerName",
    mobile_number: "farmerMobile",
    snooze_till_date: "snoozeTillDate",
    farmer_level_snooze_choice: "farmerLevelSnoozeChoice",
    snooze_comments: "snoozeComments",
  },
}

const snoozeDialogFormInitialState = {
  snoozeTillDate: {},
  farmerLevelSnoozeChoice: {},
  snoozeComments: {},
}

const updateStatusDialogFormDefinition = {
  forceNewLineAfterControl: true,
  defaultClasses: 'mb-3',
  formControls: {
    animalId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Id",
      readOnly: 1,
    },
    animalEarTag: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Ear Tag",
      readOnly: 1,
    },
    farmerId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Id",
      readOnly: 1,
    },
    farmerName: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Name",
      readOnly: 1,
    },
    farmerMobile: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Mobile",
      readOnly: 1,
    },
    sellingAnimalCurrentStatusId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Seller Animal Selling Current Status",
      readOnly: 1,
    },
    sellingAnimalCurrentStatus: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Seller Animal Selling Current Status",
      readOnly: 1,
    },
    animalInformationTrackerLink: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Information Link",
      errorMessage: "Add a valid link",
      placeHolderText: "Add the tracker link",
      validations: [{ type: "regex", regexArray: ["HttpsLink"] }],
    },
    sellingAnimalNextStatus: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "Animal Seller Selling Process Status",
      errorMessage: "Choosing the status the animal needs to be updated to",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Choose Yes or No",
      validations: [{ type: "Mandatory" }],
    },
    updateStatusComments: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Comments",
      validations: [{ type: "Mandatory" }],
      placeHolderText: "Enter Any Comments",
    },
  },
  visibleFormControls: [
    "animalId",
    "animalEarTag",
    "farmerId",
    "farmerName",
    "farmerMobile",
    "sellingAnimalCurrentStatusId",
    "sellingAnimalCurrentStatus",
    "animalInformationTrackerLink",
    "sellingAnimalNextStatus",
    "updateStatusComments",
  ],
  elementToDataMapping: {
    animalId: "animal_id",
    animalEarTag: "animal_ear_tag",
    farmerId: "customer_id",
    farmerName: "customer_name",
    farmerMobile: "mobile_number",
    sellingAnimalCurrentStatusId: "selling_animal_current_status_id",
    sellingAnimalCurrentStatus: "selling_animal_current_status",
    animalInformationTrackerLink: "animal_information_tracker_link",
    sellingAnimalNextStatus: "selling_animal_next_status",
    updateStatusComments: "update_status_comments",
  },
  dataToElementMapping: {
    animal_id: "animalId",
    animalEarTag: "animalEarTag",
    customer_id: "farmerId",
    customer_name: "farmerName",
    mobile_number: "farmerMobile",
    selling_animal_current_status_id: "sellingAnimalCurrentStatusId",
    selling_animal_current_status: "sellingAnimalCurrentStatus",
    animal_information_tracker_link: "animalInformationTrackerLink",
    selling_animal_next_status: "sellingAnimalNextStatus",
    update_status_comments: "updateStatusComments",
  },
}

const updateStatusDialogFormInitialState = {
  animalId: {},
  animalEarTag: {},
  farmerId: {},
  farmerName: {},
  farmerMobile: {},
  sellingAnimalCurrentStatusId: {},
  sellingAnimalCurrentStatus: {},
  animalInformationTrackerLink: {},
  sellingAnimalNextStatus: {},
  updateStatusComments: {},
}

const markForSaleDialogFormDefinition = {
  forceNewLineAfterControl: true,
  defaultClasses: 'mb-3',
  formControls: {
    animalId: {
      type: "text",
      label: "Animal Id",
      readOnly: 1,
    },
    animalEarTag: {
      type: "text",
      label: "Animal Ear Tag",
      readOnly: 1,
    },
    farmerId: {
      type: "text",
      label: "Farmer Id",
      readOnly: 1,
    },
    farmerName: {
      type: "text",
      label: "Farmer Name",
      readOnly: 1,
    },
    farmerMobile: {
      type: "text",
      label: "Farmer Mobile",
      readOnly: 1,
    },
    allowSaleTillDate: {
      type: "date",
      label: "Allow Sale Till",
      placeHolderText: "Enter Date",
      errorMessage: "Allow Sale Till Date is Mandatory!",
      validations: [{ type: "Mandatory" }, {type: "MinimumDate", value: new Date()}, {type: "MaximumDate", value: moment().add(1, 'M').toDate()}],
    },
    minimumPrice: {
      type: "positive-number",
      label: "Minimum Price",
      placeHolderText: "Enter Number",
      errorMessage: "Minimum price is mandatory!",
      validations: [{ type: "Mandatory" }],
    },
    maximumPrice: {
      type: "positive-number",
      label: "Maximum Price",
      placeHolderText: "Enter Number",
      errorMessage: "Maximum price is mandatory!",
      validations: [{ type: "Mandatory" }],
    },
    markForSaleComments: {
      type: "text",
      label: "Comments",
      validations: [{ type: "Mandatory" }],
      placeHolderText: "Enter Any Comments",
    },
  },
  visibleFormControls: [
    "animalId",
    "animalEarTag",
    "farmerId",
    "farmerName",
    "farmerMobile",
    "allowSaleTillDate",
    "minimumPrice",
    "maximumPrice",
    "markForSaleComments",
  ],
  elementToDataMapping: {
    animalId: "animal_id",
    animalEarTag: "animal_ear_tag",
    farmerId: "customer_id",
    farmerName: "customer_name",
    farmerMobile: "mobile_number",
    allowSaleTillDate: "allow_sale_till_date",
    minimumPrice: "minimum_price",
    maximumPrice: "maximum_price",
    markForSaleComments: "mark_for_sale_comments",
  },
  dataToElementMapping: {
    animal_id: "animalId",
    animalEarTag: "animalEarTag",
    customer_id: "farmerId",
    customer_name: "farmerName",
    mobile_number: "farmerMobile",
    allow_sale_till_date: "allowSaleTillDate",
    minimum_price: "minimumPrice",
    maximum_price: "maximumPrice",
    mark_for_sale_comments: "markForSaleComments",
  },
}

const markForSaleDialogFormInitialState = {
  animalId: {},
  animalEarTag: {},
  farmerId: {},
  farmerName: {},
  farmerMobile: {},
  allowSaleTillDate: {},
  minimumPrice: {},
  maximumPrice: {},
  markForSaleComments: {},
}

const animalDocumentsMediaCarouselInitialConfiguration = {
  completeInformation: false,
  showLatestOnlyFlag: true,
  enableLatestOnlyFlag: true,
  latestOnlyFlag: false,
  enableDownload: true,
  enablePreview: true,
  enableViewInBrowser: true,
  styleContainer: {
    mediaCardStyle: { display: 'inline-block', padding: '0px', margin: '0px', width: '225px', height: 'auto' },
    mediaCardBodyStyle: {padding: '12px'},
    mediaTagWrapperDivStyle: {position: 'relative', width: 'auto'},
    mediaTagWrapperOnHoverDivStyle: undefined,
    mediaTagPreviewOverlayStyle: undefined,
    mediaTagPreviewOverlayOnHoverStyle: undefined,
    mediaTagStyle: { maxWidth: '200px', height: 'auto', position: 'relative'},
  },
  enableUploadOfFiles: false,
  uploadFolder: 'ah/animal',
  documentTypesForFilter: [
    {label:'Animal Thumbnail Photo', value:1000260001},
    {label:'Animal Photo Any Angle', value:1000260002},
    {label:'Animal Eartag Photo', value:1000260012},
    {label:'Foreign Body Area', value:1000260013},
    {label:'Animal classification documents', value:1000260016},
  ],
  document_query_information : {
    entity_name: 'animal',
    related_additional_entity_types: ['animal_classification']
  },
  base_entity_information: {
    entity_1_type_id: 1000220002,
  }
}

const sellableAnimalCommentsModalDialogInitialConfiguration = {
  title: 'Sellable Animal Comments',
  note_query_configuration: {
    note_type_id: [
      1000440008,
      1000440009
    ],
    entity_1_type_id: 1000220002
  }
}

const AnimalsThatFarmersMayWantToSell = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    tableConfiguration.enableTableExport = true
    tableConfiguration.enableTableExportFromCloud = true
  }

  const [reportData, setReportData] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const tableRef = useRef()

  const postInitialLoadCB = () => {
    assignDataToFormState(
      snoozeDialogFormDefinition,
      {
        farmer_level_snooze_choice_list: [{reference_name:'Yes', reference_id: 1000105001}, {reference_name:'No', reference_id: 1000105002}],
      },
      snoozeDialogFormState,
      setSnoozeDialogFormState
    )
    assignDataToFormState(
      updateStatusDialogFormDefinition,
      {
        selling_animal_next_status_list: [
          {reference_name_l10n:'Farmer Contacted, Follow up Call Required',reference_id: 1000490001},
          {reference_name_l10n:'Farmer Contacted, Detailed Process Explanation Required',reference_id: 1000490002},
          {reference_name_l10n:'Process Explained, Deposit Not Collected',reference_id: 1000490003},
          {reference_name_l10n:'Deposit Collected, Latest Photos Not Collected',reference_id: 1000490004},
          {reference_name_l10n:'Photos Collected, Other Process Not Completed',reference_id: 1000490005},
          {reference_name_l10n:'Animal Put Up For Sale',reference_id: 1000490006},
      ],
      },
      updateStatusDialogFormState,
      setUpdateStatusDialogFormState
    )
  }

  const loadInitialFilterDataCB = (additionalParams) => {
    const selectedColumns = tableRef.current.getSelectedColumnsInTable()
    return loadFilters(userToken, selectedColumns, additionalParams)
  }

  const loadReportCB = (reportQueryParams, additionalParams) => {
    if (excludeBDMInQuery) {
      reportQueryParams.excludeBDM = true
    }
    return loadReport(userToken, reportQueryParams, additionalParams)
  }

  const [snoozeModalDialogVisibility, setSnoozeModalDialogVisibility] = useState(false)
  const [updateStatusModalDialogVisibility, setUpdateStatusModalDialogVisibility] = useState(false)
  const [markForSaleModalDialogVisibility, setMarkForSaleModalDialogVisibility] = useState(false)

  const [excludeBDMInQuery, setExcludeBDMInQuery] = useState(true)

  const [snoozeDialogFormState, setSnoozeDialogFormState] = useState(snoozeDialogFormInitialState);
  const [updateStatusDialogFormState, setUpdateStatusDialogFormState] = useState(updateStatusDialogFormInitialState)
  const [markForSaleDialogFormState, setMarkForSaleDialogFormState] = useState(markForSaleDialogFormInitialState)

  const [animalDocumentsMediaCarouselConfiguration, setAnimalDocumentsMediaCarouselConfiguration] = useState(animalDocumentsMediaCarouselInitialConfiguration)
  const handleViewAnimalDocuments = () => {
    if (Array.isArray(selectedRowKeys) && selectedRowKeys.length > 0) {
      const updatedConfiguration = {
        ...animalDocumentsMediaCarouselConfiguration,
        base_entity_information: {
          ...animalDocumentsMediaCarouselConfiguration.base_entity_information,
          entity_uuid: Array.isArray(selectedRowKeys) && selectedRowKeys.length > 0 ? selectedRowKeys[0] : undefined
        },
        completeInformation: true,
      }
      setAnimalDocumentsMediaCarouselConfiguration(updatedConfiguration)
      return true
    } else {
      return false
    }
  }

  const handleViewAnimalClick = (event) => {
    console.log('DCOFSA hVAC 1')
    const selectedRow = reportData.filter((object) => {
      return (object['animal_id'] === selectedRowKeys[0])
    })

    const animalId = selectedRow[0]['animal_id']
    const customerId = selectedRow[0]['customer_id']
    // navigate({
    //   pathname: `../oc/farmer-details`,
    //   search: `?qP=${encodedParamsAsString}`
    // })
    const navigationLink = `/e/farmer-animal-details/${customerId}/animal/${animalId}`
    if (event.ctrlKey || event.metaKey) {
      window.open(navigationLink, '_blank')
    } else {
      navigate({
        pathname: navigationLink,
        // search: `?qP=${encodedParamsAsString}`
      })
    }
  }

  const handleSnoozeClick = () => {
    // this is one at a time
    // update state for animal id, ear tag, seller farmer name, seller farmer mobile
    console.log('PSAWL hSC 1, selectedRowKeys = ', selectedRowKeys)
    const selectedRow = reportData.filter((object) => {
      return (object['animal_id'] === selectedRowKeys[0])
    })
    console.log('PSAWL hSC 2, selectedRow = ', selectedRow)
    assignDataToFormState(
      snoozeDialogFormDefinition,
      selectedRow[0],
      snoozeDialogFormState,
      setSnoozeDialogFormState
    )

    // launch popup
    // based on krushalocform
    //    pick up date
    //    pick up if override is at farmer level
    //    pick up comments
    // on save, callback
    // call api
    console.log('PSAWL hSC 1')
    // setSnoozeDialogFormState(snoozeDialogFormInitialState)
    setSnoozeModalDialogVisibility(true)
  }

  const handleUnsnoozeClick = async () => {
    try {
      console.log('PSA hUC 2')
      const headers = {
        useCase: 'Mark Animals As Unsnoozed',
        token: userToken.accessToken, //authToken
        refreshtoken: localStorage.getItem("refreshToken")
      }
      console.log('PSA hUC 2')
      const responseWithData = await unsnoozeSellableAnimals(headers, {to_be_unsnoozed_animal_array: selectedRowKeys})
      console.log('PSA hUC 3, responseWithData = ', responseWithData)
      const response = responseWithData.data
      if (response.return_code === 0) {
        ToastersService.successToast("Successfully Unsnoozed Animal");
        // await reloadDataAndMoveToPageOne(tableConfiguration)
        if (tableRef.current) {
          tableRef.current.reloadDataAndMoveToPageOne()
        }
      } else {
        ToastersService.failureToast("Could not unsnooze animal")  
      }
    } catch (error) {
      console.log('PSAWL hMASC 10, error')
      console.log('PSAWL hMASC 10a, error = ', error)
      ToastersService.failureToast("Could not unsnooze animal")
    }
  }

  const handleMarkAnimalSellableClick = async () => {
    const selectedRow = reportData.filter((object) => {
      return (object['animal_id'] === selectedRowKeys[0])
    })
    console.log('PSAWL hSC 2, selectedRow = ', selectedRow)
    assignDataToFormState(
      markForSaleDialogFormDefinition,
      selectedRow[0],
      markForSaleDialogFormState,
      setMarkForSaleDialogFormState
    )

    // launch popup
    // based on krushalocform
    //    pick up date
    //    pick up if override is at farmer level
    //    pick up comments
    // on save, callback
    // call api
    console.log('PSAWL hSC 1')
    // setSnoozeDialogFormState(snoozeDialogFormInitialState)
    setMarkForSaleModalDialogVisibility(true)
  }

  const onSnoozeModalOK = async () => {
    try {
      /* const [currentFormState, validationFailed] = validateFormElements(
        snoozeDialogFormDefinition,
        snoozeDialogFormState,
        setSnoozeDialogFormState
      ) */
      const [currentFormState, validationFailed] = validateFormElements2(
        snoozeDialogFormDefinition,
        snoozeDialogFormState,
        setSnoozeDialogFormState
      )
      // console.log("AF1 sF 2, currentFormStateTab0 = ", currentFormStateTab0);
      if (validationFailed) {
        // do something
        console.log("Validation Failed");
        ToastersService.failureToast("Validation failed!");
      } else {
        console.log("you can save now");
        const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(snoozeDialogFormDefinition, currentFormState, setSnoozeDialogFormState)
        const extractedValues = extractChangedValues(snoozeDialogFormDefinition, updatedCurrentFormState, ['animalId', 'farmerId'])
        console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValues)
        const animalWithSnoozeInformation = {...extractedValues}
        const headers = {
          useCase: 'Snooze for Sellable Animals',
          token: userToken.accessToken, //authToken
          refreshtoken: localStorage.getItem("refreshToken")
        }
        // configureParams(extractedValues, navigate,reason)
        // callSnoozeAPI
        const responseWithData = await snoozeForSellableAnimals(headers, animalWithSnoozeInformation)
        const response = responseWithData.data
        console.log('AF1 sF 2, response = ', response)
        if (response.return_code === 0) {
          ToastersService.successToast("Snoozed Successfully")
          // await reloadDataAndMoveToPageOne(tableConfiguration)
          if (tableRef.current) {
            tableRef.current.reloadDataAndMoveToPageOne()
          }
  
          setSnoozeModalDialogVisibility(false)
        } else {
          ToastersService.failureToast("Could not snooze")
        }
      }
    } catch (error) {
      console.log('AF1 sF 10, error')
      console.log('AF1 sF 10a, error = ', error)
      ToastersService.failureToast("Could not snooze")
    }
  }

  const onSnoozeModalCancel = () => {
    setSnoozeModalDialogVisibility(false)
  }

  const isNewSellableStatusAcceptableFromPrevious = (extractedValues) => {
    const newStatusId = extractedValues.selling_animal_next_status[0]
    let currentStatusId = extractedValues.selling_animal_current_status_id
    let acceptableStatus = false
    if (currentStatusId) {
      if (typeof currentStatusId === 'string' || currentStatusId instanceof String) {
        currentStatusId = parseInt(currentStatusId)
      }
      switch (currentStatusId) {
        case 1000490000:
          if ([1000490001, 1000490002].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000490001:
          if ([1000490002].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000490002:
          if ([1000490003].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000490003:
          if ([1000490004].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000490004:
          if ([1000490005].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000490005:
          if ([1000490006].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
      }
    } else {
      if ([1000490001, 1000490002].includes(newStatusId)) {
        acceptableStatus = true
      }
    }
    return acceptableStatus
  }

  const onUpdateStatusModalOK = async () => {
    try {
      /* const [currentFormState, validationFailed] = validateFormElements(
        updateStatusDialogFormDefinition,
        updateStatusDialogFormState,
        setUpdateStatusDialogFormState
      ) */
      const [currentFormState, validationFailed] = validateFormElements2(
        updateStatusDialogFormDefinition,
        updateStatusDialogFormState,
        setUpdateStatusDialogFormState
      )
      // console.log("AF1 sF 2, currentFormStateTab0 = ", currentFormStateTab0);
      if (validationFailed) {
        // do something
        console.log("Validation Failed");
        ToastersService.failureToast("Validation failed!");
      } else {
        const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(updateStatusDialogFormDefinition, currentFormState, setUpdateStatusDialogFormState)
        const extractedValues = extractChangedValues(updateStatusDialogFormDefinition, updatedCurrentFormState, ['animalId', 'farmerId', 'sellingAnimalCurrentStatusId', 'sellingAnimalCurrentStatus'])
        console.log("AF1 sF 1, extractedValues = ", extractedValues)
        const acceptableStatus = isNewSellableStatusAcceptableFromPrevious(extractedValues)
        if (!acceptableStatus) {
          ToastersService.failureToast("Not an acceptable status")
        } else {
          // update sellable status
          const animalStatusUpdateInformation = {...extractedValues}
          const headers = {
            useCase: 'Update Sellable Animal Status',
            token: userToken.accessToken, //authToken
            refreshtoken: localStorage.getItem("refreshToken")
          }
          // configureParams(extractedValues, navigate,reason)
          // callSnoozeAPI
          const responseWithData = await updateSellableAnimalStatus(headers, animalStatusUpdateInformation)
          const response = responseWithData.data
          console.log('AF1 sF 2, response = ', response)
          if (response.return_code === 0) {
            setUpdateStatusModalDialogVisibility(false)
            ToastersService.successToast("Status Updated Successfully")
            // await reloadDataAndMoveToPageOne(tableConfiguration)
            if (tableRef.current) {
              tableRef.current.reloadDataAndMoveToPageOne()
            }    
          } else {
            ToastersService.failureToast("Could not update status of animal")
          }
        }
        /* 
         */
      }
    } catch (error) {
      console.log('AF1 sF 10, error')
      console.log('AF1 sF 10a, error = ', error)
      ToastersService.failureToast("Could not snooze")
    }
  }

  const onUpdateStatusModalCancel = () => {
    setUpdateStatusModalDialogVisibility(false)
  }

  const onMarkForSaleModalOK = async () => {
    try {
      const [currentFormState, validationFailed] = validateFormElements(
        markForSaleDialogFormDefinition,
        markForSaleDialogFormState,
        setMarkForSaleDialogFormState
      );
      // console.log("AF1 sF 2, currentFormStateTab0 = ", currentFormStateTab0);
      if (validationFailed) {
        // do something
        console.log("Validation Failed");
        ToastersService.failureToast("Validation failed!");
      } else {
        const extractedValues = extractChangedValues(markForSaleDialogFormDefinition, currentFormState, ['animalId', 'farmerId'])
        console.log("AF1 sF 1, extractedValues = ", extractedValues)
        // update sellable status
        const sellingAnimalInformation = {...extractedValues, animal_id_array: selectedRowKeys}
        console.log('PSAWL hMASC 1')
        const headers = {
          useCase: 'Mark Animals As Sellable',
          token: userToken.accessToken, //authToken
          refreshtoken: localStorage.getItem("refreshToken")
        }
        console.log('PSAWL hMASC 2')
        const responseWithData = await markAnimalsAsSellable(headers, sellingAnimalInformation)
        console.log('PSAWL hMASC 3, responseWithData = ', responseWithData)
        const response = responseWithData.data
        if (response.return_code === 0) {
          setMarkForSaleModalDialogVisibility(false)
          ToastersService.successToast("Marked animal as sellable");
          // await reloadDataAndMoveToPageOne(tableConfiguration)
          if (tableRef.current) {
            tableRef.current.reloadDataAndMoveToPageOne()
          }
  
        } else {
          ToastersService.failureToast("Could not mark animal as sellable")  
        }
        /* 
         */
      }
    } catch (error) {
      console.log('AF1 sF 10, error')
      console.log('AF1 sF 10a, error = ', error)
      ToastersService.failureToast("Could not snooze")
    }
  }

  const onMarkForSaleModalCancel = () => {
    setMarkForSaleModalDialogVisibility(false)
  }

  const enableButton = (selectedRowKeys, reportData, buttonType) => {
    let returnValue = true
    if (selectedRowKeys.length === 1) {
      const selectedRow = reportData.filter((object) => {
        return (object['animal_id'] === selectedRowKeys[0])
      })
      if (selectedRow.length > 0) {
        // if (selectedRow[0].listing_status !== 'Listed Animal') {
          if (buttonType === 'MARKFORSALE') {
            if (selectedRow[0].selling_animal_current_status_id === 1000490006
                || selectedRow[0].selling_animal_current_status_id === '1000490006') {
              returnValue = false  
            }
          } else if (['ANIMALDETAILS', 'STATUSUPDATE', 'SNOOZE', 'ANIMALDOCUMENTS', 'COMMENTS'].includes(buttonType)) {
            returnValue = false
          }
        // }
      }
      
    }
    return returnValue
  }

  const handleUpdateStatusOfAnimalClick = () => {
    // this is one at a time
    // update state for animal id, ear tag, seller farmer name, seller farmer mobile
    console.log('PSAWL hSC 1, selectedRowKeys = ', selectedRowKeys)
    const selectedRow = reportData.filter((object) => {
      return (object['animal_id'] === selectedRowKeys[0])
    })
    console.log('PSAWL hSC 2, selectedRow = ', selectedRow)
    assignDataToFormState(
      updateStatusDialogFormDefinition,
      selectedRow[0],
      updateStatusDialogFormState,
      setUpdateStatusDialogFormState
    )

    // launch popup
    // based on krushalocform
    //    pick up status
    //    pick up comments
    // on save, callback
    // call api
    console.log('PSAWL hSC 1')
    setUpdateStatusModalDialogVisibility(true)
  }

  const [sellableAnimalCommentModalDialogConfiguration, setSellableAnimalCommentModalDialogConfiguration] = useState(sellableAnimalCommentsModalDialogInitialConfiguration)
  const handleViewSellableAnimalCommentsClick = () => {
    console.log('ATFMWTS hVSACC 1')
    const updatedConfiguration = {
      ...sellableAnimalCommentModalDialogConfiguration,
      note_query_configuration: {
        ...sellableAnimalCommentModalDialogConfiguration.note_query_configuration,
        entity_1_uuid: selectedRowKeys[0]
      }
    }
    setSellableAnimalCommentModalDialogConfiguration(updatedConfiguration)
  }

  /* const backToQueueListLinkUIViewAnimalDocumentsFarmerSnoozeAndFarmerUpdateStatusMarkAnimalSellableViewAnimalCommentsButtons = () => {
    return (
      <>
        <Link to="/dashboard/oc">Back to Queue List</Link>
        <MediaCarouselModalButton
          buttonTitle="View Animal Documents"
          disabled={enableButton(selectedRowKeys, reportData, 'ANIMALDOCUMENTS')}
          onClickHandler={handleViewAnimalDocuments}
          modalDialogTitle="Animal Documents"
          configuration={animalDocumentsMediaCarouselConfiguration}
          />
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'SNOOZE')} onClick={handleSnoozeClick}>Snooze</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'UNSNOOZE')} onClick={handleUnsnoozeClick}>Un-snooze</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateStatusOfAnimalClick}>Update Status Of Animal</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'MARKFORSALE')} onClick={handleMarkAnimalSellableClick}>Mark Animal Sellable</Button>
        <CommentViewerModalButton
          buttonTitle="View Sellable Animal Comments"
          disabled={enableButton(selectedRowKeys, reportData, 'COMMENTS')}
          onClickHandler={handleViewSellableAnimalCommentsClick}
          configuration={sellableAnimalCommentModalDialogConfiguration}
          />
      </>
    )
  } */

  /* const backToQueueListLinkUI = () => {
    return (
      <Link to="/dashboard/oc">Back to Queue List</Link>
    )
  }

  const viewAnimalDocumentsFarmerSnoozeAndFarmerUpdateStatusMarkAnimalSellableViewAnimalCommentsButtons = () => {
    return (
      <>
        <MediaCarouselModalButton
          buttonTitle="View Animal Documents"
          disabled={enableButton(selectedRowKeys, reportData, 'ANIMALDOCUMENTS')}
          onClickHandler={handleViewAnimalDocuments}
          modalDialogTitle="Animal Documents"
          configuration={animalDocumentsMediaCarouselConfiguration}
          />
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'SNOOZE')} onClick={handleSnoozeClick}>Snooze</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'UNSNOOZE')} onClick={handleUnsnoozeClick}>Un-snooze</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateStatusOfAnimalClick}>Update Status Of Animal</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'MARKFORSALE')} onClick={handleMarkAnimalSellableClick}>Mark Animal Sellable</Button>
        <CommentViewerModalButton
          buttonTitle="View Sellable Animal Comments"
          disabled={enableButton(selectedRowKeys, reportData, 'COMMENTS')}
          onClickHandler={handleViewSellableAnimalCommentsClick}
          configuration={sellableAnimalCommentModalDialogConfiguration}
          />
      </>
    )
  } */

  return (
    <>
      <Modal
        title="Snooze Animal"
        open={snoozeModalDialogVisibility}
        onOk={onSnoozeModalOK}
        onCancel={onSnoozeModalCancel}
        >
          <>
            <KrushalOCForm
              formState={snoozeDialogFormState}
              setFormState={setSnoozeDialogFormState}
              formDefinition={snoozeDialogFormDefinition}
            />
          </>
      </Modal>
      <Modal
        title="Update Status"
        open={updateStatusModalDialogVisibility}
        onOk={onUpdateStatusModalOK}
        onCancel={onUpdateStatusModalCancel}
        >
          <>
            <KrushalOCForm
              formState={updateStatusDialogFormState}
              setFormState={setUpdateStatusDialogFormState}
              formDefinition={updateStatusDialogFormDefinition}
            />
          </>
      </Modal>
      <Modal
        title="Mark for Sale"
        open={markForSaleModalDialogVisibility}
        onOk={onMarkForSaleModalOK}
        onCancel={onMarkForSaleModalCancel}
        >
          <>
            <KrushalOCForm
              formState={markForSaleDialogFormState}
              setFormState={setMarkForSaleDialogFormState}
              formDefinition={markForSaleDialogFormDefinition}
            />
          </>
      </Modal>
      <Space style={{marginTop:'8px', marginBottom:'8px'}}>
        <Link to="/oc">Back to Queue List</Link>
        <MediaCarouselModalButton
          buttonTitle="View Animal Documents"
          disabled={enableButton(selectedRowKeys, reportData, 'ANIMALDOCUMENTS')}
          onClickHandler={handleViewAnimalDocuments}
          modalDialogTitle="Animal Documents"
          configuration={animalDocumentsMediaCarouselConfiguration}
          />
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'ANIMALDETAILS')} onClick={handleViewAnimalClick}>View Animal Details</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'SNOOZE')} onClick={handleSnoozeClick}>Snooze</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'UNSNOOZE')} onClick={handleUnsnoozeClick}>Un-snooze</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateStatusOfAnimalClick}>Update Status Of Animal</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'MARKFORSALE')} onClick={handleMarkAnimalSellableClick}>Mark Animal Sellable</Button>
        <CommentViewerModalButton
          buttonTitle="View Sellable Animal Comments"
          disabled={enableButton(selectedRowKeys, reportData, 'COMMENTS')}
          onClickHandler={handleViewSellableAnimalCommentsClick}
          configuration={sellableAnimalCommentModalDialogConfiguration}
          />
        <Checkbox
          checked={excludeBDMInQuery}
          onChange={() => {
            setExcludeBDMInQuery(!excludeBDMInQuery)
          }}
          >
            Exclude BDM From Query
        </Checkbox>
        <ReloadOutlined onClick={() => {
          if (tableRef.current) {
            tableRef.current.reloadDataAndMoveToPageOne()
          }
        }}/>
      </Space>
      <OCTable 
        ref={tableRef}
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={tableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        postInitialLoadCallback={postInitialLoadCB}
        loadReportCallback={loadReportCB}
        loadInitialFilterDataCallback={loadInitialFilterDataCB}
        parentPageParams={pageParams}
        // showTableHeader={true}
        // omniSearchRowEnabled={true}
        // omniSearchRowBeginning={backToQueueListLinkUI}
        // omniSearchRowMiddle={viewAnimalDocumentsFarmerSnoozeAndFarmerUpdateStatusMarkAnimalSellableViewAnimalCommentsButtons}
        // actionsRow={backToQueueListLinkUIViewAnimalDocumentsFarmerSnoozeAndFarmerUpdateStatusMarkAnimalSellableViewAnimalCommentsButtons}
      />
    </>
  );
}

// export default AnimalsThatFarmersMayWantToSell
export default withAuthorization(AnimalsThatFarmersMayWantToSell, [])