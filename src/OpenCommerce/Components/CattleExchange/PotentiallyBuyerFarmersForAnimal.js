import { Space, Button, Modal } from "antd";
import { useState, useRef, createRef } from "react"
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import {useSelector} from "react-redux"
import ToastersService from "../../../Services/toasters.service";

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  validateFormElements2,
  assignValuesFromControlReferencesToFormState,
  extractChangedValues,
} from "../../../Components/Common/KrushalOCForm";

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../../Components/user/authorize')

const { getPotentialBuyerFarmersForAnimalReport, getPotentialBuyerFarmersForAnimalReportFilterData, snoozeForBuyerFarmers, updateBuyerFarmerStatus, getItemDetailByItemListingId } = require('../../../router')

const { OCTable, createPageParamsFromURLString, CommentViewerModalButton } = require('../../../Components/Common/AntTableHelper')

// const { MediaCarousel: MediaCarousel2, standardMediaCallback } = require('../../../Components/Common/MediaCarousel')
const { MediaCarousel } = require('../../../Components/Common/MediaComponents')

const loadReport = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Potential Buyer Farmers For Animal Report',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getPotentialBuyerFarmersForAnimalReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadFilters = async (userToken) => {
  try {
    const headers = {
      useCase: 'Get Potential Buyer Farmers For Animal Report Filter Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getPotentialBuyerFarmersForAnimalReportFilterData(headers)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadItemInformation = async (userToken, ocItemListingUUID) => {
  try {
    const headers = {
      useCase: 'Get Item Listing Information',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const itemListingObject = {
      oc_item_listing_uuid: ocItemListingUUID
    }
    const response = await getItemDetailByItemListingId(headers, itemListingObject)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
  
}

const tableConfiguration = {
  tableNavigationUrl: '../oc/potential-buyer-farmers-for-animal',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'customer_id',
  columns: [
    {
      title: "Farmer Id",
      key: "customer_id",
    },
    {
      title: 'Snoozed or Not',
      key: 'snooze_status',
    },
    {
      title: 'Buying Farmer Current Status',
      key: 'buying_farmer_current_status'
    },
    {
      title: "Farmer Name",
      key: "customer_name",
    },
    {
      title: "Farmer Mobile Number",
      key: "mobile_number",
    },
    {
      title: "No. of Adult Animals",
      key: "number_of_adult_animals",
    },
    {
      title: "Type of Dairy Farm",
      key: "type_of_dairy_farm",
    },
    {
      title: "Taluk",
      key: "taluk_name",
    },
    {
      title: "Village",
      key: "village_name",
    },
    {
      title: "With Krushal Since",
      key: "with_krushal_since",
    },
    {
      title: "Distance In Km From Seller Farmer",
      key: "distance_from_seller_farmer",
    }/*,
    {
      title: "BDM",
      key: "bdm",
    },
    {
      title: "BDM Mobile",
      key: "bdm_mobile",
    }*/
  ],
  columnConfiguration: {
    with_krushal_since: {
      formatType: 'date',
      format: 'MMM-YYYY',
    },
    oc_item_listing_uuid: {
      headingText: "Item Listing Id",
      allowFilterInPageParams: true,
    },
    snooze_status: {
      headingText: 'Snooze Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'Snoozed', value: 'Snoozed' },
        { text: 'Not Snoozed', value: 'Not Snoozed' },
      ],
      defaultFilteredValue: ['Not Snoozed'],
      enableSort: true,
      sortColumnType: 'string',
    },
    buying_farmer_current_status: {
      headingText: 'Buying Farmer Current Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      filters: [
        { text: 'Not Spoken To', value: 'Not Spoken To', value_int: ********** },
        { text: 'Farmer Contacted, Follow up Call Required', value: 'Farmer Contacted, Follow up Call Required', value_int: ********** },
        { text: 'Farmer Contacted, Detailed Process Explanation Required', value: 'Farmer Contacted, Detailed Process Explanation Required', value_int: ********** },
      ],
      defaultFilteredValue: ['Not Spoken To', 'Farmer Contacted, Follow up Call Required'],
      translateFilteredValuesToAlt: 'value_int',
      altColumn: 'buying_farmer_current_status_id'
    },
    customer_name: {
      headingText: 'Farmer Name',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    mobile_number: {
      headingText: 'Mobile Number',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    taluk_name: {
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      dataType: 'int',
      altColumn: 'taluk_id'
    },
    village_name: {
      headingText: 'Village',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
    },
    distance_from_seller_farmer: {
      headingText: 'Health Score',
      type: 'number_range',
      enableFilter: true,
      defaultFilteredValue: [[25,100]],
    }
/*     snooze_status: {
      headingText: 'Snooze Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'Snoozed', value: 'Snoozed' },
        { text: 'Not Snoozed', value: 'Not Snoozed' },
      ],
      defaultFilteredValue: ['Not Snoozed'],
      enableSort: true,
      sortColumnType: 'string',
    },
    buying_farmer_current_status: {
      headingText: 'Buying Farmer Current Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      filters: [
        { text: 'Not Spoken To', value: ********** },
        { text: 'Farmer Contacted, Follow up Call Required', value: ********** },
        { text: 'Farmer Contacted, Detailed Process Explanation Required', value: ********** },
        { text: 'Process Explained, Deposit Not Collected', value: ********** },
        { text: 'Deposit Collected, Visit Not Collected', value: 1000710004 },
        { text: 'Visit Arranged, Not Finalized', value: 1000710005 },
        { text: 'Farmer Purchased Animal', value: 1000710006 },
      ],
      defaultFilteredValue: [**********],
      altColumn: 'buying_farmer_current_status_id'
    },
    customer_id: {
      headingText: 'Customer Id',
      allowFilterInPageParams: true,
      paramSearchType: 'array',
      paramSearchDataType: 'string'
    },
    animal_ear_tag: {
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    customer_name: {
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    mobile_number: {
      type: 'string',
      includeInOmnisearch: true
    },
    taluk_name: {
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      // dataType: 'int',
      // altColumn: 'taluk_id'
    },
    bdm: {
      headingText: 'BDM',
      enableSort: true,
      sortColumnType: 'string',
    },
    with_krushal_from_date: {
      headingText: 'With Krushal From Date',
      formatType: 'date',
      format: 'MMM-YYYY',
      enableSort: true,
      sortColumnType: 'date',
      defaultSort: 'ascend'
    },
 */  }
}

const snoozeDialogFormDefinition = {
  forceNewLineAfterControl: true,
  defaultClasses: 'mb-3',
  formControls: {
    farmerId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Id",
      readOnly: 1,
    },
    farmerName: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Name",
      readOnly: 1,
    },
    farmerMobile: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Mobile",
      readOnly: 1,
    },
    snoozeTillDate: {
      ocFormRef: createRef(),
      type: "date-time_2",
      label: "Snooze Till",
      placeHolderText: "Enter Date",
      errorMessage: "Snooze Till Date is Mandatory!",
      validations: [{ type: "Mandatory" }, {type: "MinimumDate", value: new Date()}],
    },
    farmerLevelSnoozeChoice: {
      ocFormRef: createRef(),
      type: "single-select_2",
      label: "Snooze At Farmer Level*",
      errorMessage: "Choosing if Farmer Needs to be Snoozed or Not Is Mandatory",
      labelKeyInList: "reference_name",
      valueKeyInList: "reference_id",
      placeHolderText: "Choose Yes or No",
      validations: [{ type: "Mandatory" }],
    },
    snoozeComments: {
      ocFormRef: createRef(),
      type: "text-area_2",
      label: "Comments",
      validations: [{ type: "Mandatory" }],
      placeHolderText: "Enter Any Comments",
    },
  },
  visibleFormControls: [
    "farmerId",
    "farmerName",
    "farmerMobile",
    "snoozeTillDate",
    "farmerLevelSnoozeChoice",
    "snoozeComments",
  ],
  elementToDataMapping: {
    farmerId: "customer_id",
    farmerName: "customer_name",
    farmerMobile: "mobile_number",
    snoozeTillDate: "snooze_till_date",
    farmerLevelSnoozeChoice: "farmer_level_snooze_choice",
    snoozeComments: "snooze_comments",
  },
  dataToElementMapping: {
    customer_id: "farmerId",
    customer_name: "farmerName",
    mobile_number: "farmerMobile",
    snooze_till_date: "snoozeTillDate",
    farmer_level_snooze_choice: "farmerLevelSnoozeChoice",
    snooze_comments: "snoozeComments",
  },
}

const snoozeDialogFormInitialState = {
  snoozeTillDate: {},
  farmerLevelSnoozeChoice: {},
  snoozeComments: {},
}

const updateStatusDialogFormDefinition = {
  forceNewLineAfterControl: true,
  defaultClasses: 'mb-3',
  formControls: {
    farmerId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Id",
      readOnly: 1,
    },
    farmerName: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Name",
      readOnly: 1,
    },
    farmerMobile: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Mobile",
      readOnly: 1,
    },
    buyingFarmerCurrentStatusId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Buying Farmer Current Status",
      readOnly: 1,
    },
    buyingFarmerCurrentStatus: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Buying Farmer Current Status",
      readOnly: 1,
    },
    buyingFarmerNextStatus: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "Buying Farmer Process Next Status",
      errorMessage: "Choosing the status the farmer needs to be updated to",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Choose Yes or No",
      validations: [{ type: "Mandatory" }],
    },
    updateStatusComments: {
      ocFormRef: createRef(),
      type: "text-area_2",
      label: "Comments",
      validations: [{ type: "Mandatory" }],
      placeHolderText: "Enter Any Comments",
    },
  },
  visibleFormControls: [
    "farmerId",
    "farmerName",
    "farmerMobile",
    "buyingFarmerCurrentStatusId", 
    "buyingFarmerCurrentStatus",
    "buyingFarmerNextStatus",
    "updateStatusComments",
  ],
  elementToDataMapping: {
    animalId: "animal_id",
    animalEarTag: "animal_ear_tag",
    farmerId: "customer_id",
    farmerName: "customer_name",
    farmerMobile: "mobile_number",
    buyingFarmerCurrentStatusId: "buying_farmer_current_status_id", 
    buyingFarmerCurrentStatus: "buying_farmer_current_status",
    buyingFarmerNextStatus: "buying_farmer_next_status",
    updateStatusComments: "update_status_comments",
  },
  dataToElementMapping: {
    customer_id: "farmerId",
    customer_name: "farmerName",
    mobile_number: "farmerMobile",
    buying_farmer_current_status_id: "buyingFarmerCurrentStatusId", 
    buying_farmer_current_status: "buyingFarmerCurrentStatus",
    buying_farmer_next_status: "buyingFarmerNextStatus",
    update_status_comments: "updateStatusComments",
  },
}

const updateStatusDialogFormInitialState = {
  farmerId: {},
  farmerName: {},
  farmerMobile: {},
  buyingFarmerCurrentStatusId: {},
  buyingFarmerCurrentStatus: {},
  buyingFarmerNextStatus: {},
  updateStatusComments: {},
}

const animalInformationFormDefinition = {
  // forceNewLineAfterControl: true,
  formControls: {
    itemListingUUID: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Item Listing Id",
      readOnly: 1,
    },
    itemUUID: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Item Id",
      readOnly: 1,
    },
    itemName: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Item Name",
      readOnly: 1,
    },
    minimumPrice: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Minimum Price",
      readOnly: 1,
    },
    maximumPrice: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Maximum Price",
      readOnly: 1,
    },
    numberOfCalvings: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "No. of Calvings",
      readOnly: 1,
    },
    numberOfMonthsPregnant: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "No. of Months Pregnamt",
      readOnly: 1,
    },
    healthScore: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Health Score",
      readOnly: 1,
    },
    bodyScore: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Body Score",
      readOnly: 1,
    },
    udderAndTeatScore: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Udder and Teat Score",
      readOnly: 1,
    },
    animalInformationTrackerLink: {
      type: "hyper-link",
      label: "Animal Information Link",
      readOnly: 1,
    }
  },
  visibleFormControls: [
    "itemListingUUID",
    "itemUUID",
    "itemName",
    "minimumPrice", 
    "maximumPrice",
    "numberOfCalvings",
    "numberOfMonthsPregnant",
    "healthScore",
    "bodyScore",
    "udderAndTeatScore",
    "animalInformationTrackerLink"
  ],
  elementToDataMapping: {
    itemListingUUID: "oc_item_listing_uuid",
    itemUUID: "oc_item_uuid",
    itemName: "item_name",
    minimumPrice: "minimum_price", 
    maximumPrice: "maximum_price",
    numberOfCalvings: "number_of_calvings",
    numberOfMonthsPregnant: "number_of_months_pregnant",
    healthScore: "health_score",
    bodyScore: "body_score",
    udderAndTeatScore: "udder_and_teat_score",
    animalInformationTrackerLink: "animal_information_tracker_link"
  },
  dataToElementMapping: {
    oc_item_listing_uuid: "itemListingUUID",
    oc_item_uuid: "itemUUID",
    item_name: "itemName",
    minimum_price: "minimumPrice", 
    maximum_price: "maximumPrice",
    number_of_calvings: "numberOfCalvings",
    number_of_months_pregnant: "numberOfMonthsPregnant",
    health_score: "healthScore",
    body_score: "bodyScore",
    udder_and_teat_score: "udderAndTeatScore",
    animal_information_tracker_link: "animalInformationTrackerLink"
  },
}

const animalInformationFormInitialState = {
  itemListingUUID: {},
  itemUUID: {},
  itemName: {},
  minimumPrice: {},
  maximumPrice: {},
  numberOfCalvings: {},
  numberOfMonthsPregnant: {},
  healthScore: {},
  bodyScore: {},
  udderAndTeatScore: {},
  animalInformationTrackerLink: {}
}

const itemBuyerCommentsModalDialogInitialConfiguration = {
  title: 'Item Buyer Comments',
  note_query_configuration: {
    note_type_id: [
      **********,
      **********
    ],
    entity_1_type_id: **********,
    entity_2_type_id: **********
  }
}

const PotentiallyBuyerFarmersForAnimal = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    tableConfiguration.enableTableExport = true
    tableConfiguration.enableTableExportFromCloud = true
  }

  const [reportData, setReportData] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const tableRef = useRef()

  const [snoozeModalDialogVisibility, setSnoozeModalDialogVisibility] = useState(false)
  const [updateStatusModalDialogVisibility, setUpdateStatusModalDialogVisibility] = useState(false)
  const [snoozeDialogFormState, setSnoozeDialogFormState] = useState(snoozeDialogFormInitialState);
  const [updateStatusDialogFormState, setUpdateStatusDialogFormState] = useState(updateStatusDialogFormInitialState);
  const [animalInformationFormState, setAnimalInformationFormState] = useState(animalInformationFormInitialState);
  const [animalId, setAnimalId] = useState(null);

  const loadInitialDataAsync = async () => {
    const itemInformation = await loadItemInformation(userToken, ocItemListingUUID)
    console.log('PBFFA uE 1, itemInformation = ', itemInformation)
    assignDataToFormState(animalInformationFormDefinition, itemInformation.result, animalInformationFormState, setAnimalInformationFormState)
    setAnimalId(itemInformation.result.linked_entity_uuid)
  }

  const postInitialLoadCB = () => {
    if (ocItemListingUUID) {
      loadInitialDataAsync()
    }
    assignDataToFormState(
      snoozeDialogFormDefinition,
      {
        farmer_level_snooze_choice_list: [{reference_name:'Yes', reference_id: 1000105001}, {reference_name:'No', reference_id: 1000105002}],
        // farmer_level_snooze_choice_list: [{label:'Yes', id: 1}, {label:'No', id: 2}],
      },
      snoozeDialogFormState,
      setSnoozeDialogFormState
    )
    assignDataToFormState(
      updateStatusDialogFormDefinition,
      {
        buying_farmer_next_status_list: [
          // {reference_name_l10n:{ul:'Farmer Contacted, Follow up Call Required'},reference_id: 1000490001},
          // {reference_name_l10n:{ul:'Farmer Contacted, Detailed Process Explanation Required'},reference_id: 1000490002},
          // {reference_name_l10n:{ul:'Process Explained, Deposit Not Collected'},reference_id: 1000490003},
          // {reference_name_l10n:{ul:'Deposit Collected, Latest Photos Not Collected'},reference_id: 1000490004},
          // {reference_name_l10n:{ul:'Photos Collected, Other Process Not Completed'},reference_id: 1000490005},
          // {reference_name_l10n:{ul:'Animal Put Up For Sale'},reference_id: 1000490006},
          {reference_name_l10n:'Not Spoken To',reference_id: **********},
          {reference_name_l10n:'Farmer Contacted, Follow up Call Required',reference_id: **********},
          {reference_name_l10n:'Farmer Contacted, Detailed Process Explanation Required',reference_id: **********},
          /* {reference_name_l10n:'Process Explained, Deposit Not Collected',reference_id: 1000710004},
          {reference_name_l10n:'Deposit Collected, Visit Not Arranged',reference_id: 1000710005},
          {reference_name_l10n:'Visit Arranged, Not Finalized',reference_id: 1000710006},
          {reference_name_l10n:'Farmer Purchased Animal',reference_id: 1000710007},
          {reference_name_l10n:'Farmer Abandoned Purchase',reference_id: 1000710008},
          {reference_name_l10n:'Farmer Provided Token Deposit',reference_id: 1000710009},
          {reference_name_l10n:'Farmer Forfeited Token Deposit',reference_id: 1000710010}, */
      ],
        // farmer_level_snooze_choice_list: [{label:'Yes', id: 1}, {label:'No', id: 2}],
      },
      updateStatusDialogFormState,
      setUpdateStatusDialogFormState
    )
  }

  const loadInitialFilterDataCB = (additionalParams) => {
    return loadFilters(userToken, additionalParams)
  }

  const loadReportCB = (reportQueryParams, additionalParams) => {
    return loadReport(userToken, reportQueryParams, additionalParams)
  }

  const ocItemListingUUID = (pageParams && pageParams.filterParams && Array.isArray(pageParams.filterParams.oc_item_listing_uuid)) ? pageParams.filterParams.oc_item_listing_uuid[0] : undefined

  const handleSnoozeClick = () => {
    // this is one at a time
    // update state for animal id, ear tag, seller farmer name, seller farmer mobile
    console.log('PBFFA hSC 1, selectedRowKeys = ', selectedRowKeys)
    const selectedRow = reportData.filter((object) => {
      return (object['customer_id'] === selectedRowKeys[0])
    })
    console.log('PBFFA hSC 2, selectedRow = ', selectedRow)
    assignDataToFormState(
      snoozeDialogFormDefinition,
      selectedRow[0],
      snoozeDialogFormState,
      setSnoozeDialogFormState
    )

    // launch popup
    // based on krushalocform
    //    pick up date
    //    pick up if override is at farmer level
    //    pick up comments
    // on save, callback
    // call api
    console.log('PSAWL hSC 1')
    // setSnoozeDialogFormState(snoozeDialogFormInitialState)
    setSnoozeModalDialogVisibility(true)
  }

  const onSnoozeModalOK = async () => {
    try {
      /* const [currentFormState, validationFailed] = validateFormElements(
        snoozeDialogFormDefinition,
        snoozeDialogFormState,
        setSnoozeDialogFormState
      ) */
      const [currentFormState, validationFailed] = validateFormElements2(
        snoozeDialogFormDefinition,
        snoozeDialogFormState,
        setSnoozeDialogFormState
      )
      // console.log("AF1 sF 2, currentFormStateTab0 = ", currentFormStateTab0);
      if (validationFailed) {
        // do something
        console.log("Validation Failed");
        ToastersService.failureToast("Validation failed!");
      } else {
        console.log("you can save now")
        const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(snoozeDialogFormDefinition, currentFormState, setSnoozeDialogFormState)
        const extractedValues = extractChangedValues(snoozeDialogFormDefinition, updatedCurrentFormState, ['farmerId'])
        const buyerFarmerWithAnimalAndSnoozeInformation = {...extractedValues, oc_item_listing_uuid: ocItemListingUUID}
        console.log("PBFFA sF 1, buyerFarmerWithAnimalAndSnoozeInformation = ", buyerFarmerWithAnimalAndSnoozeInformation)
        const headers = {
          useCase: 'Snooze for Buyer Farmers',
          token: userToken.accessToken, //authToken
          refreshtoken: localStorage.getItem("refreshToken")
        }
        // configureParams(extractedValues, navigate,reason)
        // callSnoozeAPI
        const responseWithData = await snoozeForBuyerFarmers(headers, buyerFarmerWithAnimalAndSnoozeInformation)
        const response = responseWithData.data
        console.log('PBFFA sF 2, response = ', response)
        if (response.return_code === 0) {
          setSnoozeModalDialogVisibility(false)
          ToastersService.successToast("Snoozed Successfully")
          // await reloadDataAndMoveToPageOne(tableConfiguration)
          if (tableRef.current) {
            tableRef.current.reloadDataAndMoveToPageOne()
          }
  
        } else {
          ToastersService.failureToast("Could not snooze")
        }
      }
    } catch (error) {
      console.log('PBFFA sF 10, error')
      console.log('PBFFA sF 10a, error = ', error)
      ToastersService.failureToast("Could not snooze")
    }
  }

  const onSnoozeModalCancel = () => {
    setSnoozeModalDialogVisibility(false)
  }

  const isNewBuyerFarmerStatusAcceptableFromPrevious = (extractedValues) => {
    const newStatusId = extractedValues.buying_farmer_next_status[0]
    let currentStatusId = extractedValues.buying_farmer_current_status_id
    let acceptableStatus = false
    if (currentStatusId) {
      if (typeof currentStatusId === 'string' || currentStatusId instanceof String) {
        currentStatusId = parseInt(currentStatusId)
      }
      switch (currentStatusId) {
        case **********:
          if ([**********, **********].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case **********:
          if ([**********].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case **********:
          if ([1000710004].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000710004:
          if ([1000710005].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000710005:
          if ([1000710006].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000710006:
          if ([1000710007].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
      }
    } else {
      if ([**********, **********].includes(newStatusId)) {
        acceptableStatus = true
      }
    }
    return acceptableStatus
  }

  const onUpdateStatusModalOK = async () => {
    try {
      /* const [currentFormState, validationFailed] = validateFormElements(
        updateStatusDialogFormDefinition,
        updateStatusDialogFormState,
        setUpdateStatusDialogFormState
      ) */
      const [currentFormState, validationFailed] = validateFormElements2(
        updateStatusDialogFormDefinition,
        updateStatusDialogFormState,
        setUpdateStatusDialogFormState
      )
      // console.log("AF1 sF 2, currentFormStateTab0 = ", currentFormStateTab0);
      if (validationFailed) {
        // do something
        console.log("Validation Failed");
        ToastersService.failureToast("Validation failed!");
      } else {
        const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(updateStatusDialogFormDefinition, currentFormState, setUpdateStatusDialogFormState)
        const extractedValues = extractChangedValues(updateStatusDialogFormDefinition, updatedCurrentFormState, ['farmerId'])
        console.log("AF1 sF 1, extractedValues = ", extractedValues)
        const acceptableStatus = isNewBuyerFarmerStatusAcceptableFromPrevious(extractedValues)
        
        if (!acceptableStatus) {
          ToastersService.failureToast("Not an acceptable status")
        } else {
          // update sellable status
          const selectedRow = reportData.filter((object) => {
            return (object['customer_id'] === selectedRowKeys[0])
          })
          const farmerStatusUpdateInformation = {...extractedValues, oc_item_listing_uuid: ocItemListingUUID}
          const headers = {
            useCase: 'Update Buyer Farmer Status',
            token: userToken.accessToken, //authToken
            refreshtoken: localStorage.getItem("refreshToken")
          }
          // configureParams(extractedValues, navigate,reason)
          // callSnoozeAPI
          const responseWithData = await updateBuyerFarmerStatus(headers, farmerStatusUpdateInformation)
          const response = responseWithData.data
          console.log('AF1 sF 2, response = ', response)
          if (response.return_code === 0) {
            setUpdateStatusModalDialogVisibility(false)
            ToastersService.successToast("Status Updated Successfully")
            // await reloadDataAndMoveToPageOne(tableConfiguration)
            if (tableRef.current) {
              tableRef.current.reloadDataAndMoveToPageOne()
            }
  
          } else {
            ToastersService.failureToast("Could not update status of animal")
          }
        }
      }
    } catch (error) {
      console.log('AF1 sF 10, error')
      console.log('AF1 sF 10a, error = ', error)
      ToastersService.failureToast("Could not snooze")
    }
  }

  const onUpdateStatusModalCancel = () => {
    setUpdateStatusModalDialogVisibility(false)
  }

  const enableButton = (selectedRowKeys, reportData, buttonType) => {
    let returnValue = true
    if (selectedRowKeys.length === 1) {
      const selectedRow = reportData.filter((object) => {
        return (object['customer_id'] === selectedRowKeys[0])
      })
      if (selectedRow.length > 0) {
        returnValue = false
      }
    }
    return returnValue
  }

  const handleUpdateStatusOfCustomerClick = () => {
    // this is one at a time
    // update state for animal id, ear tag, seller farmer name, seller farmer mobile
    console.log('PBFFA hUS 1, selectedRowKeys = ', selectedRowKeys)
    const selectedRow = reportData.filter((object) => {
      return (object['customer_id'] === selectedRowKeys[0])
    })
    console.log('PBFFA hUS 2, selectedRow = ', selectedRow)
    assignDataToFormState(
      updateStatusDialogFormDefinition,
      selectedRow[0],
      updateStatusDialogFormState,
      setUpdateStatusDialogFormState
    )

    // launch popup
    // based on krushalocform
    //    pick up status
    //    pick up comments
    // on save, callback
    // call api
    console.log('PBFFA hSC 1')
    setUpdateStatusModalDialogVisibility(true)
  }

  const [itemBuyerCommentsModalDialogConfiguration, setItemBuyerCommentsModalDialogConfiguration] = useState(itemBuyerCommentsModalDialogInitialConfiguration)
  const handleViewItemBuyerCommentsClick = () => {
    const updatedConfiguration = {
      ...itemBuyerCommentsModalDialogConfiguration,
      note_query_configuration: {
        ...itemBuyerCommentsModalDialogConfiguration.note_query_configuration,
        entity_1_uuid: ocItemListingUUID,
        entity_2_uuid: selectedRowKeys[0]
      }
    }
    setItemBuyerCommentsModalDialogConfiguration(updatedConfiguration)
  }

  /* const backToQueueListLinkUIAndSnoozeFarmerViewBuyerCommentsButtons = () => {
    return (
      <>
        <Link to="/dashboard/oc">Back to Queue List</Link>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'SNOOZE')} onClick={handleSnoozeClick}>Snooze</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateStatusOfCustomerClick}>Update Status Of Farmer</Button>
        <CommentViewerModalButton
          buttonTitle="View Item Buyer Comments"
          disabled={enableButton(selectedRowKeys, reportData, 'COMMENTS')}
          onClickHandler={handleViewItemBuyerCommentsClick}
          configuration={itemBuyerCommentsModalDialogConfiguration}
          />
      </>
    )
  } */

  /* const backToQueueListLinkUI = () => {
    return (
      <Link to="/dashboard/oc">Back to Queue List</Link>
    )
  }

  const snoozeFarmerViewBuyerCommentsButtons = () => {
    return (
      <>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'SNOOZE')} onClick={handleSnoozeClick}>Snooze</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateStatusOfCustomerClick}>Update Status Of Farmer</Button>
        <CommentViewerModalButton
          buttonTitle="View Item Buyer Comments"
          disabled={enableButton(selectedRowKeys, reportData, 'COMMENTS')}
          onClickHandler={handleViewItemBuyerCommentsClick}
          configuration={itemBuyerCommentsModalDialogConfiguration}
          />
      </>
    )
  } */

  return (
    <>
      <Link to="/oc">Back to Queue List</Link>
      {ocItemListingUUID ? (
        <>
          <div>
            <KrushalOCForm
              formState={animalInformationFormState}
              setFormState={setAnimalInformationFormState}
              formDefinition={animalInformationFormDefinition}
            />
            <MediaCarousel
              key='pbffa-aa'
              mediaConfiguration={
                {
                  completeInformation: (animalId === undefined) ? false : true,
                  showLatestOnlyFlag: true,
                  enableLatestOnlyFlag: true,
                  latestOnlyFlag: false,
                  enablePreview: true,
                  enableUploadOfFiles: true,
                  uploadFolder: 'ah/animal',
                  // uploadEntity: 'document',
                  uploadEntity: 'document',
                  styleContainer: {
                    mediaCardStyle: { display: 'inline-block', padding: '0px', margin: '0px', width: '225px', height: 'auto' },
                    mediaCardBodyStyle: {padding: '12px'},
                    mediaTagWrapperDivStyle: {position: 'relative', width: 'auto'},
                    mediaTagWrapperOnHoverDivStyle: undefined,
                    mediaTagPreviewOverlayStyle: undefined,
                    mediaTagPreviewOverlayOnHoverStyle: undefined,
                    mediaTagStyle: { maxWidth: '200px', height: 'auto', position: 'relative'},
                  },
                  uploadEntityInformation: {
                    entity_1_type_id: **********,
                    entity_1_entity_uuid: ocItemListingUUID,
                  },
                  uploadEntityDocumentInformation: {
                    entityType: 'item',
                    localFolderName: 'item',
                    serverFolderName: 'item',
                    individualLocalFolderName: 'item',
                    individualServerFolderName: 'item'
                  },
                  documentTypesForFilter: [
                    {label:'Animal Thumbnail Photo', value:1000260001},
                    {label:'Animal Photo Any Angle', value:1000260002},
                    {label:'Animal Eartag Photo', value:1000260012},
                    {label:'Foreign Body Area', value:1000260013},
                    {label:'Animal classification documents', value:1000260016},
                  ],
                  documentTypesForUpload: [
                    {label:'Animal Thumbnail Photo', value:1000260001},
                    {label:'Animal classification documents', value:1000260016},
                  ],
                  document_query_information : {
                    entity_name: 'farmer'
                  },
                  base_entity_information: {
                    entity_1_type_id: **********,
                    entity_uuid: ocItemListingUUID
                  }
                }
              }
            />
            {/* <MediaCarousel2 
              mediaCallback={standardMediaCallback}
              mediaConfiguration={
                {
                  completeInformation: (animalId === undefined) ? false : true,
                  showLatestOnlyFlag: true,
                  enableLatestOnlyFlag: true,
                  latestOnlyFlag: false,
                  enableUploadOfFiles: true,
                  uploadFolder: 'ah/animal',
                  // uploadEntity: 'document',
                  uploadEntity: 'document',
                  uploadEntityInformation: {
                    entity_1_type_id: **********,
                    entity_1_entity_uuid: ocItemListingUUID,
                  },
                  uploadEntityDocumentInformation: {
                      entityType: 'item',
                      localFolderName: 'item',
                      serverFolderName: 'item',
                      individualLocalFolderName: 'item',
                      individualServerFolderName: 'item'
                  },
                  documentTypesForFilter: [
                    {label:'Animal Thumbnail Photo', value:1000260001},
                    {label:'Animal Photo Any Angle', value:1000260002},
                    {label:'Animal Eartag Photo', value:1000260012},
                    {label:'Foreign Body Area', value:1000260013},
                    {label:'Animal classification documents', value:1000260016},
                  ],
                  documentTypesForUpload: [
                    {label:'Animal Thumbnail Photo', value:1000260001},
                    {label:'Animal classification documents', value:1000260016},
                  ],
                  // document_query_table_information : {
                  //   entity_name: 'document'
                  // },
                  // base_entity_information: {
                  //   entity_1_type_id: 1000220002,
                  //   entity_1_entity_uuid: animalId
                  // }
                  document_query_table_information : {
                    entity_name: 'document'
                  },
                  base_entity_information: {
                    entity_1_type_id: **********,
                    entity_1_entity_uuid: ocItemListingUUID
                  }

                }
              }
            /> */}
          </div>
          <Modal
            title="Snooze Animal"
            open={snoozeModalDialogVisibility}
            onOk={onSnoozeModalOK}
            onCancel={onSnoozeModalCancel}
            >
              <>
                <KrushalOCForm
                  formState={snoozeDialogFormState}
                  setFormState={setSnoozeDialogFormState}
                  formDefinition={snoozeDialogFormDefinition}
                />
              </>
          </Modal>
          <Modal
            title="Update Status"
            open={updateStatusModalDialogVisibility}
            onOk={onUpdateStatusModalOK}
            onCancel={onUpdateStatusModalCancel}
            >
              <>
                <KrushalOCForm
                  formState={updateStatusDialogFormState}
                  setFormState={setUpdateStatusDialogFormState}
                  formDefinition={updateStatusDialogFormDefinition}
                />
              </>
          </Modal>
          <Space style={{marginTop:'8px', marginBottom:'8px'}}>
            <Link to="/oc">Back to Queue List</Link>
            <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'SNOOZE')} onClick={handleSnoozeClick}>Snooze</Button>
            <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateStatusOfCustomerClick}>Update Status Of Farmer</Button>
            <CommentViewerModalButton
              buttonTitle="View Item Buyer Comments"
              disabled={enableButton(selectedRowKeys, reportData, 'COMMENTS')}
              onClickHandler={handleViewItemBuyerCommentsClick}
              configuration={itemBuyerCommentsModalDialogConfiguration}
              />
          </Space>
          <OCTable
            ref={tableRef}
            reportData={reportData}
            setReportData={setReportData}
            tableConfiguration={tableConfiguration}
            selectedRowKeys={selectedRowKeys}
            setSelectedRowKeys={setSelectedRowKeys}
            postInitialLoadCallback={postInitialLoadCB}
            loadReportCallback={loadReportCB}
            loadInitialFilterDataCallback={loadInitialFilterDataCB}
            parentPageParams={pageParams}
            // showTableHeader={true}
            // omniSearchRowEnabled={true}
            // omniSearchRowBeginning={backToQueueListLinkUI}
            // omniSearchRowMiddle={snoozeFarmerViewBuyerCommentsButtons}
            // actionsRow={backToQueueListLinkUIAndSnoozeFarmerViewBuyerCommentsButtons}
          />
        </>
      ) : (
        <div>Item Listing Id was not passed. Cannot display this page</div>
      )}
    </>
  );
}

// export default PotentiallyBuyerFarmersForAnimal
export default withAuthorization(PotentiallyBuyerFarmersForAnimal, [])