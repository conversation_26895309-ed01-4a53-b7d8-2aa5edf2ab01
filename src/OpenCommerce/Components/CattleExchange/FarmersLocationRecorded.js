import { Check<PERSON>, Space, Button } from "antd"
import { ReloadOutlined } from '@ant-design/icons';

import { useState, useRef } from "react"
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import {useSelector} from "react-redux"

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../../Components/user/authorize')

const { getFarmerDataReport, /* getFarmersWithLocationRecordedReport,  */getFarmerDataReportsPageLevelFilterData/* , getFarmersWithLocationRecordedReportFilterData */ } = require('../../../router')

const { delayInMillis, createPageParamsFromURLString, createURLQueryStringFromPageParams, OCTable } = require('../../../Components/Common/AntTableHelper')

const loadReport = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Farmers With Location Recorded Report',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // await delayInMillis(10000)
    // const response = await getFarmersWithLocationRecordedReport(headers, queryParams)
    const response = await getFarmerDataReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadFilters = async (userToken, selectedColumns, additionalParams) => {
  try {
    const headers = {
      useCase: 'Get Farmers With Not Location Recorded Report Filter Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getFarmerDataReportsPageLevelFilterData(headers, {selectedColumns: selectedColumns, searchConfiguration: tableConfiguration})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const tableConfiguration = {
  tableNavigationUrl: '../oc/farmers-location-recorded',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'customer_id',
  columns: [
    {
      title: "Farmer Id",
      key: "customer_id",
    },
    {
      title: "Farmer Name",
      key: "customer_name",
    },
    {
      title: "Farmer Mobile Number",
      key: "mobile_number",
    },
    {
      title: "No. of Adult Cows",
      key: "cow_count",
    },
    {
      title: "Location Updated Timestamp",
      key: "location_updated_timestamp",
    },
    {
      title: "Herd Last Updated At",
      key: "herd_last_updated_timestamp",
    },
    {
      title: "Taluk",
      key: "taluk",
    },
    {
      title: "Village",
      key: "village",
    },
    {
      title: "Farmer Count in Village",
      key: "farmer_count_in_village",
    },
    {
      title: "Farmer Count in Village With Location Not Recorded",
      key: "farmer_count_in_village_with_no_location",
    },
    {
      title: "BDM",
      key: "bdm",
    },
    {
      title: "BDM Mobile",
      key: "bdm_mobile",
    },
    {
      title: "Paravet",
      key: "paravet",
    },
    {
      title: "Paravet Mobile",
      key: "paravet_mobile",
    }
  ],
  columnConfiguration: {
    customer_name: {
      headingText: 'Customer Name',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    mobile_number: {
      headingText: 'Mobile Number',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    location_updated_timestamp: {
      headingText: 'Location Updated Timestsamp',
      enableFilter: true,
      type: 'dayjs_range',
      formatType: 'date',
      format: 'h:mm a, DD-MMM-YYYY',
      enableSort: true,
      sortColumnType: 'date',
      defaultSort: 'descend'
    },
    taluk: {
      headingText: 'Taluk',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      altColumn: 'taluk_id',
      updateFilterDataDuringFilterCall: true,
    },
    village: {
      headingText: 'Village',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      updateFilterDataDuringReportCall: true,
      // defaultFilteredValue: ['Shelgaon'],
    },
    bdm: {
      headingText: 'BDM',
      enableFilter: true,
      type: 'single_select_array',
      dataType: 'string',
      enableSort: true,
      sortColumnType: 'string',
      altColumn: 'bdm_uuid',
      updateFilterDataDuringFilterCall: true,
      filterValuesKey: 'staff_filter_values'
    },
    herd_last_updated_timestamp: {
      headingText: 'Herd Last Updated Time',
      enableFilter: true,
      type: 'dayjs_range',
      enableSort: true,
      formatType: 'date',
      format: 'h:mm a, DD-MMM-YYYY',
      sortColumnType: 'date',
      // defaultSort: 'ascend'
    },
    farmer_count_in_village: {
      headingText: 'Farmer Count In Village',
      enableFilter: true,
      type: 'number',
    },
    cow_count: {
      headingText: 'Number of Adult Cows',
      enableFilter: true,
      type: 'number_range',
      // defaultFilteredValue: [[1,5]],
    },
    farmer_count_in_village_with_no_location: {
      headingText: 'Farmer Count In Village With No Location',
      enableFilter: true,
      type: 'number',
      enableSort: true,
      sortColumnType: 'int',
    }
  }
}

const FarmersLocationRecorded = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const tableRef = useRef()

  const [excludeBDMInQuery, setExcludeBDMInQuery] = useState(true)

  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    tableConfiguration.enableTableExport = true
    tableConfiguration.enableTableExportFromCloud = true
  }

  const [reportData, setReportData] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const loadInitialFilterDataCB = (additionalParams) => {
    const selectedColumns = tableRef.current.getSelectedColumnsInTable()
    return loadFilters(userToken, selectedColumns, additionalParams)
  }

  const loadReportCB = (reportQueryParams, additionalParams) => {
    if (excludeBDMInQuery) {
      reportQueryParams.excludeBDM = true
    }
    return loadReport(userToken, reportQueryParams, additionalParams)
  }

  const handleViewAnimalsClick = () => {
    const filterParams = { 
      customer_id: selectedRowKeys,
      // health_score: [ [4, 5] ] 
    }; // Example query parameters
    const sortParams = { 
      // columnKey: 'bdm',
      // order: 'ascend'
    }; // Example query parameters
    const urlQueryString = createURLQueryStringFromPageParams(undefined, filterParams, sortParams)
    console.log('FLR hVAC 1, urlQueryString = ', urlQueryString)
    navigate({
      pathname: '/oc/data-collection-of-potentially-sellable-animals',
      search: `?${urlQueryString}`
    })
  }

  /* const backToQueueListLinkUIAndViewRelatedAnimalButtons = () => {
    return (
      <>
        <Link to="/dashboard/oc">Back to Queue List</Link>
        <Button size="small" style={{ marginLeft: 8 }} disabled={(selectedRowKeys.length === 0)} onClick={handleViewAnimalsClick}>View Related Animals</Button>
      </>
    )
  } */

  return (
    <>
      <Space>
        <Link to="/oc">Back to Queue List</Link>
        <Button size="small" style={{ marginLeft: 8 }} disabled={(selectedRowKeys.length === 0)} onClick={handleViewAnimalsClick}>View Related Animals</Button>
        <Checkbox
          checked={excludeBDMInQuery}
          onChange={() => {
            setExcludeBDMInQuery(!excludeBDMInQuery)
          }}
          >
            Exclude BDM From Query
        </Checkbox>
        <ReloadOutlined onClick={() => {
          if (tableRef.current) {
            tableRef.current.reloadDataAndMoveToPageOne()
          }
        }}/>
      </Space>
      <OCTable
        ref={tableRef}
        /* tableKey={tableKey}
        setTableKey={setTableKey} */
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={tableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        // postInitialLoadCallback={postInitialLoadCB}
        loadReportCallback={loadReportCB}
        loadInitialFilterDataCallback={loadInitialFilterDataCB}
        // parentPageParams={pageParams}
        // showTableHeader={true}
        // omniSearchRowEnabled={true}
        // omniSearchRowBeginning={backToQueueListLinkUI}
        // omniSearchRowMiddle={viewRelatedAnimalButtons}
        // actionsRow={backToQueueListLinkUIAndViewRelatedAnimalButtons}
      />
    </>
  );
}

// export default FarmersLocationRecorded
export default withAuthorization(FarmersLocationRecorded, [])