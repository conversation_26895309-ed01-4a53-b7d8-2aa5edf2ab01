import { Space, Button, Modal } from "antd";

import { useState, useRef } from "react"
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import {useSelector} from "react-redux"
import ToastersService from "../../../Services/toasters.service";

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  extractChangedValues,
} from "../../../Components/Common/KrushalOCForm";

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../../Components/user/authorize')

const { getAnimalExchangeOrdersReport, getAnimalExchangeOrdersReportFilterData, updateOrderFarmerStatus } = require('../../../router')

const { OCTable, createPageParamsFromURLString } = require('../../../Components/Common/AntTableHelper')

const loadReport = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Farmers Who May Want To Buy Animals Report',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getAnimalExchangeOrdersReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadFilters = async (userToken) => {
  try {
    const headers = {
      useCase: 'Get Farmers Who May Want To Buy Animals Report Filter Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getAnimalExchangeOrdersReportFilterData(headers)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const tableConfiguration = {
  tableNavigationUrl: '../oc/animal-exchange-orders',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'oc_order_uuid',
  columns: [
    {
      title: 'Order Id',
      dataIndex: 'oc_order_uuid'
    },
    {
      title: 'Order Type',
      dataIndex: 'order_type'
    },
    {
      title: "Tracker Link",
      dataIndex: "animal_information_tracker_link"
    },
    {
      title: "Farmer Id",
      dataIndex: "customer_id",
    },
    {
      title: 'Farmer Order Current Status Id',
      dataIndex: 'farmer_order_current_status_id',
    },
    {
      title: 'Farmer Order Current Status',
      dataIndex: 'farmer_order_current_status',
    },
    {
      title: "Farmer Name",
      dataIndex: "customer_name",
    },
    {
      title: "Farmer Mobile Number",
      dataIndex: "mobile_number",
    },
    {
      title: "No. of Adult Animals",
      dataIndex: "number_of_adult_animals",
    },
    {
      title: "Type of Dairy Farm",
      dataIndex: "type_of_dairy_farm",
    },
    {
      title: "Taluk",
      dataIndex: "taluk_name",
    },
    {
      title: "Village",
      dataIndex: "village_name",
    },
    {
      title: "With Krushal Since",
      dataIndex: "with_krushal_since",
    },
    {
      title: "Distance In Km From Seller Farmer",
      dataIndex: "distance_from_seller_farmer",
    },/*
    {
      title: "BDM",
      dataIndex: "bdm",
    },
    {
      title: "BDM Mobile",
      dataIndex: "bdm_mobile",
    },*/
    {
      title: 'Animal Id',
      dataIndex: 'animal_id',
    },
    {
      title: 'No of Calvings',
      dataIndex: 'number_of_calvings',
    },
    {
      title: 'No of Months Pregnant',
      dataIndex: 'number_of_months_pregnant',
    },
    {
      title: 'Health Score',
      dataIndex: 'health_score',
    },
    {
      title: 'Body Score',
      dataIndex: 'body_score',
    },
    {
      title: 'Udder and Teat Score',
      dataIndex: 'udder_and_teat_score',
    }
  ],
  columnConfiguration: {
    animal_information_tracker_link: {
      headingText: 'Tracker Link',
      formatType: 'hyperLink',
    },
    with_krushal_since: {
      formatType: 'date',
      format: 'MMM-YYYY',
    },
    animal_id: {
      headingText: "Animal Id",
      allowFilterInPageParams: true,
    }
/*     snooze_status: {
      headingText: 'Snooze Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'Snoozed', value: 'Snoozed' },
        { text: 'Not Snoozed', value: 'Not Snoozed' },
      ],
      defaultFilteredValue: ['Not Snoozed'],
      enableSort: true,
      sortColumnType: 'string',
    },
    buying_farmer_current_status: {
      headingText: 'Buying Farmer Current Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      filters: [
        { text: 'Not Spoken To', value: 1000710000 },
        { text: 'Farmer Contacted, Follow up Call Required', value: 1000710001 },
        { text: 'Farmer Contacted, Detailed Process Explanation Required', value: 1000710002 },
        { text: 'Process Explained, Deposit Not Collected', value: 1000710003 },
        { text: 'Deposit Collected, Visit Not Collected', value: 1000710004 },
        { text: 'Visit Arranged, Not Finalized', value: 1000710005 },
        { text: 'Farmer Purchased Animal', value: 1000710006 },
      ],
      defaultFilteredValue: [1000710000],
      altColumn: 'buying_farmer_current_status_id'
    },
    customer_id: {
      headingText: 'Customer Id',
      allowFilterInPageParams: true,
      paramSearchType: 'array',
      paramSearchDataType: 'string'
    },
    animal_ear_tag: {
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    customer_name: {
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    mobile_number: {
      type: 'string',
      includeInOmnisearch: true
    },
    taluk_name: {
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      // dataType: 'int',
      // altColumn: 'taluk_id'
    },
    bdm: {
      headingText: 'BDM',
      enableSort: true,
      sortColumnType: 'string',
    },
    with_krushal_from_date: {
      headingText: 'With Krushal From Date',
      formatType: 'date',
      format: 'MMM-YYYY',
      enableSort: true,
      sortColumnType: 'date',
      defaultSort: 'ascend'
    },
 */  }
}

const updateStatusDialogFormDefinition = {
  forceNewLineAfterControl: true,
  defaultClasses: 'mb-3',
  formControls: {
    ocOrderUUID: {
      type: "text",
      label: "Order ID",
      readOnly: 1,
    },
    farmerId: {
      type: "text",
      label: "Farmer Id",
      readOnly: 1,
    },
    farmerName: {
      type: "text",
      label: "Farmer Name",
      readOnly: 1,
    },
    farmerMobile: {
      type: "text",
      label: "Farmer Mobile",
      readOnly: 1,
    },
    farmerOrderCurrentStatusId: {
      type: "text",
      label: "Current Farmer Order Status Id",
      readOnly: 1,
    },
    farmerOrderCurrentStatus: {
      type: "text",
      label: "Current Farmer Order Status",
      readOnly: 1,
    },
    farmerOrderNextStatusId: {
      type: "single-select-l10n",
      label: "Farmer Order Process Next Status",
      errorMessage: "Choosing the status the farmer needs to be updated to",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Choose next status",
      validations: [{ type: "Mandatory" }],
    },
    updateStatusComments: {
      type: "text",
      label: "Comments",
      validations: [{ type: "Mandatory" }],
      placeHolderText: "Enter Any Comments",
    },
  },
  visibleFormControls: [
    "ocOrderUUID",
    "farmerId",
    "farmerName",
    "farmerMobile",
    "farmerOrderCurrentStatusId", 
    "farmerOrderCurrentStatus",
    "farmerOrderNextStatusId",
    "updateStatusComments",
  ],
  elementToDataMapping: {
    ocOrderUUID: "oc_order_uuid",
    farmerId: "customer_id",
    farmerName: "customer_name",
    farmerMobile: "mobile_number",
    farmerOrderCurrentStatusId: "farmer_order_current_status_id", 
    farmerOrderCurrentStatus: "farmer_order_current_status",
    farmerOrderNextStatusId: "farmer_order_next_status_id",
    updateStatusComments: "update_status_comments",
  },
  dataToElementMapping: {
    oc_order_uuid: "ocOrderUUID",
    customer_id: "farmerId",
    customer_name: "farmerName",
    mobile_number: "farmerMobile",
    farmer_order_current_status_id: "farmerOrderCurrentStatusId", 
    farmer_order_current_status: "farmerOrderCurrentStatus",
    farmer_order_next_status_id: "farmerOrderNextStatusId",
    update_status_comments: "updateStatusComments",
  },
}

const updateStatusDialogFormInitialState = {
  ocOrderUUID: {},
  farmerId: {},
  farmerName: {},
  farmerMobile: {},
  farmerOrderCurrentStatusId: {},
  farmerOrderCurrentStatus: {},
  farmerOrderNextStatusId: {},
  updateStatusComments: {},
}

const AnimalExchangeOrders = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    tableConfiguration.enableTableExport = true
    tableConfiguration.enableTableExportFromCloud = true
  }

  const [reportData, setReportData] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const tableRef = useRef()

  const postInitialLoadCB = () => {
    assignDataToFormState(
      updateStatusDialogFormDefinition,
      {
        farmer_order_next_status_id_list: [
          // {reference_name_l10n:{ul:'Farmer Contacted, Follow up Call Required'},reference_id: 1000490001},
          // {reference_name_l10n:{ul:'Farmer Contacted, Detailed Process Explanation Required'},reference_id: 1000490002},
          // {reference_name_l10n:{ul:'Process Explained, Deposit Not Collected'},reference_id: 1000490003},
          // {reference_name_l10n:{ul:'Deposit Collected, Latest Photos Not Collected'},reference_id: 1000490004},
          // {reference_name_l10n:{ul:'Photos Collected, Other Process Not Completed'},reference_id: 1000490005},
          // {reference_name_l10n:{ul:'Animal Put Up For Sale'},reference_id: 1000490006},
          {reference_name_l10n:'Order Created, Deposit Made, Visit to be arranged',reference_id: 1000740001},
          {reference_name_l10n:'Order Visit Arranged, Final Decision To Be Made',reference_id: 1000740002},
          {reference_name_l10n:'Order Cancelled, Money Not Refunded',reference_id: 1000740003},
          {reference_name_l10n:'Order Cancelled, Money Refunded Because Animal Rejected',reference_id: 1000740004},
          {reference_name_l10n:'Order Cancelled, Money Refunded Because Of Another Buyer Purchase',reference_id: 1000740005},
          {reference_name_l10n:'Order Progressed, Token Deposit Awaited',reference_id: 1000740006},
          {reference_name_l10n:'Order Progressed, Token Deposit Received',reference_id: 1000740007},
          {reference_name_l10n:'Order Progressed, Token Deposit Forfeited',reference_id: 1000740008},
          {reference_name_l10n:'Order Progressed, Money Received from Buyer',reference_id: 1000740009},
          {reference_name_l10n:'Order Progressed, Seller Paid',reference_id: 1000740010},
          {reference_name_l10n:'Order Progressed, Animal Collected',reference_id: 1000740011},
          {reference_name_l10n:'Order Closed',reference_id: 1000740012},
      ],
        // farmer_level_snooze_choice_list: [{label:'Yes', id: 1}, {label:'No', id: 2}],
      },
      updateStatusDialogFormState,
      setUpdateStatusDialogFormState
    )
  }

  const loadInitialFilterDataCB = (additionalParams) => {
    return loadFilters(userToken, additionalParams)
  }

  const loadReportCB = (reportQueryParams, additionalParams) => {
    return loadReport(userToken, reportQueryParams, additionalParams)
  }

  const [updateStatusModalDialogVisibility, setUpdateStatusModalDialogVisibility] = useState(false)
  const [updateStatusDialogFormState, setUpdateStatusDialogFormState] = useState(updateStatusDialogFormInitialState);
  
  const isNewBuyerFarmerStatusAcceptableFromPrevious = (extractedValues) => {
    const newStatusId = extractedValues.farmer_order_next_status_id[0]
    let currentStatusId = extractedValues.farmer_order_current_status_id
    let acceptableStatus = false
    if (currentStatusId) {
      if (typeof currentStatusId === 'string' || currentStatusId instanceof String) {
        currentStatusId = parseInt(currentStatusId)
      }
      switch (currentStatusId) {
        case 1000740001:
          if ([1000740002].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000740002:
          if ([1000740003, 1000740006, 1000740009].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000740003:
          if ([1000740004, 1000740005].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000740006:
          if ([1000740007].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000740007:
          if ([1000740008, 1000740009].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000740009:
          if ([1000740010].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000740010:
          if ([1000740011].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000740011:
          if ([1000740012].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        }
    }
    return acceptableStatus
  }

  const onUpdateStatusModalOK = async () => {
    try {
      const [currentFormState, validationFailed] = validateFormElements(
        updateStatusDialogFormDefinition,
        updateStatusDialogFormState,
        setUpdateStatusDialogFormState
      );
      // console.log("AF1 sF 2, currentFormStateTab0 = ", currentFormStateTab0);
      if (validationFailed) {
        // do something
        console.log("Validation Failed");
        ToastersService.failureToast("Validation failed!");
      } else {
        const extractedValues = extractChangedValues(updateStatusDialogFormDefinition, currentFormState, ['ocOrderUUID', 'farmerId', 'farmerOrderCurrentStatusId'])
        console.log("AEO oUSMO 1, extractedValues = ", extractedValues)
        const acceptableStatus = isNewBuyerFarmerStatusAcceptableFromPrevious(extractedValues)
        
        if (!acceptableStatus) {
          ToastersService.failureToast("Not an acceptable status")
        } else {
          // update sellable status
          const farmerStatusUpdateInformation = {...extractedValues}
          const headers = {
            useCase: 'Update Order Farmer Status',
            token: userToken.accessToken, //authToken
            refreshtoken: localStorage.getItem("refreshToken")
          }
          // configureParams(extractedValues, navigate,reason)
          // callSnoozeAPI
          const responseWithData = await updateOrderFarmerStatus(headers, farmerStatusUpdateInformation)
          const response = responseWithData.data
          console.log('AEO oUSMO 2, response = ', response)
          if (response.return_code === 0) {
            setUpdateStatusModalDialogVisibility(false)
            ToastersService.successToast("Status Updated Successfully")
            // await reloadDataAndMoveToPageOne(tableConfiguration)
            if (tableRef.current) {
              tableRef.current.reloadDataAndMoveToPageOne()
            }    
          } else {
            ToastersService.failureToast("Could not update status of order")
          }
        }
      }
    } catch (error) {
      console.log('AEO oUSMO 10, error')
      console.log('AEO oUSMO 10a, error = ', error)
      ToastersService.failureToast("Could not update status of order")
    }
  }

  const onUpdateStatusModalCancel = () => {
    setUpdateStatusModalDialogVisibility(false)
  }

  const enableButton = (selectedRowKeys, reportData, buttonType) => {
    let returnValue = true
    if (selectedRowKeys.length === 1) {
      const selectedRow = reportData.filter((object) => {
        return (object['oc_order_uuid'] === selectedRowKeys[0])
      })
      if (selectedRow.length > 0) {
        returnValue = false
      }
    }
    return returnValue
  }

  const handleUpdateStatusOfCustomerClick = () => {
    // this is one at a time
    // update state for animal id, ear tag, seller farmer name, seller farmer mobile
    console.log('FWMWTBA hUS 1, selectedRowKeys = ', selectedRowKeys)
    const selectedRow = reportData.filter((object) => {
      return (object['oc_order_uuid'] === selectedRowKeys[0])
    })
    console.log('FWMWTBA hUS 2, selectedRow = ', selectedRow)
    assignDataToFormState(
      updateStatusDialogFormDefinition,
      selectedRow[0],
      updateStatusDialogFormState,
      setUpdateStatusDialogFormState
    )

    // launch popup
    // based on krushalocform
    //    pick up status
    //    pick up comments
    // on save, callback
    // call api
    console.log('FWMWTBA hUS 3')
    setUpdateStatusModalDialogVisibility(true)
  }

  /* const backToQueueListLinkUIViewFarmerUpdateStatusButtons = () => {
    return (
      <>
        <Link to="/dashboard/oc">Back to Queue List</Link>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateStatusOfCustomerClick}>Update Status Of Farmer</Button>
      </>
    )
  } */

  /* const backToQueueListLinkUI = () => {
    return (
      <Link to="/dashboard/oc">Back to Queue List</Link>
    )
  }

  const viewFarmerUpdateStatusButtons = () => {
    return (
      <>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateStatusOfCustomerClick}>Update Status Of Farmer</Button>
      </>
    )
  } */

  return (
    <>
      <>
        <Modal
          title="Update Status"
          open={updateStatusModalDialogVisibility}
          onOk={onUpdateStatusModalOK}
          onCancel={onUpdateStatusModalCancel}
          >
            <>
              <KrushalOCForm
                formState={updateStatusDialogFormState}
                setFormState={setUpdateStatusDialogFormState}
                formDefinition={updateStatusDialogFormDefinition}
              />
            </>
        </Modal>
        <Space style={{marginTop:'8px', marginBottom:'8px'}}>
          <Link to="/oc">Back to Queue List</Link>
          <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateStatusOfCustomerClick}>Update Status Of Farmer</Button>
        </Space>
        <OCTable
          ref={tableRef}
          reportData={reportData}
          setReportData={setReportData}
          tableConfiguration={tableConfiguration}
          selectedRowKeys={selectedRowKeys}
          setSelectedRowKeys={setSelectedRowKeys}
          postInitialLoadCallback={postInitialLoadCB}
          loadReportCallback={loadReportCB}
          loadInitialFilterDataCallback={loadInitialFilterDataCB}
          parentPageParams={pageParams}
          // showTableHeader={true}
          // omniSearchRowEnabled={true}
          // omniSearchRowBeginning={backToQueueListLinkUI}
          // omniSearchRowMiddle={viewFarmerUpdateStatusButtons}
          // actionsRow={backToQueueListLinkUIViewFarmerUpdateStatusButtons}
        />
      </>
    </>
  );
}

// export default AnimalExchangeOrders
export default withAuthorization(AnimalExchangeOrders, [])