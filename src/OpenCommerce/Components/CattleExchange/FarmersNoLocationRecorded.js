import { useState, useRef } from "react"
import { <PERSON>, useNavigate, useSearchParams } from 'react-router-dom';
import { useSelector } from "react-redux"
import { Checkbox, Space } from "antd";
import { ReloadOutlined } from '@ant-design/icons';

const {
  configurationJSON
} = require("@krushal-it/common-core");

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../../Components/user/authorize')

const { getFarmerDataReport, /* getFarmersWithNoLocationRecordedReport,  */getFarmerDataReportsPageLevelFilterData, /* getFarmersWithNoLocationRecordedReportFilterData */ } = require('../../../router')

const { OCTable, createPageParamsFromURLString,  } = require('../../../Components/Common/AntTableHelper')

const loadReport = async (userToken, queryParams) => {
  try {
    console.log('SA lR 1')
    const authToken = localStorage.getItem("accessToken")
    console.log('SA lR 2, authToken = ', authToken)
    // const headers = createHeaders(-1, 'Get Complaint Report', authToken)
    const headers = {
      useCase: 'Get Farmers With No Location Recorded Report',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    console.log('SA lR 3, headers = ', headers)
    // const response = await getFarmersWithNoLocationRecordedReport(headers, queryParams)
    const response = await getFarmerDataReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadFilters = async (userToken, selectedColumns, additionalParams) => {
  try {
    const headers = {
      useCase: 'Get Farmers With Not Location Recorded Report Filter Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getFarmerDataReportsPageLevelFilterData(headers, {selectedColumns: selectedColumns, searchConfiguration: tableConfiguration})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const tableConfiguration = {
  tableNavigationUrl: '../oc/farmers-no-location-recorded',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'customer_id',
  columns: [
    {
      title: "Farmer Id",
      key: "customer_id",
    },
    {
      title: "Farmer Name",
      key: "customer_name",
    },
    {
      title: "Farmer Mobile Number",
      key: "mobile_number",
    },
    {
      title: "Taluk",
      key: "taluk",
    },
    {
      title: "Village",
      key: "village",
    },
    {
      title: "Farmer Count in Village",
      key: "farmer_count_in_village",
    },
    {
      title: "Farmer Count in Village With Location Not Recorded",
      key: "farmer_count_in_village_with_no_location",
    },
    {
      title: "BDM",
      key: "bdm",
    },
    {
      title: "BDM Mobile",
      key: "bdm_mobile",
    },
    {
      title: "Paravet",
      key: "paravet",
    },
    {
      title: "Paravet Mobile",
      key: "paravet_mobile",
    }
  ],
  columnConfiguration: {
    customer_name: {
      headingText: 'Customer Name',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    mobile_number: {
      headingText: 'Mobile Number',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    taluk: {
      headingText: 'Taluk',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      altColumn: 'taluk_id',
      updateFilterDataDuringFilterCall: true,
    },
    village: {
      headingText: 'Village',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      updateFilterDataDuringReportCall: true,
      // defaultFilteredValue: ['Shelgaon'],
    },
    bdm: {
      headingText: 'BDM',
      enableFilter: true,
      type: 'single_select_array',
      dataType: 'string',
      enableSort: true,
      sortColumnType: 'string',
      altColumn: 'bdm_uuid',
      updateFilterDataDuringFilterCall: true,
      filterValuesKey: 'staff_filter_values'
    },
    farmer_count_in_village: {
      headingText: 'Farmer Count In Village',
      enableFilter: true,
      type: 'number',
    },
    number_of_adult_animals: {
      headingText: 'Number of Adult Animals',
      enableFilter: true,
      type: 'number_range',
      // defaultFilteredValue: [[1,5]],
    },
    farmer_count_in_village_with_no_location: {
      headingText: 'Farmer Count In Village With No Location',
      enableFilter: true,
      type: 'number',
      enableSort: true,
      sortColumnType: 'int',
    }
  }
}

const FarmersNoLocationRecorded = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const tableRef = useRef()

  const [excludeBDMInQuery, setExcludeBDMInQuery] = useState(true)

  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    tableConfiguration.enableTableExport = true
    tableConfiguration.enableTableExportFromCloud = true
  }

  const [reportData, setReportData] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const loadInitialFilterDataCB = (additionalParams) => {
    const selectedColumns = tableRef.current.getSelectedColumnsInTable()
    return loadFilters(userToken, selectedColumns, additionalParams)
  }

  const loadReportCB = (reportQueryParams, additionalParams) => {
    if (excludeBDMInQuery) {
      reportQueryParams.excludeBDM = true
    }
    return loadReport(userToken, reportQueryParams, additionalParams)
  }

  return (
    <>
      <Space style={{marginTop:'8px', marginBottom:'8px'}}>
        <Link to="/oc">Back to Queue List</Link>
        <Checkbox
          checked={excludeBDMInQuery}
          onChange={() => {
            setExcludeBDMInQuery(!excludeBDMInQuery)
          }}
          >
            Exclude BDM From Query
        </Checkbox>
        <ReloadOutlined onClick={() => {
          if (tableRef.current) {
            tableRef.current.reloadDataAndMoveToPageOne()
          }
        }}/>
      </Space>
      <OCTable
        ref={tableRef}
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={tableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        // postInitialLoadCallback={postInitialLoadCB}
        loadReportCallback={loadReportCB}
        loadInitialFilterDataCallback={loadInitialFilterDataCB}
        // parentPageParams={pageParams}
        // showTableHeader={true}
        // omniSearchRowEnabled={true}
        // omniSearchRowBeginning={backToQueueListLinkUI}
        // omniSearchRowMiddle={viewAnimalDocumentsFarmerSnoozeAndFarmerUpdateStatusButtons}
      />
    </>
  );
}


// export default FarmersNoLocationRecorded
export default withAuthorization(FarmersNoLocationRecorded, [])