import { <PERSON>, Button } from "antd"

import { useState } from "react"
import { <PERSON>, useNavigate, useSearchParams } from 'react-router-dom';
import {useSelector} from "react-redux"

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../../Components/user/authorize')

const { getFarmersWithLocationRecordedReport, getFarmersWithLocationRecordedReportFilterData } = require('../../../router')

const { createPageParamsFromURLString, createURLQueryStringFromPageParams, OCTable } = require('../../../Components/Common/AntTableHelper')

const loadReport = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Farmers With Location Recorded Report',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getFarmersWithLocationRecordedReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadFilters = async (userToken) => {
  try {
    const headers = {
      useCase: 'Get Farmers With Location Recorded Report Filter Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getFarmersWithLocationRecordedReportFilterData(headers)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const tableConfiguration = {
  tableNavigationUrl: '../oc/manure-not-interested-customers',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'customer_id',
  columns: [
    {
      title: "Farmer Id",
      key: "customer_id",
    },
    {
      title: "Farmer Name",
      key: "customer_name",
    },
    {
      title: "Farmer Mobile Number",
      key: "mobile_number",
    },
    {
      title: "No. of Adult Animals",
      key: "number_of_adult_animals",
    },
    {
      title: "Location Updated Timestamp",
      key: "location_updated_timestamp",
    },
    {
      title: "Herd Last Updated At",
      key: "herd_last_updated_timestamp",
    },
    {
      title: "Taluk",
      key: "taluk",
    },
    {
      title: "Village",
      key: "village",
    },
    {
      title: "Farmer Count in Village",
      key: "farmer_count_in_village",
    },
    {
      title: "Farmer Count in Village With Location Not Recorded",
      key: "farmer_count_in_village_with_no_location",
    },
    {
      title: "BDM",
      key: "bdm",
    },
    {
      title: "BDM Mobile",
      key: "bdm_mobile",
    },
    {
      title: "Paravet",
      key: "paravet",
    },
    {
      title: "Paravet Mobile",
      key: "paravet_mobile",
    }
  ],
  columnConfiguration: {
    customer_name: {
      headingText: 'Customer Name',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    mobile_number: {
      headingText: 'Mobile Number',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    location_updated_timestamp: {
      headingText: 'Location Updated Timestsamp',
      enableFilter: true,
      type: 'dayjs_range',
      formatType: 'date',
      format: 'h:mm a, DD-MMM-YYYY',
      enableSort: true,
      sortColumnType: 'date',
      defaultSort: 'descend'
    },
    taluk: {
      headingText: 'Taluk',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      altColumn: 'taluk_id'
    },
    village: {
      headingText: 'Village',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      // defaultFilteredValue: ['Shelgaon'],
    },
    bdm: {
      headingText: 'BDM',
      enableFilter: true,
      type: 'single_select_array',
      dataType: 'string',
      enableSort: true,
      sortColumnType: 'string',
      altColumn: 'bdm_uuid',
      filterValuesKey: 'staff_filter_values'
    },
    herd_last_updated_timestamp: {
      headingText: 'Herd Last Updated Time',
      enableFilter: true,
      type: 'dayjs_range',
      enableSort: true,
      formatType: 'date',
      format: 'h:mm a, DD-MMM-YYYY',
      sortColumnType: 'date',
      // defaultSort: 'ascend'
    },
    farmer_count_in_village: {
      headingText: 'Farmer Count In Village',
      enableFilter: true,
      type: 'number',
    },
    number_of_adult_animals: {
      headingText: 'Number of Adult Animals',
      enableFilter: true,
      type: 'number_range',
      // defaultFilteredValue: [[1,5]],
    },
    farmer_count_in_village_with_no_location: {
      headingText: 'Farmer Count In Village With No Location',
      enableFilter: true,
      type: 'number',
      enableSort: true,
      sortColumnType: 'int',
    }
  }
}

const ManureOCFarmersNotInterested = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    tableConfiguration.enableTableExport = true
    tableConfiguration.enableTableExportFromCloud = true
  }

  const [reportData, setReportData] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const loadInitialFilterDataCB = (additionalParams) => {
    return loadFilters(userToken, additionalParams)
  }

  const loadReportCB = (reportQueryParams, additionalParams) => {
    return loadReport(userToken, reportQueryParams, additionalParams)
  }

  const handleViewFarmerClick = () => {
    navigate({
      pathname: '/e/farmer-details',
    })
  }

  /* const backToQueueListLinkUI = () => {
    return (
      <Link to="/dashboard/oc">Back to Queue List</Link>
    )
  }

  const viewFarmerDetailsButtons = () => {
    return (
      <>
        <Button size="small" style={{ marginLeft: 8 }} disabled={(selectedRowKeys.length === 0)} onClick={handleViewFarmerClick}>View Farmer Details</Button>
      </>
    )
  } */

  return (
    <>
      <Space style={{marginTop:'8px', marginBottom:'8px'}}>
        <Link to="/oc">Back to Queue List</Link>
        <Button size="small" style={{ marginLeft: 8 }} disabled={(selectedRowKeys.length === 0)} onClick={handleViewFarmerClick}>View Farmer Details</Button>
      </Space>
      <OCTable
        /* tableKey={tableKey}
        setTableKey={setTableKey} */
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={tableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        // postInitialLoadCallback={postInitialLoadCB}
        loadReportCallback={loadReportCB}
        loadInitialFilterDataCallback={loadInitialFilterDataCB}
        // parentPageParams={pageParams}
        // showTableHeader={true}
        // omniSearchRowEnabled={true}
        /* omniSearchRowBeginning={backToQueueListLinkUI}
        omniSearchRowMiddle={viewFarmerDetailsButtons} */
      />
    </>
  );
}

// export default ManureOCFarmersNotInterested
export default withAuthorization(ManureOCFarmersNotInterested, [])