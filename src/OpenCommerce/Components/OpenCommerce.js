import { Link } from 'react-router-dom';

import { Card, Row, Col } from 'antd'

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const OpenCommerce = () => {
  return (
    <>
    <Row>
      <Col span={3}>
        <Link to="/oc/farmer-no-location-recorded">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Farmers - Location Not Recorded
          </Card>
        </Link>
      </Col>
      <Col span={3}>
        <Link to="/oc/farmer-location-recorded">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Farmers - Location Recorded
          </Card>
        </Link>
      </Col>
      <Col span={3}>
        <Link to="/oc/data-collection-of-potentially-sellable-animals">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Data Collection of Potentially Sellable Animals
          </Card>
        </Link>
      </Col>
    </Row>
    <Row>
      <Col span={3}>
        <Link to="/oc/potentially-sellable-animals">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Potentially Sellable Animals
          </Card>
        </Link>
      </Col>
      <Col span={3}>
        <Link to="/oc/animals-that-farmers-may-want-to-sell">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Animals That Farmers May Want To Sell
          </Card>
        </Link>
      </Col>
    </Row>
    <Row>
      <Col span={3}>
        <Link to="/oc/sellable-animals">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Sellable Animals
          </Card>
        </Link>
      </Col>
      <Col span={3}>
        <Link to="/oc/farmers-who-may-want-to-buy">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Farmers Who May Want To Buy
          </Card>
        </Link>
      </Col>
      <Col span={3}>
        <Link to="/oc/animal-exchange-orders">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Animal Exchange Orders
          </Card>
        </Link>
      </Col>
    </Row>
    <Row>
      <Col span={3}>
        <Link to="/oc/manure-to-be-contacted-customers">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Farmers Not Yet Contacted Around Manure Open Commerce
          </Card>
        </Link>
      </Col>
      <Col span={3}>
        <Link to="/oc/manure-not-interested-customers">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Farmers Not Interested In Manure Open Commerce
          </Card>
        </Link>
      </Col>
      <Col span={3}>
        <Link to="/oc/manure-interested-customers">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Farmers Interested In Manure Open Commerce
          </Card>
        </Link>
      </Col>
    </Row>
  </>
  )
}

// export default OpenCommerce
export default withAuthorization(OpenCommerce, [])