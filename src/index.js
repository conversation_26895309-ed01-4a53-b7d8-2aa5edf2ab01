/**
 * index.js: Top-level include defining common logging.
 * 
 * This libraty is a wrapper to standardise logging across krushal platform. Its uses winstonlogging framework
 * For winston library details refer https://github.com/winstonjs/winston 
 *
 *  Author: <PERSON><PERSON><PERSON><PERSON>
 * CreatedOn: 03 Apr 2023
 * 
 */

const logger = require('./winston_logger/logger')

const {
    errorLog,
    warningLog,
    infoLog,
    verboseLog,
    debugLog,
} = require('./winston_logger/logging')

const { generateUUID, validateUUID } = require('./uuid')

module.exports = {
    logger,
    errorLog,
    warningLog,
    infoLog,
    verboseLog,
    debugLog,
    generateUUID,
    validateUUID
}