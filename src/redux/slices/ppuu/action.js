// import { instance } from "../../../../Services/api.service"
// import ToastersService from "../../../../Services/toasters.service";
import { instance } from "../../../Services/api.service";
import ToastersService from "../../../Services/toasters.service";
import { setWalletBalance } from "./reducers";
import { clearUserToken } from "../../../Components/user/action";

export function getWalletBalanceRedux(staffId,userToken,navigate){
    return async function(dispatch) {
        try {
            const response = await instance({
                url : `/ppu/get_transaction_ledge/${staffId}`,
                method : "GET",
                headers: {
                  "Content-Type": "application/json",
                  token: userToken.accessToken,
                }, 
              })
              if (response?.status === 200) {
               dispatch(setWalletBalance(response.data))
              }
        } catch (error) {
            if (error?.response?.status === 401) {
                dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
                ToastersService.failureToast("Session Expired!")
              }
        }
    }
}