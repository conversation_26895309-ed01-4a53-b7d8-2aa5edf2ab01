import {createSlice} from "@reduxjs/toolkit";

const ppuuSlice = createSlice({
    name : "ppuu",
    initialState : {
        walletBalance : 0,
        financeTabVisible : 0,
        freelancerMobNumber : ''
    },
    reducers : {
        setWalletBalance :  (state,action)=>{
            state.walletBalance = action.payload
        },
        setFinanceTabVisible : (state,action)=>{
            state.financeTabVisible = action.payload
        },
        setFreelancerMobNumber : (state,action)=>{
            state.freelancerMobNumber = action.payload
        }
    }
})

export const {setWalletBalance, setFinanceTabVisible, setFreelancerMobNumber} = ppuuSlice.actions;
export default ppuuSlice.reducer;
