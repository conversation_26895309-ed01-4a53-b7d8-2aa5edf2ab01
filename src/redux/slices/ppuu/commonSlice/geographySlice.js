import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { BASE_URL } from "../../../../Services/api.service";

export const geographyApi = createApi({
    reducerPath : "geographyApi",
    baseQuery : fetchBaseQuery({
        baseUrl : BASE_URL,
        prepareHeaders : (headers,{getState}) =>{
            const token = getState().user.userToken.accessToken;
            if (token) {
                headers.set("token",token)
            }
            return headers
        }
    }),
    endpoints : (builder) =>({
        getVillagesBasedName : builder.query({
            query : (searchText)=>({
                url : `v1/geography?type=village&searchtext=${searchText}`,
                method : "GET"
            })
        })
    })
})

export const {useGetVillagesBasedNameQuery} = geographyApi