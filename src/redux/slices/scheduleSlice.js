import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  displayString: "Schedule",
  startDate: null,
  startTime: null,
  frequency: {
    numberOfRepetitions: 0,
    type: "none",
    weekDaysRepetition: [],
  },
  endingCondition: {
    type: "none",
    endTime: null,
    endDate: null,
    endingOccurrencesNumber: null,
  },
  isAllDay: true,
};

const scheduleSlice = createSlice({
  name: "schedule",
  initialState,
  reducers: {
    updateSchedule: (state, action) => {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
});

export const { updateSchedule } = scheduleSlice.actions;

export default scheduleSlice.reducer;
