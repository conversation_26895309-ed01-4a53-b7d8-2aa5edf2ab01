import { configureStore } from "@reduxjs/toolkit";
import userReducer from "../Components/user/reducer";
import ppuuReducer from "./slices/ppuu/reducers";
import { activityManagementApi } from "../Components/Dashboard/activityManagement/reducer/activityManagementSlice";
import scheduleSlice from "./slices/scheduleSlice";
import scheduleTaskSlice from "../Components/Dashboard/Tasks/ScheduleTask/scheduleTaskSlice/scheduleTaskSlice";
import { clearUserToken } from "../Components/user/action";
import { setupListeners } from "@reduxjs/toolkit/query";
import ToastersService from "../Services/toasters.service";
import { farmManagementSlice } from "../Components/Dashboard/farm-management/slice/farmManagement.slice";
import { farmManagementConfigurationSlice } from "../Components/Dashboard/farm-management-configuration/slice/farmConfigurationSlice";

const expiredTokenMiddleware = (storeAPI : any) => (next : any) => (action : any) =>{
  const apis = [activityManagementApi]
  for (const api of apis) {
    for(const endpoint of Object.values(api.endpoints)){
      if (endpoint.matchRejected(action)) {
        if (action?.error?.code == '401') {
          localStorage.removeItem("accessToken");
          localStorage.removeItem("resetToken");
          ToastersService.failureToast("Session expired!")
          window.location.href = "/login"
        }
      }
    }
  }
  return next(action)
}

const store = configureStore({
  reducer: {
    user: userReducer,
    ppuu: ppuuReducer,
    schedule: scheduleSlice,
    scheduleTask : scheduleTaskSlice,
    [activityManagementApi.reducerPath]: activityManagementApi.reducer,
    [farmManagementSlice.reducerPath] : farmManagementSlice.reducer,
    [farmManagementConfigurationSlice.reducerPath]:farmManagementConfigurationSlice.reducer
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({serializableCheck : false}).concat(activityManagementApi.middleware).concat(farmManagementSlice.middleware).concat(farmManagementConfigurationSlice.middleware),
});

setupListeners(store.dispatch)
// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;

export default store;