// const {axios} = require("axios")
import axios from "axios";
import {updateUserToken2} from './Components/user/action';
const { /* get,  */put, /* post,  */configurationJSON, KrushalError } = require('@krushal-it/common-core')

const post = async (url, passedHeaders, data) => {
  let instanceParameters
  try {
    const headers = {
      "X-App-Id": configurationJSON().KRUSHAL_APP_ID,
      'content-type': "application/json",
      correlationId: undefined,
      partnerId: undefined,
      userDeviceId: '-1',
      ...passedHeaders
    }
    console.log('SA lR 3, headers = ', headers)
    const instance = axios.create()
    instanceParameters = {
      url,
      method: "POST",
      data,
      headers
    }
    const response = await instance(instanceParameters)
    return response
  } catch (error) {
    console.log('r p 30, caught error')
    if (error.response && error.response.data && error.response.data.name === 'KrushalError' && error.response.data.return_code === -1001 && error.response.data.additionalMessage.newAccessCode) {
      const newToken = String(error.response.data.additionalMessage.newAccessCode)
      // localStorage.setItem('accessToken', newToken)
      updateUserToken2(newToken, instanceParameters.headers.refreshtoken)
      console.log('r p 31, is token expiry error, new token = ', newToken)
      try {
        const instance = axios.create()
        instanceParameters.headers.token = newToken
        const response1 = await instance(instanceParameters);
        console.log('r p 33, made another call successful')
        return response1
      } catch (error1) {
        console.log('r p 20, error1')
        console.log('r p 20a, error1 = ', error1)
        throw error1
      }
    } else {
      console.log('r p 10, error')
      console.log('r p 10a, error = ', error)
      throw error
    }
  }
}

const get = async (url, passedHeaders) => {
  let instanceParameters
  try {
    const headers = {
      "X-App-Id": configurationJSON().KRUSHAL_APP_ID,
      "Content-Type": "application/json",
      correlationId: undefined,
      partnerId: undefined,
      userDeviceId: '-1',
      ...passedHeaders
    }
    console.log('r g 1, headers = ', headers)
    const instance = axios.create()
    instanceParameters = {
      url,
      method: "GET",
      headers
    }
    const response = await instance(instanceParameters);
    return response
  } catch (error) {
    console.log('r g 30, caught error')
    if (error.response && error.response.data && error.response.data.name === 'KrushalError' && error.response.data.return_code === -1001 && error.response.data.additionalMessage.newAccessCode) {
      const newToken = String(error.response.data.additionalMessage.newAccessCode)
      console.log('r p 31, is token expiry error, new token = ', newToken)
      try {
        instanceParameters.headers.token = newToken
        // localStorage.setItem('accessToken', newToken)
        updateUserToken2(newToken, instanceParameters.headers.refreshtoken)
        console.log('r g 32, making another call')
        const instance = axios.create()
        const response1 = await instance(instanceParameters);
        console.log('r g 33, made another call successful')
        return response1
      } catch (error1) {
        console.log('r g 20, error1')
        console.log('r g 20a, error1 = ', error1)
        throw error1
      }
    } else {
      console.log('r g 10, error')
      console.log('r g 10a, error = ', error)
      throw error
    }
  }
}

// module.exports = {getComplaintReport, getRegistrationReport, getSellableAnimalsReport}
export  {
  post, get
}