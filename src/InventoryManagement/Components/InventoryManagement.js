import { Link } from 'react-router-dom';

import { Card, Row, Col } from 'antd'

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const InventoryManagement = () => {
  return (
    <>
    <Row>
      <Col span={3}>
        <Link to="/im/pharmacy-list">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Pharmacy List
          </Card>
        </Link>
      </Col>
    </Row>
  </>
  )
}

// export default InventoryManagement
export default withAuthorization(InventoryManagement, [OPS_TEAM])