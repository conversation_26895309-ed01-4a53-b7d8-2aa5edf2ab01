import { Autocomplete, TextField, Checkbox } from "@mui/material";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { Col, Row } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { instance } from "../../Services/api.service";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import ToastersService from "../../Services/toasters.service";
import { clearUserToken } from "../../Components/user/action";
import _ from "lodash";

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

const PharmacyVillageAssign = forwardRef(({partnerId},ref) => {

    const dispatch = useDispatch()
    const navigate = useNavigate()
    const userToken = useSelector((state) => state.user.userToken);
    const [statesList, setStatesList] = useState([]);
    const [districtsList, setDistrictsList] = useState([]);
    const [talukasList, setTalukasList] = useState([]);
    const [villagesList, setVillagesList] = useState([]);
    const [geoList, setGeoList] = useState([]);
    const [selectedState, setSelectedState] = useState(null);
    const [selectedDistrict, setSelectedDistrict] = useState(null);
    const [selectedTaluk, setSelectedTaluk] = useState(null);
    const [isDisabled, setIsDisabled] = useState(false);
    const [selectedValue, setSelectedValue] = useState([]);
    const [selectAll, setSelectAll] = useState(false);
    const [refresh, setRefresh] = useState(1);
    const [compareExistGeoList, setCompareExistGeoList] = useState([])
    

    useEffect(()=>{
        getStatesList()
        getGeoDataByPartnerId()
    },[refresh])

    const getGeoDataByPartnerId = async ()=>{
      try {
        const response = await instance({
          url : `/e/get-partner-geo/${partnerId}`,
          method : "GET",
          headers: {
            "Content-Type": "application/json",
            token: userToken.accessToken,
          },
        })
        if (response.status === 200 || 201) {
          if (response.data.data == -1) {
            ToastersService.failureToast("Village details not found!");
          } else {
            setGeoList(response?.data?.data?.geo);
            setCompareExistGeoList(response?.data?.data?.geo)
          }
        }
      } catch (error) {
        if (error.response.status === 401) {
          dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
        }
      }
    }
    const getStatesList = async () => {
        try {
          const response = await instance({
            url: "/location/state",
            method: "GET",
            headers: {
              token: userToken.accessToken,
            },
          });
          if (response.status === 200) {
            setStatesList(response.data.data);
          }
        } catch (error) {
          console.log(error);
          if (error.response.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
          }
        }
      };
    
      const getDistrictsList = async (stateId) => {
        try {
          const response = await instance({
            url: "/location/district?state_id=" + stateId,
            method: "GET",
            headers: {
              token: userToken.accessToken,
            },
          });
          if (response.status === 200) {
            setDistrictsList(response.data.data);
          }
        } catch (error) {
          console.log(error);
          if (error.response.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
          }
        }
      };
    
      const getTalukaList = async (districtId) => {
        try {
          const response = await instance({
            url: "/location/taluka?district_id=" + districtId,
            method: "GET",
            headers: {
              token: userToken.accessToken,
            },
          });
          if (response.status === 200) {
            setTalukasList(response.data.data);
          }
        } catch (error) {
          console.log(error);
          if (error.response.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
          }
        }
      };
    
      const getVillagesList = async (talukId, geoList) => {
        try {
          const response = await instance({
            url: "/location/village?taluka_id=" + talukId,
            method: "GET",
            headers: {
              token: userToken.accessToken,
            },
          });
          if (response.status === 200) {
            console.log("geoListttt", geoList);
            const filteredVillages = response.data.data.filter(
              (village) =>
                !geoList.some((geo) => geo.village_id === village.village_id)
            );
            setVillagesList(filteredVillages);
          }
        } catch (error) {
          console.log(error);
          if (error.response.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
          }
        }
      };
    
      const stateInputChangeHandler = (event, value) => {
        console.log(value);
        if (value !== null) {
          getDistrictsList(value);
          setSelectedDistrict(null);
        }
        if (districtsList?.length > 0) {
          setVillagesList([]);
          setTalukasList([]);
          setSelectedDistrict(null);
          setSelectedTaluk(null);
        }
      };
    
      const districtInputChangeHandler = (event, value) => {
        console.log(value);
        if (value !== null) {
          getTalukaList(value);
          setSelectedTaluk(null);
        }
        if (villagesList?.length > 0) {
          setVillagesList([]);
        }
      };
    
      const talukaInputChangeHandler = (event, value) => {
        console.log(value);
        if (value !== null) {
          getVillagesList(value, geoList);
        }
      };
    
      const villageInputChangeHandler = (event, value) => {
        if (value.some((option) => option.village_name_l10n.en === "Select All")) {
          if (selectAll) {
            setSelectAll(false);
            setSelectedValue([]);
          } else {
            setSelectAll(true);
            setSelectedValue(villagesList);
          }
        } else {
          setSelectAll(false);
          setSelectedValue(value);
        }
      };
    
      const addVillageHandler = () => {
        if (selectedValue.length > 0) {
          const selectedVillageIds = selectedValue.map((value) =>
            typeof value === "number" ? value : value.village_id
          );
    
          const isVillageAlreadyAdded = selectedVillageIds.some((villageId) =>
            geoList.some((option) => option.village_id === villageId)
          );
    
          if (isVillageAlreadyAdded) {
            ToastersService.failureToast("Village(s) is already added!");
            setSelectedValue([]);
          } else {
            setGeoList((prevList) => [...prevList, ...selectedValue]);
            setSelectedValue([]);
          }
        }
      };
    
      const removeVillageHandler = (index) => {
        setGeoList((prevList) => {
          const updatedList = [...prevList];
          updatedList.splice(index, 1);
          return updatedList;
        });
      };
    
      const removeAllVillageHandler = () => {
        setGeoList([]);
      };

      const addPharmacyVillages = async () => {
        try {
          const response = await instance({
            url: `/e/get-partner-geo/${partnerId}`,
            method: "PUT",
            data: {
              geo: geoList.map((option) => option.village_id)
            },
            headers: {
              "Content-Type": "application/json",
              token: userToken.accessToken,
            },
          });
          if (response.status == 200 || response.status == 201) {
            if (response.data.data !== -1) {
              ToastersService.successToast("village updated successfully!");
              setRefresh(refresh + 1);
              if (selectedTaluk) {
                getVillagesList(selectedTaluk.taluk_id, geoList);
              }
            } else {
              ToastersService.failureToast("Something went wrong!");
            }
          }
        } catch (error) {
          console.log(error);
          if (error.response.status === 409) {
            console.log(error.response.data.id, "id1");
            ToastersService.failureToast(error.response.data.message);
            const idList = error.response.data.id;
            const newList = geoList.filter(
              (value) => !idList.includes(value.village_id)
            );
            setGeoList([...newList]);
          } else if (error.response.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
          } else {
            ToastersService.failureToast("Village not updated!");
          }
        }
      };
      useImperativeHandle(ref, ()=>({
        assignVillage(){
          if (_.isEqual(geoList,compareExistGeoList)) {
            ToastersService.warningToast("You didn't add any villages to save")
          }else{
            addPharmacyVillages()
          }
        }
      }))
  return (
    <>
      <div className="d-flex ps-2">Allotted Villages</div>
      <div className="d-flex" style={{ width: "90%", flexWrap: "wrap" }}>
        {geoList.length > 0 ? (
          geoList
            .sort((a, b) =>
              a.village_name_l10n.en.localeCompare(b.village_name_l10n.en)
            )
            .map((option, index) => {
              return (
                <div key={index} className="villageAllot">
                  {/* {console.log(option, "option")} */}
                  {option.village_name_l10n.en}
                  {/* {villageName ? villageName : null} */}
                  <button
                    className="cross"
                    onClick={() => removeVillageHandler(index)}
                  >
                    &times;
                  </button>
                </div>
              );
            })
        ) : (
          <div
            className="ps-2 mt-3"
            style={{ color: "gray", fontSize: "12px" }}
          >
            no villages allotted yet!
          </div>
        )}
      </div>
      <div
        className="add-cattle__add-disease-btn mt-3"
        onClick={removeAllVillageHandler}
      >
        <i className="fa fa-trash"></i> Remove All
      </div>

      <hr />
      <Row>
        <Col lg={6} md={6} sm={12}>
          <Autocomplete
            options={
              statesList &&
              statesList.sort((a, b) => {
                const stateNameA = a.state_name_l10n.en || a.state_name_l10n.ul;
                const stateNameB = b.state_name_l10n.en || b.state_name_l10n.ul;
                return stateNameA.localeCompare(stateNameB);
              })
            }
            getOptionLabel={(option) => option?.state_name_l10n?.en}
            //   disabled={activityList.length === 0}
            sx={{
              width: "100%",
              marginTop: "10px",
              background: "#fff",
            }}
            renderInput={(params) => (
              <TextField {...params} label="Select State..." />
            )}
            onChange={(event, value) => {
              setSelectedState(value);
              stateInputChangeHandler(event, value ? value?.state_id : null);
              if (!value) {
                console.log("harry");
                setDistrictsList([]);
              }
            }}
            value={selectedState}
            disabled={isDisabled}
          />
        </Col>
        <Col lg={6} md={6} sm={12}>
          <Autocomplete
            options={districtsList}
            getOptionLabel={(option) => option?.district_name_l10n?.en}
            disabled={districtsList?.length === 0}
            sx={{
              width: "100%",
              marginTop: "10px",
              background: "#fff",
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select District..."
                disabled={isDisabled}
              />
            )}
            onChange={(event, value) => {
              setSelectedDistrict(value);
              districtInputChangeHandler(
                event,
                value ? value?.district_id : null
              );
              if (!value) {
                setTalukasList([]);
              }
            }}
            value={selectedDistrict}
          />
        </Col>
        <Col lg={6} md={6} sm={12}>
          <Autocomplete
            options={
              talukasList &&
              talukasList?.sort((a, b) => {
                const talukaNameA =
                  a.taluk_name_l10n.en || a.taluk_name_l10n.ul;
                const talukaNameB =
                  b.taluk_name_l10n.en || b.taluk_name_l10n.ul;
                return talukaNameA.localeCompare(talukaNameB);
              })
            }
            getOptionLabel={(option) => option?.taluk_name_l10n?.en}
            disabled={talukasList?.length === 0}
            sx={{
              width: "100%",
              marginTop: "10px",
              background: "#fff",
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Taluka..."
                disabled={isDisabled}
              />
            )}
            onChange={(event, value) => {
              console.log(value);
              setSelectedTaluk(value);
              talukaInputChangeHandler(event, value ? value?.taluk_id : null);
              if (!value) {
                console.log("harry");
                setVillagesList([]);
              }
            }}
            value={selectedTaluk}
          />
        </Col>
        <Col lg={6} md={6} sm={12}>
          <Autocomplete
            multiple
            options={[
              { village_name_l10n: { en: "Select All" } },
              ...villagesList,
            ]}
            disableCloseOnSelect
            id="checkboxes-tags-demo"
            getOptionLabel={(option) =>
              option?.village_name_l10n?.en || option?.village_name_l10n?.ul
            }
            disabled={villagesList?.length === 0}
            renderOption={(props, option, { selected }) => (
              <li {...props} key={option?.village_id}>
                <Checkbox
                  icon={icon}
                  checkedIcon={checkedIcon}
                  style={{ marginRight: 8 }}
                  checked={selected}
                />
                {option?.village_name_l10n?.en || option?.village_name_l10n?.ul}
              </li>
            )}
            sx={{
              width: "100%",
              marginTop: "10px",
              background: "#fff",
              maxHeight: "100px",
              overflowY: "auto",
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Village..."
                disabled={isDisabled}
              />
            )}
            onChange={(event, value) => villageInputChangeHandler(event, value)}
            value={selectedValue}
          />
        </Col>
      </Row>

      <div
        style={{
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "flex-end",
        }}
      >
        <div className="add-village-btn mt-3" onClick={addVillageHandler}>
          + Add villages
        </div>
      </div>
    </>
  );
})

export default PharmacyVillageAssign;
