import { useEffect, useState, createRef, useRef } from "react";
import { Link, useNavigate, useParams, useSearchParams,  } from "react-router-dom";
import {useSelector} from "react-redux"
import { Space, Tabs, But<PERSON>, Collapse, Flex } from 'antd'
import moment from 'moment';

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  validateFormElements2,
  assignValuesFromControlReferencesToFormState,
  extractChangedValues,
  assignReferencesAsListToForm,
} from "../../Components/Common/KrushalOCForm";
import ToastersService from "../../Services/toasters.service";
import PharmacyVillageAssign from "./PharmacyVillageAssign";

const {CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const { Panel } = Collapse
const lodashObject = require("lodash");

const { extractBasedOnLanguage } = require('@krushal-it/common-core')
const { getPartnerBasicData, getPartnerDetails, setPartnerDetails } = require('../../router')

const loadBasicPartnerData = async (userToken, partnerId) => {
  try {
    const headers = {
      useCase: 'Get Basic Partner Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getPartnerBasicData(headers, partnerId)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getPartnerData = async (userToken, partnerId, classifierArray) => {
  try {
    const headers = {
      useCase: 'Get Partner Classifier Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getPartnerDetails(headers, partnerId, classifierArray)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const savePartnerData = async (userToken, partnerId, basicDataChangedValues) => {
  try {
    const headers = {
      useCase: 'Get Basic Partner Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const dataToSend = {...basicDataChangedValues, partnerId: partnerId, partner_type_id: 1000850001}
    const response = await setPartnerDetails(headers, dataToSend)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const pharmacyBasicDetailsFormDefinition = {
  formControls: {
    pharmacyId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Pharmacy Id",
      readOnly: 1,
    },
    pharmacyName: {
      ocFormRef: createRef(),
      type: "text-l10n_2",
      label: "Pharmacy Name",
    },
  },
  visibleFormControls: [
    "pharmacyId",
    "pharmacyName",
  ],
  elementToDataMapping: {
    pharmacyId: 'partner_id',
    pharmacyName: 'partner_name_l10n',
  },
  dataToElementMapping: {
    partner_id: 'pharmacyId',
    partner_name_l10n: 'pharmacyName',
  },
}

const pharmacyBasicDetailsFormInitialState = {
}

const pharmacyInvoiceDetailsFormDefinition = {
  formControls: {
    pharmacyAddress: {
      ocFormRef: createRef(),
      type: "text-l10n_2",
      label: "Pharmacy Address",
    },
    pharmacyDistributionLicenseNumber: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "DL Number",
    },
    pharmacyGST: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "GST",
    },
    pharmacyPAN: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "PAN",
    },
    pharmacyGSTCode: {
      ocFormRef: createRef(),
      type: "positive-number_2",
      label: "GST Code",
    },
    pharmacyGSTState: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "GST State",
    },
    pharmacyInvoiceSalesPerson: {
      ocFormRef: createRef(),
      type: "text-l10n_2",
      label: "Sales Person",
    },
    partnerZohoId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Partner Zoho id",
    },
  },
  visibleFormControls: [
    "pharmacyAddress",
    "pharmacyDistributionLicenseNumber",
    "pharmacyGST",
    "pharmacyPAN",
    "pharmacyGSTCode",
    "pharmacyGSTState",
    "pharmacyInvoiceSalesPerson",
    "partnerZohoId"
  ],
  elementToDataMapping: {
    pharmacyAddress: 'partner_address',
    pharmacyDistributionLicenseNumber: 'pharmacy_distribution_license_number',
    pharmacyGST: 'partner_gst',
    pharmacyPAN: 'partner_pan',
    pharmacyGSTCode: 'partner_gst_code',
    pharmacyGSTState: 'partner_gst_state',
    pharmacyInvoiceSalesPerson: 'partner_invoice_sales_person',
    partnerZohoId : 'partner_zoho_id'
  },
  dataToElementMapping: {
    partner_address: 'pharmacyAddress',
    pharmacy_distribution_license_number: 'pharmacyDistributionLicenseNumber',
    partner_gst: 'pharmacyGST',
    partner_pan: 'pharmacyPAN',
    partner_gst_code: 'pharmacyGSTCode',
    partner_gst_state: 'pharmacyGSTState',
    partner_invoice_sales_person: 'pharmacyInvoiceSalesPerson',
    partner_zoho_id : 'partnerZohoId'
  },
}

const pharmacyInvoiceDetailsFormInitialState = {
}

const IMPharmacy = () => {
  const params = useParams()
  const navigate = useNavigate()
  const userToken = useSelector((state)=>state.user.userToken);
  const [searchParams] = useSearchParams()
  // const uncompressedFilterParamsJsonString = decodeURIComponent(searchParams.get('qP'))// urlEncodedJson)
  // const queryParams = JSON.parse(uncompressedFilterParamsJsonString)
  // const customerId = queryParams.customerId
  const partnerId = params.partnerId
  const [partnerIdFromSave, setPartnerIdFromSave] = useState('')
  const ref = useRef()

  const [pharmacyBasicDetailsFormState, setPharmacyBasicDetailsFormState] = useState(pharmacyBasicDetailsFormInitialState)
  const [pharmacyInvoiceDetailsFormState, setPharmacyInvoiceDetailsFormState] = useState(pharmacyInvoiceDetailsFormInitialState)

  const loadDataAsync = async () => {
    if (partnerId !== '-1') {
      const basicPartnerDataResponse = await loadBasicPartnerData(userToken, partnerId)
      if (basicPartnerDataResponse.return_code === 0) {
        assignDataToFormState(
          pharmacyBasicDetailsFormDefinition,
          basicPartnerDataResponse.result,
          pharmacyBasicDetailsFormState,
          setPharmacyBasicDetailsFormState
        )
      }
      const classifiersToLoadForPhramacyInvoiceForm = []
      for (const visibleControl of pharmacyInvoiceDetailsFormDefinition.visibleFormControls) {
        classifiersToLoadForPhramacyInvoiceForm.push(pharmacyInvoiceDetailsFormDefinition.elementToDataMapping[visibleControl])
      }
      const pharmacyInvoiceDataResponse = await getPartnerData(userToken, partnerId, classifiersToLoadForPhramacyInvoiceForm)
      if (pharmacyInvoiceDataResponse.return_code === 0) {
        assignDataToFormState(
          pharmacyInvoiceDetailsFormDefinition,
          pharmacyInvoiceDataResponse.data,
          pharmacyInvoiceDetailsFormState,
          setPharmacyInvoiceDetailsFormState
        )

      }
    }
  }
  
  useEffect(()=>{
    loadDataAsync()
    if (params?.partnerId) {
      setPartnerIdFromSave(params?.partnerId)
    }
  },[])

  const handleSavePartnerBasicDataClick = async () => {
    /* const [pharmacyBasicDetailsCurrentFormState, pharamcyBasicDetailsFormValidationFailed] = validateFormElements(
      pharmacyBasicDetailsFormDefinition,
      pharmacyBasicDetailsFormState,
      setPharmacyBasicDetailsFormState
    ) */
    const [pharmacyBasicDetailsCurrentFormState, pharamcyBasicDetailsFormValidationFailed] = validateFormElements2(
      pharmacyBasicDetailsFormDefinition,
      pharmacyBasicDetailsFormState,
      setPharmacyBasicDetailsFormState
    )
    /* const [pharmacyInvoiceDetailsCurrentFormState, pharamcyInvoiceDetailsFormValidationFailed] = validateFormElements(
      pharmacyInvoiceDetailsFormDefinition,
      pharmacyInvoiceDetailsFormState,
      setPharmacyInvoiceDetailsFormState
    ) */
    const [pharmacyInvoiceDetailsCurrentFormState, pharamcyInvoiceDetailsFormValidationFailed] = validateFormElements2(
      pharmacyInvoiceDetailsFormDefinition,
      pharmacyInvoiceDetailsFormState,
      setPharmacyInvoiceDetailsFormState
    )
    if (pharamcyBasicDetailsFormValidationFailed || pharamcyInvoiceDetailsFormValidationFailed) {
      // do something
      console.log("Validation Failed");
      ToastersService.failureToast("Validation failed!");
    } else {
      console.log("you can save now")
      const updatedPharmacyBasicDetailsCurrentFormState = assignValuesFromControlReferencesToFormState(pharmacyBasicDetailsFormDefinition, pharmacyBasicDetailsCurrentFormState, setPharmacyBasicDetailsFormState)
      const updatedPharmacyInvoiceDetailsCurrentFormState = assignValuesFromControlReferencesToFormState(pharmacyInvoiceDetailsFormDefinition, pharmacyInvoiceDetailsCurrentFormState, setPharmacyBasicDetailsFormState)
      const basicDataChangedValues = extractChangedValues(pharmacyBasicDetailsFormDefinition, updatedPharmacyBasicDetailsCurrentFormState)
      const invoiceDataChangedValues = extractChangedValues(pharmacyInvoiceDetailsFormDefinition, updatedPharmacyInvoiceDetailsCurrentFormState)

      const totalChangedData = {...basicDataChangedValues, ...invoiceDataChangedValues}
      
      const resposeFromSavingCropScreenData = await savePartnerData(userToken, partnerId, totalChangedData)
      if (resposeFromSavingCropScreenData.return_code === 0) {
        setPartnerIdFromSave(resposeFromSavingCropScreenData?.partner_id)
        await loadDataAsync()
        ToastersService.successToast("Updated Partner Data")
      } else {
        ToastersService.failureToast("Failed To Updated Partner Data")
      }
    }
  }

  return (
    <>
      <Button type="link" style={{margin:"10px 0 10px 0"}} onClick={()=>navigate(-1)}>Back to pharmacy list</Button>
      <Collapse defaultActiveKey={['1']} >
        <Panel header="Basic Pharmacy Details" key="1">
          <div style={{display:'flex', alignItems:"flex-start",}}>
            <div style={{flex:"1"}}>
              <KrushalOCForm
                formState={pharmacyBasicDetailsFormState}
                setFormState={setPharmacyBasicDetailsFormState}
                formDefinition={pharmacyBasicDetailsFormDefinition}
              />
            </div>
            <div style={{alignSelf: "flex-end", display:"flex", flexDirection: "column"}}>
              <Button
                onClick={() => handleSavePartnerBasicDataClick()}
                >
                Save
              </Button>
              <Button style={{marginTop:"4px"}}>Future Use Button</Button>
            </div>
          </div>
        </Panel>
        <Panel header="Other Details" key="2">
          <div style={{display:'flex', alignItems:"flex-start"}}>
            <div style={{flex:"1"}}>
              <KrushalOCForm
                formState={pharmacyInvoiceDetailsFormState}
                setFormState={setPharmacyInvoiceDetailsFormState}
                formDefinition={pharmacyInvoiceDetailsFormDefinition}
              />
            </div>
            <div style={{alignSelf: "flex-end", display:"flex", flexDirection: "column"}}>
              <Button
                onClick={() => handleSavePartnerBasicDataClick()}
                >
                Save
              </Button>
              <Button style={{marginTop:"4px"}}>Future Use Button</Button>
            </div>
          </div>
        </Panel>
        <Panel header="Villages" key="3" collapsible={partnerIdFromSave !== '-1' ? "" : "disabled"}>
          <div >
            <div style={{flex:"1"}}>
             <PharmacyVillageAssign partnerId={partnerIdFromSave} ref={ref} />
            </div>
            <div style={{display:"flex", justifyContent:"center", alignItems:"center"}}>
            <Button
                onClick={() => ref?.current?.assignVillage()}
                >
                Save
              </Button>
            </div>
          </div>
        </Panel>
      </Collapse>
    </>
    
  )
}

// export default IMPharmacy
export default withAuthorization(IMPharmacy, [OPS_TEAM])