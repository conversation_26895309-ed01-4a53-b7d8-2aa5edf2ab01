import {
  Routes,
  Route,
} from "react-router-dom";

import Sidebar from '../Components/Sidebar/Sidebar'

import InventoryManagement from './Components/InventoryManagement'
import PharmacyList from './Components/PharmacyList'
import IMPharmacy from './Components/IMPharmacy'

const InventoryManagementRoutes = () => {
  return (
    <Routes>
      <Route path="im" element={<Sidebar content={<InventoryManagement />} />} />
      <Route path="im/pharmacy-list" element={<Sidebar content={<PharmacyList />} />} />
      <Route path="im/pharmacy/:partnerId" element={<Sidebar content={<IMPharmacy />} />} />
    </Routes>
  )
}

export default InventoryManagementRoutes