import ToastersService from "./toasters.service";
import axios from "axios";

const {
  get,
  post,
  configurationJSON,
  loadConfiguration,
} = require("@krushal-it/common-core");

// export const BASE_URL = "http://**************/api";
loadConfiguration(process.env.REACT_APP_ENV_JSON);
export const BASE_URL = configurationJSON().SERVICE_END_POINTS.BACK_END;
console.log(configurationJSON());
// export const BASE_URL = "http://localhost:3001/api";
// export const BASE_URL = "http://**************/api";
// export const BASE_URL = 'https://ahs1.krushal.in/backend4/api';

export const tokenFailureHandler = (body) => {
  console.log("Response from API service", body);
  if (body.message == "token_not_found") {
    ToastersService.failureToast("Authentication failed! Please login again.");
  } else if (body.message == "auth/id-token-expired") {
    ToastersService.failureToast("Session expired! Please login again.");
  } else if (body.message == "auth/argument-error") {
    ToastersService.failureToast("Authentication failed! Please login again.");
  }
  localStorage.removeItem("token");
};

export const instance = axios.create({
  baseURL: configurationJSON().SERVICE_END_POINTS.BACK_END,
  headers: {
    
    "X-App-Id": configurationJSON().KRUSHAL_APP_ID,
  },
});
