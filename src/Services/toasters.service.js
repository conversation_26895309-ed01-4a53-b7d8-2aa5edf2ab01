import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const failureToast = (message) => {
  toast.error(
    <>
      <div>{message}</div>
      
    </>,
    {
      position: "top-right",
      autoClose: 3000,
      hideProgressBar: true,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: "colored",
    }
  );
};

const warningToast = (message) => {
  toast.warn(
    <>
      <div>{message}</div>
      
    </>,
    {
      position: "top-right",
      autoClose: 3000,
      hideProgressBar: true,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: "colored",
    }
  );
};

const successToast = (message) => {
  toast.success(
    <>
      <div>{message}</div>
      
    </>,
    {
      position: "top-right",
      autoClose: 3000,
      hideProgressBar: true,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: "colored",
    }
  );
};

const ToastersService = {
  successToast,
  failureToast,
  warningToast
};

export default ToastersService;
