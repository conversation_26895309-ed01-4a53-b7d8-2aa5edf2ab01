import {
  Routes,
  Route,
} from "react-router-dom";

import Sidebar from '../Components/Sidebar/Sidebar'

import AHVet from './Components/AHVet';
import AnimalListingForVeterinarians from './Components/AnimalListingForVeterinarians'
import MedicineList from './Components/MedicineList'

const AHVetRoutes = () => {
  return (
    <Routes>
      <Route path="ahv" element={<Sidebar content={<AHVet />} />} />
      <Route path="ahv/onboarding-animal-health-score-verification" element={<Sidebar content={<AnimalListingForVeterinarians />} />} />
      <Route path="ahv/periodic-animal-health-score-reverification" element={<Sidebar content={<AnimalListingForVeterinarians />} />} />
      <Route path="ahv/medicine-list" element={<Sidebar content={<MedicineList />} />} />
    </Routes>
  )
}

export default AHVetRoutes