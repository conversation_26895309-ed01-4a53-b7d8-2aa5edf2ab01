import { Checkbox, Space, Button, Modal } from "antd";
import { ReloadOutlined } from '@ant-design/icons';

import { useState, useRef } from "react"
import { Link, useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import {useSelector} from "react-redux"
import dayjs from 'dayjs'

import ToastersService from "../../Services/toasters.service";

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  extractChangedValues,
} from "../../Components/Common/KrushalOCForm";

const {EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, IT_ADMIN, withAuthorization} = require('../../Components/user/authorize')

const { getAnimalDataReport, /* getHealthScoreForOnboardedAnimalsReport,  */getAnimalDataReportsPageLevelFilterData, /* getHealthScoreForOnboardedAnimalsReportFilterData,  */snoozeHealthScoreAnimals, /* updateStatusOfHealthScoreAnimals */ } = require('../../router')

const { OCTable, CommentViewerModalButton, createPageParamsFromURLString, MediaCarouselModalButton } = require('../../Components/Common/AntTableHelper')

const loadReport = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Animals For Whom Health Score Needs To Be Generated and Verified Report',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getAnimalDataReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadFilters = async (userToken, selectedColumns, additionalParams) => {
  try {
    const headers = {
      useCase: 'Get Animals For Whom Health Score Needs To Be Generated and Verified Report Filter Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getAnimalDataReportsPageLevelFilterData(headers, {selectedColumns: selectedColumns, searchConfiguration: tableConfiguration})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const firstTimeHealthRecordVerification = window.location.href.includes('onboarding-animal-health-score-verification')
const periodicHealthRecordReverification = window.location.href.includes('periodic-animal-health-score-reverification')

const tableConfiguration = {
  tableNavigationUrl: '../ahv/onboarding-animal-health-score-verification',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  // enableSplittingOfServerData: true, // use this only if you have made changes to the backend
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'animal_id',
  columns: [
    {
      title: "Animal Id",
      key: "animal_id",
    },
    {
      title: "Active",
      key: "animal_active",
    },
    {
      title: 'Snoozed or Not',
      key: 'snooze_status',
    },
    {
      title: "Health Score Verification Status",
      key: "latest_health_score_verification_status",
    },
    {
      title: "Health Score Verification Status Date",
      key: "latest_health_score_verification_status_updated_at",
    },
    {
      title: "Animal Ear Tag",
      key: "animal_ear_tag",
    },
    {
      title: "Animal Type",
      key: "animal_type",
    },
    {
      title: "Cow Score Attribute Count",
      key: "cow_score_attribute_count"
    },
    {
      title: "Cow Score Image Count",
      key: "cow_score_image_count"
    },
    {
      title: "Farmer Name",
      key: "customer_name",
    },
    {
      title: "Farmer Mobile Number",
      key: "mobile_number",
    },
    {
      title: "Farmer Location Recorded Status",
      key: "farmer_location_recorded_status",
    },
    {
      title: "Location Updated Timestamp",
      key: "location_updated_timestamp",
    },
    {
      title: "Taluk",
      key: "taluk_name",
    },
    {
      title: "Village",
      key: "village_name",
    },
    {
      title: "BDM",
      key: "bdm",
    },
    {
      title: "BDM Mobile",
      key: "bdm_mobile",
    },
    {
      title: "With Krushal For Months",
      key: "with_krushal_for_months",
      dataIndex: 'with_krushal_from_date',
    },
    {
      title: "With Krushal From",
      key: "with_krushal_from_date",
    },
    {
      title: "Subscription Plan",
      key: "current_subscription_plan",
    },
    {
      title: "Subscription Date Updated On",
      key: "with_krushal_from_date_updated_at",
    },
    {
      title: "Latest LPD",
      key: "latest_lpd",
    },
    {
      title: "Latest LPD Date",
      key: "latest_lpd_date",
    },
    {
      title: "Number of Calvings",
      key: "number_of_calvings",
    },
    {
      title: "Last Calving Date",
      key: "last_calving_date",
    },
    {
      title: "Number of Months Pregnant",
      key: "number_of_months_pregnant",
    },
    {
      title: "Latest Weight",
      key: "animal_weight",
    },
    {
      title: "Latest Weight Date",
      key: "latest_animal_weight_date",
    },
    {
      title: "Latest Health Score",
      key: "health_score",
    },
    {
      title: "Health Score Calculation Date",
      key: "latest_health_score_date",
    },
    /* {
      title: "Body Score",
      key: "body_score",
    },
    {
      title: "Udder and Teat Score",
      key: "udder_and_teat_score",
    }, */
  ],
  columnConfiguration: {
    animal_id: {
      headingText: 'Animal Id',
      dataType: 'uuid',
      includeInOmnisearch: true
    },
    animal_active: {
      headingText: 'Animal Active Or Not',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      filters: [
        { text: 'Active', value: 'Active', value_int: ********** },
        { text: 'Not Active', value: 'Not Active', value_int: ********** },
      ],
      defaultFilteredValue: ['Active'],
      translateFilteredValuesToAlt: 'value_int',
      altColumn: 'animal_active_id'
    },
    snooze_status: {
      headingText: 'Snooze Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'Snoozed', value: 'Snoozed' },
        { text: 'Not Snoozed', value: 'Not Snoozed' },
      ],
      defaultFilteredValue: ['Not Snoozed'],
      enableSort: true,
      sortColumnType: 'string',
    },
    animal_ear_tag: {
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    animal_type: {
      headingText: 'Animal Type',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'Cow', value: 'Cow' },
        { text: 'Cow Calf', value: 'Cow Calf' },
        { text: 'Buffalo', value: 'Buffalo' },
        { text: 'Buffalo Calf', value: 'Buffalo Calf' },
      ],
      // defaultFilteredValue: ['Not Snoozed'],
      enableSort: true,
      sortColumnType: 'string',
    },
    latest_health_score_verification_status: {
      headingText: 'Latest Health Score Verification Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'None', value: 'None', value_int: ********** },
        { text: 'Accepted', value: 'Accepted', value_int: ********** },
        { text: 'Rejected', value: 'Rejected', value_int: ********** },
      ],
      defaultFilteredValue: firstTimeHealthRecordVerification ? ['None'] : periodicHealthRecordReverification ? ['Accepted', 'Rejected'] : undefined,
      enableSort: true,
      sortColumnType: 'string',
      // translateFilteredValuesToAlt: 'value_int',
      // altColumn: 'latest_health_score_verification_status_id'
    },
    latest_health_score_verification_status_updated_at: {
      headingText: 'Latest Health Score Verification Status Last Updated At',
      enableFilter: true,
      type: 'dayjs_range',
      formatType: 'date',
      format: 'h:mm a, DD-MMM-YYYY',
      enableSort: true,
      sortColumnType: 'date',
      defaultSort: periodicHealthRecordReverification ? 'descend' : undefined,
      defaultFilteredValue: periodicHealthRecordReverification ? [[undefined, dayjs().subtract(3, 'month')]] : undefined,
    },
    cow_score_attribute_count: {
      type: 'number_range',
      headingText: 'Cow Score Attribute Count',
      enableFilter: true,
      formatType: 'int',
      enableSort: true,
      sortColumnType: 'int',
    },
    cow_score_image_count: {
      type: 'number_range',
      headingText: 'Cow Score Attribute Count',
      enableFilter: true,
      formatType: 'int',
      enableSort: true,
      sortColumnType: 'int',
    },
    customer_id: {
      headingText: 'Customer Id',
      allowFilterInPageParams: true,
      paramSearchType: 'array',
      paramSearchDataType: 'string'
    },
    customer_name: {
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    mobile_number: {
      headingText: 'Mobile Number',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    farmer_location_recorded_status: {
      headingText: 'Farmer Location Recorded Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'Location Recorded', value: 'Location Recorded' },
        { text: 'Location Not Recorded', value: 'Location Not Recorded' },
      ],
      defaultFilteredValue: ['Location Recorded'],
      enableSort: true,
      sortColumnType: 'string',
    },
    location_updated_timestamp: {
      headingText: 'Location Updated Timestsamp',
      enableFilter: true,
      type: 'dayjs_range',
      formatType: 'date',
      format: 'h:mm a, DD-MMM-YYYY',
      enableSort: true,
      sortColumnType: 'date',
      // defaultSort: 'descend'
    },
    taluk_name: {
      headingText: 'Taluk',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      altColumn: 'taluk_id',
      updateFilterDataDuringFilterCall: true,
    },
    village_name: {
      headingText: 'Village',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      updateFilterDataDuringReportCall: true,
    },
    bdm: {
      headingText: 'BDM',
      enableFilter: true,
      type: 'single_select_array',
      dataType: 'string',
      enableSort: true,
      sortColumnType: 'string',
      altColumn: 'bdm_uuid',
      updateFilterDataDuringFilterCall: true,
      filterValuesKey: 'staff_filter_values'
    },
    with_krushal_from_date: { // for onboarding animals
      headingText: 'With Krushal From Date',
      formatType: 'date',
      format: 'MMM-YYYY',
      enableFilter: true,
      type: 'dayjs_range',
      enableSort: true,
      sortColumnType: 'date',
      defaultSort: firstTimeHealthRecordVerification ? 'ascend' : undefined
    },
    with_krushal_for_months: {
      headingText: 'With Krushal For Months',
      formatType: 'months_since',
    },
    with_krushal_from_date_updated_at: {
      formatType: 'date',
      format: 'MMM-YYYY',
      enableFilter: true,
      type: 'dayjs_range',
      enableSort: true,
      sortColumnType: 'date',
    },
    latest_lpd: {
      type: 'number_range',
      headingText: 'Latest LPD',
      enableFilter: true,
      formatType: 'int',
      enableSort: true,
      sortColumnType: 'double',
    },
    latest_lpd_date: {
      headingText: 'Latest LPD Date',
      formatType: 'date',
      format: 'DD-MMM-YYYY',
      enableFilter: true,
      type: 'dayjs_range',
      enableSort: true,
      sortColumnType: 'date',
      // defaultSort: 'ascend'
    },
    number_of_calvings: {
      type: 'number_range',
      headingText: 'Number of Calvings',
      enableFilter: true,
      formatType: 'int',
      enableSort: true,
      sortColumnType: 'int',
    },
    last_calving_date: {
      headingText: 'Last Calving Date',
      formatType: 'date',
      format: 'DD-MMM-YYYY',
      enableFilter: true,
      type: 'dayjs_range',
      enableSort: true,
      sortColumnType: 'date',
      // defaultSort: 'ascend'
    },
    number_of_months_pregnant: {
      type: 'number_range',
      headingText: 'Number of Calvings',
      enableFilter: true,
      formatType: 'double',
      format: {decimalPlaces: 1},
      enableSort: true,
      sortColumnType: 'double',
    },
    animal_weight: {
      type: 'number_range',
      headingText: 'Animal Weight',
      format: {decimalPlaces: 2},
      enableFilter: true,
      formatType: 'double',
      enableSort: true,
      sortColumnType: 'double',
    },
    latest_animal_weight_date: {
      headingText: 'Latest Animal Weight Date',
      formatType: 'date',
      format: 'DD-MMM-YYYY',
      enableFilter: true,
      type: 'dayjs_range',
      enableSort: true,
      sortColumnType: 'date',
      // defaultSort: 'ascend'
    },
    health_score: {
      type: 'number_range',
      headingText: 'Health Score',
      format: {decimalPlaces: 1},
      enableFilter: true,
      formatType: 'double',
      enableSort: true,
      sortColumnType: 'double',
    },
    latest_health_score_date: {
      headingText: 'Latest Health Score Date',
      formatType: 'date',
      format: 'DD-MMM-YYYY',
      enableFilter: true,
      type: 'dayjs_range',
      enableSort: true,
      sortColumnType: 'date',
      // defaultSort: 'ascend'
    },
  }
}

const snoozeDialogFormDefinition = {
  forceNewLineAfterControl: true,
  formControls: {
    animalId: {
      type: "text",
      label: "Animal Id",
      readOnly: 1,
    },
    animalEarTag: {
      type: "text",
      label: "Animal Ear Tag",
      readOnly: 1,
    },
    farmerId: {
      type: "text",
      label: "Farmer Id",
      readOnly: 1,
    },
    farmerName: {
      type: "text",
      label: "Farmer Name",
      readOnly: 1,
    },
    farmerMobile: {
      type: "text",
      label: "Farmer Mobile",
      readOnly: 1,
    },
    snoozeTillDate: {
      type: "date-time",
      label: "Snooze Till",
      placeHolderText: "Enter Date",
      errorMessage: "Snooze Till Date is Mandatory!",
      validations: [{ type: "Mandatory" }, {type: "MinimumDate", value: new Date()}],
    },
    farmerLevelSnoozeChoice: {
      type: "single-select",
      label: "Snooze At Farmer Level*",
      errorMessage: "Choosing if Farmer Needs to be Snoozed or Not Is Mandatory",
      labelKeyInList: "reference_name",
      valueKeyInList: "reference_id",
      placeHolderText: "Choose Yes or No",
      validations: [{ type: "Mandatory" }],
    },
    snoozeComments: {
      type: "text",
      label: "Comments",
      validations: [{ type: "Mandatory" }],
      placeHolderText: "Enter Any Comments",
    },
  },
  visibleFormControls: [
    "animalId",
    "animalEarTag",
    "farmerId",
    "farmerName",
    "farmerMobile",
    "snoozeTillDate",
    "farmerLevelSnoozeChoice",
    "snoozeComments",
  ],
  elementToDataMapping: {
    animalId: "animal_id",
    animalEarTag: "animal_ear_tag",
    farmerId: "customer_id",
    farmerName: "customer_name",
    farmerMobile: "mobile_number",
    snoozeTillDate: "snooze_till_date",
    farmerLevelSnoozeChoice: "farmer_level_snooze_choice",
    snoozeComments: "snooze_comments",
  },
  dataToElementMapping: {
    animal_id: "animalId",
    animalEarTag: "animalEarTag",
    customer_id: "farmerId",
    customer_name: "farmerName",
    mobile_number: "farmerMobile",
    snooze_till_date: "snoozeTillDate",
    farmer_level_snooze_choice: "farmerLevelSnoozeChoice",
    snooze_comments: "snoozeComments",
  },
}

const snoozeDialogFormInitialState = {
  snoozeTillDate: {},
  farmerLevelSnoozeChoice: {},
  snoozeComments: {},
}

/* const updateStatusDialogFormDefinition = {
  forceNewLineAfterControl: true,
  defaultClasses: 'mb-3',
  formControls: {
    animalId: {
      type: "text",
      label: "Animal Id",
      readOnly: 1,
    },
    animalEarTag: {
      type: "text",
      label: "Animal Ear Tag",
      readOnly: 1,
    },
    farmerId: {
      type: "text",
      label: "Farmer Id",
      readOnly: 1,
    },
    farmerName: {
      type: "text",
      label: "Farmer Name",
      readOnly: 1,
    },
    farmerMobile: {
      type: "text",
      label: "Farmer Mobile",
      readOnly: 1,
    },
    dataVerificationAnimalCurrentStatusId: {
      type: "text",
      label: "Data Verification Animal Selling Process Status Id",
      readOnly: 1,
    },
    dataVerificationAnimalCurrentStatus: {
      type: "text",
      label: "Data Verification Animal Selling Process Status",
      readOnly: 1,
    },
    dataVerificationAnimalNextStatus: {
      type: "single-select-l10n",
      label: "Data Verification Animal Selling Process Status",
      errorMessage: "Choosing the status the animal needs to be updated to",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Choose Yes or No",
      validations: [{ type: "Mandatory" }],
    },
    updateStatusComments: {
      type: "text",
      label: "Comments",
      validations: [{ type: "Mandatory" }],
      placeHolderText: "Enter Any Comments",
    },
  },
  visibleFormControls: [
    "animalId",
    "animalEarTag",
    "farmerId",
    "farmerName",
    "farmerMobile",
    "dataVerificationAnimalCurrentStatusId",
    "dataVerificationAnimalCurrentStatus",
    "dataVerificationAnimalNextStatus",
    "updateStatusComments",
  ],
  elementToDataMapping: {
    animalId: "animal_id",
    animalEarTag: "animal_ear_tag",
    farmerId: "customer_id",
    farmerName: "customer_name",
    farmerMobile: "mobile_number",
    dataVerificationAnimalCurrentStatusId: 'data_verification_animal_current_status_id',
    dataVerificationAnimalCurrentStatus: 'data_verification_animal_current_status',
    dataVerificationAnimalNextStatus: 'data_verification_animal_next_status',
    updateStatusComments: "update_status_comments",
  },
  dataToElementMapping: {
    animal_id: "animalId",
    animalEarTag: "animalEarTag",
    customer_id: "farmerId",
    customer_name: "farmerName",
    mobile_number: "farmerMobile",
    data_verification_animal_current_status_id: 'dataVerificationAnimalCurrentStatusId',
    data_verification_animal_current_status: 'dataVerificationAnimalCurrentStatus',
    data_verification_animal_next_status: 'dataVerificationAnimalNextStatus',
    update_status_comments: "updateStatusComments",
  },
} */

const animalDocumentsMediaCarouselInitialConfiguration = {
  completeInformation: false,
  showLatestOnlyFlag: true,
  enableDownload: true,
  enablePreview: true,
  enableViewInBrowser: true,
  enableLatestOnlyFlag: true,
  latestOnlyFlag: false,
  styleContainer: {
    mediaCardStyle: { display: 'inline-block', padding: '0px', margin: '0px', width: '225px', height: 'auto' },
    mediaCardBodyStyle: {padding: '12px'},
    mediaTagWrapperDivStyle: {position: 'relative', width: 'auto'},
    mediaTagWrapperOnHoverDivStyle: undefined,
    mediaTagPreviewOverlayStyle: undefined,
    mediaTagPreviewOverlayOnHoverStyle: undefined,
    mediaTagStyle: { maxWidth: '200px', height: 'auto', position: 'relative'},
  },
  enableUploadOfFiles: false,
  uploadFolder: 'ah/animal',
  documentTypesForFilter: [
    {label:'Animal Thumbnail Photo', value:1000260001},
    {label:'Animal Photo Any Angle', value:1000260002},
    {label:'Animal Eartag Photo', value:1000260012},
    {label:'Foreign Body Area', value:1000260013},
    {label:'Animal classification documents', value:1000260016},
  ],
  document_query_information : {
    entity_name: 'animal',
    related_additional_entity_types: ['animal_classification']
  },
  base_entity_information: {
    entity_1_type_id: [1000220002, 1000220003],
    // entity_1_entity_uuid: Array.isArray(selectedRowKeys) && selectedRowKeys.length > 0 ? selectedRowKeys[0] : undefined
  }

}

const dataVerificationAnimalCommentsModalDialogInitialConfiguration = {
  title: 'Data Collection / Verification Animal Comments',
  note_query_configuration: {
    note_type_id: [
      1000440013,
      1000440014
    ],
    entity_1_type_id: 1000220002
  }
}

const AnimalListingForVeterinarians = () => {
  const navigate = useNavigate()
  const location = useLocation()

  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    tableConfiguration.enableTableExport = true
    tableConfiguration.enableTableExportFromCloud = true
  }
  const [reportData, setReportData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const tableRef = useRef()

  const postInitialLoadCB = () => {
    assignDataToFormState(
      snoozeDialogFormDefinition,
      {
        farmer_level_snooze_choice_list: [{reference_name:'Yes', reference_id: 1000105001}, {reference_name:'No', reference_id: 1000105002}],
        // farmer_level_snooze_choice_list: [{label:'Yes', id: 1}, {label:'No', id: 2}],
      },
      snoozeDialogFormState,
      setSnoozeDialogFormState
    )
    /* assignDataToFormState(
      updateStatusDialogFormDefinition,
      {
        data_verification_animal_next_status_list: [
          // {reference_name_l10n:{ul:'Farmer Contacted, Follow up Call Required'},reference_id: 1000490001},
          // {reference_name_l10n:{ul:'Farmer Contacted, Detailed Process Explanation Required'},reference_id: 1000490002},
          // {reference_name_l10n:{ul:'Process Explained, Deposit Not Collected'},reference_id: 1000490003},
          // {reference_name_l10n:{ul:'Deposit Collected, Latest Photos Not Collected'},reference_id: 1000490004},
          // {reference_name_l10n:{ul:'Photos Collected, Other Process Not Completed'},reference_id: 1000490005},
          // {reference_name_l10n:{ul:'Animal Put Up For Sale'},reference_id: 1000490006},
          {reference_name_l10n:'Not Spoken To',reference_id: **********},
          {reference_name_l10n:'Animal Accepted For Assigned Plan',reference_id: **********},
          {reference_name_l10n:'Animal Rejected For Assigned Plan',reference_id: **********},
          // {reference_name_l10n:'Process Explained, Deposit Not Collected',reference_id: 1000490003},
          // {reference_name_l10n:'Deposit Collected, Latest Photos Not Collected',reference_id: 1000490004},
          // {reference_name_l10n:'Photos Collected, Other Process Not Completed',reference_id: 1000490005},
          // {reference_name_l10n:'Animal Put Up For Sale',reference_id: 1000490006},
        ],
        // farmer_level_snooze_choice_list: [{label:'Yes', id: 1}, {label:'No', id: 2}],
      },
      updateStatusDialogFormState,
      setUpdateStatusDialogFormState
    ) */
  }

  const loadInitialFilterDataCB = (additionalParams) => {
    const selectedColumns = tableRef.current.getSelectedColumnsInTable()
    return loadFilters(userToken, selectedColumns, additionalParams)
  }

  const loadReportCB = (reportQueryParams, additionalParams) => {
    if (excludeBDMInQuery) {
      reportQueryParams.excludeBDM = true
    }
    return loadReport(userToken, reportQueryParams, additionalParams)
  }

  const [animalDocumentsMediaCarouselConfiguration, setAnimalDocumentsMediaCarouselConfiguration] = useState(animalDocumentsMediaCarouselInitialConfiguration)
  const handleViewAnimalDocuments = () => {
    if (Array.isArray(selectedRowKeys) && selectedRowKeys.length > 0) {
      const updatedConfiguration = {
        ...animalDocumentsMediaCarouselConfiguration,
        completeInformation: true,
        base_entity_information: {
          ...animalDocumentsMediaCarouselConfiguration.base_entity_information,
          entity_uuid: Array.isArray(selectedRowKeys) && selectedRowKeys.length > 0 ? selectedRowKeys[0] : undefined
        }
      }
      setAnimalDocumentsMediaCarouselConfiguration(updatedConfiguration)
      return true
    } else {
      return false
    }
  }

  const handleSnoozeClick = () => {
    // this is one at a time
    // update state for animal id, ear tag, seller farmer name, seller farmer mobile
    console.log('PSAWL hSC 1, selectedRowKeys = ', selectedRowKeys)
    const selectedRow = reportData.filter((object) => {
      return (object['animal_id'] === selectedRowKeys[0])
    })
    console.log('PSAWL hSC 2, selectedRow = ', selectedRow)
    assignDataToFormState(
      snoozeDialogFormDefinition,
      selectedRow[0],
      snoozeDialogFormState,
      setSnoozeDialogFormState
    )

    // launch popup
    // based on krushalocform
    //    pick up date
    //    pick up if override is at farmer level
    //    pick up comments
    // on save, callback
    // call api
    console.log('PSAWL hSC 1')
    // setSnoozeDialogFormState(snoozeDialogFormInitialState)
    setSnoozeModalDialogVisibility(true)
  }

  const onSnoozeModalOK = async () => {
    try {
      const [currentFormState, validationFailed] = validateFormElements(
        snoozeDialogFormDefinition,
        snoozeDialogFormState,
        setSnoozeDialogFormState
      );
      // console.log("AF1 sF 2, currentFormStateTab0 = ", currentFormStateTab0);
      if (validationFailed) {
        // do something
        console.log("Validation Failed");
        ToastersService.failureToast("Validation failed!");
      } else {
        console.log("you can save now");
        const extractedValues = extractChangedValues(snoozeDialogFormDefinition, currentFormState, ['animalId', 'farmerId'])
        console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValues)
        const animalWithSnoozeInformation = {...extractedValues}
        const headers = {
          useCase: 'Snooze Animal For Data Collection',
          token: userToken.accessToken, //authToken
          refreshtoken: localStorage.getItem("refreshToken")
        }
        // configureParams(extractedValues, navigate,reason)
        // callSnoozeAPI
        const responseWithData = await snoozeHealthScoreAnimals(headers, animalWithSnoozeInformation)
        const response = responseWithData.data
        console.log('AF1 sF 2, response = ', response)
        if (response.return_code === 0) {
          setSnoozeModalDialogVisibility(false)
          ToastersService.successToast("Snoozed Animal Successfully")
          if (tableRef.current) {
            tableRef.current.reloadDataAndMoveToPageOne()
          }
        } else {
          ToastersService.failureToast("Could not snooze")
        }
      }
    } catch (error) {
      console.log('AF1 sF 10, error')
      console.log('AF1 sF 10a, error = ', error)
      ToastersService.failureToast("Could not snooze")
    }
  }

  const onSnoozeModalCancel = () => {
    setSnoozeModalDialogVisibility(false)
  }

  const isNewBuyerFarmerStatusAcceptableFromPrevious = (extractedValues) => {
    const newStatusId = extractedValues.data_verification_animal_next_status[0]
    let currentStatusId = extractedValues.data_verification_animal_current_status_id
    let acceptableStatus = false
    if (currentStatusId) {
      if (typeof currentStatusId === 'string' || currentStatusId instanceof String) {
        currentStatusId = parseInt(currentStatusId)
      }
      switch (currentStatusId) {
        case **********:
          if ([**********, **********].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case **********:
          if ([**********].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case **********:
          if ([**********].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
      }
    } else {
      if ([**********, **********].includes(newStatusId)) {
        acceptableStatus = true
      }
    }
    return acceptableStatus
  }

  /* const onUpdateStatusModalOK = async () => {
    try {
      const [currentFormState, validationFailed] = validateFormElements(
        updateStatusDialogFormDefinition,
        updateStatusDialogFormState,
        setUpdateStatusDialogFormState
      );
      // console.log("AF1 sF 2, currentFormStateTab0 = ", currentFormStateTab0);
      if (validationFailed) {
        // do something
        console.log("Validation Failed");
        ToastersService.failureToast("Validation failed!");
      } else {
        const extractedValues = extractChangedValues(updateStatusDialogFormDefinition, currentFormState, ['animalId'])
        console.log("AF1 sF 1, extractedValues = ", extractedValues)
        const acceptableStatus = isNewBuyerFarmerStatusAcceptableFromPrevious(extractedValues)
        
        if (!acceptableStatus) {
          ToastersService.failureToast("Not an acceptable status")
        } else {
          // update sellable status
          const selectedRow = reportData.filter((object) => {
            return (object['customer_id'] === selectedRowKeys[0])
          })
          const farmerStatusUpdateInformation = {...extractedValues, oc_item_listing_uuid: ocItemListingUUID}
          const headers = {
            useCase: 'Update Animal Data Verification Status',
            token: userToken.accessToken, //authToken
            refreshtoken: localStorage.getItem("refreshToken")
          }
          // configureParams(extractedValues, navigate,reason)
          // callSnoozeAPI
          const responseWithData = await updateBuyerFarmerStatus(headers, farmerStatusUpdateInformation)
          const response = responseWithData.data
          console.log('AF1 sF 2, response = ', response)
          if (response.return_code === 0) {
            setUpdateStatusModalDialogVisibility(false)
            ToastersService.successToast("Status Updated Successfully")
            // await reloadDataAndMoveToPageOne(tableConfiguration)
            if (tableRef.current) {
              tableRef.current.reloadDataAndMoveToPageOne()
            }
  
          } else {
            ToastersService.failureToast("Could not update status of animal")
          }
        }
      }
    } catch (error) {
      console.log('AF1 sF 10, error')
      console.log('AF1 sF 10a, error = ', error)
      ToastersService.failureToast("Could not snooze")
    }
  }

  const onUpdateStatusModalCancel = () => {
    setUpdateStatusModalDialogVisibility(false)
  }

  const handleUpdateDataVerificationStatusOfAnimalClick = () => {
    // this is one at a time
    // update state for animal id, ear tag, seller farmer name, seller farmer mobile
    console.log('PBFFA hUS 1, selectedRowKeys = ', selectedRowKeys)
    const selectedRow = reportData.filter((object) => {
      return (object['animal_id'] === selectedRowKeys[0])
    })
    console.log('PBFFA hUS 2, selectedRow = ', selectedRow)
    assignDataToFormState(
      updateStatusDialogFormDefinition,
      selectedRow[0],
      updateStatusDialogFormState,
      setUpdateStatusDialogFormState
    )

    // launch popup
    // based on krushalocform
    //    pick up status
    //    pick up comments
    // on save, callback
    // call api
    console.log('PBFFA hSC 1')
    setUpdateStatusModalDialogVisibility(true)
  } */

  const enableButton = (selectedRowKeys, reportData, buttonType) => {
    let returnValue = true
    if (selectedRowKeys.length === 1) {
      const selectedRow = reportData.filter((object) => {
        return (object['animal_id'] === selectedRowKeys[0])
      })
      if (selectedRow.length > 0) {
        if (selectedRow[0].listing_status !== 'Listed Animal') {
          returnValue = false
        }
      }
    }
    return returnValue
  }

  const handleViewAnimalClick = (event) => {
    console.log('DCOFSA hVAC 1')
    const selectedRow = reportData.filter((object) => {
      return (object['animal_id'] === selectedRowKeys[0])
    })
    const { pathname, search } = location

    const animalId = selectedRow[0]['animal_id']
    const customerId = selectedRow[0]['customer_id']
    // navigate({
    //   pathname: `../oc/farmer-details`,
    //   search: `?qP=${encodedParamsAsString}`
    // })
    const navigationLink = `/e/farmer-animal-details/${customerId}/animal/${animalId}`
    if (event.ctrlKey || event.metaKey) {
      window.open(navigationLink + '?role=cv', '_blank')
    } else {
      navigate({
        pathname: navigationLink,
        search: `?role=cv`
      })
    }
  }

  const [dataVerificationAnimalCommentsModalDialogConfiguration, setDataVerificationAnimalCommentsModalDialogConfiguration] = useState(dataVerificationAnimalCommentsModalDialogInitialConfiguration)
  const handleViewDataVerificationAnimalCommentsClick = () => {
    const updatedConfiguration = {
      ...dataVerificationAnimalCommentsModalDialogConfiguration,
      note_query_configuration: {
        ...dataVerificationAnimalCommentsModalDialogConfiguration.note_query_configuration,
        entity_1_uuid: selectedRowKeys[0]
      }
    }
    setDataVerificationAnimalCommentsModalDialogConfiguration(updatedConfiguration)
  }

  const [snoozeModalDialogVisibility, setSnoozeModalDialogVisibility] = useState(false)
  // const [updateStatusModalDialogVisibility, setUpdateStatusModalDialogVisibility] = useState(false)

  const [snoozeDialogFormState, setSnoozeDialogFormState] = useState(snoozeDialogFormInitialState)
  // const [updateStatusDialogFormState, setUpdateStatusDialogFormState] = useState(updateStatusDialogFormDefinition)

  const [excludeBDMInQuery, setExcludeBDMInQuery] = useState(true)

  return (
    <>
      <Modal
        title="Snooze Animal"
        open={snoozeModalDialogVisibility}
        onOk={onSnoozeModalOK}
        onCancel={onSnoozeModalCancel}
        >
          <>
            <KrushalOCForm
              formState={snoozeDialogFormState}
              setFormState={setSnoozeDialogFormState}
              formDefinition={snoozeDialogFormDefinition}
            />
          </>
      </Modal>
      {/* <Modal
        title="Update Status"
        open={updateStatusModalDialogVisibility}
        onOk={onUpdateStatusModalOK}
        onCancel={onUpdateStatusModalCancel}
        >
          <>
            <KrushalOCForm
              formState={updateStatusDialogFormState}
              setFormState={setUpdateStatusDialogFormState}
              formDefinition={updateStatusDialogFormDefinition}
            />
          </>
      </Modal> */}
      <Space style={{marginTop:'8px', marginBottom:'8px'}}>
        <Link to="/ahv">Back to Queue List</Link>
        <MediaCarouselModalButton
          buttonTitle="View Animal Documents"
          disabled={enableButton(selectedRowKeys, reportData, 'ANIMALDOCUMENTS')}
          onClickHandler={handleViewAnimalDocuments}
          modalDialogTitle="Animal Documents"
          configuration={animalDocumentsMediaCarouselConfiguration}
          />
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'ANIMALDETAILS')} onClick={handleViewAnimalClick}>View Animal Details</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'SNOOZE')} onClick={handleSnoozeClick}>Snooze</Button>
        {/* <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateDataVerificationStatusOfAnimalClick}>Update Animal Data Verification Status</Button> */}
        <CommentViewerModalButton
          buttonTitle="View Data Collection and Verification Animal Comments"
          disabled={enableButton(selectedRowKeys, reportData, 'COMMENTS')}
          onClickHandler={handleViewDataVerificationAnimalCommentsClick}
          configuration={dataVerificationAnimalCommentsModalDialogConfiguration}
          />
        <Checkbox
          checked={excludeBDMInQuery}
          onChange={() => {
            setExcludeBDMInQuery(!excludeBDMInQuery)
          }}
          >
            Exclude BDM From Query
        </Checkbox>
        <ReloadOutlined onClick={() => {
          if (tableRef.current) {
            tableRef.current.reloadDataAndMoveToPageOne()
          }
        }}/>
      </Space>
      <OCTable
        /* tableKey={tableKey}
        setTableKey={setTableKey} */
        ref={tableRef}
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={tableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        postInitialLoadCallback={postInitialLoadCB}
        loadReportCallback={loadReportCB}
        loadInitialFilterDataCallback={loadInitialFilterDataCB}
        parentPageParams={pageParams}
        // showTableHeader={true}
        // omniSearchRowEnabled={true}
        // omniSearchRowBeginning={backToQueueListLinkUI}
        // omniSearchRowMiddle={viewAnimalDocumentsFarmerSnoozeAndFarmerUpdateStatusButtons}
        // actionsRow={backToQueueListLinkUIViewAnimalDocumentsFarmerSnoozeAndFarmerUpdateStatusButtons}
        // enableColumnConfiguration={true}
      />
    </>
  );
}

// export default AnimalListingForVeterinarians
export default withAuthorization(AnimalListingForVeterinarians, [VET_OPS, CENTRAL_VET])