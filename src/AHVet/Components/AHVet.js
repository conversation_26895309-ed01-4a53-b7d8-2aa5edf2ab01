import { Link } from 'react-router-dom';

import { Card, Row, Col } from 'antd'

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, CENTRAL_VET, VET_OPS, OPS_TEAM, SALES_OPS, IT_ADMIN, withAuthorization} = require('../../Components/user/authorize')

const AHVet = () => {
  return (
    <>
    <Row>
      <Col span={3}>
        <Link to="/ahv/onboarding-animal-health-score-verification">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Animal Onboarding Health Score Verification
          </Card>
        </Link>
      </Col>
      <Col span={3}>
        <Link to="/ahv/periodic-animal-health-score-reverification">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Animal Periodic Health Score Verification
          </Card>
        </Link>
      </Col>
    </Row>
    <Row>
      <Col span={3}>
        <Link to="medicine-list">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Manage Medicines
          </Card>
        </Link>
      </Col>
    </Row>
  </>
  )
}

// export default AHVet
export default withAuthorization(AHVet, [CS_TEAM, VET_OPS, CENTRAL_VET, IT_ADMIN])