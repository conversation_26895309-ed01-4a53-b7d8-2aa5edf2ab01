const { /* get,  */put, /* post,  */configurationJSON, KrushalError } = require('@krushal-it/common-core')
const { post, get } = require('../routerhelper')

const getHealthScoreForOnboardedAnimalsReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gPSAWLR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/onboarding-animals-health-score-verification`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/animal-list`
    console.log('r gPSAWLR  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gPSAWLR 10, error')
    console.log('r gPSAWLR 10a, error = ', error)
    throw error
  }
}

const getHealthScoreForOnboardedAnimalsReportFilterData = async (headers) => {
  try {
    console.log('r gFWLRRFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/onboarding-animals-health-score-verification`
    console.log('r gFWLRRFD  2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRRFD 10, error')
    console.log('r gFWLRRFD 10a, error = ', error)
    throw error
  }
}

const snoozeHealthScoreAnimals = async (headers, animalWithSnoozeInformation) => {
  try {
    console.log('r sFSA 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/snooze-animal-for-data-collection`
    console.log('r sFSA  2, url = ', url)
    const response = await post(url, headers, animalWithSnoozeInformation)
    return response
  } catch (error) {
    console.log('r sFSA 10, error')
    console.log('r sFSA 10a, error = ', error)
    throw error
  }
}

const unsnoozeSellableAnimals = async (headers, toBeSnoozeAnimalInformation) => {
  try {
    console.log('r sFSA 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/unsnooze-animal-for-data-collection`
    console.log('r sFSA  2, url = ', url)
    const response = await post(url, headers, toBeSnoozeAnimalInformation)
    return response
  } catch (error) {
    console.log('r sFSA 10, error')
    console.log('r sFSA 10a, error = ', error)
    throw error
  }
}

const updateStatusOfHealthScoreAnimals = async (headers, animalUpdateStatusData) => {
  try {
    console.log('r uSAS 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/update-animal-selling-status`
    console.log('r uSAS  2, url = ', url)
    const response = await post(url, headers, animalUpdateStatusData)
    return response
  } catch (error) {
    console.log('r uSAS 10, error')
    console.log('r uSAS 10a, error = ', error)
    throw error
  }
}

const getMedicineReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gMR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/onboarding-animals-health-score-verification`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/r/medicine-list`
    console.log('r gMR  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gMR 10, error')
    console.log('r gMR 10a, error = ', error)
    throw error
  }
}

const getMedicineReportFilterData = async (headers) => {
  try {
    console.log('r gMRFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/r/medicine-list`
    console.log('r gMRFD  2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gMRFD 10, error')
    console.log('r gMRFD 10a, error = ', error)
    throw error
  }
}

export {
  getHealthScoreForOnboardedAnimalsReport, getHealthScoreForOnboardedAnimalsReportFilterData,
  snoozeHealthScoreAnimals, updateStatusOfHealthScoreAnimals,
  getMedicineReport, getMedicineReportFilterData
}