// import ImgsViewer from "react-images-viewer";

// const ImageViewer = (IMG_SET) => {
//   return (
//     <ImgsViewer
//         // imgs={[
//         //   { src: "http://example.com/img1.jpg" },
//         //   { src: "http://example.com/img2.png" },
//         // ]}
//       imgs={IMG_SET}
//       currImg={this.state.currImg}
//       isOpen={this.state.viewerIsOpen}
//       onClickPrev={this.gotoPrevious}
//       onClickNext={this.gotoNext}
//       onClose={this.closeViewer}
//     />
//   );
// };

// export default ImageViewer;