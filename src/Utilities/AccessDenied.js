import { Button, Container } from "react-bootstrap";
import { useNavigate } from "react-router-dom";

const AccessDenied = () => {
  // const navigate = useNavigate();

  return (
    <>
      <Container className="py-5">
        <div className="text-center" style={{ marginTop: "5rem" }}>
          {/* <h1 className="mb-2" style={{ fontWeight: 800 }}>
            403
          </h1> */}
          <div className="text-center mb-5">
            <img
              alt=""
              src={process.env.PUBLIC_URL + "krushal.png"}
              width="140px"
            />
          </div>
          <h5 style={{ fontWeight: 600, color: "#4c4c4c" }}>Access Denied!</h5>
          <p style={{ fontWeight: 600, color: "gray" }}>
            You are not allowed to access this portal.
          </p>
          {/* <Button
            variant="primary"
            className="mt-3"
            style={{
              backgroundColor: "#673ab7",
              color: "white",
              border: "none",
            }}
            onClick={() => {
              navigate("/login");
              window.location.reload();
            }}
          >
            <i className="fa fa-arrow-left"></i> &nbsp; Back to Login
          </Button> */}
        </div>
      </Container>
    </>
  );
};

export default AccessDenied;
