import { Box, Stack, Typography } from "@mui/material";
import packageJson from "../../package.json";
import { Image } from "antd";
import krushal from "../Components/Dashboard/Tasks/TaskItem/krushal.png";

const Version = () => {
  return (
    <Stack
      sx={{
        backgroundColor: "#111927",
        backgroundImage:
          "radial-gradient(at 47% 33%, hsl(261.60, 52%, 47%) 0, transparent 59%),radial-gradient(at 82% 65%, hsl(14.25, 83%, 56.99999999999999%) 0, transparent 55%)",
      }}
      height="100vh"
      justifyContent="center"
      alignItems="center"
    >
      <Box
        sx={{
          backdropFilter: "blur(16px) saturate(180%)",
          backgroundColor: "rgba(190, 189, 189, 0.75)",
          borderRadius: "12px",
          border: "1px solid rgba(255, 255, 255, 0.125)",
          color: "#cccccc",
          height: 200,
          width: 400,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          flexDirection: "column",
        }}
      >
        <Image src={krushal} height={100} width={300} preview={false} />
        <Typography mt={1} sx={{ color: "#111" }}>
          App Version {packageJson.version}
        </Typography>
      </Box>
    </Stack>
  );
};

export default Version;
