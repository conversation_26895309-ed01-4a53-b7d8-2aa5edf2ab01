/* 
.round{
    height: 150px;
    width: 150px;
    background: rgb(124, 198, 244);
    border-radius: 50%;
} */
.round {
    height: 150px;
    width: 150px;
    background: rgb(124, 198, 244);
    border-radius: 50%;
    box-shadow: 0px 8px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease 0s;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

.round:hover {
    background-color: rgb(114, 188, 234);
    box-shadow: 0px 15px 20px rgba(46, 229, 157, 0.4);
    color: #fff;
    transform: translateY(-7px);
}

.centerDiv{
    display: flex;
    justify-content: center;
    align-items: center;
}
.name_text{
    text-align: center;
    margin-top: 20px;
    font-weight: 700;
    color: #111;
}
.name_text_modified{
    display: flex;
    flex-wrap: nowrap;
}
.modified_by{
    font-size: 14px;
    font-weight: 600;
}
.rec_box{
    /* padding: 20px; */
    height: 100px;
    width: 250px;
    background: rgb(124, 198, 244);
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    transition: all 0.3s ease 0s;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 1rem;
}
.rec_box:hover {
    background-color: rgb(114, 188, 234);
    box-shadow: 0px 15px 20px rgba(46, 229, 157, 0.4);
    color: #fff;
    transform: translateY(-7px);
}