import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";
import { TextareaAutosize } from "@mui/material";
import { useMemo } from "react";

const ReasonModal = (props) => {
  const { isModalOpen, handleCancel, setReason, reasonCreateHandler, isMandatory } = props;

  const memoizedValue = useMemo(
    () => (
      <Modal
        show={isModalOpen}
        onHide={handleCancel}
        centered
        backdrop="static"
      >
        {/* <Modal.Header>
          <small>Please enter the reason</small>
        </Modal.Header> */}
        <Modal.Body>
          <div>
            <small>Please enter the notes{isMandatory ? "*":""}</small>
          </div>
          <TextareaAutosize
            type="input"
            className="input-field"
            style={{ width: "100%" }}
            onChange={(e) => setReason(e.target.value)}
          />
          <div
            style={{
              display: "flex",
              alignItems: "flex-end",
              justifyContent: "flex-end",
              padding: "10px",
            }}
          >
            <Button variant="secondary" onClick={handleCancel}>
              Close
            </Button>
            <Button
              variant="primary"
              onClick={reasonCreateHandler}
              style={{ marginLeft: "10px" }}
            >
              Continue
            </Button>
          </div>
        </Modal.Body>
      </Modal>
    ),
    [isModalOpen, handleCancel, setReason, reasonCreateHandler, isMandatory]
  );

  return memoizedValue;
};

export default ReasonModal;
