import {useState} from "react";
import { LoadingOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { message, Modal, Button } from 'antd';
import ToastersService from "../../Services/toasters.service";
import { instance } from "../../Services/api.service";
import {useNavigate} from "react-router-dom";
import resizeFile from "../../Services/file-resizer.service";
import {useDispatch,useSelector} from "react-redux";
import { clearUserToken } from "../../Components/user/action";



const getBase64 = (img, callback) => {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result));
  reader.readAsDataURL(img);
};
const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('You can only upload JPG/PNG file!');
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('Image must smaller than 2MB!');
  }
  return isJpgOrPng && isLt2M;
};
const ImageUploaderAntd = (props) => {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState();
  const [image,setImage] = useState();
  const [show,setShow] = useState(false)
  const navigate = useNavigate();
  const [fileObj,setFileObj] = useState("");
  const dispatch = useDispatch();
  const userToken = useSelector((state)=>state.user.userToken)

  const uploadImageToServer = async (formData) => {
    try {
      // const response = await axios.post(BASE_URL + "/document", formData, {
      //   headers: {
      //     token: localStorage.getItem("accessToken"),
      //     "X-App-Id": "123456789"
      //   },
      // });
      const response = await instance({
        url: "/document",
        method: "POST",
        data: formData,
        headers: {
          token: userToken.accessToken,
        },
      });
      console.log("File upload response", response);
      if (response.status === 201) {
        setShow(false)
        props.setProfileUrl(response?.data?.data?.doc_key)
        ToastersService.successToast("Image uploaded to server!");
        // const reqBody = {
        //   document_entries: [
        //     {
        //       document_type: 'staff_thumbnail_photo_1',
        //       entity: "pay_per_use_user",
        //       entity_1_id: "",
        //       url: response.data.data.doc_key,
        //       document_information: "",
        //     },
        //   ],
        // };
        // console.log("ReqBody", reqBody);
        // changeProfilePic(reqBody);

        // changeProfilePic(
        //   "document_type": "farm_photo_1",
        //   "entity": "farmer",
        //   "entity_1_id": 583,
        //   "url": " frshrTech/test/167272720397452cf3be8143fbcdf.jpg",
        //   "document_information": " test"
        // )
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate,'/login', 'Session Expired!'))
      }
      if (error.response.status === 413) {
        ToastersService.failureToast(error.response.data.error ? error.response.data.error : 'File size limit exceeded!');
      }
      // setCreatingTaskLoader(false);
    }
  }
  
  const handleImageChange = async (event) => {
    const file = event.target.files[0];
    const acceptedImageTypes = ['image/jpeg', 'image/png'];

    if (file && acceptedImageTypes.includes(file['type'])) {
      setImage(event.target.files[0]);
      const fileObjs = event.target.files && await resizeFile(event.target.files[0]);
      setFileObj(fileObjs)
      if (!fileObjs) {
        return;
      }
      console.log("fileObj is", fileObjs);
    }else {
      ToastersService.failureToast("File is not an image. Please select a .jpeg or .png file.")
    }
  };
console.log(fileObj)
  const onOkHandler = ()=>{ 
    if (image) {
      let formData = new FormData();
    formData.append("file", fileObj);
    formData.append('entity', 'staff');
    uploadImageToServer(formData);
    } else {
      ToastersService.failureToast("Please select the image!")
    }
  }
  const removeImageHandler = () => {
   let image = undefined;
    console.log("Image", image);
    setImage(image);
  };
  return (
    <>
        <Button type="primary" icon={<UploadOutlined />} size="large" onClick={()=>setShow(true)}>
            Upload Staff Image
          </Button>
          <Modal title="Staff Image" open={show} onCancel={()=>setShow(false)} onOk={onOkHandler}>
          <input
            className="input-field"
            style={{ width: "100%" }}
            type="file"
            onChange={handleImageChange}
            accept="image/jpeg,image/png"
          />
          {image?.type?.split("/")[0] == "image" && (
                <div className="add-farmer__img-div">
                  <div
                    className="add-farmer__remove-img-btn"
                    onClick={() => removeImageHandler()}
                  >
                    <i className="fa fa-times"></i>
                  </div>
                  <img
                    src={URL.createObjectURL(image)}
                    alt=""
                    className="new-img"
                  />
                </div>
              )}
          </Modal>
      {/* <Upload
        name="avatar"
        listType="picture-circle"
        className="avatar-uploader"
        showUploadList={false}
        action="https://run.mocky.io/v3/435e224c-44fb-4773-9faf-380c5e6a2188"
        beforeUpload={beforeUpload}
        onChange={handleChange}
      >
        {imageUrl ? (
          <img
            src={imageUrl}
            alt="avatar"
            style={{
              width: '100%',
            }}
          />
        ) : (
          uploadButton
        )}
      </Upload> */}
    </>
  );
};
export default ImageUploaderAntd;