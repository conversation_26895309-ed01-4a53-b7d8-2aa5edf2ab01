const commonRegexes = {
  IndiaMobileNumber: "^[6789]\\d{9}$",
  A<PERSON>haarNumber: "^[123456789]\\d{11}$",
  // PositiveDecimal: "^([0-9]+(?:[\\.][0-9]*)?|\\.[0-9]+)$",
  PositiveDecimal: "^\\s*$|^([0-9]+(?:[\\.][0-9]*)?|\\.[0-9]+)$",
  AnyDecimal: "^\\s*$|^[-+]?([0-9]+(?:[\\.][0-9]*)?|\\.[0-9]+)$",
  AnyNumber: "^-?[1-9]\\d*$",
  PositiveNumber: "^[1-9]\\d*$",
  Name : "^(?!.*  )[a-zA-Z-' ]+$", // only letters, spaces, hyphens, and apostrophes.
  Email: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$", // normal email address validation,
  PanNumber : "^[A-Z]{5}[0-9]{4}[A-Z]{1}$"
}

const generateErrorMessage = (validationRegexReference, value) => {
  let errorMessage = ''
  switch (validationRegexReference) {
    case 'IndiaMobileNumber': {
      if (value.length === 10) {
        errorMessage = 'Is not a valid mobile number'
      } else if (value.length === 0) {
      } else if (value.length > 0 && value.length !== 10) {
        errorMessage = 'A mobile number should be 10 digits'
      }
    }
    break
    case 'HttpsLink': {
      let url
      try {
        url = new URL(value)
        if (url.protocol !== 'https:') {
          errorMessage = 'Please enter a valid https url'
        }
      } catch (error) {
        errorMessage = 'Please enter a valid https url'
      }
    }
    break
  }
  return errorMessage
}

module.exports = {commonRegexes, generateErrorMessage}
