import React, { useState, useEffect } from 'react';
import style from "./scrollup.module.css";
import {Button} from "antd";
import {ArrowUpOutlined} from "@ant-design/icons";

const ScrollupButton = () => {
    const [scrollStyle, setScrollStyle] = useState(style.scroll_up_button_none);
    const scrollupHandler = ()=>{
        window.scrollTo(0,0)
    }

    useEffect(() => {
        const checkScroll = () => {
            if (window.scrollY > 500) {
                setScrollStyle(style.scroll_up_button);
            } else {
                setScrollStyle(style.scroll_up_button_none);
            }
        };

        // Listen for scroll events
        window.addEventListener('scroll', checkScroll);

        // Clean up the event listener when the component is unmounted
        return () => {
            window.removeEventListener('scroll', checkScroll);
        };
    }, []);

    return (
        <div className={scrollStyle}>
            <Button onClick={scrollupHandler} type="primary" shape="circle" icon={<ArrowUpOutlined />} />
        </div>
    );
}

export default ScrollupButton;
