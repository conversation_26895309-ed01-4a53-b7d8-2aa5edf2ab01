import { instance } from "../../Services/api.service";
import { clearUserToken } from "../../Components/user/action";


export const getCareCalendarReferencesData = async (dispatch,navigate)=>{
    try {
      const response = await instance({
        url: "/references/care-calendar",
        method: "GET",
        // data: JSON.stringify(reqBody),
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      if (response?.data?.result === true) {
        // global.referencesWithoutCategoryId.referenceMap = response?.data?.data
        return response?.data?.data
      }
    } catch (error) {
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  }

export const loadReferenceForMedicine = async (dispatch,navigate)=>{
    try {
        const response = await instance({
            url : "/medicine",
            method : "GET",
            headers: {
                token: localStorage.getItem("accessToken"),
              },
        })
        if (response.status === 200) {
            return response.data
        } else {
            console.log("something wrong")
        }
    } catch (error) {
        if (error?.response?.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
          }
    }
}
