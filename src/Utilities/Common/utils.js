import moment from "moment";
import dayjs from 'dayjs';
// for converting date and time format
const { assignL10NObjectOrObjectArrayToItSelf } = require('@krushal-it/common-core')

export const convertGMTDateToISTDate = (date) => {
  const givenDate = new Date(date);
  const istOffset = 5.5;
  const gmtDate = new Date(givenDate.getTime() + istOffset * 60 * 60 * 1000);
  return gmtDate;
};
//  export const convertUtcToIst = (utcDateTime) => {
//     const utcDate = new Date(utcDateTime);
//     const istOffset = 5.5 * 60 * 60 * 1000; // IST offset in milliseconds
//     const istDate = new Date(utcDate.getTime() + istOffset);
//     return istDate.toISOString().slice(0, 16);
//   };
export const convertUtcToIst = (utcDateTime) => {
  const utcDate = new Date(utcDateTime);
  const istOffset = 5.5 * 60 * 60 * 1000; // IST offset in milliseconds
  const istDate = new Date(utcDate.getTime());
  return istDate;
};
export const convertUtcToIstFormatted = (utcDateTime) => {
  const utcDate = moment.utc(utcDateTime);
  const istDate = utcDate.add(5.5, "hours");
  return istDate.format("DD MMM YYYY hh:mm A");
};

// helper for table

export const tableHeader = (data) => {
  return (
    <thead>
      <tr>
        {data.map((item, index) => (
          <th key={index}>{item}</th>
        ))}
      </tr>
    </thead>
  );
};

export const tableBody = (data) => {
  return (
    <tbody>
      {data.map((row, index) => {
        return (
          <tr key={index}>
            {row.map((item, i) => {
              return <td key={i}>{item}</td>;
            })}
          </tr>
        );
      })}
    </tbody>
  );
};
// html content for html tags
export const htmlContent = (data) => {
  return (
    <div>
      {data.map((item, index) => (
        <div key={index}>
          {/* Render the ul value as HTML */}
          <div dangerouslySetInnerHTML={{ __html: item.note.ul }} />
        </div>
      ))}
    </div>
  );
};
// remove html tags from string
export const removeHtmlTags = (input) => {
  return input.replace(/<[^>]*>/g, "");
};
//   export const Box =({ children })=> {
//     const style = {
//         border: '1px solid black',
//         padding: '10px',
//         margin: '10px'
//     };

//     return <div style={style}>{children}</div>;
// }
export const Box = ({ children }) => {
  const boxStyle = {
    border: "1px solid #ccc",
    padding: "20px",
    margin: "10px",
    // maxHeight: '150px',
    // overflow: 'auto',
    borderRadius: "10px",
    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
    backgroundColor: "#f8f8f8",
    color: "#333",
  };

  return <div style={boxStyle}>{children}</div>;
};

export const formatedDate = (dateString) => {
  return moment(dateString).format("DD MMM YYYY, hh:mm A");
};

export const getRandomColor = () => {
  const letters = "0123456789ABCDEF";
  let color = "#";
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

// regular expressions
export const notesPattern = new RegExp(
  "^[A-Za-z0-9!@#$%^&*()_+=-\\s]*[A-Za-z][A-Za-z0-9!@#$%^&*()_+=-\\s]*$"
);

//common vide extensions

export const videoExtensions = [
  ".mp4", // MPEG-4 Part 14
  ".mkv", // Matroska
  ".flv", // Flash Video
  ".avi", // Audio Video Interleave
  ".mov", // Apple QuickTime Movie
  ".wmv", // Windows Media Video
  ".rm", // RealMedia
  ".rmvb", // RealMedia Variable Bitrate
  ".webm", // WebM
  ".vob", // DVD Video Object
  ".ogv", // Ogg Video
  ".m4v", // MPEG-4 Video
];

export const parseAndAssign = (obj, keys) => {
  return keys.reduce((acc, key) => {
    acc[key] = [parseFloat(obj[key])];
    return acc;
  }, {});
};


export const globalReferencesMapper = (categoryId) =>{
  if (typeof(global?.references?.referenceCategoryToReferencesMap) === 'object' && global?.references?.referenceCategoryToReferencesMap?.hasOwnProperty(categoryId)) {
    const referenceDataWithLabelInJSON = global?.references?.referenceCategoryToReferencesMap[categoryId]
    const referenceDataWithLabel =  assignL10NObjectOrObjectArrayToItSelf(referenceDataWithLabelInJSON, ['reference_name_l10n'])
    return referenceDataWithLabel
  }else{
    return []
  }
}
export function extractBasedOnLanguageMod(e, t) {
  if (t === undefined) {
    t = "en";
  }
  if (e === null || e === undefined) {
    return "";
  }
  if (typeof e === "object") {
    if (e.ul !== undefined && e.ul !== null) {
      return e.ul;
    } else if (e[t] !== undefined && e[t] !== null) {
      return e[t];
    } else if (e.en !== undefined && e.en !== null) {
      return e.en;
    } else {
      return "";
    }
  } else {
    return e;
  }
}


export const getMedicineDetailsByMedId = (medicineId) => {
  return global?.medicineReferences[medicineId];
};
export const unitMappingByMedId = (medicineId) => {
  const getMedicineUnitRefId = getMedicineDetailsByMedId(medicineId);
  const medicineUnitId = getMedicineUnitRefId?.medicine_unit
    ? getMedicineUnitRefId?.medicine_unit[0]
    : null;
  const getUnitName = globalReferencesMapper(10009500)?.filter(
    (item) => item?.reference_id === medicineUnitId
  );

  // Check if getUnitName is not empty
  if (getUnitName && getUnitName.length > 0) {
    return getUnitName[0]?.reference_name_l10n;
  } else {
    // console.error("No unit name found for the given medicine ID");
    return ""
  }
};

export const removeUTC = (dateString)=>{
  const date = dayjs(dateString);
  const formattedDate = date.format('YYYY-MM-DD HH:mm:ss');
  return formattedDate
}
