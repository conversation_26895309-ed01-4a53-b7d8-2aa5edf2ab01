import { Menu, Checkbox, Dropdown, Button } from "antd";
import DvrIcon from '@mui/icons-material/Dvr';

const ColumnDisplayer = (props) => {
  const dropdownHandler = (value) => {
    props.setFilteredColumns((prevState) => {
      if (prevState.includes(value)) {
        return prevState.filter((col) => col !== value);
      } else {
        return [...prevState, value];
      }
    });
  };

  const menu = () => {
    return (
      <Menu>
        {props.columns.map((item, index) => (
          <Menu.Item key={index}>
            <Checkbox
              defaultChecked={!props.columns?.includes(item.title)}
              onChange={() => dropdownHandler(item.title)}
            >
              {item.title}
            </Checkbox>
          </Menu.Item>
        ))}
      </Menu>
    );
  };

  return (
    <div>
    <Dropdown overlay={menu} trigger={['click']}>
      <Button style={{backgroundColor:"#673ab7",color:"white"}} icon={<DvrIcon color="white"/>}>
        Display columns
      </Button>
    </Dropdown>
  </div>
  );
};

export default ColumnDisplayer;
