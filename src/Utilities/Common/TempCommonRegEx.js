 // Regular expressions for validation
 export const nameRegex = /^(?!.*  )[a-zA-Z-' ]+$/; // Only letters and white space allowed 
 export const emailRegex = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/; // Standard email format
 export const mobileRegex = /^(\+91)?[0-9]{10}$/; // Only 10 digit numbers allowed
 export const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/
 export const aadharRegex =  /^[1-9]\d{11}$/

