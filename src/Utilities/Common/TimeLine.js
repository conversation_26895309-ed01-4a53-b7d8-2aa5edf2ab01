import * as React from "react";
import Timeline from "@mui/lab/Timeline";
import TimelineItem from "@mui/lab/TimelineItem";
import TimelineSeparator from "@mui/lab/TimelineSeparator";
import TimelineConnector from "@mui/lab/TimelineConnector";
import TimelineContent from "@mui/lab/TimelineContent";
import TimelineOppositeContent from "@mui/lab/TimelineOppositeContent";
import TimelineDot from "@mui/lab/TimelineDot";
import AccessTimeFilledIcon from "@mui/icons-material/AccessTimeFilled";
import Typography from "@mui/material/Typography";
import { formatedDate, getRandomColor } from "./utils";
import style from "./timeLine.module.css";
import { useSelector } from "react-redux";
import ScrollupButton from "./ScrollupButton";
const { extractBasedOnLanguage } = require("@krushal-it/common-core");

const TimeLine = (props) => {
  const { timeLine ,staffName, staffRole} = props;
  const userLanguage = useSelector((state)=>state.user.userLanguage);
  
  const statusMapping = {
    1000100001: "Active",
    1000100002: "Inactive",
    1000230001: "Router Officer",
    1000230002: "BackOffice Person",
    1000230003: "Vet",
    1000230004: "Paravet",
    1000230005: "Zonal Officer",
  };

  const modifiedResponse = (data) => {
    const regex = new RegExp(Object.keys(statusMapping).join("|"), "g");
    return data.replace(regex, (matched) => {
      return statusMapping[matched];
    });
  };
  const timeLineJsx = () => {
    return (
      <>
        <div className={style.centerDiv}>
          <div className={style.rec_box}>
            <div style={{display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column"}}>
            <p className={style.name_text}>{extractBasedOnLanguage(staffName,userLanguage)}</p>
            <p>{extractBasedOnLanguage(staffRole,userLanguage)}</p>
            </div>
          </div>
        </div>
        <Timeline position="alternate">
          {timeLine.map((item, index) => (
            <TimelineItem key={index}>
              <TimelineOppositeContent
                sx={{
                  m: "auto 0",
                }}
                align="right"
                variant="body2"
                color="#673AB7"
              >
                {formatedDate(item.modified_on)}
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineConnector />
                <TimelineDot sx={{ bgcolor: getRandomColor() }}>
                  <AccessTimeFilledIcon style={{ color: "white" }} />
                </TimelineDot>
                <TimelineConnector />
              </TimelineSeparator>
              <TimelineContent sx={{ py: "12px", px: 2 }}>
                <Typography variant="h6" component="span" color="#ec6237">
                  {modifiedResponse(item.activity)}
                </Typography>
                <Typography
                  sx={{ fontSize: "14px", color: "grey", fontWeight: 400 }}
                >
                  {item.activity_notes_l10n
                    ? extractBasedOnLanguage(JSON.parse(item.activity_notes_l10n),userLanguage)
                    : ""}
                </Typography>
                <Typography
                  sx={{ fontSize: "14px", color: "grey", fontWeight: 400 }}
                >
                  <span className={style.modified_by}>Modified by : </span>
                  {/* {item.modified_by.en ||
                    item.modified_by.hi ||
                    item.modified_by.mr} */}
                    {extractBasedOnLanguage(item.modified_by,userLanguage)}
                </Typography>
              </TimelineContent>
            </TimelineItem>
          ))}
        </Timeline>
        <ScrollupButton/>
      </>
    );
  };

  return <>{timeLineJsx()}</>;
};

export default TimeLine;
