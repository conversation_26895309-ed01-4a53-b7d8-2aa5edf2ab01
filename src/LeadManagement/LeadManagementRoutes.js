import {
  Routes,
  Route,
} from "react-router-dom";

import Sidebar from '../Components/Sidebar/Sidebar'

import LeadManagement from "./Components/LeadManagement";
import DirectlyContactedFarmerLeads from './Components/DirectlyContactedFarmerLeads'

const LeadManagementRoutes = () => {
  return (
    <Routes>
      <Route path="lm" element={<Sidebar content={<LeadManagement />} />} />
      <Route path="lm/directly-conctacted-farmer-leads" element={<Sidebar content={<DirectlyContactedFarmerLeads />} />} />
    </Routes>
  )
}

export default LeadManagementRoutes