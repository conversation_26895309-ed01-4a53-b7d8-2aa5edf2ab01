import { Link } from 'react-router-dom';

import { Card, Row, Col } from 'antd'

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, IT_ADMIN, withAuthorization} = require('../../Components/user/authorize')

const LeadManagement = () => {
  return (
    <>
    <Row>
      <Col span={3}>
        <Link to="/lm/farmer-no-location-recorded">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            FB Leads
          </Card>
        </Link>
      </Col>
      <Col span={3}>
        <Link to="/lm/directly-conctacted-farmer-leads">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Directly Contacted Farmer Leads
          </Card>
        </Link>
      </Col>
    </Row>
  </>
  )
}

// export default LeadManagement
export default withAuthorization(LeadManagement, [CS_TEAM])