import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
const { loadConfiguration, configurationJSON } = require('@krushal-it/common-core')

loadConfiguration(process.env.REACT_APP_ENV_JSON)


// const firebaseConfig = {
//   apiKey: "AIzaSyDDKUKv7e36yU3jAruX6bsspCadbYbn6kg",
//   authDomain: "krushal-paravet-pwa.firebaseapp.com",
//   projectId: "krushal-paravet-pwa",
//   storageBucket: "krushal-paravet-pwa.appspot.com",
//   messagingSenderId: "559289408623", 
//   appId: "1:559289408623:web:68723e794605b1e1bbc19a"
// };

// const firebaseConfig = {
//   apiKey: "AIzaSyCQfCwNkwO6RL_4_iBIw286X5NftPWsMsg",
//   authDomain: "stage1-app.firebaseapp.com",
//   projectId: "stage1-app",
//   storageBucket: "stage1-app.appspot.com",
//   messagingSenderId: "842244074850",
//   appId: "1:842244074850:web:58e38202aebc327240903b"
// };

const firebaseConfig = configurationJSON().FIREBASE_AUTH
// console.log('firebaseConfig = ', firebaseConfig)
// console.log('firebaseConfig1 = ', firebaseConfig1)

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);