const logger = require("./logger");
const {parse, stringify, toJSO<PERSON>, fromJSON} = require('flatted');
var path = require("path");

const logLevels = {
  ERROR: "error",
  WARN: "warn",
  INFO: "info",
  VERBOSE: "verbose",
  DEBUG: "debug",
};

const getStackInfo = (stackIndex) => {
  const stacklist = new Error().stack.split("\n").slice(3);
  const stackReg = /at\s+(.*)\s+\((.*):(\d*):(\d*)\)/gi;
  const stackReg2 = /at\s+()(.*):(\d*):(\d*)/gi;
  const s = stacklist[stackIndex] || stacklist[0];
  const sp = stackReg.exec(s) || stackReg2.exec(s);

  if (sp) {
    return {
      method: sp[1],
      relativePath: sp[2],
      line: sp[3],
      file: path.basename(sp[2]),
    };
  }
};

const formatLogArguments = (args) => {
  args = Array.prototype.slice.call(args);

  const stackInfo = getStackInfo(1);

  if (stackInfo) {
    const calleeStr =
      "[" +
      stackInfo.relativePath +
      " " +
      stackInfo.method +
      ":" +
      stackInfo.line +
      "]";

    if (typeof args[0] === "string") {
      args[0] = calleeStr + " " + args[0];
    } else {
      args.unshift(calleeStr);
    }
  }
  return args;
};

const extractDataFromArgs = (...args) => {
  let loggingArray = [];
  if (args.length > 2) {
    for (let i = 1; i < args.length; i++) {
      loggingArray.push(args[i]);
    }
  } else {
    loggingArray = args[1];
  }
  const data = loggingArray;
  const dataStringified = stringify(data)
  return dataStringified
};

const logHandler = (logLevel, ...args) => {
  const filepathandlinenumber = formatLogArguments(...args);
  let message = args[0];
  // let params = args[1];
  let params = undefined
  let data = extractDataFromArgs(...args);
  if (params && params.headers && params.headers["x-correlation-id"]) {
    return createLog(
      logLevel,
      params.headers["x-correlation-id"],
      message,
      data,
      filepathandlinenumber[0]
    );
  } else {
    let correlationId = "no-correlation-id";
    return createLog(
      logLevel,
      correlationId,
      message,
      data,
      filepathandlinenumber[0]
    );
  }
};

//Arument order => Message(Mandatory), Params(Optional), Data(Optional)
const errorLog = (...args) => {
  logHandler(logLevels.ERROR, ...args);
};

//Arument order => Message(Mandatory), Params(Optional), Data(Optional)
const warningLog = (...args) => {
  logHandler(logLevels.WARN, ...args);
};

//Arument order => Message(Mandatory), Params(Optional), Data(Optional)
const infoLog = (...args) => {
  logHandler(logLevels.INFO, ...args);
};

//Arument order => Message(Mandatory), Params(Optional), Data(Optional)
const verboseLog = (...args) => {
  logHandler(logLevels.VERBOSE, ...args);
};

//Arument order => Message(Mandatory), Params(Optional), Data(Optional)
const debugLog = (...args) => {
  logHandler(logLevels.DEBUG, ...args);
};

const createLog = (
  level,
  correlationId,
  description,
  data,
  filepathandlinenumber
) => {
  if (data && typeof data !== "object" && !Array.isArray(data)) {
    description = description + data;
  }
  logger.log(level, description, typeof data === "object" ? { data } : null, {
    label: filepathandlinenumber,
    correlationId: correlationId,
  });
};

module.exports = {
  errorLog,
  warningLog,
  infoLog,
  verboseLog,
  debugLog,
};
