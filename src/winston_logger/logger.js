const { createLogger, format, transports } = require('winston')
require('winston-daily-rotate-file')

const { combine, colorize, timestamp, printf } = format

const myFormat = printf((data) => {
  if (data.data && typeof data.data === 'object') {
    return `${data.timestamp} ${data.level} : ${data.correlationId} : ${data.label} : ${data.message}: ${JSON.stringify(data.data, null, 2)}`
  }
  return `${data.timestamp} ${data.level} : ${data.correlationId} : ${data.label} : ${data.message}`
})

const commonProperties = {
  datePattern: 'YYYY-MM-DD-HH',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '30d',
  colorize: true,
  prettyPrint: true,
  json: true
}

const combinedFormat = combine(
  timestamp(),
  myFormat,
  colorize(),
  format.splat()
)

const consoleTransport = new transports.Console({
  format: combinedFormat
})

const _transports = [
  process.env.IS_SERVER ? new (transports.DailyRotateFile)({
    filename: 'logs/application-%DATE%.log',
    level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
    handleRejections: true,
    ...commonProperties
  }) : consoleTransport
]

const exceptionTransports = [
  process.env.IS_SERVER
    ? new (transports.DailyRotateFile)({
      filename: 'logs/exception-%DATE%.log',
      ...commonProperties
    })
    : consoleTransport
]

const logger = createLogger({
  level: 'debug',
  format: combinedFormat,
  transports: _transports,
  exitOnError: false
})

logger.exceptions.handle(exceptionTransports)

if (process.env.NODE_ENV !== 'production' && process.env.IS_SERVER) {
  logger.add(consoleTransport)
}

module.exports = logger
