import { useEffect, useState, createRef } from "react";
import { Link, useNavigate, useParams, useSearchParams,  } from "react-router-dom";
import {useSelector} from "react-redux"
import { Space, Tabs, Button, Collapse, Flex } from 'antd'
import moment from 'moment';

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  validateFormElements2,
  assignValuesFromControlReferencesToFormState,
  extractChangedValues,
  assignReferencesAsListToForm,
} from "../../Components/Common/KrushalOCForm";

import {
  Territory
} from '../../Components/Common/Territory'
import ToastersService from "../../Services/toasters.service";

const {EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const { Panel } = Collapse
const lodashObject = require("lodash");

const { extractBasedOnLanguage, assignL10NObjectOrObjectArrayToItSelf } = require('@krushal-it/common-core')
const { getPartnerBasicData, getPartnerDetails, setPartnerDetails, getStateList, getDistrictList, getTalukList, getVillageList, getGeographyListing, getGeographyDataForVillage, getGeographyDataForVillage2 } = require('../../router')

const loadBasicPartnerData = async (userToken, partnerId) => {
  try {
    const headers = {
      useCase: 'Get Basic Partner Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getPartnerBasicData(headers, partnerId)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadPartnerData = async (userToken, partnerId, classifierArray) => {
  try {
    const headers = {
      useCase: 'Get Basic Partner Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getPartnerDetails(headers, partnerId, classifierArray)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const savePartnerData = async (userToken, partnerId, basicDataChangedValues) => {
  try {
    const headers = {
      useCase: 'Get Basic Partner Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const dataToSend = {...basicDataChangedValues, partnerId: partnerId, partner_type_id: 1000850002}
    const response = await setPartnerDetails(headers, dataToSend)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getAllStates = async (userToken) => {
  try {
    const headers = {
      useCase: 'Get States',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await getStateList(headers)
    const response = await getGeographyListing(headers, {parentGeographyTypeId: 1000320001})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getDistrictsOfState = async (userToken, stateIdArray) => {
  try {
    const headers = {
      useCase: 'Get Districts',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await getDistrictList(headers, stateId)
    const response = await getGeographyListing(headers, {parentGeographyIdArray: stateIdArray, parentToChildEntityRelationshipTypeId: 1000210029, parentGeographyTypeId: 1000320001, childGeographyTypeId: 1000320002})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getTaluksOfDistrict = async (userToken, districtIdArray) => {
  try {
    const headers = {
      useCase: 'Get Taluks',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await getTalukList(headers, districtId)
    const response = await getGeographyListing(headers, {parentGeographyIdArray: districtIdArray, parentToChildEntityRelationshipTypeId: 1000210030, parentGeographyTypeId: 1000320002, childGeographyTypeId: 1000320003})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getVillagesOfTaluk = async (userToken, talukIdArray) => {
  try {
    const headers = {
      useCase: 'Get Villages',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await getVillageList(headers, talukId)
    const response = await getGeographyListing(headers, {parentGeographyIdArray: talukIdArray, parentToChildEntityRelationshipTypeId: 1000210031, parentGeographyTypeId: 1000320003, childGeographyTypeId: 1000320004})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadGeographyData = async (userToken, villageIdArray) => {
  try {
    const headers = {
      useCase: 'Get Geography Data For Village',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await getGeographyDataForVillage(headers, villageId)
    const response = await getGeographyDataForVillage2(headers, villageIdArray)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const partnerBasicDetailsFormDefinition = {
  formControls: {
    bcoId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "BCO Id",
      readOnly: 1,
    },
    bcoName: {
      ocFormRef: createRef(),
      type: "text-l10n_2",
      label: "BCO Name",
    },
    bcoPrimaryContactPersonName: {
      ocFormRef: createRef(),
      type: "text-l10n_2",
      label: "Primary Contact Person Name",
    },
    bcoPrimaryContactPersonMobileNumber: {
      ocFormRef: createRef(),
      type: "text-numeric_2",
      label: "Primary Contact Mobile Number",
      placeHolderText: "Enter BCO Mobile Number",
      validations: [
        { type: "regex", regexArray: ["IndiaMobileNumber"] },
      ],
      errorMessage: "Enter a valid mobile number!",
    },
    bcoState: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "BCO State",
      labelKeyInList: "geography_name_l10n",
      valueKeyInList: "geography_id",
      placeHolderText: "Select BCO State",
      errorMessage: "BCO State is mandatory",
      // validations: [{ type: "Mandatory" }, { type: "regex", regexArray: ["PositiveDecimal"] }],
      onSetValue2: async (
        stateIdInArray,
        additionalParams,
      ) => {
        const userToken = additionalParams[0]
        // const partnerBasicDetailsFormState = additionalParams[1]
        // const setPartnerBasicDetailsFormState = additionalParams[2]
        const districtResponse = await getDistrictsOfState(userToken, stateIdInArray)
        const districtList = assignL10NObjectOrObjectArrayToItSelf(districtResponse.list_data, ['geography_name_l10n'])

        if (partnerBasicDetailsFormDefinition.formControls
            && partnerBasicDetailsFormDefinition.formControls.bcoDistrict
            && partnerBasicDetailsFormDefinition.formControls.bcoDistrict.ocFormRef
            && partnerBasicDetailsFormDefinition.formControls.bcoDistrict.ocFormRef.current
            && partnerBasicDetailsFormDefinition.formControls.bcoDistrict.ocFormRef.current !== null) {
          partnerBasicDetailsFormDefinition.formControls.bcoDistrict.ocFormRef.current.setOptions(districtList)
          partnerBasicDetailsFormDefinition.formControls.bcoDistrict.ocFormRef.current.setValue([])
        }

        if (partnerBasicDetailsFormDefinition.formControls
          && partnerBasicDetailsFormDefinition.formControls.bcoTaluk
          && partnerBasicDetailsFormDefinition.formControls.bcoTaluk.ocFormRef
          && partnerBasicDetailsFormDefinition.formControls.bcoTaluk.ocFormRef.current
          && partnerBasicDetailsFormDefinition.formControls.bcoTaluk.ocFormRef.current !== null) {
          partnerBasicDetailsFormDefinition.formControls.bcoTaluk.ocFormRef.current.setOptions([])
          partnerBasicDetailsFormDefinition.formControls.bcoTaluk.ocFormRef.current.setValue([])
        }

        if (partnerBasicDetailsFormDefinition.formControls
          && partnerBasicDetailsFormDefinition.formControls.bcoVillage
          && partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef
          && partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef.current
          && partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef.current !== null) {
          partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef.current.setOptions([])
          partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef.current.setValue([])
        }
        
        /* const updatedFormState = assignDataToFormState(
          partnerBasicDetailsFormDefinition,
          {bco_district_id_list: districtList, bco_district_id: [], bco_taluk_id_list: [], bco_taluk_id: [], partner_operational_village_id_list: [], partner_operational_village_id: []},
          partnerBasicDetailsFormState,
          setPartnerBasicDetailsFormState
        )
        return updatedFormState */
      },
    },
    bcoDistrict: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "BCO District",
      labelKeyInList: "geography_name_l10n",
      valueKeyInList: "geography_id",
      placeHolderText: "Select BCO District",
      errorMessage: "BCO District is mandatory",
      // validations: [{ type: "Mandatory" }, { type: "regex", regexArray: ["PositiveDecimal"] }],
      onSetValue2: async (
        districtIdInArray,
        additionalParams,
      ) => {
        const userToken = additionalParams[0]
        const talukResponse = await getTaluksOfDistrict(userToken, districtIdInArray)
        const talukList = assignL10NObjectOrObjectArrayToItSelf(talukResponse.list_data, ['geography_name_l10n'])
        if (partnerBasicDetailsFormDefinition.formControls
          && partnerBasicDetailsFormDefinition.formControls.bcoTaluk
          && partnerBasicDetailsFormDefinition.formControls.bcoTaluk.ocFormRef
          && partnerBasicDetailsFormDefinition.formControls.bcoTaluk.ocFormRef.current
          && partnerBasicDetailsFormDefinition.formControls.bcoTaluk.ocFormRef.current !== null) {
          partnerBasicDetailsFormDefinition.formControls.bcoTaluk.ocFormRef.current.setOptions(talukList)
          partnerBasicDetailsFormDefinition.formControls.bcoTaluk.ocFormRef.current.setValue([])
        }

        if (partnerBasicDetailsFormDefinition.formControls
          && partnerBasicDetailsFormDefinition.formControls.bcoVillage
          && partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef
          && partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef.current
          && partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef.current !== null) {
          partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef.current.setOptions([])
          partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef.current.setValue([])
        }
      },
    },
    bcoTaluk: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "BCO Taluk",
      placeHolderText: "Select BCO Taluk",
      errorMessage: "BCO Taluk is mandatory",
      labelKeyInList: "geography_name_l10n",
      valueKeyInList: "geography_id",
      onSetValue2: async (
        talukIdInArray,
        additionalParams,
      ) => {
        const userToken = additionalParams[0]
        const villageResponse = await getVillagesOfTaluk(userToken, talukIdInArray)
        const villageList = assignL10NObjectOrObjectArrayToItSelf(villageResponse.list_data, ['geography_name_l10n'])
        if (partnerBasicDetailsFormDefinition.formControls
          && partnerBasicDetailsFormDefinition.formControls.bcoVillage
          && partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef
          && partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef.current
          && partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef.current !== null) {
          partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef.current.setOptions(villageList)
          partnerBasicDetailsFormDefinition.formControls.bcoVillage.ocFormRef.current.setValue([])
        }
      },
      // validations: [{ type: "Mandatory" }, { type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    bcoVillage: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "BCO Village",
      placeHolderText: "Select BCO Village",
      errorMessage: "BCO Village is mandatory",
      labelKeyInList: "geography_name_l10n",
      valueKeyInList: "geography_id",
      validations: [{ type: "Mandatory" }],
    },
    bcoLocation: {
      type: "map-marker",
      label: "BCO Location",
      readOnly: 1,
      zoom: 13,
      markerTooltip: 'BCO Location',
      componentStyle: { height: '225px', width: '225px', marginLeft: '10px' }
    },
  },
  visibleFormControls: [
    "bcoId",
    "bcoName",
    "bcoPrimaryContactPersonName",
    "bcoPrimaryContactPersonMobileNumber",
    "bcoState",
    "bcoDistrict",
    "bcoTaluk",
    "bcoVillage",
    "bcoLocation",
  ],
  elementToDataMapping: {
    bcoId: 'partner_id',
    bcoName: 'partner_name_l10n',
    bcoPrimaryContactPersonName: 'partner_primary_contact_person_name',
    bcoPrimaryContactPersonMobileNumber: 'mobile_number',
    bcoState: 'bco_state_id',
    bcoDistrict: 'bco_district_id',
    bcoTaluk: 'bco_taluk_id',
    bcoVillage: 'partner_operational_village_id',
    bcoLocation: 'bco_location',
  },
  dataToElementMapping: {
    partner_id: 'bcoId',
    partner_name_l10n: 'bcoName',
    partner_primary_contact_person_name: 'bcoPrimaryContactPersonName',
    mobile_number: 'bcoPrimaryContactPersonMobileNumber',
    bco_state_id: 'bcoState',
    bco_district_id: 'bcoDistrict',
    bco_taluk_id: 'bcoTaluk',
    partner_operational_village_id: 'bcoVillage',
    bco_location: 'bcoLocation',
  },
}

const partnerBasicDetailsFormInitialState = {
}

const territoryConfiguration = {
  stateLabel: 'State',
  districtLabel: 'District',
  talukLabel: 'Taluk',
  villageLabel: 'Village',
  statePlaceHolderText: 'Choose One',
  districtPlaceHolderText: 'Choose One',
  talukPlaceHolderText: 'Choose One',
  villagePlaceHolderText: 'Choose One',
}

const CSBCO = () => {
  const params = useParams()
  const navigate = useNavigate()
  const userToken = useSelector((state)=>state.user.userToken);

  const [searchParams] = useSearchParams()
  // const uncompressedFilterParamsJsonString = decodeURIComponent(searchParams.get('qP'))// urlEncodedJson)
  // const queryParams = JSON.parse(uncompressedFilterParamsJsonString)
  // const customerId = queryParams.customerId
  const partnerId = params.partnerId

  const [partnerBasicDetailsFormState, setPartnerBasicDetailsFormState] = useState(partnerBasicDetailsFormInitialState)
  const [selectedVillageIdArray, setSelectedVillageIdArray] = useState([])
  const [loading, setLoading] = useState(true)

  const territoryRef = createRef()
  
  const additionalParams = []
  additionalParams.push(userToken)
  additionalParams.push(partnerBasicDetailsFormState)
  additionalParams.push(setPartnerBasicDetailsFormState)

  const loadDataAsync = async () => {
    try {
      const excludeFromClassifiersFormControlArray = ['partner_id', 'partner_name_l10n', 'mobile_number', 'bco_state_id', 'bco_district_id', 'bco_taluk_id', 'bco_location']
      // const classifiers = ['partner_operational_village_id']
      const classifiers = ['bco_location_position']
      for (const visibleFormControl of partnerBasicDetailsFormDefinition.visibleFormControls) {
        const columnMapping = partnerBasicDetailsFormDefinition.elementToDataMapping[visibleFormControl]
        if (!excludeFromClassifiersFormControlArray.includes(columnMapping)) {
          classifiers.push(columnMapping)
        }
      }

      if (partnerId !== '-1') {
        const basicDataResponse = await loadBasicPartnerData(userToken, partnerId)
        const partnerDataResponse = await loadPartnerData(userToken, partnerId, classifiers)
        let completeDataLoaded = {}
        if (partnerDataResponse && partnerDataResponse.data && partnerDataResponse.data['partner_operational_village_id'] !== undefined && partnerDataResponse.data['partner_operational_village_id'] !== null && Array.isArray(partnerDataResponse.data['partner_operational_village_id']) && partnerDataResponse.data['partner_operational_village_id'].length > 0) {
          // get stateid, districtid, talukid, and also the district_list, taluk_list and village_list
          const geographyData = await loadGeographyData(userToken, partnerDataResponse.data['partner_operational_village_id'])
          const stateList = assignL10NObjectOrObjectArrayToItSelf(geographyData.state_list, ['geography_name_l10n'])
          const districtList = assignL10NObjectOrObjectArrayToItSelf(geographyData.district_list, ['geography_name_l10n'])
          const talukList = assignL10NObjectOrObjectArrayToItSelf(geographyData.taluk_list, ['geography_name_l10n'])
          const villageList = assignL10NObjectOrObjectArrayToItSelf(geographyData.village_list, ['geography_name_l10n'])
          completeDataLoaded.bco_state_id = geographyData.state_id
          completeDataLoaded.bco_district_id = geographyData.district_id
          completeDataLoaded.bco_taluk_id = geographyData.taluk_id
          completeDataLoaded.bco_state_id_list = stateList
          completeDataLoaded.bco_district_id_list = districtList
          completeDataLoaded.bco_taluk_id_list = talukList
          completeDataLoaded.partner_operational_village_id_list = villageList
          // setSelectedVillageIdArray([partnerDataResponse.data['partner_operational_village_id'][0]])
        } else {
          const stateListResponse = await getAllStates(userToken)
          if (stateListResponse.return_code === 0) {
            const stateList = assignL10NObjectOrObjectArrayToItSelf(stateListResponse.list_data, ['geography_name_l10n'])
            completeDataLoaded.bco_state_id_list = stateList
          }
        }
        if (basicDataResponse.return_code === 0 && partnerDataResponse.return_code === 0) {
          if (basicDataResponse.result && basicDataResponse.result.mobile_number && basicDataResponse.result.mobile_number !== null) {
            if (basicDataResponse.result.mobile_number.startsWith('+91')) {
              basicDataResponse.result.mobile_number = basicDataResponse.result.mobile_number.slice(3)
            }
          }
          completeDataLoaded = {...completeDataLoaded, ...basicDataResponse.result, ...partnerDataResponse.data}
          const updatedPartnerBasicDetailsFormState = assignDataToFormState(
            partnerBasicDetailsFormDefinition,
            completeDataLoaded,
            partnerBasicDetailsFormState,
            setPartnerBasicDetailsFormState
          )
          
        } else {
          ToastersService.failureToast('Could not load data')
        }
      } else {
        const stateListResponse = await getAllStates(userToken)
        if (stateListResponse.return_code === 0) {
          const stateList = assignL10NObjectOrObjectArrayToItSelf(stateListResponse.list_data, ['geography_name_l10n'])
          partnerBasicDetailsFormDefinition.formControls.bcoState.list = stateList
        }
      }
    } catch (error) {
      console.log('CS C CSBCO lDA 10, error')
      console.log('CS C CSBCO lDA 10a, error = ', error)
      throw error
    } finally {
      setLoading(false)
    }
  }
  
  useEffect(()=>{
    loadDataAsync()
  },[])

  const handleSavePartnerBasicDataClick = async () => {
    /* const [currentFormState, validationFailed] = validateFormElements(
      partnerBasicDetailsFormDefinition,
      partnerBasicDetailsFormState,
      setPartnerBasicDetailsFormState
    ) */
    const [currentFormState, validationFailed] = validateFormElements2(
      partnerBasicDetailsFormDefinition,
      partnerBasicDetailsFormState,
      setPartnerBasicDetailsFormState
    )
    if (validationFailed) {
      // do something
      console.log("Validation Failed");
      ToastersService.failureToast("Validation failed!");
    } else {
      // const geographyValues = territoryRef.current.getSelectedGeography()
      // verify return code?
      console.log("you can save now");
      const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(partnerBasicDetailsFormDefinition, currentFormState, setPartnerBasicDetailsFormState)
      const basicDataChangedValues = extractChangedValues(partnerBasicDetailsFormDefinition, updatedCurrentFormState)
      /* if (geographyValues.selectedVillageIdArray) {
        basicDataChangedValues['partner_operational_village_id'] = geographyValues['selectedVillageIdArray']
      } */

      const fieldsToIgnoreInSaving = ['bco_state_id', 'bco_district_id', 'bco_taluk_id']
      for (const fieldChanged of Object.keys(basicDataChangedValues)) {
        if (fieldsToIgnoreInSaving.includes(fieldChanged)) {
          delete basicDataChangedValues[fieldChanged]
        }
      }

      if (basicDataChangedValues['mobile_number'] && basicDataChangedValues['mobile_number'] !== null) {
        basicDataChangedValues['mobile_number'] = '+91' + basicDataChangedValues['mobile_number']
      }
      const resposeFromSavingCropScreenData = await savePartnerData(userToken, partnerId, basicDataChangedValues)
      if (resposeFromSavingCropScreenData.return_code === 0) {
        await loadDataAsync()
        ToastersService.successToast("Updated Partner Data")
      } else {
        ToastersService.failureToast("Failed To Updated Partner Data")
      }
    }
  }

  return (
    <>
      <Collapse defaultActiveKey={['1']} >
        <Panel header="Basic BCO Details" key="1">
          <div style={{display:'flex', alignItems:"flex-start"}}>
            <div style={{flex:"1"}}>
              <KrushalOCForm
                formState={partnerBasicDetailsFormState}
                setFormState={setPartnerBasicDetailsFormState}
                formDefinition={partnerBasicDetailsFormDefinition}
                additionalParams={additionalParams}
              />
              {/* <Territory
                loading={loading}
                configuration={territoryConfiguration}
                selectedVillageIdArray={selectedVillageIdArray}
                ref={territoryRef}
                /> */}
            </div>
            <div style={{alignSelf: "flex-end", display:"flex", flexDirection: "column"}}>
              <Button
                onClick={() => handleSavePartnerBasicDataClick()}
                >
                Save
              </Button>
              <Button style={{marginTop:"4px"}}>Future Use Button</Button>
            </div>
          </div>
        </Panel>
      </Collapse>
    </>
    
  )
}

// export default CSBCO
export default withAuthorization(CSBCO, [CS_TEAM])