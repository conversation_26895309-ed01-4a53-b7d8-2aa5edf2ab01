import { Link } from 'react-router-dom';

import { Card, Row, Col } from 'antd'

const {EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const CustomerService = () => {
  return (
    <>
    <Row>
      <Col span={3}>
        <Link to="bco-list">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            BCO List
          </Card>
        </Link>
      </Col>
      <Col span={3}>
        <Link to="staff-list">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Staff List
          </Card>
        </Link>
      </Col>

    </Row>
  </>
  )
}

// export default CustomerService
export default withAuthorization(CustomerService, [CS_TEAM])