import { Checkbox, Space, Button, Modal } from "antd";
import { ReloadOutlined } from '@ant-design/icons';

import { useState, useRef } from "react"
import { Link, useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import {useSelector} from "react-redux"
import dayjs from 'dayjs'

import ToastersService from "../../Services/toasters.service";

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  extractChangedValues,
} from "../../Components/Common/KrushalOCForm";

const {EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const { getCustomerServiceTasksReport, getCustomerServiceTasksReportFilterData, snoozeCustomerServiceAnimals, updateCustomerServiceTaskStatus, /* updateStatusOfHealthScoreAnimals */ } = require('../../router')

const { OCTable, CommentViewerModalButton, createPageParamsFromURLString, MediaCarouselModalButton } = require('../../Components/Common/AntTableHelper')

const loadReport = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Customer Service Tasks Report',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getCustomerServiceTasksReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadFilters = async (userToken) => {
  try {
    const headers = {
      useCase: 'Get Customer Service Tasks Report Filter Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getCustomerServiceTasksReportFilterData(headers)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const tableConfiguration = {
  tableNavigationUrl: '../cs/current-task-list',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  enableSplittingOfServerData: true, // use this only if you have made changes to the backend
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'care_calendar_id',
  columns: [
    {
      title: "Task Id",
      key: "care_calendar_id",
    },
    {
      title: "Active",
      key: "animal_active",
    },
    {
      title: 'Snoozed or Not',
      key: 'snooze_status',
    },
    {
      title: "Task Status",
      key: "task_status",
    },
    {
      title: "Task Completion Date",
      key: "task_completion_time",
    },
    {
      title: "Task Instructions",
      key: "task_instructions",
    },
    {
      title: "Animal Task Link",
      dataIndex: "task_instructions",
      key: "animal_task_link",
    },
    {
      title: "Animal Ear Tag",
      key: "animal_ear_tag",
    },
    {
      title: "Animal Type",
      key: "animal_type",
    },
    {
      title: "Farmer Name",
      key: "customer_name",
    },
    {
      title: "Farmer Mobile Number",
      key: "mobile_number",
    },
    {
      title: "Taluk",
      key: "taluk_name",
    },
    {
      title: "Village",
      key: "village_name",
    },
    {
      title: "BDM",
      key: "bdm",
    },
    {
      title: "BDM Mobile",
      key: "bdm_mobile",
    },
    {
      title: "Paravet",
      key: "paravet",
    },
    {
      title: "Paravet Mobile",
      key: "paravet_mobile",
    },
    {
      title: "Subscription Plan",
      key: "current_subscription_plan",
    },
    {
      title: "With Krushal From",
      key: "with_krushal_from_date",
    },
    {
      title: "Subscription Date Updated On",
      key: "with_krushal_from_date_updated_at",
    },
  ],
  columnConfiguration: {
    care_calendar_id: {
      headingText: 'Task Id',
      dataType: 'uuid',
      includeInOmnisearch: true
    },
    animal_active: {
      headingText: 'Animal Active Or Not',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      filters: [
        { text: 'Active', value: 'Active', value_int: 1000100001 },
        { text: 'Not Active', value: 'Not Active', value_int: 1000100002 },
      ],
      defaultFilteredValue: ['Active'],
      translateFilteredValuesToAlt: 'value_int',
      altColumn: 'animal_active_id'
    },
    snooze_status: {
      headingText: 'Snooze Status',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'Snoozed', value: 'Snoozed' },
        { text: 'Not Snoozed', value: 'Not Snoozed' },
      ],
      defaultFilteredValue: ['Not Snoozed'],
      enableSort: true,
      sortColumnType: 'string',
    },
    animal_task_link: {
      heading:'Animal Task Link',
      formatType: 'custom',
      formatFunction: (custom, record) => {
        const animalId = record['animal_id']
        const customerId = record['customer_id']
        // navigate({
        //   pathname: `../oc/farmer-details`,
        //   search: `?qP=${encodedParamsAsString}`
        // })
        const navigationLink = `/e/farmer-animal-details/${customerId}/animal/${animalId}`
        return (
          <a href={navigationLink} target="_blank" rel="noopener noreferrer">
            Update Animal Details
          </a>
        )
      }
    },
    animal_ear_tag: {
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    animal_type: {
      headingText: 'Animal Type',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'Cow', value: 'Cow' },
        { text: 'Cow Calf', value: 'Cow Calf' },
        { text: 'Buffalo', value: 'Buffalo' },
        { text: 'Buffalo Calf', value: 'Buffalo Calf' },
      ],
      // defaultFilteredValue: ['Not Snoozed'],
      enableSort: true,
      sortColumnType: 'string',
    },    
    customer_id: {
      headingText: 'Customer Id',
      allowFilterInPageParams: true,
      paramSearchType: 'array',
      paramSearchDataType: 'string'
    },
    customer_name: {
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    mobile_number: {
      headingText: 'Mobile Number',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    taluk_name: {
      headingText: 'Taluk',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      altColumn: 'taluk_id'
    },
    village_name: {
      headingText: 'Village',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
    },
    bdm: {
      headingText: 'BDM',
      enableFilter: true,
      type: 'single_select_array',
      dataType: 'string',
      enableSort: true,
      sortColumnType: 'string',
      altColumn: 'bdm_uuid',
      filterValuesKey: 'staff_filter_values'
    },
    with_krushal_from_date: { // for onboarding animals
      headingText: 'With Krushal From Date',
      formatType: 'date',
      format: 'MMM-YYYY',
      enableFilter: true,
      type: 'dayjs_range',
      enableSort: true,
      sortColumnType: 'date',
      // defaultSort: firstTimeHealthRecordVerification ? 'ascend' : undefined
    },
    with_krushal_for_months: {
      headingText: 'With Krushal For Months',
      formatType: 'months_since',
    },
    with_krushal_from_date_updated_at: {
      formatType: 'date',
      format: 'MMM-YYYY',
      enableFilter: true,
      type: 'dayjs_range',
      enableSort: true,
      sortColumnType: 'date',
    },
  }
}

const snoozeDialogFormDefinition = {
  forceNewLineAfterControl: true,
  defaultClasses: 'mb-3',
  formControls: {
    taskId: {
      type: "text",
      label: "Task Id",
      readOnly: 1,
    },
    animalId: {
      type: "text",
      label: "Animal Id",
      readOnly: 1,
    },
    animalEarTag: {
      type: "text",
      label: "Animal Ear Tag",
      readOnly: 1,
    },
    farmerId: {
      type: "text",
      label: "Farmer Id",
      readOnly: 1,
    },
    farmerName: {
      type: "text",
      label: "Farmer Name",
      readOnly: 1,
    },
    farmerMobile: {
      type: "text",
      label: "Farmer Mobile",
      readOnly: 1,
    },
    snoozeTillDate: {
      type: "date-time",
      label: "Snooze Till",
      placeHolderText: "Enter Date",
      errorMessage: "Snooze Till Date is Mandatory!",
      validations: [{ type: "Mandatory" }, {type: "MinimumDate", value: new Date()}],
    },
    farmerLevelSnoozeChoice: {
      type: "single-select",
      label: "Snooze At Farmer Level*",
      errorMessage: "Choosing if Farmer Needs to be Snoozed or Not Is Mandatory",
      labelKeyInList: "reference_name",
      valueKeyInList: "reference_id",
      placeHolderText: "Choose Yes or No",
      validations: [{ type: "Mandatory" }],
    },
    snoozeComments: {
      type: "text",
      label: "Comments",
      validations: [{ type: "Mandatory" }],
      placeHolderText: "Enter Any Comments",
    },
  },
  visibleFormControls: [
    "taskId",
    "animalId",
    "animalEarTag",
    "farmerId",
    "farmerName",
    "farmerMobile",
    "snoozeTillDate",
    "farmerLevelSnoozeChoice",
    "snoozeComments",
  ],
  elementToDataMapping: {
    taskId: "care_calendar_id",
    animalId: "animal_id",
    animalEarTag: "animal_ear_tag",
    farmerId: "customer_id",
    farmerName: "customer_name",
    farmerMobile: "mobile_number",
    snoozeTillDate: "snooze_till_date",
    farmerLevelSnoozeChoice: "farmer_level_snooze_choice",
    snoozeComments: "snooze_comments",
  },
  dataToElementMapping: {
    care_calendar_id: "taskId",
    animal_id: "animalId",
    animalEarTag: "animalEarTag",
    customer_id: "farmerId",
    customer_name: "farmerName",
    mobile_number: "farmerMobile",
    snooze_till_date: "snoozeTillDate",
    farmer_level_snooze_choice: "farmerLevelSnoozeChoice",
    snooze_comments: "snoozeComments",
  },
}

const snoozeDialogFormInitialState = {
  snoozeTillDate: {},
  farmerLevelSnoozeChoice: {},
  snoozeComments: {},
}

const updateStatusDialogFormDefinition = {
  forceNewLineAfterControl: true,
  defaultClasses: 'mb-3',
  formControls: {
    taskId: {
      type: "text",
      label: "Task Id",
      readOnly: 1,
    },
    animalId: {
      type: "text",
      label: "Animal Id",
      readOnly: 1,
    },
    animalEarTag: {
      type: "text",
      label: "Animal Ear Tag",
      readOnly: 1,
    },
    farmerId: {
      type: "text",
      label: "Farmer Id",
      readOnly: 1,
    },
    farmerName: {
      type: "text",
      label: "Farmer Name",
      readOnly: 1,
    },
    farmerMobile: {
      type: "text",
      label: "Farmer Mobile",
      readOnly: 1,
    },
    taskStatus: {
      type: "single-select-l10n",
      label: "Task Status",
      errorMessage: "Choosing the status the task needs to be updated to",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Choose Status",
      validations: [{ type: "Mandatory" }],
    },
    updateStatusComments: {
      type: "text",
      label: "Comments",
      validations: [{ type: "Mandatory" }],
      placeHolderText: "Enter Any Comments",
    },
  },
  visibleFormControls: [
    "taskId",
    "animalId",
    "animalEarTag",
    "farmerId",
    "farmerName",
    "farmerMobile",
    "taskStatus",
    "updateStatusComments",
  ],
  elementToDataMapping: {
    taskId: "care_calendar_id",
    animalId: "animal_id",
    animalEarTag: "animal_ear_tag",
    farmerId: "customer_id",
    farmerName: "customer_name",
    farmerMobile: "mobile_number",
    taskStatus: "task_status_id",
    updateStatusComments: "update_status_comments",
  },
  dataToElementMapping: {
    care_calendar_id: "taskId",
    animal_id: "animalId",
    animalEarTag: "animalEarTag",
    customer_id: "farmerId",
    customer_name: "farmerName",
    mobile_number: "farmerMobile",
    task_status_id: "taskStatus",
    update_status_comments: "updateStatusComments",
  },
}

const animalDocumentsMediaCarouselInitialConfiguration = {
  completeInformation: false,
  showLatestOnlyFlag: true,
  enableDownload: true,
  enablePreview: true,
  enableViewInBrowser: true,
  enableLatestOnlyFlag: true,
  latestOnlyFlag: false,
  styleContainer: {
    mediaCardStyle: { display: 'inline-block', padding: '0px', margin: '0px', width: '225px', height: 'auto' },
    mediaCardBodyStyle: {padding: '12px'},
    mediaTagWrapperDivStyle: {position: 'relative', width: 'auto'},
    mediaTagWrapperOnHoverDivStyle: undefined,
    mediaTagPreviewOverlayStyle: undefined,
    mediaTagPreviewOverlayOnHoverStyle: undefined,
    mediaTagStyle: { maxWidth: '200px', height: 'auto', position: 'relative'},
  },
  enableUploadOfFiles: false,
  uploadFolder: 'ah/animal',
  documentTypesForFilter: [
    {label:'Animal Thumbnail Photo', value:1000260001},
    {label:'Animal Photo Any Angle', value:1000260002},
    {label:'Animal Eartag Photo', value:1000260012},
    {label:'Foreign Body Area', value:1000260013},
    {label:'Animal classification documents', value:1000260016},
  ],
  document_query_information : {
    entity_name: 'animal',
    related_additional_entity_types: ['animal_classification']
  },
  base_entity_information: {
    entity_1_type_id: [1000220002, 1000220003],
    // entity_1_entity_uuid: Array.isArray(selectedRowKeys) && selectedRowKeys.length > 0 ? selectedRowKeys[0] : undefined
  }

}

const dataVerificationAnimalCommentsModalDialogInitialConfiguration = {
  title: 'Customer Service Task Comments',
  note_query_configuration: {
    note_type_id: [
      1000440016,
      1000440017
    ],
    entity_1_type_id: 1000220003
  }
}

const CustomerServiceTasks = () => {
  const navigate = useNavigate()
  const location = useLocation()

  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    tableConfiguration.enableTableExport = true
    tableConfiguration.enableTableExportFromCloud = true
  }

  const [reportData, setReportData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const tableRef = useRef()

  const postInitialLoadCB = () => {
    assignDataToFormState(
      snoozeDialogFormDefinition,
      {
        farmer_level_snooze_choice_list: [{reference_name:'Yes', reference_id: 1000105001}, {reference_name:'No', reference_id: 1000105002}],
        // farmer_level_snooze_choice_list: [{label:'Yes', id: 1}, {label:'No', id: 2}],
      },
      snoozeDialogFormState,
      setSnoozeDialogFormState
    )
    assignDataToFormState(
      updateStatusDialogFormDefinition,
      {
        task_status_id_list: [
          // {reference_name_l10n:{ul:'Farmer Contacted, Follow up Call Required'},reference_id: 1000490001},
          // {reference_name_l10n:{ul:'Farmer Contacted, Detailed Process Explanation Required'},reference_id: 1000490002},
          // {reference_name_l10n:{ul:'Process Explained, Deposit Not Collected'},reference_id: 1000490003},
          // {reference_name_l10n:{ul:'Deposit Collected, Latest Photos Not Collected'},reference_id: 1000490004},
          // {reference_name_l10n:{ul:'Photos Collected, Other Process Not Completed'},reference_id: 1000490005},
          // {reference_name_l10n:{ul:'Animal Put Up For Sale'},reference_id: 1000490006},
          {reference_name_l10n:'Ticket Entry Raised',reference_id: 1000290001},
          {reference_name_l10n:'Task Completed',reference_id: 1000300050},
          {reference_name_l10n:'Task Cancelled',reference_id: 1000300051},
          {reference_name_l10n:'Task Abandoned',reference_id: 1000300052},
          // {reference_name_l10n:'Process Explained, Deposit Not Collected',reference_id: 1000490003},
          // {reference_name_l10n:'Deposit Collected, Latest Photos Not Collected',reference_id: 1000490004},
          // {reference_name_l10n:'Photos Collected, Other Process Not Completed',reference_id: 1000490005},
          // {reference_name_l10n:'Animal Put Up For Sale',reference_id: 1000490006},
        ],
        // farmer_level_snooze_choice_list: [{label:'Yes', id: 1}, {label:'No', id: 2}],
      },
      updateStatusDialogFormState,
      setUpdateStatusDialogFormState
    )
  }

  const loadInitialFilterDataCB = (additionalParams) => {
    return loadFilters(userToken, additionalParams)
  }

  const loadReportCB = (reportQueryParams, additionalParams) => {
    if (excludeBDMInQuery) {
      reportQueryParams.excludeBDM = true
    }
    return loadReport(userToken, reportQueryParams, additionalParams)
  }

  const [animalDocumentsMediaCarouselConfiguration, setAnimalDocumentsMediaCarouselConfiguration] = useState(animalDocumentsMediaCarouselInitialConfiguration)
  const handleViewAnimalDocuments = () => {
    const selectedRow = reportData.filter((object) => {
      return (object['care_calendar_id'] === selectedRowKeys[0])
    })
    if (Array.isArray(selectedRowKeys) && selectedRowKeys.length > 0) {
      const updatedConfiguration = {
        ...animalDocumentsMediaCarouselConfiguration,
        completeInformation: true,
        base_entity_information: {
          ...animalDocumentsMediaCarouselConfiguration.base_entity_information,
          entity_uuid: selectedRow[0]['animal_id']
        }
      }
      setAnimalDocumentsMediaCarouselConfiguration(updatedConfiguration)
      return true
    } else {
      return false
    }
  }

  const handleSnoozeClick = () => {
    // this is one at a time
    // update state for animal id, ear tag, seller farmer name, seller farmer mobile
    console.log('PSAWL hSC 1, selectedRowKeys = ', selectedRowKeys)
    const selectedRow = reportData.filter((object) => {
      return (object['care_calendar_id'] === selectedRowKeys[0])
    })
    console.log('PSAWL hSC 2, selectedRow = ', selectedRow)
    assignDataToFormState(
      snoozeDialogFormDefinition,
      selectedRow[0],
      snoozeDialogFormState,
      setSnoozeDialogFormState
    )

    // launch popup
    // based on krushalocform
    //    pick up date
    //    pick up if override is at farmer level
    //    pick up comments
    // on save, callback
    // call api
    console.log('PSAWL hSC 1')
    // setSnoozeDialogFormState(snoozeDialogFormInitialState)
    setSnoozeModalDialogVisibility(true)
  }

  const onSnoozeModalOK = async () => {
    try {
      const [currentFormState, validationFailed] = validateFormElements(
        snoozeDialogFormDefinition,
        snoozeDialogFormState,
        setSnoozeDialogFormState
      );
      // console.log("AF1 sF 2, currentFormStateTab0 = ", currentFormStateTab0);
      if (validationFailed) {
        // do something
        console.log("Validation Failed");
        ToastersService.failureToast("Validation failed!");
      } else {
        console.log("you can save now");
        const extractedValues = extractChangedValues(snoozeDialogFormDefinition, currentFormState, ['taskId', 'animalId', 'farmerId'])
        console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValues)
        const taskWithSnoozeInformation = {...extractedValues}
        const headers = {
          useCase: 'Snooze Animal For Customer Service Task',
          token: userToken.accessToken, //authToken
          refreshtoken: localStorage.getItem("refreshToken")
        }
        // configureParams(extractedValues, navigate,reason)
        // callSnoozeAPI
        const responseWithData = await snoozeCustomerServiceAnimals(headers, taskWithSnoozeInformation)
        const response = responseWithData.data
        console.log('AF1 sF 2, response = ', response)
        if (response.return_code === 0) {
          setSnoozeModalDialogVisibility(false)
          ToastersService.successToast("Snoozed Animal Successfully")
          if (tableRef.current) {
            tableRef.current.reloadDataAndMoveToPageOne()
          }
        } else {
          ToastersService.failureToast("Could not snooze")
        }
      }
    } catch (error) {
      console.log('AF1 sF 10, error')
      console.log('AF1 sF 10a, error = ', error)
      ToastersService.failureToast("Could not snooze")
    }
  }

  const onSnoozeModalCancel = () => {
    setSnoozeModalDialogVisibility(false)
  }

  const isNewBuyerFarmerStatusAcceptableFromPrevious = (extractedValues) => {
    const newStatusId = extractedValues.data_verification_animal_next_status[0]
    let currentStatusId = extractedValues.data_verification_animal_current_status_id
    let acceptableStatus = false
    if (currentStatusId) {
      if (typeof currentStatusId === 'string' || currentStatusId instanceof String) {
        currentStatusId = parseInt(currentStatusId)
      }
      switch (currentStatusId) {
        case 1000890001:
          if ([1000890002, 1000890003].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000890002:
          if ([1000890003].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
        case 1000890003:
          if ([1000890002].includes(newStatusId)) {
            acceptableStatus = true
          }
          break
      }
    } else {
      if ([1000890002, 1000890003].includes(newStatusId)) {
        acceptableStatus = true
      }
    }
    return acceptableStatus
  }

  const onUpdateStatusModalOK = async () => {
    try {
      const [currentFormState, validationFailed] = validateFormElements(
        updateStatusDialogFormDefinition,
        updateStatusDialogFormState,
        setUpdateStatusDialogFormState
      );
      // console.log("AF1 sF 2, currentFormStateTab0 = ", currentFormStateTab0);
      if (validationFailed) {
        // do something
        console.log("Validation Failed");
        ToastersService.failureToast("Validation failed!");
      } else {
        const extractedValues = extractChangedValues(updateStatusDialogFormDefinition, currentFormState, ['taskId', 'animalId', 'customerId'])
        console.log("AF1 sF 1, extractedValues = ", extractedValues)
        if (!extractedValues['task_status_id']) {
          // no change in status? should we proceed
          ToastersService.failureToast("Not an acceptable status")
        } else {
          const headers = {
            useCase: 'Update Customer Service Task Status',
            token: userToken.accessToken, //authToken
            refreshtoken: localStorage.getItem("refreshToken")
          }
          // configureParams(extractedValues, navigate,reason)
          // callSnoozeAPI
          const responseWithData = await updateCustomerServiceTaskStatus(headers, extractedValues)
          const response = responseWithData.data
          console.log('AF1 sF 2, response = ', response)
          if (response.return_code === 0) {
            ToastersService.successToast("Task Status Updated Successfully")
            // await reloadDataAndMoveToPageOne(tableConfiguration)
            if (tableRef.current) {
              tableRef.current.reloadDataAndMoveToPageOne()
            }
            setUpdateStatusModalDialogVisibility(false)
          } else {
            ToastersService.failureToast("Could not update status of task")
          }
        }

        /* const acceptableStatus = isNewBuyerFarmerStatusAcceptableFromPrevious(extractedValues)
        
        if (!acceptableStatus) {
          ToastersService.failureToast("Not an acceptable status")
        } else {
          // update sellable status
          const selectedRow = reportData.filter((object) => {
            return (object['customer_id'] === selectedRowKeys[0])
          })
          const farmerStatusUpdateInformation = {...extractedValues, oc_item_listing_uuid: ocItemListingUUID}
          const headers = {
            useCase: 'Update Animal Data Verification Status',
            token: userToken.accessToken, //authToken
            refreshtoken: localStorage.getItem("refreshToken")
          }
          // configureParams(extractedValues, navigate,reason)
          // callSnoozeAPI
          const responseWithData = await updateBuyerFarmerStatus(headers, farmerStatusUpdateInformation)
          const response = responseWithData.data
          console.log('AF1 sF 2, response = ', response)
          if (response.return_code === 0) {
            setUpdateStatusModalDialogVisibility(false)
            ToastersService.successToast("Status Updated Successfully")
            // await reloadDataAndMoveToPageOne(tableConfiguration)
            if (tableRef.current) {
              tableRef.current.reloadDataAndMoveToPageOne()
            }
  
          } else {
            ToastersService.failureToast("Could not update status of animal")
          }
        } */
      }
    } catch (error) {
      console.log('AF1 sF 10, error')
      console.log('AF1 sF 10a, error = ', error)
      ToastersService.failureToast("Could not snooze")
    }
  }
  const onUpdateStatusModalCancel = () => {
    setUpdateStatusModalDialogVisibility(false)
  }

  const handleUpdateCustomerServiceTaskStatusClick = () => {
    // this is one at a time
    // update state for animal id, ear tag, seller farmer name, seller farmer mobile
    console.log('PBFFA hUS 1, selectedRowKeys = ', selectedRowKeys)
    const selectedRow = reportData.filter((object) => {
      return (object['care_calendar_id'] === selectedRowKeys[0])
    })
    console.log('PBFFA hUS 2, selectedRow = ', selectedRow)
    assignDataToFormState(
      updateStatusDialogFormDefinition,
      selectedRow[0],
      updateStatusDialogFormState,
      setUpdateStatusDialogFormState
    )

    // launch popup
    // based on krushalocform
    //    pick up status
    //    pick up comments
    // on save, callback
    // call api
    console.log('PBFFA hSC 1')
    setUpdateStatusModalDialogVisibility(true)
  }

  const enableButton = (selectedRowKeys, reportData, buttonType) => {
    let returnValue = true
    if (selectedRowKeys.length === 1) {
      const selectedRow = reportData.filter((object) => {
        return (object['care_calendar_id'] === selectedRowKeys[0])
      })
      if (buttonType === 'STATUSUPDATE') {
        if (selectedRow.length === 1) {
          if (selectedRow[0].task_status_id === 1000290001) {
            returnValue = false
          }
        }
      } else {
        if (selectedRow.length === 1) {
          returnValue = false
        }
      }
    }
    return returnValue
  }

  const handleViewAnimalClick = (event) => {
    console.log('DCOFSA hVAC 1')
    const selectedRow = reportData.filter((object) => {
      return (object['care_calendar_id'] === selectedRowKeys[0])
    })
    const { pathname, search } = location

    const animalId = selectedRow[0]['animal_id']
    const customerId = selectedRow[0]['customer_id']
    // navigate({
    //   pathname: `../oc/farmer-details`,
    //   search: `?qP=${encodedParamsAsString}`
    // })
    const navigationLink = `/e/farmer-animal-details/${customerId}/animal/${animalId}`
    if (event.ctrlKey || event.metaKey) {
      // window.open(navigationLink + '?role=cv', '_blank')
      window.open(navigationLink, '_blank')
    } else {
      navigate({
        pathname: navigationLink,
        search: `?role=cv`
      })
    }
  }

  const [dataVerificationAnimalCommentsModalDialogConfiguration, setDataVerificationAnimalCommentsModalDialogConfiguration] = useState(dataVerificationAnimalCommentsModalDialogInitialConfiguration)
  const handleViewDataVerificationAnimalCommentsClick = () => {
    const updatedConfiguration = {
      ...dataVerificationAnimalCommentsModalDialogConfiguration,
      note_query_configuration: {
        ...dataVerificationAnimalCommentsModalDialogConfiguration.note_query_configuration,
        entity_1_uuid: selectedRowKeys[0]
      }
    }
    setDataVerificationAnimalCommentsModalDialogConfiguration(updatedConfiguration)
  }

  const [snoozeModalDialogVisibility, setSnoozeModalDialogVisibility] = useState(false)
  const [updateStatusModalDialogVisibility, setUpdateStatusModalDialogVisibility] = useState(false)

  const [snoozeDialogFormState, setSnoozeDialogFormState] = useState(snoozeDialogFormInitialState)
  const [updateStatusDialogFormState, setUpdateStatusDialogFormState] = useState({})
  
  const [excludeBDMInQuery, setExcludeBDMInQuery] = useState(false)
  
  return (
    <>
      <Modal
        title="Snooze Animal"
        open={snoozeModalDialogVisibility}
        onOk={onSnoozeModalOK}
        onCancel={onSnoozeModalCancel}
        >
          <>
            <KrushalOCForm
              formState={snoozeDialogFormState}
              setFormState={setSnoozeDialogFormState}
              formDefinition={snoozeDialogFormDefinition}
            />
          </>
      </Modal>
      <Modal
        title="Update Status"
        open={updateStatusModalDialogVisibility}
        onOk={onUpdateStatusModalOK}
        onCancel={onUpdateStatusModalCancel}
        >
          <>
            <KrushalOCForm
              formState={updateStatusDialogFormState}
              setFormState={setUpdateStatusDialogFormState}
              formDefinition={updateStatusDialogFormDefinition}
            />
          </>
      </Modal>
      <Space style={{marginTop:'8px', marginBottom:'8px'}}>
        <Link to="/cs">Back to Queue List</Link>
        <MediaCarouselModalButton
          buttonTitle="View Animal Documents"
          disabled={enableButton(selectedRowKeys, reportData, 'ANIMALDOCUMENTS')}
          onClickHandler={handleViewAnimalDocuments}
          modalDialogTitle="Animal Documents"
          configuration={animalDocumentsMediaCarouselConfiguration}
          />
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'ANIMALDETAILS')} onClick={handleViewAnimalClick}>View Animal Details</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'SNOOZE')} onClick={handleSnoozeClick}>Snooze</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={enableButton(selectedRowKeys, reportData, 'STATUSUPDATE')} onClick={handleUpdateCustomerServiceTaskStatusClick}>Update Customer Service Task Status</Button>
        <CommentViewerModalButton
          buttonTitle="View Data Collection and Verification Animal Comments"
          disabled={enableButton(selectedRowKeys, reportData, 'COMMENTS')}
          onClickHandler={handleViewDataVerificationAnimalCommentsClick}
          configuration={dataVerificationAnimalCommentsModalDialogConfiguration}
          />
        <Checkbox
          checked={excludeBDMInQuery}
          onChange={() => {
            setExcludeBDMInQuery(!excludeBDMInQuery)
          }}
          >
            Exclude BDM From Query
        </Checkbox>
        <ReloadOutlined onClick={() => {
          if (tableRef.current) {
            tableRef.current.reloadDataAndMoveToPageOne()
          }
        }}/>
      </Space>
      <OCTable
        /* tableKey={tableKey}
        setTableKey={setTableKey} */
        ref={tableRef}
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={tableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        postInitialLoadCallback={postInitialLoadCB}
        loadReportCallback={loadReportCB}
        loadInitialFilterDataCallback={loadInitialFilterDataCB}
        parentPageParams={pageParams}
        // showTableHeader={true}
        // omniSearchRowEnabled={true}
        // omniSearchRowBeginning={backToQueueListLinkUI}
        // omniSearchRowMiddle={viewAnimalDocumentsFarmerSnoozeAndFarmerUpdateStatusButtons}
        // actionsRow={backToQueueListLinkUIViewAnimalDocumentsFarmerSnoozeAndFarmerUpdateStatusButtons}
        // enableColumnConfiguration={true}
      />
    </>
  );
}

// export default CustomerServiceTasks
export default withAuthorization(CustomerServiceTasks, [CS_TEAM])