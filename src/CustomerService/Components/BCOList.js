import { Checkbox, Space, Button, Modal } from "antd";
import { ReloadOutlined } from '@ant-design/icons';

import { useState, useRef } from "react"
import { Link, useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import {useSelector} from "react-redux"
import dayjs from 'dayjs'

import ToastersService from "../../Services/toasters.service";

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  extractChangedValues,
} from "../../Components/Common/KrushalOCForm";

const {EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const { getPartnerReport, getPartnerReportFilterData, } = require('../../router')

const { OCTable, CommentViewerModalButton, createPageParamsFromURLString, MediaCarouselModalButton } = require('../../Components/Common/AntTableHelper')

const loadReport = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get BCO List',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getPartnerReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadFilters = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get BCO List Filter Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getPartnerReportFilterData(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const tableConfiguration = {
  // tableNavigationUrl: '/cs/bco-list',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  // enableSplittingOfServerData: true, // use this only if you have made changes to the backend
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'partner_id',
  columns: [
    {
      title: "BCO Id",
      key: "partner_id",
    },
    {
      title: "Active",
      key: "partner_active",
    },
    {
      title: 'BCO Name',
      key: 'partner_name',
    },
    {
      title: 'Primary Contact',
      key: 'primary_contact_name'
    },
    {
      title: 'Mobile Number of Primary Contact',
      key: 'mobile_number'
    },
    {
      title: 'Taluk',
      key: 'taluk_name'
    },
    {
      title: 'Village',
      key: 'village_name'
    }
  ],
  columnConfiguration: {
    partner_active: {
      headingText: 'BCO Active Or Not',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      filters: [
        { text: 'Active', value: 'Active', value_int: 1000100001 },
        { text: 'Not Active', value: 'Not Active', value_int: 1000100002 },
      ],
      defaultFilteredValue: ['Active'],
      translateFilteredValuesToAlt: 'value_int',
      altColumn: 'partner_active_id'
    },
    partner_name: {
      headingText: 'BCO Name',
      enableFilter: true,
      type: 'string',
    },
    primary_contact_name: {
      headingText: 'BCO Primary Contact Name',
      enableFilter: true,
      type: 'string',
    },
    mobile_number: {
      headingText: 'Mobile Number of BCO Primary Contact',
      enableFilter: true,
      type: 'string',
    },
    taluk_name: {
      type: 'default_array',
      dataType: 'string',
      dataType: 'int',
      altColumn: 'taluk_id',
      updateFilterDataDuringFilterCall: true,
    },
    village_name: {
      headingText: 'Village',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      updateFilterDataDuringReportCall: true,
    }
  }
}

const currentPageName = '/bco-list'

const BCOList = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { pathname, search } = location
  tableConfiguration.tableNavigationUrl = pathname
  const baseLink = pathname.substring(0, pathname.length - currentPageName.length)
  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    tableConfiguration.enableTableExport = true
    tableConfiguration.enableTableExportFromCloud = true
  }

  const [reportData, setReportData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const tableRef = useRef()

  const postInitialLoadCB = () => {
    /* assignDataToFormState(
      snoozeDialogFormDefinition,
      {
        farmer_level_snooze_choice_list: [{reference_name:'Yes', reference_id: 1000105001}, {reference_name:'No', reference_id: 1000105002}],
        // farmer_level_snooze_choice_list: [{label:'Yes', id: 1}, {label:'No', id: 2}],
      },
      snoozeDialogFormState,
      setSnoozeDialogFormState
    ) */
    /* assignDataToFormState(
      updateStatusDialogFormDefinition,
      {
        data_verification_animal_next_status_list: [
          // {reference_name_l10n:{ul:'Farmer Contacted, Follow up Call Required'},reference_id: 1000490001},
          // {reference_name_l10n:{ul:'Farmer Contacted, Detailed Process Explanation Required'},reference_id: 1000490002},
          // {reference_name_l10n:{ul:'Process Explained, Deposit Not Collected'},reference_id: 1000490003},
          // {reference_name_l10n:{ul:'Deposit Collected, Latest Photos Not Collected'},reference_id: 1000490004},
          // {reference_name_l10n:{ul:'Photos Collected, Other Process Not Completed'},reference_id: 1000490005},
          // {reference_name_l10n:{ul:'Animal Put Up For Sale'},reference_id: 1000490006},
          {reference_name_l10n:'Not Spoken To',reference_id: 1000890001},
          {reference_name_l10n:'Animal Accepted For Assigned Plan',reference_id: 1000890002},
          {reference_name_l10n:'Animal Rejected For Assigned Plan',reference_id: 1000890003},
          // {reference_name_l10n:'Process Explained, Deposit Not Collected',reference_id: 1000490003},
          // {reference_name_l10n:'Deposit Collected, Latest Photos Not Collected',reference_id: 1000490004},
          // {reference_name_l10n:'Photos Collected, Other Process Not Completed',reference_id: 1000490005},
          // {reference_name_l10n:'Animal Put Up For Sale',reference_id: 1000490006},
        ],
        // farmer_level_snooze_choice_list: [{label:'Yes', id: 1}, {label:'No', id: 2}],
      },
      updateStatusDialogFormState,
      setUpdateStatusDialogFormState
    ) */
  }

  const loadInitialFilterDataCB = (additionalParams) => {
    const reportQueryParams = {searchConfiguration: tableConfiguration}
    reportQueryParams.partnerTypeIds = [1000850002]
    return loadFilters(userToken, reportQueryParams, additionalParams)
  }

  const loadReportCB = (reportQueryParams, additionalParams) => {
    reportQueryParams.partnerTypeIds = [1000850002]
    return loadReport(userToken, reportQueryParams, additionalParams)
  }

  /* const [animalDocumentsMediaCarouselConfiguration, setAnimalDocumentsMediaCarouselConfiguration] = useState(animalDocumentsMediaCarouselInitialConfiguration)
  const handleViewAnimalDocuments = () => {
    if (Array.isArray(selectedRowKeys) && selectedRowKeys.length > 0) {
      const updatedConfiguration = {
        ...animalDocumentsMediaCarouselConfiguration,
        completeInformation: true,
        base_entity_information: {
          ...animalDocumentsMediaCarouselConfiguration.base_entity_information,
          entity_uuid: Array.isArray(selectedRowKeys) && selectedRowKeys.length > 0 ? selectedRowKeys[0] : undefined
        }
      }
      setAnimalDocumentsMediaCarouselConfiguration(updatedConfiguration)
      return true
    } else {
      return false
    }
  } */

  const enableButton = (selectedRowKeys, reportData, buttonType) => {
    let returnValue = true
    if (selectedRowKeys.length === 1) {
      const selectedRow = reportData.filter((object) => {
        return (object['partner_id'] === selectedRowKeys[0])
      })
    }
    return returnValue
  }

  const handleAddBCOClick = (event) => {
    const partnerId = -1
    // navigate({
    //   pathname: `../oc/farmer-details`,
    //   search: `?qP=${encodedParamsAsString}`
    // })
    const navigationLink = `${baseLink}/bco/${partnerId}`
    if (event.ctrlKey || event.metaKey) {
      window.open(navigationLink, '_blank')
    } else {
      navigate({
        pathname: navigationLink,
      })
    }
  }

  const handleViewBCOClick = (event) => {
    console.log('hVMC hVPC 1')
    const selectedRow = reportData.filter((object) => {
      return (object['partner_id'] === selectedRowKeys[0])
    })
    const { pathname, search } = location

    const partnerId = selectedRow[0]['partner_id']
    // navigate({
    //   pathname: `../oc/farmer-details`,
    //   search: `?qP=${encodedParamsAsString}`
    // })
    const navigationLink = `${baseLink}/bco/${partnerId}`
    if (event.ctrlKey || event.metaKey) {
      window.open(navigationLink, '_blank')
    } else {
      navigate({
        pathname: navigationLink,
      })
    }
  }

  /* const handleViewAnimalClick = (event) => {
    console.log('DCOFSA hVAC 1')
    const selectedRow = reportData.filter((object) => {
      return (object['animal_id'] === selectedRowKeys[0])
    })
    const { pathname, search } = location

    const animalId = selectedRow[0]['animal_id']
    const customerId = selectedRow[0]['customer_id']
    // navigate({
    //   pathname: `../oc/farmer-details`,
    //   search: `?qP=${encodedParamsAsString}`
    // })
    const navigationLink = `../e/farmer-animal-details/${customerId}/animal/${animalId}`
    if (event.ctrlKey || event.metaKey) {
      window.open(navigationLink + '?role=cv', '_blank')
    } else {
      navigate({
        pathname: navigationLink,
        search: `?role=cv`
      })
    }
  }

   const [dataVerificationAnimalCommentsModalDialogConfiguration, setDataVerificationAnimalCommentsModalDialogConfiguration] = useState(dataVerificationAnimalCommentsModalDialogInitialConfiguration)*/
  /* const handleViewDataVerificationAnimalCommentsClick = () => {
    const updatedConfiguration = {
      ...dataVerificationAnimalCommentsModalDialogConfiguration,
      note_query_configuration: {
        ...dataVerificationAnimalCommentsModalDialogConfiguration.note_query_configuration,
        entity_1_uuid: selectedRowKeys[0]
      }
    }
    setDataVerificationAnimalCommentsModalDialogConfiguration(updatedConfiguration)
  } */

/*   const [snoozeModalDialogVisibility, setSnoozeModalDialogVisibility] = useState(false)
  // const [updateStatusModalDialogVisibility, setUpdateStatusModalDialogVisibility] = useState(false)

  const [snoozeDialogFormState, setSnoozeDialogFormState] = useState(snoozeDialogFormInitialState)
  // const [updateStatusDialogFormState, setUpdateStatusDialogFormState] = useState(updateStatusDialogFormDefinition)
 */
  return (
    <>
      <Space style={{marginTop:'8px', marginBottom:'8px'}}>
        <Link to={`${baseLink}`}>Back to Queue List</Link>
        <Button size="small" style={{ marginLeft: 8 }} disabled={selectedRowKeys.length !== 0} onClick={handleAddBCOClick}>Add BCO</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={selectedRowKeys.length !== 1} onClick={handleViewBCOClick}>View BCO</Button>
      </Space>
      <OCTable
        /* tableKey={tableKey}
        setTableKey={setTableKey} */
        ref={tableRef}
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={tableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        // postInitialLoadCallback={postInitialLoadCB}
        loadReportCallback={loadReportCB}
        loadInitialFilterDataCallback={loadInitialFilterDataCB}
        parentPageParams={pageParams}
      />
    </>
  );
}

// export default BCOList
export default withAuthorization(BCOList, [CS_TEAM])