import { useEffect, useState, useRef, createRef } from "react";
import { Link, useNavigate, useParams, useSearchParams,  } from "react-router-dom";
import { renderToStaticMarkup } from 'react-dom/server';
import { useSelector } from "react-redux"
import { Spin, Row, Col, Space, Tabs, Button, Collapse, Flex } from 'antd'
import moment from 'moment';

import { Marker, Popup, LayerGroup, MapContainer, LayersControl, GeoJSON, TileLayer } from 'react-leaflet'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

import tileLayer from '../../Components/Dashboard/BgLocation/util/tileLayer'


import {
  KrushalOCForm,
  KrushalOCDropdown,
  assignDataToFormState,
  validateFormElements,
  validateFormElements2,
  assignValuesFromControlReferencesToFormState,
  extractChangedValues,
  assignReferencesAsListToForm,
} from "../../Components/Common/KrushalOCForm";

import {
  Territory
} from '../../Components/Common/Territory'
import ToastersService from "../../Services/toasters.service";

const { OCTable, CommentViewerModalButton, createPageParamsFromURLString, MediaCarouselModalButton } = require('../../Components/Common/AntTableHelper')

const {EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const { Panel } = Collapse
const lodashObject = require("lodash");

const { extractBasedOnLanguage, assignL10NObjectOrObjectArrayToItSelf } = require('@krushal-it/common-core')
const { getStaffBasicData, getStaffDetails, setStaffDetails, getGeographyListing, getGeoJSONs, getGeographyDataForVillage, getTerritoryAssignmentReport, deleteStaffTerritories, assignTerritoriesToStaff, getPartnerReport } = require('../../router')

const locationPinMarkerIcon = new L.Icon({
  // iconUrl: {process.env.PUBLIC_URL + "../../img/location-marker.png"}, // 'path-to-your-custom-marker-icon.png', // Replace with the path to your custom marker icon
  iconUrl: process.env.PUBLIC_URL + "../../img/location-marker.png",
  iconSize: [32, 32], // Adjust the size of your icon
  iconAnchor: [16, 32], // Adjust the anchor point if needed
  popupAnchor: [0, -32], // Adjust the popup anchor point if needed
});

const loadBasicStaffData = async (userToken, partnerId) => {
  try {
    const headers = {
      useCase: 'Get Basic Staff Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getStaffBasicData(headers, partnerId)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadStaffData = async (userToken, staffId, classifierArray) => {
  try {
    const headers = {
      useCase: 'Get Staff Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getStaffDetails(headers, staffId, classifierArray)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const saveStaffData = async (userToken, staffId, basicDataChangedValues) => {
  try {
    const headers = {
      useCase: 'Get Basic Partner Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const dataToSend = {...basicDataChangedValues, staffId: staffId}
    const response = await setStaffDetails(headers, dataToSend)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const assignVillage = async (userToken, staffId, geographyInformation) => {
  try {
    const headers = {
      useCase: 'Assign Territory to Staff',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const dataToSend = {...geographyInformation, staffId: staffId}
    const response = await assignTerritoriesToStaff(headers, dataToSend)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadTerritoryReport = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Staff Territory Assignment List',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getTerritoryAssignmentReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const deleteTerritories = async (userToken, deleteTerritoriesStructure) => {
  try {
    const headers = {
      useCase: 'Delete Staff Territory Assignment',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await deleteStaffTerritories(headers, deleteTerritoriesStructure)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const deleteVillagesOfStaff = async (userToken, staffId, villageId) => {
  try {
    const headers = {
      useCase: 'Delete Staff Territory Assignment',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await deleteStaffTerritories(headers, entityGeographyIdArray)
    // return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getAllStates = async (userToken) => {
  try {
    const headers = {
      useCase: 'Get States',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await getStateListV2(headers)
    const response = await getGeographyListing(headers, {parentGeographyTypeId: 1000320001})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getDistrictsOfState = async (userToken, stateIdArray) => {
  try {
    const headers = {
      useCase: 'Get Districts',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await getDistrictListV2(headers, stateId)
    const response = await getGeographyListing(headers, {parentGeographyIdArray: stateIdArray, parentToChildEntityRelationshipTypeId: 1000210029, parentGeographyTypeId: 1000320001, childGeographyTypeId: 1000320002})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getTaluksOfDistrict = async (userToken, districtIdArray) => {
  try {
    const headers = {
      useCase: 'Get Taluks',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await getTalukListV2(headers, districtId)
    const response = await getGeographyListing(headers, {parentGeographyIdArray: districtIdArray, parentToChildEntityRelationshipTypeId: 1000210030, parentGeographyTypeId: 1000320002, childGeographyTypeId: 1000320003})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getVillagesOfTaluk = async (userToken, talukIdArray) => {
  try {
    const headers = {
      useCase: 'Get Villages',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await getVillageList2(headers, talukId)
    const response = await getGeographyListing(headers, {parentGeographyIdArray: talukIdArray, parentToChildEntityRelationshipTypeId: 1000210031, parentGeographyTypeId: 1000320003, childGeographyTypeId: 1000320004})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getGeoJSONsForTaluk = async (userToken, talukIdArray) => {
  try {
    const headers = {
      useCase: 'Get Districts',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await getDistrictListV2(headers, stateId)
    const response = await getGeoJSONs(headers, {parentGeographyIdArray: talukIdArray, parentToChildEntityRelationshipTypeId: 1000210031, parentGeographyTypeId: 1000320003, childGeographyTypeId: 1000320004})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getBCOLocations = async (userToken, talukIdArray) => {
  try {
    const headers = {
      useCase: 'Get BCO Geo Locations',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await getDistrictListV2(headers, stateId)
    const queryParams = {
      searchConfiguration: {
        partner_active: {
          "enableFilter": true,
          "type": "default_array",
          "dataType": "int",
          "altColumn": "partner_active_id"
        },
        taluk_name: {
          "type": "default_array",
          "dataType": "int",
          "altColumn": "taluk_id",
        },
      },
      selectedColumns: [
        "partner_id",
        "partner_active",
        "taluk_name",
        "bco_location",
      ],
      filters: {
        partner_active: [
          1000100001
        ],
        taluk_name: [
          ...talukIdArray
        ]
      },
      forceLoadAllColumns: true,
    }

    queryParams.partnerTypeIds = [1000850002]
    const response = await getPartnerReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadGeographyData = async (userToken, villageId) => {
  try {
    const headers = {
      useCase: 'Get Geography Data For Village',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getGeographyDataForVillage(headers, villageId)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const staffBasicDetailsFormDefinition = {
  formControls: {
    staffId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Staff Id",
      readOnly: 1,
    },
    staffName: {
      ocFormRef: createRef(),
      type: "text-l10n_2",
      label: "Staff Name",
      placeHolderText: "Staff Name",
      validations: [
        { type: "Mandatory" },
      ],
      errorMessage: "Staff Name is mandatory",
    },
    staffMobileNumber: {
      ocFormRef: createRef(),
      type: "text-numeric_2",
      label: "Staff Mobile Number",
      placeHolderText: "Enter Staff Mobile Number",
      validations: [
        { type: "regex", regexArray: ["IndiaMobileNumber"] },
      ],
      errorMessage: "Enter a valid mobile number!",
    },
    staffType: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "Staff Type",
      placeHolderText: "Select Staff Type",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      validations: [
        { type: "Mandatory" },
      ],
      referenceCategoryId: 10002300,
      errorMessage: "Choose a staff type!",
    },
    vehicleType: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "Vehicle Type",
      placeHolderText: "Select Vehicle Type",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      /* validations: [
        { type: "Mandatory" },
      ], */
      referenceCategoryId: 10012400,
      errorMessage: "Choose a vehicle type!",
    },
  },
  visibleFormControls: [
    "staffId",
    "staffName",
    "staffMobileNumber",
    "staffType",
    "vehicleType",
  ],
  elementToDataMapping: {
    staffId: 'staff_id',
    staffName: 'staff_name_l10n',
    staffMobileNumber: 'mobile_number',
    staffType: 'staff_type_id',
    vehicleType: 'staff_vehicle_type_1',
  },
  dataToElementMapping: {
    staff_id: 'staffId',
    staff_name_l10n: 'staffName',
    mobile_number: 'staffMobileNumber',
    staff_type_id: 'staffType',
    staff_vehicle_type_1: 'vehicleType',
  },
}

const staffBasicDetailsFormInitialState = {
}

const assignedTerritoryTableConfiguration = {
  // tableNavigationUrl: '/cs/bco-list',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  // enableSplittingOfServerData: true, // use this only if you have made changes to the backend
  enableColumnConfiguration: true,
  getAllDataAtOnce: true,
  disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'entity_geography_id',
  columns: [
    {
      title: "Entity Geography Id",
      key: "entity_geography_id",
    },
    {
      title: "State",
      key: "state_name_l10n",
    },
    {
      title: 'District',
      key: 'district_name_l10n',
    },
    {
      title: 'Taluk',
      key: 'taluk_name_l10n',
    },
    {
      title: 'Village',
      key: 'village_name_l10n',
    },
  ],
  columnConfiguration: {
    state_name_l10n: {
      headingText: 'Staff Name',
      enableFilter: true,
      formatType: 'l10n',
      type: 'string',
    },
    district_name_l10n: {
      headingText: 'Staff Name',
      enableFilter: true,
      formatType: 'l10n',
      type: 'string',
    },
    taluk_name_l10n: {
      headingText: 'Staff Name',
      enableFilter: true,
      formatType: 'l10n',
      type: 'string',
    },
    village_name_l10n: {
      headingText: 'Staff Name',
      enableFilter: true,
      formatType: 'l10n',
      type: 'string',
    },
  }
}

const MapComponent = (props) => {
  // Define different styles for subsets
  const userToken = useSelector((state)=>state.user.userToken)

  const [layerGroup, setLayerGroup] = useState(null)
  const [loading, setLoading] = useState(false)
  
  const districtLayerRef = createRef()
  const talukLayerRef = createRef()
  const villageLayerRef = createRef()
  const bcoLocationLayerRef = createRef()

  // const [districtLayerKey, setDistrictLayerKey] = useState(0)
  // const [talukLayerKey, setTalukLayerKey] = useState(0)
  // const [villageLayerKey, setVillageLayerKey] = useState(0)
  // const [bcoLocationsLayerKey, setBCOLocationsLayerKey] = useState(0)

  useEffect(()=>{
    if (districtLayerRef.current) {
      districtLayerRef.current.clearLayers().addData(props.districtsGeoJSONData);
    }
  },[props.districtsGeoJSONData])

  useEffect(()=>{
    if (talukLayerRef.current) {
      talukLayerRef.current.clearLayers().addData(props.taluksGeoJSONData);
    }
  },[props.taluksGeoJSONData])

  useEffect(()=>{
    // setVillageLayerKey(prevKey => prevKey + 1)
    if (villageLayerRef.current) {
      villageLayerRef.current.clearLayers().addData(props.villagesGeoJSONData);
    }
  },[props.villagesGeoJSONData])

  useEffect(()=>{
    // setBCOLocationsLayerKey(prevKey => prevKey + 1)
    if (layerGroup) {
      layerGroup.clearLayers()// .addData(props.villagesGeoJSONData);
      {props.bcoLocations.map((marker, index) => {
        const newMarker = (
          <Marker key={index} position={marker.position} icon={locationPinMarkerIcon}>
            <Popup><div>
                  <div><strong>Name:</strong> {marker.name}</div>
                  <div><strong>Taluk:</strong> {marker.taluk}</div>
                  <div><strong>Village:</strong> {marker.village}</div>
                </div>
                </Popup>
          </Marker>
        )
        layerGroup.addLayer(newMarker);
      })}
    }
  },[props.bcoLocations, layerGroup])

  const styleFunction = (feature) => {
    const subset = feature.properties.subset;

    // Define your styles based on subsets
    if (subset === 'A') {
      return { fillColor: '#0000FF66', color: '#0000FF99', weight: 0.6 };
    } else if (subset === 'B') {
      return { fillColor: '#FF0000', color: '#FF0000', weight: 0.8 };
    } else if (subset === 'C') {
      return { fillColor: '#00009966', color: '#00009999', weight: 0.4 };
    } else if (subset === 'D') {
      return { fillColor: '#0000BB99', color: '#0000BBBB', weight: 0.6 };
    } else if (subset === 'E') {
      return { fillColor: '#00003366', color: '#00003399', weight: 0.2 };
    } else if (subset === 'F') {
      return { fillColor: '00006666', color: '00006699', weight: 0.4 };
    } else {
      // Default style
      return { fillColor: 'gray', color: 'gray', weight: 2 };
    }
  }

  const handleAssignGeographyOnMapClick = async (geographyId) => {
    console.log('S MC hAGOMC 1, geographyId = ', geographyId)
    setLoading(true)
    const assignVillageResponse = await assignVillage(userToken, props.staffId, {villages_to_assign: [geographyId]})
    if (assignVillageResponse.return_code === 0) {
      await props.assignedTerritoryTableRef.current.reloadDataAndMoveToPageOne()
      ToastersService.successToast('Successfully assigned a geography')
    } else {
      ToastersService.failureToast('Could not assign geography')
    }
    setLoading(false)
  }

  const handleUnassignGeographyOnMapClick = async (geographyId) => {
    console.log('S MC hUGOMC 1, geographyId = ', geographyId)
    setLoading(true)
    const deleteVillagesOfStaffResponse = await deleteTerritories(userToken, {staff_type_id: 1000460003, staff_id: props.staffId, geography_type_id: 1000320004, geography_id: geographyId})
    if (deleteVillagesOfStaffResponse.return_code === 0) {
      await props.assignedTerritoryTableRef.current.reloadDataAndMoveToPageOne()
      ToastersService.successToast('Successfully unassigned a geography')
    } else {
      ToastersService.failureToast('Could not assign geography')
    }
    setLoading(false)
  }
  
  const addButtonClickHandler = async (event, geographyId, layer) => {
    console.log('S MC lbp oA 1, event = ', event)
    console.log('S MC lbp oA 1a, geographyId = ', geographyId)
    const button = event.target._popup._content.querySelector('button')
    if (button) {
      if (button.id.indexOf('unAssignGeography') > -1) {
        button.addEventListener('click', () => {
          handleUnassignGeographyOnMapClick(geographyId)
          layer.closePopup()
        })
      } else {
        button.addEventListener('click', () => {
          handleAssignGeographyOnMapClick(geographyId)
          layer.closePopup()
        })
      }
    }
    /* console.log('S MC lbp oA 2, button = ', button)
    if (button.id.indexOf('unAssignedGeography') > -1) {
      console.log('unassign')
    } else {
      console.log('assign')
      const resposeFromSavingCropScreenData = await assignVillage(userToken, props.staffId, {villages_to_assign: [geographyId]})
      if (resposeFromSavingCropScreenData.return_code === 0) {
        props.assignedTerritoryTableRef.current.reloadDataAndMoveToPageOne()
      }
    } */
    /* switch (feature.properties.subset) {
      case 'A':
        handleAssignGeographyOnMapClick(feature.properties.id)
        break
      case 'B':
        handleUnassignGeographyOnMapClick(feature.properties.id)
        break
    }
    if (button) {
      button.addEventListener('click', handleGeographyAssignmentButtonClick)
    } */
  }
  
  return (
    <div>
    <Spin spinning={loading} tip="Loading...">
      <MapContainer
      
        // center={[/* specify your center coordinates */]}
        center={[17.9853025, 74.4127819]}
        // zoom={/* specify your zoom level */}
        zoom={10}
        style={{ height: '500px', width: '100%' }}
      >
        <LayersControl position="topright">
          <LayersControl.BaseLayer checked name="Base Map">
            {/* Add a tile layer (you can choose any provider) */}
            <TileLayer
              {...tileLayer}
            />
          </LayersControl.BaseLayer>
          {props.districtsGeoJSONData !== undefined && Array.isArray(props.districtsGeoJSONData) && props.districtsGeoJSONData.length > 0 ? (
          <LayersControl.Overlay
            name="Districts"
            // key={districtLayerKey}
            >
              {/* Add GeoJSON layer with custom style function */}
            <GeoJSON
              ref={districtLayerRef}
              data={props.districtsGeoJSONData}
              style={styleFunction}
              onEachFeature={(feature, layer) => {
                // Add any additional functionality per feature if needed
                const districtName = feature.properties.district
                let popupContent = (
                  <>ABC</>
                )
                switch (feature.properties.subset) {
                  case 'E':
                    popupContent = (
                      <>
                        <p><strong>Neighbouring District:</strong> {districtName}</p>
                      </>
                    )
                    break 
                  case 'F':
                    popupContent = (
                      <>
                        <p><strong>Current District:</strong> {districtName}</p>
                      </>
                    )
                    break 
                }
                // layer.bindPopup(`${feature.properties.popup}`)
                // layer.bindPopup(popupContent)
                // layer.bindPopup('ABC')

                const popupString = renderToStaticMarkup(popupContent)

                // Create a DOM node from the HTML string
                const popupNode = new DOMParser().parseFromString(popupString, 'text/html').body.firstChild

                // Bind the popup to the layer
                layer.bindPopup(popupNode)
              }}
            />
          </LayersControl.Overlay>
          ) : null}
          {props.taluksGeoJSONData !== undefined && Array.isArray(props.taluksGeoJSONData) && props.taluksGeoJSONData.length > 0 ? (
          <LayersControl.Overlay
            name="Taluks"
            // key={talukLayerKey}
            >
              {/* Add GeoJSON layer with custom style function */}
              <GeoJSON
                ref={talukLayerRef}
                data={props.taluksGeoJSONData}
                style={styleFunction}
                onEachFeature={(feature, layer) => {
                  // Add any additional functionality per feature if needed
                  const talukName = feature.properties.taluk
                  let popupContent = (
                    <>ABC</>
                  )
                  switch (feature.properties.subset) {
                    case 'C':
                      popupContent = (
                        <>
                          <p><strong>Neighbouring Taluk:</strong> {talukName}</p>
                        </>
                      )
                      break 
                    case 'D':
                      popupContent = (
                        <>
                          <p><strong>Current Taluk:</strong> {talukName}</p>
                        </>
                      )
                      break 
                  }
                  // layer.bindPopup(`${feature.properties.popup}`)
                  // layer.bindPopup(popupContent)
                  // layer.bindPopup('ABC')

                  const popupString = renderToStaticMarkup(popupContent)

                  // Create a DOM node from the HTML string
                  const popupNode = new DOMParser().parseFromString(popupString, 'text/html').body.firstChild

                  // Bind the popup to the layer
                  layer.bindPopup(popupNode)
                }}
              />
          </LayersControl.Overlay>
          ) : null}
          {props.villagesGeoJSONData !== undefined && Array.isArray(props.villagesGeoJSONData) && props.villagesGeoJSONData.length > 0 ? (
          <LayersControl.Overlay
            // key={villageLayerKey}
            name="Village"
            >
              {/* Add GeoJSON layer with custom style function */}
              <GeoJSON
                ref={villageLayerRef}
                data={props.villagesGeoJSONData}
                style={styleFunction}
                onEachFeature={(feature, layer) => {
                  // Add any additional functionality per feature if needed
                  const districtName = feature.properties.district
                  const talukName = feature.properties.taluk
                  const villageName = feature.properties.village
                  let popupContent = (
                    <>ABC</>
                  )
                  let geographyAssignmentButtonId
                  switch (feature.properties.subset) {
                    case 'A':
                      geographyAssignmentButtonId = 'assignGeography-' + feature.properties.id
                      popupContent = (
                        <div>
                          <div><strong>District:</strong> {districtName}</div>
                          <div><strong>Taluk:</strong> {talukName}</div>
                          <div><strong>Village Not Assigned:</strong> {villageName}</div>
                          <button id={geographyAssignmentButtonId}>Assign Geography</button>
                        </div>
                      )
                      break
                    case 'B':
                      geographyAssignmentButtonId = 'unAssignGeography-' + feature.properties.id
                      popupContent = (
                        <div>
                          <div><strong>District:</strong> {districtName}</div>
                          <div><strong>Taluk:</strong> {talukName}</div>
                          <div><strong>Village Currently Assigned:</strong> {villageName}</div>
                          <button id={geographyAssignmentButtonId}>Unassign Geography</button>
                        </div>
                      )
                      break 
                  }
                  // layer.bindPopup(`${feature.properties.popup}`)
                  // layer.bindPopup(popupContent)
                  // layer.bindPopup('ABC')

                  const popupString = renderToStaticMarkup(popupContent)

                  // Create a DOM node from the HTML string
                  const popupNode = new DOMParser().parseFromString(popupString, 'text/html').body.firstChild

                  // Bind the popup to the layer
                  // layer.bindPopup(popupNode)
                  layer.bindPopup(popupNode, { maxWidth: 'auto' }).on('add', (event) => addButtonClickHandler(event, feature.properties.id, layer))
                }}
              />
          </LayersControl.Overlay>
          ) : null}
          {props.bcoLocations !== undefined && Array.isArray(props.bcoLocations) && props.bcoLocations.length > 0 ? (
            <LayersControl.Overlay
            name="BCO Locations"
            // key={bcoLocationsLayerKey}
            >
              <LayerGroup
                // ref={bcoLocationLayerRef}
                ref={(ref) => setLayerGroup(ref && ref.leafletElement)}
              >
                {props.bcoLocations.map((marker, index) => (
                    <Marker key={index} position={marker.position} icon={locationPinMarkerIcon}>
                      <Popup><div>
                            <div><strong>Name:</strong> {marker.name}</div>
                            <div><strong>Taluk:</strong> {marker.taluk}</div>
                            <div><strong>Village:</strong> {marker.village}</div>
                          </div>
                          </Popup>
                    </Marker>
                  )
                )}
              </LayerGroup>
          </LayersControl.Overlay>
          ) : null}
        </LayersControl>
      </MapContainer>
    </Spin>
    </div>
  )
}

const Staff = () => {
  const params = useParams()
  const navigate = useNavigate()
  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    assignedTerritoryTableConfiguration.enableTableExport = true
    assignedTerritoryTableConfiguration.enableTableExportFromCloud = true
  }

  const stateDropdownRef = createRef()
  const districtDropdownRef = createRef()
  const talukDropdownRef = createRef()
  const villageDropdownRef = createRef()


  const [searchParams] = useSearchParams()
  // const uncompressedFilterParamsJsonString = decodeURIComponent(searchParams.get('qP'))// urlEncodedJson)
  // const queryParams = JSON.parse(uncompressedFilterParamsJsonString)
  // const customerId = queryParams.customerId
  const staffId = params.staffId

  const [staffBasicDetailsFormState, setStaffBasicDetailsFormState] = useState(staffBasicDetailsFormInitialState)
  const [selectedVillageIdArray, setSelectedVillageIdArray] = useState([])
  const [districtsGeoJSONArray, setDistrictsGeoJSONArray] = useState([])
  const [taluksGeoJSONArray, setTaluksGeoJSONArray] = useState([])
  const [villagesGeoJSONArray, setVillagesGeoJSONArray] = useState([])
  const [bcoLocations, setBCOLocations] = useState([])

  const territoryRef = createRef()
  

  const [assignedTerritoryReportData, setAssignedTerritoryReportData] = useState([]);
  const [assignedTerritorySelectedRowKeys, setAssignedTerritorySelectedRowKeys] = useState([])

  const assignedTerritoryTableRef = useRef()

  const drawMapForAssignedGeography = async (territoryList) => {
    const districtIdsInAssignedTerritories = []
    const talukIdsInAssignedTerritories = []
    const villageIdsInAssignedTerritories = []
    const districtArray = []
    const talukArray = []
    const villageArray = []
    const bcoArray = []
    for (const territoryRow of territoryList) {
      if (territoryRow['district_id'] !== undefined && territoryRow['district_id'] !== null) {
        districtIdsInAssignedTerritories.push(territoryRow['district_id'])
      }
      if (territoryRow['taluk_id'] !== undefined && territoryRow['taluk_id'] !== null) {
        talukIdsInAssignedTerritories.push(territoryRow['taluk_id'])
      }
      if (territoryRow['village_id'] !== undefined && territoryRow['village_id'] !== null) {
        villageIdsInAssignedTerritories.push(territoryRow['village_id'])
      }
    }
    const geoJSONsResponse = await getGeoJSONsForTaluk(userToken, talukIdsInAssignedTerritories)
    const bcoLocationsResponse = await getBCOLocations(userToken, talukIdsInAssignedTerritories)
    if (geoJSONsResponse.return_code === 0 && bcoLocationsResponse.return_code === 0) {
      const villagesWithGeoJSONData = geoJSONsResponse.village_geojson_array
      const districtsWithGeoJSONData = geoJSONsResponse.district_geojson_array
      const taluksWithGeoJSONData = geoJSONsResponse.taluk_geojson_array
      if (villagesWithGeoJSONData.length === 0) {
        ToastersService.warningToast('Could not load geography data')
      } else {
        const districtsAdded = []
        for (const districtWithGeoJSONData of districtsWithGeoJSONData) {
          const geographyId = districtWithGeoJSONData['geography_id']
          if (districtsAdded.includes(geographyId)) {
            continue
          }
          districtsAdded.push(geographyId)
          const geographyGeoJSON = districtWithGeoJSONData['geography_geojson']
          const cousinOnceRemovedDistrict = districtWithGeoJSONData['geography_name_l10n'] !== undefined && districtWithGeoJSONData['geography_name_l10n'] !== null ? extractBasedOnLanguage(districtWithGeoJSONData['geography_name_l10n']) : 'Not Identified'
          const geoJSONProperties = { id: geographyId, district: cousinOnceRemovedDistrict, subset: 'E' }
          if (districtIdsInAssignedTerritories.includes(geographyId)) {
            geoJSONProperties.subset = 'F'
          }
          const individualGeoJSONData = {
            type: 'Feature',
            properties: geoJSONProperties,
            geometry: geographyGeoJSON,
          }
          districtArray.push(individualGeoJSONData)
        }

        const taluksAdded = []
        for (const talukWithGeoJSONData of taluksWithGeoJSONData) {
          const geographyId = talukWithGeoJSONData['geography_id']
          if (taluksAdded.includes(geographyId)) {
            continue
          }
          taluksAdded.push(geographyId)
          const geographyGeoJSON = talukWithGeoJSONData['geography_geojson']
          const siblingTaluk = talukWithGeoJSONData['sibling_gen_geography_name_l10n'] !== undefined && talukWithGeoJSONData['sibling_gen_geography_name_l10n'] !== null ? extractBasedOnLanguage(talukWithGeoJSONData['sibling_gen_geography_name_l10n']) : 'Not Identified'
          const geoJSONProperties = { id: geographyId, taluk: siblingTaluk, subset: 'C' }
          if (talukIdsInAssignedTerritories.includes(geographyId)) {
            geoJSONProperties.subset = 'D'
          }
          const individualGeoJSONData = {
            type: 'Feature',
            properties: geoJSONProperties,
            geometry: geographyGeoJSON,
          }
          talukArray.push(individualGeoJSONData)
        }

        for (const villageWithGeoJSONData of villagesWithGeoJSONData) {
          const geographyId = villageWithGeoJSONData['geography_id']
          const talukName = villageWithGeoJSONData['current_gen_geography_name_l10n'] !== undefined && villageWithGeoJSONData['current_gen_geography_name_l10n'] !== null ? extractBasedOnLanguage(villageWithGeoJSONData['current_gen_geography_name_l10n']) : 'Not Identified'
          const villageName = villageWithGeoJSONData['next_gen_geography_name_l10n'] !== undefined && villageWithGeoJSONData['next_gen_geography_name_l10n'] !== null ? extractBasedOnLanguage(villageWithGeoJSONData['next_gen_geography_name_l10n']) : 'Not Identified'
          const districtName = villageWithGeoJSONData['district_name_l10n'] !== undefined && villageWithGeoJSONData['district_name_l10n'] !== null ? extractBasedOnLanguage(villageWithGeoJSONData['district_name_l10n']) : 'Not Identified'
          // const stateName = villageWithGeoJSONData['state_name_l10n'] !== undefined && villageWithGeoJSONData['state_name_l10n'] !== null ? extractBasedOnLanguage(villageWithGeoJSONData['state_name_l10n']) : 'Not Identified'
          const geographyGeoJSON = villageWithGeoJSONData['geography_geojson']
          const geoJSONProperties = { id: geographyId, village: villageName, taluk: talukName, district: districtName, subset: 'A' }
          if (villageIdsInAssignedTerritories.includes(geographyId)) {
            geoJSONProperties.subset = 'B'
          }
          const individualGeoJSONData = {
            type: 'Feature',
            properties: geoJSONProperties,
            geometry: geographyGeoJSON,
          }
          villageArray.push(individualGeoJSONData)
        }

        for (const bcoAndLocation of bcoLocationsResponse.report) {
          const bcoLocation = bcoAndLocation['bco_location']
          if (bcoLocation !== undefined && bcoLocation.latitude !== undefined && bcoLocation.longitude !== undefined) {
            const latitude = bcoLocation.latitude
            const longitude = bcoLocation.longitude
            const partnerName = bcoAndLocation['partner_name']
            const talukName = bcoAndLocation['taluk_name']
            const villageName = bcoAndLocation['village_name']
            const bcoObject = { name: partnerName, taluk: talukName, village: villageName, position: [latitude, longitude]}
            bcoArray.push(bcoObject)
          }
        }
        setDistrictsGeoJSONArray(districtArray)
        setTaluksGeoJSONArray(talukArray)
        setVillagesGeoJSONArray(villageArray)
        setBCOLocations(bcoArray)
      }
    } else {
      ToastersService.warningToast('Could not load geography data')
    }
  }

  const loadAssignedTerritoryReportCB = async (reportQueryParams, additionalParams) => {
    reportQueryParams.staffId = staffId
    const returnValue = await loadTerritoryReport(userToken, reportQueryParams, additionalParams)

    if (!Array.isArray(stateDropdownRef.current.getOptions()) || stateDropdownRef.current.getOptions().length === 0) {
      const stateListResponse = await getAllStates(userToken)
      const stateListUnfiltered = assignL10NObjectOrObjectArrayToItSelf(stateListResponse.list_data, ['geography_name_l10n'])
  
      const statesToBeRemoved = []
      for (const assignedTerritoryRow of returnValue.report) {
        if (assignedTerritoryRow['state_id'] !== null && assignedTerritoryRow['district_id'] === null) {
          statesToBeRemoved.push(assignedTerritoryRow['state_id'])
        }
      }
      const stateList = stateListUnfiltered.filter(object => {
        return !(statesToBeRemoved.includes(object['geography_id']))
      })
      stateDropdownRef.current.setOptions(stateList)
    }
    await drawMapForAssignedGeography(returnValue.report)
    return returnValue
  }

  const loadDataAsync = async () => {
    try {
      const excludeFromClassifiersFormControlArray = ['staff_id', 'staff_name_l10n', 'mobile_number', 'staff_state_id', 'staff_district_id', 'staff_taluk_id']
      // const classifiers = ['partner_operational_village_id']
      const classifiers = []
      for (const visibleFormControl of staffBasicDetailsFormDefinition.visibleFormControls) {
        const columnMapping = staffBasicDetailsFormDefinition.elementToDataMapping[visibleFormControl]
        if (!excludeFromClassifiersFormControlArray.includes(columnMapping)) {
          classifiers.push(columnMapping)
        }
      }

      if (staffId !== '-1') {
        const basicDataResponse = await loadBasicStaffData(userToken, staffId)
        const staffDataResponse = await loadStaffData(userToken, staffId, classifiers)

        let completeDataLoaded = {}
        /* if (partnerDataResponse && partnerDataResponse.data && partnerDataResponse.data['partner_operational_village_id'] !== undefined && partnerDataResponse.data['partner_operational_village_id'] !== null && Array.isArray(partnerDataResponse.data['partner_operational_village_id']) && partnerDataResponse.data['partner_operational_village_id'].length > 0) {
          // get stateid, districtid, talukid, and also the district_list, taluk_list and village_list
          const geographyData = await loadGeographyData(userToken, partnerDataResponse.data['partner_operational_village_id'][0])
          const stateList = assignL10NObjectOrObjectArrayToItSelf(geographyData.state_list, ['state_name_l10n'])
          const districtList = assignL10NObjectOrObjectArrayToItSelf(geographyData.district_list, ['district_name_l10n'])
          const talukList = assignL10NObjectOrObjectArrayToItSelf(geographyData.taluk_list, ['taluk_name_l10n'])
          const villageList = assignL10NObjectOrObjectArrayToItSelf(geographyData.village_list, ['village_name_l10n'])
          completeDataLoaded.bco_state_id = geographyData.state_id
          completeDataLoaded.bco_district_id = geographyData.district_id
          completeDataLoaded.bco_taluk_id = geographyData.taluk_id
          completeDataLoaded.bco_state_id_list = stateList
          completeDataLoaded.bco_district_id_list = districtList
          completeDataLoaded.bco_taluk_id_list = talukList
          completeDataLoaded.partner_operational_village_id_list = villageList
          // setSelectedVillageIdArray([partnerDataResponse.data['partner_operational_village_id'][0]])
        } else {
          const stateListResponse = await getAllStates(userToken)
          if (stateListResponse.return_code === 0) {
            const stateList = assignL10NObjectOrObjectArrayToItSelf(stateListResponse.list_data, ['state_name_l10n'])
            completeDataLoaded.bco_state_id_list = stateList
          }
        } */
        if (basicDataResponse.return_code === 0 && staffDataResponse.return_code === 0) {
          if (basicDataResponse.result && basicDataResponse.result.mobile_number && basicDataResponse.result.mobile_number !== null) {
            if (basicDataResponse.result.mobile_number.startsWith('+91')) {
              basicDataResponse.result.mobile_number = basicDataResponse.result.mobile_number.slice(3)
            }
          }
          completeDataLoaded = {...completeDataLoaded, ...basicDataResponse.result, ...staffDataResponse.data}
          const updatedPartnerBasicDetailsFormState = assignDataToFormState(
            staffBasicDetailsFormDefinition,
            completeDataLoaded,
            staffBasicDetailsFormState,
            setStaffBasicDetailsFormState
          )
        } else {
          ToastersService.failureToast('Could not load data')
        }
      }
    } catch (error) {
      console.log('CS C CSBCO lDA 10, error')
      console.log('CS C CSBCO lDA 10a, error = ', error)
      throw error
    } finally {
      // setLoading(false)
    }
  }
  
  useEffect(()=>{
    assignReferencesAsListToForm(staffBasicDetailsFormDefinition)
    loadDataAsync()
  },[])

  const handleSavePartnerBasicDataClick = async () => {
    /* const [currentFormState, validationFailed] = validateFormElements(
      partnerBasicDetailsFormDefinition,
      partnerBasicDetailsFormState,
      setPartnerBasicDetailsFormState
    ) */
    const [currentFormState, validationFailed] = validateFormElements2(
      staffBasicDetailsFormDefinition,
      staffBasicDetailsFormState,
      setStaffBasicDetailsFormState
    )
    if (validationFailed) {
      // do something
      console.log("Validation Failed");
      ToastersService.failureToast("Validation failed!");
    } else {
      // const geographyValues = territoryRef.current.getSelectedGeography()
      // verify return code?
      console.log("you can save now");
      const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(staffBasicDetailsFormDefinition, currentFormState, setStaffBasicDetailsFormState)
      const basicDataChangedValues = extractChangedValues(staffBasicDetailsFormDefinition, updatedCurrentFormState)
      /* if (geographyValues.selectedVillageIdArray) {
        basicDataChangedValues['partner_operational_village_id'] = geographyValues['selectedVillageIdArray']
      } */

      const fieldsToIgnoreInSaving = ['staff_state_id', 'staff_district_id', 'staff_taluk_id']
      for (const fieldChanged of Object.keys(basicDataChangedValues)) {
        if (fieldsToIgnoreInSaving.includes(fieldChanged)) {
          delete basicDataChangedValues[fieldChanged]
        }
      }

      if (basicDataChangedValues['mobile_number'] && basicDataChangedValues['mobile_number'] !== null) {
        basicDataChangedValues['mobile_number'] = '+91' + basicDataChangedValues['mobile_number']
      }
      const resposeFromSavingCropScreenData = await saveStaffData(userToken, staffId, basicDataChangedValues)
      if (resposeFromSavingCropScreenData.return_code === 0) {
        await loadDataAsync()
        ToastersService.successToast("Updated Staff Data")
      } else {
        ToastersService.failureToast("Failed To Updated Staff Data")
      }
    }
  }

  const handleChangeStateClick = async (selectedStates) => {
    if (Array.isArray(selectedStates) && selectedStates.length > 0) {
      const districtResponse = await getDistrictsOfState(userToken, selectedStates)
      const districtUnfilteredList = assignL10NObjectOrObjectArrayToItSelf(districtResponse.list_data, ['geography_name_l10n'])
      const districtsToBeRemoved = []
      for (const assignedTerritoryRow of assignedTerritoryReportData) {
        if (assignedTerritoryRow['district_id'] !== null && assignedTerritoryRow['taluk_id'] === null) {
          districtsToBeRemoved.push(assignedTerritoryRow['district_id'])
        }
      }
      const districtList = districtUnfilteredList.filter(object => {
        return !(districtsToBeRemoved.includes(object['geography_id']))
      })
      districtDropdownRef.current.setOptions(districtList)
    }
  }

  const handleChangeDistrictClick = async (selectedDistricts) => {
    if (Array.isArray(selectedDistricts) && selectedDistricts.length > 0) {
      const talukResponse = await getTaluksOfDistrict(userToken, selectedDistricts)
      const talukListUnfiltered = assignL10NObjectOrObjectArrayToItSelf(talukResponse.list_data, ['geography_name_l10n'])
      const taluksToBeRemoved = []
      for (const assignedTerritoryRow of assignedTerritoryReportData) {
        if (assignedTerritoryRow['taluk_id'] !== null && assignedTerritoryRow['village_id'] === null) {
          taluksToBeRemoved.push(assignedTerritoryRow['taluk_id'])
        }
      }
      const talukList = talukListUnfiltered.filter(object => {
        return !(taluksToBeRemoved.includes(object['geography_id']))
      })
      talukDropdownRef.current.setOptions(talukList)
    }
  }

  const handleChangeTalukClick = async (selectedTaluks) => {
    if (Array.isArray(selectedTaluks) && selectedTaluks.length > 0) {
      const villageResponse = await getVillagesOfTaluk(userToken, selectedTaluks)
      const villageListUnfiltered = assignL10NObjectOrObjectArrayToItSelf(villageResponse.list_data, ['geography_name_l10n'])
      const villagesToBeRemoved = []
      for (const assignedTerritoryRow of assignedTerritoryReportData) {
        if (assignedTerritoryRow['village_id'] !== null) {
          villagesToBeRemoved.push(assignedTerritoryRow['village_id'])
        }
      }
      const villageList = villageListUnfiltered.filter(object => {
        return !(villagesToBeRemoved.includes(object['geography_id']))
      })

      villageDropdownRef.current.setOptions(villageList)
    }
  }

  const handleAssignGeographyToStaffClick = async () => {    
    if (villageDropdownRef.current !== null && Array.isArray(villageDropdownRef.current.getValue()) && villageDropdownRef.current.getValue().length > 0) {
      const villagesToAssign = villageDropdownRef.current.getValue()
      const resposeFromSavingCropScreenData = await assignVillage(userToken, staffId, {villages_to_assign: villageDropdownRef.current.getValue()})
      if (resposeFromSavingCropScreenData.return_code === 0) {
        assignedTerritoryTableRef.current.reloadDataAndMoveToPageOne()
        const currentVillages = villageDropdownRef.current.getOptions()
        const villagesToBeSet = currentVillages.filter(object => {
          if (!villagesToAssign.includes(object['geography_id'])) {
            return true
          } else {
            return false
          }
        })
        villageDropdownRef.current.setValue([])
        villageDropdownRef.current.setOptions(villagesToBeSet)
      }
    } else {
      ToastersService.warningToast('Nothing to Assign, please assign 1 or more villages')
    }
  }

  const handleDeleteTerritoryAllocationsClick = async () => {
    // send keys and token
    const deleteTerritoriesResponse = await deleteTerritories(userToken, {to_be_unassigned_territories: assignedTerritorySelectedRowKeys})
    if (deleteTerritoriesResponse.return_code === 0) {
      // refresh table
      await assignedTerritoryTableRef.current.reloadDataAndMoveToPageOne()
      ToastersService.successToast('Successfully unassigned territory')
    } else {
      ToastersService.successToast('Could not unassign territory')
    }
  }

  return (
    <>
      <div>
        <Link to={-1}>Back to List</Link>
      </div>
      <Collapse defaultActiveKey={['1']} >
        <Panel header="Basic Staff Details" key="1">
          <div style={{display:'flex', alignItems:"flex-start"}}>
            <div style={{flex:"1"}}>
              <KrushalOCForm
                formState={staffBasicDetailsFormState}
                setFormState={setStaffBasicDetailsFormState}
                formDefinition={staffBasicDetailsFormDefinition}
                // additionalParams={additionalParams}
              />
              {/* <Territory
                loading={loading}
                configuration={territoryConfiguration}
                selectedVillageIdArray={selectedVillageIdArray}
                ref={territoryRef}
                /> */}
            </div>
            <div style={{alignSelf: "flex-end", display:"flex", flexDirection: "column"}}>
              <Button
                onClick={() => handleSavePartnerBasicDataClick()}
                >
                Save
              </Button>
              <Button style={{marginTop:"4px"}}>Future Use Button</Button>
            </div>
          </div>
        </Panel>
        {staffId === '-1' ? null : (
          <Panel header="Territories" key="2">
            <div>
              <div>
                <Row style={{marginTop: '8px', marginBottom: '8px'}} gutter={[16, 16]}>
                  <Col span={8}>
                    <KrushalOCDropdown
                      ref={stateDropdownRef}
                      type='single-select-l10n_2'
                      label='State'
                      // placeHolderText={columnConfiguration.editablePlaceHolderText}
                      // validations={columnConfiguration.editableValidations}
                      // errorMessage={columnConfiguration.editableErrorMessage}
                      // fieldNames={columnConfiguration.editableDropdownFieldNames}
                      labelKeyInList='geography_name_l10n'
                      valueKeyInList='geography_id'
                      // options={record[columnKey + '_list'] ? record[columnKey + '_list'] : columnConfiguration.editableList}
                      // value={columnValue}
                      onChangeCallback={(selectedStates) => {
                        handleChangeStateClick(selectedStates)
                      }}
                      // onBlurCallback={() => {
                      //   if (columnConfiguration.editableOnBlurCallback) {
                      //     columnConfiguration.editableOnBlurCallback(columnKey, recordKey)
                      //   }
                      // }}
                    />
                  </Col>
                  <Col span={8}>
                    <KrushalOCDropdown
                      //style={{width: '150px'}}
                      ref={districtDropdownRef}
                      type='single-select-l10n_2'
                      label='District'
                      // placeHolderText={columnConfiguration.editablePlaceHolderText}
                      // validations={columnConfiguration.editableValidations}
                      // errorMessage={columnConfiguration.editableErrorMessage}
                      // fieldNames={columnConfiguration.editableDropdownFieldNames}
                      labelKeyInList='geography_name_l10n'
                      valueKeyInList='geography_id'
                      // options={record[columnKey + '_list'] ? record[columnKey + '_list'] : columnConfiguration.editableList}
                      // value={columnValue}
                      onChangeCallback={(selectedDistricts) => {
                        handleChangeDistrictClick(selectedDistricts)
                      }}
                      // onBlurCallback={() => {
                      //   if (columnConfiguration.editableOnBlurCallback) {
                      //     columnConfiguration.editableOnBlurCallback(columnKey, recordKey)
                      //   }
                      // }}
                    />
                  </Col>
                  <Col span={8}>
                    <KrushalOCDropdown
                      // style={{width: '150px'}}
                      ref={talukDropdownRef}
                      type='single-select-l10n_2'
                      label='Taluk'
                      // placeHolderText={columnConfiguration.editablePlaceHolderText}
                      // validations={columnConfiguration.editableValidations}
                      // errorMessage={columnConfiguration.editableErrorMessage}
                      // fieldNames={columnConfiguration.editableDropdownFieldNames}
                      labelKeyInList='geography_name_l10n'
                      valueKeyInList='geography_id'
                      // options={record[columnKey + '_list'] ? record[columnKey + '_list'] : columnConfiguration.editableList}
                      // value={columnValue}
                      onChangeCallback={(selectedTaluks) => {
                        handleChangeTalukClick(selectedTaluks)
                      }}
                      // onBlurCallback={() => {
                      //   if (columnConfiguration.editableOnBlurCallback) {
                      //     columnConfiguration.editableOnBlurCallback(columnKey, recordKey)
                      //   }
                      // }}
                    />
                  </Col>      
                </Row>
                <Row style={{marginTop: '8px', marginBottom: '8px'}} gutter={[16, 16]}>
                  <Col span={8}>
                    <KrushalOCDropdown
                      ref={villageDropdownRef}
                      type='multi-select-l10n_2'
                      label='Village'
                      // placeHolderText={columnConfiguration.editablePlaceHolderText}
                      // validations={columnConfiguration.editableValidations}
                      // errorMessage={columnConfiguration.editableErrorMessage}
                      // fieldNames={columnConfiguration.editableDropdownFieldNames}
                      labelKeyInList='geography_name_l10n'
                      valueKeyInList='geography_id'
                      // options={record[columnKey + '_list'] ? record[columnKey + '_list'] : columnConfiguration.editableList}
                      // value={columnValue}
                      // onChangeCallback={(selectedValues) => {
                      //   if (columnConfiguration.editableOnChangeCallback) {
                      //     columnConfiguration.editableOnChangeCallback(columnKey, recordKey, selectedValues)
                      //   }
                      // }}
                      // onBlurCallback={() => {
                      //   if (columnConfiguration.editableOnBlurCallback) {
                      //     columnConfiguration.editableOnBlurCallback(columnKey, recordKey)
                      //   }
                      // }}
                    />
                  </Col>
                  <Col span={8}>
                    <Button onClick={handleAssignGeographyToStaffClick}>Assign Geography</Button>
                  </Col>
                </Row>
              </div>
              <>
                <Space style={{marginTop:'8px', marginBottom:'8px'}}>
                  <Button size="small" style={{ marginLeft: 8 }} disabled={assignedTerritorySelectedRowKeys.length === 0} onClick={handleDeleteTerritoryAllocationsClick}>Unassign Territory Allocations</Button>
                </Space>
                <OCTable
                  ref={assignedTerritoryTableRef}
                  reportData={assignedTerritoryReportData}
                  setReportData={setAssignedTerritoryReportData}
                  tableConfiguration={assignedTerritoryTableConfiguration}
                  selectedRowKeys={assignedTerritorySelectedRowKeys}
                  setSelectedRowKeys={setAssignedTerritorySelectedRowKeys}
                  // postInitialLoadCallback={postInitialLoadCB}
                  loadReportCallback={loadAssignedTerritoryReportCB}
                  // loadInitialFilterDataCallback={loadInitialFilterDataCB}
                  // parentPageParams={pageParams}
                />

              </>
            </div>
          </Panel>
        )}
        {staffId === '-1' ? null : (
          <Panel header="Map" key="3">
            <div>
              <MapComponent
                assignedTerritoryTableRef={assignedTerritoryTableRef}
                staffId={staffId}
                villagesGeoJSONData={villagesGeoJSONArray}
                taluksGeoJSONData={taluksGeoJSONArray}
                districtsGeoJSONData={districtsGeoJSONArray}
                bcoLocations={bcoLocations}
               />
            </div>
          </Panel>
        )}
      </Collapse>
    </>
    
  )
}

// export default CSBCO
export default withAuthorization(Staff, [CS_TEAM])