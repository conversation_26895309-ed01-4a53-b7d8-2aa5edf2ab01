import { Checkbox, Space, Button, Modal } from "antd";
import { ReloadOutlined } from '@ant-design/icons';

import { useState, useRef } from "react"
import { Link, useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import {useSelector} from "react-redux"
import dayjs from 'dayjs'

import ToastersService from "../../Services/toasters.service";

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  extractChangedValues,
} from "../../Components/Common/KrushalOCForm";

const {EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const { getStaffReport, getStaffReportFilterData, } = require('../../router')

const { OCTable, CommentViewerModalButton, createPageParamsFromURLString, MediaCarouselModalButton } = require('../../Components/Common/AntTableHelper')

const loadReport = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Staff List',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getStaffReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadFilters = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Staff List Filter Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getStaffReportFilterData(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const tableConfiguration = {
  // tableNavigationUrl: '/cs/bco-list',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  // enableSplittingOfServerData: true, // use this only if you have made changes to the backend
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'staff_id',
  columns: [
    {
      title: "Staff Id",
      key: "staff_id",
    },
    {
      title: "Active",
      key: "staff_active",
    },
    {
      title: 'Staff Name',
      key: 'staff_name',
    },
    {
      title: 'Staff Mobile Number',
      key: 'mobile_number'
    },
    {
      title: 'Staff Type',
      key: 'staff_type'
    },
  ],
  columnConfiguration: {
    staff_active: {
      headingText: 'Staff Active Or Not',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      filters: [
        { text: 'Active', value: 'Active', value_int: 1000100001 },
        { text: 'Not Active', value: 'Not Active', value_int: 1000100002 },
      ],
      defaultFilteredValue: ['Active'],
      translateFilteredValuesToAlt: 'value_int',
      altColumn: 'staff_active_id'
    },
    staff_name: {
      headingText: 'Staff Name',
      enableFilter: true,
      type: 'string',
    },
    mobile_number: {
      headingText: 'Staff Mobile Number',
      enableFilter: true,
      type: 'string',
    },
    staff_type: {
      headingText: 'Staff Type',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      translateFilteredValuesToAlt: 'value_int',
      altColumn: 'staff_type_id',
      updateFilterDataDuringFilterCall: true,
    },
  }
}

const currentPageName = '/staff-list'

const StaffList = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { pathname, search } = location
  tableConfiguration.tableNavigationUrl = pathname
  const baseLink = pathname.substring(0, pathname.length - currentPageName.length)
  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    tableConfiguration.enableTableExport = true
    tableConfiguration.enableTableExportFromCloud = true
  }

  const [reportData, setReportData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const tableRef = useRef()

  const postInitialLoadCB = () => {
  }

  const loadInitialFilterDataCB = (additionalParams) => {
    const reportQueryParams = {searchConfiguration: tableConfiguration}
    return loadFilters(userToken, reportQueryParams, additionalParams)
  }

  const loadReportCB = (reportQueryParams, additionalParams) => {
    return loadReport(userToken, reportQueryParams, additionalParams)
  }

  const enableButton = (selectedRowKeys, reportData, buttonType) => {
    let returnValue = true
    if (selectedRowKeys.length === 1) {
      const selectedRow = reportData.filter((object) => {
        return (object['staff_id'] === selectedRowKeys[0])
      })
    }
    return returnValue
  }

  const handleAddStaffClick = (event) => {
    const staffId = -1
    // navigate({
    //   pathname: `../oc/farmer-details`,
    //   search: `?qP=${encodedParamsAsString}`
    // })
    const navigationLink = `${baseLink}/staff/${staffId}`
    if (event.ctrlKey || event.metaKey) {
      window.open(navigationLink, '_blank')
    } else {
      navigate({
        pathname: navigationLink,
      })
    }
  }

  const handleViewStaffClick = (event) => {
    console.log('hVMC hVPC 1')
    const selectedRow = reportData.filter((object) => {
      return (object['staff_id'] === selectedRowKeys[0])
    })
    const { pathname, search } = location

    const staffId = selectedRow[0]['staff_id']
    // navigate({
    //   pathname: `../oc/farmer-details`,
    //   search: `?qP=${encodedParamsAsString}`
    // })
    const navigationLink = `${baseLink}/staff/${staffId}`
    if (event.ctrlKey || event.metaKey) {
      window.open(navigationLink, '_blank')
    } else {
      navigate({
        pathname: navigationLink,
      })
    }
  }

  return (
    <>
      <Space style={{marginTop:'8px', marginBottom:'8px'}}>
        <Link to={`${baseLink}`}>Back to Queue List</Link>
        <Button size="small" style={{ marginLeft: 8 }} disabled={selectedRowKeys.length !== 0} onClick={handleAddStaffClick}>Add Staff</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={selectedRowKeys.length !== 1} onClick={handleViewStaffClick}>View Staff</Button>
      </Space>
      <OCTable
        /* tableKey={tableKey}
        setTableKey={setTableKey} */
        ref={tableRef}
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={tableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        // postInitialLoadCallback={postInitialLoadCB}
        loadReportCallback={loadReportCB}
        loadInitialFilterDataCallback={loadInitialFilterDataCB}
        parentPageParams={pageParams}
      />
    </>
  );
}

// export default BCOList
export default withAuthorization(StaffList, [CS_TEAM])