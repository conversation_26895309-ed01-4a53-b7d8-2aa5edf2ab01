const { /* get,  */put, /* post,  */configurationJSON, KrushalError } = require('@krushal-it/common-core')
const { post, get } = require('../routerhelper')

const getCustomerServiceTasksReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gPSAWLR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/customer-service-tasks`
    console.log('r gPSAWLR  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gPSAWLR 10, error')
    console.log('r gPSAWLR 10a, error = ', error)
    throw error
  }
}

const getCustomerServiceTasksReportFilterData = async (headers) => {
  try {
    console.log('r gFWLRRFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/customer-service-tasks`
    console.log('r gFWLRRFD  2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRRFD 10, error')
    console.log('r gFWLRRFD 10a, error = ', error)
    throw error
  }
}

const snoozeCustomerServiceAnimals = async (headers, taskWithSnoozeInformation) => {
  try {
    console.log('r sFSA 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/snooze-animal-for-customer-service-task`
    console.log('r sFSA  2, url = ', url)
    const response = await post(url, headers, taskWithSnoozeInformation)
    return response
  } catch (error) {
    console.log('r sFSA 10, error')
    console.log('r sFSA 10a, error = ', error)
    throw error
  }
}

const unsnoozeSellableAnimals = async (headers, toBeSnoozeAnimalInformation) => {
  try {
    console.log('r sFSA 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/unsnooze-animal-for-data-collection`
    console.log('r sFSA  2, url = ', url)
    const response = await post(url, headers, toBeSnoozeAnimalInformation)
    return response
  } catch (error) {
    console.log('r sFSA 10, error')
    console.log('r sFSA 10a, error = ', error)
    throw error
  }
}

const updateStatusOfHealthScoreAnimals = async (headers, animalUpdateStatusData) => {
  try {
    console.log('r uSAS 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/update-animal-selling-status`
    console.log('r uSAS  2, url = ', url)
    const response = await post(url, headers, animalUpdateStatusData)
    return response
  } catch (error) {
    console.log('r uSAS 10, error')
    console.log('r uSAS 10a, error = ', error)
    throw error
  }
}

const updateCustomerServiceTaskStatus = async (headers, taskAndStatusData) => {
  try {
    console.log('r uSAS 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/update-task-status`
    console.log('r uSAS  2, url = ', url)
    const response = await post(url, headers, taskAndStatusData)
    return response
  } catch (error) {
    console.log('r uSAS 10, error')
    console.log('r uSAS 10a, error = ', error)
    throw error
  }
}

export {
  getCustomerServiceTasksReport, getCustomerServiceTasksReportFilterData, snoozeCustomerServiceAnimals,
  updateCustomerServiceTaskStatus,
}