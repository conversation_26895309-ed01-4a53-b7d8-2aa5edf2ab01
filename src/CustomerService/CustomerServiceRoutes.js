import {
  Routes,
  Route,
} from "react-router-dom";

import Sidebar from '../Components/Sidebar/Sidebar'

import CustomerService from './Components/CustomerService';
import CustomerServiceTasks from './Components/CustomerServiceTasks'
import BCOList from './Components/BCOList'
import CSBCO from './Components/CSBCO'
import GovindDairyDemo from "./Components/GovindDairyDemo";
// import AnimalListingForVeterinarians from './Components/AnimalListingForVeterinarians'

const CustomerServiceRoutes = () => {
  return (
    <Routes>
      <Route path="cs" element={<Sidebar content={<CustomerService />} />} />
      <Route path="gd" element={<Sidebar content={<GovindDairyDemo />} />} />
      <Route path="cs/current-task-list" element={<Sidebar content={<CustomerServiceTasks />} />} />
      <Route path="cs/bco-list" element={<Sidebar content={<BCOList />} />} />
      <Route path="cs/bco/:partnerId" element={<Sidebar content={<CSBCO />} />} />
      {/* <Route path="cs/whatsapp-messages" element={<AnimalListingForVeterinarians />} /> */}
    </Routes>
  )
}

export default CustomerServiceRoutes