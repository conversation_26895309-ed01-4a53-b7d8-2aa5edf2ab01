import {
  Routes,
  Route,
} from "react-router-dom";

import Sidebar from '../Components/Sidebar/Sidebar'

import GovindDairyDemo from "./Components/GovindDairyDemo";
import BCOList from './Components/BCOList'
import CSBCO from './Components/CSBCO'
import StaffList from './Components/StaffList'
import Staff from "./Components/Staff";
// import AnimalListingForVeterinarians from './Components/AnimalListingForVeterinarians'

const GovindDairyDemoRoutes = () => {
  return (
    <Routes>
      <Route path="gdd" element={<Sidebar content={<GovindDairyDemo />} />} />
      <Route path="gdd/bco-list" element={<Sidebar content={<BCOList />} />} />
      <Route path="gdd/bco/:partnerId" element={<Sidebar content={<CSBCO />} />} />
      <Route path="gdd/staff-list" element={<Sidebar content={<StaffList />} />} />
      <Route path="gdd/staff/:staffId" element={<Sidebar content={<Staff />} />} />
      {/* <Route path="cs/whatsapp-messages" element={<AnimalListingForVeterinarians />} /> */}
    </Routes>
  )
}

export default GovindDairyDemoRoutes