.App {
  letter-spacing: 0.2px;
  background-color: #f5fbfd;
  font-family: "Open Sans";
}

small {
  font-weight: 600;
  color: #4c4c4c;
}

.input-field {
  border: 1px solid #bdbdbd;
  border-radius: 5px;
  padding: 10px 15px;
  cursor: pointer;
  background-color: #ffffff;
  margin-bottom: 0.5rem;
}

.input-field:focus {
  outline: none;
  border: 1px solid #673ab7;
  background-color: #f7f4fb;
}

/* Remove arrows from input type number */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}

.active {
  background: #333;
  color: #e2e2e2;
}

.not-clickable {
  text-decoration: line-through;
  color: #b9b9b9;
}

.info-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

small {
  display: inline-block;
}

small + small {
  border-left: 1px solid #2094f3;
  margin-left: 10px;
  padding-left: 10px;
}

small a {
  text-decoration: none;
  color: #2094f3;
}

small a:hover {
  text-decoration: underline;
}

.leaflet-container {
  margin: 0px auto;
  width: 100%;
   /* height: 100%;  */
   height: calc(100vh - 100px);
  z-index: 0;
  border: 5px solid #333;
  overflow: hidden;
 
}

iframe {
  display: block;
  width: 95%;
  border: 0;
}

.grid {
  display: grid;
  width: 100%;
  grid-template-areas:
    "nav head"
    "nav main";
  grid-template-columns: 300px 1fr;
  grid-template-rows: 100px;
  gap: 0 30px;
  max-width: 1350px;
}

header {
  grid-area: head;
}

nav {
  grid-area: nav;
}

nav {
  /* width: 100%;
  height: 100vh;
  background: #fff;
  min-height: 100%;
  padding-bottom: 30px;
  border-right: 2px solid #333;
  box-shadow: 0 0 10px rgba(51, 51, 51, 0.2); */
  display: block;
}

main {
  grid-area: main;
  max-width: 1000px;
  width: 100%;
  padding-right: 30px;
}

