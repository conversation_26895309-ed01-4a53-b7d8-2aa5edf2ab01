import dayjs from "dayjs";

import ToastersService from "../Services/toasters.service";

const lodashObject = require("lodash")

const customParseFormat = require('dayjs/plugin/customParseFormat')
const localeData = require('dayjs/plugin/localeData')
const localizedFormat = require('dayjs/plugin/localizedFormat')
const englishLocale = require('dayjs/locale/en')
const marathiLocale = require('dayjs/locale/mr')
const gujaratiLocale = require('dayjs/locale/gu')

dayjs.extend(customParseFormat)
dayjs.extend(localeData)
dayjs.extend(localizedFormat)

const { extractBasedOnLanguage } = require('@krushal-it/common-core')

const { getFarmerBasicData, getCustomerInformationBasedOnClassifiers, getAnimalsForFarmer,
  getCustomerAndAnimalDetailsForSmartRation,
  getAnimalFeedDetailsForCustomerAnimals,
  generateSmartRationReportsAndUploadToS3, createDocument, upsertDocumentByLanguage, getSmartFeedData,
  getSmartRationReport, saveSmartFeedData, getPerCustomerLatestSmartRationReport, } = require('../router')

const availableLanguageLocales = ['en', 'mr', 'gu']

const loadBasicFarmerData = async (userToken, customerId) => {
  try {
    const headers = {
      useCase: 'Get Basic Farmer Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getFarmerBasicData(headers, customerId)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadAnimalReport = async (userToken, queryParams, additionalParams, customerId) => {    
  try {
    const headers = {
      useCase: 'Get Customer Animals',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getAnimalsForFarmer(headers, queryParams, customerId)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadSmartRationCustomerReport = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Smart Ration Customers',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // await delayInMillis(10000)
    // const response = await getFarmersWithLocationRecordedReport(headers, queryParams)
    const response = await getPerCustomerLatestSmartRationReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const generateSmartRationReports = async (userToken, reportData) => {
  try {
    console.log('OCFD gFD 1')
    const headers = {
      useCase: 'Generate Smart Ration Report',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await generateSmartRationReportsAndUploadToS3(headers, reportData)
    return response.data
  } catch (error) {
    console.log('OCFD gFD 10, error')
    console.log('OCFD gFD 10a, error = ', error)
    throw error
  }
}

const insertDocumentIntoDB = async (userToken, documentData) => {
  try {
    console.log('OCFD gFD 1')
    const headers = {
      useCase: 'Insert Document Into DB',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await createDocument(headers, documentData)
    return response.data
  } catch (error) {
    console.log('OCFD gFD 10, error')
    console.log('OCFD gFD 10a, error = ', error)
    throw error
  }
}

const upsertDocumentArrayIntoDB = async (userToken, documentData) => {
  try {
    console.log('OCFD gFD 1')
    const headers = {
      useCase: 'Insert Documents Into DB',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await upsertDocumentByLanguage(headers, documentData)
    return response.data
  } catch (error) {
    console.log('OCFD gFD 10, error')
    console.log('OCFD gFD 10a, error = ', error)
    throw error
  }
}

const loadSmartRationReport = async (userToken, queryParams, additionalParams) => {    
  try {
    const headers = {
      useCase: 'Get Smart Ration Report',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getSmartRationReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getCustomerAndAnimalDetails = async (userToken, customerId) => {
  try {
    console.log('OCFD gFD 1')
    const headers = {
      useCase: 'Get Customer and Animal Details for Generating Feed Recommendation',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getCustomerAndAnimalDetailsForSmartRation(headers, customerId)
    return response.data
  } catch (error) {
    console.log('OCFD gFD 10, error')
    console.log('OCFD gFD 10a, error = ', error)
    throw error
  }
}

/* const getCustomerAndAnimalDetails2 = async (userToken, queryParams) => {
  try {
    console.log('OCFD gFD 1')
    const headers = {
      useCase: 'Get Customer and Animal Details for Generating Feed Recommendation',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getAnimalsForFarmer(headers, queryParams, customerId)
    return response.data

  } catch (error) {
    console.log('OCFD gFD 10, error')
    console.log('OCFD gFD 10a, error = ', error)
    throw error
  }
} */

const getAnimalFeedDetails = async (userToken, customerInformationAndAnimalInformation) => {
  try {
    console.log('OCFD gFD 1')
    const headers = {
      useCase: 'Generate Feed Recommendation For Customer And Animal Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    
    const response = await getAnimalFeedDetailsForCustomerAnimals(headers, customerInformationAndAnimalInformation)
    return response.data
  } catch (error) {
    console.log('OCFD gFD 10, error')
    console.log('OCFD gFD 10a, error = ', error)
    throw error
  }
}

const loadSmartRationReportData = async (userToken, isFarmerLevel, farmerLevelReportAssetId) => {
  try {
    console.log('OCFD gFD 1')
    const headers = {
      useCase: 'Get Smart Ration Report Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    let queryString
    if (isFarmerLevel === true) {
      queryString = `asset_id=${farmerLevelReportAssetId}`
    } else {
      queryString = `entity_1_type_id=1000460012&entity_1_entity_uuid=${farmerLevelReportAssetId}&entity_relationship_type_id=1000210032`
    }
    const response = await getSmartFeedData(headers, queryString)
    return response.data
  } catch (error) {
    console.log('OCFD gFD 10, error')
    console.log('OCFD gFD 10a, error = ', error)
    throw error
  }
}

const feedTypeToFeedDisplayType = {green: {'en': 'Green', 'mr': 'हिरवा', 'gu': 'લીલા', 'ta': 'பச்சை', 'te': 'ఆకుపచ్చ'}, dry: {'en': 'Dry', 'mr': 'सुका', 'gu': 'સૂકો', 'ta': 'உலர்', 'te': 'పొడి'}, readymade: {'en': 'Concentrate', 'mr': 'कॉन्सन्ट्रेट', 'gu': 'કોન્સન્ટ્રેટ', 'ta': 'கவனம் செலுத்துங்கள்', 'te': 'ఫీడ్ ఏకాగ్రత'}, other: {'en': 'Others', 'mr': 'इतर', 'gu': 'અન્ય', 'ta': 'மற்றவைகள்', 'te': 'ఇతరులు'}}

const getTypeDisplayFormat = (type, language = 'en') => {
  if (feedTypeToFeedDisplayType[type] !== undefined) {
    return extractBasedOnLanguage(feedTypeToFeedDisplayType[type], language)
  } else {
    return type
  }
}

const formatFloatValue = (object, precision) => {
  let finalValue = ''
  if (object === null || object === undefined || object === '') {
    finalValue = ''
  } else {
    finalValue = parseFloat(object)
    if (isNaN(finalValue)) {
      finalValue = ''
    } else {
      finalValue = finalValue.toFixed(precision)
    }
  }
  return finalValue
}

const getQuantityBasedOnDM = (dm, feedProperties) => {
  if (dm === undefined || dm === null || feedProperties === undefined || feedProperties === null) {
    return 0
  }
  let feedComponentPerKg = feedProperties['dm'] ? feedProperties['dm'] : 0
  if (feedComponentPerKg !== 0) {
    feedComponentPerKg = parseFloat(feedComponentPerKg)
    if (isNaN(feedComponentPerKg)) {
      feedComponentPerKg = 0
    }
  }
  const feedWeight = feedComponentPerKg ? dm * 100 / feedComponentPerKg : 0
  return feedWeight
}

const getFeedComponentQuantity = (feedWeightInKg, feedProperties, feedComponent, isOtherNutrient, isPricePerKg) => {
  if (feedWeightInKg === undefined || feedWeightInKg === null || feedProperties === undefined || feedProperties === null) {
    return 0
  }
  let feedComponentPerKg = feedWeightInKg && feedProperties && feedProperties[feedComponent] ? feedProperties[feedComponent] : undefined
  if (feedComponentPerKg !== undefined) {
    feedComponentPerKg = parseFloat(feedComponentPerKg)
    if (isNaN(feedComponentPerKg)) {
      feedComponentPerKg = undefined
    }
  }
  let feedDM = 0
  if (isPricePerKg === true) {
    feedDM = feedComponentPerKg ? feedWeightInKg * feedComponentPerKg : 0
  } else {
    feedDM = feedComponentPerKg ? feedWeightInKg * feedComponentPerKg / 100 : 0
  }

  return feedDM
}

const getSmartRationDataCreateReportUploadToS3AndCreateDocument = async (userToken, customerId, reportLanguage, reportGenerationStructureArray) => {
  const referenceMap = global.references.referenceCategoryToReferencesMap
  const foliages = referenceMap[10001300]
  const concentrates = referenceMap[10013000]
  const otherNutrients = referenceMap[10013005]
  const otherNutrientReferenceIdArray = otherNutrients.map(object => object['reference_id'])
  const feedReferences = [...foliages, ...concentrates, ...otherNutrients].filter(object => {
    let returnValue = false
    if (object['reference_information'] && object['reference_information']['feed_properties'] && object['reference_information']['feed_properties']['used'] === 'true') {
      returnValue = true
    }
    return returnValue
  })



  // reportAssetId, typeOfSmartRationReport, documentTypeId
  const reportIdToReportConfigurationMap = {}
  const reportIdArray = reportGenerationStructureArray.map(reportGenerationStructure => {
    const reportId = reportGenerationStructure['asset_id']
    reportIdToReportConfigurationMap[reportId] = reportGenerationStructure
    return reportId
  })
  const farmerBasicDataResponse = await loadBasicFarmerData(userToken, customerId)
  if (farmerBasicDataResponse.return_code !== 0) {
    ToastersService.failureToast('Could not obtain farmer data')
    return farmerBasicDataResponse
  }

  const customerData = {customer_name_as_l10n: farmerBasicDataResponse.result.customer_name, mobile_number: farmerBasicDataResponse.result.mobile_number}

  const reportIdToReportDataMap = {}
  let animalIds = []
  for (const reportId of reportIdArray) {
    const reportData = {}
    const farmerLevelSmartRationReportData = await loadSmartRationReportData(userToken, true, reportId)
    if (farmerLevelSmartRationReportData.return_code !== 0) {
      ToastersService.failureToast('Could not obtain farmer level report data')
      return farmerLevelSmartRationReportData
    }

    reportData['farmer_level_report_data'] = farmerLevelSmartRationReportData.data[0]
    const animalLevelSmartRationReportData = await loadSmartRationReportData(userToken, false, reportId)
    if (animalLevelSmartRationReportData.return_code !== 0) {
      ToastersService.failureToast('Could not obtain animal level report data')
      return animalLevelSmartRationReportData
    }
    reportData['animal_level_report_data'] = animalLevelSmartRationReportData.data
    reportIdToReportDataMap[reportId] = reportData
    const animalIdsForReport = animalLevelSmartRationReportData.data.map(object => object['animal_id'])
    animalIds = [...animalIds, ...animalIdsForReport]
  }

  const getAnimalDataReportResponse = await collateAnimalInformationForSmartRation(userToken, customerId, animalIds)
  if (getAnimalDataReportResponse.return_code !== 0) {
    ToastersService.failureToast('Could not obtain animal data')
    return getAnimalDataReportResponse
  }
  const animalIdToAnimalInformationNeededForSmartRationGenerationMap = getAnimalDataReportResponse.animalIdToAnimalInformationNeededForSmartRationGenerationMap

  const baseAnimalData = {}
  let counter = 0
  for (const animalId of animalIds) {
    counter++
    const animalData = animalIdToAnimalInformationNeededForSmartRationGenerationMap[animalId]

    const animalObject = {
      sn: counter.toString(),
      tag:  animalData['ear_tag_1'] ? animalData['ear_tag_1'] : 'N/A',
      weight: animalData['animal_weight_1']  ? parseFloat(animalData['animal_weight_1']).toFixed(0) : 'N/A',
      bs: animalData['animal_body_score_1'] ? parseFloat(animalData['animal_body_score_1']).toFixed(1) : 'N/A',
      mypd: animalData['animal_average_lpd_1'] ? parseFloat(animalData['animal_average_lpd_1']).toFixed(1) : 'N/A',
      mf: animalData['animal_milk_fat_1'] ? parseFloat(animalData['animal_milk_fat_1']).toFixed(1) : 'N/A',
      pm: animalData['number_of_months_pregnant_1']?animalData['number_of_months_pregnant_1']:"N/A",
      dim: animalData['days_since_last_calving'] ? animalData['days_since_last_calving'] : 'N/A',
      pmypd:  animalData['animal_average_lpd_1'] ? (parseFloat(animalData['animal_average_lpd_1'])*1.15).toFixed(1) : 'N/A',

    }

    baseAnimalData[animalData['animal_id']] = animalObject
  }

  for (const reportId of reportIdArray) {
    const reportConfiguration = reportIdToReportConfigurationMap[reportId]
    const typeOfSmartRationReportForFileName = reportConfiguration['report_type_in_file_name']
    const typeOfSmartRationReport = reportTypeAsJSON[typeOfSmartRationReportForFileName]
    const reportData = reportIdToReportDataMap[reportId]
    reportData['mfa'] = []
    const farmerLevelReportData = reportData['farmer_level_report_data']
    console.log('farmerLevelReportData = ', farmerLevelReportData)
    reportData['report_type_as_l10n'] = typeOfSmartRationReport
    reportData['report_type_for_file_name'] = typeOfSmartRationReportForFileName
    const allFarmerFeeds = [...farmerLevelReportData['smart_ration_foliages'], ...farmerLevelReportData['smart_ration_concentrates'], ...farmerLevelReportData['smart_ration_other_nutrients']]
    let farmerLevelMonthlyCost = 0
    for (const monthlyFeedData of allFarmerFeeds) {
      const feedReferenceId = monthlyFeedData['reference_id']
      const selectedFeedReference = feedReferences.filter(object => object['reference_id'] === feedReferenceId)
      const feedReferenceInformation = selectedFeedReference[0]['reference_information']
      const feedProperties = feedReferenceInformation && feedReferenceInformation['feed_properties'] !== undefined ? feedReferenceInformation['feed_properties'] : undefined
      const feedType = feedProperties && feedProperties['type'] ? feedProperties['type'] : ''
      const feedName = selectedFeedReference[0]['reference_name_l10n']
      const weight = monthlyFeedData['smart_ration_weight'] !== undefined && monthlyFeedData['smart_ration_weight'] !== null && !isNaN(parseFloat(monthlyFeedData['smart_ration_weight'])) ? parseFloat(monthlyFeedData['smart_ration_weight']).toFixed(0) : ''
      let monthlyCost = ''
      if (monthlyFeedData['smart_ration_cost'] !== undefined && monthlyFeedData['smart_ration_cost'] !== null && !isNaN(parseFloat(monthlyFeedData['smart_ration_cost']))) {
        monthlyCost = parseFloat(monthlyFeedData['smart_ration_cost']).toFixed(0)
        farmerLevelMonthlyCost = farmerLevelMonthlyCost + monthlyFeedData['smart_ration_cost']
      }
      const feedRowInMFA = {feedAsL10N: feedName, typeCode: feedType, quantity: weight, cost: monthlyCost}
      reportData['mfa'].push(feedRowInMFA)
    }
    reportData.mntly_cost = farmerLevelMonthlyCost.toFixed(0)
    if (farmerLevelReportData['feed_generation_date']) {
      // reportData.report_date = dayjs(farmerLevelReportData['feed_generation_date']).format('D-MMM-YYYY')
      reportData['report_date_without_locale'] = dayjs(farmerLevelReportData['feed_generation_date'])
    }

    const animalIdToReportDataMap = {}
    reportData['animal_id_to_report_data_map'] = animalIdToReportDataMap

    const animalLevelReportData = reportData['animal_level_report_data']
    for (const animalFeedData of animalLevelReportData) {
      const animalId = animalFeedData['animal_id']
      const allAnimalFeeds = [...animalFeedData['smart_ration_foliages'], ...animalFeedData['smart_ration_concentrates'], ...animalFeedData['smart_ration_other_nutrients']]
      const animalDailyFeedRation = []
      let dailyCostAtAnimalLevel = 0
      const animalObject = {animal_id: animalId}
      for (const dailyFeedData of allAnimalFeeds) {
        const feedReferenceId = dailyFeedData['reference_id']
        const selectedFeedReference = feedReferences.filter(object => object['reference_id'] === feedReferenceId)
        const feedReferenceInformation = selectedFeedReference[0]['reference_information']
        const feedProperties = feedReferenceInformation && feedReferenceInformation['feed_properties'] !== undefined ? feedReferenceInformation['feed_properties'] : undefined
        const isOtherNutrient = (feedReferenceId !== undefined && otherNutrientReferenceIdArray.includes(feedReferenceId)) ? true : false
        const feedType = feedProperties && feedProperties['type'] ? feedProperties['type'] : ''
        const feedName = selectedFeedReference[0]['reference_name_l10n']
        let weight = dailyFeedData['smart_ration_weight'] !== undefined && dailyFeedData['smart_ration_weight'] !== null && !isNaN(parseFloat(dailyFeedData['smart_ration_weight'])) ? parseFloat(dailyFeedData['smart_ration_weight']) : 0
        weight = weight === 0 ? '' : isOtherNutrient ? (weight*1000).toFixed(0) : weight.toFixed(1)
        const unit = isOtherNutrient ? 'gm' : 'kg'
        // const dailyCost = dailyFeedData['smart_ration_cost'] !== undefined && dailyFeedData['smart_ration_cost'] !== null && !isNaN(parseFloat(dailyFeedData['smart_ration_cost'])) ? parseFloat(dailyFeedData['smart_ration_cost']).toFixed(0) : ''
        let dailyCostAtFeedLevel = ''
        if (dailyFeedData['smart_ration_cost'] !== undefined && dailyFeedData['smart_ration_cost'] !== null && !isNaN(parseFloat(dailyFeedData['smart_ration_cost']))) {
          dailyCostAtFeedLevel = parseFloat(dailyFeedData['smart_ration_cost']).toFixed(0)
          dailyCostAtAnimalLevel = dailyCostAtAnimalLevel + dailyFeedData['smart_ration_cost']
        }
        
        const feedRowInDFR = {feedAsL10N: feedName, typeCode: feedType, quantity: weight, u: unit, cost: dailyCostAtFeedLevel}
        animalDailyFeedRation.push(feedRowInDFR)
      }
      if (animalDailyFeedRation.length > 0) {
        animalObject.doesNotHaveDfr = false
        animalObject.hasDfr = true
        animalObject.dfr = animalDailyFeedRation
        animalObject.daily_cost = dailyCostAtAnimalLevel.toFixed(0)
      } else {
        animalObject.dfr = []
        animalObject.doesNotHaveDfr = true
        animalObject.hasDfr = false
      }
      animalIdToReportDataMap[animalId] = animalObject
    }
  }

  console.log('reportIdToReportDataMap = ', reportIdToReportDataMap)

  const reportDataWithLanguageArray = []
  // {unique_key_for_document: reportId, language: , asset_id: report_id, file_name, document_type_id, dataForReport}
  for (const individualLanguage of reportLanguage) {
    const customerDataForLanguage = lodashObject.cloneDeep(customerData)
    customerDataForLanguage['customer_name'] = extractBasedOnLanguage(customerDataForLanguage['customer_name_as_l10n'], individualLanguage)
    delete customerDataForLanguage['customer_name_as_l10n']

    for (const reportId of reportIdArray) {
      const reportData = lodashObject.cloneDeep(reportIdToReportDataMap[reportId])
      const reportConfiguration = reportIdToReportConfigurationMap[reportId]
      const reportMFA = reportData['mfa']
      const animalIdToReportDataMap = reportData['animal_id_to_report_data_map']
      const reportType = extractBasedOnLanguage(reportData['report_type_as_l10n'], individualLanguage)
      if (reportData['report_date_without_locale']) {
        if (availableLanguageLocales.includes(individualLanguage)) {
          dayjs.locale(individualLanguage)
        } else {
          dayjs.locale('en')
        }
        // reportData['report_date'] = dayjs(reportData['report_date_without_locale']).format('D-MMM-YYYY')
        reportData['report_date'] = reportData['report_date_without_locale'].format('D-MMM-YYYY')
        delete reportData['report_date_without_locale']
        dayjs.locale('en')
      }
      for (const monthlyIndividualFeed of reportMFA) {
        monthlyIndividualFeed['feed'] = extractBasedOnLanguage(monthlyIndividualFeed['feedAsL10N'], individualLanguage)
        monthlyIndividualFeed['type'] = getTypeDisplayFormat(monthlyIndividualFeed['typeCode'], individualLanguage)
        delete monthlyIndividualFeed['feedAsL10N']
        delete monthlyIndividualFeed['typeCode']
      }

      const reportAnimals = []

      for (const reportAnimalId of Object.keys(animalIdToReportDataMap)) {
        const animalIdReport = {...baseAnimalData[reportAnimalId], ...animalIdToReportDataMap[reportAnimalId]}
        for (const dailyAnimalIndividualFeed of animalIdReport.dfr) {
          dailyAnimalIndividualFeed['feed'] = extractBasedOnLanguage(dailyAnimalIndividualFeed['feedAsL10N'], individualLanguage)
          dailyAnimalIndividualFeed['type'] = getTypeDisplayFormat(dailyAnimalIndividualFeed['typeCode'], individualLanguage)
          delete dailyAnimalIndividualFeed['feedAsL10N']
          delete dailyAnimalIndividualFeed['typeCode']
          }
        reportAnimals.push(animalIdReport)
      }

      const totalProjectedLPD = reportAnimals.filter(value=>!isNaN(value.pmypd)).reduce((p, c) => p + parseFloat(c.pmypd) , 0)
      const totalCurrentLPD = reportAnimals.filter(value => !isNaN(value.pmypd)).reduce((p, c) => p + parseFloat(c.mypd), 0)
      console.log("totalProjectedLPD",totalProjectedLPD, totalCurrentLPD)
      const costOfRationPerLitreOfProjectedMilk = totalProjectedLPD>0?(reportData.mntly_cost ? (reportData.mntly_cost/totalProjectedLPD/30).toFixed(2):0):0

      const reportDataForLanguage = {
        unique_key_for_file: reportId + '-' + individualLanguage,
        entity_1_entity_uuid: reportId,
        report_template_key: 'FARMER_LEVEL_FEED_RECOMMENDATION',
        document_type_id: reportConfiguration['document_type_id'],
        language: individualLanguage,
        file_name: 'Smart Ration - ' + reportData['report_type_for_file_name'] + ' - ' + customerData['mobile_number'].slice(-10) + ' - ' + individualLanguage + '.pdf',
        file_location_on_s3: 'smartration',
        report_data: {
          ...customerDataForLanguage,
          mntly_cost: reportData.mntly_cost,
          report_type: reportType,
          report_date: reportData['report_date'],
          mfa: reportMFA,
          animals: reportAnimals,
          totalProjectedLPD: totalProjectedLPD.toFixed(2),
          totalCurrentLPD: totalCurrentLPD.toFixed(2),
          costOfRationPerLitreOfProjectedMilk
        },
      }
      reportDataWithLanguageArray.push(reportDataForLanguage)
    }
  }


  console.log('reportDataWithLanguageArray = ', reportDataWithLanguageArray)
  const generateSmartRationReportsResponse = await generateSmartRationReports(userToken, reportDataWithLanguageArray)
  console.log('generateSmartRationReportsResponse = ', generateSmartRationReportsResponse)
  // return
  if (generateSmartRationReportsResponse.return_code !== 0) {
    ToastersService.failureToast('Could not generate report file')
    return generateSmartRationReportsResponse
  }

  const uniqueKeyForFileToS3KeyMap = generateSmartRationReportsResponse['unique_key_for_file_to_s3_key_map']

  const documentObjectArray = []
  for (const reportDataWithLanguage of reportDataWithLanguageArray) {
    const uniqueKeyForFile = reportDataWithLanguage['unique_key_for_file']
    const s3FileKey = uniqueKeyForFileToS3KeyMap[uniqueKeyForFile]
    const documentObject = {
      document_type_id: reportDataWithLanguage['document_type_id'], // 1000260024,
      entity_1_type_id: 1000460012,
      entity_1_entity_uuid: reportDataWithLanguage['entity_1_entity_uuid'],
      language: reportDataWithLanguage['language'],
      document_information: {
        url: s3FileKey,
        fileName: reportDataWithLanguage['file_name'],
        fileType: 'application/pdf',
        entityType: 'asset',
        description: '',
        language: reportDataWithLanguage['language'],
        localFolderName: 'smartration',
        serverFolderName: 'smartration',
        individualLocalFolderName: 'smartration',
        individualServerFolderName: 'smartration',
      }
    }
    documentObjectArray.push(documentObject)
  }

  const upsertDocumentArrayIntoDBResponse = await upsertDocumentArrayIntoDB(userToken, documentObjectArray)
  if (upsertDocumentArrayIntoDBResponse.return_code !== 0) {
    ToastersService.failureToast('Could not create document')
    return upsertDocumentArrayIntoDBResponse
  }
  return upsertDocumentArrayIntoDBResponse
}

const createSmartRationReport = async (userToken, customerId, restrictToFarmerFoliage, customerData, animalData) => {
  const dataToSend = {
    allAnimalsData: {
      data: {
        customer: {
          customer_id: customerId,
          ...customerData, //...feedRelatedInformationOfCustomerResponse.data
        },
        animals: animalData, // animalsObject
      }
    }, // customerInfomrationAndAnimalInformation,
    saveFeeds: 1,
    useOnlyFarmerFeeds: restrictToFarmerFoliage === true ? 1 : 0
  }
  const getAnimalFeedDetailsResponse = await getAnimalFeedDetails(userToken, dataToSend)
  return getAnimalFeedDetailsResponse
}

const reportTypeAsJSON = {
  'Type 1': {en: 'Vet Recommended', 'mr': 'पशुवैद्य शिफारस', 'gu': 'પશુવૈદ ભલામણ કરેલ', 'ta': 'கால்நடை மருத்துவர் பரிந்துரைக்கப்படுகிறது', 'te': 'వెట్ సిఫార్సు చేయబడింది'},
  'Type 2': {en: 'Farmer Foliage Based', 'mr': 'शेतकरी पर्णसंभार आधारित', 'gu': 'ખેડૂત પર્ણસમૂહ આધારિત', 'ta': 'உழவர் தழை அடிப்படையிலானது', 'te': 'రైతు ఆకుల ఆధారంగా'},
  'Type 3': {en: 'Vet Recommended', 'mr': 'पशुवैद्य शिफारस', 'gu': 'પશુવૈદ ભલામણ કરેલ', 'ta': 'கால்நடை மருத்துவர் பரிந்துரைக்கப்படுகிறது', 'te': 'వెట్ సిఫార్సు చేయబడింది'},
}

const collateAnimalInformationForSmartRation = async (userToken, customerId, selectedAnimalIds) => {
  const queryParams = {
    searchConfiguration: {
      customer_id: {
        allowFilterInPageParams: true,
        paramSearchType: 'array',
        paramSearchDataType: 'string'
      },
      animal_id: {
        enableFilter: true,
        type: 'default_array',
        dataType: 'uuid',
      }
    },
    selectedColumns: [
      "animal_id",
      "animal_active",
      "animal_active_id",
      "animal_weight_1",
      "animal_average_lpd_1",
      "animal_milk_fat_1",
      "ear_tag_1",
      "months_since_last_calving_1",
      "month_since_calving_as_of_data_1",
      "number_of_months_pregnant_1",
      "number_of_months_pregnant_as_of_date_1",
    ],
    filters: {
    },
    sorting: {
      columnKey: 'customer_id',
      order: 'descend',
    },
    excludeBDM: true,
    forceLoadAllColumns: true,
  }
  /* if (selectedAnimalRowKeys.length > 0) {
    queryParams.filters['animal_id'] = selectedAnimalRowKeys
  } */
  queryParams.filters['animal_id'] = selectedAnimalIds

  const getAnimalDataReportResponse = await loadAnimalReport(userToken, queryParams, undefined, customerId)
  console.log('OCFD hGSRC 1, getAnimalDataReportResponse = ', getAnimalDataReportResponse)
  if (getAnimalDataReportResponse.return_code !== 0) {
    return getAnimalDataReportResponse
  }
  const animalsObject = {}
  getAnimalDataReportResponse.report.map(object => {
    let monthsSinceLastCalving = object['months_since_last_calving_1']
    const monthsSinceLastCalvingAsOfDate = object['month_since_calving_as_of_data_1']
    if (monthsSinceLastCalvingAsOfDate !== undefined) {
      const numberOfDaysDifference = dayjs().diff(dayjs(monthsSinceLastCalvingAsOfDate), 'day')
      const numberOfMonths = Math.round(numberOfDaysDifference*100/30)/100
      if (monthsSinceLastCalving === 0) {
        monthsSinceLastCalving = 0
      } else if ((monthsSinceLastCalving + numberOfMonths) > 12) {
        monthsSinceLastCalving = 0
      } {
        monthsSinceLastCalving = monthsSinceLastCalving + numberOfMonths
      }
    }
    object['days_since_last_calving'] = Math.round(monthsSinceLastCalving*30)

    let numberOfMonthsPregnant = object['number_of_months_pregnant_1']
    const numberOfMonthsPregnantAsOfDate = object['number_of_months_pregnant_as_of_date_1']
    object['from_db_number_of_months_pregnant_1'] = object['number_of_months_pregnant_1']
    object['from_db_number_of_months_pregnant_as_of_date_1'] = object['number_of_months_pregnant_as_of_date_1']
    delete object['number_of_months_pregnant_as_of_date_1']
    if (numberOfMonthsPregnantAsOfDate !== undefined) {
      const numberOfDaysDifference = dayjs().diff(dayjs(numberOfMonthsPregnantAsOfDate), 'day')
      const numberOfMonths = (Math.round(numberOfDaysDifference*100/30)) / 100
      if (numberOfMonthsPregnant === 0) {

      } else if ((numberOfMonthsPregnant + numberOfMonths) > 9) {
        object['number_of_months_pregnant_1'] = 0
      } else if ((numberOfMonthsPregnant + numberOfMonths) <= 9) {
        object['number_of_months_pregnant_1'] = Math.round(numberOfMonthsPregnant + numberOfMonths)
      }
    }
    animalsObject[object['animal_id']] = object
  })
  getAnimalDataReportResponse.animalIdToAnimalInformationNeededForSmartRationGenerationMap = animalsObject
  return getAnimalDataReportResponse
}

const generateSmartRationReportFarmerAnimals = async (userToken, customerId, farmerFoliageData, animalDetailsNeededForSmartRation) => {
  let systemGeneratedReportId
  let constraintGeneratedReportId
  const systemGenerationSmartRationReportResponse = await createSmartRationReport(userToken, customerId, false, farmerFoliageData, animalDetailsNeededForSmartRation)
  if (systemGenerationSmartRationReportResponse && systemGenerationSmartRationReportResponse.monthlyFeeds) {
    systemGeneratedReportId = systemGenerationSmartRationReportResponse.monthlyFeeds.feedAssetId
  }

  // const getAnimalFeedDetailsResponse = {}
  // const getAnimalFeedDetailsResponse = {monthlyFeeds: {feedAssetId: '63f818e8-c73b-11ee-9226-e7d88c3a62ba'}}
  // const getAnimalFeedDetailsResponse = {monthlyFeeds: {feedAssetId: 'a0c3fdc8-c73b-11ee-b21a-e3d342302924'}}
  const systemGenerationFarmerFoliageSmartRationReportResponse = await createSmartRationReport(userToken, customerId, true, farmerFoliageData, animalDetailsNeededForSmartRation)
  if (systemGenerationFarmerFoliageSmartRationReportResponse && systemGenerationFarmerFoliageSmartRationReportResponse.monthlyFeeds) {
    constraintGeneratedReportId = systemGenerationFarmerFoliageSmartRationReportResponse.monthlyFeeds.feedAssetId
  }
  // const getAnimalFeedDetailsResponse = {}
  // const getAnimalFeedDetailsResponse = {monthlyFeeds: {feedAssetId: '63f818e8-c73b-11ee-9226-e7d88c3a62ba'}}
  // const getAnimalFeedDetailsResponse = {monthlyFeeds: {feedAssetId: 'a0c3fdc8-c73b-11ee-b21a-e3d342302924'}}
  return [systemGeneratedReportId, constraintGeneratedReportId]
}

const getReportTypeForFileNameAndDocumentTypeIdForReportIdReportTypeIdArray = (reportArray) => {
  const returnValue = reportArray.map(object => {
    object['asset_id'] = object['report_id']
    switch (object['report_type_id']) {
      case 1001500001:
        object['report_type_in_file_name'] = 'Type 1'
        object['document_type_id'] = 1000260024
        break
      case 1001500002:
        object['report_type_in_file_name'] = 'Type 2'
        object['document_type_id'] = 1000260023
        break
      case 1001500003:
        object['report_type_in_file_name'] = 'Type 3'
        object['document_type_id'] = 1000260022
        break
    }
    return object
  })
  return returnValue
}

const saveVetGeneratedSmartRation = async (userToken, feedData) => {    
  try {
    const headers = {
      useCase: 'Save Vet Generated Smart Ration',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await saveSmartFeedData(headers, feedData)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

export  {
  getTypeDisplayFormat,
  formatFloatValue,
  getQuantityBasedOnDM,
  getFeedComponentQuantity,
  loadSmartRationReportData,
  loadSmartRationReport,
  loadSmartRationCustomerReport,
  getSmartRationDataCreateReportUploadToS3AndCreateDocument,
  collateAnimalInformationForSmartRation,
  generateSmartRationReportFarmerAnimals,
  getReportTypeForFileNameAndDocumentTypeIdForReportIdReportTypeIdArray,
  saveVetGeneratedSmartRation,
}