const { /* get,  */put, /* post,  */configurationJSON, KrushalError } = require('@krushal-it/common-core')
const { post, get } = require('../routerhelper')

const getCustomerAndAnimalDetailsForSmartRation = async (headers, customerId) => {
  try {
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/animalFeeds/animalFeedsData/${customerId}`
    console.log('r gFBD  2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gCR 10, error')
    console.log('r gCR 10a, error = ', error)
    throw error
  }
}

const getAnimalFeedDetailsForCustomerAnimals = async (headers, customerAndAnimalData) => {
  try {
    console.log('r gAFDFCA 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/animalFeeds/allAnimalsFeedRecommendations`
    console.log('r gAFDFCA 2, url = ', url)
    const response = await post(url, headers, customerAndAnimalData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gAFDFCA 10, error')
    console.log('r gAFDFCA 10a, error = ', error)
    throw error
  }
}

const generateSmartRationReportsAndUploadToS3 = async (headers, reportData) => {
  try {
    console.log('r gSRR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v2/sr/generate-smart-ration-report-as-pdf`
    console.log('r gSRR 2, url = ', url)
    const response = await post(url, headers, reportData)
    return response
  } catch (error) {
    console.log('r gSRR 10, error')
    console.log('r gSRR 10a, error = ', error)
    throw error
  }
}

const createDocument = async (headers, documentData) => {
  try {
    console.log('r gSRR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v2/document`
    console.log('r gSRR 2, url = ', url)
    const response = await post(url, headers, documentData)
    return response
  } catch (error) {
    console.log('r gSRR 10, error')
    console.log('r gSRR 10a, error = ', error)
    throw error
  }
}

const upsertDocumentByLanguage = async (headers, documentData) => {
  try {
    console.log('r gSRR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/upsert-document-by-language`
    console.log('r gSRR 2, url = ', url)
    const response = await post(url, headers, documentData)
    return response
  } catch (error) {
    console.log('r gSRR 10, error')
    console.log('r gSRR 10a, error = ', error)
    throw error
  }
}

const getSmartFeedData = async (headers, queryString) => {
  try {
    console.log('r gSFD  1, queryString = ', queryString)
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v2/smartFeed?${queryString}`
    console.log('r gSFD  2, url = ', url)
    const response = await get(url, headers)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSFD 10, error')
    console.log('r gSFD 10a, error = ', error)
    throw error
  }
}

const saveSmartFeedData = async (headers, feedObject) => {
  try {
    console.log('r gSFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/animalFeeds/saveSmartFeedsData`
    console.log('r gSFD 2, url = ', url)
    const response = await post(url, headers, feedObject)
    return response
  } catch (error) {
    console.log('r gSFD 10, error')
    console.log('r gSFD 10a, error = ', error)
    throw error
  }
}

const getSmartRationReport = async (headers, configuration) => {
  try {
    console.log('r gSRR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/smart-ration-list`
    console.log('r gSRR  2, url = ', url)
    const response = await post(url, headers, configuration)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSRR 10, error')
    console.log('r gSRR 10a, error = ', error)
    throw error
  }
}

const getPerCustomerLatestSmartRationReport = async (headers, configuration) => {
  try {
    console.log('r gSRR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/per-customer-latest-smart-ration-list`
    console.log('r gSRR  2, url = ', url)
    const response = await post(url, headers, configuration)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSRR 10, error')
    console.log('r gSRR 10a, error = ', error)
    throw error
  }
}

const setReportAttributes = async (headers, assetIdArrayAndClassifierData) => {
  try {
    console.log('r gSRR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/set-report-attributes`
    console.log('r gSRR  2, url = ', url)
    const response = await post(url, headers, assetIdArrayAndClassifierData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSRR 10, error')
    console.log('r gSRR 10a, error = ', error)
    throw error
  }
}

export  {
  getCustomerAndAnimalDetailsForSmartRation, getAnimalFeedDetailsForCustomerAnimals, generateSmartRationReportsAndUploadToS3, createDocument, upsertDocumentByLanguage, getSmartFeedData, saveSmartFeedData, getSmartRationReport,
  getPerCustomerLatestSmartRationReport, setReportAttributes,
}