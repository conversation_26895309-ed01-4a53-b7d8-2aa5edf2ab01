import { Checkbox, Space, Button } from "antd"
import { ReloadOutlined } from '@ant-design/icons';

import { useState, useRef } from "react"
import { Link, useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import {useSelector} from "react-redux"
import ToastersService from "../../Services/toasters.service";

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const { getFarmerDataReportsPageLevelFilterData, setReportAttributes } = require('../../router')

const { delayInMillis, createPageParamsFromURLString, createURLQueryStringFromPageParams, OCTable } = require('../../Components/Common/AntTableHelper')

const { loadSmartRationCustomerReport } = require('../SmartRationHelper')

const loadFilters = async (userToken, selectedColumns, additionalParams) => {
  try {
    const headers = {
      useCase: 'Get Farmers With Not Location Recorded Report Filter Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getFarmerDataReportsPageLevelFilterData(headers, {selectedColumns: selectedColumns, searchConfiguration: tableConfiguration})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const markSmartRationReportAsSent = async (userToken, assetIdArray) => {
  try {
    console.log('OCFD sFD 1')
    const headers = {
      useCase: 'Mark Report As Sent',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await setReportAttributes(headers, {assetIdArray: assetIdArray, classificationData: {smart_ration_report_sent_to_customer: 1000105001}})
    return response.data
  } catch (error) {
    console.log('OCFD sFD 10, error')
    console.log('OCFD sFD 10a, error = ', error)
    throw error
  }
}

const tableConfiguration = {
  // tableNavigationUrl: '/sr/smart-ration-customers',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'customer_id',
  columns: [
    /* {
      title: "Farmer Id",
      key: "customer_id",
    }, */
    {
      title: "Farmer Name",
      key: "customer_name",
    },
    {
      title: "Farmer Mobile Number",
      key: "mobile_number",
    },
    {
      title: "No. of Active Animals",
      key: "active_animal_count",
    },
    {
      title: "Last Update of Smart Ration Related Attributes",
      key: "smart_ration_attribute_last_updated_at",
    },
    {
      title: "Smart Ration Report Generated",
      key: "smart_ration_generated_or_not",
    },
    {
      title: "Last System Report Generation Date",
      key: "last_system_generated_report_date",
    },
    {
      title: "Animals For Report",
      key: "s_g_n_o_a_i_r",
    },
    {
      title: "Animals With Sys Gen Report",
      key: "s_g_n_o_a_i_r_w_sr",
    },
    {
      title: "Animals With Constraint Gen Report",
      key: "s_g_f_f_n_o_a_i_r_w_sr",
    },
    {
      title: "Animals With Vet Gen Report",
      key: "v_g_n_o_a_i_r_w_sr",
    },
    {
      title: "Sys Gen Report - English",
      key: "s_g_r_d_i_en",
    },
    {
      title: "Sys Gen Report - Marathi",
      key: "s_g_r_d_i_mr",
    },
    {
      title: "Sys Gen Report - Tamil",
      key: "s_g_r_d_i_ta",
    },
    {
      title: "Sys Gen Report - Telugu",
      key: "s_g_r_d_i_te",
    },
    {
      title: "Constraint Gen Report - English",
      key: "s_g_f_f_r_d_i_en",
    },
    {
      title: "Constraint Gen Report - Marathi",
      key: "s_g_f_f_r_d_i_mr",
    },
    {
      title: "Constraint Gen Report - Tamil",
      key: "s_g_f_f_r_d_i_tam",
    },
    {
      title: "Constraint Gen Report - Telugu",
      key: "s_g_f_f_r_d_i_te",
    },
    {
      title: "Vet Gen Report - English",
      key: "v_g_r_d_i_en",
    },
    {
      title: "Vet Gen Report - Marathi",
      key: "v_g_r_d_i_mr",
    },
    {
      title: "Vet Gen Report - Tamil",
      key: "v_g_r_d_i_ta",
    },
    {
      title: "Vet Gen Report - Telugu",
      key: "v_g_r_d_i_te",
    },
    {
      title: "SR Sent to Customer",
      key: "s_r_s_t_c",
    },
    /* {
      title: "Vet Gen Report Id",
      key: "v_g_r_id",
    }, */
    {
      title: "Taluk",
      key: "taluk",
    },
    {
      title: "Village",
      key: "village",
    },
  ],
  columnConfiguration: {
    customer_name: {
      headingText: 'Customer Name',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    mobile_number: {
      headingText: 'Mobile Number',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    active_animal_count: {
      headingText: 'Number of Adult Animals',
      enableFilter: true,
      type: 'number_range',
      // defaultFilteredValue: [[1,5]],
    },
    smart_ration_attribute_last_updated_at: {
      headingText: 'Smart Ration Attributes Last Updated Date',
      formatType: 'date',
      format: 'DD-MMM-YYYY',  
      enableFilter: true,
      type: 'dayjs_range',
      enableSort: true,
      sortColumnType: 'date',
      defaultSort: 'descend',
    },
    smart_ration_generated_or_not: {
      headingText: 'Smart Ration Generated Or Not',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      filters: [
        { text: 'Yes', value: 'Yes', value_int: 1000105001 },
        { text: 'No', value: 'No', value_int: 1000105002 },
      ],
      // defaultFilteredValue: ['No'],
      translateFilteredValuesToAlt: 'value_int',
      altColumn: 'smart_ration_generated_or_not_id'
    },
    last_system_generated_report_date: {
      headingText: 'Smart Ration Report Generation Date',
      formatType: 'date',
      format: 'DD-MMM-YYYY',  
      enableFilter: true,
      type: 'dayjs_range',
      enableSort: true,
      sortColumnType: 'date',
    },
    s_g_n_o_a_i_r: {
      type: 'number_range',
      headingText: 'Number of Animals Used For Smart Ration Report',
      enableFilter: true,
      formatType: 'int',
      enableSort: true,
      sortColumnType: 'int',
    },
    s_g_n_o_a_i_r_w_sr: {
      type: 'number_range',
      headingText: 'Number of Animals In Sys Gen Smart Ration Report',
      enableFilter: true,
      formatType: 'int',
      enableSort: true,
      sortColumnType: 'int',
    },
    s_g_f_f_n_o_a_i_r_w_sr: {
      type: 'number_range',
      headingText: 'Number of Animals In Constraint Gen Smart Ration Report',
      enableFilter: true,
      formatType: 'int',
      enableSort: true,
      sortColumnType: 'int',
    },
    v_g_n_o_a_i_r_w_sr: {
      type: 'number_range',
      headingText: 'Number of Animals In Vet Gen Smart Ration Report',
      enableFilter: true,
      formatType: 'int',
      enableSort: true,
      sortColumnType: 'int',
    },
    s_g_r_d_i_en: {
      formatType: 's3Link',
      linkText: 'Smart Ration (en)'
    },
    s_g_r_d_i_mr: {
      formatType: 's3Link',
      linkText: 'Smart Ration (mr)'
    },
    s_g_r_d_i_ta: {
      formatType: 's3Link',
      linkText: 'Smart Ration (ta)'
    },
    s_g_f_f_r_d_i_en: {
      formatType: 's3Link',
      linkText: 'Smart Ration (en)'
    },
    s_g_f_f_r_d_i_mr: {
      formatType: 's3Link',
      linkText: 'Smart Ration (mr)'
    },
    s_g_f_f_r_d_i_ta: {
      formatType: 's3Link',
      linkText: 'Smart Ration (ta)'
    },
    s_g_f_f_r_d_i_te: {
      formatType: 's3Link',
      linkText: 'Smart Ration (te)'
    },
    v_g_r_d_i_en: {
      formatType: 's3Link',
      linkText: 'Smart Ration (en)'
    },
    v_g_r_d_i_mr: {
      formatType: 's3Link',
      linkText: 'Smart Ration (mr)'
    },
    v_g_r_d_i_ta: {
      formatType: 's3Link',
      linkText: 'Smart Ration (ta)'
    },
    taluk: {
      headingText: 'Taluk',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      altColumn: 'taluk_id',
      updateFilterDataDuringFilterCall: true,
    },
    village: {
      headingText: 'Village',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      updateFilterDataDuringReportCall: true,
      // defaultFilteredValue: ['Shelgaon'],
    },
    s_r_s_t_c: {
      headingText: 'Report Sent to Customer',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      filters: [
        { text: 'Yes', value: 'Yes', value_int: 1000105001 },
        { text: 'No', value: 'No', value_int: 1000105002 },
      ],
      // defaultFilteredValue: ['No'],
      translateFilteredValuesToAlt: 'value_int',
      altColumn: 's_r_s_t_c_id'
    }
  }
}

const SmartRationCustomers = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { pathname, search } = location
  tableConfiguration.tableNavigationUrl = pathname

  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const tableRef = useRef()

  const [excludeBDMInQuery, setExcludeBDMInQuery] = useState(true)

  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    tableConfiguration.enableTableExport = true
    tableConfiguration.enableTableExportFromCloud = true
  }

  const [reportData, setReportData] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const loadInitialFilterDataCB = (additionalParams) => {
    const selectedColumns = tableRef.current.getSelectedColumnsInTable()
    return loadFilters(userToken, selectedColumns, additionalParams)
  }

  const loadReportCB = (reportQueryParams, additionalParams) => {
    reportQueryParams.customerTypeIds = [1000220001, 1000220009]
    return loadSmartRationCustomerReport(userToken, reportQueryParams, additionalParams)
  }

  const handleAddFarmerClick = async (event) => {
    const navigationLink = `/e/farmer-details/-1`
    const navigationSearchQuery = 'cti=1000220009'
    const navigationLinkWithSuffix = navigationLink + '?' + navigationSearchQuery
    if (event.ctrlKey || event.metaKey) {
      window.open(navigationLinkWithSuffix, '_blank')
    } else {
      navigate({
        pathname: navigationLink,
        search: navigationSearchQuery
      })
    }
  }

  const handleViewFarmerDetailsClick = (event) => {
    const customerId = selectedRowKeys[0]
    const queryParams = {
      cId: customerId,
    }; // Example query parameters
    const paramsAsString = JSON.stringify(queryParams)
    const encodedParamsAsString = encodeURIComponent(paramsAsString)
    const navigationLink = `/e/farmer-details/${customerId}`
    // const urlQueryString = '' // 'role=sr'
    const urlQueryString = 'role=sr'
    if (event.ctrlKey || event.metaKey) {
      window.open(navigationLink + '?' + urlQueryString, '_blank')
    } else {
      navigate({
        pathname: navigationLink,
        // search: `?qP=${encodedParamsAsString}`
        search: urlQueryString === '' ? undefined : urlQueryString
      })
    }
  }

  const handleViewFarmerSmartRationClick = (event) => {
    const customerId = selectedRowKeys[0]
    const selectedRow = reportData.filter((object) => {
      return (object['customer_id'] === selectedRowKeys[0])
    })
    const systemGeneratedReportId = selectedRow[0]['s_g_r_id']
    const systemGeneratedFarmerFoliageReportId = selectedRow[0]['s_g_f_f_r_id']
    const vetGeneratedReportId = selectedRow[0]['v_g_r_id']
    const queryParams = {
      sgRId: systemGeneratedReportId !== undefined && systemGeneratedReportId !== null ? systemGeneratedReportId : undefined ,
      sgFFRId: systemGeneratedFarmerFoliageReportId !== undefined && systemGeneratedFarmerFoliageReportId !== null ? systemGeneratedFarmerFoliageReportId : undefined,
      vRId: vetGeneratedReportId !== undefined && vetGeneratedReportId !== null ? vetGeneratedReportId : undefined,
    }; // Example query parameters
    const paramsAsString = JSON.stringify(queryParams)
    const encodedParamsAsString = encodeURIComponent(paramsAsString)
    // const urlQueryString = 'srRs=' + encodedParamsAsString
    const urlQueryString = ''
  
    const navigationLink = `../sr/customer-smart-ration/${customerId}`
    if (event.ctrlKey || event.metaKey) {
      // window.open(navigationLink + '?' + urlQueryString, '_blank')
      window.open(navigationLink, '_blank')
    } else {
      navigate({
        pathname: navigationLink,
        // search: `?qP=${encodedParamsAsString}`
        // search: urlQueryString,
      })
    }
  }

  const handleMarkReportAsSentClick = async (event) => {
    try {
      console.log('SRC hMRASC 1')
      const selectedRows = reportData.filter((object) => {
        return (selectedRowKeys.includes(object['customer_id']))
      })
      const systemGeneratedReportIdArray = selectedRows.map(object => {
        return object['s_g_r_id']
      })
      const markSmartRationReportAsSentResponse = await markSmartRationReportAsSent(userToken, systemGeneratedReportIdArray)
      if (markSmartRationReportAsSentResponse.return_code !== 0) {
        ToastersService.failureToast('Error in marking report as sent')
        return
      }
      tableRef.current.reloadDataAndMoveToPageOne()
      ToastersService.successToast('Successfully marked reports as sent')
    } catch (error) {
      console.log('SRC hMRASC 10, error')
      console.log('SRC hMRASC 10a, error = ', error)
    } finally {

    }
  }

  return (
    <>
      <Space>
        <Link to="/sr">Back to Queue List</Link>
        <Button size="small" style={{ marginLeft: 8 }} disabled={(selectedRowKeys.length !== 0)} onClick={handleAddFarmerClick}>Add Farmer</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={(selectedRowKeys.length === 0)} onClick={handleViewFarmerDetailsClick}>View Farmer Details</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={(selectedRowKeys.length === 0)} onClick={handleViewFarmerSmartRationClick}>View Farmer Smart Ration</Button>
        <Button size="small" style={{ marginLeft: 8 }} disabled={(selectedRowKeys.length === 0)} onClick={handleMarkReportAsSentClick}>Mark Report As Sent</Button>
      </Space>
      <OCTable
        ref={tableRef}
        /* tableKey={tableKey}
        setTableKey={setTableKey} */
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={tableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        // postInitialLoadCallback={postInitialLoadCB}
        loadReportCallback={loadReportCB}
        loadInitialFilterDataCallback={loadInitialFilterDataCB}
        // parentPageParams={pageParams}
        // showTableHeader={true}
        // omniSearchRowEnabled={true}
        // omniSearchRowBeginning={backToQueueListLinkUI}
        // omniSearchRowMiddle={viewRelatedAnimalButtons}
        // actionsRow={backToQueueListLinkUIAndViewRelatedAnimalButtons}
      />
    </>
  );
}

// export default SmartRationCustomers
export default withAuthorization(SmartRationCustomers, [CS_TEAM, CENTRAL_VET])