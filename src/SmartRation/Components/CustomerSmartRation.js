import { Spin, Row, Col, Card, Typography, Checkbox, InputNumber, Table, Select, Space, Button, Alert } from "antd"
import { ReloadOutlined } from '@ant-design/icons';

import { useState, useRef, useEffect } from "react"
import { Link, useLocation, useNavigate, useParams, useSearchParams } from 'react-router-dom';
import {useSelector} from "react-redux"
import dayjs from "dayjs";

import '../../index.css'
import ToastersService from '../../Services/toasters.service'

const lodashObject = require("lodash");

const { Option } = Select
const { Text } = Typography

const {
  extractBasedOnLanguage,
  assignL10NObjectOrObjectArrayToItSelf,
  configurationJSON,
} = require("@krushal-it/common-core")

const BASE_URL = configurationJSON().SERVICE_END_POINTS.BACK_END

const { KrushalOCDropdown, KrushalOCInput } = require('../../Components/Common/KrushalOCForm')

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const { getFarmerBasicData, getCustomerInformationBasedOnClassifiers, getSmartFeedData, getAnimalsForFarmer, saveSmartFeedData, generateSmartRationReportAndUploadToS3, createDocument } = require('../../router')
const { loadSmartRationReportData, collateAnimalInformationForSmartRation, getTypeDisplayFormat, formatFloatValue, getQuantityBasedOnDM, getFeedComponentQuantity, saveVetGeneratedSmartRation, loadSmartRationCustomerReport, getSmartRationDataCreateReportUploadToS3AndCreateDocument } = require('../SmartRationHelper')

const { delayInMillis, createPageParamsFromURLString, createURLQueryStringFromPageParams, OCTable } = require('../../Components/Common/AntTableHelper')

const loadBasicFarmerData = async (userToken, customerId) => {
  try {
    const headers = {
      useCase: 'Get Basic Farmer Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getFarmerBasicData(headers, customerId)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getFarmerData = async (userToken, customerId, classifierKeyArray) => {
  try {
    console.log('OCFD gFD 1')
    const headers = {
      useCase: 'Get Farmers Data Based On Classification',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getCustomerInformationBasedOnClassifiers(headers, {customerId: customerId, classifierArray: classifierKeyArray})
    return response.data
  } catch (error) {
    console.log('OCFD gFD 10, error')
    console.log('OCFD gFD 10a, error = ', error)
    throw error
  }
}

const CustomerSmartRation = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { pathname, search } = location
  
  const params = useParams()
  const [searchParams] = useSearchParams()
  let systemGeneratedReportId
  let systemGeneratedFarmerFoliageReportId
  let vetGeneratedReportId
  // if (searchParams.get('srRs')) {
  //   const uncompressedFilterParamsJsonString = decodeURIComponent(searchParams.get('srRs'))
  //   const smartRationReportIds = JSON.parse(uncompressedFilterParamsJsonString)
  //   // let {systemGeneratedReportId, systemGeneratedFarmerFoliageReportId, vetGeneratedReportId} = smartRationReportIds
  //   console.log('Check Values')
  //   ;({sgRId: systemGeneratedReportId, sgFFRId: systemGeneratedFarmerFoliageReportId, vRId: vetGeneratedReportId} = smartRationReportIds);
  //   console.log('Check Values')
  // }
  const customerId = params.customerId
  
  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  
  const [vetGeneratedFeedTableSelectedRowKeys, setVetGeneratedFeedTableSelectedRowKeys] = useState({})

  const [loading, setLoading] = useState(false)
  const [showSystemGeneratedColumn, setShowSystemGeneratedColumn] = useState(false)
  const [showConstraintGeneratedColumn, setShowConstraintGeneratedColumn] = useState(false)
  const [showVetGeneratedColumn, setShowVetGeneratedColumn] = useState(true)
  const [showType, setShowType] = useState(true)
  const [showConstituents, setShowConstituents] = useState(true)
  const [reportLanguage, setReportLanguage] = useState(['en'])
  const [forceVetGeneratedSummary, setForceVetGeneratedSummary] = useState(false)
  const [forceDelayVetGeneratedSummary, setForceDelayVetGeneratedSummary] = useState(false)
  const [forDelayOfSaveFeedClick, setForDelayOfSaveFeedClick] = useState(-1)

  const [foliageReferenceIdArray, setFoliageReferenceIdArray] = useState([])
  const [concentrateReferenceIdArray, setConcentrateReferenceIdArray] = useState([])
  const [otherNutrientReferenceIdArray, setOtherNutrientReferenceIdArray] = useState([])
  const [feedReferences, setFeedReferences] = useState([])
  const [feedReferencesInEnglish, setFeedReferencesInEnglish] = useState([])

  const [loadInitialData, setLoadInitialData] = useState(false)

  
  const [farmerCardData, setFarmerCardData] = useState({})
  const [animalCardData, setAnimalCardData] = useState([])

  const [farmerLevelSystemGeneratedFeedSummaryReport, setFarmerLevelSystemGeneratedFeedSummaryReport] = useState([])
  const [farmerLevelConstraintGeneratedFeedSummaryReport, setFarmerLevelConstraintGeneratedFeedSummaryReport] = useState([])
  const [farmerLevelVetGeneratedFeedSummaryReport, setFarmerLevelVetGeneratedFeedSummaryReport] = useState([])

  const [systemGeneratedFeedByAnimalData, setSystemGeneratedFeedByAnimalData] = useState({})
  const [constraintGeneratedFeedByAnimalData, setConstraintGeneratedFeedByAnimalData] = useState({})
  const [vetGeneratedFeedByAnimalData, setVetGeneratedFeedByAnimalData] = useState({})

  const [systemGeneratedReportEn, setSystemGeneratedReportEn] = useState({})
  const [systemGeneratedReportMr, setSystemGeneratedReportMr] = useState({})
  const [systemGeneratedReportTa, setSystemGeneratedReportTa] = useState({})
  const [systemGeneratedReportTe, setSystemGeneratedReportTe] = useState({})
  const [constraintGeneratedReportEn, setConstraintGeneratedReportEn] = useState({})
  const [constraintGeneratedReportMr, setConstraintGeneratedReportMr] = useState({})
  const [constraintGeneratedReportTa, setConstraintGeneratedReportTa] = useState({})
  const [constraintGeneratedReportTe, setConstraintGeneratedReportTe] = useState({})
  const [vetGeneratedReportEn, setVetGeneratedReportEn] = useState({})
  const [vetGeneratedReportMr, setVetGeneratedReportMr] = useState({})
  const [vetGeneratedReportTa, setVetGeneratedReportTa] = useState({})
  const [vetGeneratedReportTe, setVetGeneratedReportTe] = useState({})
  const [animalSelectedForReportMap, setAnimalSelectedForReportMap] = useState({})
  const [allAnimalCheckboxState, setAllAnimalCheckboxState] = useState(true)
  const [allAnimalIndeterminateState, setAllAnimalIndeterminateState] = useState(false)

  const onChangeCheckAllAnimalsForReport = (arg1, arg2, arg3, arg4) => {
    // console.log('CSR oCCAAFR 1')
    let copiedAnimalSelectedForReportMap = lodashObject.cloneDeep(animalSelectedForReportMap)
    if (allAnimalIndeterminateState) {
      // console.log('CSR oCCAAFR 2')
      setAllAnimalIndeterminateState(false)
      setAllAnimalCheckboxState(true)
      // select individual animals
      copiedAnimalSelectedForReportMap = lodashObject.mapValues(copiedAnimalSelectedForReportMap, () => 1000105001)
    } else {
      // console.log('CSR oCCAAFR 3')
      if (!allAnimalCheckboxState) {
        // console.log('CSR oCCAAFR 4')
        setAllAnimalCheckboxState(true)
        // select individual animals
        copiedAnimalSelectedForReportMap = lodashObject.mapValues(copiedAnimalSelectedForReportMap, () => 1000105001)
      } else {
        // console.log('CSR oCCAAFR 5')
        setAllAnimalCheckboxState(false)
        // unselect individual animals
        copiedAnimalSelectedForReportMap = lodashObject.mapValues(copiedAnimalSelectedForReportMap, () => 1000105002)
      }
    }
    setAnimalSelectedForReportMap(copiedAnimalSelectedForReportMap)
    setForceDelayVetGeneratedSummary(true)
  }

  const updateAllAnimalsCheckboxState = (index, animalId) => {
    const copiedAnimalSelectedForReportMap = lodashObject.cloneDeep(animalSelectedForReportMap)
    if (copiedAnimalSelectedForReportMap[animalId] === 1000105002) {
      copiedAnimalSelectedForReportMap[animalId] = 1000105001
    } else {
      copiedAnimalSelectedForReportMap[animalId] = 1000105002
    }
    const valuesInAnimalsSelectedForReportMap = lodashObject.values(copiedAnimalSelectedForReportMap)
    const countOfValuesInAnimalsSelectedForReportMap = lodashObject.countBy(valuesInAnimalsSelectedForReportMap);
    const keysOfCountOfValuesInAnimalsSelectedForReportMap = Object.keys(countOfValuesInAnimalsSelectedForReportMap);
    // console.log('CSR uAACBS 1, valuesInAnimalsSelectedForReportMap = ', valuesInAnimalsSelectedForReportMap)
    // console.log('CSR uAACBS 2, countOfValuesInAnimalsSelectedForReportMap = ', countOfValuesInAnimalsSelectedForReportMap)
    // console.log('CSR uAACBS 3, keysOfCountOfValuesInAnimalsSelectedForReportMap = ', keysOfCountOfValuesInAnimalsSelectedForReportMap)
    if (keysOfCountOfValuesInAnimalsSelectedForReportMap.length > 1) {
      setAllAnimalIndeterminateState(true)
    } else {
      setAllAnimalIndeterminateState(false)
      // console.log('CSR uAACBS 3, valuesInAnimalsSelectedForReportMap[0] = ', valuesInAnimalsSelectedForReportMap[0])
      if (valuesInAnimalsSelectedForReportMap[0] === 1000105001) {
        // console.log('CSR uAACBS 4, setting to 1000105001')
        setAllAnimalCheckboxState(true)
      } else {
        // console.log('CSR uAACBS 5, setting to 1000105002')
        setAllAnimalCheckboxState(false)
      }
    }
    setAnimalSelectedForReportMap(copiedAnimalSelectedForReportMap)
    setForceDelayVetGeneratedSummary(true)
  }

  const setDataForFarmerInfoCard = (farmerBasicData, farmerFoliageAndConcentrateData) => {
    const farmerName = extractBasedOnLanguage(farmerBasicData['customer_name'])
    const farmerMobile = extractBasedOnLanguage(farmerBasicData['mobile_number'])
    const referenceMap = global.references.referenceCategoryToReferencesMap
    let farmerFoliages
    if (farmerFoliageAndConcentrateData['farmer_foliage_available_1'] && Array.isArray(farmerFoliageAndConcentrateData['farmer_foliage_available_1']) && farmerFoliageAndConcentrateData['farmer_foliage_available_1'].length > 0) {
      const referencesOfCategory = referenceMap[10001300]
      const englishReferencesOfCategory = assignL10NObjectOrObjectArrayToItSelf(referencesOfCategory, 'reference_name_l10n')
      const farmerFoliagesAsObjectArray = englishReferencesOfCategory.filter(object => farmerFoliageAndConcentrateData['farmer_foliage_available_1'].includes(object['reference_id']))
      const farmerFoliagesAsArray = farmerFoliagesAsObjectArray.map(object => object['reference_name_l10n'])
      farmerFoliages = farmerFoliagesAsArray.join(', ')
    }
    let farmerConcentrates
    if (farmerFoliageAndConcentrateData['farmer_concentrates_available_1'] && Array.isArray(farmerFoliageAndConcentrateData['farmer_concentrates_available_1']) && farmerFoliageAndConcentrateData['farmer_concentrates_available_1'].length > 0) {
      const referencesOfCategory = referenceMap[10013000]
      const englishReferencesOfCategory = assignL10NObjectOrObjectArrayToItSelf(referencesOfCategory, 'reference_name_l10n')
      const farmerConcentratesAsObjectArray = englishReferencesOfCategory.filter(object => farmerFoliageAndConcentrateData['farmer_concentrates_available_1'].includes(object['reference_id']))
      const farmerConcentratesAsArray = farmerConcentratesAsObjectArray.map(object => object['reference_name_l10n'])
      farmerConcentrates = farmerConcentratesAsArray.join(', ')
    }
    setFarmerCardData({name: farmerName, mobile: farmerMobile, foliages: farmerFoliages, concentrates: farmerConcentrates})
  }

  const setDataForAnimalInfoCard = (animalDataReport, animalNutritionRequirement) => {
    const animalCardArray = []
    const animalSelectedForReportMap = {}
    for (const animalData of animalDataReport) {
      const animalId = animalData['animal_id']

      animalSelectedForReportMap[animalId] = 1000105001
      
      const animalEarTag = animalData['ear_tag_1']
      const animalWeight = formatFloatValue(animalData['animal_weight_1'], 0)
      const animalBodyScore = formatFloatValue(animalData['animal_body_score_1'], 1)
      let milkFatPercentage = formatFloatValue(animalData['animal_milk_fat_1'], 1)
      if (milkFatPercentage !== '') {
        milkFatPercentage = milkFatPercentage + ' %'
      }
      let numberOfMonthsPregnant = animalData['number_of_months_pregnant_1']
      const numberOfMonthsPregnantAsOfDate = animalData['number_of_months_pregnant_as_of_date_1']
      if (numberOfMonthsPregnantAsOfDate !== undefined) {
        const numberOfMonths = dayjs().diff(dayjs(numberOfMonthsPregnantAsOfDate), 'month')
        if (numberOfMonthsPregnant === 0) {
          
        } else if ((numberOfMonthsPregnant + numberOfMonths) > 9) {
          numberOfMonthsPregnant = 0
        } else if ((numberOfMonthsPregnant + numberOfMonths) <= 9) {
          numberOfMonthsPregnant = numberOfMonthsPregnant + numberOfMonths
        }
      }

      let monthsSinceLastCalving = animalData['months_since_last_calving_1']
      const monthsSinceLastCalvingAsOfDate = animalData['month_since_calving_as_of_data_1']
      if (monthsSinceLastCalvingAsOfDate !== undefined) {
        const numberOfMonths = dayjs().diff(dayjs(monthsSinceLastCalvingAsOfDate), 'month')
        if (monthsSinceLastCalving === 0) {
          monthsSinceLastCalving = 0
        }
         
          monthsSinceLastCalving = monthsSinceLastCalving + numberOfMonths
        
      }
      const daysSinceCalving = monthsSinceLastCalving*30
      const litresPerDay = formatFloatValue(animalData['animal_average_lpd_1'], 1)
      const projectedLitresPerDay = formatFloatValue(animalData['animal_average_lpd_1']*1.15, 1)
      const dmMax = formatFloatValue(animalNutritionRequirement[animalId]['dm_max'], 2)
      const dmMin = formatFloatValue(animalNutritionRequirement[animalId]['dm_min'], 2)
      const dcp = formatFloatValue(animalNutritionRequirement[animalId]['dcp'], 2)
      const tdn = formatFloatValue(animalNutritionRequirement[animalId]['tdn'], 2)
      const animalCardObject = {
        animal_id: animalId,
        ear_tag: animalEarTag,
        weight: animalWeight,
        body_score: animalBodyScore,
        milk_fat_percentage: milkFatPercentage,
        months_pregnant: numberOfMonthsPregnant,
        days_since_calving: daysSinceCalving,
        litres_per_day: litresPerDay,
        projected_litres_per_day: projectedLitresPerDay,
        dm_max: dmMax,
        dm_min: dmMin,
        dcp: dcp,
        tdn: tdn
      }
      animalCardArray.push(animalCardObject)
    }
    setAnimalSelectedForReportMap(animalSelectedForReportMap)
    setAnimalCardData(animalCardArray)
  }

  const setDataForFeedRecommendationCard = (feedData, isAnimalRelatedFeed) => {
    const feedRecommendationCardLevelFormattedData = []
    let counter = 0
    for (const feedItem of [...feedData.smart_ration_foliages, ...feedData.smart_ration_concentrates, ...feedData.smart_ration_other_nutrients]) {
      counter++
      const feedReferenceId = feedItem['reference_id']
      const isOtherNutrient = (feedReferenceId !== undefined && otherNutrientReferenceIdArray.includes(feedReferenceId)) ? true : false
      let feedWeightInKg = feedItem['smart_ration_weight']
      let feedWeightInKgAsString = feedItem['smart_ration_weight']
      if (feedWeightInKgAsString === undefined || feedWeightInKgAsString === null || feedWeightInKgAsString === '') {
        feedWeightInKgAsString = ''
        feedWeightInKg = undefined
      } else {
        feedWeightInKgAsString = parseFloat(feedWeightInKgAsString)
        if (!isNaN(feedWeightInKgAsString)) {
          feedWeightInKg = feedWeightInKgAsString
          if (isOtherNutrient && isAnimalRelatedFeed === true) {
            feedWeightInKgAsString = feedWeightInKgAsString*1000
          }
          if (isOtherNutrient && !isAnimalRelatedFeed) {
            feedWeightInKgAsString = feedWeightInKgAsString.toFixed(2)
          } else {
            feedWeightInKgAsString = feedWeightInKgAsString.toFixed(0)
          }
        } else {
          feedWeightInKgAsString = ''
          feedWeightInKg = undefined
        }
      }
      
      let feedCostWithoutRupee = feedItem['smart_ration_cost']
      let feedCost = ''
      if (feedCostWithoutRupee === undefined || feedCostWithoutRupee === null || feedCostWithoutRupee === '') {
        feedCostWithoutRupee = 0
      }
      if (feedCostWithoutRupee !== 0) {
        feedCostWithoutRupee = parseFloat(feedCostWithoutRupee)
        if (!isNaN(feedCostWithoutRupee)) {
          feedCost = '₹ ' + feedCostWithoutRupee.toFixed(0)
        } else {
          feedCost = ''
          feedCostWithoutRupee = 0
        }
      }
      
      const feedObject = feedReferences.filter(object => object['reference_id'] === feedReferenceId)
      const feedName = extractBasedOnLanguage(feedObject[0]['reference_name_l10n'])
      const feedReferenceInformation = feedObject[0]['reference_information']
      const feedProperties = feedReferenceInformation && feedReferenceInformation['feed_properties'] !== undefined ? feedReferenceInformation['feed_properties'] : undefined

      const feedDMAsFloat = getFeedComponentQuantity(feedWeightInKg, feedProperties, 'dm', isOtherNutrient)
      const feedDCPAsFloat = getFeedComponentQuantity(feedWeightInKg, feedProperties, 'dcp', isOtherNutrient)
      const feedTDNAsFloat = getFeedComponentQuantity(feedWeightInKg, feedProperties, 'tdn', isOtherNutrient)
      const feedDM = (feedDMAsFloat === 0) ? '' : feedDMAsFloat.toFixed(2)
      const feedDCP = (feedDCPAsFloat === 0) ? '' : feedDCPAsFloat.toFixed(2)
      const feedTDN = (feedTDNAsFloat === 0) ? '' : feedTDNAsFloat.toFixed(2)

      const feedType = feedProperties && feedProperties['type'] ? getTypeDisplayFormat(feedProperties['type']) : ''
      
      const feedRow = {
        feed_row: counter,
        feed: feedName,
        type: feedType,
        quantity: feedWeightInKgAsString,
        quantity_as_float: feedWeightInKg,
        unit: 'kg',
        cost: feedCost,
        cost_without_rupee: feedCostWithoutRupee,
        dm: feedDM,
        dcp: feedDCP,
        tdn: feedTDN,
        dm_as_float: feedDMAsFloat,
        dcp_as_float: feedDCPAsFloat,
        tdn_as_float: feedTDNAsFloat,
        feed_reference_id: feedReferenceId,
      }
      if (isAnimalRelatedFeed) {
        feedRow.animal_id = feedData['animal_id']
      }
      feedRecommendationCardLevelFormattedData.push(feedRow)
    }
    return feedRecommendationCardLevelFormattedData
  }

  const loadReportLinks = async (userToken) => {
    let systemGeneratedReportId
    let systemGeneratedFarmerFoliageReportId
    let vetGeneratedReportId
    const queryParams = {
      searchConfiguration: {
        customer_id: {
          enableFilter: true,
          type: 'default_array',
          dataType: 'uuid',
        }
      },
      selectedColumns: [
        'customer_name',
        's_g_r_d_i_en',
        's_g_r_d_i_mr',
        's_g_r_d_i_ta',
        's_g_r_d_i_te',
        's_g_f_f_r_d_i_en',
        's_g_f_f_r_d_i_mr',
        's_g_f_f_r_d_i_ta',
        's_g_f_f_r_d_i_te',
        'v_g_r_d_i_en',
        'v_g_r_d_i_mr',
        'v_g_r_d_i_ta',
        'v_g_r_d_i_te',
      ],
      start: 0,
      size: -1,
      filters: {
        customer_id: [customerId]
      },
      forceLoadAllColumns: true
    }
    
    const latestReportResponse = await loadSmartRationCustomerReport(userToken, queryParams)
    if (latestReportResponse.return_code === 0) {
      if (latestReportResponse.report.length > 0) {
        const customerReportsRow = latestReportResponse.report[0]
        const systemGeneratedSmartRationReportPDFEnglishDocumentInformation = customerReportsRow['s_g_r_d_i_en']
        const systemGeneratedSmartRationReportPDFMarathiDocumentInformation = customerReportsRow['s_g_r_d_i_mr']
        const systemGeneratedSmartRationReportPDFTamilDocumentInformation = customerReportsRow['s_g_r_d_i_ta']
        const systemGeneratedSmartRationReportPDFTeluguDocumentInformation = customerReportsRow['s_g_r_d_i_te']
        const constraintGeneratedSmartRationReportPDFEnglishDocumentInformation = customerReportsRow['s_g_f_f_r_d_i_en']
        const constraintGeneratedSmartRationReportPDFMarathiDocumentInformation = customerReportsRow['s_g_f_f_r_d_i_mr']
        const constraintGeneratedSmartRationReportPDFTamilDocumentInformation = customerReportsRow['s_g_f_f_r_d_i_ta']
        const constraintGeneratedSmartRationReportPDFTeluguDocumentInformation = customerReportsRow['s_g_f_f_r_d_i_te']
        const vetGeneratedSmartRationReportPDFEnglishDocumentInformation = customerReportsRow['v_g_r_d_i_en']
        const vetGeneratedSmartRationReportPDFMarathiDocumentInformation = customerReportsRow['v_g_r_d_i_mr']
        const vetGeneratedSmartRationReportPDFTamilDocumentInformation = customerReportsRow['v_g_r_d_i_ta']
        const vetGeneratedSmartRationReportPDFTeluguDocumentInformation = customerReportsRow['v_g_r_d_i_te']
        systemGeneratedReportId = customerReportsRow['s_g_r_id']
        systemGeneratedFarmerFoliageReportId = customerReportsRow['s_g_f_f_r_id']
        vetGeneratedReportId = customerReportsRow['v_g_r_id']
        if (systemGeneratedSmartRationReportPDFEnglishDocumentInformation !== undefined && systemGeneratedSmartRationReportPDFEnglishDocumentInformation !== null) {
          setSystemGeneratedReportEn(systemGeneratedSmartRationReportPDFEnglishDocumentInformation)
        }
        if (systemGeneratedSmartRationReportPDFMarathiDocumentInformation !== undefined && systemGeneratedSmartRationReportPDFMarathiDocumentInformation !== null) {
          setSystemGeneratedReportMr(systemGeneratedSmartRationReportPDFMarathiDocumentInformation)
        }
        if (systemGeneratedSmartRationReportPDFTamilDocumentInformation !== undefined && systemGeneratedSmartRationReportPDFTamilDocumentInformation !== null) {
          setSystemGeneratedReportTa(systemGeneratedSmartRationReportPDFTamilDocumentInformation)
        }
        if (systemGeneratedSmartRationReportPDFTeluguDocumentInformation !== undefined && systemGeneratedSmartRationReportPDFTeluguDocumentInformation !== null) {
          setSystemGeneratedReportTe(systemGeneratedSmartRationReportPDFTeluguDocumentInformation)
        }
        if (constraintGeneratedSmartRationReportPDFEnglishDocumentInformation !== undefined && constraintGeneratedSmartRationReportPDFEnglishDocumentInformation !== null) {
          setConstraintGeneratedReportEn(constraintGeneratedSmartRationReportPDFEnglishDocumentInformation)
        }
        if (constraintGeneratedSmartRationReportPDFMarathiDocumentInformation !== undefined && constraintGeneratedSmartRationReportPDFMarathiDocumentInformation !== null) {
          setConstraintGeneratedReportMr(constraintGeneratedSmartRationReportPDFMarathiDocumentInformation)
        }
        if (constraintGeneratedSmartRationReportPDFTamilDocumentInformation !== undefined && constraintGeneratedSmartRationReportPDFTamilDocumentInformation !== null) {
          setConstraintGeneratedReportTa(constraintGeneratedSmartRationReportPDFTamilDocumentInformation)
        }
        if (constraintGeneratedSmartRationReportPDFTeluguDocumentInformation !== undefined && constraintGeneratedSmartRationReportPDFTeluguDocumentInformation !== null) {
          setConstraintGeneratedReportTe(constraintGeneratedSmartRationReportPDFTeluguDocumentInformation)
        }
        if (vetGeneratedSmartRationReportPDFEnglishDocumentInformation !== undefined && vetGeneratedSmartRationReportPDFEnglishDocumentInformation !== null) {
          setVetGeneratedReportEn(vetGeneratedSmartRationReportPDFEnglishDocumentInformation)
        }
        if (vetGeneratedSmartRationReportPDFMarathiDocumentInformation !== undefined && vetGeneratedSmartRationReportPDFMarathiDocumentInformation !== null) {
          setVetGeneratedReportMr(vetGeneratedSmartRationReportPDFMarathiDocumentInformation)
        }
        if (vetGeneratedSmartRationReportPDFTamilDocumentInformation !== undefined && vetGeneratedSmartRationReportPDFTamilDocumentInformation !== null) {
          setVetGeneratedReportTa(vetGeneratedSmartRationReportPDFTamilDocumentInformation)
        }
        if (vetGeneratedSmartRationReportPDFTeluguDocumentInformation !== undefined && vetGeneratedSmartRationReportPDFTeluguDocumentInformation !== null) {
          setVetGeneratedReportTe(vetGeneratedSmartRationReportPDFTeluguDocumentInformation)
        }
      }
    }
    return [systemGeneratedReportId, systemGeneratedFarmerFoliageReportId, vetGeneratedReportId]
  }

  const asyncUseEffect = async () => {
    try {
      setLoading(true)
      const farmerBasicDataResponse = await loadBasicFarmerData(userToken, customerId)
      const farmerFoliageAndConcentrateDataResponse = await getFarmerData(userToken, customerId, ['farmer_foliage_available_1', 'farmer_concentrates_available_1'])
      if (farmerBasicDataResponse.return_code !== 0
          || farmerFoliageAndConcentrateDataResponse.return_code !== 0) {
        ToastersService.failureToast('Could not obtain farmer data')
        return
      }
      setDataForFarmerInfoCard(farmerBasicDataResponse.result, farmerFoliageAndConcentrateDataResponse.data)

      const [systemGeneratedReportId1, systemGeneratedFarmerFoliageReportId1, vetGeneratedReportId1] = await loadReportLinks(userToken)
      if (systemGeneratedReportId1 !== undefined && systemGeneratedReportId1 !== null) {
        systemGeneratedReportId = systemGeneratedReportId1
      }
      if (systemGeneratedFarmerFoliageReportId1 !== undefined && systemGeneratedFarmerFoliageReportId1 !== null) {
        systemGeneratedFarmerFoliageReportId = systemGeneratedFarmerFoliageReportId1
      }
      if (vetGeneratedReportId1 !== undefined && vetGeneratedReportId1 !== null) {
        vetGeneratedReportId = vetGeneratedReportId1
      }

      const animalLevelNutrition = {}
      if (systemGeneratedReportId !== undefined) {
        setShowSystemGeneratedColumn(true)
        const farmerLevelSmartRationReportData = await loadSmartRationReportData(userToken, true, systemGeneratedReportId)
        if (farmerLevelSmartRationReportData.return_code !== 0) {
          ToastersService.failureToast('Could not obtain System Generated Smart Ration REport')
          return
        }
        if (farmerLevelSmartRationReportData.data && Array.isArray(farmerLevelSmartRationReportData.data) && farmerLevelSmartRationReportData.data.length > 0) {
          const farmerLevelMonthlyFeedData = setDataForFeedRecommendationCard(farmerLevelSmartRationReportData.data[0], false)
          setFarmerLevelSystemGeneratedFeedSummaryReport(farmerLevelMonthlyFeedData)
        }
        const animalLevelSmartRationReportData = await loadSmartRationReportData(userToken, false, systemGeneratedReportId)
        if (animalLevelSmartRationReportData.return_code !== 0) {
          ToastersService.failureToast('Could not obtain System Generated Smart Ration REport')
          return
        }
        console.log('animalLevelSmartRationReportData = ', animalLevelSmartRationReportData)
        const systemGeneratedReportAnimalLevelData = {}
        for (const animalLevelSmartRation of animalLevelSmartRationReportData.data) {
          animalLevelNutrition[animalLevelSmartRation['animal_id']] = animalLevelSmartRation
          const animalLevelDailyFeedData = setDataForFeedRecommendationCard(animalLevelSmartRation, true)
          systemGeneratedReportAnimalLevelData[animalLevelSmartRation['animal_id']] = animalLevelDailyFeedData
        }
        setSystemGeneratedFeedByAnimalData(systemGeneratedReportAnimalLevelData)
      }
      if (systemGeneratedFarmerFoliageReportId !== undefined) {
        if (systemGeneratedReportId === undefined) {
          setShowConstraintGeneratedColumn(true)
        }
        const farmerLevelFarmerFoliageSmartRationReportData = await loadSmartRationReportData(userToken, true, systemGeneratedFarmerFoliageReportId)
        if (farmerLevelFarmerFoliageSmartRationReportData.data && Array.isArray(farmerLevelFarmerFoliageSmartRationReportData.data) && farmerLevelFarmerFoliageSmartRationReportData.data.length > 0) {
          const farmerLevelFarmerFoliageMonthlyFeedData = setDataForFeedRecommendationCard(farmerLevelFarmerFoliageSmartRationReportData.data[0], false)
          setFarmerLevelConstraintGeneratedFeedSummaryReport(farmerLevelFarmerFoliageMonthlyFeedData)
        }
        const animalLevelFarmerFoliageSmartRationReportData = await loadSmartRationReportData(userToken, false, systemGeneratedFarmerFoliageReportId)
        console.log('animalLevelFarmerFoliageSmartRationReportData = ', animalLevelFarmerFoliageSmartRationReportData)
        const constraintGeneratedReportAnimalLevelData = {}
        for (const animalLevelSmartRation of animalLevelFarmerFoliageSmartRationReportData.data) {
          if (!animalLevelNutrition[animalLevelSmartRation['animal_id']]) {
            animalLevelNutrition[animalLevelSmartRation['animal_id']] = animalLevelSmartRation
          }
          const animalLevelDailyFeedData = setDataForFeedRecommendationCard(animalLevelSmartRation, true)
          constraintGeneratedReportAnimalLevelData[animalLevelSmartRation['animal_id']] = animalLevelDailyFeedData
        }
        setConstraintGeneratedFeedByAnimalData(constraintGeneratedReportAnimalLevelData)
      }
      if (vetGeneratedReportId !== undefined) {
        const animalLevelVetGeneratedSmartRationReportData = await loadSmartRationReportData(userToken, false, vetGeneratedReportId)
        console.log('animalLevelVetGeneratedSmartRationReportData = ', animalLevelVetGeneratedSmartRationReportData)
        const vetGeneratedReportAnimalLevelData = {}
        for (const animalLevelSmartRation of animalLevelVetGeneratedSmartRationReportData.data) {
          if (!animalLevelNutrition[animalLevelSmartRation['animal_id']]) {
            animalLevelNutrition[animalLevelSmartRation['animal_id']] = animalLevelSmartRation
          }
          const animalLevelDailyFeedData = setDataForFeedRecommendationCard(animalLevelSmartRation, true)
          vetGeneratedReportAnimalLevelData[animalLevelSmartRation['animal_id']] = animalLevelDailyFeedData
        }
        setVetGeneratedFeedByAnimalData(vetGeneratedReportAnimalLevelData)
        setForceVetGeneratedSummary(!forceVetGeneratedSummary)
      }
      const animalIds = Object.keys(animalLevelNutrition)
      const getAnimalDataReportResponse = await collateAnimalInformationForSmartRation(userToken, customerId, animalIds)
      console.log('OCFD hGSRC 1, getAnimalDataReportResponse = ', getAnimalDataReportResponse)
      if (getAnimalDataReportResponse.return_code !== 0) {
        ToastersService.failureToast('Could not generate feed')
        return
      }
      const animalInformationArray = Object.values(getAnimalDataReportResponse.animalIdToAnimalInformationNeededForSmartRationGenerationMap)
      console.log('get animal data is done')
      setDataForAnimalInfoCard(animalInformationArray, animalLevelNutrition)
      console.log('set animal data into formattable output')
      // get animal data
      // set animal data into formattable output
      //
      
      // setAnimalLevelVetGeneratedFeed(vetGeneratedFeedByAnimalDataStructure)
      // setAnimalLevelVetGeneratedFeedSelectedRows({'1211': []})
    } catch (error) {
      ToastersService.failureToast('Could not load initial data')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const referenceMap = global.references.referenceCategoryToReferencesMap
    const foliagesInUseEffect = referenceMap[10001300]
    const foliageReferenceIdUseEffectArray = foliagesInUseEffect.map(object => object['reference_id'])
    const concentratesInUseEffect = referenceMap[10013000]
    const concentrateReferenceIdUseEffectArray = concentratesInUseEffect.map(object => object['reference_id'])
    const otherNutrientsInUseEffect = referenceMap[10013005]
    const otherNutrientReferenceIdUseEffectArray = otherNutrientsInUseEffect.map(object => object['reference_id'])
    setFoliageReferenceIdArray(foliageReferenceIdUseEffectArray)
    setConcentrateReferenceIdArray(concentrateReferenceIdUseEffectArray)
    setOtherNutrientReferenceIdArray(otherNutrientReferenceIdUseEffectArray)
    const feedReferencesWithoutCheckingUsed = [...foliagesInUseEffect, ...concentratesInUseEffect, ...otherNutrientsInUseEffect]
    const feedReferencesInUseEffect = feedReferencesWithoutCheckingUsed.filter(object => {
      let returnValue = false
      if (object && object['reference_information'] && object['reference_information']['feed_properties'] && object['reference_information']['feed_properties']['used'] === 'true') {
        returnValue = true
      }
      return returnValue
    })
    setFeedReferences(feedReferencesInUseEffect)
    const feedReferencesInEnglishInUseEffect = assignL10NObjectOrObjectArrayToItSelf(feedReferencesInUseEffect, 'reference_name_l10n')
    setFeedReferencesInEnglish(feedReferencesInEnglishInUseEffect)
    setLoadInitialData(true)
  }, [])

  useEffect(() => {
    if (forceDelayVetGeneratedSummary) {
      setForceVetGeneratedSummary(!forceVetGeneratedSummary)
      setForceDelayVetGeneratedSummary(false)
    }
  }, [forceDelayVetGeneratedSummary])
  useEffect(() => {
    if (loadInitialData) {
      asyncUseEffect()
    }
  }, [loadInitialData])

  useEffect(() => {
    const feedMap = {}
    const animalIdArray = Object.keys(vetGeneratedFeedByAnimalData)
    for (const animalId of animalIdArray) {
      if (animalSelectedForReportMap[animalId] === 1000105002) {
        continue
      }
      const vetGeneratedFeedArrayForAnimal = vetGeneratedFeedByAnimalData[animalId]
      for (const vetGeneratedFeedForAnimal of vetGeneratedFeedArrayForAnimal) {
        const feedReferenceId = vetGeneratedFeedForAnimal['feed_reference_id']
        const feedQuantity = vetGeneratedFeedForAnimal['quantity_as_float']
        if (feedReferenceId !== undefined && feedReferenceId !== null && feedQuantity !== undefined && feedQuantity !== null && feedQuantity !== '' && feedQuantity !== 0) {
          const feedReference = feedReferences.filter(object => object['reference_id'] === feedReferenceId)
          const feedReferenceInformation = feedReference[0]['reference_information']
          const feedProperties = feedReferenceInformation && feedReferenceInformation['feed_properties'] !== undefined ? feedReferenceInformation['feed_properties'] : undefined
          const isOtherNutrient = (feedReferenceId !== undefined && otherNutrientReferenceIdArray.includes(feedReferenceId)) ? true : false
          if (feedMap[feedReferenceId] === undefined) {
            const monthlyFeedQuantity = feedQuantity*30
            const feedObject = {
              feed_reference_id: feedReferenceId,
              feed: vetGeneratedFeedForAnimal['feed'],
              type: vetGeneratedFeedForAnimal['type'],
              quantity_as_float: monthlyFeedQuantity,
              quantity: isOtherNutrient ? monthlyFeedQuantity.toFixed(2) : monthlyFeedQuantity.toFixed(0)
            }
            const feedCostAsFloat = getFeedComponentQuantity(feedQuantity*30, feedProperties, 'pricePkg', isOtherNutrient, true)
            const feedCost = (feedCostAsFloat === 0) ? '' : feedCostAsFloat.toFixed(0)
            feedObject['cost_without_rupee'] = feedCostAsFloat
            feedObject['cost'] = '₹ ' + feedCost
      
      
            feedMap[feedReferenceId] = feedObject
          } else {
            const feedObject = feedMap[feedReferenceId]
            const updatedFeedQuantity = feedObject['quantity_as_float'] + feedQuantity*30
            feedObject['quantity_as_float'] = updatedFeedQuantity
            if (isOtherNutrient) {
              feedObject['quantity'] = updatedFeedQuantity.toFixed(2)
            } else {
              feedObject['quantity'] = updatedFeedQuantity.toFixed(0)
            }
            const feedCostAsFloat = getFeedComponentQuantity(updatedFeedQuantity, feedProperties, 'pricePkg', isOtherNutrient, true)
            const feedCost = (feedCostAsFloat === 0) ? '' : feedCostAsFloat.toFixed(0)
            feedObject['cost_without_rupee'] = feedCostAsFloat
            feedObject['cost'] = '₹ ' + feedCost
          }
        }
      }
    }
    const farmerFeedArray = Object.values(feedMap)
    setFarmerLevelVetGeneratedFeedSummaryReport(farmerFeedArray)
  }, [forceVetGeneratedSummary, vetGeneratedFeedByAnimalData])

  const getColumn1Span = () => {
    if (showSystemGeneratedColumn && showConstraintGeneratedColumn && showVetGeneratedColumn) {
      return 2
    } else if (showSystemGeneratedColumn && showConstraintGeneratedColumn && !showVetGeneratedColumn) {
      return 4
    } else if ((showSystemGeneratedColumn && !showConstraintGeneratedColumn && showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && showConstraintGeneratedColumn && showVetGeneratedColumn)) {
      return 3
    } else if ((showSystemGeneratedColumn && !showConstraintGeneratedColumn && !showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && showConstraintGeneratedColumn && !showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && !showConstraintGeneratedColumn && showVetGeneratedColumn)) {
      return 5
    }
  }

  const getColumn2And3Span = () => {
    if (showSystemGeneratedColumn && showConstraintGeneratedColumn && showVetGeneratedColumn) {
      return 6
    } else if (showSystemGeneratedColumn && showConstraintGeneratedColumn && !showVetGeneratedColumn) {
      return 10
    } else if ((showSystemGeneratedColumn && !showConstraintGeneratedColumn && showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && showConstraintGeneratedColumn && showVetGeneratedColumn)) {
      return 9
    } else if ((showSystemGeneratedColumn && !showConstraintGeneratedColumn && !showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && showConstraintGeneratedColumn && !showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && !showConstraintGeneratedColumn && showVetGeneratedColumn)) {
      return 19
    }
  }

  const getColumn4Span = () => {
    if (showSystemGeneratedColumn && showConstraintGeneratedColumn && showVetGeneratedColumn) {
      return 10
    } else if (showSystemGeneratedColumn && showConstraintGeneratedColumn && !showVetGeneratedColumn) {
      return 10
    } else if ((showSystemGeneratedColumn && !showConstraintGeneratedColumn && showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && showConstraintGeneratedColumn && showVetGeneratedColumn)) {
      return 12
    } else if ((!showSystemGeneratedColumn && showConstraintGeneratedColumn && !showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && !showConstraintGeneratedColumn && showVetGeneratedColumn)) {
      return 19
    }
  }

  const getColumn1Column1Span = () => {
    if (showSystemGeneratedColumn && showConstraintGeneratedColumn && showVetGeneratedColumn) {
      return 10
    } else if ((showSystemGeneratedColumn && showConstraintGeneratedColumn && !showVetGeneratedColumn)
                || (showSystemGeneratedColumn && !showConstraintGeneratedColumn && showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && showConstraintGeneratedColumn && showVetGeneratedColumn)) {
      return 16
    } else if ((showSystemGeneratedColumn && !showConstraintGeneratedColumn && !showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && showConstraintGeneratedColumn && !showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && !showConstraintGeneratedColumn && showVetGeneratedColumn)) {
      return 16
    }
  }

  const getColumn1Column2Span = () => {
    if (showSystemGeneratedColumn && showConstraintGeneratedColumn && showVetGeneratedColumn) {
      return 5
    } else if ((showSystemGeneratedColumn && showConstraintGeneratedColumn && !showVetGeneratedColumn)
                || (showSystemGeneratedColumn && !showConstraintGeneratedColumn && showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && showConstraintGeneratedColumn && showVetGeneratedColumn)) {
      return 8
    } else if ((showSystemGeneratedColumn && !showConstraintGeneratedColumn && !showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && showConstraintGeneratedColumn && !showVetGeneratedColumn)
                || (!showSystemGeneratedColumn && !showConstraintGeneratedColumn && showVetGeneratedColumn)) {
      return 8
    }
  }
  
  const onHeaderCellRender = () => {
    return {
      ['style']: {paddingTop: 16, paddingBottom: 16, paddingLeft: 2, paddingRight: 2},
    }
  }

  const onCellRender = (editableRow) => {
    if (editableRow === true) {
      return {
        ['style']: {paddingTop: 4, paddingBottom: 4, paddingLeft: 2, paddingRight: 2},
      }
    } else {
      return {
        ['style']: {paddingTop: 9, paddingBottom: 9, paddingLeft: 2, paddingRight: 2},
      }
    }
  }

  const nonEditableFarmerLevelFeedTableColumns = [
    {
      title: 'Feed',
      dataIndex: 'feed',
    },
    {
      title: 'Type',
      dataIndex: 'type',
      hidden: !showType,
    },
    {
      title: 'Qnty',
      dataIndex: 'quantity',
      align: 'right',
    },
    {
      title: 'Cost',
      dataIndex: 'cost',
      align: 'right',
    },
  ]

  const nonEditableAnimalLevelFeedTableColumns = [
    {
      title: 'Feed',
      dataIndex: 'feed',
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender()},
    },
    {
      title: 'Type',
      dataIndex: 'type',
      hidden: !showType,
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender()},
    },
    {
      title: 'Qnty',
      dataIndex: 'quantity',
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender()},
      align: 'right',
    },
    {
      title: 'DM',
      dataIndex: 'dm',
      hidden: !showConstituents,
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender()},
      align: 'right',
    },
    {
      title: 'DCP',
      dataIndex: 'dcp',
      hidden: !showConstituents,
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender()},
      align: 'right',
    },
    {
      title: 'TDN',
      dataIndex: 'tdn',
      hidden: !showConstituents,
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender()},
      align: 'right',
    },
    {
      title: 'Cost',
      dataIndex: 'cost',
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender()},
      align: 'right',
    },
  ]

  const handleFeedSelectionChange = (keyValue, keyObject, value, record) => {
    try {
      setLoading(true)
      console.log('keyValue = ', keyValue)
      console.log('keyObject = ', keyObject)
      const feedReferenceId = parseInt(keyObject['key'])
      if (isNaN(feedReferenceId)) {
        return
      }
      console.log('feedReferenceId = ', feedReferenceId)
      console.log('value = ', value)
      console.log('record = ', record)

      const feedObject = feedReferences.filter(object => object['reference_id'] === feedReferenceId)
      if (feedObject.length === 0) {
        return
      }
      const feedReferenceInformation = feedObject[0]['reference_information']
      const feedProperties = feedReferenceInformation && feedReferenceInformation['feed_properties'] !== undefined ? feedReferenceInformation['feed_properties'] : undefined
      const feedType = feedProperties && feedProperties['type'] ? getTypeDisplayFormat(feedProperties['type']) : ''

      const animalId = record['animal_id']
      const feedRow = record['feed_row']
      const copiedVetGeneratedFeedByAnimalData = lodashObject.cloneDeep(vetGeneratedFeedByAnimalData)
      const animalLevelVetGeneratedFeedData = copiedVetGeneratedFeedByAnimalData[animalId]
      for (const vetGeneratedFeedRow of animalLevelVetGeneratedFeedData) {
        if (vetGeneratedFeedRow['feed_row'] !== feedRow) {
          continue
        }
        vetGeneratedFeedRow['feed_reference_id'] = feedReferenceId
        vetGeneratedFeedRow['feed'] = keyValue
        vetGeneratedFeedRow['type'] = feedType
        vetGeneratedFeedRow['quantity_as_float'] = 0
        vetGeneratedFeedRow['quantity'] = ''
        vetGeneratedFeedRow['dm'] = ''
        vetGeneratedFeedRow['dcp'] = ''
        vetGeneratedFeedRow['tdn'] = ''
        vetGeneratedFeedRow['dm_as_float'] = 0
        vetGeneratedFeedRow['dcp_as_float'] = 0
        vetGeneratedFeedRow['tdn_as_float'] = 0
        vetGeneratedFeedRow['cost_without_rupee'] = 0
        vetGeneratedFeedRow['cost'] = ''
        break
      }
      setVetGeneratedFeedByAnimalData(copiedVetGeneratedFeedByAnimalData)
    } catch (error) {
      ToastersService.failureToast('Could not manage feed selection change')
    } finally {
      setLoading(false)
    }
  }

  const handleOnQuantityBlurEvent = (event, value, record) => {
    try {
      setLoading(true)
      console.log('handleOnQuantityBlurEvent, event = ', event)
      let feedWeight = event.target.value
      feedWeight = parseFloat(feedWeight)
      if (isNaN(feedWeight)) {
        feedWeight = 0
      }
      let feedWeightInGrams = feedWeight
      let valueAsFloat = parseFloat(value)
      if (isNaN(valueAsFloat)) {
        valueAsFloat = 0
      }
      if (feedWeight.toFixed(2) === valueAsFloat.toFixed(2)) {
        return
      }
      const animalId = record['animal_id']
      const feedRow = record['feed_row']
      const feedReferenceId = record['feed_reference_id']
      const copiedVetGeneratedFeedByAnimalData = lodashObject.cloneDeep(vetGeneratedFeedByAnimalData)
      const animalLevelVetGeneratedFeedData = copiedVetGeneratedFeedByAnimalData[animalId]
      for (const vetGeneratedFeedRow of animalLevelVetGeneratedFeedData) {
        if (vetGeneratedFeedRow['feed_row'] !== feedRow) {
          continue
        }
        const isOtherNutrient = (feedReferenceId !== undefined && otherNutrientReferenceIdArray.includes(feedReferenceId)) ? true : false
        if (isOtherNutrient) {
          vetGeneratedFeedRow['is_other_nutrient'] = true
          feedWeight = feedWeight / 1000
        } else {
          feedWeightInGrams = feedWeight * 1000
        }
        vetGeneratedFeedRow['quantity_as_float'] = feedWeight
        vetGeneratedFeedRow['quantity'] = isOtherNutrient ? feedWeightInGrams.toFixed(0) : feedWeight.toFixed(2)

        const feedObject = feedReferences.filter(object => object['reference_id'] === feedReferenceId)
        const feedReferenceInformation = feedObject[0]['reference_information']
        const feedProperties = feedReferenceInformation && feedReferenceInformation['feed_properties'] !== undefined ? feedReferenceInformation['feed_properties'] : undefined
        
        const feedDMAsFloat = getFeedComponentQuantity(feedWeight, feedProperties, 'dm', isOtherNutrient)
        const feedDCPAsFloat = getFeedComponentQuantity(feedWeight, feedProperties, 'dcp', isOtherNutrient)
        const feedTDNAsFloat = getFeedComponentQuantity(feedWeight, feedProperties, 'tdn', isOtherNutrient)
        const feedCostAsFloat = getFeedComponentQuantity(feedWeight, feedProperties, 'pricePkg', isOtherNutrient, true)

        const feedDM = (feedDMAsFloat === 0) ? '' : feedDMAsFloat.toFixed(2)
        const feedDCP = (feedDCPAsFloat === 0) ? '' : feedDCPAsFloat.toFixed(2)
        const feedTDN = (feedTDNAsFloat === 0) ? '' : feedTDNAsFloat.toFixed(2)
        const feedCost = (feedCostAsFloat === 0) ? '' : isOtherNutrient ? feedCostAsFloat.toFixed(2) : feedCostAsFloat.toFixed(0)
        
        vetGeneratedFeedRow['dm_as_float'] = feedDMAsFloat
        vetGeneratedFeedRow['dcp_as_float'] = feedDCPAsFloat
        vetGeneratedFeedRow['tdn_as_float'] = feedTDNAsFloat
        vetGeneratedFeedRow['dm'] = feedDM
        vetGeneratedFeedRow['dcp'] = feedDCP
        vetGeneratedFeedRow['tdn'] = feedTDN
        vetGeneratedFeedRow['cost_without_rupee'] = feedCostAsFloat
        vetGeneratedFeedRow['cost'] = '₹ ' + feedCostAsFloat.toFixed(0)
        break
      }
      setVetGeneratedFeedByAnimalData(copiedVetGeneratedFeedByAnimalData)
    } catch (error) {
      ToastersService.failureToast('Could not manage quantity change event')
    } finally {
      setLoading(false)
    }
  }

  const handleOnDMBlurEvent = (event, value, record) => {
    try {
      setLoading(true)
      console.log('handleOnQuantityBlurEvent, event = ', event)
      let feedDMAsFloat = event.target.value
      feedDMAsFloat = parseFloat(feedDMAsFloat)
      if (isNaN(feedDMAsFloat)) {
        feedDMAsFloat = 0
      }

      let valueAsFloat = parseFloat(value)
      if (isNaN(valueAsFloat)) {
        valueAsFloat = 0
      }
      if (feedDMAsFloat.toFixed(2) === valueAsFloat.toFixed(2)) {
        return
      }
      const animalId = record['animal_id']
      const feedRow = record['feed_row']
      const feedReferenceId = record['feed_reference_id']
      const copiedVetGeneratedFeedByAnimalData = lodashObject.cloneDeep(vetGeneratedFeedByAnimalData)
      const animalLevelVetGeneratedFeedData = copiedVetGeneratedFeedByAnimalData[animalId]
      for (const vetGeneratedFeedRow of animalLevelVetGeneratedFeedData) {
        if (vetGeneratedFeedRow['feed_row'] !== feedRow) {
          continue
        }

        const feedObject = feedReferences.filter(object => object['reference_id'] === feedReferenceId)
        const feedReferenceInformation = feedObject[0]['reference_information']
        const feedProperties = feedReferenceInformation && feedReferenceInformation['feed_properties'] !== undefined ? feedReferenceInformation['feed_properties'] : undefined
        const isOtherNutrient = (feedReferenceId !== undefined && otherNutrientReferenceIdArray.includes(feedReferenceId)) ? true : false
        const feedWeightAsFloat = getQuantityBasedOnDM(feedDMAsFloat, feedProperties)
        const feedWeight = isOtherNutrient ? (feedWeightAsFloat*1000).toFixed(0) : feedWeightAsFloat.toFixed(2)
        // const feedDM = getFeedComponentQuantity(feedWeight, feedProperties, 'dm')
        const feedDCPAsFloat = getFeedComponentQuantity(feedWeightAsFloat, feedProperties, 'dcp', isOtherNutrient)
        const feedTDNAsFloat = getFeedComponentQuantity(feedWeightAsFloat, feedProperties, 'tdn', isOtherNutrient)
        const feedCostAsFloat = getFeedComponentQuantity(feedWeightAsFloat, feedProperties, 'pricePkg', isOtherNutrient, true)

        const feedDCP = (feedDCPAsFloat === 0) ? '' : feedDCPAsFloat.toFixed(2)
        const feedTDN = (feedTDNAsFloat === 0) ? '' : feedTDNAsFloat.toFixed(2)
        const feedCost = (feedCostAsFloat === 0) ? '' : isOtherNutrient ? feedCostAsFloat.toFixed(2) : feedCostAsFloat.toFixed(0)
        
        vetGeneratedFeedRow['quantity_as_float'] = feedWeightAsFloat
        vetGeneratedFeedRow['quantity'] = feedWeight
        vetGeneratedFeedRow['dm'] = feedDMAsFloat.toFixed(2)
        vetGeneratedFeedRow['dcp'] = feedDCP
        vetGeneratedFeedRow['tdn'] = feedTDN
        vetGeneratedFeedRow['dm_as_float'] = feedDMAsFloat
        vetGeneratedFeedRow['dcp_as_float'] = feedDCPAsFloat
        vetGeneratedFeedRow['tdn_as_float'] = feedTDNAsFloat
        vetGeneratedFeedRow['cost_without_rupee'] = feedCostAsFloat
        vetGeneratedFeedRow['cost'] = '₹ ' + feedCost
        // vetGeneratedFeedRow['cost'] = 0
        break
      }
      setVetGeneratedFeedByAnimalData(copiedVetGeneratedFeedByAnimalData)
    } catch (error) {
      ToastersService.failureToast('Could not manage change of DM event')
    } finally {
      setLoading(false)
    }
  }

  const vetGeneratedAnimalLevelFeedTableColumns = [
    {
      title: 'Feed',
      dataIndex: 'feed',
      render: (value, record) => {
        return (
          <Select
            showSearch
            value={value}
            style={{ width: '100%' }}
            placeholder="Select one"
            onChange={(value, object) => handleFeedSelectionChange(value, object, value, record)}
            virtual={false}
          >
            {feedReferencesInEnglish.map((foliagesOrContentrate) => (
              <Option key={foliagesOrContentrate.reference_id} value={foliagesOrContentrate.reference_name_l10n}>
                {foliagesOrContentrate.reference_name_l10n}
              </Option>
            ))}
          </Select>
        )
      },
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender(true)},
    },
    {
      title: 'Type',
      dataIndex: 'type',
      hidden: !showType,
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender(true)},
    },
    {
      title: 'Qnty',
      dataIndex: 'quantity',
      render: (value, record) => {
        if (record['feed'] === undefined) {
          return null
        } else {
          return (
            <div class='sr-va-in' style={{display:'flex'}}>
              <InputNumber
                min={0}
                // value={record['is_other_nutrient'] === true ? value*1000 : value}
                value={value}
                style={{width: '60px'}}
                onBlur={(inputValue) => handleOnQuantityBlurEvent(inputValue, value, record)}
                />
            </div>
          )
        }
      },
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender(true)},
    },
    {
      title: 'DM',
      dataIndex: 'dm',
      hidden: !showConstituents,
      render: (value, record) => {
        const feedReferenceId = record['feed_reference_id']
        const isOtherNutrient = (feedReferenceId !== undefined && otherNutrientReferenceIdArray.includes(feedReferenceId)) ? true : false
        if (record['feed'] === undefined || isOtherNutrient) {
          return null
        } else {
          return (
            <div class='sr-va-in' style={{display:'flex'}}>
              <InputNumber
                min={0}
                value={value}
                style={{width: '60px'}}
                onBlur={(inputValue) => handleOnDMBlurEvent(inputValue, value, record)}
                />
            </div>
          )
        }
      },
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender(true)},
    },
    {
      title: 'DCP',
      dataIndex: 'dcp',
      hidden: !showConstituents,
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender(true)},
    },
    {
      title: 'TDN',
      dataIndex: 'tdn',
      hidden: !showConstituents,
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender(true)},
    },
    {
      title: 'Cost',
      dataIndex: 'cost',
      align: 'right',
      onHeaderCell: (text, record) => {return onHeaderCellRender()},
      onCell: (text, record) => { return onCellRender(true)},
    },
  ]

  const handleAddFeedClick = (animalId) => {
    try {
      setLoading(true)
      const copiedVetGeneratedFeedByAnimalData = lodashObject.cloneDeep(vetGeneratedFeedByAnimalData)
      let animalLevelVetGeneratedFeedData = copiedVetGeneratedFeedByAnimalData[animalId]
      if (animalLevelVetGeneratedFeedData === undefined) {
        animalLevelVetGeneratedFeedData = []
        copiedVetGeneratedFeedByAnimalData[animalId] = animalLevelVetGeneratedFeedData
      }
      let lastCounter = 0
      if (animalLevelVetGeneratedFeedData.length > 0) {
        const lastRecord = animalLevelVetGeneratedFeedData[animalLevelVetGeneratedFeedData.length - 1]
        lastCounter = lastRecord['feed_row']
      }
      const newFeedRow = {animal_id: animalId, feed_row: lastCounter + 1}
      animalLevelVetGeneratedFeedData.push(newFeedRow)
      setVetGeneratedFeedByAnimalData(copiedVetGeneratedFeedByAnimalData)
      // setAnimalLevelVetGeneratedFeed(copiedRowsForAnimals)
    } catch (error) {
      ToastersService.failureToast('Could not manage add feed event')
    } finally {
      setLoading(false)
    }
  }

  const handleRemoveFeedClick = (animalId) => {
    try {
      setLoading(true)
      const selectedKeys = vetGeneratedFeedTableSelectedRowKeys[animalId]
      const clonedVetGeneratedFeedTableSelectedRowKeys = lodashObject.cloneDeep(vetGeneratedFeedTableSelectedRowKeys)
      console.log('selectedKeys = ', selectedKeys)

      const copiedVetGeneratedFeedByAnimalData = lodashObject.cloneDeep(vetGeneratedFeedByAnimalData)
      const animalLevelVetGeneratedFeedData = copiedVetGeneratedFeedByAnimalData[animalId]
      const updatedAnimalLevelVetGeneratedFeedData = animalLevelVetGeneratedFeedData.filter(object => {
        if (selectedKeys.includes(object['feed_row'])) {
          return false
        } else {
          return true
        }
      })
      copiedVetGeneratedFeedByAnimalData[animalId] = updatedAnimalLevelVetGeneratedFeedData
      setVetGeneratedFeedByAnimalData(copiedVetGeneratedFeedByAnimalData)
      clonedVetGeneratedFeedTableSelectedRowKeys[animalId] = []
      setVetGeneratedFeedTableSelectedRowKeys(clonedVetGeneratedFeedTableSelectedRowKeys)
    } catch (error) {
      ToastersService.failureToast('Could not manage remove feed event')
    } finally {
      setLoading(false)
    }
  }

  const handleCopyFromGeneratedFeedClick = (animalId, systemOrConstraint) => {
    try {
      setLoading(true)
      let animalFeedDataToCopy
      if (systemOrConstraint) {
        animalFeedDataToCopy = systemGeneratedFeedByAnimalData[animalId]
      } else {
        animalFeedDataToCopy = constraintGeneratedFeedByAnimalData[animalId]
      }
      if (animalFeedDataToCopy === undefined) {
        ToastersService.warningToast('No data to copy')
        return
      }
      const copiedVetGeneratedFeedByAnimalData = lodashObject.cloneDeep(vetGeneratedFeedByAnimalData)
      const copiedAnimalFeedDataToCopy = lodashObject.cloneDeep(animalFeedDataToCopy)
      copiedVetGeneratedFeedByAnimalData[animalId] = copiedAnimalFeedDataToCopy
      setVetGeneratedFeedByAnimalData(copiedVetGeneratedFeedByAnimalData)
    } catch (error) {
      ToastersService.failureToast('Could not copy from generated feed')
    } finally {
      setLoading(false)
    }
  }

  const shouldHideField = (object) => {
    if (object === undefined || object === null || object === '') {
      return true
    } else {
      return false
    }
  }

  const FarmerLevelSummaryTable = ({columns, tableReport}) => {
    return (
      <Table
        className="srfr-table"
        rowKey='feed_row'
        columns={columns}
        dataSource={tableReport}
        pagination={false}
        summary={(tableData) => {
          let totalCostWithoutRupee = 0
          for (const tableRow of tableData) {
            if (tableRow['cost_without_rupee'] !== undefined) {
              totalCostWithoutRupee = totalCostWithoutRupee + tableRow['cost_without_rupee']
            }
          }
          const totalCost = '₹ ' + totalCostWithoutRupee.toFixed(0)
          if (tableData.length === 0) {
            return null
          } else {
            return (
              <>
                <Table.Summary.Row>
                  <Table.Summary.Cell>Total</Table.Summary.Cell>
                  {!showType ? null : (
                    <Table.Summary.Cell />
                  )}
                  <Table.Summary.Cell />
                  <div style={{justifyContent: 'right', display: 'flex'}}>
                    <Table.Summary.Cell >
                      {totalCost}
                    </Table.Summary.Cell>
                  </div>
                </Table.Summary.Row>
              </>
            )
          }
        }}
      />
    )
  }

  const AnimalLevelSummaryTable = ({columns, report}) => {
    return (
      <Table
        rowKey='feed_row'
        className="fra-table"
        columns={columns}
        dataSource={report}
        pagination={false}
        summary={(tableData) => {
          // !showConstituents ? undefined : 
          let totalCostWithoutRupee = 0
          let totalDM = 0
          let totalDCP = 0
          let totalTDN = 0
          for (const tableRow of tableData) {
            if (tableRow['cost_without_rupee'] !== undefined) {
              totalCostWithoutRupee = totalCostWithoutRupee + tableRow['cost_without_rupee']
            }
            if (tableRow['dm_as_float'] !== undefined) {
              totalDM = totalDM + tableRow['dm_as_float']
            }
            if (tableRow['dcp_as_float'] !== undefined) {
              totalDCP = totalDCP + tableRow['dcp_as_float']
            }
            if (tableRow['tdn_as_float'] !== undefined) {
              totalTDN = totalTDN + tableRow['tdn_as_float']
            }
          }
          totalDM = formatFloatValue(totalDM, 2)
          totalDCP = formatFloatValue(totalDCP, 2)
          totalTDN = formatFloatValue(totalTDN, 2)
          const totalCost = '₹ ' + totalCostWithoutRupee.toFixed(0)
          if (tableData.length === 0) {
            return null
          } else {
            return (
              <>
                <Table.Summary.Row>
                  <Table.Summary.Cell>Total</Table.Summary.Cell>
                  {!showType ? null : (
                    <Table.Summary.Cell />
                  )}
                  <Table.Summary.Cell />
                  {!showConstituents ? null : (
                    <Table.Summary.Cell>
                      <div style={{width: '100%', justifyContent: 'right', display: 'inline-flex'}}>
                        <Text>{totalDM}</Text>
                      </div>
                    </Table.Summary.Cell>
                    )}
                  {!showConstituents ? null : (
                    <Table.Summary.Cell>
                      <div style={{width: '100%', justifyContent: 'right', display: 'inline-flex'}}>
                        <Text>{totalDCP}</Text>
                      </div>
                    </Table.Summary.Cell>
                  )}
                  {!showConstituents ? null : (
                    <Table.Summary.Cell>
                      <div style={{width: '100%', justifyContent: 'right', display: 'inline-flex'}}>
                        <Text>{totalTDN}</Text>
                      </div>
                    </Table.Summary.Cell>
                  )}
                  <Table.Summary.Cell>
                    <div style={{width: '100%', justifyContent: 'right', display: 'inline-flex'}}>
                      <Text>{totalCost}</Text>
                    </div>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </>
            )
          }
        }}
      />
    )
  }

  const saveCustomFeedDelayed = async () => {
    try {
      setLoading(true)
      // create delay so that customer level object gets updated    
      if (farmerLevelVetGeneratedFeedSummaryReport.length === 0) {
        ToastersService.warningToast('No proper feed information added')
        return
      }
      const animalDailyFeedsMap = {}
      for (const animalId of Object.keys(vetGeneratedFeedByAnimalData)) {
        if (animalSelectedForReportMap[animalId] === 1000105002) {
          ToastersService.warningToast('Not selected for report ' + animalId)    
          continue
        }
        let atLeastOneValidRow = false
        const animalLevelVetGeneratedFeedData = vetGeneratedFeedByAnimalData[animalId]

        const animalObject = {
          solution: {code: 0},
          smart_ration_feasible_solution: 1000105001,
          feed_generation_type:1001500003,
          foliagesPerDay: [],
          concentratesPerDay: [],
          otherNutrientsPerDay: [],
        }
        for (const animalLevelVetGeneratedFeedDataRow of animalLevelVetGeneratedFeedData) {
          if (animalLevelVetGeneratedFeedDataRow['quantity_as_float'] !== undefined && animalLevelVetGeneratedFeedDataRow['quantity_as_float'] !== null && animalLevelVetGeneratedFeedDataRow['quantity_as_float'] > 0) {
            const feedReferenceId = animalLevelVetGeneratedFeedDataRow['feed_reference_id']
            if (foliageReferenceIdArray.includes(feedReferenceId)) {
              atLeastOneValidRow = true
              animalObject.foliagesPerDay.push({reference_id: feedReferenceId, quantity: animalLevelVetGeneratedFeedDataRow['quantity_as_float']})
            }
            if (concentrateReferenceIdArray.includes(feedReferenceId)) {
              atLeastOneValidRow = true
              animalObject.concentratesPerDay.push({reference_id: feedReferenceId, quantity: animalLevelVetGeneratedFeedDataRow['quantity_as_float']})
            }
            if (otherNutrientReferenceIdArray.includes(feedReferenceId)) {
              atLeastOneValidRow = true
              animalObject.otherNutrientsPerDay.push({reference_id: feedReferenceId, quantity: animalLevelVetGeneratedFeedDataRow['quantity_as_float']})
            }
          }
        }

        if (atLeastOneValidRow) {
          animalDailyFeedsMap[animalId] = animalObject
        } else {
          ToastersService.warningToast('No data constructed for animal ' + animalId)    
        }
      }
      ToastersService.warningToast('Constructed Data To Send For Custom Report')


      const monthlyFeedsMap = {
        foliages: [],
        concentrates: [],
        otherNutrients: [],
        smart_ration_feasible_solution: 1000105001,
      }
      
      for (const farmerLevelVetGeneratedSummaryAtFeedLevel of farmerLevelVetGeneratedFeedSummaryReport) {
        const feedReferenceId = farmerLevelVetGeneratedSummaryAtFeedLevel['feed_reference_id']
        const quantity = farmerLevelVetGeneratedSummaryAtFeedLevel['quantity_as_float']
        if (foliageReferenceIdArray.includes(feedReferenceId)) {
          monthlyFeedsMap.foliages.push({reference_id: feedReferenceId, quantity: quantity})
        }
        if (concentrateReferenceIdArray.includes(feedReferenceId)) {
          monthlyFeedsMap.concentrates.push({reference_id: feedReferenceId, quantity: quantity})
        }
        if (otherNutrientReferenceIdArray.includes(feedReferenceId)) {
          monthlyFeedsMap.otherNutrients.push({reference_id: feedReferenceId, quantity: quantity})
        }
      }
      ToastersService.warningToast('Constructed Monthly Feed Map')

      const feedData = {
        customer_id: customerId,
        smartFeedsData: {
          animalDailyFeeds: animalDailyFeedsMap,
          monthlyFeeds: monthlyFeedsMap,
        },
        feed_generation_type:1001500003,
      }
      ToastersService.warningToast('Calling Save Function')
      const saveVetGeneratedSmartRationResponse = await saveVetGeneratedSmartRation(userToken, feedData)
      ToastersService.warningToast('Obtained Response on Saving')
      console.log('saveVetGeneratedSmartRationResponse = ', saveVetGeneratedSmartRationResponse)
      if (saveVetGeneratedSmartRationResponse.return_code !== 0) {
        ToastersService.failureToast('Could not save custom Smart Ration 1')
        return
      }
      const vetGeneratedReportId = saveVetGeneratedSmartRationResponse.data.monthlyFeedAssetId
      // const reportResponse = await generateSmartRationReportUploadToS3AndCreateDocument(saveVetGeneratedSmartRationResponse.data.monthlyFeedAssetId, 'Type 3', 1000260022)
      ToastersService.successToast('Successfully created the report')
      if (reportLanguage.length > 0) {
        const generatePDFReportResponse = await getSmartRationDataCreateReportUploadToS3AndCreateDocument(
          userToken, customerId, reportLanguage,
          [
            {asset_id: vetGeneratedReportId, report_type_in_file_name: 'Type 3', document_type_id: 1000260022},
          ]
        )
        console.log('generatePDFReportResponse = ', generatePDFReportResponse)
        if (generatePDFReportResponse.return_code !== 0) {
          ToastersService.failureToast('Could not create the PDF report from data in DB')
          return
        }
        ToastersService.successToast('Successfully saved report as PDF')
      }
      return
    } catch (error) {
      ToastersService.failureToast('Could not save custom Smart Ration 2')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveCustomFeedClick = async (event) => {
    setForDelayOfSaveFeedClick(1)
  }

  const createLinkUsingDocumentInformation = (linkText, documentInformation) => {
    if (documentInformation && documentInformation.url) {
      const urlLink = BASE_URL + "/media-key?url=" + documentInformation.url
      return (
        <a href={urlLink} target="_blank" rel="noopener noreferrer">
          {linkText}
        </a>
      )
    } else {
      return null
    }
  }

  useEffect(() => {
    if (forDelayOfSaveFeedClick === 1) {
      saveCustomFeedDelayed()
      setForDelayOfSaveFeedClick(-1)
      // save function
    }
  }, [forDelayOfSaveFeedClick])

  return (
    <>
      <Space>
        <Link to={-1}>Back</Link>
        <Checkbox checked={showSystemGeneratedColumn} onChange={() => {
          if (!showConstraintGeneratedColumn && !showVetGeneratedColumn && showSystemGeneratedColumn) {
            alert('At least one of the three columns should be always visible')
          } else {
            setShowSystemGeneratedColumn(!showSystemGeneratedColumn)
          }
        }}>
          Sys Generated
        </Checkbox>
        <Checkbox checked={showConstraintGeneratedColumn} onChange={() => {
          if (!showVetGeneratedColumn && !showSystemGeneratedColumn && showConstraintGeneratedColumn) {
            alert('At least one of the three columns should be always visible')
          } else {
            setShowConstraintGeneratedColumn(!showConstraintGeneratedColumn)
          }
        }}>
          Farmer Constraint
        </Checkbox>
        <Checkbox checked={showVetGeneratedColumn} onChange={() => {
          if (!showSystemGeneratedColumn && !showConstraintGeneratedColumn && showVetGeneratedColumn) {
            alert('At least one of the three columns should be always visible')
          } else {
            setShowVetGeneratedColumn(!showVetGeneratedColumn)
          }
        }}>
          Vet Generated
        </Checkbox>
        <Checkbox checked={showType} onChange={() => {
          setShowType(!showType)
        }}>
          Show Type
        </Checkbox>
        <Checkbox checked={showConstituents} onChange={() => {
          setShowConstituents(!showConstituents)
        }}>
          Show Constituents
        </Checkbox>
        <Select
          mode="multiple"
          value={reportLanguage}
          style={{
            width: 120,
          }}
          onChange={(valueOfSelectedOption) => {
            setReportLanguage(valueOfSelectedOption)
          }}
          options={[
            {
              value: 'en',
              label: 'English',
            },
            {
              value: 'mr',
              label: 'Marathi',
            },
            {
              value: 'ta',
              label: 'Tamil',
            },
            {
              value: 'te',
              label: 'Telugu',
            },
          ]}
        />
        <Button
          onClick={handleSaveCustomFeedClick}
          type="primary"
          style={{
            marginBottom: 16, marginRight: 8
          }}
        >
          Save Custom Feed
        </Button>
      </Space>
      <Spin spinning={loading} tip="Loading...">
        <Row gutter={[8, 8]} style={{ display: 'flex' }}>
          <Col span={getColumn1Span()}>
            <div style={{
                fontSize: '14px',
                
                margin: '4px',
                paddingTop: '8px',
                textAlign: 'top',
                alignItems: 'top',
                justifyContent: 'center',
                height: '100%',
                backgroundColor: 'white',
                display: 'flex'
              }}
            >
              <div>
                <div><span style={{fontWeight: 'bold',}}>Farmer Details</span></div>
                <div><span style={{fontStyle: 'italic'}}>Latest Reports</span></div>
              </div>
            </div>
          </Col>
          {!showSystemGeneratedColumn ? null : (
          <Col span={getColumn2And3Span()}>
            <div style={{
                fontSize: '14px',
                fontWeight: 'bold',
                margin: '4px',
                paddingTop: '8px',
                textAlign: 'top',
                alignItems: 'top',
                justifyContent: 'center',
                height: '100%',
                backgroundColor: 'white',
                display: 'flex',
                width: '100%',
              }}
            >
              <div style={{ width: '100%', textAlign: 'center' }}>
                <div><span>Monthly Summary</span></div>
                <div>
                  <Row>
                  <Col span={12}>{createLinkUsingDocumentInformation('Smart Ration Report (en)', systemGeneratedReportEn)}</Col>
                  <Col span={12}>{createLinkUsingDocumentInformation('Smart Ration Report (mr)', systemGeneratedReportMr)}</Col>
                  <Col span={12}>{createLinkUsingDocumentInformation('Smart Ration Report (ta)', systemGeneratedReportTa)}</Col>
                  <Col span={12}>{createLinkUsingDocumentInformation('Smart Ration Report (te)', systemGeneratedReportTe)}</Col>
                  </Row>
                </div>
              </div>
            </div>
          </Col>
          )}
          {!showConstraintGeneratedColumn ? null : (
          <Col span={getColumn2And3Span()}>
            <div style={{
                fontSize: '14px',
                fontWeight: 'bold',
                margin: '4px',
                paddingTop: '8px',
                textAlign: 'top',
                alignItems: 'top',
                justifyContent: 'center',
                height: '100%',
                backgroundColor: 'white',
                display: 'flex'
              }}
            >
              <div style={{ width: '100%', textAlign: 'center' }}>
                <div><span>Monthly Summary - With Constraints</span></div>
                <div>
                  <Row>
                    <Col span={12}>{createLinkUsingDocumentInformation('Smart Ration Report (en)', constraintGeneratedReportEn)}</Col>
                    <Col span={12}>{createLinkUsingDocumentInformation('Smart Ration Report (mr)', constraintGeneratedReportMr)}</Col>
                    <Col span={12}>{createLinkUsingDocumentInformation('Smart Ration Report (ta)', constraintGeneratedReportTa)}</Col>
                    <Col span={12}>{createLinkUsingDocumentInformation('Smart Ration Report (te)', constraintGeneratedReportTe)}</Col>
                  </Row>
                </div>
              </div>
            </div>
          </Col>
          )}
          {!showVetGeneratedColumn ? null : (
          <Col span={getColumn4Span()}>
            <div style={{
                fontSize: '14px',
                fontWeight: 'bold',
                margin: '4px',
                paddingTop: '8px',
                textAlign: 'top',
                alignItems: 'top',
                justifyContent: 'center',
                height: '100%',
                backgroundColor: 'white',
                display: 'flex'
              }}
            >
              <div style={{ width: '100%', textAlign: 'center' }}>
                <div><span>Monthly Summary - Vet Generated</span></div>
                <div>
                  <Row>
                    <Col span={12}>{createLinkUsingDocumentInformation('Smart Ration Report (en)', vetGeneratedReportEn)}</Col>
                    <Col span={12}>{createLinkUsingDocumentInformation('Smart Ration Report (mr)', vetGeneratedReportMr)}</Col>
                    <Col span={12}>{createLinkUsingDocumentInformation('Smart Ration Report (ta)', vetGeneratedReportTa)}</Col>
                    <Col span={12}>{createLinkUsingDocumentInformation('Smart Ration Report (te)', vetGeneratedReportTe)}</Col>
                  </Row></div>
              </div>
              
            </div>
          </Col>
          )}
        </Row>
        <Row gutter={[8, 8]} style={{ marginTop: '8px', display: 'flex' }}>
          <Col span={getColumn1Span()}>
            <div style={{
                fontSize: '14px',
                margin: '4px',
                padding: '8px',
                textAlign: 'top',
                alignItems: 'top',
                justifyContent: 'center',
                height: '100%',
                backgroundColor: 'white',
              }}
            >
              <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>Name:</div></Col><Col span={getColumn1Column2Span()}></Col></Row>
              <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div>{farmerCardData['name']}</div></Col><Col span={getColumn1Column2Span()}></Col></Row>
              <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>Mobile:</div></Col><Col span={getColumn1Column2Span()}></Col></Row>
              <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div>{farmerCardData['mobile']}</div></Col><Col span={getColumn1Column2Span()}></Col></Row>
              {farmerCardData['foliages'] === undefined ? null : (
                <>
                  <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>Foliages Available:</div></Col><Col span={getColumn1Column2Span()}></Col></Row>
                  <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div>{farmerCardData['foliages']}</div></Col><Col span={getColumn1Column2Span()}></Col></Row>
                </>
              )}
              {farmerCardData['concentrates'] === undefined ? null : (
                <>
                  <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>Concentrates Available:</div></Col><Col span={getColumn1Column2Span()}></Col></Row>
                  <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div>{farmerCardData['concentrates']}</div></Col><Col span={getColumn1Column2Span()}></Col></Row>
                </>
              )}
            </div>
          </Col>
          {!showSystemGeneratedColumn ? null : (
          <Col span={getColumn2And3Span()}>
            <div style={{
                fontSize: '14px',
                margin: '4px',
                padding: '4px',
                textAlign: 'top',
                alignItems: 'top',
                justifyContent: 'center',
                height: '100%',
                backgroundColor: 'white',
              }}
            >
              <FarmerLevelSummaryTable
                columns={nonEditableFarmerLevelFeedTableColumns}
                tableReport={farmerLevelSystemGeneratedFeedSummaryReport}
              />
            </div>
          </Col>
          )}
          {!showConstraintGeneratedColumn ? null : (
          <Col span={getColumn2And3Span()}>
            <div style={{
                fontSize: '14px',
                margin: '4px',
                padding: '4px',
                textAlign: 'top',
                alignItems: 'top',
                justifyContent: 'center',
                height: '100%',
                backgroundColor: 'white',
              }}
            >
              <FarmerLevelSummaryTable
                columns={nonEditableFarmerLevelFeedTableColumns}
                tableReport={farmerLevelConstraintGeneratedFeedSummaryReport}
              />
            </div>
          </Col>
          )}
          {!showVetGeneratedColumn ? null : (
          <Col span={getColumn4Span()}>
            <div style={{
                fontSize: '14px',
                margin: '4px',
                padding: '4px',
                textAlign: 'top',
                alignItems: 'top',
                justifyContent: 'center',
                height: '100%',
                backgroundColor: 'white',
              }}
            >
              <FarmerLevelSummaryTable
                columns={nonEditableFarmerLevelFeedTableColumns}
                tableReport={farmerLevelVetGeneratedFeedSummaryReport}
              />
            </div>
          </Col>
          )}
        </Row>
        <Row gutter={[8, 8]} style={{ marginTop: '16px', display: 'flex' }}>
          <Col span={getColumn1Span()}>
            <div style={{
                  fontSize: '14px',
                  fontWeight: 'bold',
                  margin: '4px',
                  paddingTop: '8px',
                  textAlign: 'top',
                  alignItems: 'top',
                  justifyContent: 'center',
                  textAlign: 'center',
                  height: '100%',
                  backgroundColor: 'white',
                  display: 'flex',
                  flexDirection: 'column',
                }}>
              Animal Details
            </div>
          </Col>
          {!showSystemGeneratedColumn ? null : (
          <Col span={getColumn2And3Span()}>
            <div style={{
                fontSize: '14px',
                fontWeight: 'bold',
                margin: '4px',
                paddingTop: '8px',
                textAlign: 'top',
                alignItems: 'top',
                justifyContent: 'center',
                height: '100%',
                backgroundColor: 'white',
                display: 'flex'
              }}
            >
              Daily Summary
            </div>
          </Col>
          )}
          {!showConstraintGeneratedColumn ? null : (
          <Col span={getColumn2And3Span()}>
            <div style={{
                fontSize: '14px',
                fontWeight: 'bold',
                margin: '4px',
                paddingTop: '8px',
                textAlign: 'top',
                alignItems: 'top',
                justifyContent: 'center',
                height: '100%',
                backgroundColor: 'white',
                display: 'flex'
              }}
            >
              Daily Summary - With Constraints
            </div>
          </Col>
          )}
          {!showVetGeneratedColumn ? null : (
          <Col span={getColumn4Span()}>
            <div style={{
                fontSize: '14px',
                fontWeight: 'bold',
                margin: '4px',
                paddingTop: '8px',
                textAlign: 'top',
                alignItems: 'top',
                justifyContent: 'center',
                height: '100%',
                backgroundColor: 'white',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <div>
                Daily Summary - Vet Generated
              </div>
              <Checkbox
                indeterminate={allAnimalIndeterminateState}
                onChange={onChangeCheckAllAnimalsForReport}
                checked={allAnimalCheckboxState}
              >
              </Checkbox>
            </div>
          </Col>
          )}
        </Row>
        {animalCardData.map((animalCard, index) => {
          console.log('Animal Card, animalCard = ', animalCard)
          const animalId = animalCard['animal_id']
          const systemGeneratedAnimalFeed = systemGeneratedFeedByAnimalData[animalId]
          // const checkBoxCheckedState = (animalSelectedForReportMap[animalId] === 1000105001)
          const constraintGeneratedAnimalFeed = constraintGeneratedFeedByAnimalData[animalId]
          const vetGeneratedAnimalFeed = vetGeneratedFeedByAnimalData[animalId]
          return (
            <Row gutter={[8, 8]} style={{ marginTop: '8px', display: 'flex' }}>
              <Col span={getColumn1Span()}>
                <div style={{
                    fontSize: '14px',
                    margin: '4px',
                    padding: '8px',
                    textAlign: 'top',
                    alignItems: 'top',
                    justifyContent: 'center',
                    height: '100%',
                    backgroundColor: 'white',
                  }}
                >
                  {/* <Checkbox checked={checkBoxCheckedState} onChange={() => updateAllAnimalsCheckboxState(index, animalId)}>
                  </Checkbox> */}
                  {shouldHideField(animalCard['ear_tag']) ? null : (
                    <>
                      <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>Ear Tag:</div></Col><Col span={getColumn1Column2Span()}></Col></Row>
                      <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div>{animalCard['ear_tag']}</div></Col><Col span={getColumn1Column2Span()}></Col></Row>
                    </>
                  )}
                  {shouldHideField(animalCard['weight']) ? null : (
                    <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>Weight (kg):</div></Col><Col span={getColumn1Column2Span()}>{animalCard['weight']}</Col></Row>
                  )}
                  {shouldHideField(animalCard['body_score']) ? null : (
                    <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>Body Score:</div></Col><Col span={getColumn1Column2Span()}>{animalCard['body_score']}</Col></Row>
                  )}
                  {shouldHideField(animalCard['milk_fat_percentage']) ? null : (
                    <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>Milk Fat %:</div></Col><Col span={getColumn1Column2Span()}>{animalCard['milk_fat_percentage']}</Col></Row>
                  )}
                  {shouldHideField(animalCard['months_pregnant']) ? null : (
                    <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>Months Pregnant:</div></Col><Col span={getColumn1Column2Span()}>{animalCard['months_pregnant']}</Col></Row>
                  )}
                  {shouldHideField(animalCard['days_since_calving']) ? null : (
                    <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>Days Since Calving:</div></Col><Col span={getColumn1Column2Span()}>{animalCard['days_since_calving']}</Col></Row>
                  )}
                  {shouldHideField(animalCard['litres_per_day']) ? null : (
                    <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>Litres / day:</div></Col><Col span={getColumn1Column2Span()}>{animalCard['litres_per_day']}</Col></Row>
                  )}
                  {shouldHideField(animalCard['projected_litres_per_day']) ? null : (
                    <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>Projected Litres / day:</div></Col><Col span={getColumn1Column2Span()}>{animalCard['projected_litres_per_day']}</Col></Row>
                  )}
                  {shouldHideField(animalCard['dm_max']) ? null : (
                    <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>DM Max</div></Col><Col span={getColumn1Column2Span()}>{animalCard['dm_max']}</Col></Row>
                  )}
                  {shouldHideField(animalCard['dm_min']) ? null : (
                    <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>DM Min</div></Col><Col span={getColumn1Column2Span()}>{animalCard['dm_min']}</Col></Row>
                  )}
                  {shouldHideField(animalCard['dcp']) ? null : (
                    <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>DCP</div></Col><Col span={getColumn1Column2Span()}>{animalCard['dcp']}</Col></Row>
                  )}
                  {shouldHideField(animalCard['tdn']) ? null : (
                    <Row gutter={[16, 16]}><Col span={getColumn1Column1Span()}><div style={{fontWeight: 'bold'}}>TDN</div></Col><Col span={getColumn1Column2Span()}>{animalCard['tdn']}</Col></Row>
                  )}
                </div>
              </Col>
              {!showSystemGeneratedColumn ? null : (
              <Col span={getColumn2And3Span()}>
                <div style={{
                    fontSize: '14px',
                    margin: '4px',
                    padding: '4px',
                    textAlign: 'top',
                    alignItems: 'top',
                    justifyContent: 'center',
                    height: '100%',
                    backgroundColor: 'white',
                  }}
                >
                  <AnimalLevelSummaryTable
                    columns={nonEditableAnimalLevelFeedTableColumns}
                    report={systemGeneratedAnimalFeed}
                  />
                </div>
              </Col>
              )}
              {!showConstraintGeneratedColumn ? null : (
              <Col span={getColumn2And3Span()}>
                <div style={{
                    fontSize: '14px',
                    margin: '4px',
                    padding: '4px',
                    textAlign: 'top',
                    alignItems: 'top',
                    justifyContent: 'center',
                    height: '100%',
                    backgroundColor: 'white',
                  }}
                >
                  <AnimalLevelSummaryTable
                    columns={nonEditableAnimalLevelFeedTableColumns}
                    report={constraintGeneratedAnimalFeed}
                  />
                </div>
              </Col>
              )}
              {!showVetGeneratedColumn ? null : (
              <Col span={getColumn4Span()}>
                <div style={{
                    fontSize: '14px',
                    margin: '4px',
                    padding: '4px',
                    textAlign: 'top',
                    alignItems: 'top',
                    justifyContent: 'center',
                    height: '100%',
                    backgroundColor: 'white',
                  }}
                  class='vafr-table'
                >
                  <Table
                    rowKey='feed_row'
                    columns={vetGeneratedAnimalLevelFeedTableColumns}
                    dataSource={vetGeneratedAnimalFeed}
                    pagination={false}
                    rowSelection={{
                      onHeaderCell: (text, record) => {
                        // console.log('onCellCalled')
                        return {
                          ['style']: {paddingTop: 2, paddingBottom: 2, paddingLeft: 2, paddingRight: 2},
                        };
                      },
                      onCell: (text, record) => {
                        // console.log('onCellCalled')
                        return {
                          ['style']: {paddingTop: 2, paddingBottom: 2, paddingLeft: 2, paddingRight: 2},
                        };
                      },
                      // columnWidth: '16px',
                      selectedRowKeys: vetGeneratedFeedTableSelectedRowKeys[animalId],
                      onChange: (selectedKeys) => {
                        const clonedVetGeneratedFeedTableSelectedRowKeys = lodashObject.cloneDeep(vetGeneratedFeedTableSelectedRowKeys)
                        clonedVetGeneratedFeedTableSelectedRowKeys[animalId] = selectedKeys
                        setVetGeneratedFeedTableSelectedRowKeys(clonedVetGeneratedFeedTableSelectedRowKeys)
                      },
                      // You can customize the selection behavior, such as disallowing some rows to be selected
                      getCheckboxProps: (record) => ({
                        // Example: Disable selection for rows with a specific condition
                        disabled: record.someCondition === true,
                        // style: {paddingTop: 2, paddingBottom: 2, paddingLeft: 2, paddingRight: 2},
                      }),
                    }}
                    summary={(tableData) => {
                      // !showConstituents ? undefined : 
                      let totalCostWithoutRupee = 0
                      let totalDM = 0
                      let totalDCP = 0
                      let totalTDN = 0
                      for (const tableRow of tableData) {
                        if (tableRow['cost_without_rupee'] !== undefined) {
                          totalCostWithoutRupee = totalCostWithoutRupee + tableRow['cost_without_rupee']
                        }
                        if (tableRow['dm_as_float'] !== undefined) {
                          totalDM = totalDM + tableRow['dm_as_float']
                        }
                        if (tableRow['dcp_as_float'] !== undefined) {
                          totalDCP = totalDCP + tableRow['dcp_as_float']
                        }
                        if (tableRow['tdn_as_float'] !== undefined) {
                          totalTDN = totalTDN + tableRow['tdn_as_float']
                        }
                      }
                      totalDM = formatFloatValue(totalDM, 2)
                      totalDCP = formatFloatValue(totalDCP, 2)
                      totalTDN = formatFloatValue(totalTDN, 2)
                      const totalCost = '₹ ' + totalCostWithoutRupee.toFixed(0)
                      if (tableData.length === 0) {
                        return null
                      } else {
                        return (
                          <>
                          <Table.Summary.Row>
                            <Table.Summary.Cell />
                            <Table.Summary.Cell>Total</Table.Summary.Cell>
                            {!showType ? null : (
                              <Table.Summary.Cell />
                            )}
                            <Table.Summary.Cell />
                            {!showConstituents ? null : (
                              <Table.Summary.Cell>
                                <div style={{width: '100%', justifyContent: 'right', display: 'inline-flex'}}>
                                  <Text>{totalDM}</Text>
                                </div>
                              </Table.Summary.Cell>
                              )}
                            {!showConstituents ? null : (
                              <Table.Summary.Cell>
                                <div style={{width: '100%', justifyContent: 'right', display: 'inline-flex'}}>
                                  <Text>{totalDCP}</Text>
                                </div>
                              </Table.Summary.Cell>
                            )}
                            {!showConstituents ? null : (
                              <Table.Summary.Cell>
                                <div style={{width: '100%', justifyContent: 'right', display: 'inline-flex'}}>
                                  <Text>{totalTDN}</Text>
                                </div>
                              </Table.Summary.Cell>
                            )}
                            <Table.Summary.Cell>
                            <div style={{width: '100%', justifyContent: 'right', display: 'inline-flex'}}>
                              <Text>{totalCost}</Text>
                              </div>
                            </Table.Summary.Cell>
                          </Table.Summary.Row>
                        </>
                        )
                      }
                    }}
                  />
                  <div style={{display: 'flex'}}>
                    <Button
                      onClick={() => handleAddFeedClick(animalId)}
                      type="primary"
                      style={{
                        marginBottom: 16, marginRight: 8
                      }}
                    >
                      Add Feed
                    </Button>
                    <Button
                      onClick={() => handleRemoveFeedClick(animalId)}
                      type="primary"
                      style={{
                        marginBottom: 16, marginRight: 8
                      }}
                    >
                      Delete
                    </Button>
                    <Button
                      onClick={() => handleCopyFromGeneratedFeedClick(animalId, true)}
                      type="primary"
                      style={{
                        marginBottom: 16, marginRight: 8
                      }}
                    >
                      Copy Sys Gen
                    </Button>
                    <Button
                      onClick={() => handleCopyFromGeneratedFeedClick(animalId, false)}
                      type="primary"
                      style={{
                        marginBottom: 16,
                      }}
                    >
                      Copy Constraint Gen
                    </Button>
                    <Checkbox style={{marginLeft: 16}} checked={(animalSelectedForReportMap[animalId] === 1000105001)} onChange={() => updateAllAnimalsCheckboxState(index, animalId)}>
                      Select For Report
                    </Checkbox>
                  </div>
                  
                </div>
              </Col>
              )}
            </Row>
          )
        })}
      </Spin>
    </>
  );
}

// export default SmartRationCustomers
export default withAuthorization(CustomerSmartRation, [CS_TEAM, CENTRAL_VET])