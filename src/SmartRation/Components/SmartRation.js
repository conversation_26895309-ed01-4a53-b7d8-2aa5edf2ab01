import { Link } from 'react-router-dom';

import { Card, Row, Col } from 'antd'

const {EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const SmartRation = () => {
  return (
    <>
    <Row>
      <Col span={3}>
        <Link to="smart-ration-customers-added-as-freelance-paravet-customers">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Smart Ration Customers Added As Freelance Paravet Farmers
          </Card>
        </Link>
      </Col>
      <Col span={3}>
        <Link to="smart-ration-customers">
          <Card style={{
              fontSize: '20px',
              margin: '10px',
              textAlign: 'center'
            }}
          >
            Smart Ration Customers
          </Card>
        </Link>
      </Col>
    </Row>
  </>
  )
}

// export default SmartRation
export default withAuthorization(SmartRation, [CS_TEAM, CENTRAL_VET])