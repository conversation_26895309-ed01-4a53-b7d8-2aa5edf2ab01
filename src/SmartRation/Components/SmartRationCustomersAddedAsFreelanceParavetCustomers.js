import { Check<PERSON>, Space, Button } from "antd"
import { ReloadOutlined } from '@ant-design/icons';

import { useState, useRef } from "react"
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import {useSelector} from "react-redux"

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const { getFarmerDataReport, /* getFarmersWithLocationRecordedReport,  */getFarmerDataReportsPageLevelFilterData/* , getFarmersWithLocationRecordedReportFilterData */ } = require('../../router')

const { delayInMillis, createPageParamsFromURLString, createURLQueryStringFromPageParams, OCTable } = require('../../Components/Common/AntTableHelper')

const loadReport = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Smart Ration Customers',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // await delayInMillis(10000)
    // const response = await getFarmersWithLocationRecordedReport(headers, queryParams)
    const response = await getFarmerDataReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadFilters = async (userToken, selectedColumns, additionalParams) => {
  try {
    const headers = {
      useCase: 'Get Farmers With Not Location Recorded Report Filter Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getFarmerDataReportsPageLevelFilterData(headers, {selectedColumns: selectedColumns, searchConfiguration: tableConfiguration})
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const tableConfiguration = {
  tableNavigationUrl: '/sr/smart-ration-customers-added-as-freelance-paravet-customers',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: true,
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'customer_id',
  columns: [
    {
      title: "Farmer Id",
      key: "customer_id",
    },
    {
      title: "Farmer Name",
      key: "customer_name",
    },
    {
      title: "Farmer Mobile Number",
      key: "mobile_number",
    },
    {
      title: "No. of Active Animals",
      key: "active_animal_count",
    },
    {
      title: "Taluk",
      key: "taluk",
    },
    {
      title: "Village",
      key: "village",
    },
  ],
  columnConfiguration: {
    customer_name: {
      headingText: 'Customer Name',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    mobile_number: {
      headingText: 'Mobile Number',
      enableFilter: true,
      type: 'string',
      includeInOmnisearch: true
    },
    active_animal_count: {
      headingText: 'Number of Adult Animals',
      enableFilter: true,
      type: 'number_range',
      // defaultFilteredValue: [[1,5]],
    },
    taluk: {
      headingText: 'Taluk',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      altColumn: 'taluk_id',
      updateFilterDataDuringFilterCall: true,
    },
    village: {
      headingText: 'Village',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      updateFilterDataDuringReportCall: true,
      // defaultFilteredValue: ['Shelgaon'],
    },
  }
}

const SmartRationCustomersAddedAsFreelanceParavetCustomers = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const tableRef = useRef()

  const [excludeBDMInQuery, setExcludeBDMInQuery] = useState(true)

  const userToken = useSelector((state)=>state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    tableConfiguration.enableTableExport = true
    tableConfiguration.enableTableExportFromCloud = true
  }

  const [reportData, setReportData] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const loadInitialFilterDataCB = (additionalParams) => {
    const selectedColumns = tableRef.current.getSelectedColumnsInTable()
    return loadFilters(userToken, selectedColumns, additionalParams)
  }

  const loadReportCB = (reportQueryParams, additionalParams) => {
    reportQueryParams.customerTypeIds = [1000220007]
    return loadReport(userToken, reportQueryParams, additionalParams)
  }

  const handleViewFarmerClick = (event) => {
    const customerId = selectedRowKeys[0]
    const queryParams = {
      cId: customerId,
    }; // Example query parameters
    const paramsAsString = JSON.stringify(queryParams)
    const encodedParamsAsString = encodeURIComponent(paramsAsString)
    const navigationLink = `/e/farmer-details/${customerId}`
    if (event.ctrlKey || event.metaKey) {
      window.open(navigationLink + '?role=sr', '_blank')
    } else {
      navigate({
        pathname: navigationLink,
        // search: `?qP=${encodedParamsAsString}`
        search: 'role=sr'
      })
    }
  }

  return (
    <>
      <Space>
        <Link to="/sr">Back to Queue List</Link>
        <Button size="small" style={{ marginLeft: 8 }} disabled={(selectedRowKeys.length === 0)} onClick={handleViewFarmerClick}>View Farmer Details</Button>
      </Space>
      <OCTable
        ref={tableRef}
        /* tableKey={tableKey}
        setTableKey={setTableKey} */
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={tableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        // postInitialLoadCallback={postInitialLoadCB}
        loadReportCallback={loadReportCB}
        loadInitialFilterDataCallback={loadInitialFilterDataCB}
        // parentPageParams={pageParams}
        // showTableHeader={true}
        // omniSearchRowEnabled={true}
        // omniSearchRowBeginning={backToQueueListLinkUI}
        // omniSearchRowMiddle={viewRelatedAnimalButtons}
        // actionsRow={backToQueueListLinkUIAndViewRelatedAnimalButtons}
      />
    </>
  );
}

// export default SmartRationCustomers
export default withAuthorization(SmartRationCustomersAddedAsFreelanceParavetCustomers, [CS_TEAM, CENTRAL_VET])