import {
  Routes,
  Route,
} from "react-router-dom";

import Sidebar from '../Components/Sidebar/Sidebar'

import SmartRation from './Components/SmartRation'
import SmartRationCustomers from './Components/SmartRationCustomers'
import CustomerSmartRation from './Components/CustomerSmartRation'
import SmartRationCustomersAddedAsFreelanceParavetCustomers from './Components/SmartRationCustomersAddedAsFreelanceParavetCustomers'

const SmartRationRoutes = () => {
  return (
    <Routes>
      <Route path="sr" element={<Sidebar content={<SmartRation />} />} />
      <Route path="sr/smart-ration-customers-added-as-freelance-paravet-customers" element={<Sidebar content={<SmartRationCustomersAddedAsFreelanceParavetCustomers />} />} />
      <Route path="sr/smart-ration-customers" element={<Sidebar content={<SmartRationCustomers />} />} />
      <Route path="sr/customer-smart-ration/:customerId" element={<Sidebar content={<CustomerSmartRation />} />} />
    </Routes>
  )
}

export default SmartRationRoutes