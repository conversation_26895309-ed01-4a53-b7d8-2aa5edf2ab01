// const {axios} = require("axios")
import axios from "axios";
import { updateUserToken2 } from './Components/user/action';

import { BASE_URL, instance } from "./Services/api.service";
import { useSelector, useDispatch } from "react-redux";

const { /* get,  */put, /* post,  */configurationJSON, KrushalError } = require('@krushal-it/common-core')
const { post, get } = require('./routerhelper')
const { getSellableAnimalsReportOld,
  getFarmersWithNoLocationRecordedReport, getFarmersWithNoLocationRecordedReportFilterData,
  getFarmersWithLocationRecordedReport, getFarmersWithLocationRecordedReportFilterData,
  getDataForPotentiallySellableAnimalsReport, getDataForPotentiallySellableAnimalsReportFilterData,
  getPotentiallySellableAnimalsReport, getPotentiallySellableAnimalsReportFilterData,
  snoozeForSellableAnimals, unsnoozeSellableAnimals, updateSellableAnimalStatus, markAnimalsAsSellable,
  getSellableAnimalsReport, getSellableAnimalsReportFilterData,
  getItemDetailByItemListingId, getPotentialBuyerFarmersForAnimalReport, getPotentialBuyerFarmersForAnimalReportFilterData,
  snoozeForBuyerFarmers, updateBuyerFarmerStatus,
  getFarmersWhoMayWantToBuyAnimalsReport, getFarmersWhoMayWantToBuyAnimalsReportFilterData,
  createOrderForAnimalSale, getAnimalExchangeOrdersReport, getAnimalExchangeOrdersReportFilterData,
  updateOrderFarmerStatus, getDocuments, uploadFileToS3, saveDocuments,
  getComments,
  getManureOCFarmersReport, getManureOCFarmersReportReportFilterData, snoozeManureOCFarmers,
  getDocumentDetails, getFarmersReport, getFarmersFilterData,
  loadReferencesForReferenceCategories, calculateHealthScore, generatePriceForAnimal } = require('./OpenCommerce/ocrouter')

const {
  getHealthScoreForOnboardedAnimalsReport, getHealthScoreForOnboardedAnimalsReportFilterData,
  snoozeHealthScoreAnimals, updateStatusOfHealthScoreAnimals,
  getMedicineReport, getMedicineReportFilterData
} = require('./AHVet/ahvetrouter')

const {
  getFarmerDataReport, getFarmerDataReportsPageLevelFilterData, getAnimalDataReport, getAnimalDataReportsPageLevelFilterData,
  getAnimalsForFarmer,
  getFarmerBasicData, getCustomerInformationBasedOnClassifiers, updateCustomerInformationBasedOnClassifiers,
  getAnimalBasicData, getAnimalInformationBasedOnClassifiers, updateAnimalInformationBasedOnClassifiers,
  createTaskForCustomerService, getCropDetailedData, saveCropDetailedData,
  getMedicineData, saveMedicineData,
  getPartnerReport, getPartnerReportFilterData,
  getPartnerBasicData, getPartnerDetails, setPartnerDetails,
  getStaffReport, getStaffReportFilterData, getStaffBasicData, getStaffDetails, setStaffDetails,
  getTerritoryAssignmentReport, deleteStaffTerritories, assignTerritoriesToStaff,
} = require('./Entities/entityrouter')
const {
  getCustomerAndAnimalDetailsForSmartRation, getAnimalFeedDetailsForCustomerAnimals, generateSmartRationReportsAndUploadToS3, createDocument, upsertDocumentByLanguage, getSmartFeedData, saveSmartFeedData, getSmartRationReport,
  getPerCustomerLatestSmartRationReport, setReportAttributes,
} = require('./SmartRation/smartrationrouter')

const {
  getCustomerServiceTasksReport, getCustomerServiceTasksReportFilterData, snoozeCustomerServiceAnimals,
  updateCustomerServiceTaskStatus
} = require('./CustomerService/csrouter')

const {
  getStateList, getStateListV2, getDistrictList, getDistrictListV2, getTalukList, getTalukListV2, getVillageList, getVillageList2, getGeographyListing, getGeoJSONs, getGeographyDataForVillage, getGeographyDataForVillage2, getGeographyData,
} = require('./Entities/georouter')


const getComplaintReport = async (headers, searchAndFilterData) => {
  try {
    console.log('r gCR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/report/complaint-report`
    console.log('r gCR 2, url = ', url)
    const response = await post(url, headers, searchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gCR 10, error')
    console.log('r gCR 10a, error = ', error)
    throw error
  }
}

const getRegistrationReport = async (headers, searchAndFilterData) => {
  try {
    console.log('r gCR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/report/registration-report`
    console.log('r gCR 2, url = ', url)
    const response = await post(url, headers, searchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gCR 10, error')
    console.log('r gCR 10a, error = ', error)
    throw error
  }
}

const getDairyFarmLocationReport = async (headers, searchAndFilterData) => {
  try {
    console.log('r gCR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/get-all-customer-locations`
    console.log('r gCR 2, url = ', url)
    const response = await post(url, headers, searchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gCR 10, error')
    console.log('r gCR 10a, error = ', error)
    throw error
  }
}

const getStaffKMReport = async (headers, searchAndFilterData) => {
  try {
    console.log('r gCR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/bgLocation/staff-km-data`
    console.log('r gCR 2, url = ', url)
    const response = await post(url, headers, searchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gCR 10, error')
    console.log('r gCR 10a, error = ', error)
    throw error
  }
}

const getAllStaffDailyKmReport = async (headers, searchAndQueryParams) => {
  try {
    console.log('r gFWLRRFD 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-with-location-recorded`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + '/bgLocation/staff-daily-km-report'
    console.log('r gFWLRRFD  2, url = ', url)
    const response = await post(url, headers, searchAndQueryParams)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRRFD 10, error')
    console.log('r gFWLRRFD 10a, error = ', error)
    throw error
  }
}

const getAllStaffMonthlyKmReport = async (headers, searchAndQueryParams) => {
  try {
    console.log('r gFWLRRFD 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-with-location-recorded`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + '/bgLocation/staff-monthly-km-report'
    console.log('r gFWLRRFD  2, url = ', url)
    const response = await post(url, headers, searchAndQueryParams)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRRFD 10, error')
    console.log('r gFWLRRFD 10a, error = ', error)
    throw error
  }
}

const upsertApprovedStaffKM = async (headers, upsertData) => {
  try {
    console.log('r gFWLRRFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + '/bgLocation/staff-save-km-report'
    console.log('r gFWLRRFD  2, url = ', url)
    const response = await post(url, headers, upsertData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRRFD 10, error')
    console.log('r gFWLRRFD 10a, error = ', error)
    throw error
  }
}

const upsertApprovedStaffTravelDay = async (headers, upsertData) => {
  try {
    console.log('r gFWLRRFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + '/bgLocation/staff-save-km-approval-report'
    console.log('r gFWLRRFD  2, url = ', url)
    const response = await post(url, headers, upsertData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRRFD 10, error')
    console.log('r gFWLRRFD 10a, error = ', error)
    throw error
  }
}

const checkIfUserExistsInDBAndGetRelatedData = async (headers, userInformation) => {
  try {
    console.log('r gCR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/get-user-app-groups`
    console.log('r gCR 2, url = ', url)
    const response = await post(url, headers, userInformation)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gCR 10, error')
    console.log('r gCR 10a, error = ', error)
    throw error
  }
}

// module.exports = {getComplaintReport, getRegistrationReport, getSellableAnimalsReport}
export {
  post, get,

  getComplaintReport, getRegistrationReport, getDairyFarmLocationReport, getStaffKMReport,
  getAllStaffDailyKmReport, getAllStaffMonthlyKmReport, upsertApprovedStaffKM, upsertApprovedStaffTravelDay,

  checkIfUserExistsInDBAndGetRelatedData,

  getSellableAnimalsReportOld,
  getFarmersWithNoLocationRecordedReport, getFarmersWithNoLocationRecordedReportFilterData,
  getFarmersWithLocationRecordedReport, getFarmersWithLocationRecordedReportFilterData,
  getDataForPotentiallySellableAnimalsReport, getDataForPotentiallySellableAnimalsReportFilterData,
  getPotentiallySellableAnimalsReport, getPotentiallySellableAnimalsReportFilterData,
  snoozeForSellableAnimals, unsnoozeSellableAnimals, updateSellableAnimalStatus, markAnimalsAsSellable,
  getSellableAnimalsReport, getSellableAnimalsReportFilterData,
  getItemDetailByItemListingId, getPotentialBuyerFarmersForAnimalReport, getPotentialBuyerFarmersForAnimalReportFilterData,
  snoozeForBuyerFarmers, updateBuyerFarmerStatus,
  getFarmersWhoMayWantToBuyAnimalsReport, getFarmersWhoMayWantToBuyAnimalsReportFilterData,
  createOrderForAnimalSale, getAnimalExchangeOrdersReport, getAnimalExchangeOrdersReportFilterData,
  updateOrderFarmerStatus, getDocuments, uploadFileToS3, saveDocuments,
  getComments,
  getManureOCFarmersReport, getManureOCFarmersReportReportFilterData, snoozeManureOCFarmers,
  getCustomerInformationBasedOnClassifiers, updateCustomerInformationBasedOnClassifiers,

  getFarmerDataReport, getFarmerDataReportsPageLevelFilterData, getAnimalDataReport, getAnimalDataReportsPageLevelFilterData, getAnimalsForFarmer,
  getFarmerBasicData, getDocumentDetails, getAnimalBasicData, getAnimalInformationBasedOnClassifiers, updateAnimalInformationBasedOnClassifiers, getFarmersReport, getFarmersFilterData,
  loadReferencesForReferenceCategories, calculateHealthScore, generatePriceForAnimal, createTaskForCustomerService,
  getCropDetailedData, saveCropDetailedData,
  getMedicineData, saveMedicineData,
  getPartnerReport, getPartnerReportFilterData,
  getPartnerBasicData, getPartnerDetails, setPartnerDetails,
  getStaffReport, getStaffReportFilterData, getStaffBasicData, getStaffDetails, setStaffDetails,
  getTerritoryAssignmentReport, deleteStaffTerritories, assignTerritoriesToStaff,


  getHealthScoreForOnboardedAnimalsReport, getHealthScoreForOnboardedAnimalsReportFilterData,
  snoozeHealthScoreAnimals, updateStatusOfHealthScoreAnimals,
  getMedicineReport, getMedicineReportFilterData,

  getCustomerAndAnimalDetailsForSmartRation, getAnimalFeedDetailsForCustomerAnimals, generateSmartRationReportsAndUploadToS3, createDocument, upsertDocumentByLanguage, getSmartFeedData, saveSmartFeedData, getSmartRationReport,
  getPerCustomerLatestSmartRationReport, setReportAttributes,
  getCustomerServiceTasksReport, getCustomerServiceTasksReportFilterData, snoozeCustomerServiceAnimals,
  updateCustomerServiceTaskStatus,

  getStateList, getStateListV2, getDistrictList, getDistrictListV2, getTalukList, getTalukListV2, getVillageList, getVillageList2, getGeographyListing, getGeoJSONs, getGeographyDataForVillage, getGeographyDataForVillage2, getGeographyData,
}

//   getCustomerAndAnimalDetailsForSmartRation, getAnimalFeedDetailsForCustomerAnimals, generateSmartRationReportAndUploadToS3, createDocument, getSmartFeedData, saveSmartFeedData, getSmartRationReport,
// }


// // const {axios} = require("axios")
// import axios from "axios";
// import {updateUserToken2} from './Components/user/action';
// const { /* get,  */put, /* post,  */configurationJSON, KrushalError } = require('@krushal-it/common-core')

// const post = async (url, passedHeaders, data) => {
//   let instanceParameters
//   try {
//     const headers = {
//       "X-App-Id": configurationJSON().KRUSHAL_APP_ID,
//       'content-type': "application/json",
//       correlationId: undefined,
//       partnerId: undefined,
//       userDeviceId: '-1',
//       ...passedHeaders
//     }
//     console.log('SA lR 3, headers = ', headers)
//     const instance = axios.create()
//     instanceParameters = {
//       url,
//       method: "POST",
//       data,
//       headers
//     }
//     const response = await instance(instanceParameters)
//     return response
//   } catch (error) {
//     console.log('r p 30, caught error')
//     if (error.response && error.response.data && error.response.data.name === 'KrushalError' && error.response.data.return_code === -1001 && error.response.data.additionalMessage.newAccessCode) {
//       const newToken = String(error.response.data.additionalMessage.newAccessCode)
//       // localStorage.setItem('accessToken', newToken)
//       updateUserToken2(newToken, instanceParameters.headers.refreshtoken)
//       console.log('r p 31, is token expiry error, new token = ', newToken)
//       try {
//         const instance = axios.create()
//         instanceParameters.headers.token = newToken
//         const response1 = await instance(instanceParameters);
//         console.log('r p 33, made another call successful')
//         return response1
//       } catch (error1) {
//         console.log('r p 20, error1')
//         console.log('r p 20a, error1 = ', error1)
//         throw error1
//       }
//     } else {
//       console.log('r p 10, error')
//       console.log('r p 10a, error = ', error)
//       throw error
//     }
//   }
// }

// const get = async (url, passedHeaders) => {
//   let instanceParameters
//   try {
//     const headers = {
//       "X-App-Id": configurationJSON().KRUSHAL_APP_ID,
//       "Content-Type": "application/json",
//       correlationId: undefined,
//       partnerId: undefined,
//       userDeviceId: '-1',
//       ...passedHeaders
//     }
//     console.log('r g 1, headers = ', headers)
//     const instance = axios.create()
//     instanceParameters = {
//       url,
//       method: "GET",
//       headers
//     }
//     const response = await instance(instanceParameters);
//     return response
//   } catch (error) {
//     console.log('r g 30, caught error')
//     if (error.response && error.response.data && error.response.data.name === 'KrushalError' && error.response.data.return_code === -1001 && error.response.data.additionalMessage.newAccessCode) {
//       const newToken = String(error.response.data.additionalMessage.newAccessCode)
//       console.log('r p 31, is token expiry error, new token = ', newToken)
//       try {
//         instanceParameters.headers.token = newToken
//         // localStorage.setItem('accessToken', newToken)
//         updateUserToken2(newToken, instanceParameters.headers.refreshtoken)
//         console.log('r g 32, making another call')
//         const instance = axios.create()
//         const response1 = await instance(instanceParameters);
//         console.log('r g 33, made another call successful')
//         return response1
//       } catch (error1) {
//         console.log('r g 20, error1')
//         console.log('r g 20a, error1 = ', error1)
//         throw error1
//       }
//     } else {
//       console.log('r g 10, error')
//       console.log('r g 10a, error = ', error)
//       throw error
//     }
//   }
// }

// const getComplaintReport = async (headers, searchAndFilterData) => {
//   try {
//     console.log('r gCR 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/report/complaint-report`
//     console.log('r gCR 2, url = ', url)
//     const response = await post(url, headers, searchAndFilterData)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gCR 10, error')
//     console.log('r gCR 10a, error = ', error)
//     throw error
//   }
// }

// const getRegistrationReport = async (headers, searchAndFilterData) => {
//   try {
//     console.log('r gCR 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/report/registration-report`
//     console.log('r gCR 2, url = ', url)
//     const response = await post(url, headers, searchAndFilterData)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gCR 10, error')
//     console.log('r gCR 10a, error = ', error)
//     throw error
//   }
// }

// const getSellableAnimalsReportOld = async (headers, pageSearchAndFilterData) => {
//   try {
//     console.log('r gSAR 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/sellable-animals-report-old`
//     console.log('r gSAR  2, url = ', url)
//     const response = await post(url, headers, pageSearchAndFilterData)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gSAR 10, error')
//     console.log('r gSAR 10a, error = ', error)
//     throw error
//   }
// }

// const getFarmersWithNoLocationRecordedReport = async (headers, pageSearchAndFilterData) => {
//   try {
//     console.log('r gFWNLRR 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-with-no-location-recorded`
//     console.log('r gFWNLRR 2, url = ', url)
//     const response = await post(url, headers, pageSearchAndFilterData)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gFWNLRR 10, error')
//     console.log('r gFWNLRR 10a, error = ', error)
//     throw error
//   }
// }

// const getFarmersWithNoLocationRecordedReportFilterData = async (headers) => {
//   try {
//     console.log('r gFWLRRFD 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-with-no-location-recorded`
//     console.log('r gFWLRRFD  2, url = ', url)
//     const response = await get(url, headers)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gFWLRRFD 10, error')
//     console.log('r gFWLRRFD 10a, error = ', error)
//     throw error
//   }
// }

// const getFarmersWithLocationRecordedReport = async (headers, pageSearchAndFilterData) => {
//   try {
//     console.log('r gFWLRR 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-with-location-recorded`
//     console.log('r gFWLRR  2, url = ', url)
//     const response = await post(url, headers, pageSearchAndFilterData)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gFWLRR 10, error')
//     console.log('r gFWLRR 10a, error = ', error)
//     throw error
//   }
// }

// const getFarmersWithLocationRecordedReportFilterData = async (headers) => {
//   try {
//     console.log('r gFWLRRFD 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-with-location-recorded`
//     console.log('r gFWLRRFD  2, url = ', url)
//     const response = await get(url, headers)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gFWLRRFD 10, error')
//     console.log('r gFWLRRFD 10a, error = ', error)
//     throw error
//   }
// }

// const getDataForPotentiallySellableAnimalsReport = async (headers, pageSearchAndFilterData) => {
//   try {
//     console.log('r gPSAWLR 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/data-for-potentially-sellable-animals`
//     console.log('r gPSAWLR  2, url = ', url)
//     const response = await post(url, headers, pageSearchAndFilterData)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gPSAWLR 10, error')
//     console.log('r gPSAWLR 10a, error = ', error)
//     throw error
//   }
// }

// const getDataForPotentiallySellableAnimalsReportFilterData = async (headers) => {
//   try {
//     console.log('r gFWLRRFD 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/data-for-potentially-sellable-animals`
//     console.log('r gFWLRRFD  2, url = ', url)
//     const response = await get(url, headers)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gFWLRRFD 10, error')
//     console.log('r gFWLRRFD 10a, error = ', error)
//     throw error
//   }
// }

// const getPotentiallySellableAnimalsReport = async (headers, pageSearchAndFilterData) => {
//   try {
//     console.log('r gPSAWLR 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/potentially-sellable-animals`
//     console.log('r gPSAWLR  2, url = ', url)
//     const response = await post(url, headers, pageSearchAndFilterData)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gPSAWLR 10, error')
//     console.log('r gPSAWLR 10a, error = ', error)
//     throw error
//   }
// }

// const getPotentiallySellableAnimalsReportFilterData = async (headers) => {
//   try {
//     console.log('r gFWLRRFD 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/potentially-sellable-animals`
//     console.log('r gFWLRRFD  2, url = ', url)
//     const response = await get(url, headers)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gFWLRRFD 10, error')
//     console.log('r gFWLRRFD 10a, error = ', error)
//     throw error
//   }
// }

// const snoozeForSellableAnimals = async (headers, animalWithSnoozeInformation) => {
//   try {
//     console.log('r sFSA 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/snooze-for-sellable-animals`
//     console.log('r sFSA  2, url = ', url)
//     const response = await post(url, headers, animalWithSnoozeInformation)
//     return response
//   } catch (error) {
//     console.log('r sFSA 10, error')
//     console.log('r sFSA 10a, error = ', error)
//     throw error
//   }
// }

// const unsnoozeSellableAnimals = async (headers, toBeSnoozeAnimalInformation) => {
//   try {
//     console.log('r sFSA 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/unsnooze-sellable-animals`
//     console.log('r sFSA  2, url = ', url)
//     const response = await post(url, headers, toBeSnoozeAnimalInformation)
//     return response
//   } catch (error) {
//     console.log('r sFSA 10, error')
//     console.log('r sFSA 10a, error = ', error)
//     throw error
//   }
// }

// const updateSellableAnimalStatus = async (headers, animalUpdateStatusData) => {
//   try {
//     console.log('r uSAS 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/update-animal-selling-status`
//     console.log('r uSAS  2, url = ', url)
//     const response = await post(url, headers, animalUpdateStatusData)
//     return response
//   } catch (error) {
//     console.log('r uSAS 10, error')
//     console.log('r uSAS 10a, error = ', error)
//     throw error
//   }
// }

// const markAnimalsAsSellable = async (headers, animalsData) => {
//   try {
//     console.log('r mAAS 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/mark-animals-as-sellable`
//     console.log('r mAAS  2, url = ', url)
//     const response = await post(url, headers, animalsData)
//     return response
//   } catch (error) {
//     console.log('r mAAS 10, error')
//     console.log('r mAAS 10a, error = ', error)
//     throw error
//   }
// }

// const getSellableAnimalsReport = async (headers, pageSearchAndFilterData) => {
//   try {
//     console.log('r gSAR 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/sellable-animals`
//     console.log('r gSAR  2, url = ', url)
//     const response = await post(url, headers, pageSearchAndFilterData)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gSAR 10, error')
//     console.log('r gSAR 10a, error = ', error)
//     throw error
//   }
// }

// const getSellableAnimalsReportFilterData = async (headers) => {
//   try {
//     console.log('r gSARFD 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/sellable-animals`
//     console.log('r gSARFD  2, url = ', url)
//     const response = await get(url, headers)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gSARFD 10, error')
//     console.log('r gSARFD 10a, error = ', error)
//     throw error
//   }
// }

// const getItemDetailByItemListingId = async (headers, itemListingInformation) => {
//   try {
//     console.log('r gIDBILI 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/item-detail-by-item-listing-id`
//     console.log('r gIDBILI  2, url = ', url)
//     const response = await post(url, headers, itemListingInformation)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gIDBILI 10, error')
//     console.log('r gIDBILI 10a, error = ', error)
//     throw error
//   }
// }

// const getPotentialBuyerFarmersForAnimalReport = async (headers, pageSearchAndFilterData) => {
//   try {
//     console.log('r gSAR 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/potential-buyer-farmers-for-animal`
//     console.log('r gSAR  2, url = ', url)
//     const response = await post(url, headers, pageSearchAndFilterData)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gSAR 10, error')
//     console.log('r gSAR 10a, error = ', error)
//     throw error
//   }
// }

// const getPotentialBuyerFarmersForAnimalReportFilterData = async (headers) => {
//   try {
//     console.log('r gSARFD 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/potential-buyer-farmers-for-animal`
//     console.log('r gSARFD  2, url = ', url)
//     const response = await get(url, headers)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gSARFD 10, error')
//     console.log('r gSARFD 10a, error = ', error)
//     throw error
//   }
// }

// const snoozeForBuyerFarmers = async (headers, buyerFarmerWithAnimalAndSnoozeInformation) => {
//   try {
//     console.log('r sFSA 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/snooze-for-buyer-farmers`
//     console.log('r sFSA  2, url = ', url)
//     const response = await post(url, headers, buyerFarmerWithAnimalAndSnoozeInformation)
//     return response
//   } catch (error) {
//     console.log('r sFSA 10, error')
//     console.log('r sFSA 10a, error = ', error)
//     throw error
//   }
// }

// const updateBuyerFarmerStatus = async (headers, farmerUpdateStatusData) => {
//   try {
//     console.log('r uSAS 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/update-buying-farmer-status`
//     console.log('r uSAS  2, url = ', url)
//     const response = await post(url, headers, farmerUpdateStatusData)
//     return response
//   } catch (error) {
//     console.log('r uSAS 10, error')
//     console.log('r uSAS 10a, error = ', error)
//     throw error
//   }
// }

// const getFarmersWhoMayWantToBuyAnimalsReport = async (headers, pageSearchAndFilterData) => {
//   try {
//     console.log('r gFWMWTBAR 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-who-may-want-to-buy-animals`
//     console.log('r gFWMWTBAR 2, url = ', url)
//     const response = await post(url, headers, pageSearchAndFilterData)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gFWMWTBAR 10, error')
//     console.log('r gFWMWTBAR 10a, error = ', error)
//     throw error
//   }
// }

// const getFarmersWhoMayWantToBuyAnimalsReportFilterData = async (headers) => {
//   try {
//     console.log('r gFWMWTBARFD 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/farmers-who-may-want-to-buy-animals`
//     console.log('r gFWMWTBARFD 2, url = ', url)
//     const response = await get(url, headers)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gFWMWTBARFD 10, error')
//     console.log('r gFWMWTBARFD 10a, error = ', error)
//     throw error
//   }
// }

// const createOrderForAnimalSale = async (headers, data) => {
//   try {
//     console.log('r cOFAS 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/create-order-for-animal-sale`
//     console.log('r cOFAS 2, url = ', url)
//     const response = await post(url, headers, data)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r cOFAS 10, error')
//     console.log('r cOFAS 10a, error = ', error)
//     throw error
//   }
// }

// const getAnimalExchangeOrdersReport = async (headers, pageSearchAndFilterData) => {
//   try {
//     console.log('r gSAR 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/animal-exchange-orders`
//     console.log('r gSAR  2, url = ', url)
//     const response = await post(url, headers, pageSearchAndFilterData)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gSAR 10, error')
//     console.log('r gSAR 10a, error = ', error)
//     throw error
//   }
// }

// const getAnimalExchangeOrdersReportFilterData = async (headers) => {
//   try {
//     console.log('r gSARFD 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/animal-exchange-orders`
//     console.log('r gSARFD  2, url = ', url)
//     const response = await get(url, headers)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gSARFD 10, error')
//     console.log('r gSARFD 10a, error = ', error)
//     throw error
//   }
// }

// const updateOrderFarmerStatus = async (headers, farmerUpdateStatusData) => {
//   try {
//     console.log('r uOFS 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/update-order-farmer-status`
//     console.log('r uOFS  2, url = ', url)
//     const response = await post(url, headers, farmerUpdateStatusData)
//     return response
//   } catch (error) {
//     console.log('r uOFS 10, error')
//     console.log('r uOFS 10a, error = ', error)
//     throw error
//   }
// }

// const getDocuments = async (headers, documentQueryInformation) => {
//   try {
//     console.log('r uOFS 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/get-documents`
//     console.log('r uOFS  2, url = ', url)
//     const response = await post(url, headers, documentQueryInformation)
//     return response
//   } catch (error) {
//     console.log('r uOFS 10, error')
//     console.log('r uOFS 10a, error = ', error)
//     throw error
//   }
// }

// const uploadFileToS3 = async (headers, formData) => {
//   try {
//     console.log('r uFTS3 1')
//     const url =
//       configurationJSON().SERVICE_END_POINTS.BACK_END +
//       '/v2/file'
//     const response = await post(url, headers, formData)
//     console.log('r uFTS3 2, response = ', response)
//     return response
//   } catch (error) {
//     console.log('r uFTS3 10, error')
//     console.log('r uFTS3 10a, error = ', error)
//     throw error
//   }
// }

// const saveDocuments = async (headers, documents) => {
//   try {
//     console.log('r sD 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/save-documents`
//     console.log('r sD  2, url = ', url)
//     const response = await post(url, headers, documents)
//     return response
//   } catch (error) {
//     console.log('r sD 10, error')
//     console.log('r sD 10a, error = ', error)
//     throw error
//   }
// }

// const getComments = async (headers, commentConfiguration) => {
//   try {
//     console.log('r gC 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/get-comments`
//     console.log('r gC  2, url = ', url)
//     const response = await post(url, headers, commentConfiguration)
//     return response
//   } catch (error) {
//     console.log('r gC 10, error')
//     console.log('r gC 10a, error = ', error)
//     throw error
//   }
// }

// // farmers who are not contacted and snooze is not there

// const getManureOCFarmersReport = async (headers, customerFilterConfiguration) => {
//   try {
//     console.log('r gMOCFR 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/manure-farmers`
//     console.log('r gMOCFR 2, url = ', url)
//     const response = await post(url, headers, customerFilterConfiguration)
//     return response
//   } catch (error) {
//     console.log('r gMOCFR 10, error')
//     console.log('r gMOCFR 10a, error = ', error)
//     throw error
//   }
// }

// const getManureOCFarmersReportReportFilterData = async (headers) => {
//   try {
//     console.log('r gMOCFRFD 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/manure-farmers`
//     console.log('r gMOCFRFD  2, url = ', url)
//     const response = await get(url, headers)
//     // console.log('response = ', response)
//     // console.log('ahr pR gF 2a, response.data = ', response.data)
//     return response
//   } catch (error) {
//     console.log('r gMOCFRFD 10, error')
//     console.log('r gMOCFRFD 10a, error = ', error)
//     throw error
//   }
// }

// // not interested, we snooze them for a year
// const snoozeManureOCFarmers = async (headers, customerFilterConfiguration) => {
//   try {
//     console.log('r gMONCF 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/manure-snooze-farmers`
//     console.log('r gC  2, url = ', url)
//     const response = await post(url, headers, customerFilterConfiguration)
//     return response
//   } catch (error) {
//     console.log('r gC 10, error')
//     console.log('r gC 10a, error = ', error)
//     throw error
//   }
// }

// // view farmer detailed information
// const getCustomerInformationBasedOnClassifiers = async (headers, customerIdAndClassifierArray) => {
//   try {
//     console.log('r gCIBOC 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/get-customer-details`
//     console.log('r gCIBOC  2, url = ', url)
//     const response = await post(url, headers, customerIdAndClassifierArray)
//     return response
//   } catch (error) {
//     console.log('r gCIBOC 10, error')
//     console.log('r gCIBOC 10a, error = ', error)
//     throw error
//   }
// }

// // view farmer detailed information
// const updateCustomerInformationBasedOnClassifiers = async (headers, customerIdAndClassifierData) => {
//   try {
//     console.log('r uCIBOC 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/set-customer-details`
//     console.log('r uCIBOC  2, url = ', url)
//     const response = await post(url, headers, customerIdAndClassifierData)
//     return response
//   } catch (error) {
//     console.log('r uCIBOC 10, error')
//     console.log('r uCIBOC 10a, error = ', error)
//     throw error
//   }
// }

// const getFarmerBasicData = async (headers, customerId) => {
//   try {
//     console.log('r gFBD 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/get-customer-details/${customerId}`
//     console.log('r gFBD  2, url = ', url)
//     const response = await get(url, headers)
//     return response
//   } catch (error) {
//     console.log('r gFBD 10, error')
//     console.log('r gFBD 10a, error = ', error)
//     throw error
//   }
// }

// const getDocumentDetails = async (headers, documentId) => {
//   try {
//     console.log('r gDD 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/document-information/${documentId}`
//     console.log('r gDD 2, url = ', url)
//     const response = await get(url, headers)
//     return response
//   } catch (error) {
//     console.log('r gDD 10, error')
//     console.log('r gDD 10a, error = ', error)
//     throw error
//   }
// }

// const getAnimalBasicData = async (headers, animalId) => {
//   try {
//     console.log('r gABD 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/get-animal-details/${animalId}`
//     console.log('r gABD  2, url = ', url)
//     const response = await get(url, headers)
//     return response
//   } catch (error) {
//     console.log('r gABD 10, error')
//     console.log('r gABD 10a, error = ', error)
//     throw error
//   }
// }

// // view farmer detailed information
// const getAnimalInformationBasedOnClassifiers = async (headers, animalIdAndClassifierArray) => {
//   try {
//     console.log('r gCIBOC 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/get-animal-details`
//     console.log('r gCIBOC  2, url = ', url)
//     const response = await post(url, headers, animalIdAndClassifierArray)
//     return response
//   } catch (error) {
//     console.log('r gCIBOC 10, error')
//     console.log('r gCIBOC 10a, error = ', error)
//     throw error
//   }
// }

// // view farmer detailed information
// const updateAnimalInformationBasedOnClassifiers = async (headers, customerIdAndClassifierData) => {
//   try {
//     console.log('r uCIBOC 1')
//     const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/set-animal-details`
//     console.log('r uCIBOC  2, url = ', url)
//     const response = await post(url, headers, customerIdAndClassifierData)
//     return response
//   } catch (error) {
//     console.log('r uCIBOC 10, error')
//     console.log('r uCIBOC 10a, error = ', error)
//     throw error
//   }
// }




// const get_requisition_list = async (userToken, queryParams) => {
//   try {
//     const response = await instance({
//       url: "v3/requisitionlist?type=admin",
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//         token: userToken.accessToken,
//       },
//       data: queryParams
//     });
//     console.log("get route plan", response);
//     return response
//   }
//   catch (error) {
//     throw error
//   }

// }


// const get_medicine_inventory = async (userToken, queryParams) => {
//   try {
//     const response = await instance({
//       url: "v3/medinventory",
//       method: "GET",
//       headers: {
//         "Content-Type": "application/json",
//         token: userToken.accessToken,
//       },
//       data: queryParams
//     });
//     console.log("get medicine inventory", response);
//     return response
//   }
//   catch (error) {
//     throw error
//   }

// }


const get_medicine_inventory = async (headers, searchAndQueryParams) => {
  try {
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + '/v3/medinventory'
    const response = await post(url, headers, searchAndQueryParams)
    return response
  }
  catch (error) {
    throw error
  }

}

const get_requisition_list = async (headers, searchAndQueryParams) => {
  try {
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + '/inventory/requests/all'
    const response = await post(url, headers, searchAndQueryParams)
    return response
  } catch (error) {
    throw error
  }
}

const get_all_requisition = async (userToken) => {
  try {
    const response = await instance({
      url: "v3/requisitionlist",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        token: userToken.accessToken,
      },
      data: {
        "r_id": null,
        "r_requisitioner": [],
        "r_status": [],
        "r_fullfiller": [],
        "r_approver": [],
        "r_requisitioner_type": [],
        "r_approver_type": [],
        "r_fullfiller_type": [],
        "r_requisitioner_name": "",
        "r_approver_name": "",
        "r_fullfiller_name": "",
        "r_created_at_from": "",
        "r_created_at_to": "",
        "r_pps_id": [],
        "r_pmds_id": [],
        "r_pis_id": []
      }
    });
    return response
  }
  catch (error) {
    throw error
  }

}

const get_pharmacy_requistion = async (headers, searchAndQueryParams, type) => {
  try {
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/pharmacyrequisition/${type}`
    const response = await post(url, headers, searchAndQueryParams)
    return response
  }
  catch (error) {
    throw error
  }

}

const get_ledger_list = async (headers, searchAndQueryParams) => {
  try {
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/inventorykit`
    const response = await post(url, headers, searchAndQueryParams)
    return response
  }
  catch (error) {
    throw error
  }

}


export { get_all_requisition, get_requisition_list, get_medicine_inventory, get_ledger_list, get_pharmacy_requistion }



// // module.exports = {getComplaintReport, getRegistrationReport, getSellableAnimalsReport}
// export {
//   getComplaintReport, getRegistrationReport, getSellableAnimalsReportOld,
//   getFarmersWithNoLocationRecordedReport, getFarmersWithNoLocationRecordedReportFilterData,
//   getFarmersWithLocationRecordedReport, getFarmersWithLocationRecordedReportFilterData,
//   getDataForPotentiallySellableAnimalsReport, getDataForPotentiallySellableAnimalsReportFilterData,
//   getPotentiallySellableAnimalsReport, getPotentiallySellableAnimalsReportFilterData,
//   snoozeForSellableAnimals, unsnoozeSellableAnimals, updateSellableAnimalStatus, markAnimalsAsSellable,
//   getSellableAnimalsReport, getSellableAnimalsReportFilterData,
//   getItemDetailByItemListingId, getPotentialBuyerFarmersForAnimalReport, getPotentialBuyerFarmersForAnimalReportFilterData,
//   snoozeForBuyerFarmers, updateBuyerFarmerStatus,
//   getFarmersWhoMayWantToBuyAnimalsReport, getFarmersWhoMayWantToBuyAnimalsReportFilterData,
//   createOrderForAnimalSale, getAnimalExchangeOrdersReport, getAnimalExchangeOrdersReportFilterData,
//   updateOrderFarmerStatus, getDocuments, uploadFileToS3, saveDocuments,
//   getComments,
//   getManureOCFarmersReport, getManureOCFarmersReportReportFilterData, snoozeManureOCFarmers,
//   getCustomerInformationBasedOnClassifiers, updateCustomerInformationBasedOnClassifiers,
//   getFarmerBasicData, getDocumentDetails, getAnimalBasicData, getAnimalInformationBasedOnClassifiers, updateAnimalInformationBasedOnClassifiers
// }
