import {
  Routes,
  Route,
} from "react-router-dom";

import Sidebar from '../Components/Sidebar/Sidebar'

import OCAnimalDetails from './Components/OCAnimalDetails'
import OCFarmerDetails from './Components/OCFarmerDetails'
import OCMedicineDetails from './Components/OCMedicineDetails'

const EntitiesRoutes = () => {
  return (
    <Routes>
      <Route path="e/farmer-details/:customerId" element={<Sidebar content={<OCFarmerDetails />} />} />
      <Route path="e/farmer-details/:customerId/:tabKey" element={<Sidebar content={<OCFarmerDetails />} />} />
      <Route path="e/farmer-animal-details/:customerId/animal/:animalId" element={<Sidebar content={<OCAnimalDetails />} />} />
      <Route path="e/farmer-animal-details/:customerId/animal/:animalId/:tabKey" element={<Sidebar content={<OCAnimalDetails />} />} />
      <Route path="e/medicine/:medicineId" element={<Sidebar content={<OCMedicineDetails />} />} />
    </Routes>
  )
}

export default EntitiesRoutes