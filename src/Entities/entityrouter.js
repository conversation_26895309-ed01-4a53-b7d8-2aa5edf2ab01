const { /* get,  */put, /* post,  */configurationJSON, KrushalError } = require('@krushal-it/common-core')
const { post, get } = require('../routerhelper')

const getFarmerDataReportsPageLevelFilterData = async (headers, configuration) => {
  try {
    console.log('r gFDRPLFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/customer-list-filter`
    console.log('r gFDRPLFD  2, url = ', url)
    const response = await post(url, headers, configuration)
    return response
  } catch (error) {
    console.log('r gFDRPLFD 10, error')
    console.log('r gFDRPLFD 10a, error = ', error)
    throw error
  }
}

const getFarmerDataReport = async (headers, configuration) => {
  try {
    console.log('r gFWLRR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/customer-list`
    console.log('r gFWLRR  2, url = ', url)
    const response = await post(url, headers, configuration)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRR 10, error')
    console.log('r gFWLRR 10a, error = ', error)
    throw error
  }
}

const getAnimalDataReportsPageLevelFilterData = async (headers, configuration) => {
  try {
    console.log('r gADRPLFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/animal-list-filter`
    console.log('r gADRPLFD  2, url = ', url)
    const response = await post(url, headers, configuration)
    return response
  } catch (error) {
    console.log('r gADRPLFD 10, error')
    console.log('r gADRPLFD 10a, error = ', error)
    throw error
  }
}

const getAnimalDataReport = async (headers, configuration) => {
  try {
    console.log('r gFWLRR 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/animal-list`
    console.log('r gFWLRR  2, url = ', url)
    const response = await post(url, headers, configuration)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRR 10, error')
    console.log('r gFWLRR 10a, error = ', error)
    throw error
  }
}

const getAnimalsForFarmer = async (headers, configuration, customerId) => {
  try {
    console.log('r gFWLRR 1')
    /* const configuration = { 
      searchConfiguration: {},
      selectedColumns: [],
      start: -1,
      size: 10,
      current: 1,
      pageParams: {},
      filters: {},
      sorting: {}
    } */
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/animal-list?cId=${customerId}`
    console.log('r gFWLRR  2, url = ', url)
    const response = await post(url, headers, configuration)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gFWLRR 10, error')
    console.log('r gFWLRR 10a, error = ', error)
    throw error
  }
}


// view farmer detailed information
const getCustomerInformationBasedOnClassifiers = async (headers, customerIdAndClassifierArray) => {
  try {
    console.log('r gCIBOC 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/get-customer-details`
    console.log('r gCIBOC  2, url = ', url)
    const response = await post(url, headers, customerIdAndClassifierArray)
    return response
  } catch (error) {
    console.log('r gCIBOC 10, error')
    console.log('r gCIBOC 10a, error = ', error)
    throw error
  }
}

// view farmer detailed information
const updateCustomerInformationBasedOnClassifiers = async (headers, customerIdAndClassifierData) => {
  try {
    console.log('r uCIBOC 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/set-customer-details`
    console.log('r uCIBOC  2, url = ', url)
    const response = await post(url, headers, customerIdAndClassifierData)
    return response
  } catch (error) {
    console.log('r uCIBOC 10, error')
    console.log('r uCIBOC 10a, error = ', error)
    throw error
  }
}

const getFarmerBasicData = async (headers, customerId) => {
  try {
    console.log('r gFBD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/get-customer-details/${customerId}`
    console.log('r gFBD  2, url = ', url)
    const response = await get(url, headers)
    return response
  } catch (error) {
    console.log('r gFBD 10, error')
    console.log('r gFBD 10a, error = ', error)
    throw error
  }
}

const getAnimalBasicData = async (headers, animalId) => {
  try {
    console.log('r gABD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/get-animal-details/${animalId}`
    console.log('r gABD  2, url = ', url)
    const response = await get(url, headers)
    return response
  } catch (error) {
    console.log('r gABD 10, error')
    console.log('r gABD 10a, error = ', error)
    throw error
  }
}

// view farmer detailed information
const getAnimalInformationBasedOnClassifiers = async (headers, animalIdAndClassifierArray) => {
  try {
    console.log('r gCIBOC 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/get-animal-details`
    console.log('r gCIBOC  2, url = ', url)
    const response = await post(url, headers, animalIdAndClassifierArray)
    return response
  } catch (error) {
    console.log('r gCIBOC 10, error')
    console.log('r gCIBOC 10a, error = ', error)
    throw error
  }
}

// view farmer detailed information
const updateAnimalInformationBasedOnClassifiers = async (headers, customerIdAndClassifierData) => {
  try {
    console.log('r uCIBOC 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v3/set-animal-details`
    console.log('r uCIBOC  2, url = ', url)
    const response = await post(url, headers, customerIdAndClassifierData)
    return response
  } catch (error) {
    console.log('r uCIBOC 10, error')
    console.log('r uCIBOC 10a, error = ', error)
    throw error
  }
}

const createTaskForCustomerService = async (headers, customerServiceTask) => {
  try {
    console.log('r cTFCS 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/create-cs-task`
    console.log('r cTFCS  2, url = ', url)
    const response = await post(url, headers, customerServiceTask)
    return response
  } catch (error) {
    console.log('r cTFCS 10, error')
    console.log('r cTFCS 10a, error = ', error)
    throw error
  }
}

const getCropDetailedData = async (headers, customerId) => {
  try {
    console.log('r gCDD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/crop-details/${customerId}`
    console.log('r gCDD 2, url = ', url)
    const response = await get(url, headers)
    return response
  } catch (error) {
    console.log('r gCDD 10, error')
    console.log('r gCDD 10a, error = ', error)
    throw error
  }
}

const saveCropDetailedData = async (headers, cropDetailsData) => {
  try {
    console.log('r sCDD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/crop-details`
    console.log('r sCDD 2, url = ', url)
    const response = await post(url, headers, cropDetailsData)
    return response
  } catch (error) {
    console.log('r sCDD 10, error')
    console.log('r sCDD 10a, error = ', error)
    throw error
  }
}

const getMedicineData = async (headers, medicineId) => {
  try {
    console.log('r gMD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/r/medicine/${medicineId}`
    console.log('r gMD 2, url = ', url)
    const response = await get(url, headers)
    return response
  } catch (error) {
    console.log('r gMD 10, error')
    console.log('r gMD 10a, error = ', error)
    throw error
  }
}

const saveMedicineData = async (headers, medicineDetailsData) => {
  try {
    console.log('r sMD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/r/medicine`
    console.log('r sMD 2, url = ', url)
    const response = await post(url, headers, medicineDetailsData)
    return response
  } catch (error) {
    console.log('r sMD 10, error')
    console.log('r sMD 10a, error = ', error)
    throw error
  }
}

const getPartnerReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gPR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/onboarding-animals-health-score-verification`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/e/partner-list`
    console.log('r gPR  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gPR 10, error')
    console.log('r gPR 10a, error = ', error)
    throw error
  }
}

const getPartnerReportFilterData = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gPRFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/e/partner-list-filter`
    console.log('r gPRFD  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gPRFD 10, error')
    console.log('r gPRFD 10a, error = ', error)
    throw error
  }
}

const getPartnerBasicData = async (headers, partnerId) => {
  try {
    console.log('r gPBD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/e/get-partner-details/${partnerId}`
    console.log('r gPBD  2, url = ', url)
    const response = await get(url, headers)
    return response
  } catch (error) {
    console.log('r gPBD 10, error')
    console.log('r gPBD 10a, error = ', error)
    throw error
  }
}

const getPartnerDetails = async (headers, partnerId, classifierArray) => {
  try {
    console.log('r gPD 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/onboarding-animals-health-score-verification`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/e/get-partner-details`
    console.log('r gPD  2, url = ', url)
    const dataToSend = {partner_id: partnerId, classifierArray: classifierArray}
    const response = await post(url, headers, dataToSend)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gPD 10, error')
    console.log('r gPD 10a, error = ', error)
    throw error
  }
}

const setPartnerDetails = async (headers, partnerData) => {
  try {
    console.log('r gPSAWLR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/onboarding-animals-health-score-verification`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/e/set-partner-details`
    console.log('r gPSAWLR  2, url = ', url)
    const response = await post(url, headers, partnerData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gPSAWLR 10, error')
    console.log('r gPSAWLR 10a, error = ', error)
    throw error
  }
}

const getStaffReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gSR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/onboarding-animals-health-score-verification`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/e/staff-list`
    console.log('r gSR  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSR 10, error')
    console.log('r gSR 10a, error = ', error)
    throw error
  }
}

const getStaffReportFilterData = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gSRFD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/e/staff-list-filter`
    console.log('r gSRFD  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSRFD 10, error')
    console.log('r gSRFD 10a, error = ', error)
    throw error
  }
}

const getStaffBasicData = async (headers, staffId) => {
  try {
    console.log('r gSBD 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/e/get-staff-details/${staffId}`
    console.log('r gSBD  2, url = ', url)
    const response = await get(url, headers)
    return response
  } catch (error) {
    console.log('r gSBD 10, error')
    console.log('r gSBD 10a, error = ', error)
    throw error
  }
}

const getStaffDetails = async (headers, staffId, classifierArray) => {
  try {
    console.log('r gPD 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/onboarding-animals-health-score-verification`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/e/get-staff-details`
    console.log('r gPD  2, url = ', url)
    const dataToSend = {staff_id: staffId, classifierArray: classifierArray}
    const response = await post(url, headers, dataToSend)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gPD 10, error')
    console.log('r gPD 10a, error = ', error)
    throw error
  }
}

const setStaffDetails = async (headers, staffData) => {
  try {
    console.log('r gPSAWLR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/onboarding-animals-health-score-verification`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/e/set-staff-details`
    console.log('r gPSAWLR  2, url = ', url)
    const response = await post(url, headers, staffData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gPSAWLR 10, error')
    console.log('r gPSAWLR 10a, error = ', error)
    throw error
  }
}

const getTerritoryAssignmentReport = async (headers, pageSearchAndFilterData) => {
  try {
    console.log('r gSR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/onboarding-animals-health-score-verification`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/e/staff-territory-list`
    console.log('r gSR  2, url = ', url)
    const response = await post(url, headers, pageSearchAndFilterData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSR 10, error')
    console.log('r gSR 10a, error = ', error)
    throw error
  }
}

const deleteStaffTerritories = async (headers, territoryActionData) => {
  try {
    console.log('r gSR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/onboarding-animals-health-score-verification`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/e/delete-territory-assignment`
    console.log('r gSR  2, url = ', url)
    const response = await post(url, headers, territoryActionData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSR 10, error')
    console.log('r gSR 10a, error = ', error)
    throw error
  }
}

const assignTerritoriesToStaff = async (headers, geographyAssignmentData) => {
  try {
    console.log('r gSR 1')
    // const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/oc/onboarding-animals-health-score-verification`
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/e/assign-territory`
    console.log('r gSR  2, url = ', url)
    const response = await post(url, headers, geographyAssignmentData)
    // console.log('response = ', response)
    // console.log('ahr pR gF 2a, response.data = ', response.data)
    return response
  } catch (error) {
    console.log('r gSR 10, error')
    console.log('r gSR 10a, error = ', error)
    throw error
  }
}

export  {
  getFarmerDataReport, getFarmerDataReportsPageLevelFilterData,
  getAnimalDataReport, getAnimalDataReportsPageLevelFilterData, getAnimalsForFarmer,
  getFarmerBasicData, getCustomerInformationBasedOnClassifiers, updateCustomerInformationBasedOnClassifiers, 
  getAnimalBasicData, getAnimalInformationBasedOnClassifiers, updateAnimalInformationBasedOnClassifiers,
  createTaskForCustomerService, getCropDetailedData, saveCropDetailedData,
  getMedicineData, saveMedicineData,

  getPartnerReport, getPartnerReportFilterData,
  getPartnerBasicData, getPartnerDetails, setPartnerDetails,

  getStaffReport, getStaffReportFilterData, getStaffBasicData, getStaffDetails, setStaffDetails,
  getTerritoryAssignmentReport, deleteStaffTerritories, assignTerritoriesToStaff,
}