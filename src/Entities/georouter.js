const { /* get,  */put, /* post,  */configurationJSON, KrushalError } = require('@krushal-it/common-core')
const { post, get } = require('../routerhelper')

const getStateList = async (headers) => {
  try {
    console.log('r gSL 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/geo/states`
    console.log('r gSL  2, url = ', url)
    const response = await get(url, headers)
    return response
  } catch (error) {
    console.log('r gSL 10, error')
    console.log('r gSL 10a, error = ', error)
    throw error
  }
}

const getStateListV2 = async (headers) => {
  try {
    console.log('r gSL 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v2/geo/states`
    console.log('r gSL  2, url = ', url)
    const response = await get(url, headers)
    return response
  } catch (error) {
    console.log('r gSL 10, error')
    console.log('r gSL 10a, error = ', error)
    throw error
  }
}

const getDistrictList = async(headers, stateId) => {
  try {
    console.log('r gDL 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/geo/districts?stateId=${stateId}`
    console.log('r gDL  2, url = ', url)
    const response = await post(url, headers, {})
    return response
  } catch (error) {
    console.log('r gDL 10, error')
    console.log('r gDL 10a, error = ', error)
    throw error
  }
}

const getDistrictListV2 = async(headers, stateId) => {
  try {
    console.log('r gDL 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v2/geo/districts?stateId=${stateId}`
    console.log('r gDL  2, url = ', url)
    const response = await post(url, headers, {})
    return response
  } catch (error) {
    console.log('r gDL 10, error')
    console.log('r gDL 10a, error = ', error)
    throw error
  }
}

const getTalukList = async(headers, districtId) => {
  try {
    console.log('r gDL 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/geo/taluks?districtId=${districtId}`
    console.log('r gDL  2, url = ', url)
    const response = await post(url, headers, {})
    return response
  } catch (error) {
    console.log('r gDL 10, error')
    console.log('r gDL 10a, error = ', error)
    throw error
  }
}

const getTalukListV2 = async(headers, districtId) => {
  try {
    console.log('r gDL 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v2/geo/taluks?districtId=${districtId}`
    console.log('r gDL  2, url = ', url)
    const response = await post(url, headers, {})
    return response
  } catch (error) {
    console.log('r gDL 10, error')
    console.log('r gDL 10a, error = ', error)
    throw error
  }
}

const getVillageList = async(headers, talukId) => {
  try {
    console.log('r gDL 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/geo/villages?talukId=${talukId}`
    console.log('r gDL  2, url = ', url)
    const response = await post(url, headers, {})
    return response
  } catch (error) {
    console.log('r gDL 10, error')
    console.log('r gDL 10a, error = ', error)
    throw error
  }
}

const getVillageList2 = async(headers, talukId) => {
  try {
    console.log('r gDL 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v2/geo/villages?talukId=${talukId}`
    console.log('r gDL  2, url = ', url)
    const response = await post(url, headers, {})
    return response
  } catch (error) {
    console.log('r gDL 10, error')
    console.log('r gDL 10a, error = ', error)
    throw error
  }
}

const getGeographyListing = async(headers, relationshipConfiguration) => {
  try {
    console.log('r gDL 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v2/geo`
    console.log('r gDL  2, url = ', url)
    const response = await post(url, headers, relationshipConfiguration)
    return response
  } catch (error) {
    console.log('r gDL 10, error')
    console.log('r gDL 10a, error = ', error)
    throw error
  }
}

const getGeoJSONs = async(headers, relationshipConfiguration) => {
  try {
    console.log('r gDL 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v2/geo-json`
    console.log('r gDL  2, url = ', url)
    const response = await post(url, headers, relationshipConfiguration)
    return response
  } catch (error) {
    console.log('r gDL 10, error')
    console.log('r gDL 10a, error = ', error)
    throw error
  }
}

const getGeographyDataForVillage = async(headers, villageId) => {
  try {
    console.log('r gDL 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v2/geo/get-geo-data?villageId=${villageId}`
    console.log('r gDL  2, url = ', url)
    const response = await get(url, headers)
    return response
  } catch (error) {
    console.log('r gDL 10, error')
    console.log('r gDL 10a, error = ', error)
    throw error
  }
}

const getGeographyDataForVillage2 = async (headers, villageIdArray) => {
  try {
    console.log('r gDL 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v2/geos-for-village`
    console.log('r gDL  2, url = ', url)
    const response = await post(url, headers, {village_id_array: villageIdArray})
    return response
  } catch (error) {
    console.log('r gDL 10, error')
    console.log('r gDL 10a, error = ', error)
    throw error
  }
}

const getGeographyData = async(headers, configuration) => {
  try {
    console.log('r gDL 1')
    const url = configurationJSON().SERVICE_END_POINTS.BACK_END + `/v2/geo/get-geo-data`
    console.log('r gDL  2, url = ', url)
    const response = await post(url, headers, configuration)
    return response
  } catch (error) {
    console.log('r gDL 10, error')
    console.log('r gDL 10a, error = ', error)
    throw error
  }
}

export  {
  getStateList,
  getStateListV2,
  getDistrictList,
  getDistrictListV2,
  getTalukList,
  getTalukListV2,
  getVillageList,
  getVillageList2,
  getGeographyListing,
  getGeoJSONs,
  getGeographyDataForVillage,
  getGeographyDataForVillage2,
  getGeographyData,
}