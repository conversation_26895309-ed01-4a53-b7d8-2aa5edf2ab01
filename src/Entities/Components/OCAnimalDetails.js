import { useEffect, useState, useCallback, createRef } from "react";
import { Link, useNavigate, useLocation, useParams, useSearchParams } from "react-router-dom";
import { useSelector } from "react-redux"
import { Spin, Space, Modal, Collapse, Ta<PERSON>, Button } from 'antd'
import moment from 'moment';

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  validateFormElements2,
  assignValuesFromControlReferencesToFormState,
  extractChangedValues,
  assignReferencesAsListToForm,
  getValueFormControl
} from "../../Components/Common/KrushalOCForm";
import ToastersService from "../../Services/toasters.service";

const lodashObject = require("lodash");

const { Panel } = Collapse

const {
  extractBasedOnLanguage
} = require("@krushal-it/common-core")

const { MediaCarousel } = require('../../Components/Common/MediaComponents')
const { getFarmerBasicData, getAnimalBasicData, getAnimalInformationBasedOnClassifiers, updateAnimalInformationBasedOnClassifiers, calculateHealthScore, generatePriceForAnimal, createTaskForCustomerService } = require('../../router')

const loadBasicFarmerData = async (userToken, customerId) => {
  try {
    const headers = {
      useCase: 'Get Basic Farmer Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getFarmerBasicData(headers, customerId)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}
const getBasicDataOfAnimal = async (userToken, animalId) => {
  try {
    const headers = {
      useCase: 'Get Basic Animal Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getAnimalBasicData(headers, animalId)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getAnimalData = async (userToken, animalId, classifierKeyArray) => {
  try {
    console.log('OCAD gAD 1')
    const headers = {
      useCase: 'Get Animal Data Based On Classification',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getAnimalInformationBasedOnClassifiers(headers, {animalId: animalId, classifierArray: classifierKeyArray})
    return response.data
  } catch (error) {
    console.log('OCAD gAD 10, error')
    console.log('OCAD gAD 10a, error = ', error)
    throw error
  }
}

const setAnimalData = async (userToken, animalId, classificationData) => {
  try {
    console.log('OCFD sFD 1')
    const headers = {
      useCase: 'Update Animal Classification Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await updateAnimalInformationBasedOnClassifiers(headers, {animalId: animalId, classificationData: classificationData})
    return response.data
  } catch (error) {
    console.log('OCFD sFD 10, error')
    console.log('OCFD sFD 10a, error = ', error)
    throw error
  }
}

const createCustomerServiceTask = async (userToken, taskData) => {
  try {
    console.log('OCFD sFD 1')
    const headers = {
      useCase: 'Update Animal Classification Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const responseWithData = await createTaskForCustomerService(headers, taskData)
    const response = responseWithData.data
    return response
  } catch (error) {
    console.log('OCFD sFD 10, error')
    console.log('OCFD sFD 10a, error = ', error)
    throw error
  }
}

const handleCalculateHealthScoreClick = async (userToken, animalId) => {
  try {
    console.log('OCAD gAD 1')
    const headers = {
      useCase: 'Recalculate Health Score for Animals',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await calculateHealthScore(headers, {animal_ids: [animalId]})
    return response.data
  } catch (error) {
    console.log('OCAD gAD 10, error')
    console.log('OCAD gAD 10a, error = ', error)
    throw error
  }
}

const handleGenerateSellablePriceOfCattleExchangeAnimalClick = async (userToken, animalId) => {
  try {
    console.log('OCAD gAD 1')
    const headers = {
      useCase: 'Generate Price of Cattle Exchange Animal',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await generatePriceForAnimal(headers, {animal_ids: [animalId]})
    return response.data
  } catch (error) {
    console.log('OCAD gAD 10, error')
    console.log('OCAD gAD 10a, error = ', error)
    throw error
  }
}

const animalBasicDetailsFormDefinition = {
  formControls: {
    animalId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Id",
      readOnly: 1,
    },
    animalVisualId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Visual Id",
      readOnly: 1,
    },
    animalEarTag: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Ear Tag",
      readOnly: 1,
    },
    animalBreedAndType: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Breed And Type",
      readOnly: 1,
    },
    animalAge: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Age",
      readOnly: 1,
    },
    customerTypeId: {
      ocFormRef: createRef(),
      type: "positive-number_2",
      label: "Customer Type Id",
      visible: false,
      readOnly: 1,
    },
    farmerNameAndMobile: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Name And Mobile",
      readOnly: 1,
    },
    farmerDistrictTalukVillage: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer District Taluk Village",
      readOnly: 1,
    },
    farmerBDMAndMobile: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer BDM And Mobile",
      readOnly: 1,
    },
    farmerParavetAndMobile: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Paravet And Mobile",
      readOnly: 1,
    },
    farmerThumbnail: {
      type: "media",
      label: "Farmer Thumbnail",
      mediaCardStyle: { display: 'inline-block', padding: '0px', margin: '0px', width: '225px', height: 'auto' },
      mediaCardBodyStyle: {padding: '12px'},
      mediaTagWrapperDivStyle: {position: 'relative', width: 'auto'},
      mediaTagStyle: { maxWidth: '200px', height: 'auto', position: 'relative'},
      // enableDownload: true,
      // enableViewInBrowser: true,
      enablePreview: true,
      enableUploadOfFiles: false,
      // uploadMultiple: false,
      uploadMaxCount: 1,
      uploadFolder: 'ah/farmer',
      uploadEntity: 'document',
      uploadEntityInformation: {
        document_type_id: 1000260003,
        entity_1_type_id: 1000220001,
        // entity_1_entity_uuid: ocItemListingUUID,
      },
      uploadEntityDocumentInformation: {
        entityType: 'farmer',
        localFolderName: 'farmer',
        serverFolderName: 'farmer',
        individualLocalFolderName: 'farmer',
        individualServerFolderName: 'farmer'
      }
    },
    animalThumbnail: {
      type: "media",
      label: "Animal Thumbnail",
      mediaCardStyle: { display: 'inline-block', padding: '0px', margin: '0px', width: '225px', height: 'auto' },
      mediaCardBodyStyle: {padding: '12px'},
      mediaTagWrapperDivStyle: {position: 'relative', width: 'auto'},
      mediaTagStyle: { maxWidth: '200px', height: 'auto', position: 'relative'},
      // enableDownload: true,
      // enableViewInBrowser: true,
      enablePreview: true,
      enableUploadOfFiles: false,
      // uploadMultiple: false,
      uploadMaxCount: 1,
      uploadFolder: 'ah/farmer',
      uploadEntity: 'document',
      uploadEntityInformation: {
        document_type_id: 1000260001,
        entity_1_type_id: 1000220002,
        // entity_1_entity_uuid: ocItemListingUUID,
      },
      uploadEntityDocumentInformation: {
        entityType: 'animal',
        localFolderName: 'animal',
        serverFolderName: 'animal',
        individualLocalFolderName: 'animal',
        individualServerFolderName: 'animal'
      }
    },
    animalEarTagPhoto: {
      type: "media",
      label: "Animal Ear Tag",
      mediaCardStyle: { display: 'inline-block', padding: '0px', margin: '0px', width: '225px', height: 'auto' },
      mediaCardBodyStyle: {padding: '12px'},
      mediaTagWrapperDivStyle: {position: 'relative', width: 'auto'},
      mediaTagStyle: { maxWidth: '200px', height: 'auto', position: 'relative'},
      // enableDownload: true,
      // enableViewInBrowser: true,
      enablePreview: true,
      enableUploadOfFiles: false,
      // uploadMultiple: false,
      uploadMaxCount: 1,
      uploadFolder: 'ah/animal',
      uploadEntity: 'document',
      uploadEntityInformation: {
        document_type_id: 1000260012,
        entity_1_type_id: 1000220002,
        // entity_1_entity_uuid: ocItemListingUUID,
      },
      uploadEntityDocumentInformation: {
        entityType: 'animal',
        localFolderName: 'animal',
        serverFolderName: 'animal',
        individualLocalFolderName: 'animal',
        individualServerFolderName: 'animal'
      }
    },
    farmerDairyFarmLocation: {
      type: "map-marker",
      label: "Farmer Dairy Farm Location",
      readOnly: 1,
      zoom: 13,
      markerTooltip: 'Dairy Farm Location',
      componentStyle: { height: '225px', width: '225px', marginLeft: '10px' }
    },
  },
  visibleFormControls: [
    "animalId",
    "animalVisualId",
    "animalEarTag",
    "animalBreedAndType",
    /*"animalAge",*/
    "customerTypeId",
    "farmerNameAndMobile",
    "farmerDistrictTalukVillage",
    "farmerBDMAndMobile",
    "farmerParavetAndMobile",
    "farmerThumbnail",
    "animalThumbnail",
    "animalEarTagPhoto",
    "farmerDairyFarmLocation",
  ],
  elementToDataMapping: {
    animalId: "animal_id",
    animalVisualId: 'animal_visual_id',
    animalEarTag: "animal_ear_tag",
    animalBreedAndType: "animal_breed_and_type",
    animalAge: "animal_age_1",
    customerTypeId: "customer_type_id",
    farmerNameAndMobile: 'customer_name_and_mobile',
    farmerDistrictTalukVillage: 'district_taluk_village',
    farmerBDMAndMobile: 'bdm_and_mobile',
    farmerParavetAndMobile: 'paravet_and_mobile',
    farmerThumbnail: 'customer_thumbnail',
    animalThumbnail: 'animal_thumbnail',
    animalEarTagPhoto: 'animal_ear_tag_photo',
    farmerDairyFarmLocation: "dairy_farm_location"
  },
  dataToElementMapping: {
    animal_id: 'animalId',
    animal_visual_id: 'animalVisualId',
    animal_ear_tag: 'animalEarTag',
    animal_breed_and_type: 'animalBreedAndType',
    animal_age_1: 'animalAge',
    customer_type_id: "customerTypeId",
    customer_name_and_mobile: 'farmerNameAndMobile',
    district_taluk_village: 'farmerDistrictTalukVillage',
    bdm_and_mobile: 'farmerBDMAndMobile',
    paravet_and_mobile: 'farmerParavetAndMobile',
    customer_thumbnail: 'farmerThumbnail',
    animal_thumbnail: 'animalThumbnail',
    animal_ear_tag_photo: 'animalEarTagPhoto',
    dairy_farm_location: 'farmerDairyFarmLocation'
  },
}

const animalBasicDetailsFormInitialState = {
}

const newAnimalFormDefinition = {
  formControls: {
    farmerNameAndMobile: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Name And Mobile",
      readOnly: 1,
    },
    customerTypeId: {
      ocFormRef: createRef(),
      type: "positive-number_2",
      label: "Customer Type Id",
      visible: false,
      readOnly: 1,
    },
    farmerDistrictTalukVillage: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer District Taluk Village",
      readOnly: 1,
    },
    animalId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Id",
      readOnly: 1,
    },
    animalEarTag: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Ear Tag",
    },
    animalName: {
      ocFormRef: createRef(),
      type: "text-l10n_2",
      label: "Animal Name",
    },
    animalType: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "Animal Type",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      errorMessage: 'Animal Type is mandatory',
      referenceCategoryId: 10001400,
      validations: [
        { type: "Mandatory" },
      ],
    },
    animalBreed: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "Animal Breed",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      errorMessage: 'Animal Breed is mandatory',
      referenceCategoryId: 10001500,
      validations: [
        { type: "Mandatory" },
      ],
    },
  },
  visibleFormControls: [
    "farmerNameAndMobile",
    "customerTypeId",
    "farmerDistrictTalukVillage",
    "animalId",
    "animalEarTag",
    "animalName",
    "animalType",
    "animalBreed",
  ],
  elementToDataMapping: {
    farmerNameAndMobile: "customer_name_and_mobile",
    customerTypeId: "customer_type_id",
    farmerDistrictTalukVillage: "customer_district_taluk_village",
    animalId: "animal_id",
    animalEarTag: 'ear_tag_1',
    animalType: 'animal_type',
    animalBreed: 'animal_breed_1',
  },
  dataToElementMapping: {
    customer_name_and_mobile: "farmerNameAndMobile",
    customer_type_id: "customerTypeId",
    customer_district_taluk_village: "farmerDistrictTalukVillage",
    animal_id: "animalId",
    ear_tag_1: 'animalEarTag',
    animal_type: 'animalType',
    animal_breed_1: 'animalBreed',
  },
}

const cowScoreDetailsFormAdditionalVisibleControlsForNonCentralParavet = [
  'MinimumSellingPrice',
  'MaximumSellingPrice',
  'MinimumBuyingPrice',
  'MaximumBuyingPrice'
]

const cowScoreDetailsFormAdditionalVisibleControlsForCentralParavet = [
  "latestAnimalWeight",
  "latestAnimalWeightDate",
  "latestLPD",
  "latestLPDDate",
  "maximumLPD",
  "maximumLPDDate",
  "numberOfCalvings",
  "animalAgeToday",
  "lastCalvingDate",
  "numberOfMonthsPregnant",
  'HealthScoreVerificationStatusDropdown',
]

const cowScoreDetailsFormDefinition = {
  formControls: {
    bsTailAndPinBoneDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Tail and Pin Bone',
      readOnly: 1
    },
    /* bsTailAndPinBoneDouble2: {
      type: 'positive-decimal',
      label: 'Tail and Pin Bone',
      // readOnly: 1
    }, */
    bsTailAndPinBoneDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Tail and Pin Bone',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Tail and Pin Bone',
      referenceCategoryId: 10006600,
      // readOnly: 1,
    },
    bsBackBoneDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Back Bone',
      readOnly: 1
    },
    bsBackBoneDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Back Bone',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Back Bone',
      referenceCategoryId: 10006700,
      // readOnly: 1,
    },
    bsHipAndPinBoneDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Hip And Pin Bone',
      readOnly: 1
    },
    bsHipAndPinBoneDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Hip And Pin Bone',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Hip And Pin Bone',
      referenceCategoryId: 10006800,
      // readOnly: 1,
    },
    UdderCleftVisibleDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Cleft Visible',
      readOnly: 1
    },
    UdderCleftVisibleDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Cleft Visible',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Cleft Visible',
      referenceCategoryId: 10006200,
      // readOnly: 1,
    },
    UdderDepthOrPositionDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Udder Depth or Position',
      readOnly: 1
    },
    UdderDepthOrPositionDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Udder Depth or Position',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Udder Depth or Position',
      referenceCategoryId: 10005900,
      // readOnly: 1,
    },
    UdderBalanceDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Udder Balance',
      readOnly: 1
    },
    UdderBalanceDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Udder Balance',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Udder Balance',
      referenceCategoryId: 10006100,
      // readOnly: 1,
    },
    TeatPlacementAndDirectionDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Teat Placement And Direction',
      readOnly: 1
    },
    TeatPlacementAndDirectionDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Teat Placement And Direction',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Teat Placement And Direction',
      referenceCategoryId: 10006300,
      // readOnly: 1,
    },
    TeatLengthDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Teat Length',
      readOnly: 1
    },
    TeatLengthDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Teat Length',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Teat Length',
      referenceCategoryId: 10006400,
      // readOnly: 1,
    },
    GapInTeethDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Gap in Teeth',
      readOnly: 1
    },
    GapInTeethDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Gap in Teeth',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Gap in Teeth',
      referenceCategoryId: 10007700,
      // readOnly: 1,
    },
    HornsExistDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Horns Exist Or Not',
      readOnly: 1
    },
    HornsExistDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Horns Exist Or Not',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Horns Exist Or Not',
      referenceCategoryId: 10007800,
      // readOnly: 1,
    },
    SpreadOfPatchesDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Spread of Patches',
      readOnly: 1
    },
    SpreadOfPatchesDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Spread of Patches',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Spread of Patches',
      referenceCategoryId: 10005600,
      // readOnly: 1,
    },
    UdderStretchabilityDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Udder Strechability',
      readOnly: 1
    },
    UdderStretchabilityDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Udder Strechability',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Udder Strechability',
      referenceCategoryId: 10005700,
      // readOnly: 1,
    },
    VeinsVisibilityDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Veins Visibility',
      readOnly: 1
    },
    VeinsVisibilityDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Veins Visibility',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Veins Visibility',
      referenceCategoryId: 10005800,
      // readOnly: 1,
    },
    AdditionalTeatDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Additional Teat',
      readOnly: 1
    },
    AdditionalTeatDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Additional Teat',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Additional Teat',
      referenceCategoryId: 10008100,
      // readOnly: 1,
    },
    ForeignBodyAreaInt: {
      ocFormRef: createRef(),
      type: 'positive-number_2',
      label: 'Foreign Body',
      readOnly: 1
    },
    ForeignBodyAreaDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Foreign Body',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Foreign Body',
      referenceCategoryId: 10008000,
      // readOnly: 1,
    },
    LocomotionScoreDouble: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Locomotion Score',
      readOnly: 1
    },
    LocomotionScoreDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Animal Locomotion',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Locomotion Type',
      referenceCategoryId: 10006500,
      // readOnly: 1,
    },
    BodyScore: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Body Score',
      readOnly: 1
    },
    UdderAndTeatScore: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Udder And Teat Score',
      readOnly: 1
    },
    HealthScore: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Animal Health Score',
      readOnly: 1
    },
    MinimumSellingPrice: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Minimum Selling Price',
      readOnly: 1
    },
    MaximumSellingPrice: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Maximum Selling Price',
      readOnly: 1
    },
    MinimumBuyingPrice: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Minimum Buying Price',
      readOnly: 1
    },
    MaximumBuyingPrice: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Maximum Buying Price',
      readOnly: 1
    },
    HealthScoreVerificationStatusDropdown: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Health Score Verification Status',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Status',
      referenceCategoryId: 10008900,
      // readOnly: 1,
    },
    latestAnimalWeight: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Latest Recorded Weight",
      placeHolderText: "Enter weight kg",
      errorMessage: "Weight should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    latestAnimalWeightDate: {
      ocFormRef: createRef(),
      type: "date_2",
      label: "Latest Weight Recorded Date",
      readOnly: 1,
    },
    numberOfCalvings: {
      ocFormRef: createRef(),
      type: "positive-number_2",
      label: "Number of Calvings",
      placeHolderText: "Enter weight kg",
      errorMessage: "Number of Calvings should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    lastCalvingDate: {
      ocFormRef: createRef(),
      type: "date_2",
      label: "Last Calving Date",
      readOnly: 1,
    },
    numberOfMonthsPregnant: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Number of Months Pregnant",
      placeHolderText: "Enter no. of months pregnant",
      errorMessage: "Number of months pregnant should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    latestLPD: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Average LPD of Animal",
      placeHolderText: "Enter average LPD",
      errorMessage: "LPD should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    latestLPDDate: {
      ocFormRef: createRef(),
      type: "date_2",
      label: "Date of Collection of Average LPD of Animal",
      readOnly: 1,
    },
    maximumLPD: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Maximum (not average) LPD of Animal",
      placeHolderText: "Enter maximum LPD",
      errorMessage: "LPD should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    maximumLPDDate: {
      ocFormRef: createRef(),
      type: "date_2",
      label: "Date of Collection of Maximum LPD of Animal",
      readOnly: 1,
    },
    animalAgeToday: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Animal Age",
    },
  },
  visibleFormControls: [
    // 'bsTailAndPinBoneDouble',
    // 'bsTailAndPinBoneDouble2',
    'bsTailAndPinBoneDropdown',
    // 'bsBackBoneDouble',
    'bsBackBoneDropdown',
    // 'bsHipAndPinBoneDouble',
    'bsHipAndPinBoneDropdown',
    // 'UdderCleftVisibleDouble',
    'UdderCleftVisibleDropdown',
    // 'UdderDepthOrPositionDouble',
    'UdderDepthOrPositionDropdown',
    // 'UdderBalanceDouble',
    'UdderBalanceDropdown',
    // 'TeatPlacementAndDirectionDouble',
    'TeatPlacementAndDirectionDropdown',
    // 'TeatLengthDouble',
    'TeatLengthDropdown',
    // 'GapInTeethDouble',
    'GapInTeethDropdown',
    // 'HornsExistDouble',
    'HornsExistDropdown',
    // 'SpreadOfPatchesDouble',
    'SpreadOfPatchesDropdown',
    // 'UdderStretchabilityDouble',
    'UdderStretchabilityDropdown',
    // 'VeinsVisibilityDouble',
    'VeinsVisibilityDropdown',
    // 'AdditionalTeatDouble',
    'AdditionalTeatDropdown',
    // 'ForeignBodyAreaInt',
    'ForeignBodyAreaDropdown',
    // 'LocomotionScoreDouble',
    'LocomotionScoreDropdown',
    'BodyScore',
    'UdderAndTeatScore',
    'HealthScore',
    
 /**/  ],
  elementToDataMapping: {
    bsTailAndPinBoneDouble: 'bs_tail_and_pin_bone_double',
    // bsTailAndPinBoneDouble2: 'bs_tail_and_pin_bone_double',
    bsTailAndPinBoneDropdown: 'bs_tail_and_pin_bone_dropdown',
    bsBackBoneDouble: 'bs_back_bone_double',
    bsBackBoneDropdown: 'bs_back_bone_dropdown',
    bsHipAndPinBoneDouble: 'bs_hip_and_pin_bone_double',
    bsHipAndPinBoneDropdown: 'bs_hip_and_pin_bone_dropdown',
    UdderCleftVisibleDouble: 'uts_udder_cleft_visibility_double',
    UdderCleftVisibleDropdown: 'uts_udder_cleft_visibility_dropdown',
    UdderDepthOrPositionDouble: 'uts_udder_position_double',
    UdderDepthOrPositionDropdown: 'uts_udder_position_dropdown',
    UdderBalanceDouble: 'uts_udder_balance_double',
    UdderBalanceDropdown: 'uts_udder_balance_dropdown',
    TeatPlacementAndDirectionDouble: 'uts_udder_teat_placement_double',
    TeatPlacementAndDirectionDropdown: 'uts_udder_teat_placement_dropdown',
    TeatLengthDouble: 'uts_udder_teat_length_double',
    TeatLengthDropdown: 'uts_udder_teat_length_dropdown',
    GapInTeethDouble: 'ap_gap_in_teeth_double',
    GapInTeethDropdown: 'ap_gap_in_teeth_dropdown',
    HornsExistDouble: 'ap_horns_exist_or_not_double',
    HornsExistDropdown: 'ap_horns_exist_or_not_dropdown',
    SpreadOfPatchesDouble: 'ap_spread_of_patches_double',
    SpreadOfPatchesDropdown: 'ap_spread_of_patches_dropdown',
    UdderStretchabilityDouble: 'aphs_stretchability_of_udder_double',
    UdderStretchabilityDropdown: 'aphs_stretchability_of_udder_dropdown',
    VeinsVisibilityDouble: 'aphs_visibility_of_veins_double',
    VeinsVisibilityDropdown: 'aphs_visibility_of_veins_dropdown',
    AdditionalTeatDouble: 'aphs_additional_teats_double',
    AdditionalTeatDropdown: 'aphs_additional_teats_dropdown',
    ForeignBodyAreaInt: 'hs_foreign_body_area_double',
    ForeignBodyAreaDropdown: 'hs_foreign_body_area_dropdown',
    LocomotionScoreDouble: 'animal_locomotion_score_double',
    LocomotionScoreDropdown: 'animal_locomotion_score_dropdown',
    BodyScore: 'animal_body_score_1',
    UdderAndTeatScore: 'animal_udder_score_1',
    HealthScore: 'animal_health_score',
    MinimumSellingPrice: 'minimum_selling_price',
    MaximumSellingPrice: 'maximum_selling_price',
    MinimumBuyingPrice: 'minimum_buying_price',
    MaximumBuyingPrice: 'maximum_buying_price',
    HealthScoreVerificationStatusDropdown: 'latest_health_score_verification_status',
    latestAnimalWeight: "animal_weight_1",
    latestAnimalWeightDate: "animal_weight_1_updated_at",
    numberOfCalvings: "number_of_calvings_1",
    numberOfMonthsPregnant: "number_of_months_pregnant",
    lastCalvingDate: "last_calving_date_1",
    latestLPD: "animal_average_lpd_1",
    latestLPDDate: "animal_average_lpd_1_updated_at",
    maximumLPD: "max_lpd_of_animal_1",
    maximumLPDDate: "max_lpd_of_animal_1_updated_at",
    animalAgeToday: "animal_age_today",
  },
  dataToElementMapping: {
    bs_tail_and_pin_bone_double: 'bsTailAndPinBoneDouble',
    // bs_tail_and_pin_bone_double: 'bsTailAndPinBoneDouble2',
    bs_tail_and_pin_bone_dropdown: 'bsTailAndPinBoneDropdown',
    bs_back_bone_double: 'bsBackBoneDouble',
    bs_back_bone_dropdown: 'bsBackBoneDropdown',
    bs_hip_and_pin_bone_double: 'bsHipAndPinBoneDouble',
    bs_hip_and_pin_bone_dropdown: 'bsHipAndPinBoneDropdown',
    uts_udder_cleft_visibility_double: 'UdderCleftVisibleDouble',
    uts_udder_cleft_visibility_dropdown: 'UdderCleftVisibleDropdown',
    uts_udder_position_double: 'UdderDepthOrPositionDouble',
    uts_udder_position_dropdown: 'UdderDepthOrPositionDropdown',
    uts_udder_balance_double: 'UdderBalanceDouble',
    uts_udder_balance_dropdown: 'UdderBalanceDropdown',
    uts_udder_teat_placement_double: 'TeatPlacementAndDirectionDouble',
    uts_udder_teat_placement_dropdown: 'TeatPlacementAndDirectionDropdown',
    uts_udder_teat_length_double: 'TeatLengthDouble',
    uts_udder_teat_length_dropdown: 'TeatLengthDropdown',
    ap_gap_in_teeth_double: 'GapInTeethDouble',
    ap_gap_in_teeth_dropdown: 'GapInTeethDropdown',
    ap_horns_exist_or_not_double: 'HornsExistDouble',
    ap_horns_exist_or_not_dropdown: 'HornsExistDropdown',
    ap_spread_of_patches_double: 'SpreadOfPatchesDouble',
    ap_spread_of_patches_dropdown: 'SpreadOfPatchesDropdown',
    aphs_stretchability_of_udder_double: 'UdderStretchabilityDouble',
    aphs_stretchability_of_udder_dropdown: 'UdderStretchabilityDropdown',
    aphs_visibility_of_veins_double: 'VeinsVisibilityDouble',
    aphs_visibility_of_veins_dropdown: 'VeinsVisibilityDropdown',
    aphs_additional_teats_double: 'AdditionalTeatDouble',
    aphs_additional_teats_dropdown: 'AdditionalTeatDropdown',
    hs_foreign_body_area_double: 'ForeignBodyAreaInt',
    hs_foreign_body_area_dropdown: 'ForeignBodyAreaDropdown',
    animal_locomotion_score_double: 'LocomotionScoreDouble',
    animal_locomotion_score_dropdown: 'LocomotionScoreDropdown',
    animal_body_score_1: 'BodyScore',
    animal_udder_score_1: 'UdderAndTeatScore',
    animal_health_score: 'HealthScore',
    minimum_selling_price: 'MinimumSellingPrice',
    maximum_selling_price: 'MaximumSellingPrice',
    minimum_buying_price: 'MinimumBuyingPrice',
    maximum_buying_price: 'MaximumBuyingPrice',
    latest_health_score_verification_status: 'HealthScoreVerificationStatusDropdown',
    animal_weight_1: "latestAnimalWeight",
    animal_weight_1_updated_at: "latestAnimalWeightDate",
    number_of_calvings_1: "numberOfCalvings",
    number_of_months_pregnant: "numberOfMonthsPregnant",
    last_calving_date_1: "lastCalvingDate",
    animal_average_lpd_1: "latestLPD",
    animal_average_lpd_1_updated_at: "latestLPDDate",
    max_lpd_of_animal_1: "maximumLPD",
    max_lpd_of_animal_1_updated_at: "maximumLPDDate",
    animal_age_today: "animalAgeToday",
  },
}

const cowScoreDetailsFormInitialState = {
}

const animalExchangeBasicDataFormDefinition = {
  formControls: {
    latestAnimalWeight: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Latest Recorded Weight",
      placeHolderText: "Enter weight kg",
      errorMessage: "Weight should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    latestAnimalWeightDate: {
      ocFormRef: createRef(),
      type: "date_2",
      label: "Latest Weight Recorded Date",
      readOnly: 1,
    },
    numberOfCalvings: {
      ocFormRef: createRef(),
      type: "positive-number_2",
      label: "Number of Calvings",
      placeHolderText: "Enter weight kg",
      errorMessage: "Number of Calvings should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    lastCalvingDate: {
      ocFormRef: createRef(),
      type: "date_2",
      label: "Last Calving Date",
      readOnly: 1,
    },
    numberOfMonthsPregnant: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Number of Months Pregnant",
      placeHolderText: "Enter no. of months pregnant",
      errorMessage: "Number of months pregnant should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    latestLPD: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Average LPD of Animal",
      placeHolderText: "Enter average LPD",
      errorMessage: "LPD should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    latestLPDDate: {
      ocFormRef: createRef(),
      type: "date_2",
      label: "Date of Collection of Average LPD of Animal",
      readOnly: 1,
    },
    maximumLPD: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Maximum (not average) LPD of Animal",
      placeHolderText: "Enter maximum LPD",
      errorMessage: "LPD should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    maximumLPDDate: {
      ocFormRef: createRef(),
      type: "date_2",
      label: "Date of Collection of Maximum LPD of Animal",
      readOnly: 1,
    },
    animalAgeToday: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Animal Age",
    },
  },
  visibleFormControls: [
    "latestAnimalWeight",
    "latestAnimalWeightDate",
    "numberOfCalvings",
    "lastCalvingDate",
    "numberOfMonthsPregnant",
    "latestLPD",
    "latestLPDDate",
    "maximumLPD",
    "maximumLPDDate",
    "animalAgeToday"
  ],
  elementToDataMapping: {
    latestAnimalWeight: "animal_weight_1",
    latestAnimalWeightDate: "animal_weight_1_updated_at",
    numberOfCalvings: "number_of_calvings_1",
    numberOfMonthsPregnant: "number_of_months_pregnant",
    lastCalvingDate: "last_calving_date_1",
    latestLPD: "animal_average_lpd_1",
    latestLPDDate: "animal_average_lpd_1_updated_at",
    maximumLPD: "max_lpd_of_animal_1",
    maximumLPDDate: "max_lpd_of_animal_1_updated_at",
    animalAgeToday: "animal_age_today"
  },
  dataToElementMapping: {
    animal_weight_1: "latestAnimalWeight",
    animal_weight_1_updated_at: "latestAnimalWeightDate",
    number_of_calvings_1: "numberOfCalvings",
    number_of_months_pregnant: "numberOfMonthsPregnant",
    last_calving_date_1: "lastCalvingDate",
    animal_average_lpd_1: "latestLPD",
    animal_average_lpd_1_updated_at: "latestLPDDate",
    max_lpd_of_animal_1: "maximumLPD",
    max_lpd_of_animal_1_updated_at: "maximumLPDDate",
    animal_age_today: "animalAgeToday"
  },
}

const animalExchangeBasicDataFormInitialState = {
}

const smartRationDataFormDefinition = {
  formControls: {
    latestAnimalWeight: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Latest Recorded Weight",
      placeHolderText: "Enter weight kg",
      errorMessage: "Weight should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    latestAnimalWeightDate: {
      ocFormRef: createRef(),
      type: "date_2",
      label: "Latest Weight Recorded Date",
      readOnly: 1,
    },
    pregnancyStatus: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "Pregnancy Status",
      placeHolderText: "Select Pregnancy Status",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      referenceCategoryId: 10001050,
    },
    numberOfMonthsPregnant: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Number of Months Pregnant",
      placeHolderText: "Enter no. of months pregnant",
      errorMessage: "Number of months pregnant should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    monthsSinceLastCalving: {
      ocFormRef: createRef(),
      type: "positive-number_2",
      label: "Months Since Last Calving",
      placeHolderText: "Enter months since last calvig",
      errorMessage: "Months since last calving should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    latestLPD: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Average LPD of Animal",
      placeHolderText: "Enter average LPD",
      errorMessage: "LPD should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    latestLPDDate: {
      ocFormRef: createRef(),
      type: "date_2",
      label: "Date of Collection of Average LPD of Animal",
      readOnly: 1,
    },
    milkFatPercentage: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Milk Fat Percentage",
      placeHolderText: "Enter Milk Fat %",
      errorMessage: "Milk Fat % should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    animalAgeToday: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Animal Age",
      errorMessage: "Age should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    numberOfCalvings: {
      ocFormRef: createRef(),
      type: "positive-number_2",
      label: "Number of Calvings",
      errorMessage: "Number of Calvings should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    }
  },
  visibleFormControls: [
    "latestAnimalWeight",
    "latestAnimalWeightDate",
    "pregnancyStatus",
    "numberOfMonthsPregnant",
    "monthsSinceLastCalving",
    "latestLPD",
    "latestLPDDate",
    "milkFatPercentage",
    "animalAgeToday",
    "numberOfCalvings",
  ],
  elementToDataMapping: {
    latestAnimalWeight: "animal_weight_1",
    latestAnimalWeightDate: "animal_weight_1_updated_at",
    pregnancyStatus: "animal_preganancy_1",
    numberOfMonthsPregnant: "number_of_months_pregnant",
    monthsSinceLastCalving: "months_since_last_calving",
    latestLPD: "animal_average_lpd_1",
    latestLPDDate: "animal_average_lpd_1_updated_at",
    milkFatPercentage: 'animal_milk_fat_1',
    animalAgeToday: "animal_age_today",
    numberOfCalvings: "number_of_calvings_1",
  },
  dataToElementMapping: {
    animal_weight_1: "latestAnimalWeight",
    animal_weight_1_updated_at: "latestAnimalWeightDate",
    animal_preganancy_1: "pregnancyStatus",
    number_of_months_pregnant: "numberOfMonthsPregnant",
    months_since_last_calving: "monthsSinceLastCalving",
    animal_average_lpd_1: "latestLPD",
    animal_average_lpd_1_updated_at: "latestLPDDate",
    animal_milk_fat_1: 'milkFatPercentage',
    animal_age_today: "animalAgeToday",
    number_of_calvings_1: "numberOfCalvings",
  },
}

const smartRationDataFormInitialState = {
}

const customerServiceTaskFormDefinition = {
  forceNewLineAfterControl: true,
  defaultClasses: 'mb-3',
  formControls: {
    animalId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Id",
      readOnly: 1,
    },
    animalEarTag: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Animal Ear Tag",
      readOnly: 1,
    },
    farmerId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Id",
      readOnly: 1,
    },
    farmerNameAndMobileNumber: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Name And Mobile Number",
      readOnly: 1,
    },
    activityDate: {
      ocFormRef: createRef(),
      type: "date-time_2",
      // type: "date_2",
      label: "Complete By",
      placeHolderText: "Enter Date",
      errorMessage: "Completion By Date which is beyond today is Mandatory!",
      dateEditableText: true,
      validations: [{ type: "Mandatory" }, {type: "MinimumDate", value: new Date()}],
    },
    activityDetails: {
      ocFormRef: createRef(),
      type: "text-area_2",
      // type: "text_2",
      label: "Activity Details",
      placeHolderText: "Enter Activity Details",
      errorMessage: "Activity Details is Mandatory!",
      validations: [{ type: "Mandatory" }],
    },
  },
  visibleFormControls: [
    "animalId",
    "animalEarTag",
    "farmerNameAndMobileNumber",
    "activityDate",
    "activityDetails",
  ],
  elementToDataMapping: {
    animalId: "animal_id",
    animalEarTag: "animal_ear_tag",
    farmerNameAndMobileNumber: "customer_name_and_mobile",
    activityDate: "activity_date",
    activityDetails: "activity_details",
  },
  dataToElementMapping: {
    animal_id: "animalId",
    animal_ear_tag: "animalEarTag",
    customer_name_and_mobile: "farmerNameAndMobileNumber",
    activity_date: "activityDate",
    activity_details: "activityDetails",
  },
}

const CowScoreDetailsTab = () => {
  const userToken = useSelector((state)=>state.user.userToken)
  const params = useParams()
  const animalId = params.animalId
  const [searchParams] = useSearchParams()
  const [loading, setLoading] = useState(false)
  const isCentralVet = searchParams && searchParams.get('role') && searchParams.get('role') === 'cv'
  const additionalVisibleControls = (isCentralVet) ? cowScoreDetailsFormAdditionalVisibleControlsForCentralParavet : cowScoreDetailsFormAdditionalVisibleControlsForNonCentralParavet
  let addAdditionalVisibleControlsToExistingFormDefinition = true
  for (const additionalVisibleControl of additionalVisibleControls) {
    if (cowScoreDetailsFormDefinition.visibleFormControls.includes(additionalVisibleControl)) {
      addAdditionalVisibleControlsToExistingFormDefinition = false
    }
  }
  if (addAdditionalVisibleControlsToExistingFormDefinition) {
    cowScoreDetailsFormDefinition.visibleFormControls = [...cowScoreDetailsFormDefinition.visibleFormControls, ...additionalVisibleControls]
  }

  const [cowScoreDetailsFormState, setCowScoreDetailsFormState] = useState(cowScoreDetailsFormInitialState)
  
  const loadDataAsync = async () => {
    /* const newFormState = assignDataToFormState(
      cowScoreDetailsFormDefinition,
      {
        // bs_tail_and_pin_bone_dropdown_list: [{reference_name_l10n:{ul: 'Filled'}, reference_id: 1000660001}, {reference_name_l10n:{ul: 'Slightly Sunken'}, reference_id: 1000660002}, {reference_name_l10n:{ul: 'Sunken'}, reference_id: 1000660003}],
        bs_tail_and_pin_bone_dropdown_list: [{reference_name_l10n:'Filled', reference_id: 1000660001}, {reference_name_l10n:'Slightly Sunken', reference_id: 1000660002}, {reference_name_l10n:'Sunken', reference_id: 1000660003}],
        // farmer_level_snooze_choice_list: [{label:'Yes', id: 1}, {label:'No', id: 2}],
      },
      cowScoreDetailsFormState,
      setCowScoreDetailsFormState
    ) */
    try {
      const additionalClassifierKeysToLoad = ['cattle_exchange_price', 'number_of_months_pregnant_1', 'months_pregnant_as_of_date', 'animal_age_1', 'age_of_animal_as_of_1']
      const fieldsToExclude = ['MinimumSellingPrice', 'MaximumSellingPrice', 'MinimumBuyingPrice', 'MaximumBuyingPrice', 'numberOfMonthsPregnant', 'latestLPDDate', 'animalAgeToday', 'latestAnimalWeightDate']
      let classifierKeyArray = cowScoreDetailsFormDefinition.visibleFormControls.map(formField => {
        if (!fieldsToExclude.includes(formField)) {
          return cowScoreDetailsFormDefinition.elementToDataMapping[formField]
        }
      })
      classifierKeyArray = classifierKeyArray.concat(additionalClassifierKeysToLoad)
      const cowScoreDetailsRequestResponse = await getAnimalData(userToken, animalId, classifierKeyArray)
      if (cowScoreDetailsRequestResponse.return_code === 0) {
        const cattleExchangePrice = cowScoreDetailsRequestResponse.data['cattle_exchange_price']
        const sellingMinimumPrice = cattleExchangePrice ? cattleExchangePrice['sellingPriceLowerRange'] : undefined
        const sellingMaximumPrice = cattleExchangePrice ? cattleExchangePrice['sellingPriceUpperRange'] : undefined
        const buyingMinimumPrice = cattleExchangePrice ? cattleExchangePrice['buyingPriceLowerRange'] : undefined
        const buyingMaximumPrice = cattleExchangePrice ? cattleExchangePrice['buyingPriceUpperRange'] : undefined
        const newData = {...cowScoreDetailsRequestResponse.data,
          minimum_selling_price: sellingMinimumPrice,
          maximum_selling_price: sellingMaximumPrice,
          minimum_buying_price: buyingMinimumPrice,
          maximum_buying_price: buyingMaximumPrice
        }
        const noOfMonthsPregnant = newData['number_of_months_pregnant_1']
        const noOfMonthsPregnantAsOfDate = newData['months_pregnant_as_of_date']
        if (noOfMonthsPregnant && noOfMonthsPregnantAsOfDate && noOfMonthsPregnant !== 0) {
          const numberOfMonthsPregnantToday = moment().diff(moment(noOfMonthsPregnantAsOfDate), 'months') + noOfMonthsPregnant
          if (numberOfMonthsPregnantToday < 9) {
            newData['number_of_months_pregnant'] = numberOfMonthsPregnantToday
          }
        }
        const animalAge = newData['animal_age_1']
        const animalAgeAsOf = newData['age_of_animal_as_of_1']
        if (animalAge && animalAgeAsOf) {
          const animalAgeToday = moment().diff(moment(animalAgeAsOf), 'years') + animalAge
          newData['animal_age_today'] = animalAgeToday
        }
        assignDataToFormState(
          cowScoreDetailsFormDefinition,
          newData,
          cowScoreDetailsFormState,
          setCowScoreDetailsFormState
        )
      }
    } catch (error) {
      ToastersService.failureToast('Could not load data')
    } finally {
    }
  }

  const saveCowScoreData = async () => {
    /* const [currentFormState, validationFailed] = validateFormElements(
      cowScoreDetailsFormDefinition,
      cowScoreDetailsFormState,
      setCowScoreDetailsFormState
    ) */
    try {
      setLoading(true)
      const [currentFormState, validationFailed] = validateFormElements2(
        cowScoreDetailsFormDefinition,
        cowScoreDetailsFormState,
        setCowScoreDetailsFormState
      )
      if (validationFailed) {
        // do something
        console.log("Validation Failed")
        ToastersService.failureToast("Validation failed!")
      } else {
        console.log("you can save now")
        const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(cowScoreDetailsFormDefinition, currentFormState, setCowScoreDetailsFormState)
        const extractedValues = extractChangedValues(cowScoreDetailsFormDefinition, updatedCurrentFormState)
        console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValues)
        if (Object.keys(extractedValues).includes('number_of_months_pregnant')) {
          extractedValues['number_of_months_pregnant_1'] = extractedValues['number_of_months_pregnant']
          // extractedValues['number_of_months_pregnant_as_of_date_1'] = new Date()
        }
        delete extractedValues['number_of_months_pregnant']
        if (Object.keys(extractedValues).includes('animal_age_today')) {
          extractedValues['animal_age_1'] = extractedValues['animal_age_today']
          extractedValues['age_of_animal_as_of_1'] = new Date()
        }
        delete extractedValues['animal_age_today']
        console.log("AF1 sF 2, extractedValuesTab0 = ", extractedValues)
        const saveAnimalResponse = await setAnimalData(userToken, animalId, extractedValues)
        console.log('AF1 sF 2, response = ', saveAnimalResponse)
        if (saveAnimalResponse.return_code === 0) {
          await loadDataAsync(false)
          ToastersService.successToast("Data Updated Successfully")
        } else {
          ToastersService.failureToast("Failed to Update Data")
        }
      }
    } catch (error) {
      ToastersService.failureToast('Could not score cow score data')
    } finally {
      setLoading(false)
    }
  }

  useEffect(()=>{
    assignReferencesAsListToForm(cowScoreDetailsFormDefinition)
    loadDataAsync()
  },[])

  return (
    <>
      {<KrushalOCForm
        formState={cowScoreDetailsFormState}
        setFormState={setCowScoreDetailsFormState}
        formDefinition={cowScoreDetailsFormDefinition}
      />}
      <Button
        onClick={() => saveCowScoreData()}
        >
        Save
      </Button>
      <Button
        onClick={async () => {
          const response = await handleCalculateHealthScoreClick(userToken, animalId)
          if (response && response.return_code === 0) {
            ToastersService.successToast("Healthscore Successfully Calculated")
            await loadDataAsync()
          } else {
            ToastersService.failureToast("Failed to Calculate Healthscore")
          }
        }}
        >
        Recalculate Health Score
      </Button>
      {isCentralVet ? null : (
        <Button
          onClick={async () => {
            const response = await handleGenerateSellablePriceOfCattleExchangeAnimalClick(userToken, animalId)
            if (response && response.return_code === 0) {
              ToastersService.successToast("Price Successfully Calculated")
            } else {
              let errorCodes = []
              if (response && response.animal_price_generation_results && Array.isArray(response.animal_price_generation_results) && response.animal_price_generation_results.length > 0) {
                const animalResponse = response.animal_price_generation_results[0]
                errorCodes = errorCodes.concat(animalResponse.animal_price_generation_warnings)
                errorCodes = errorCodes.concat(animalResponse.animal_price_generation_errors)
              }
              if (errorCodes.length > 0) {
                const errorCodesCommaSeparated = errorCodes.join(', ')
                ToastersService.failureToast("Error codes: " + errorCodesCommaSeparated)
              }
              if (response && response.data && response.data.return_code === -1001) {
                ToastersService.failureToast("System Configuration Problem, please call system admin")
              } else if (response && response.data && response.data.return_code === -1003) {
                ToastersService.failureToast("Prices for some animals were generated successfully")
              } else if (response && response.data && response.data.return_code === -1002) {
                ToastersService.failureToast("Prices for no animals were generated successfully")
              } else {
                ToastersService.failureToast("Failed to Calculate Price")
              }
            }
            await loadDataAsync()
          }}
          >
          Generate Price For Cattle Exchange
        </Button>
      )}
    </>
    
  )
}

const AnimalExchangeBasicDataTab = () => {
  const userToken = useSelector((state)=>state.user.userToken)
  const params = useParams()
  const animalId = params.animalId

  const [animalExchangeBasicDataFormState, setAnimalExchangeBasicDataFormState] = useState(animalExchangeBasicDataFormInitialState)
  
  const loadTabDataAsync = async () => {
    const classifierKeyArray = []
    const fieldsToNotLoad = ['numberOfMonthsPregnant', 'latestLPDDate', 'maximumLPDDate', 'animalAgeToday', 'latestAnimalWeightDate']
    for (const formField of animalExchangeBasicDataFormDefinition.visibleFormControls) {
      if (!fieldsToNotLoad.includes(formField)) {
        classifierKeyArray.push(animalExchangeBasicDataFormDefinition.elementToDataMapping[formField])
      }
    }
    classifierKeyArray.push('number_of_months_pregnant_1')
    classifierKeyArray.push('months_pregnant_as_of_date')
    classifierKeyArray.push('animal_age_1')
    classifierKeyArray.push('age_of_animal_as_of_1')
    const animalExchangeBasicDataRequestResponse = await getAnimalData(userToken, animalId, classifierKeyArray)
    if (animalExchangeBasicDataRequestResponse.return_code === 0) {
      const newData = {...animalExchangeBasicDataRequestResponse.data,
        // bs_tail_and_pin_bone_dropdown_list: [{reference_name_l10n:'Filled', reference_id: 1000660001}, {reference_name_l10n:'Slightly Sunken', reference_id: 1000660002}, {reference_name_l10n:'Sunken', reference_id: 1000660003}],
      }
      const noOfMonthsPregnant = newData['number_of_months_pregnant_1']
      const noOfMonthsPregnantAsOfDate = newData['months_pregnant_as_of_date']
      if (noOfMonthsPregnant && noOfMonthsPregnantAsOfDate && noOfMonthsPregnant !== 0) {
        const numberOfMonthsPregnantToday = moment().diff(moment(noOfMonthsPregnantAsOfDate), 'months') + noOfMonthsPregnant
        if (numberOfMonthsPregnantToday < 9) {
          newData['number_of_months_pregnant'] = numberOfMonthsPregnantToday
        }
      }
      const animalAge = newData['animal_age_1']
      const animalAgeAsOf = newData['age_of_animal_as_of_1']
      if (animalAge && animalAgeAsOf) {
        const animalAgeToday = moment().diff(moment(animalAgeAsOf), 'years') + animalAge
        newData['animal_age_today'] = animalAgeToday
      }
      assignDataToFormState(
        animalExchangeBasicDataFormDefinition,
        newData,
        animalExchangeBasicDataFormState,
        setAnimalExchangeBasicDataFormState
      )
    }
  }
  
  const saveTabData = async () => {
    /* const [currentFormState, validationFailed] = validateFormElements(
      animalExchangeBasicDataFormDefinition,
      animalExchangeBasicDataFormState,
      setAnimalExchangeBasicDataFormState
    ) */
    const [currentFormState, validationFailed] = validateFormElements2(
      animalExchangeBasicDataFormDefinition,
      animalExchangeBasicDataFormState,
      setAnimalExchangeBasicDataFormState
    )
    if (validationFailed) {
      // do something
      console.log("Validation Failed");
      ToastersService.failureToast("Validation failed!")
    } else {
      console.log("you can save now")
      const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(animalExchangeBasicDataFormDefinition, currentFormState, setAnimalExchangeBasicDataFormState)
      const extractedValues = extractChangedValues(animalExchangeBasicDataFormDefinition, updatedCurrentFormState)
      if (Object.keys(extractedValues).includes('number_of_months_pregnant')) {
        extractedValues['number_of_months_pregnant_1'] = extractedValues['number_of_months_pregnant']
        // extractedValues['number_of_months_pregnant_as_of_date_1'] = new Date()
      }
      delete extractedValues['number_of_months_pregnant']
      if (Object.keys(extractedValues).includes('animal_age_today')) {
        extractedValues['animal_age_1'] = extractedValues['animal_age_today']
        extractedValues['age_of_animal_as_of_1'] = new Date()
      }
      delete extractedValues['animal_age_today']
      console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValues)
      const saveAnimalResponse = await setAnimalData(userToken, animalId, extractedValues)
      if (saveAnimalResponse.return_code === 0) {
        ToastersService.successToast("Data Updated Successfully")
      } else {
        ToastersService.failureToast("Failed to Update Data")
      }
    }
  }

  useEffect(()=>{
    loadTabDataAsync()
  },[])

  return (
    <>
      <KrushalOCForm
        formState={animalExchangeBasicDataFormState}
        setFormState={setAnimalExchangeBasicDataFormState}
        formDefinition={animalExchangeBasicDataFormDefinition}
      />
      <Button
        onClick={() => saveTabData()}
        >
        Save
      </Button>
    </>
    
  )
}

const SmartRationDataTab = () => {
  const userToken = useSelector((state)=>state.user.userToken)
  const params = useParams()
  const animalId = params.animalId

  const [smartRationDataFormState, setSmartRationDataFormState] = useState(smartRationDataFormInitialState)
  
  const loadTabDataAsync = async () => {
    // months_since_last_calving: "monthsSinceLastCalving",
    const classifierKeyArray = []
    const fieldsToNotLoad = ['monthsSinceLastCalving', 'numberOfMonthsPregnant', 'latestLPDDate', 'animalAgeToday', 'latestAnimalWeightDate']
    for (const formField of smartRationDataFormDefinition.visibleFormControls) {
      if (!fieldsToNotLoad.includes(formField)) {
        classifierKeyArray.push(smartRationDataFormDefinition.elementToDataMapping[formField])
      }
    }
    classifierKeyArray.push('months_since_last_calving_1')
    classifierKeyArray.push('month_since_calving_as_of_data_1')
    classifierKeyArray.push('number_of_months_pregnant_1')
    classifierKeyArray.push('months_pregnant_as_of_date')
    classifierKeyArray.push('animal_age_1')
    classifierKeyArray.push('age_of_animal_as_of_1')
    const animalExchangeBasicDataRequestResponse = await getAnimalData(userToken, animalId, classifierKeyArray)
    if (animalExchangeBasicDataRequestResponse.return_code === 0) {
      const newData = {...animalExchangeBasicDataRequestResponse.data,
        // bs_tail_and_pin_bone_dropdown_list: [{reference_name_l10n:'Filled', reference_id: 1000660001}, {reference_name_l10n:'Slightly Sunken', reference_id: 1000660002}, {reference_name_l10n:'Sunken', reference_id: 1000660003}],
      }
      const noOfMonthsPregnant = newData['number_of_months_pregnant_1']
      const noOfMonthsPregnantAsOfDate = newData['months_pregnant_as_of_date']
      if (noOfMonthsPregnant && noOfMonthsPregnantAsOfDate && noOfMonthsPregnant !== 0) {
        const numberOfMonthsPregnantToday = moment().diff(moment(noOfMonthsPregnantAsOfDate), 'months') + noOfMonthsPregnant
        if (numberOfMonthsPregnantToday < 9) {
          newData['number_of_months_pregnant'] = numberOfMonthsPregnantToday
        }
      }

      const monthsSinceLastCalving = newData['months_since_last_calving_1']
      const monthsSinceLastCalvingAsOfDate = newData['month_since_calving_as_of_data_1']
      if (monthsSinceLastCalving && monthsSinceLastCalvingAsOfDate && monthsSinceLastCalving !== 0) {
        const monthsSinceLastCalvingToday = moment().diff(moment(monthsSinceLastCalvingAsOfDate), 'months') + monthsSinceLastCalving
        newData['months_since_last_calving'] = monthsSinceLastCalvingToday
      }

      const animalAge = newData['animal_age_1']
      const animalAgeAsOf = newData['age_of_animal_as_of_1']
      if (animalAge && animalAgeAsOf) {
        const animalAgeToday = moment().diff(moment(animalAgeAsOf), 'years') + animalAge
        newData['animal_age_today'] = animalAgeToday
      }

      if (newData['animal_milk_fat_1'] === undefined) {
        newData['animal_milk_fat_1'] = 3.5
      }
      assignDataToFormState(
        smartRationDataFormDefinition,
        newData,
        smartRationDataFormState,
        setSmartRationDataFormState
      )
    }
  }
  
  const saveTabData = async () => {
    // months_since_last_calving: "monthsSinceLastCalving",
    /* const [currentFormState, validationFailed] = validateFormElements(
      smartRationDataFormDefinition,
      smartRationDataFormState,
      setSmartRationDataFormState
    ); */
    const [currentFormState, validationFailed] = validateFormElements2(
      smartRationDataFormDefinition,
      smartRationDataFormState,
      setSmartRationDataFormState
    )
    if (validationFailed) {
      // do something
      console.log("Validation Failed");
      ToastersService.failureToast("Validation failed!");
    } else {
      console.log("you can save now");
      const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(smartRationDataFormDefinition, currentFormState, setSmartRationDataFormState)
      const extractedValues = extractChangedValues(smartRationDataFormDefinition, updatedCurrentFormState)
      if (Object.keys(extractedValues).includes('number_of_months_pregnant')) {
        extractedValues['number_of_months_pregnant_1'] = extractedValues['number_of_months_pregnant']
        // extractedValues['number_of_months_pregnant_as_of_date_1'] = new Date()
      }
      delete extractedValues['number_of_months_pregnant']

      if (Object.keys(extractedValues).includes('animal_age_today')) {
        extractedValues['animal_age_1'] = extractedValues['animal_age_today']
        extractedValues['age_of_animal_as_of_1'] = new Date()
      }
      delete extractedValues['animal_age_today']

      if (Object.keys(extractedValues).includes('months_since_last_calving')) {
        extractedValues['months_since_last_calving_1'] = extractedValues['months_since_last_calving']
      }
      delete extractedValues['months_since_last_calving']

      console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValues)
      const saveAnimalResponse = await setAnimalData(userToken, animalId, extractedValues)
      if (saveAnimalResponse.return_code === 0) {
        ToastersService.successToast("Data Updated Successfully")
      } else {
        ToastersService.failureToast("Failed to Update Data")
      }
    }
  }

  useEffect(()=>{
    loadTabDataAsync()
  },[])

  return (
    <>
      <KrushalOCForm
        formState={smartRationDataFormState}
        setFormState={setSmartRationDataFormState}
        formDefinition={smartRationDataFormDefinition}
      />
      <Button
        onClick={() => saveTabData()}
        >
        Save
      </Button>
    </>
    
  )
}

const defaultTabItems = [
  {
    key: '1',
    label: 'Cow Score Details',
    children: (
      <CowScoreDetailsTab/>
    ),
  },
  {
    key: '2',
    label: 'Animal Exchange Basic Data',
    // children: 'Content of Finance, et al',
    children: (
      <AnimalExchangeBasicDataTab/>
    ),
  },
  {
    key: '11',
    label: 'Finance, et al',
    children: 'Content of Finance, et al',
  },
]

const smartRationTabItems = [
  {
    key: '3',
    label: 'Smart Ration Data',
    // children: 'Content of Finance, et al',
    children: (
      <SmartRationDataTab/>
    ),
  },
  {
    key: '11',
    label: 'Finance, et al',
    children: 'Content of Finance, et al',
  },
]

const OCAnimalDetails = () => {
  const params = useParams()
  const [searchParams] = useSearchParams()
  const location = useLocation()
  const { pathname, search } = location

  const navigate = useNavigate()
  // const routes = useRoutes()
  // const matches = useMatches()
  const isSmartRationRole = searchParams && searchParams.get('role') && searchParams.get('role') === 'sr'
  const userToken = useSelector((state)=>state.user.userToken)

  // const [searchParams] = useSearchParams()
  // const uncompressedFilterParamsJsonString = decodeURIComponent(searchParams.get('qP'))// urlEncodedJson)
  // const queryParams = JSON.parse(uncompressedFilterParamsJsonString)
  // const customerId = queryParams.customerId
  const customerId = params.customerId
  const animalId = params.animalId
  const addAnimalView = (animalId === '-1')
  animalBasicDetailsFormDefinition.formControls.farmerThumbnail.uploadEntityInformation.entity_1_entity_uuid = customerId
  animalBasicDetailsFormDefinition.formControls.animalThumbnail.uploadEntityInformation.entity_1_entity_uuid = animalId
  animalBasicDetailsFormDefinition.formControls.animalEarTagPhoto.uploadEntityInformation.entity_1_entity_uuid = animalId

  const [animalBasicDetailsFormState, setAnimalBasicDetailsFormState] = useState(animalBasicDetailsFormInitialState);
  const [customerServiceTaskModalDialogVisibility, setCustomerServiceTaskModalDialogVisibility] = useState(false)
  const [customerServiceTaskFormState, setCustomerServiceTaskFormState] = useState({})
  const [formResetValue, setFormResetValue] = useState(0)
  const [loading, setLoading] = useState(false)

  const tabItems = ((animalBasicDetailsFormState['customerTypeId'] && animalBasicDetailsFormState['customerTypeId']['value'] === 1000220009) || isSmartRationRole ) ? smartRationTabItems : defaultTabItems
  const defaultTabKeyInDetailsSection = ((animalBasicDetailsFormState['customerTypeId'] && animalBasicDetailsFormState['customerTypeId']['value'] === 1000220009) || isSmartRationRole) ? '3' : '1'
  const tabKeyFromURL = (params.tabKey !== undefined ? params.tabKey : defaultTabKeyInDetailsSection)

  let defaultPanelOpenKey = ['1']
  if (addAnimalView) {
    defaultPanelOpenKey = ['1']
  }

  const setBasicData = async () => {
    const response = await getBasicDataOfAnimal(userToken, animalId)
    if (response.return_code === 0) {
      assignDataToFormState(
        animalBasicDetailsFormDefinition,
        response.result,
        animalBasicDetailsFormState,
        setAnimalBasicDetailsFormState
      )
    }
  }

  const getDataForCreatingAnimal = async () => {
    // for customer, get name number, customer type, district taluk village
    const basicFarmerDataResponse = await loadBasicFarmerData(userToken, customerId)
    if (basicFarmerDataResponse.return_code === 0) {
      const basicFarmerDetails = lodashObject.cloneDeep(basicFarmerDataResponse.result)
      const farmerNameAndMobile = extractBasedOnLanguage(basicFarmerDetails['customer_name']) + ' ' + basicFarmerDetails['mobile_number']
      basicFarmerDetails['customer_name_and_mobile'] = farmerNameAndMobile
      let farmerDistrictTalukVillage = basicFarmerDetails['district_name'] + ' ' + basicFarmerDetails['taluk_name']
      if (basicFarmerDetails['village_name'] && basicFarmerDetails['village_name'] !== null) {
        farmerDistrictTalukVillage = farmerDistrictTalukVillage + basicFarmerDetails['village_name']
      }
      basicFarmerDetails['customer_district_taluk_village'] = farmerDistrictTalukVillage
      assignDataToFormState(
        newAnimalFormDefinition,
        basicFarmerDetails,
        animalBasicDetailsFormState,
        setAnimalBasicDetailsFormState
      )
    }
  }

  const addAnimal = async () => {
    try {
      setLoading(true)
      const [currentFormState, validationFailed] = validateFormElements2(
        newAnimalFormDefinition,
        animalBasicDetailsFormState,
        setAnimalBasicDetailsFormState
      )
      if (validationFailed) {
        // do something
        console.log("Validation Failed")
        ToastersService.failureToast("Validation failed!")
      } else {
        console.log("additional checks")
        const animalEarTag = getValueFormControl(newAnimalFormDefinition, 'animalEarTag')
        const animalName = getValueFormControl(newAnimalFormDefinition, 'animalName')
        const customerTypeId = animalBasicDetailsFormState['customerTypeId']['value']
        if ((animalName === undefined || animalName === '') && (animalEarTag === undefined || animalEarTag === '')) {
          ToastersService.failureToast("Need to identify eartag or name")
        } else {
          console.log("you can save now")
          const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(newAnimalFormDefinition, currentFormState, setAnimalBasicDetailsFormState)
          const extractedValues = extractChangedValues(newAnimalFormDefinition, updatedCurrentFormState)
          const fieldsToIgnoreInSaving = ['customer_name_and_mobile', 'customer_type_id', 'customer_district_taluk_village', 'animal_id']
          for (const fieldChanged of Object.keys(extractedValues)) {
            if (fieldsToIgnoreInSaving.includes(fieldChanged)) {
              delete extractedValues[fieldChanged]
            }
          }
          extractedValues['customer_type_id'] = customerTypeId
          extractedValues['customer_id'] = customerId
          const setAnimalDataResponse = await setAnimalData(userToken, undefined, extractedValues)
          if (setAnimalDataResponse.return_code === 0) {
            const newAnimalIndicatorString = '-1'
            const animalId = setAnimalDataResponse.animalId
            const lastIndex = pathname.lastIndexOf(newAnimalIndicatorString)
            const newPathName = pathname.substring(0, lastIndex) + animalId + pathname.substring(lastIndex + newAnimalIndicatorString.length)
            navigate({
              // pathname: `../e/farmer-animal-details/${customerId}/animal/${animalId}/${tabKey}`,
              pathname: newPathName,
              // search: search,
              replace: true
            })
            setAnimalBasicDetailsFormState(animalBasicDetailsFormInitialState)
            setFormResetValue(formResetValue + 1)
            ToastersService.successToast("Successfully updated animal data!")
          } else {
            ToastersService.failureToast("Failed to update farmer data!")
          }
        }
      }
    } catch (error) {
      ToastersService.failureToast('Could not add animal')
    } finally {
      setLoading(false)
    }
  }

  const asyncFormResetValue = async () => {
    try {
      setLoading(true)
      assignReferencesAsListToForm(smartRationDataFormDefinition)
      if (addAnimalView) {
        assignReferencesAsListToForm(newAnimalFormDefinition)
        await getDataForCreatingAnimal()
      } else {
        await setBasicData()
      }
    } catch (error) {
      ToastersService.failureToast('Could not load data')
    } finally {
      setLoading(false)
    }
  }

  useEffect(()=>{
    asyncFormResetValue()
  },[formResetValue])

  const extractParts = (input, animalId) => {
    const regex1 = new RegExp("(.*)?\/e\/(.*)\/animal\/" + animalId + "\/(.*)")
    const regex2 = new RegExp("(.*)?\/e\/(.*)\/animal\/" + animalId)
    // const regex = new RegExp(/(.*)\/e(.*)/);
    let fMatch = input.match(regex1);
    if (fMatch === null) {
      fMatch = input.match(regex2);
    }
    return {
      tillE: fMatch[1],
      eToAnimal: fMatch[2],
      currentTabNumber: fMatch[3]
      // animalToQuestionMark,
      // questionMarkAndBeyond,
    };
  }

  const onTabChange = (tabKey) => {
    console.log('OCFD oTC 1, tabKey = ', tabKey)
    const { pathname, search } = location
    // setTabKey(tabKey)
    const extractedParts = extractParts(pathname, animalId)
    const newPathName = `/e/${extractedParts.eToAnimal}/animal/${animalId}/${tabKey}`
    navigate({
      // pathname: `../e/farmer-animal-details/${customerId}/animal/${animalId}/${tabKey}`,
      pathname: newPathName,
      search: search,
      replace: true
    })
  }

  const onCustomerServiceTaskModalDialogOK = async () => {
    try {
      /* const [currentFormState, validationFailed] = validateFormElements(
        customerServiceTaskFormDefinition,
        customerServiceTaskFormState,
        setCustomerServiceTaskFormState
      ) */
      const [currentFormState, validationFailed] = validateFormElements2(
        customerServiceTaskFormDefinition,
        customerServiceTaskFormState,
        setCustomerServiceTaskFormState
      )
      if (validationFailed) {
        // do something
        console.log("Validation Failed");
        ToastersService.failureToast("Validation failed!");
      } else {
        console.log("you can save now")
        const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(customerServiceTaskFormDefinition, currentFormState, setCustomerServiceTaskFormState)
        const extractedValues = extractChangedValues(customerServiceTaskFormDefinition, updatedCurrentFormState, ['animalId'])
        console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValues)
        const response = await createCustomerServiceTask(userToken, extractedValues)
        console.log('AF1 sF 2, response = ', response)
        if (response.return_code === 0) {
          ToastersService.successToast("Data Updated Successfully")
          setCustomerServiceTaskModalDialogVisibility(false)
        } else {
          ToastersService.failureToast("Failed to Update Data")
        }
      }
    } catch (error) {
      console.log('OCAD oCSTMDO 10, error')
      console.log('OCAD oCSTMDO 10a, error = ', error)
      ToastersService.failureToast("Failed to create task for Customer Service");
    }
  }

  const handleAddCustomerServiceTaskButtonClick = () => {
    assignDataToFormState(
      customerServiceTaskFormDefinition,
      {animal_id: animalBasicDetailsFormState["animalId"].value, animal_ear_tag: animalBasicDetailsFormState["animalEarTag"].value, customer_name_and_mobile: animalBasicDetailsFormState["farmerNameAndMobile"].value},
      customerServiceTaskFormState,
      setCustomerServiceTaskFormState
    )
    setCustomerServiceTaskModalDialogVisibility(true)
  }

  return (userToken === null || userToken === undefined) ? null : (
    <>
      <Modal
        title="Create Task for Customer Service"
        open={customerServiceTaskModalDialogVisibility}
        onOk={onCustomerServiceTaskModalDialogOK}
        onCancel={() => {setCustomerServiceTaskModalDialogVisibility(false)}}
        >
          <>
            <KrushalOCForm
              formState={customerServiceTaskFormState}
              setFormState={setCustomerServiceTaskFormState}
              formDefinition={customerServiceTaskFormDefinition}
            />
          </>
      </Modal>
      <Space>
        <Link to={`/e/farmer-details/${customerId}`}>Back to FarmerDetails</Link>
        {addAnimalView ? null : (
          <Button size="small" style={{ marginTop: 8, marginBottom: 8 }} onClick={handleAddCustomerServiceTaskButtonClick}>Add Customer Service Task</Button>
        )}
      </Space>
      <Spin spinning={loading} tip="Loading...">
        <Collapse defaultActiveKey={defaultPanelOpenKey} >
          <Panel header="Basic Animal Details" key="1">
            <KrushalOCForm
              formState={animalBasicDetailsFormState}
              setFormState={setAnimalBasicDetailsFormState}
              formDefinition={addAnimalView ? newAnimalFormDefinition : animalBasicDetailsFormDefinition}
            />
            {!(addAnimalView && animalBasicDetailsFormState['customerTypeId'] !== 1000220009) ? null : (
              <Button
                onClick={() => addAnimal()}
                >
                Add Animal
              </Button>
            )}
          </Panel>
          {addAnimalView ? null : (
            <Panel header="All Images" key="2">
              <MediaCarousel
                key='pbffa-aa'
                mediaConfiguration={
                  {
                    completeInformation: (animalId === undefined) ? false : true,
                    showLatestOnlyFlag: true,
                    enableLatestOnlyFlag: true,
                    latestOnlyFlag: false,
                    enablePreview: true,
                    enableUploadOfFiles: true,
                    uploadFolder: 'ah/animal',
                    // uploadEntity: 'document',
                    uploadEntity: 'document',
                    styleContainer: {
                      mediaCardStyle: { display: 'inline-block', padding: '0px', margin: '0px', width: '225px', height: 'auto' },
                      mediaCardBodyStyle: {padding: '12px'},
                      mediaTagWrapperDivStyle: {position: 'relative', width: 'auto'},
                      mediaTagWrapperOnHoverDivStyle: undefined,
                      mediaTagPreviewOverlayStyle: undefined,
                      mediaTagPreviewOverlayOnHoverStyle: undefined,
                      mediaTagStyle: { maxWidth: '200px', height: 'auto', position: 'relative'},
                    },
                    uploadEntityInformation: {
                      entity_1_type_id: 1000220002,
                      entity_1_entity_uuid: animalId,
                    },
                    uploadEntityDocumentInformation: {
                      entityType: 'animal',
                      localFolderName: 'animal',
                      serverFolderName: 'animal',
                      individualLocalFolderName: 'animal',
                      individualServerFolderName: 'animal'
                    },
                    documentTypesForFilter: [
                      {label:'Animal Thumbnail Photo', value:1000260001},
                      {label:'Animal Photo Any Angle', value:1000260002},
                      {label:'Animal Eartag Photo', value:1000260012},
                      {label:'Foreign Body Area', value:1000260013},
                      {label:'Animal classification documents', value:1000260016},
                    ],
                    documentTypesForUpload: [
                      {label:'Animal Thumbnail Photo', value:1000260001},
                      {label:'Animal classification documents', value:1000260016},
                    ],
                    document_query_information : {
                      entity_name: 'animal',
                      related_additional_entity_types: ['animal_classification']
                    },
                    base_entity_information: {
                      entity_1_type_id: 1000220002,
                      entity_uuid: animalId
                    }
                  }
                }
              />
            </Panel>
          )}
          {addAnimalView ? null : (
            <Panel header="Details In Tabs Section" key="3">
              <Tabs 
                // defaultActiveKey="1"
                defaultActiveKey={defaultTabKeyInDetailsSection}
                activeKey={tabKeyFromURL}
                items={tabItems}
                destroyInactiveTabPane={true}
                onChange={onTabChange}
                />
            </Panel>
          )}
        </Collapse>
      </Spin>
    </>
    
  )
}

export default OCAnimalDetails