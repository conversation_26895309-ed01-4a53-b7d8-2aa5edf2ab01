import { useEffect, useState, createRef } from "react";
import { Link, useNavigate, useParams, useSearchParams,  } from "react-router-dom";
import {useSelector} from "react-redux"
import { <PERSON><PERSON>, <PERSON><PERSON>, Flex } from 'antd'
import moment from 'moment';

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  validateFormElements2,
  assignValuesFromControlReferencesToFormState,
  extractChangedValues,
  assignReferencesAsListToForm,
} from "../../Components/Common/KrushalOCForm";
import ToastersService from "../../Services/toasters.service";

const lodashObject = require("lodash");

const { MediaCarousel } = require('../../Components/Common/MediaComponents')

const { extractBasedOnLanguage } = require('@krushal-it/common-core')
const { getMedicineData, saveMedicineData, } = require('../../router')

const { OCTable, addRefToEditableTableItemsIfNotAvailable,
          addCounterToTableRows,
          deleteKeysFromObjectInObjectArray, deleteRefKeysFromObjectInObjectArray, extractEditableTableDelta,
          extractTableListWithUpdatedValues } = require('../../Components/Common/AntTableHelper')

const medicineDetailsFormDefinition = {
  formControls: {
    medicineId: {
      ocFormRef: createRef(),
      type: "positive-number_2",
      label: "Medicine Id",
      readOnly: 1,
      placeHolderText: 'Medicine Id',
    },
    medicineName: {
      ocFormRef: createRef(),
      type: "text-l10n_2",
      label: "Medicine Name",
      placeHolderText: 'Enter Name',
    },
    medicineMRP: {
      ocFormRef: createRef(),
      type: "positive-decimal_2",
      label: "Medicine MRP",
      placeHolderText: 'Enter Number',
    },
    medicineUnit: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Medicine Unit',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Unit',
      referenceCategoryId: 10009500,
    },
    medicineForm: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Medicine Form',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Form',
      referenceCategoryId: 10009600,
    },
    medicinePacksize: {
      ocFormRef: createRef(),
      type: 'positive-decimal_2',
      label: 'Pack Size',
      placeHolderText: 'Enter Number',
    },
    medicineUsedByFlag: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Medicine Used By Flag',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Used By Flag',
      referenceCategoryId: 10011000,
    },
    medicineActiveFlag: {
      ocFormRef: createRef(),
      type: 'single-select-l10n_2',
      label: 'Medicine Active or Not',
      labelKeyInList: 'reference_name_l10n',
      valueKeyInList: 'reference_id',
      placeHolderText: 'Select Active Flag',
      referenceCategoryId: 10001000,
    },
    medicineZohoItemId: {
      ocFormRef: createRef(),
      type: 'text_2',
      label: 'Zoho Item Id',
      placeHolderText: 'Enter Zoho Item Id',
    },
  },
  visibleFormControls: [
    "medicineId",
    "medicineName",
    "medicineMRP",
    "medicineUnit",
    "medicineForm",
    "medicinePacksize",
    "medicineUsedByFlag",
    "medicineActiveFlag",
    "medicineZohoItemId",
  ],
  elementToDataMapping: {
    medicineId: 'medicine_id',
    medicineName: 'medicine_name_l10n',
    medicineMRP: 'medicine_mrp',
    medicineUnit: 'medicine_unit',
    medicineForm: 'medicine_form',
    medicinePacksize: 'medicine_pack_size',
    medicineUsedByFlag: 'medicine_used_by_flag',
    medicineActiveFlag: 'medicine_active_id',
    medicineZohoItemId: 'medicine_zoho_item_id',
  },
  dataToElementMapping: {
    medicine_id: 'medicineId',
    medicine_name_l10n: 'medicineName',
    medicine_mrp: 'medicineMRP',
    medicine_unit: 'medicineUnit',
    medicine_form: 'medicineForm',
    medicine_pack_size: 'medicinePacksize',
    medicine_used_by_flag: 'medicineUsedByFlag',
    medicine_active_id: 'medicineActiveFlag',
    medicine_zoho_item_id: 'medicineZohoItemId',
  },
}

const medicineDetailsFormInitialState = {
}

const loadMedicineData = async (userToken, medicineId) => {
  try {
    console.log('OCMD lMD 1')
    const headers = {
      useCase: 'Get Basic Farmer Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getMedicineData(headers, medicineId)
    return response.data
  } catch (error) {
    console.log('OCMD lMD 10, error')
    console.log('OCMD lMD 10, error = ', error)
    throw error
  }
}

const updateMedicineData = async (userToken, medicineId, medicineDetails) => {
  try {
    console.log('OCMD sMD 1')
    const headers = {
      useCase: 'Update Animal Classification Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const {medicine_name_l10n, medicine_active_id, ...medicineClassificationDetails } = medicineDetails
    const serverObject = {medicineId: medicineId}
    if (medicine_name_l10n) {
      serverObject.medicine_name_l10n = medicine_name_l10n
    }
    if (medicine_active_id) {
      serverObject.medicine_active_id = medicine_active_id
    }
    if (Object.keys(medicineClassificationDetails).length > 0) {
      serverObject.classificationData = medicineClassificationDetails
    } else {
      serverObject.classificationData = {}
    }
    const responseWithData = await saveMedicineData(headers, serverObject)
    const response = responseWithData.data
    console.log('OCMD sMD 2, response = ', response)
    if (response.return_code === 0) {
      ToastersService.successToast("Data Updated Successfully")
    } else {
      ToastersService.failureToast("Failed to Update Data")
    }
  } catch (error) {
    console.log('OCMD sMD 10, error')
    console.log('OCMD sMD 10a, error = ', error)
    throw error
  }
}

const medicineDocumentInitialConfiguration = {
  // completeInformation: (medicineId === undefined) ? false : true,
  completeInformation: true,
  showLatestOnlyFlag: false,
  enableLatestOnlyFlag: true,
  latestOnlyFlag: false,
  enablePreview: true,
  enableUploadOfFiles: true,
  uploadFolder: 'ah/medicine',
  // uploadEntity: 'document',
  uploadEntity: 'document',
  styleContainer: {
    mediaCardStyle: { display: 'inline-block', padding: '0px', margin: '0px', width: '225px', height: 'auto' },
    mediaCardBodyStyle: {padding: '12px'},
    mediaTagWrapperDivStyle: {position: 'relative', width: 'auto'},
    mediaTagWrapperOnHoverDivStyle: undefined,
    mediaTagPreviewOverlayStyle: undefined,
    mediaTagPreviewOverlayOnHoverStyle: undefined,
    mediaTagStyle: { maxWidth: '200px', height: 'auto', position: 'relative'},
  },
  uploadEntityInformation: {
    entity_1_type_id: 1000460013,
  },
  uploadEntityDocumentInformation: {
    entityType: 'item',
    localFolderName: 'item',
    serverFolderName: 'item',
    individualLocalFolderName: 'item',
    individualServerFolderName: 'item'
  },
  documentTypesForFilter: [
    {label:'Medicine Thumbnail Photo', value:1000260020},
  ],
  documentTypesForUpload: [
    {label:'Medicine Thumbnail Photo', value:1000260020},
  ],
  base_entity_information: {
    entity_1_type_id: 1000460013
  }
}

const OCMedicineDetails = () => {
  const params = useParams()
  const navigate = useNavigate()
  const userToken = useSelector((state)=>state.user.userToken)

  const medicineId = parseInt(params.medicineId)
  const medicineDocumentConfiguration = lodashObject.cloneDeep(medicineDocumentInitialConfiguration)
  medicineDocumentConfiguration.uploadEntityInformation.entity_1_entity_id = medicineId
  medicineDocumentConfiguration.base_entity_information.entity_id = medicineId
  
  const [medicineDetailsFormState, setMedicineDetailsFormState] = useState(medicineDetailsFormInitialState);
  
  const assignMedicineListFromServerToState = async () => {
    const response = await loadMedicineData(userToken, medicineId)
    if (response.return_code === 0) {
      assignDataToFormState(
        medicineDetailsFormDefinition,
        response.result,
        medicineDetailsFormState,
        setMedicineDetailsFormState
      )
    }
  }

  const saveMedicineData = async () => {
    /* const [currentFormState, validationFailed] = validateFormElements(
      medicineDetailsFormDefinition,
      medicineDetailsFormState,
      setMedicineDetailsFormState
    ) */
    const [currentFormState, validationFailed] = validateFormElements2(
      medicineDetailsFormDefinition,
      medicineDetailsFormState,
      setMedicineDetailsFormState
    )
    if (validationFailed) {
      // do something
      console.log("Validation Failed");
      ToastersService.failureToast("Validation failed!");
    } else {
      console.log("you can save now")
      const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(medicineDetailsFormDefinition, currentFormState, setMedicineDetailsFormState)
      const extractedValues = extractChangedValues(medicineDetailsFormDefinition, updatedCurrentFormState)
      console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValues)
      updateMedicineData(userToken, medicineId, extractedValues)
    }
  }
  useEffect(()=>{
    assignReferencesAsListToForm(medicineDetailsFormDefinition)
    assignMedicineListFromServerToState()
  },[])

  return (
    <>
      <KrushalOCForm
        formState={medicineDetailsFormState}
        setFormState={setMedicineDetailsFormState}
        formDefinition={medicineDetailsFormDefinition}
      />
      <MediaCarousel
        key='pbffa-aa'
        mediaConfiguration={
          medicineDocumentConfiguration
        }
      />
      <Button
        onClick={() => saveMedicineData()}
        >
        Save
      </Button>
    </>
    
  )
}

export default OCMedicineDetails