import { useEffect, useState, useRef, createRef } from "react";
import { Link, useNavigate, useLocation, useParams, useSearchParams,  } from "react-router-dom";
import {useSelector} from "react-redux"
import { Spin, Space, Tabs, Select, Button, Collapse, Flex } from 'antd'
import moment from 'moment';

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  validateFormElements2,
  assignValuesFromControlReferencesToFormState,
  extractChangedValues,
  assignReferencesAsListToForm,
  resetSelectWithNewOptionsInForm,
  getValueFormControl
} from "../../Components/Common/KrushalOCForm";
import ToastersService from "../../Services/toasters.service";
import { ca } from "date-fns/locale";

const { Option } = Select
const { Panel } = Collapse
const lodashObject = require("lodash")

const { extractBasedOnLanguage, assignL10NObjectOrObjectArrayToItSelf } = require('@krushal-it/common-core')

const { EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../Components/user/authorize')

const { getFarmerBasicData, getCustomerInformationBasedOnClassifiers,
          getAnimalsForFarmer, updateCustomerInformationBasedOnClassifiers,
          getCropDetailedData, saveCropDetailedData, getGeographyListing } = require('../../router')

const { loadSmartRationReport, getSmartRationDataCreateReportUploadToS3AndCreateDocument, getReportTypeForFileNameAndDocumentTypeIdForReportIdReportTypeIdArray, collateAnimalInformationForSmartRation, generateSmartRationReportFarmerAnimals } = require('../../SmartRation/SmartRationHelper')

const { OCTable, addRefToEditableTableItemsIfNotAvailable,
          addCounterToTableRows,
          deleteKeysFromObjectInObjectArray, deleteRefKeysFromObjectInObjectArray, extractEditableTableDelta,
          extractTableListWithUpdatedValues } = require('../../Components/Common/AntTableHelper')

const getGeographyDropdownData = async (userToken, geographyListingConfiguration) => {
  try {
    const headers = {
      useCase: 'Get Villages',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await getVillageList(headers, talukId)
    const response = await getGeographyListing(headers, geographyListingConfiguration)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadBasicFarmerData = async (userToken, customerId) => {
  try {
    const headers = {
      useCase: 'Get Basic Farmer Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getFarmerBasicData(headers, customerId)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getFarmerData = async (userToken, customerId, classifierKeyArray) => {
  try {
    console.log('OCFD gFD 1')
    const headers = {
      useCase: 'Get Farmers Data Based On Classification',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getCustomerInformationBasedOnClassifiers(headers, {customerId: customerId, classifierArray: classifierKeyArray})
    return response.data
  } catch (error) {
    console.log('OCFD gFD 10, error')
    console.log('OCFD gFD 10a, error = ', error)
    throw error
  }
}

const setFarmerData = async (userToken, customerId, classificationData) => {
  try {
    console.log('OCFD sFD 1')
    const headers = {
      useCase: 'Update Farmer Classification Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await updateCustomerInformationBasedOnClassifiers(headers, {customerId: customerId, classificationData: classificationData})
    return response.data
  } catch (error) {
    console.log('OCFD sFD 10, error')
    console.log('OCFD sFD 10a, error = ', error)
    throw error
  }
}

const getCropScreenData = async (userToken, customerId) => {
  try {
    console.log('OCFD gFD 1')
    const headers = {
      useCase: 'Get Farmer Crop Related Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getCropDetailedData(headers, customerId)
    return response.data
  } catch (error) {
    console.log('OCFD gFD 10, error')
    console.log('OCFD gFD 10a, error = ', error)
    throw error
  }
}

const setCropScreenData = async (userToken, customerId, extractedValues, farmListOrDeltaObject, periodToCropsListDeltaObject, seasonCropAcreageListEditableTableDeltaObject) => {
  try {
    console.log('OCFD gFD 1')
    const headers = {
      useCase: 'Set Farmer Crop Related Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const dataToBeSaved = {
      customer_id: customerId,
      farmer_classification_data: extractedValues
    }
    if (farmListOrDeltaObject !== undefined) {
      // {anyChange, toBeInsertedList, toBeUpdatedListKeyMap, toBeDeletedKeyList}      
      if ((farmListOrDeltaObject.anyChange !== undefined && farmListOrDeltaObject.anyChange === true)
        || farmListOrDeltaObject.anyChange === undefined) {
        if (farmListOrDeltaObject.toBeInsertedList !== undefined) {
          dataToBeSaved.farm_list_to_be_inserted_list = farmListOrDeltaObject.toBeInsertedList
        }
        if (farmListOrDeltaObject.toBeUpdatedListKeyMap !== undefined) {
          dataToBeSaved.farm_list_to_be_updated_key_map = farmListOrDeltaObject.toBeUpdatedListKeyMap
        }
        if (farmListOrDeltaObject.toBeDeletedKeyList !== undefined) {
          dataToBeSaved.farm_list_to_be_deleted_key_list = farmListOrDeltaObject.toBeDeletedKeyList
        }
      }
    }
    if (periodToCropsListDeltaObject !== undefined) {
      // {anyChange, toBeInsertedList, toBeUpdatedListKeyMap, toBeDeletedKeyList}      
      if ((periodToCropsListDeltaObject.anyChange !== undefined && periodToCropsListDeltaObject.anyChange === true)
        || periodToCropsListDeltaObject.anyChange === undefined) {
        if (periodToCropsListDeltaObject.toBeInsertedList !== undefined) {
          dataToBeSaved.period_list_to_be_inserted_list = periodToCropsListDeltaObject.toBeInsertedList
        }
        if (periodToCropsListDeltaObject.toBeUpdatedListKeyMap !== undefined) {
          dataToBeSaved.period_list_to_be_updated_key_map = periodToCropsListDeltaObject.toBeUpdatedListKeyMap
        }
        if (periodToCropsListDeltaObject.toBeDeletedKeyList !== undefined) {
          dataToBeSaved.period_list_to_be_deleted_key_list = periodToCropsListDeltaObject.toBeDeletedKeyList
        }
      }
    }
    if (seasonCropAcreageListEditableTableDeltaObject !== undefined) {
      // {anyChange, toBeInsertedList, toBeUpdatedListKeyMap, toBeDeletedKeyList}      
      if ((seasonCropAcreageListEditableTableDeltaObject.anyChange !== undefined && seasonCropAcreageListEditableTableDeltaObject.anyChange === true)
        || seasonCropAcreageListEditableTableDeltaObject.anyChange === undefined) {
        if (seasonCropAcreageListEditableTableDeltaObject.toBeInsertedList !== undefined) {
          dataToBeSaved.farm_patch_acreage_to_be_inserted_list = seasonCropAcreageListEditableTableDeltaObject.toBeInsertedList
        }
        if (seasonCropAcreageListEditableTableDeltaObject.toBeUpdatedListKeyMap !== undefined) {
          dataToBeSaved.farm_patch_acreage_to_be_updated_key_map = seasonCropAcreageListEditableTableDeltaObject.toBeUpdatedListKeyMap
        }
        if (seasonCropAcreageListEditableTableDeltaObject.toBeDeletedKeyList !== undefined) {
          dataToBeSaved.farm_patch_acreage_to_be_deleted_key_list = seasonCropAcreageListEditableTableDeltaObject.toBeDeletedKeyList
        }
      }
    }
    const response = await saveCropDetailedData(headers, dataToBeSaved)
    return response.data
  } catch (error) {
    console.log('OCFD gFD 10, error')
    console.log('OCFD gFD 10a, error = ', error)
    throw error
  }
}

const farmerBasicDetailsFormDefinition = {
  formControls: {
    farmerId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Id",
      readOnly: 1,
    },
    customerVisualId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Customer Visual Id",
      readOnly: 1,
    },
    farmerName: {
      ocFormRef: createRef(),
      type: "text-l10n_2",
      label: "Farmer Name",
      readOnly: 1,
    },
    customerTypeId: {
      ocFormRef: createRef(),
      type: "positive-number_2",
      label: "Customer Type Id",
      visible: false,
      readOnly: 1,
    },
    farmerMobile: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Mobile",
      readOnly: 1,
    },
    farmerDistrict: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer District",
      readOnly: 1,
    },
    farmerTaluk: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Taluk",
      readOnly: 1,
    },
    farmerVillage: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Village",
      readOnly: 1,
    },
    farmerBDM: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer BDM",
      readOnly: 1,
    },
    farmerBDMMobile: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer BDM Mobile",
      readOnly: 1,
    },
    farmerParavet: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer BDM",
      readOnly: 1,
    },
    farmerParavetMobile: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer BDM Mobile",
      readOnly: 1,
    },
    farmerThumbnail: {
      type: "media",
      label: "Farmer Thumbnail",
      mediaCardStyle: { display: 'inline-block', padding: '0px', margin: '0px', width: '225px', height: 'auto' },
      mediaCardBodyStyle: {padding: '12px'},
      mediaTagWrapperDivStyle: {position: 'relative', width: 'auto'},
      mediaTagStyle: { maxWidth: '200px', height: 'auto', position: 'relative'},
      // enableDownload: true,
      // enableViewInBrowser: true,
      enablePreview: true,
      enableUploadOfFiles: false,
      // uploadMultiple: false,
      uploadMaxCount: 1,
      uploadFolder: 'ah/farmer',
      uploadEntity: 'document',
      uploadEntityInformation: {
        document_type_id: 1000260003,
        entity_1_type_id: 1000220001,
        // entity_1_entity_uuid: ocItemListingUUID,
      },
      uploadEntityDocumentInformation: {
        entityType: 'farmer',
        localFolderName: 'farmer',
        serverFolderName: 'farmer',
        individualLocalFolderName: 'farmer',
        individualServerFolderName: 'farmer'
      }
    },
    farmerDairyFarmLocation: {
      type: "map-marker",
      label: "Farmer Dairy Farm Location",
      readOnly: 1,
      zoom: 13,
      markerTooltip: 'Dairy Farm Location',
      componentStyle: { height: '225px', width: '225px', marginLeft: '10px' }
    },
  },
  visibleFormControls: [
    "farmerId",
    "customerVisualId",
    "farmerName",
    "customerTypeId",
    "farmerMobile",
    "farmerDistrict",
    "farmerTaluk",
    "farmerVillage",
    "farmerBDM",
    "farmerBDMMobile",
    "farmerParavet",
    "farmerParavetMobile",
    "farmerThumbnail",
    "farmerDairyFarmLocation"
  ],
  elementToDataMapping: {
    farmerId: 'customer_id',
    customerVisualId: 'customer_visual_id',
    farmerName: 'customer_name',
    customerTypeId: 'customer_type_id',
    farmerMobile: 'mobile_number',
    farmerDistrict: 'district_name',
    farmerTaluk: 'taluk_name',
    farmerVillage: 'village_name',
    farmerBDM: 'bdm',
    farmerBDMMobile: 'bdm_mobile',
    farmerParavet: 'paravet',
    farmerParavetMobile: 'paravet_mobile',
    farmerThumbnail: 'customer_thumbnail',
    farmerDairyFarmLocation: "dairy_farm_location"
  },
  dataToElementMapping: {
    customer_id: 'farmerId',
    customer_visual_id: 'customerVisualId',
    customer_name: 'farmerName',
    customer_type_id: 'customerTypeId',
    mobile_number: 'farmerMobile',
    district_name: 'farmerDistrict',
    taluk_name: 'farmerTaluk',
    village_name: 'farmerVillage',
    bdm: 'farmerBDM',
    bdm_mobile: 'farmerBDMMobile',
    paravet: 'farmerParavet',
    paravet_mobile: 'farmerParavetMobile',
    customer_thumbnail: 'farmerThumbnail',
    dairy_farm_location: "farmerDairyFarmLocation"
  },
}

const farmerBasicDetailsFormInitialState = {
}

const newFarmerBasicDetailsFormDefinition = {
  formControls: {
    farmerId: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Id",
      readOnly: 1,
    },
    farmerName: {
      ocFormRef: createRef(),
      type: "text-l10n_2",
      label: "Farmer Name",
      placeHolderText: "Enter Farmer Name",
      validations: [
        { type: "Mandatory" },
      ],
      errorMessage: "Name of Farmer is mandatory",
    },
    farmerMobile: {
      ocFormRef: createRef(),
      type: "text_2",
      label: "Farmer Mobile",
      placeHolderText: "Enter Farmer Mobile Number",
      validations: [
        { type: "Mandatory" },
        { type: "regex", regexArray: ["IndiaMobileNumber"] },
      ],
      errorMessage: "Enter a valid mobile number!",
    },
    farmerState: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "Farmer State",
      labelKeyInList: "geography_name_l10n",
      valueKeyInList: "geography_id",
      placeHolderText: "Select Farmer State",
      // errorMessage: "Farmer State is mandatory",
      // validations: [{ type: "Mandatory" }, { type: "regex", regexArray: ["PositiveDecimal"] }],
      onSetValue2: async (
        stateIdInArray,
        additionalParams,
      ) => {
        const userToken = additionalParams[0]
        const farmerBasicDetailsFormState = additionalParams[1]
        const setFarmerBasicDetailsFormState = additionalParams[2]
        if (stateIdInArray.length > 0) {
          // const districtResponse = await getDistrictsOfState(userToken, stateIdInArray)
          const districtResponse = await getGeographyDropdownData(userToken, {parentGeographyIdArray: stateIdInArray, parentToChildEntityRelationshipTypeId: 1000210029, parentGeographyTypeId: 1000320001, childGeographyTypeId: 1000320002})
          const districtList = assignL10NObjectOrObjectArrayToItSelf(districtResponse.list_data, ['geography_name_l10n'])
          // resetSelectWithNewOptionsInForm(newFarmerBasicDetailsFormDefinition, 'farmerDistrict', districtList)
          // resetSelectWithNewOptionsInForm(newFarmerBasicDetailsFormDefinition, 'farmerTaluk', [])
          // resetSelectWithNewOptionsInForm(newFarmerBasicDetailsFormDefinition, 'farmerVillage', [])
          const updatedFormState = assignDataToFormState(
            newFarmerBasicDetailsFormDefinition,
            {farmer_district_list: districtList, farmer_district: [], farmer_taluka_1_list: [], farmer_taluka_1: [], farmer_village_1_list: [], farmer_village_1: []},
            farmerBasicDetailsFormState,
            setFarmerBasicDetailsFormState
          )
          return updatedFormState
        } else {
          const updatedFormState = assignDataToFormState(
            newFarmerBasicDetailsFormDefinition,
            {farmer_district_list: [], farmer_district: [], farmer_taluka_1_list: [], farmer_taluka_1: [], farmer_village_1_list: [], farmer_village_1: []},
            farmerBasicDetailsFormState,
            setFarmerBasicDetailsFormState
          )
          return updatedFormState
        }
      },
    },
    farmerDistrict: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "Farmer District",
      labelKeyInList: "geography_name_l10n",
      valueKeyInList: "geography_id",
      placeHolderText: "Select Farmer District",
      // errorMessage: "Farmer District is mandatory",
      // validations: [{ type: "Mandatory" }, { type: "regex", regexArray: ["PositiveDecimal"] }],
      onSetValue2: async (
        districtIdInArray,
        additionalParams,
      ) => {
        const userToken = additionalParams[0]
        const farmerBasicDetailsFormState = additionalParams[1]
        const setFarmerBasicDetailsFormState = additionalParams[2]
        if (districtIdInArray.length > 0) {
          // const talukResponse = await getTaluksOfDistrict(userToken, districtIdInArray)
          const talukResponse = await getGeographyDropdownData(userToken, {parentGeographyIdArray: districtIdInArray, parentToChildEntityRelationshipTypeId: 1000210030, parentGeographyTypeId: 1000320002, childGeographyTypeId: 1000320003})
          const talukList = assignL10NObjectOrObjectArrayToItSelf(talukResponse.list_data, ['geography_name_l10n'])
          // resetSelectWithNewOptionsInForm(newFarmerBasicDetailsFormDefinition, 'farmerTaluk', talukList)
          // resetSelectWithNewOptionsInForm(newFarmerBasicDetailsFormDefinition, 'farmerVillage', [])
          const updatedFormState = assignDataToFormState(
            newFarmerBasicDetailsFormDefinition,
            {farmer_taluka_1_list: talukList, farmer_taluka_1: [], farmer_village_1_list: [], farmer_village_1: []},
            farmerBasicDetailsFormState,
            setFarmerBasicDetailsFormState
          )
          return updatedFormState
        } else {
          const updatedFormState = assignDataToFormState(
            newFarmerBasicDetailsFormDefinition,
            {farmer_taluka_1_list: [], farmer_taluka_1: [], farmer_village_1_list: [], farmer_village_1: []},
            farmerBasicDetailsFormState,
            setFarmerBasicDetailsFormState
          )
          return updatedFormState
        }
      },
    },
    farmerTaluk: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "Farmer Taluk",
      placeHolderText: "Select Farmer Taluk",
      // errorMessage: "Farmer Taluk is mandatory",
      labelKeyInList: "geography_name_l10n",
      valueKeyInList: "geography_id",
      onSetValue2: async (
        talukIdInArray,
        additionalParams,
      ) => {
        const userToken = additionalParams[0]
        const farmerBasicDetailsFormState = additionalParams[1]
        const setFarmerBasicDetailsFormState = additionalParams[2]
        if (talukIdInArray.length > 0) {
          // const villageResponse = await getVillagesOfTaluk(userToken, talukIdInArray)
          const villageResponse = await getGeographyDropdownData(userToken, {parentGeographyIdArray: talukIdInArray, parentToChildEntityRelationshipTypeId: 1000210031, parentGeographyTypeId: 1000320003, childGeographyTypeId: 1000320004})
          const villageList = assignL10NObjectOrObjectArrayToItSelf(villageResponse.list_data, ['geography_name_l10n'])
          // resetSelectWithNewOptionsInForm(newFarmerBasicDetailsFormDefinition, 'farmerVillage', villageList)
          const updatedFormState = assignDataToFormState(
            newFarmerBasicDetailsFormDefinition,
            {farmer_village_1_list: villageList, farmer_village_1: []},
            farmerBasicDetailsFormState,
            setFarmerBasicDetailsFormState
          )
          return updatedFormState
        } else {
          const updatedFormState = assignDataToFormState(
            newFarmerBasicDetailsFormDefinition,
            {farmer_village_1_list: [], farmer_village_1: []},
            farmerBasicDetailsFormState,
            setFarmerBasicDetailsFormState
          )
          return updatedFormState
        }
      },
      // validations: [{ type: "Mandatory" }, { type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    farmerVillage: {
      ocFormRef: createRef(),
      type: "single-select-l10n_2",
      label: "Farmer Village",
      placeHolderText: "Select Farmer Village",
      // errorMessage: "Farmer Village is mandatory",
      labelKeyInList: "geography_name_l10n",
      valueKeyInList: "geography_id",
      // validations: [{ type: "Mandatory" }],
    },
  },
  visibleFormControls: [
    "farmerId",
    "farmerName",
    "farmerMobile",
    "farmerState",
    "farmerDistrict",
    "farmerTaluk",
    "farmerVillage",
  ],
  elementToDataMapping: {
    farmerId: 'customer_id',
    farmerName: 'customer_name_l10n',
    farmerMobile: 'mobile_number',
    farmerState: 'farmer_state',
    farmerDistrict: 'farmer_district',
    farmerTaluk: 'farmer_taluka_1',
    farmerVillage: 'farmer_village_1',
  },
  dataToElementMapping: {
    customer_id: 'farmerId',
    customer_name_l10n: 'farmerName',
    mobile_number: 'farmerMobile',
    farmer_state: 'farmerState',
    farmer_district: 'farmerDistrict',
    farmer_taluka_1: 'farmerTaluk',
    farmer_village_1: 'farmerVillage',
  },
}

const newFarmerBasicDetailsFormInitialState = {
}

const dairyFarmDetailsFormDefinition = {
  formControls: {
    numberOfAdultCows: {
      ocFormRef: createRef(),
      type: "positive-number_2",
      label: "Number of Adult Cows",
    },
    numberOfAdultBuffaloes: {
      ocFormRef: createRef(),
      type: "positive-number_2",
      label: "Number of Adult Buffaloes",
    },
    farmerFoliageCrops: {
      ocFormRef: createRef(),
      type: "multi-select-l10n_2",
      label: "Foliage Crops",
      placeHolderText: "Select Foliage Crops",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      // referenceCategoryId: 10001300,
    },
    farmerConcentrates: {
      ocFormRef: createRef(),
      type: "multi-select-l10n_2",
      label: "Concentrates",
      placeHolderText: "Select Concentrates",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      // referenceCategoryId: 10013000,
    },
  },
  visibleFormControls: [
    "numberOfAdultCows",
    "numberOfAdultBuffaloes",
    "farmerFoliageCrops",
    "farmerConcentrates",
  ],
  elementToDataMapping: {
    numberOfAdultCows: 'number_of_adult_cows',
    numberOfAdultBuffaloes: 'number_of_adult_buffaloes',
    farmerFoliageCrops: "farmer_foliage_available_1",
    farmerConcentrates: "farmer_concentrates_available_1",
  },
  dataToElementMapping: {
    number_of_adult_cows: 'numberOfAdultCows',
    number_of_adult_buffaloes: 'numberOfAdultBuffaloes',
    farmer_foliage_available_1: "farmerFoliageCrops",
    farmer_concentrates_available_1: "farmerConcentrates",
  },
}

const dairyFarmDetailsFormInitialState = {
}

const DairyFarmTab = () => {
  const userToken = useSelector((state)=>state.user.userToken)
  const params = useParams()
  const customerId = params.customerId

  const [dairyFarmFormState, setDairyFarmFormState] = useState(dairyFarmDetailsFormInitialState)
  
  const loadDataAsync = async () => {
    const dairyFarmDataRequestResponse = await getFarmerData(userToken, customerId, Object.keys(dairyFarmDetailsFormDefinition.dataToElementMapping))
    if (dairyFarmDataRequestResponse.return_code === 0) {
      assignDataToFormState(
        dairyFarmDetailsFormDefinition,
        dairyFarmDataRequestResponse.data,
        dairyFarmFormState,
        setDairyFarmFormState
      )
    }
  }

  const saveDairyFarmData = async () => {
    /* const [currentFormState, validationFailed] = validateFormElements(
      dairyFarmDetailsFormDefinition,
      dairyFarmFormState,
      setDairyFarmFormState
    ) */
    const [currentFormState, validationFailed] = validateFormElements2(
      dairyFarmDetailsFormDefinition,
      dairyFarmFormState,
      setDairyFarmFormState
    )
    if (validationFailed) {
      // do something
      console.log("Validation Failed")
      ToastersService.failureToast("Validation failed!")
    } else {
      console.log("you can save now")
      const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(dairyFarmDetailsFormDefinition, currentFormState, setDairyFarmFormState)
      const extractedValues = extractChangedValues(dairyFarmDetailsFormDefinition, updatedCurrentFormState)
      console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValues)
      const setFarmerDataResponse = await setFarmerData(userToken, customerId, extractedValues)
      if (setFarmerDataResponse.return_code === 0) {
        ToastersService.successToast("Successfully updated farmer data!")
      } else {
        ToastersService.failureToast("Failed to update farmer data!")
      }
    }
  }

  useEffect(()=>{
    loadDataAsync()
  },[])

  return (
    <>
      {<KrushalOCForm
        formState={dairyFarmFormState}
        setFormState={setDairyFarmFormState}
        formDefinition={dairyFarmDetailsFormDefinition}
      />}
      <Button
        onClick={() => saveDairyFarmData()}
        >
        Save
      </Button>
    </>
    
  )
}

const basicCropFarmDetailsFormDefinition = {
  formControls: {
    acresOfLand: {
      ocFormRef: createRef(),
      // type: "postive-decimal",
      type: "text-decimal_2",
      label: "Acres of Land",
      placeHolderText: "Enter Acres",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
      errorMessage: {ul: 'Should be positive number'}
    },
    cropsGrown: {
      ocFormRef: createRef(),
      type: "multi-select-l10n_2",
      allowOthers: 1,
      othersLabelInDropdown: { en: "Others" },
      othersLabel: { en: "Others Crops" },
      label: "Recent Crops Grown",
      labelKeyInList: "crop_name_l10n",
      valueKeyInList: "crop_id",
      placeHolderText: "Select Crops Grown",
      list: [
        {crop_id:300000001, crop_name_l10n:'Sugarcane'},
        {crop_id:300000002, crop_name_l10n:'Wheat'},
        {crop_id:300000003, crop_name_l10n:'Maize'},
        {crop_id:300000004, crop_name_l10n:'Bajra'},
        {crop_id:300000005, crop_name_l10n:'Groundnut'},
        {crop_id:300000006, crop_name_l10n:'Rice'},
        {crop_id:300000007, crop_name_l10n:'Mango'},

      ],
      validations: [{ type: "Mandatory"}],
      errorMessage: {ul: "Choose at least 1 crop"},
    },
    numberOfFarms: {
      ocFormRef: createRef(),
      type: "positive-number_2",
      label: "Number of Farms",
      placeHolderText: "Enter Acres",
    },
  },
  visibleFormControls: [
    "acresOfLand",
    "cropsGrown",
    "numberOfFarms",
  ],
  elementToDataMapping: {
    acresOfLand: 'acres_of_land',
    cropsGrown: "crops_grown",
    numberOfFarms: "number_of_farms"
  },
  dataToElementMapping: {
    acres_of_land: 'acresOfLand',
    crops_grown: "cropsGrown",
    number_of_farms: "numberOfFarms"
  },
}

const basicCropFarmDetailsFormInitialState = {
}

const farmAcreageTableConfiguration = {
  // tableNavigationUrl: '../oc/manure-to-be-contacted-customers',
  // enableSelection: true,
  // defaultPageSize: 10,
  enableHorizontalScrolling: true,
  // showTableHeader: true,
  // omniSearchEnabled: true,
  // enableColumnConfiguration: true,
  getAllDataAtOnce: true,
  disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  hasEditableColumns: true,
  rowKey: 'farm_list_index',
  columns: [
    {
      title: "Farm Name",
      key: "asset_name_l10n",
    },
    {
      title: "Farm Size",
      key: "acres_of_land",
    },
  ],
  columnConfiguration: {
    asset_name_l10n: {
      headingText: 'Farm Name',
      editable: true,
      // editableReadOnly: true,
      editableType: 'text-l10n_2',
      editableLabel: { en: "Farmer Name", mr: "शेतकऱ्याचे नाव" },
      editablePlaceHolderText: {ul: "Enter Farm Name"},
      editableValidations: [{ type: "Mandatory" },],
      editableErrorMessage: "Name is mandatory and should not have special characters",
    },
    acres_of_land: {
      headingText: 'Farm Name',
      editable: true,
      editableType: 'positive-decimal_2',
      editableLabel: { en: "Farmer Name", mr: "शेतकऱ्याचे नाव" },
      editablePlaceHolderText: {ul: "Enter Farm Acreage"},
      editableValidations: [{ type: "Mandatory" }, { type: "regex", regexArray: ["PositiveDecimal"] }],
      editableErrorMessage: "Acreage is mandatory and should be a positive number",
    }
  }
}

const periodAndCropsTableConfiguration = {
  // tableNavigationUrl: '../oc/manure-to-be-contacted-customers',
  // enableSelection: true,
  // defaultPageSize: 10,
  enableHorizontalScrolling: true,
  // showTableHeader: true,
  // omniSearchEnabled: true,
  // enableColumnConfiguration: true,
  getAllDataAtOnce: true,
  disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  hasEditableColumns: true,
  rowKey: 'period_id',
  columns: [
    {
      title: "Period",
      key: "period_name",
    },
    {
      title: "Crops",
      key: "farmer_grown_crops",
    },
    {
      title: "Farms Grown In",
      key: "farms_grown_in",
    },
  ],
  columnConfiguration: {
    period_name: {
      headingText: 'Period',
      formatType: 'custom',
      formatFunction: (text, record) => {
        console.log('OCFD ff 1')
        return (
          <>
            {extractBasedOnLanguage(record?.period_name_l10n)} - ({moment(record?.period_start_date).format("MMM-YY")} to {moment(record?.period_end_date).format("MMM-YY")})
          </>
        )
      }
      // enableFilter: true,
      // type: 'string',
      // includeInOmnisearch: true
    },
    farmer_grown_crops: {
      headingText: 'Crops',
      editable: true,
      editableType: 'multi-select-l10n_2',
      editableStyle: {width: '150px'},
      editableDropdownFieldNames:{ label: 'crop_name_l10n', value: 'crop_id' },
      editableLabel: { ul: "Crop"},
      editablePlaceHolderText: {ul: "Select one or more"},
      // editableValidations: [{ type: "Mandatory" }],
      // editableErrorMessage: "Crop is Mandatory",
    },
    farms_grown_in: {
      headingText: 'Farms Grown In',
      editable: true,
      editableType: 'multi-select-l10n_2',
      editableStyle: {width: '150px'},
      editableDropdownFieldNames:{ label: 'asset_name_l10n', value: 'asset_id' }
    }
  }
}

const seasonFarmCropAcreageTableConfiguration = {
  // tableNavigationUrl: '../oc/manure-to-be-contacted-customers',
  // enableSelection: true,
  // defaultPageSize: 10,
  enableHorizontalScrolling: true,
  // showTableHeader: true,
  // omniSearchEnabled: true,
  // enableColumnConfiguration: true,
  getAllDataAtOnce: true,
  disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  hasEditableColumns: true,
  rowKey: 'customer_feature_classification_id',
  columns: [
    {
      title: "Period",
      key: "period_name",
    },
    {
      title: "Farm Name",
      key: "farm_name",
    },
    {
      title: "Crop",
      key: "crop_name",
    },
    {
      title: "Acres",
      key: "crop_acres_in_farm_period",
    },
  ],
  columnConfiguration: {
    period_name: {
      headingText: 'Period',
      formatType: 'custom',
      formatFunction: (text, record) => {
        return (
          <>
            {extractBasedOnLanguage(record?.period_name_l10n)} - ({moment(record?.period_start_date).format("MMM-YY")} to {moment(record?.period_end_date).format("MMM-YY")})
          </>
        )
      }
      // enableFilter: true,
      // type: 'string',
      // includeInOmnisearch: true
    },
    crop_name: {
      headingText: 'Crop',
      formatType: 'custom',
      formatFunction: (text, record) => {
        return (
          <>
            {extractBasedOnLanguage(record?.crop_name_l10n)}
          </>
        )
      }
      // enableFilter: true,
      // type: 'string',
      // includeInOmnisearch: true
    },
    farm_name: {
      headingText: 'Crop',
      formatType: 'custom',
      formatFunction: (text, record) => {
        return (
          <>
            {extractBasedOnLanguage(record?.farm_asset_name_l10n)}
          </>
        )
      }
      // enableFilter: true,
      // type: 'string',
      // includeInOmnisearch: true
    },
    crop_acres_in_farm_period: {
      headingText: 'Crops',
      editable: true,
      editableType: 'positive-decimal_2',
      formatType: 'double',
      format: {decimalPlaces: 2},
    }
  }
}

const CropFarmTab = () => {
  const userToken = useSelector((state)=>state.user.userToken)
  const params = useParams()
  const customerId = params.customerId

  const [basicCropFarmFormState, setBasicCropFarmFormState] = useState(basicCropFarmDetailsFormInitialState)
  const [collapsePanelsActiveKey, setCollapsePanelsActiveKey] = useState('1')

  const [farmList, setFarmList] = useState([])
  const [farmListFromDB, setFarmListFromDB] = useState([])
  
  const [periodsAndCropsList, setPeriodsAndCropsList] = useState([])
  const [periodsAndCropsListFromDB, setPeriodsAndCropsListFromDB] = useState([])
  // const [farmSize, setFarmSize] = useState([])

  const [seasonCropAcreageList, setSeasonCropAcreageList] = useState([])
  const [seasonCropAcreageListFromDB, setSeasonCropAcreageListFromDB] = useState([])

  const farmAcreageTableRef = createRef()
  const periodAndCropTableRef = createRef()
  const seasonCropAcreageTableRef = createRef()

  const loadDataAsync = async () => {
    const cropFarmDataRequestResponse = await getCropScreenData(userToken, customerId)
    if (cropFarmDataRequestResponse.return_code === 0 && cropFarmDataRequestResponse.data) {
      const farmListFromDB = cropFarmDataRequestResponse.data.farms
      const clonedFarmList = lodashObject.cloneDeep(farmListFromDB)
      addCounterToTableRows(clonedFarmList, 'farm_list_index')
      const clonedFarmListForReference = lodashObject.cloneDeep(clonedFarmList)      
      setFarmListFromDB(clonedFarmListForReference)
      let numberOfFarms = Array.isArray(farmListFromDB) ? farmListFromDB.length : 0
      addRefToEditableTableItemsIfNotAvailable(clonedFarmList, farmAcreageTableConfiguration)
      setFarmList(clonedFarmList)

      const periodsAndCropsFromDB = cropFarmDataRequestResponse.data.periods_and_crops
      const clonedPeriodsAndCrops = lodashObject.cloneDeep(periodsAndCropsFromDB)

      for (const periodAndCrop of clonedPeriodsAndCrops) {
        const cropList = lodashObject.cloneDeep(global.references.crops).filter(object => {
          if (periodAndCrop.possible_farmer_crops.includes(object.crop_id)) {
            return true
          } else {
            return false
          }
        })
        periodAndCrop.farmer_grown_crops_list = cropList
      }

      const clonedPeriodsAndCropsForReference = lodashObject.cloneDeep(clonedPeriodsAndCrops)
      setPeriodsAndCropsListFromDB(clonedPeriodsAndCropsForReference)
      addRefToEditableTableItemsIfNotAvailable(clonedPeriodsAndCrops, periodAndCropsTableConfiguration)
      setPeriodsAndCropsList(clonedPeriodsAndCrops)

      const seasonFarmCropAcreageListFromDB = cropFarmDataRequestResponse.data.season_farm_crop_acreage
      const clonedSeasonFarmCropAcreageList = lodashObject.cloneDeep(seasonFarmCropAcreageListFromDB)
      const seasonFarmCropAcreageListForReference = lodashObject.cloneDeep(clonedSeasonFarmCropAcreageList)
      setSeasonCropAcreageListFromDB(seasonFarmCropAcreageListForReference)
      addRefToEditableTableItemsIfNotAvailable(clonedSeasonFarmCropAcreageList, seasonFarmCropAcreageTableConfiguration)
      setSeasonCropAcreageList(clonedSeasonFarmCropAcreageList)


      const basicFarmData = lodashObject.cloneDeep(cropFarmDataRequestResponse.data.basic_classification_data)
      basicFarmData['number_of_farms'] = numberOfFarms
      const newBasicCropFarmFormState = assignDataToFormState(
        basicCropFarmDetailsFormDefinition,
        basicFarmData,
        basicCropFarmFormState,
        setBasicCropFarmFormState
      )
      if (numberOfFarms === 0) {
        const new2BasicCropFarmFormState = {...newBasicCropFarmFormState}
        const numberOfFarmsFormState = new2BasicCropFarmFormState['numberOfFarms']
        const newNumberOfFarmsFormState = numberOfFarmsFormState ? {...numberOfFarmsFormState} : {dbValue: 0}
        newNumberOfFarmsFormState['value'] = 1
        newNumberOfFarmsFormState['displayValue'] = '1'
        new2BasicCropFarmFormState['numberOfFarms'] = newNumberOfFarmsFormState
        setBasicCropFarmFormState(new2BasicCropFarmFormState)
      }
    }

    // load number of acres from customer_classification
    // load recent crops of customer from customer_classification
    // load master data of relevant periods. Let us pick up till 2 years before plus for which data is available
    // Also, obtain the potential crops based on the period types, short list based on the crops he has grown
    // --- case where this is new data
    // list all the periods on the left side of a table. populate multi-select dropdowns on the right side
    // on save
    // identify if there was any delta
    // if not, update the values, else insert
    // for each row, check if there was any delta. if not, allow to update / insert the data
    // save this data to the server
    // it should save in customer_classification - period_id (put it in int) to reference_id?
  }

  const resetFarmListNameAndAcreageTableValues = (acresOfLandToBeDivided, numberOfFarms, originalValueOfNumberOfFarms) => {
    const newFarmList = [...farmList]
    if (numberOfFarms > originalValueOfNumberOfFarms) {
      for (let counter = originalValueOfNumberOfFarms + 1; counter <= numberOfFarms; counter++) {
        const newFarm = {acres_of_land: 1}
        newFarmList.push(newFarm)
      }
      const newFarmListLength = newFarmList.length
      // {asset_name_l10n:{ul: 'My Farm'}, 
      newFarmList.map((farm, index) => {
        if (farm['asset_name_l10n'] === undefined) {
          farm['asset_name_l10n'] = {ul: 'Unnamed Farm ' + (index + 1)}
        }
        farm['acres_of_land'] = acresOfLandToBeDivided / newFarmListLength
      })
    } else if (numberOfFarms < originalValueOfNumberOfFarms) {
      const totalNumberOfFarmsToBeDeleted = originalValueOfNumberOfFarms - numberOfFarms
      let newFarmListLength = newFarmList.length
      let numberOfFarmsDeleted = 0
      for (let counter = newFarmListLength - 1; counter >= 0; counter--) {
        if (numberOfFarmsDeleted >= totalNumberOfFarmsToBeDeleted) {
          break
        }
        const farm = newFarmList[counter]
        const assetName = extractBasedOnLanguage(farm['asset_name_l10n'])
        if (assetName.indexOf('Unnamed Farm ') >= 0) {
          newFarmList.splice(counter, 1)
          numberOfFarmsDeleted++
        }
      }
      if (numberOfFarmsDeleted < totalNumberOfFarmsToBeDeleted) {
        for (let counter = newFarmListLength - 1; counter >= 0; counter--) {
          if (numberOfFarmsDeleted >= totalNumberOfFarmsToBeDeleted) {
            break
          }
          newFarmList.splice(counter, 1)
          numberOfFarmsDeleted++
        }
      }
      newFarmListLength = newFarmList.length
      // {asset_name_l10n:{ul: 'My Farm'}, 
      newFarmList.map((farm, index) => {
        if (farm['asset_name_l10n'] === undefined) {
          farm['asset_name_l10n'] = {ul: 'Unnamed Farm ' + (index + 1)}
        }
        farm['acres_of_land'] = acresOfLandToBeDivided / newFarmListLength
      })
    } else {
      throw new Error('This method should not have been called')
    }
    addCounterToTableRows(newFarmList, 'farm_list_index')
    addRefToEditableTableItemsIfNotAvailable(newFarmList, farmAcreageTableConfiguration)
    setFarmList(newFarmList)
  }

  const updateTableKeys = () => {
    if (farmAcreageTableRef.current !== undefined && farmAcreageTableRef.current !== null) {
      farmAcreageTableRef.current.reset()
    }
    if (periodAndCropTableRef.current !== undefined && periodAndCropTableRef.current !== null) {
      periodAndCropTableRef.current.reset()
    }
  }
  
  const saveFarmAcreageAndRecentCropsData = async () => {
    /* const [currentFormState, validationFailed] = validateFormElements(
      basicCropFarmDetailsFormDefinition,
      basicCropFarmFormState,
      setBasicCropFarmFormState
    ); */
    const [currentFormState, validationFailed] = validateFormElements2(
      basicCropFarmDetailsFormDefinition,
      basicCropFarmFormState,
      setBasicCropFarmFormState
    );
    if (validationFailed) {
      // do something
      console.log("Validation Failed");
      ToastersService.failureToast("Validation failed!");
    } else {
      console.log("you can save now");
      const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(basicCropFarmDetailsFormDefinition, currentFormState, setBasicCropFarmFormState)
      const extractedValues = extractChangedValues(basicCropFarmDetailsFormDefinition, updatedCurrentFormState)
      const extractedValuesImportant = extractChangedValues(basicCropFarmDetailsFormDefinition, updatedCurrentFormState, ['acresOfLand', 'numberOfFarms'])
      console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValues)
      const numberOfFarms = extractedValues['number_of_farms']
      const originalValueOfNumberOfFarms = updatedCurrentFormState['numberOfFarms'].dbValue
      const acresOfLand = extractedValues['acres_of_land']
      const acresOfLandForced = extractedValuesImportant['acres_of_land']
      const acresOfLandToBeUsed = acresOfLand ? acresOfLand : acresOfLandForced
      const numberOfFarmsForced = extractedValuesImportant['number_of_farms']
      
      const updatedFarmList = extractTableListWithUpdatedValues(farmList, farmAcreageTableConfiguration)
      deleteRefKeysFromObjectInObjectArray(updatedFarmList, farmAcreageTableConfiguration)
      const farmListEditableTableDeltaResult = extractEditableTableDelta(farmListFromDB, updatedFarmList, 'farm_list_index', 'asset_id', 'asset_id')
      deleteKeysFromObjectInObjectArray(farmListEditableTableDeltaResult.toBeInsertedList, ['farm_list_index'])
      deleteKeysFromObjectInObjectArray(Object.values(farmListEditableTableDeltaResult.toBeUpdatedListKeyMap), ['farm_list_index'])
      
      const periodToCropAssignmentList = extractTableListWithUpdatedValues(periodsAndCropsList, periodAndCropsTableConfiguration)
      deleteRefKeysFromObjectInObjectArray(periodToCropAssignmentList, periodAndCropsTableConfiguration)
      const periodToCropsListEditableTableDeltaResult = extractEditableTableDelta(periodsAndCropsListFromDB, periodToCropAssignmentList, 'period_id', 'farms_grown_in', 'period_id')

      const seasonCropAcreageAssignmentList = extractTableListWithUpdatedValues(seasonCropAcreageList, seasonFarmCropAcreageTableConfiguration)
      deleteRefKeysFromObjectInObjectArray(seasonCropAcreageAssignmentList, seasonFarmCropAcreageTableConfiguration)
      const seasonCropAcreageListEditableTableDeltaResult = extractEditableTableDelta(seasonCropAcreageListFromDB, seasonCropAcreageAssignmentList, 'farm_patch_id', 'farm_patch_acreage_asset_classification_id', 'farm_patch_id')

      if (numberOfFarms === undefined) {
        if (acresOfLand === undefined) {
          delete currentFormState['number_of_farms']
          const resposeFromSavingCropScreenData = await setCropScreenData(userToken, customerId, extractedValues, farmListEditableTableDeltaResult, periodToCropsListEditableTableDeltaResult, seasonCropAcreageListEditableTableDeltaResult)
          if (resposeFromSavingCropScreenData.return_code === 0) {
            await loadDataAsync()
            updateTableKeys()
            ToastersService.successToast("Updated Farmer Crop Data")
          } else {
            ToastersService.failureToast("Failed To Updated Farmer Crop Data")
          }
        } else if (originalValueOfNumberOfFarms === 1 && acresOfLand !== undefined) {
          // update asset to farm size
          // const newFarmList = lodashObject.cloneDeep(farmList)
          updatedFarmList[0]['acres_of_land'] = acresOfLand
          // assignObjectOrObjectArrayWithKeyValueAsStringToItSelfAsL10NObjectUL(updatedFarmList[0], 'asset_name_l10n')
          const assetId = updatedFarmList[0]['asset_id']
          const toBeUpdatedListKeyMap = {}
          toBeUpdatedListKeyMap[assetId] = updatedFarmList[0]
          const resposeFromSavingCropScreenData = await setCropScreenData(userToken, customerId, extractedValues, {toBeUpdatedListKeyMap}, periodToCropsListEditableTableDeltaResult, seasonCropAcreageListEditableTableDeltaResult)
          if (resposeFromSavingCropScreenData.return_code === 0) {
            await loadDataAsync()
            updateTableKeys()
            ToastersService.successToast("Updated Farmer Crop Data")
          } else {
            ToastersService.failureToast("Failed To Updated Farmer Crop Data")
          }
        } else /* if (farmListEditableTableDeltaResult.anyChange === true) */ {
          const resposeFromSavingCropScreenData = await setCropScreenData(userToken, customerId, extractedValues, farmListEditableTableDeltaResult, periodToCropsListEditableTableDeltaResult, seasonCropAcreageListEditableTableDeltaResult)
          if (resposeFromSavingCropScreenData.return_code === 0) {
            await loadDataAsync()
            updateTableKeys()
            ToastersService.successToast("Updated Farmer Crop Data")
          } else {
            ToastersService.failureToast("Failed To Updated Farmer Crop Data")
          }
        }
      } else if (numberOfFarms !== undefined && originalValueOfNumberOfFarms === 0 && numberOfFarms === 1) {
        if (acresOfLandForced !== undefined && acresOfLandForced !== null && acresOfLandForced > 0) {
          const defaultFarm = [{asset_name_l10n:{ul: 'My Farm'}, acres_of_land: acresOfLandForced}]
          const resposeFromSavingCropScreenData = await setCropScreenData(userToken, customerId, extractedValues, {toBeInsertedList: defaultFarm}, periodToCropsListEditableTableDeltaResult, seasonCropAcreageListEditableTableDeltaResult)
          if (resposeFromSavingCropScreenData.return_code === 0) {
            await loadDataAsync()
            updateTableKeys()
            ToastersService.successToast("Updated Farmer Crop Data")
          } else {
            ToastersService.failureToast("Failed To Updated Farmer Crop Data")
          }
        } else {
          const resposeFromSavingCropScreenData = await setCropScreenData(userToken, customerId, extractedValues, undefined, periodToCropsListEditableTableDeltaResult, seasonCropAcreageListEditableTableDeltaResult)
          if (resposeFromSavingCropScreenData.return_code === 0) {
            await loadDataAsync()
            updateTableKeys()
            ToastersService.successToast("Updated Farmer Crop Data")
          } else {
            ToastersService.failureToast("Failed To Updated Farmer Crop Data")
          }
        }
      } else {
        // if new farms are more, add new farms, divide acreage amongst all
        if (numberOfFarms !== originalValueOfNumberOfFarms) {
          if (farmList.length === numberOfFarms) {
            const resposeFromSavingCropScreenData = await setCropScreenData(userToken, customerId, extractedValues, farmListEditableTableDeltaResult, periodToCropsListEditableTableDeltaResult, seasonCropAcreageListEditableTableDeltaResult)
            if (resposeFromSavingCropScreenData.return_code === 0) {
              await loadDataAsync()
              updateTableKeys()
              ToastersService.successToast("Updated Farmer Crop Data")
            } else {
              ToastersService.failureToast("Failed To Updated Farmer Crop Data")
            }
          } else {
            ToastersService.warningToast("Please save farm related data")
            resetFarmListNameAndAcreageTableValues(acresOfLandToBeUsed, numberOfFarms, originalValueOfNumberOfFarms === undefined ? 0 : originalValueOfNumberOfFarms)
            setCollapsePanelsActiveKey('2')
            updateTableKeys()
          }
        }
        // if new farms are less, set the ones to be deleted to 0, divide the total acreage between the remaining
      }
    }
  }

  useEffect(()=>{
    loadDataAsync()
  },[])

  return (
    <>
      <Collapse /* defaultActiveKey={['1']} */ activeKey={collapsePanelsActiveKey} onChange={(arg1, arg2) => {
        console.log('OCFD CFT r CoC 1, arg1 = ', arg1)
        console.log('OCFD CFT r CoC 1, arg2 = ', arg2)
        setCollapsePanelsActiveKey(arg1)
      }}>
        <Panel header="Crop Acreage" key="1">
          <div style={{display:'flex', alignItems:"flex-start"}}>
            <div style={{flex:"1"}}>
              <KrushalOCForm
                formState={basicCropFarmFormState}
                setFormState={setBasicCropFarmFormState}
                formDefinition={basicCropFarmDetailsFormDefinition}
              />
            </div>
            <div style={{alignSelf: "flex-end", display:"flex", flexDirection: "column"}}>
              <Button
                onClick={() => saveFarmAcreageAndRecentCropsData()}
                >
                Save
              </Button>
              <Button style={{marginTop:"4px"}}>Individual Farm Acreage</Button>
            </div>
          </div>
        </Panel>
        <Panel header="Acreage of Different Farms" key="2">
          <div style={{display:'flex', alignItems:"flex-start"}}>
            <div style={{flex:"1"}}>
              {/* <Table
                key={farmSizeTableKey}
                dataSource={farmList}
                columns={farmsTableColumns}
                pagination={false}
              /> */}
              <OCTable
                ref={farmAcreageTableRef}
                /* tableKey={tableKey}
                setTableKey={setTableKey} */
                reportData={farmList}
                setReportData={setFarmList}
                tableConfiguration={farmAcreageTableConfiguration}
                // selectedRowKeys={selectedRowKeys}
                // setSelectedRowKeys={setSelectedRowKeys}
                // postInitialLoadCallback={postInitialLoadCB}
                // loadReportCallback={loadReportCB}
                // loadInitialFilterDataCallback={loadInitialFilterDataCB}
                // parentPageParams={pageParams}
                // showTableHeader={true}
                // omniSearchRowEnabled={true}
                // omniSearchRowBeginning={backToQueueListLinkUI}
                // omniSearchRowMiddle={viewFarmerDetailsButtons}
              />
            </div>
            <div style={{alignSelf: "flex-end", display:"flex", flexDirection: "column"}}>
              <Button
                onClick={() => {
                  saveFarmAcreageAndRecentCropsData()
                }}
                >
                Save
              </Button>
            </div>
          </div>
        </Panel>
        <Panel header="Crops In Different Periods" key="3">
          <div style={{display:'flex', alignItems:"flex-start"}}>
            <div style={{flex:"1"}}>
              <OCTable
                ref={periodAndCropTableRef}
                /* tableKey={tableKey}
                setTableKey={setTableKey} */
                reportData={periodsAndCropsList}
                setReportData={setPeriodsAndCropsList}
                tableConfiguration={periodAndCropsTableConfiguration}
                // selectedRowKeys={selectedRowKeys}
                // setSelectedRowKeys={setSelectedRowKeys}
                // postInitialLoadCallback={postInitialLoadCB}
                // loadReportCallback={loadReportCB}
                // loadInitialFilterDataCallback={loadInitialFilterDataCB}
                // parentPageParams={pageParams}
                // showTableHeader={true}
                // omniSearchRowEnabled={true}
                // omniSearchRowBeginning={backToQueueListLinkUI}
                // omniSearchRowMiddle={viewFarmerDetailsButtons}
              />
            </div>
            <div style={{alignSelf: "flex-end", display:"flex", flexDirection: "column"}}>
              <Button
                onClick={() => {
                  saveFarmAcreageAndRecentCropsData()
                }}
                >
                Save
              </Button>
            </div>
          </div>
        </Panel>
        <Panel header="Crop Cultivation Acreage" key="4">
          <div style={{display:'flex', alignItems:"flex-start"}}>
            <div style={{flex:"1"}}>
              <OCTable
                ref={seasonCropAcreageTableRef}
                /* tableKey={tableKey}
                setTableKey={setTableKey} */
                reportData={seasonCropAcreageList}
                setReportData={setSeasonCropAcreageList}
                tableConfiguration={seasonFarmCropAcreageTableConfiguration}
                // selectedRowKeys={selectedRowKeys}
                // setSelectedRowKeys={setSelectedRowKeys}
                // postInitialLoadCallback={postInitialLoadCB}
                // loadReportCallback={loadReportCB}
                // loadInitialFilterDataCallback={loadInitialFilterDataCB}
                // parentPageParams={pageParams}
                // showTableHeader={true}
                // omniSearchRowEnabled={true}
                // omniSearchRowBeginning={backToQueueListLinkUI}
                // omniSearchRowMiddle={viewFarmerDetailsButtons}
              />
            </div>
            <div style={{alignSelf: "flex-end", display:"flex", flexDirection: "column"}}>
              <Button
                onClick={() => {
                  saveFarmAcreageAndRecentCropsData()
                }}
                >
                Save
              </Button>
            </div>
          </div>
        </Panel>
        <Panel header="Manure Requirement" key="5">
          'Coming Soon'
        </Panel>
      </Collapse>
    </>
    
  )
}

const smartRationTableConfiguration = {
  // tableNavigationUrl: '/sr/smart-ration-customers',
  enableSelection: true,
  // defaultPageSize: 10,
  enableHorizontalScrolling: true,
  // showTableHeader: true,
  // omniSearchEnabled: true,
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'report_id',
  columns: [
    {
      title: "Report Type",
      key: "report_type",
    },
    {
      title: "Report Date",
      key: "report_date",
    },
    {
      title: "No. of Animals In Report",
      key: "number_of_animals_in_report",
    },
    {
      title: 'No. of Animals With Smart Ration',
      key: 'number_of_animals_with_sr_in_report'
    },
    {
      title: 'Report in English',
      key: 'en_document_information'
    },
    {
      title: 'Report in Marathi',
      key: 'mr_document_information'
    },
    {
      title: 'Report in Tamil',
      key: 'ta_document_information'
    },
    {
      title: 'Report in Telugu',
      key: 'te_document_information'
    },
  ],
  columnConfiguration: {
    report_type: {
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      translateFilteredValuesToAlt: 'value_int',
      altColumn: 'report_type_id',
      filters: [
        { text: 'System Generated', value: 'System Generated', value_int: 1001500001 },
        { text: 'Constraint Generated', value: 'Constraint Generated', value_int:1001500002 },
        { text: 'Vet Generated', value: 'Vet Generated', value_int:1001500003 },
      ],
    },
    en_document_information: {
      formatType: 's3Link',
      linkText: 'Smart Ration (en)'
    },
    number_of_animals_in_report: {
      type: 'number_range',
      headingText: 'Number of Animals In Report',
      enableFilter: true,
      formatType: 'int',
      enableSort: true,
      sortColumnType: 'int',
    },
    number_of_animals_with_sr_in_report: {
      type: 'number_range',
      headingText: 'Number of Animals With Smart Ration In Report',
      enableFilter: true,
      formatType: 'int',
      enableSort: true,
      sortColumnType: 'int',
    },
    mr_document_information: {
      formatType: 's3Link',
      linkText: 'Smart Ration (mr)'
    },
    ta_document_information: {
      formatType: 's3Link',
      linkText: 'Smart Ration (ta)'
    },
    te_document_information: {
      formatType: 's3Link',
      linkText: 'Smart Ration (te)'
    },
    report_date: {
      headingText: 'Report Date',
      formatType: 'date',
      format: 'DD-MMM-YYYY',  
      enableFilter: true,
      type: 'dayjs_range',
      enableSort: true,
      sortColumnType: 'date',
      defaultSort: 'descend',
    },
  }
}

const SmartRationReportTab = () => {
  const userToken = useSelector((state)=>state.user.userToken)
  const params = useParams()
  const customerId = params.customerId
  const smartRationReportRef = useRef()
  
  const [reportData, setReportData] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [reportLanguage, setReportLanguage] = useState([])
  const [loading, setLoading] = useState(false)
  
  const loadReportCB = (reportQueryParams, additionalParams) => {
    reportQueryParams['customer_id'] = customerId
    return loadSmartRationReport(userToken, reportQueryParams, additionalParams)
  }

  const handleGeneratePDFForSmartReportClick = async (event) => {
    try {
      // for a report, construct reportArray
      // {asset_id: systemGeneratedReportId, report_type_in_file_name: 'Type 1', document_type_id: 1000260024}
      setLoading(true)
      const selectedRows = reportData.filter((object) => {
        return (selectedRowKeys.includes(object['report_id']))
      })
      const reportGenerationStructureArray = getReportTypeForFileNameAndDocumentTypeIdForReportIdReportTypeIdArray(selectedRows)
      const reportGenerationResponseCode = await getSmartRationDataCreateReportUploadToS3AndCreateDocument(
          userToken, customerId, reportLanguage,
          reportGenerationStructureArray)
      if (reportGenerationResponseCode.return_code !== 0) {
        ToastersService.failureToast('Could not generate reports')
      }
      smartRationReportRef.current.reloadDataAndMoveToPageOne()
      ToastersService.successToast('Successfully generated reports')
    } catch (error) {
      ToastersService.failureToast('Could not generate reports')
    } finally {
      setLoading(false)
    }
  }
  return (
    <Spin spinning={loading} tip="Loading...">
      <Space>
        <Select
          mode="multiple"
          value={reportLanguage}
          style={{
            width: 120,
          }}
          onChange={(valueOfSelectedOption) => {
            setReportLanguage(valueOfSelectedOption)
          }}
          options={[
            {
              value: 'en',
              label: 'English',
            },
            {
              value: 'mr',
              label: 'Marathi',
            },
            {
              value: 'ta',
              label: 'Tamil',
            },
            {
              value: 'te',
              label: 'Telugu',
            },
          ]}
        />
        <Button 
          onClick={() => handleGeneratePDFForSmartReportClick()}
          disabled={!(selectedRowKeys.length !== 0 && reportLanguage.length !== 0)}
        >
          Generate Report PDF
        </Button>
      </Space>
      <OCTable
        ref={smartRationReportRef}
        reportData={reportData}
        setReportData={setReportData}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        tableConfiguration={smartRationTableConfiguration}
        loadReportCallback={loadReportCB}
      />
    </Spin>
    
  )
}

const animalListTableConfiguration = {
  // tableNavigationUrl: '../oc/manure-to-be-contacted-customers',
  enableSelection: true,
  // defaultPageSize: 10,
  enableHorizontalScrolling: true,
  // showTableHeader: true,
  // omniSearchEnabled: true,
  // enableColumnConfiguration: true,
  getAllDataAtOnce: true,
  disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'animal_id',
  columns: [
    {
      title: "Animal Id",
      key: "animal_id",
    },
    {
      title: "Animal Ear Tag",
      key: "ear_tag_1",
    },
    {
      title: "Animal Name",
      key: "animal_name",
    },
    {
      title: "Breed",
      key: "breed",
    },
    {
      title: "Age",
      key: "age",
    },
    {
      title: "Animal Stage",
      key: "animal_stage",
    },
    {
      title: "No of Calvings",
      key: "number_of_calvings",
    },
    {
      title: "No of Months Pregnant",
      key: "number_of_months_pregnant",
    },
    {
      title: "Last Calving Date",
      key: "last_calving_date",
    },
    {
      title: "Months Since Calving",
      key: "number_of_months_since_last_calving",
    },
  ],
  columnConfiguration: {
    ear_tag_1: {
      type: 'string',
      dataType: 'string',
    },
    animal_name: {
      type: 'string',
      dataType: 'string',
    },
    last_calving_date: {
      formatType: 'date',
      format: 'DD-MMM-YYYY',
    },
    age: {
      formatType: 'double',
      format: {decimalPlaces: 1},
    },
  }
}

const loadAnimalReport = async (userToken, queryParams, additionalParams, customerId) => {    
  try {
    const headers = {
      useCase: 'Get Customer Animals',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getAnimalsForFarmer(headers, queryParams, customerId)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const OCFarmerDetails = () => {
  const params = useParams()
  const navigate = useNavigate()
  const location = useLocation()
  const { pathname, search } = location

  const userToken = useSelector((state)=>state.user.userToken)
  const [searchParams] = useSearchParams()
  const [customerTypeId, setCustomerTypeId] = useState(undefined)
  useEffect(()=>{
    if (searchParams && searchParams.get('cti')) {
      let customerTypeIdFromParams = searchParams.get('cti')
      customerTypeIdFromParams = parseInt(customerTypeIdFromParams)
      if (typeof customerTypeIdFromParams === 'number' && String(customerTypeIdFromParams) === searchParams.get('cti')) {
        //valid customer type
        setCustomerTypeId(customerTypeIdFromParams)
      } else {
        console.log('Invalid Customer Type')
        // customerTypeId = undefined
      }
    }
  },[])

  // const uncompressedFilterParamsJsonString = decodeURIComponent(searchParams.get('qP'))// urlEncodedJson)
  // const queryParams = JSON.parse(uncompressedFilterParamsJsonString)
  // const customerId = queryParams.customerId
  const customerId = params.customerId
  const addCustomerView = (customerId === '-1')
  const isSmartRationRole = searchParams && searchParams.get('role') && searchParams.get('role') === 'sr'
  const tabKeyFromURL = (params.tabKey !== undefined ? params.tabKey : '1')
  farmerBasicDetailsFormDefinition.formControls.farmerThumbnail.uploadEntityInformation.entity_1_entity_uuid = customerId

  let defaultPanelOpenKey = ['3']
  if (addCustomerView) {
    defaultPanelOpenKey = ['1']
  }

  const [farmerBasicDetailsFormState, setFarmerBasicDetailsFormState] = useState(farmerBasicDetailsFormInitialState)
  const [selectedAnimalRowKeys, setSelectedAnimalRowKeys] = useState([])
  // const [isSmartRationCustomer, setSmartRationCustomer] = useState(false)
  const [formResetValue, setFormResetValue] = useState(0)
  const [loading, setLoading] = useState(false)

  const [animalReportData, setAnimalReportData] = useState([])
  const animalListTableRef = createRef()

  const [reportLanguage, setReportLanguage] = useState(['en'])

  const additionalParams = []
  additionalParams.push(userToken)
  additionalParams.push(farmerBasicDetailsFormState)
  additionalParams.push(setFarmerBasicDetailsFormState)

  const addFarmer = async () => {
    const [currentFormState, validationFailed] = validateFormElements2(
      newFarmerBasicDetailsFormDefinition,
      farmerBasicDetailsFormState,
      setFarmerBasicDetailsFormState
    )
    if (validationFailed) {
      // do something
      console.log("Validation Failed")
      ToastersService.failureToast("Validation failed!")
    } else {
      console.log("additional checks")
      let farmerTaluk = getValueFormControl(newFarmerBasicDetailsFormDefinition, 'farmerTaluk')
      if (farmerTaluk === undefined) {
        farmerTaluk = []
      }
      let farmerVillage = getValueFormControl(newFarmerBasicDetailsFormDefinition, 'farmerVillage')
      if (farmerVillage === undefined) {
        farmerVillage = []
      }
      if (farmerTaluk.length === 0 && farmerVillage.length === 0) {
        ToastersService.failureToast("You need to select at least a taluk, preferably the right village")
      } else if (customerTypeId === undefined) {
        ToastersService.failureToast("Something wrong with the incoming data, please contact system administrator")
      } else {
        console.log("you can save now")
        const updatedCurrentFormState = assignValuesFromControlReferencesToFormState(newFarmerBasicDetailsFormDefinition, currentFormState, setFarmerBasicDetailsFormState)
        const extractedValues = extractChangedValues(newFarmerBasicDetailsFormDefinition, updatedCurrentFormState)
        const fieldsToIgnoreInSaving = ['farmer_state', 'farmer_district']
        for (const fieldChanged of Object.keys(extractedValues)) {
          if (fieldsToIgnoreInSaving.includes(fieldChanged)) {
            delete extractedValues[fieldChanged]
          }
        }
        if (extractedValues['mobile_number'] && extractedValues['mobile_number'] !== null) {
          extractedValues['mobile_number'] = '+91' + extractedValues['mobile_number']
        }
        extractedValues['customer_type_id'] = customerTypeId
        console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValues)
        const setFarmerDataResponse = await setFarmerData(userToken, undefined, extractedValues)
        if (setFarmerDataResponse.return_code === 0) {
          const newCustomerIndicatorString = '-1'
          const customerId = setFarmerDataResponse.customerId
          const lastIndex = pathname.lastIndexOf(newCustomerIndicatorString)
          const newPathName = pathname.substring(0, lastIndex) + customerId + pathname.substring(lastIndex + newCustomerIndicatorString.length)
          navigate({
            // pathname: `../e/farmer-animal-details/${customerId}/animal/${animalId}/${tabKey}`,
            pathname: newPathName,
            // search: search,
            replace: true
          })
          setFarmerBasicDetailsFormState(farmerBasicDetailsFormInitialState)
          setFormResetValue(formResetValue + 1)
          ToastersService.successToast("Successfully updated farmer data!")
        } else {
          ToastersService.failureToast("Failed to update farmer data!")
        }
      }
    }
  }
  
  const setBasicFarmerData = async () => {
    const response = await loadBasicFarmerData(userToken, customerId)
    if (response.return_code === 0) {
      if (response.result['customer_type_id'] !== undefined && response.result['customer_type_id'] !== null) {
        setCustomerTypeId(response.result['customer_type_id'])
      }
      assignDataToFormState(
        farmerBasicDetailsFormDefinition,
        response.result,
        farmerBasicDetailsFormState,
        setFarmerBasicDetailsFormState
      )
      /* if (response.result['customer_type_id'] === 1000220009) {
        setSmartRationCustomer(true)
      } */
    }
  }

  const loadBasicFormForAddingCustomer = async () => {
    const allStatesResponse = await getGeographyDropdownData(userToken, {parentGeographyIdArray: undefined, parentToChildEntityRelationshipTypeId: undefined, parentGeographyTypeId: 1000320001, childGeographyTypeId: undefined})
    if (allStatesResponse.return_code === 0) {
      const stateList = assignL10NObjectOrObjectArrayToItSelf(allStatesResponse.list_data, ['geography_name_l10n'])
      // resetSelectWithNewOptionsInForm(newFarmerBasicDetailsFormDefinition, 'farmerState', stateList)
      // resetSelectWithNewOptionsInForm(newFarmerBasicDetailsFormDefinition, 'farmerDistrict', [])
      // resetSelectWithNewOptionsInForm(newFarmerBasicDetailsFormDefinition, 'farmerTaluk', [])
      // resetSelectWithNewOptionsInForm(newFarmerBasicDetailsFormDefinition, 'farmerVillage', [])
      const updatedFormState = assignDataToFormState(
        newFarmerBasicDetailsFormDefinition,
        {farmer_state_list: stateList, farmer_state: [], farmer_district_list: [], farmer_district: [], farmer_taluka_1_list: [], farmer_taluka_1: [], farmer_village_1_list: [], farmer_village_1: [], },
        farmerBasicDetailsFormState,
        setFarmerBasicDetailsFormState
      )
      return updatedFormState
    } else {
      ToastersService.failureToast('Could not load states')
    }
  }
  
  const asyncUseEffect = async () => {
    try {
      setLoading(true)
      // assignReferencesAsListToForm(dairyFarmDetailsFormDefinition)

      /*
      const referenceMap = global.references.referenceCategoryToReferencesMap
      const foliages = referenceMap[10001300]
      const foliageReferenceIdArray = foliages.map(object => object['reference_id'])
      const concentrates = referenceMap[10013000]
      const concentrateReferenceIdArray = concentrates.map(object => object['reference_id'])
      const otherNutrients = referenceMap[10013005]
      const otherNutrientReferenceIdArray = otherNutrients.map(object => object['reference_id'])
      const feedReferencesWithoutCheckingUsed = [...foliages, ...concentrates, ...otherNutrients]
      const feedReferences = feedReferencesWithoutCheckingUsed.filter(object => {
        let returnValue = false
        if (object && object['reference_information'] && object['reference_information']['feed_properties'] && object['reference_information']['feed_properties']['used'] === 'true') {
          returnValue = true
        }
        return returnValue
      })
      */
      const referenceMap = global.references.referenceCategoryToReferencesMap
      let foliages = referenceMap[10001300]
      let concentrates = referenceMap[10013000]
      foliages = foliages.filter(object => {
        let returnValue = false
        if (object['reference_information'] && object['reference_information']['feed_properties'] && object['reference_information']['feed_properties']['used'] === 'true') {
          returnValue = true
        }
        return returnValue
      })

      concentrates = concentrates.filter(object => {
        let returnValue = false
        if (object['reference_information'] && object['reference_information']['feed_properties'] && object['reference_information']['feed_properties']['used'] === 'true') {
          returnValue = true
        }
        return returnValue
      })
      foliages = assignL10NObjectOrObjectArrayToItSelf(foliages, 'reference_name_l10n')
      concentrates = assignL10NObjectOrObjectArrayToItSelf(concentrates, 'reference_name_l10n')

      dairyFarmDetailsFormDefinition.formControls['farmerFoliageCrops'].list = foliages
      dairyFarmDetailsFormDefinition.formControls['farmerConcentrates'].list = concentrates
      if (addCustomerView) {
        await loadBasicFormForAddingCustomer()
      } else {
        await setBasicFarmerData()
      }
    } catch (error) {
      ToastersService.failureToast('Could not load initial data')
    } finally {
      setLoading(false)
    }
  }
  
  useEffect(()=>{
    asyncUseEffect()
  },[formResetValue])

  const onTabChange = (tabKey) => {
    console.log('OCFD oTC 1, tabKey = ', tabKey)
    const currentSearchParams = location.search
    // setTabKey(tabKey)
    navigate({
      pathname: `/e/farmer-details/${customerId}/${tabKey}`,
      search: currentSearchParams,
      replace: true
    })
  }

  const handleAddAnimalClick = (event) => {
    const navigationLink = `/e/farmer-animal-details/${customerId}/animal/-1`
    if (event.ctrlKey || event.metaKey) {
      window.open(navigationLink, '_blank')
    } else {
      navigate({
        pathname: navigationLink,
        // search: `?qP=${encodedParamsAsString}`
      })
    }
  }

  const handleViewAnimalClick = (event) => {
    console.log('DCOFSA hVAC 1')
    const selectedRow = animalReportData.filter((object) => {
      return (object['animal_id'] === selectedAnimalRowKeys[0])
    })

    const animalId = selectedRow[0]['animal_id']
    // navigate({
    //   pathname: `../oc/farmer-details`,
    //   search: `?qP=${encodedParamsAsString}`
    // })
    const navigationLink = `/e/farmer-animal-details/${customerId}/animal/${animalId}`
    // const linkQueryParam = '' // isSmartRationRole ? 'role=sr' : ''
    const linkQueryParam = isSmartRationRole ? 'role=sr' : ''
    if (event.ctrlKey || event.metaKey) {
      window.open(navigationLink + '?' + linkQueryParam, '_blank')
    } else {
      navigate({
        pathname: navigationLink,
        // search: `?qP=${encodedParamsAsString}`
        search: linkQueryParam === '' ? undefined : linkQueryParam
      })
    }
  }

  const handleGenerateSmartRationClick = async (event) => {
    try {
      setLoading(true)
      // collate farmer data
      const feedRelatedInformationOfCustomerResponse = await getFarmerData(userToken, customerId, ['farmer_foliage_available_1', 'farmer_concentrates_available_1'])
      
      if (feedRelatedInformationOfCustomerResponse.return_code !== 0) {
        ToastersService.failureToast('Could not get farmer information needed to generate feed')
        return
      }
      const farmerInformationNeededForSmartRationGeneration = feedRelatedInformationOfCustomerResponse.data
      // collate animal data
      const getAnimalDataReportResponse = await collateAnimalInformationForSmartRation(userToken, customerId, selectedAnimalRowKeys)
      if (getAnimalDataReportResponse.return_code !== 0) {
        ToastersService.failureToast('Could not get animal information needed to generate feed')
        return 
      }
      const animalIdToAnimalInformationNeededForSmartRationGenerationMap = getAnimalDataReportResponse.animalIdToAnimalInformationNeededForSmartRationGenerationMap

      // collate smart ration for farmer and animal
      const [systemGeneratedReportId, constraintGeneratedReportId] = await generateSmartRationReportFarmerAnimals(userToken, customerId, farmerInformationNeededForSmartRationGeneration, animalIdToAnimalInformationNeededForSmartRationGenerationMap)

      const reportGenerationStructureArray = []

      if (systemGeneratedReportId) {
        reportGenerationStructureArray.push({asset_id: systemGeneratedReportId, report_type_in_file_name: 'Type 1', document_type_id: 1000260024})
      } else {
        ToastersService.failureToast('Failure in generating Optimized Smart Ration')  
        return {return_code: -1}
      }
      if (constraintGeneratedReportId) {
        reportGenerationStructureArray.push({asset_id: constraintGeneratedReportId, report_type_in_file_name: 'Type 2', document_type_id: 1000260023})
      } else {
        ToastersService.failureToast('Failure in generating Farmer Foliage Constraint Based Smart Ration')  
        return {return_code: -1}
      }
      if (reportGenerationStructureArray.length === 0) {
        ToastersService.failureToast('No Smart Ration Report Generated For Data')
        return
      }

      // get smart ration data for farmer and animal
      if (reportLanguage.length == 0) {
        ToastersService.warningToast('No language selected to generate report. You can always generate or overwrite reports in the Smart Ration Reports Tab')
        return
      }
      const reportGenerationResponseCode = await getSmartRationDataCreateReportUploadToS3AndCreateDocument(
        userToken, customerId, reportLanguage, reportGenerationStructureArray)
      console.log('reportGenerationResponseCode = ', reportGenerationResponseCode)
      return reportGenerationResponseCode
    } catch (error) {
      ToastersService.failureToast('Failure in generating Smart Ration')
    } finally {
      setLoading(false)
    }
  }

  const handleDummyButtonClick = async (event) => {
    // 'b93a01dc-c753-11ee-9c83-5329f4db14a7'
    // 'c7571c50-c753-11ee-9c83-2bda481a321f'
    // 'a866a00a-c775-11ee-81c3-c319381ae819'
    // const response = await generateSmartRationReportUploadToS3AndCreateDocument('b93a01dc-c753-11ee-9c83-5329f4db14a7', 'Type 1', 1000260024)
    try {
      setLoading(true)
      const response = await getSmartRationDataCreateReportUploadToS3AndCreateDocument(
        userToken, customerId, reportLanguage,
        [
          {asset_id: 'a28a06ea-cbc1-11ee-ae4c-cfcb4c57cf71', report_type_in_file_name: 'Type 1', document_type_id: 1000260024},
          {asset_id: 'a40994c2-cbc1-11ee-ae4c-47fbfdc8bbda', report_type_in_file_name: 'Type 2', document_type_id: 1000260023}
        ]
      )
      console.log('hDBC response = ', response)
    } catch (error) {
      ToastersService.failureToast('Failure in generating Smart Ration')
    } finally {
      setLoading(false)
    }
  }

  const loadAnimalReportCB = async (reportQueryParams, additionalParams) => {
    reportQueryParams.excludeBDM = true    
    const additionalClassifierKeysToLoad = ['number_of_months_pregnant_1', 'number_of_months_pregnant_as_of_date_1', 'animal_age_1', 'age_of_animal_as_of_1']
    const fieldsToExclude = ['number_of_months_pregnant', 'number_of_months_since_last_calving', 'age']
    reportQueryParams.selectedColumns = lodashObject.union(reportQueryParams.selectedColumns, additionalClassifierKeysToLoad);
    // Step 2: Remove items listed in fieldsToExclude
    reportQueryParams.selectedColumns = lodashObject.difference(reportQueryParams.selectedColumns, fieldsToExclude);
    const animalReportObject = await loadAnimalReport(userToken, reportQueryParams, additionalParams, customerId)
    animalReportObject.report.map((reportItem, index) => {
      const noOfMonthsPregnant = reportItem['number_of_months_pregnant_1']
      const noOfMonthsPregnantAsOfDate = reportItem['number_of_months_pregnant_as_of_date_1']
      const lastCalvingDate = reportItem['last_calving_date']
      const ageStored = reportItem['animal_age_1']
      const ageAsOfDate = reportItem['age_of_animal_as_of_1']
      if (noOfMonthsPregnant && noOfMonthsPregnantAsOfDate && noOfMonthsPregnant !== 0) {
        const numberOfMonthsPregnantToday = moment().diff(moment(noOfMonthsPregnantAsOfDate), 'months') + noOfMonthsPregnant
        if (numberOfMonthsPregnantToday < 9) {
          reportItem['number_of_months_pregnant'] = numberOfMonthsPregnantToday
        }
      }
      if (lastCalvingDate) {
        const monthsSinceLastCalving = moment().diff(moment(lastCalvingDate), 'months')
        reportItem['number_of_months_since_last_calving'] = monthsSinceLastCalving
      }
      if (ageStored && ageAsOfDate) {
        const ageAsOfToday = moment().diff(moment(ageAsOfDate), 'months')/12 + ageStored
        reportItem['age'] = ageAsOfToday
      }
      delete reportItem['number_of_months_pregnant_1']
      delete reportItem['number_of_months_pregnant_as_of_date_1']
      delete reportItem['animal_age_1']
      delete reportItem['age_of_animal_as_of_1']
    })
    return animalReportObject
  }

  const tabItems = [
    {
      key: '1',
      label: 'Dairy Farm',
      children: (
        <DairyFarmTab/>
      ),
    },
    {
      key: '2',
      label: 'Crops, etc.',
      children: (
        <CropFarmTab/>
      ),
    },
    ...(!(customerTypeId === 1000220009 || isSmartRationRole) ? [] : [
      {
        key: '3',
        label: 'Smart Ration Reports',
        children: <SmartRationReportTab />,
      },
    ]),
    {
      key: '4',
      label: 'Finance, et al',
      children: 'Content of Finance, et al',
    },
  ]  

  return (
    <>
      <Spin spinning={loading} tip="Loading...">
        <Collapse defaultActiveKey={defaultPanelOpenKey} >
          <Panel header="Basic Farmer Details" key="1">
            <>
            <KrushalOCForm
              formState={farmerBasicDetailsFormState}
              setFormState={setFarmerBasicDetailsFormState}
              formDefinition={addCustomerView ? newFarmerBasicDetailsFormDefinition : farmerBasicDetailsFormDefinition}
              additionalParams={additionalParams}
            />
            {!(addCustomerView && customerTypeId === 1000220009) ? null : (
              <Button
                onClick={() => addFarmer()}
                >
                Add Farmer
              </Button>
            )}
            </>
          </Panel>
          {addCustomerView ? null : (
            <Panel header="Animal List" key="2">
              <>
                <Space>
                  {addCustomerView || customerTypeId !== 1000220009 ? null : (
                    <Button size="small" style={{ marginLeft: 8 }} disabled={(selectedAnimalRowKeys.length !== 0)} onClick={handleAddAnimalClick}>Add Animal</Button>
                  )}
                  <Button size="small" style={{ marginLeft: 8 }} disabled={(selectedAnimalRowKeys.length !== 1)} onClick={handleViewAnimalClick}>View Animal Details</Button>
                  {addCustomerView || (customerTypeId !== 1000220009 && !isSmartRationRole) ? null : (
                    <>
                      <Select
                        mode="multiple"
                        value={reportLanguage}
                        style={{
                          width: 120,
                        }}
                        onChange={(valueOfSelectedOption) => {
                          setReportLanguage(valueOfSelectedOption)
                        }}
                        options={[
                          {
                            value: 'en',
                            label: 'English',
                          },
                          {
                            value: 'mr',
                            label: 'Marathi',
                          },
                          {
                            value: 'ta',
                            label: 'Tamil',
                          },
                          {
                            value: 'te',
                            label: 'Telugu',
                          },
                        ]}
                      />
                      <Button size="small" disabled={selectedAnimalRowKeys.length === 0}style={{ marginLeft: 8 }} onClick={handleGenerateSmartRationClick}>Generate Smart Ration</Button>
                    </>
                  )}
                  {/* <Button size="small" style={{ marginLeft: 8 }} onClick={handleDummyButtonClick}>Dummy</Button> */}
                </Space>
                <OCTable
                  ref={animalListTableRef}
                  /* tableKey={tableKey}
                  setTableKey={setTableKey} */
                  reportData={animalReportData}
                  setReportData={setAnimalReportData}
                  tableConfiguration={animalListTableConfiguration}
                  selectedRowKeys={selectedAnimalRowKeys}
                  setSelectedRowKeys={setSelectedAnimalRowKeys}
                  // postInitialLoadCallback={postInitialLoadCB}
                  loadReportCallback={loadAnimalReportCB}
                  // loadInitialFilterDataCallback={loadInitialFilterDataCB}
                  // parentPageParams={pageParams}
                  // showTableHeader={true}
                  // omniSearchRowEnabled={true}
                  // omniSearchRowBeginning={backToQueueListLinkUI}
                  // omniSearchRowMiddle={viewRelatedAnimalButtons}
                  // actionsRow={backToQueueListLinkUIAndViewRelatedAnimalButtons}
                />
              </>
            </Panel>
          )}
          {addCustomerView ? null : (
            <Panel header="Farmer Details" key="3">
              <Tabs 
                // defaultActiveKey="1"
                // defaultActiveKey={defaultTabKey}        
                activeKey={tabKeyFromURL}
                items={tabItems}
                destroyInactiveTabPane={true}
                onChange={onTabChange}
                />
            </Panel>
          )}
        </Collapse>
      </Spin>
    </>
    
  )
}

export default OCFarmerDetails