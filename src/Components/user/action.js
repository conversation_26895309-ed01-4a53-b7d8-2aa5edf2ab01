import { setUserToken, setUserData, setFirebaseUid, setUserAppGroups } from './reducer';
import { instance } from "../../Services/api.service";
import ToastersService from "../../Services/toasters.service";

export function checkAccessToken(navigate) {
    return function (dispatch) {
        let routeUri = window.location.href.split('/').slice(3).join('/') === ''? 
                        '/farmer' : window.location.href.split('/').slice(3).join('/')
        console.log("routeUri",routeUri)
        let accessToken = localStorage.getItem('accessToken');
        let refreshToken = localStorage.getItem('refreshToken');
        if (accessToken && refreshToken) {
            dispatch(updateUserToken(accessToken, refreshToken, navigate));
            navigate(routeUri)
        } else {
            navigate('/login')
        }
    }
}

export function updateUserToken(accessToken, refreshToken, navigate) {
    return function (dispatch) {
        console.log("accessToken",accessToken);
        console.log("refreshToken",refreshToken);
        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', refreshToken);
        dispatch(setUserToken({ accessToken, refreshToken }))
        dispatch(getUserProfileData(accessToken, navigate))
    }
}

export function updateUserToken2(accessToken, refreshToken) {
    return function (dispatch) {
        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', refreshToken);
        dispatch(setUserToken({ accessToken, refreshToken }))
    }
}

export function getUserProfileData(accessToken, navigate) {
    return async function (dispatch) {
        try {
            const response = await instance({
                url: "/common/resolve-token",
                method: "GET",
                headers: {
                    token: accessToken,
                },
            });
            if (response?.status === 200) {
                dispatch(setUserData(response.data))
            }
        } catch (error) {
            if (error?.response?.status === 401) {
                ToastersService.failureToast("Session Expired!");
                dispatch(clearUserToken(navigate, '/login'));
            }
            if (error?.response?.status === 403) {
                dispatch(clearUserToken(navigate, "/access-denied", "Forbidden Access!"));
            }
        }
    }
}

export function clearUserToken(navigate, redirectRoute = `/logout`, message) {
    return function (dispatch) {
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        if (message) {
            ToastersService.successToast(message);
        }
        navigate(redirectRoute);
    }
}

export function updateFirebaseUid(firebaseUid) {
    return function (dispatch) {
        dispatch(setFirebaseUid({ firebaseUid }))
    }
}

export function updateUserAppGroups(appGroups) {
    return function (dispatch) {
        dispatch(setUserAppGroups({ appGroups }))
    }
}