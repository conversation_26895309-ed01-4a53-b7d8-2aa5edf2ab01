import React, { useEffect, useState } from "react";
import { useSelector } from 'react-redux'
import { updateUserToken, updateFirebaseUid, updateUserAppGroups } from './action'

const lodashObject = require("lodash")

const IT_ADMIN = 1001150001
const EXEC_TEAM = 1001150002
const CS_LEADERSHIP = 1001150003
const CS_TEAM = 1001150004
const FINANCE_LEADERSHIP = 1001150005
const FINANCE_TEAM = 1001150006
const VET_LEADERSHIP = 1001150007
const VET_OPS = 1001150008
const CENTRAL_VET = 1001150009
const FIELD_VET = 1001150010
const SALES_LEADERSHIP = 1001150011
const SALES_OPS = 1001150012
const SALES_TEAM = 1001150013
const MARKETING_LEADERSHIP = 1001150014
const MARKETING_TEAM = 1001150015
const OPS_LEADERSHIP = 1001150016
const OPS_TEAM = 1001150017

const { checkIfUserExistsInDBAndGetRelatedData } = require('../../router')

const authenticateUserInDBAndGetAccessAppGroups = async (userToken) => {
  try {
    console.log('L aUIDAGAAG 1')
    const headers = {
      useCase: 'Get User Authentication Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await checkIfUserExistsInDBAndGetRelatedData(headers, {firebaseUser: userToken})
    return response.data
  } catch (error) {
    console.log('L aUIDAGAAG 10, error')
    console.log('L aUIDAGAAG 10a, error = ', error)
    throw error
  }
}

const authenticateFromBackend = async (user, dispatch, navigate) => {
  const authenticationResponse = await authenticateUserInDBAndGetAccessAppGroups(user)
  if (authenticationResponse.return_code === 0) {
    dispatch(updateUserToken(user.accessToken, user.stsTokenManager.refreshToken, navigate));
    dispatch(updateFirebaseUid(user.uid))
    dispatch(updateUserAppGroups(authenticationResponse.app_access_groups))
    global.userPreferences.appAccessGroups = authenticationResponse.app_access_groups
  }
  return authenticationResponse
}

const withAuthorization = (Component, acceptedGroups) => {
  return (props) => {
    const groupsToCheckOn = lodashObject.cloneDeep(acceptedGroups)
    groupsToCheckOn.push(EXEC_TEAM)

    const usersAppGroups = useSelector((state) => state.user.appGroups)

    const commonGroups = usersAppGroups !== undefined && usersAppGroups !== null && Array.isArray(usersAppGroups) ? lodashObject.intersection(groupsToCheckOn, usersAppGroups) : []

    // Perform your authorization logic
    const isAuthorized = (commonGroups.length > 0) //false/* Your logic to check if userRoles include acceptedRoles */;

    if (isAuthorized) {
      return <Component {...props} />
    } else {
      return (
        <div>
          You are not authorized to view this component
          {/* You can add additional messages or redirect logic here */}
        </div>
      )
    }
  }
}

// export default withAuthorization;

export {
  IT_ADMIN,
  EXEC_TEAM,
  CS_LEADERSHIP,
  CS_TEAM,
  FINANCE_LEADERSHIP,
  FINANCE_TEAM,
  VET_LEADERSHIP,
  VET_OPS,
  CENTRAL_VET,
  FIELD_VET,
  SALES_LEADERSHIP,
  SALES_OPS,
  SALES_TEAM,
  MARKETING_LEADERSHIP,
  MARKETING_TEAM,
  OPS_LEADERSHIP,
  OPS_TEAM,
  authenticateFromBackend,  
  withAuthorization,
}
