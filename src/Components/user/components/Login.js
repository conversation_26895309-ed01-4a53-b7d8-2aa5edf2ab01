import { useState } from "react"
import { Col, Container, Row } from "react-bootstrap"
import {useDispatch} from 'react-redux'
import {
  RecaptchaVerifier,
  signInWithPhoneNumber,
  signInWithPopup,
  GoogleAuthProvider,
} from "firebase/auth"
import { auth } from "../../../Config/firebase.config"

import ToastersService from "../../../Services/toasters.service"
import "./Login.css"
import Loader from "../../Loader/Loader"
import { useNavigate } from "react-router-dom"

const { authenticateFromBackend } = require('../authorize')

const Login = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [showOTPpage, setShowOTPpage] = useState(false);
  const [sendingOTPLoader, setSendingOTPLoader] = useState(false);
  const [verifyingOTPLoader, setVerifyingOTPLoader] = useState(false);
  const [mobileNumber, setMobileNumber] = useState("");
  const [otpInput, setOTPInput] = useState("");
  const [countryCode, setCountryCode] = useState("+91");

  const requestfirebaseotp = () => {
    if (!window.recaptchaVerifier) {
      window.recaptchaVerifier = new RecaptchaVerifier(
        "recaptcha-container",
        {
          size: "invisible",
          callback: (response) => {
            // reCAPTCHA solved, allow signInWithPhoneNumber.
          },
        },
        auth
      );
    }
  }

  const navigateBasedOnAuthenticationResponse = async (authenticationResponse) => {
    if (authenticationResponse.return_code === 0) {
      setVerifyingOTPLoader(false);
      ToastersService.successToast("Authentication Successful!")
      navigate("/", { replace: true });
    } else if (authenticationResponse.return_code === -1) {
      ToastersService.failureToast("Your email or phone does not exist in the system. Please check the registered number and try logging in again")
      navigate("/login")
    } else if (authenticationResponse.return_code === -2) {
      ToastersService.failureToast("Please contact the email address")
      navigate("/login");
    }
  }

  const verifyOTPHandler = async () => {
    try {
      console.log("Verify OTP", otpInput);
      if (otpInput.toString().length !== 6) {
        ToastersService.failureToast("Invalid OTP!");
      } else {
        setVerifyingOTPLoader(true);
        let confirmationResult = window.confirmationResult;
        const result1 = await confirmationResult.confirm(otpInput)
        const user = result1.user;
        console.log(user);
        const authenticationResponse = await authenticateFromBackend(user, dispatch, navigate)
        await navigateBasedOnAuthenticationResponse(authenticationResponse)
      }
    } catch (error) {
      console.log('L vOH 10, error')
      console.log('L vOH 10a, error = ', error)
      setVerifyingOTPLoader(false);
      ToastersService.failureToast("Invalid OTP!");
      // throw error
    }
  };

  const sendOTPHandler = () => {
    console.log(countryCode, mobileNumber);
    if (mobileNumber.toString().length !== 10) {
      ToastersService.failureToast("Enter a valid number!");
    } else {
      setSendingOTPLoader(true);
      var result;
      requestfirebaseotp();
      let verifier = window.recaptchaVerifier;
      signInWithPhoneNumber(auth, countryCode + mobileNumber, verifier)
        .then((confirmationResult) => {
          window.confirmationResult = confirmationResult;
          result = confirmationResult;
          console.log(result);
          ToastersService.successToast("OTP sent successfully!");
          setSendingOTPLoader(false);
          setMobileNumber("");
          setShowOTPpage(true);
        })
        .catch((err) => {
          console.log("Firebase Error", err);
          ToastersService.failureToast("Something went wrong! Try again.");
          setSendingOTPLoader(false);
        });
    }
  };

  const provider = new GoogleAuthProvider();

  const googleSignInHandler = async () => {
    try {
      console.log(auth);
      provider.setCustomParameters({
        hd: "krushal.in"
      });
      const data = await signInWithPopup(auth, provider)
      const credential = GoogleAuthProvider.credentialFromResult(data);
      const token = credential.accessToken;
      console.log(token);
      const user = data.user
      console.log(user);
      const authenticationResponse = await authenticateFromBackend(user, dispatch, navigate)
      await navigateBasedOnAuthenticationResponse(authenticationResponse)
    /* dispatch(updateUserToken(user.accessToken, user.stsTokenManager.refreshToken, navigate));
      // setVerifyingOTPLoader(false);
      ToastersService.successToast("Authentication Successful!")
      const authenticationResponse = await authenticateUserInDBAndGetAccessAppGroups()
      navigate("/dashboard", { replace: true }); */
    } catch(error) {
      console.log(error);
      ToastersService.failureToast("Something whent wrong!");
      // const errorCode = error.code;
      // const errorMessage = error.message;
      // const email = error.customData.email;
      // console.log(errorCode, errorMessage, email);
      // const credential = GoogleAuthProvider.credentialFromError(error);
    }
  };

  const countryCodeInputHandler = (event) => {
    // console.log(event.target.value);
    setCountryCode(event.target.value);
  };

  const mobileNumberInputHandler = (event) => {
    // console.log(event.target.value);
    setMobileNumber(event.target.value);
  };

  const otpUnputHandler = (event) => {
    // console.log(event.target.value);
    setOTPInput(event.target.value);
  };

  return (
    <>
      <Container className="my-5">
        <Row>
          <Col className="login__login-card">
            <img
              alt=""
              src={process.env.PUBLIC_URL + "krushal.png"}
              width="140px"
            />
            <p
              style={{ fontSize: "30px", fontWeight: "700", marginTop: "5rem" }}
            >
              Sign In
            </p>

            {showOTPpage === false ? (
              <>
                <p className="login__input-label mb-1 mt-5">Enter Mobile No.</p>
                <div className="login__phone-number-input">
                  <div>
                    <select
                      className="login__select-field"
                      onChange={countryCodeInputHandler}
                    >
                      <option value="+91">+91</option>
                      <option value="+1">+1</option>
                    </select>
                  </div>
                  <div>
                    <input
                      type="number"
                      id="mobile-input"
                      className="login__input-field"
                      placeholder="Enter here"
                      onChange={mobileNumberInputHandler}
                    />
                  </div>
                </div>

                {sendingOTPLoader === true ? (
                  <div className="login__loader-div">
                    <Loader />
                  </div>
                ) : (
                  <>
                    <div className="login__button" onClick={sendOTPHandler}>
                      Send OTP
                    </div>
                  </>
                )}
              </>
            ) : (
              <>
                <p className="login__input-label mb-1 mt-5">Enter OTP</p>
                <input
                  type="number"
                  id="otp-input"
                  className="login__otp-input-field"
                  placeholder="Enter here"
                  onChange={otpUnputHandler}
                />

                {verifyingOTPLoader === true ? (
                  <div className="login__loader-div">
                    <Loader />
                  </div>
                ) : (
                  <div className="login__button" onClick={verifyOTPHandler}>
                    Verify OTP
                  </div>
                )}
              </>
            )}
            <p className="mt-5 text-center" style={{ width: "300px" }}>
              OR
            </p>

            <button
              className="btn google-sign-in-btn"
              onClick={googleSignInHandler}
            >
              <img
                alt=""
                src={process.env.PUBLIC_URL + "Google-icon.png"}
                width="30px"
                className="me-2"
              />
              Sign-in with Google
            </button>
          </Col>
          <Col className="login__hero-image-div">
            <img
              alt=""
              src={process.env.PUBLIC_URL + "login-hero-image-next.png"}
              width="60%"
            />
          </Col>
        </Row>
      </Container>
      <div id="recaptcha-container"></div>
    </>
  );
};

export default Login;
