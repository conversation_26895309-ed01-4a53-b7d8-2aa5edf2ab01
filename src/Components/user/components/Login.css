.login__hero-image-div {
  border: 1px solid silver;
  height: 80vh;
  background-color: #000842;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 15px;
  overflow: hidden;
}

.login__input-label {
  font-size: 20px;
  font-weight: 500;
  color: #999999;
}

.login__phone-number-input {
  border: 1px solid silver;
  border-radius: 5px;
  width: 302px;
  display: flex;
  align-items: center;
}

.login__select-field {
  width: 70px;
  border: none;
  background-color: #f5fbfd;
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
}

.login__input-field {
  border: none;
  background-color: #f5fbfd;
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  width: 230px;
}

.login__otp-input-field {
  border: 1px solid silver;
  background-color: #f5fbfd;
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  width: 300px;
}

.login__select-field:focus {
  outline: none;
  border: 1px solid #673ab7;
  background-color: #f7f4fb;
}

.login__input-field:focus {
  outline: none;
  border: 1px solid #673ab7;
  background-color: #f7f4fb;
}

.login__otp-input-field:focus {
  outline: none;
  border: 1px solid #673ab7;
  background-color: #f7f4fb;
}

.login__button {
  
  background-color: #673ab7;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300px;
  font-size: 17px;
  font-weight: 600;
  height: 50px;
  cursor: pointer;
  box-shadow: 0px 4px 26px rgba(0, 0, 0, 0.25);
  border-radius: 32px;
  margin-top: 2rem;
}

.login__loader-div {
  margin-top: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300px;
}

.login-input-box {
  width: 100%;
  height: 80px;
  display: block;
}
.login-form .login-input-box input {
  border: none;
  font-family: "Poppins";
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  padding-left: 10px;
  width: 60%;
}
.login-form .login-input-box input::placeholder {
  border: none;
  font-family: "Poppins";
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  padding-left: 10px;
  width: 60%;
}
.login-form .login-input-box input:focus-visible {
  outline: none;
}

.imput-border-line {
  width: 70%;
  /* width: 14px; */
  height: 2px;
  background: #000000;
}
.login-form .submit-btn {
  width: 70%;
  background: #673ab7;
  box-shadow: 0px 4px 26px rgb(0 0 0 / 25%);
  border-radius: 32px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffff;
}
.form-forgetpass {
  width: 70%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.form-forgetpass p {
  font-family: "Open Sans";
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;

  color: #000000;
}
.form-forgetpass .forget {
  font-family: "Open Sans";
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;

  color: #4d4d4d;
}

.login_imagebackground p {
  font-family: "Open Sans";
  font-style: normal;
  font-weight: 600;
  font-size: 40px;
  line-height: 54px;

  color: #ffffff;
  text-align: center;
}
/* .login-form .login-input-box i {
    position: absolute;
    padding: 10px;
    min-width: 40px;
} */

.google-sign-in-btn {
  border: 1px solid silver;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300px;
  font-size: 17px;
  font-weight: 600;
  height: 50px;
  cursor: pointer;
  box-shadow: 0px 4px 26px rgba(0, 0, 0, 0.25);
  border-radius: 32px;
  margin-top: 1rem;
}
