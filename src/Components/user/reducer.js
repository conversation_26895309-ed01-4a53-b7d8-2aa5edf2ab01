import { createSlice } from '@reduxjs/toolkit'

export const userSlice = createSlice({
    name: 'user',
    initialState: {
        userToken: null,
        userData: null,
        userLanguage : 'en',
        firebaseUid : null,
        appGroups: []
    },
    reducers: {
        setUserToken: (state, action) => {
            state.userToken = action.payload
        },
        setUserData: (state, action) => {
            state.userData = action.payload
        },
        setUserLang : (state,action)=>{
            state.userLanguage = action.payload
        },
        setFirebaseUid : (state,action)=>{
            state.firebaseUid = action.payload.firebaseUid
        },
        setUserAppGroups : (state,action)=>{
            state.appGroups = action.payload.appGroups
        }
    }
})

export const { setUserToken, setUserData, setUserLang, setFirebaseUid, setUserAppGroups } = userSlice.actions

export default userSlice.reducer
