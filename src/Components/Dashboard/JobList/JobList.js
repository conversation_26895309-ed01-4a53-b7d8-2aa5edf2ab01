import axios from "axios";
import moment from "moment";
import { useCallback, useEffect, useState } from "react";
import { Table } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { BASE_URL, instance, tokenFailureHandler } from "../../../Services/api.service";
import ToastersService from "../../../Services/toasters.service";
import "./JobList.css";

const {CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, IT_ADMIN, withAuthorization} = require('../../user/authorize')

const JobList = () => {
  const navigate = useNavigate();

  const [translationJobStatus, setTranslationJobStatus] = useState();
  const [careCalendarJobStatus, setCareCalendarJobStatus] = useState();

  const getJobStatus = useCallback(async (jobType, action) => {
    try {
      // const response = await axios.get(
      //   BASE_URL + `/jobs?job_type=${jobType}&action=${action}`,
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "Content-Type": "application/json",
      //       "X-App-Id": "123456789"
      //     },
      //   }
      // );
      const response = await instance({
        url: `/jobs?job_type=${jobType}&action=${action}`,
        method: "GET",
        // data: JSON.stringify(reqBody),
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log(response);
      if (response.status === 200) {
        if (response.data.result === false) {
          tokenFailureHandler(response.data);
          // navigate("/login");
        }

        if (response.data[0].job_tracker_job_type === "translate") {
          console.log("Transalation Job status", response.data[0]);
          setTranslationJobStatus(response.data[0]);
        }
        if (response.data[0].job_tracker_job_type === "care-calendar") {
          console.log("Care calendar Job status", response.data[0]);
          setCareCalendarJobStatus(response.data[0]);
        }
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem('accessToken');
        localStorage.removeItem('resetToken');
        ToastersService.failureToast('Session Expired!');
        navigate('/login');
        window.location.reload();
      }
    }
  }, []);

  useEffect(() => {
    getJobStatus("translate", "status");
    getJobStatus("care-calendar", "status");
  }, []);

  //   const startJobHandler = (jobtype, action) => {
  //     console.log(jobtype, action);
  //   }

  const startJobHandler = useCallback(async (jobType, action) => {
    try {
      // const response = await axios.get(
      //   BASE_URL + `/jobs?job_type=${jobType}&action=${action}`,
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "Content-Type": "application/json",
      //       "X-App-Id": "123456789"
      //     },
      //   }
      // );
      const response = await instance({
        url: `/jobs?job_type=${jobType}&action=${action}`,
        method: "GET",
        // data: JSON.stringify(reqBody),
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log(response);
      if (response.status === 200) {
        if (response.data.result === false) {
          tokenFailureHandler(response.data);
          navigate("/login");
        }

        if (response.data.result === true) {
            ToastersService.successToast(response.data.message);
        }

        // if (response.data[0].job_tracker_job_type === "translate") {
        //   console.log("Transalation Job status", response.data[0]);
        //   setTranslationJobStatus(response.data[0]);
        // }
        // if (response.data[0].job_tracker_job_type === "care-calendar") {
        //   console.log("Care calendar Job status", response.data[0]);
        //   setCareCalendarJobStatus(response.data[0]);
        // }
        getJobStatus("translate", "status");
        getJobStatus("care-calendar", "status");
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem('accessToken');
        localStorage.removeItem('resetToken');
        ToastersService.failureToast('Session Expired!');
        navigate('/login');
        window.location.reload();
      }
    }
  }, []);

  return (
    <>
      <div className="my-3">
        <div>
          <p
            className="m-0"
            style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
          >
            Job List
          </p>
          <p
            className="m-0"
            style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
          >
            Jobs
          </p>
        </div>
      </div>

      <div className="farmers__farmers-table">
        <Table striped style={{ fontSize: "14px", textAlign: "center" }}>
          <thead>
            <tr style={{ backgroundColor: "#212121", color: "#fff" }}>
              <th style={{ padding: "1rem" }}>Job Name</th>
              <th style={{ padding: "1rem" }}>Status</th>
              <th style={{ padding: "1rem" }}>Action</th>
              <th style={{ padding: "1rem" }}>Result</th>
              <th style={{ padding: "1rem" }}>Last updated at</th>
            </tr>
          </thead>
          <tbody>
            {/* {translationJobStatus && (
              <tr>
                <td
                  style={{
                    padding: "1rem",
                    textTransform: "capitalize",
                    fontWeight: "600",
                  }}
                >
                  {translationJobStatus.job_tracker_job_type}
                </td>
                <td style={{ padding: "1rem" }}>
                  {translationJobStatus.job_tracker_job_status === "COMPLETED"
                    ? "Stopped"
                    : "Running"}
                </td>
                <td style={{ padding: "1rem" }}>
                  <button
                    className="btn btn-sm"
                    style={{ backgroundColor: "#EC6237", color: "#fff" }}
                    disabled={translationJobStatus.job_tracker_job_status !== "COMPLETED"}
                    onClick={() =>
                      startJobHandler(
                        translationJobStatus.job_tracker_job_type,
                        "start"
                      )
                    }
                  >
                    Start
                  </button>
                </td>
                <td style={{ padding: "1rem" }}>
                  {translationJobStatus.job_tracker_job_status}
                </td>
                <td style={{ padding: "1rem" }}>
                  {moment(translationJobStatus.job_tracker_updated_at).format(
                    "DD/MM/YYYY, hh:mm A"
                  )}
                </td>
              </tr>
            )} */}
            {careCalendarJobStatus && (
              <tr>
                <td
                  style={{
                    padding: "1rem",
                    textTransform: "capitalize",
                    fontWeight: "600",
                  }}
                >
                  {careCalendarJobStatus.job_tracker_job_type}
                </td>
                <td style={{ padding: "1rem" }}>
                  {careCalendarJobStatus.job_tracker_job_status === "COMPLETED"
                    ? "Stopped"
                    : "Running"}
                </td>
                <td style={{ padding: "1rem" }}>
                  <button
                    className="btn btn-sm"
                    style={{ backgroundColor: "#EC6237", color: "#fff" }}
                    disabled={careCalendarJobStatus.job_tracker_job_status !== "COMPLETED"}
                    onClick={() =>
                      startJobHandler(
                        careCalendarJobStatus.job_tracker_job_type,
                        "start"
                      )
                    }
                  >
                    Start
                  </button>
                </td>
                <td style={{ padding: "1rem" }}>
                  {careCalendarJobStatus.job_tracker_job_status}
                </td>
                <td style={{ padding: "1rem" }}>
                  {moment(careCalendarJobStatus.job_tracker_updated_at).format(
                    "DD/MM/YYYY, hh:mm A"
                  )}
                </td>
              </tr>
            )}
          </tbody>
        </Table>
      </div>
    </>
  );
};

// export default JobList;
export default withAuthorization(JobList, [CS_TEAM, IT_ADMIN])