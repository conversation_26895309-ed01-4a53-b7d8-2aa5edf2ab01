import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { BASE_URL, instance } from "../../../../Services/api.service";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { clearUserToken } from "../../../user/action";

const Invoice = () => {

    const location = useLocation()
    const navigate = useNavigate()
    const dispatch = useDispatch();
    const userToken = useSelector((state) => state.user.userToken);


    const [requisitonId, setRequisitonId] = useState(true)
    const [invoiceData, setInvoiceData] = useState([]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                // Make a request to your API to get the invoice data
                const response = await getDownloadInvoice()
                setInvoiceData(response.data);
            } catch (error) {
                console.error('Error fetching invoice data:', error);
            }
        };

        fetchData();
    }, []);

    const getDownloadInvoice = async (requisiton_id) => {
        let reqBody = {};

        try {
            const response = await instance({
                // url: `/v3/downloadinvoice?id=${requisiton_id}`,
                url: `/v3/downloadinvoice?id=85c2315a-9e7f-11ee-a4a7-5b5e30d72687`,
                method: "GET",
                data: reqBody,
                responseType: 'arraybuffer',
                headers: {
                    "Content-Type": "application/json",
                    token: userToken.accessToken,
                },
            });
            if (response.status === 200) {
                console.log("download invoice is completed");
                return response
            }
        } catch (error) {
            console.log(error);
            if (error.response.status === 401) {
                dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
            }
        }
    };

    const handleInvoice = async () => {
        console.log("Invocie to be generated")
        try {


            // Trigger the download
            const file_name = `invloce-${requisitonId}.docx`
            downloadFile(invoiceData, file_name); // Replace 'invoice.pdf' with the desired file name
        } catch (error) {
            console.error('Error downloading invoice:', error);
        }
    }

    const downloadFile = (data, fileName) => {
        console.log("download file function is called")
        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }); // Assuming the data is in PDF format
        const url = window.URL.createObjectURL(blob);
        console.log("url from the blob", url)

        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    };


    return (
        <div>
            download
        </div>
        // <div>
        //     <h2>Invoice Table</h2>
        //     <table border="1">
        //         <thead>
        //             <tr>
        //                 <th>Column 1</th>
        //                 <th>Column 2</th>
        //                 {/* Add more columns as needed */}
        //             </tr>
        //         </thead>
        //         <tbody>
        //             {invoiceData.map((invoice, index) => (
        //                 <tr key={index}>
        //                     <td>{invoice.field1}</td>
        //                     <td>{invoice.field2}</td>
        //                     {/* Add more cells based on your data structure */}
        //                 </tr>
        //             ))}
        //         </tbody>
        //     </table>
        // </div>
    );
};

export default Invoice;
