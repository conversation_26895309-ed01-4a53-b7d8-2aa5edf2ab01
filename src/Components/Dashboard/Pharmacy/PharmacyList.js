import { useNavigate } from "react-router-dom";
// import "./Farmers.css";
import { useSelector, useDispatch } from "react-redux";
import { useState, useCallback, useEffect } from "react";
import { get_pharmacy_requistion } from "../../../router";
import { clearUserToken } from "../../user/action";
import { OCTable } from "../../Common/AntTableHelper";
import { extractBasedOnLanguage } from "@krushal-it/common-core";
import { extractBasedOnLanguageMod } from "../../../Utilities/Common/utils";

import React from 'react';
import { useMediaQuery } from 'react-responsive';
// const {get_requisition_list,get_all_requisition} = require('../../../router')
import Loader from "../../Loader/Loader";
import Moment from "moment";
import ToastersService from "../../../Services/toasters.service";

const PharmacyList = () => {
    const isMobile = useMediaQuery({ maxWidth: 768 });
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const userLanguage = useSelector((state) => state.user.userLanguage);
    const [data, setData] = useState([])
    const [load, setLoad] = useState(true)
    // const [searchParams] = useSearchParams()
    // const pageParams = createPageParamsFromURLString(farmersTableConfiguration, searchParams)
    const userToken = useSelector((state) => state.user.userToken);
    const [reportData, setReportData] = useState([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState([])
    const [searchPin, setSearchPin] = useState("")

    const handlerequisitiondetailclick = (prop) => {
        console.log("clicked data")
        navigate(`/pharmacylist/pharmacyrequisitiondetails?req_user_id=${prop.requisitioner_uuid}&req_id=${prop.requisition_id}`);
        // { state: { myProp: prop } }
        console.log("clicked data", prop)
    }

    const pharmacyTableConfiguration = {
        enableSelection: false,
        // defaultPageSize: 10,
        enableHorizontalScrolling: true,
        showTableHeader: false,
        omniSearchRowEnabled: false,
        enableColumnConfiguration: false,
        enableOnChange: true,
        // getAllDataAtOnce: true,
        // disablePagination: true,
        dataTransformationRules: {
            // staff_filter_values: 'bdm_filter_values',
            // staff_name: 'bdm'
        },
        rowKey: 'requisition_id',
        columns: [
            {
                title: "Requisition Id",
                key: "requisition_id",
                render: (_, record) => {
                    return (
                        <div style={{cursor:"pointer",color:"blue"}} onClick={() => handlerequisitiondetailclick(record)}>{extractBasedOnLanguageMod(record?.requisition_id, userLanguage)}</div>
                    )
                }

            },
            {
                title: "Requisitioner Name",
                key: "requisitioner_name",
                render: (_, record) => {
                    return (
                        <div style={{cursor:"pointer",color:"blue"}} onClick={() => handlerequisitiondetailclick(record)}>{record?.requisitioner_name}</div>
                    )
                }
            },
            {
                key: "requisitioner_type_l10n",
                title: "Requisitioner Type",
                render: (_, record) => {
                    return (
                        <div>{extractBasedOnLanguageMod(record?.requisitioner_type_l10n, userLanguage)}</div>
                    )
                }

            },
            {
                title: "Fulfiller Name",
                key: "fullfiller_name_l10n",
                render: (_, record) => {
                    return (
                        <div>{extractBasedOnLanguageMod(record?.fullfiller_name_l10n, userLanguage)}</div>
                    )
                }
            },
            {
                title: "Fulfiller Type",
                key: "fullfiller_type_l10n",
                render: (_, record) => {
                    return (
                        <div>{extractBasedOnLanguageMod(record?.fullfiller_type_l10n, userLanguage)}</div>
                    )
                }
            },
            {
                title: "Approver Name",
                key: "approver_name",
                render: (_, record) => {
                    return (
                        <div>{record?.approver_name}</div>
                    )
                }
            },
            {
                title: "Approver Type",
                key: "approver_type_l10n",
                render: (_, record) => {
                    return (
                        <div>{extractBasedOnLanguageMod(record?.approver_type_l10n, userLanguage)}</div>
                    )
                }
            },
            {
                title: "Status",
                key: "status_l10n",
                render: (_, record) => {
                    return (
                        <div>{extractBasedOnLanguageMod(record?.status_l10n, userLanguage)}</div>
                    )
                }
            },
            {
                title: "created_at",
                key: "created_at",
            },
            {
                title: "updated_at",
                key: "updated_at",
            }
        ],
        columnConfiguration: {
            requisition_id: {
                headingText: 'Requisition Id',
                type: 'string'
            },
            requisitioner_name: {
                headingText: 'Requisitioner Name',
                enableFilter: true,
                type: 'string',
                includeInOmnisearch: true
            },
            requisitioner_type_l10n: {
                headingText: 'Requisitioner Type',
                type: 'default_array',
                dataType: 'int',
                enableFilter: true,
                filters: [
                    { text: 'Vet', value: "1000230003", },
                    { text: 'Paravet', value: "1000230004" },
                ],
                includeInOmnisearch: true,
                filterValuesKey: 'staff_filter_values'
            },
            fullfiller_name_l10n: {
                headingText: 'Fulfiller Name',
                type: 'string',
            },
            fullfiller_type_l10n: {
                headingText: 'fulfiller_type Type',
                type: 'string',
            },
            approver_name: {
                headingText: 'Approver Name',
                type: 'string',
                enableFilter: true,
                includeInOmnisearch: true
            },
            approver_type_l10n: {
                headingText: 'Approver Type',
                type: 'default_array',
                dataType: 'int',
                // enableFilter: true,
                // filters: [
                //     { text: 'Vet', value: 1000230003, },
                //     { text: 'Paravet', value: 1000230004 },
                // ],
                // includeInOmnisearch: true,
            },
            status_l10n: {
                headingText: 'Requisitioner Type',
                type: 'default_array',
                dataType: 'int',
                enableFilter: true,
                filters: [
                    { text: 'With Pharmacist Approved By Vet', value: 1000820003, },
                    { text: 'Delivered by Pharmacist', value: 1000830001 },
                    { text: 'Received by Paravet / Vet', value: 1000830002, },
                ],
                includeInOmnisearch: true,
            },
            created_at: {
                headingText: 'Created At',
                enableFilter: true,
                type: 'dayjs_range',
                formatType: 'date',
                format: 'h:mm a, DD-MMM-YYYY',
                // enableSort: true,
                // sortColumnType: 'date',
                // defaultSort: 'descend'
            },
            updated_at: {
                headingText: 'Updated At',
                enableFilter: true,
                type: 'dayjs_range',
                formatType: 'date',
                format: 'h:mm a, DD-MMM-YYYY',
                // enableSort: true,
                // sortColumnType: 'date',
                // defaultSort: 'descend'
            },

        }
    }

    const loadFilters = useCallback(async (userToken) => {
        // try {
        //     const headers = {
        //         useCase: 'Get Farmers With Location Recorded Report Filter Data',
        //         token: userToken.accessToken, //authToken
        //     }
        //     const response = await getFarmersFilterData(headers)
        //     return response.data
        // } catch (error) {
        //     if (error.response.status === 401) {
        //         dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
        //     }
        // }
        return []
    }, [dispatch, navigate])


    const loadReport = useCallback(async (userToken, queryParams) => {
        console.log("called load")
        try {
            const headers = {
                token: userToken.accessToken, //authToken
            };
            const response = await get_pharmacy_requistion(headers, queryParams, 'open');
            
            return response.data
        } catch (error) {
            if (error?.response?.status === 401) {
                dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
            }
        }
    }, [dispatch, navigate]);


    const loadInitialFilterDataCB = (additionalParams) => {
        console.log("load filter is called  ")
        return loadFilters(userToken, additionalParams)
    }


    const loadReportCB = (reportQueryParams, additionalParams) => {
        console.log("loadReportCB is called ", reportQueryParams)
        return loadReport(userToken, reportQueryParams, additionalParams)
    }

    useEffect(() => {

        setSearchPin("")
        // onload()
    }, [])

    const filterList = () => {
        if (searchPin) {
            return data.filter((listItem) => {
                return listItem.aname.toLowerCase().includes(searchPin.toLowerCase()) || listItem.requisitioner_name_l10n.toLowerCase().includes(searchPin.toLowerCase())
            })
        } else {
            return data
        }
    }

    const onload = async () => {

        try {
            console.log("on load called")
            // const response = await get_all_requisition(userToken);
            const response = await get_pharmacy_requistion(userToken, "", 'open');
            console.log("on load called", response)

            const array_list = []
            response.data['report'].map(item => {
                if ((item.status_id == 1000820003 || item.status_id == 1000830001 || item.status_id == 1000830002) && item.approver_uuid != null) {
                    array_list.push(item)
                }
            })
            array_list.map(item => {
                Object.keys(item).forEach(key => {
                    if (item[key] && typeof item[key] === 'object') {
                        item[key] = extractBasedOnLanguage(item[key], userLanguage);
                        item['aname'] = Moment(item.created_at).format('YYYYMMDD') + '/' + Moment(item.created_at).format('HH:mm')
                    }
                })
            })
            console.log("array_list", array_list)
            setData(array_list)
            setLoad(false)
        } catch (error) {
            if (error?.response?.status === 401) {
                dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
            }
            setLoad(false)
        }
    }

    return (
        <div>
            {!isMobile && (<div>
                <div className="my-3" style={{ display: "flex", alignItems: "center" }}>
                    <div>
                        <p
                            className="m-0"
                            style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
                        >
                            List of Open Orders
                        </p>
                    </div>

                </div>

                {/* farmer table  */}
                <OCTable
                    /* tableKey={tableKey}
                    setTableKey={setTableKey} */
                    reportData={reportData}
                    setReportData={setReportData}
                    tableConfiguration={pharmacyTableConfiguration}
                    selectedRowKeys={selectedRowKeys}
                    setSelectedRowKeys={setSelectedRowKeys}
                    // postInitialLoadCallback={postInitialLoadCB}
                    loadReportCallback={loadReportCB}
                    loadInitialFilterDataCallback={loadInitialFilterDataCB}
                // parentPageParams={pageParams}
                // showTableHeader={true}
                // omniSearchRowEnabled={true}
                // omniSearchRowBeginning={backToQueueListLinkUI}
                // omniSearchRowMiddle={viewRelatedAnimalButtons}
                />
            </div>)}

            {(isMobile && !load) && (<div style={{ background: 'white', paddingBottom: '20px' }}>
                <div className="my-3" style={{ display: "flex", alignItems: "center", paddingTop: 20, paddingLeft: 20 }}>
                    <div>
                        <p
                            className="m-0"
                            style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
                        >
                            List of Open Orders
                        </p>
                    </div>

                </div>
                <div className="d-flex" style={{ background: 'white', paddingTop: 10, justifyContent: 'center', alignItems: 'center' }}>
                    <input
                        type="text"
                        className="input-field"
                        style={{ width: "80%", borderRadius: 50, background: '#F6F3FB', height: '50px' }}
                        placeholder="Search for Requisition..."
                        onChange={(e) => { setSearchPin(e.target.value) }}

                    />
                    <img src={require('./search.png')} style={{}} />
                </div>
                {filterList().map((item, index) => {
                    // console.log('DDM KOCF sV, data = ', data)
                    return (
                        <div key={index} onClick={() => handlerequisitiondetailclick(item)} style={{ cursor: 'pointer', border: '1px solid #D9D9D9', borderRadius: 15, marginTop: 15, padding: 15, backgroundColor: 'white', marginRight: '20px', marginLeft: '20px' }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <div style={{ width: '50%' }}>
                                    <p style={{ color: 'black', fontWeight: 'bold', fontSize: 14, margin: '0px' }}>{item.aname}</p>
                                    <p style={{ color: 'black', fontWeight: 'bold', fontSize: 14, margin: '5px 0px 20px 0px' }}>{item.requisitioner_name_l10n}</p>
                                </div>
                                <div style={{ display: 'flex', alignItems: 'flex-end', justifyContent: 'flex-end', width: '50%' }}>
                                    <p style={{ textAlign: 'center', color: item.status_id == 1000820001 ? '#EC6237' : item.status_id == 1000830003 ? 'red' : item.status_id == 1000820005 ? 'red' : item.status_id == 1000820004 ? 'red' : item.status_id == 1000820003 ? '#029184' : item.status_id == 1000820002 ? 'orange' : item.status_id == 1000830002 ? 'green' : item.status_id == 1000830001 ? 'green' : 'black', fontWeight: 'bold', fontSize: 13, position: 'relative', margin: '-20px 0px 0px 50px' }}>{item.status_l10n}</p>
                                </div>
                            </div>
                            <hr style={{ marginTop: '-5px' }} />

                            <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                                <div style={{ alignSelf: 'flex-end' }}>
                                    <div style={{ display: 'flex', marginTop: '5px' }}>
                                        <div>
                                            <p style={{ color: 'black', fontSize: 13, marginLeft: 5, fontWeight: 'bold', margin: '0px' }}>Vet : </p>
                                        </div>
                                        <div>
                                            {item.requisitioner_type_l10n.toLowerCase() == 'vet' && <p style={{ color: 'black', fontSize: 13, marginLeft: 5, fontWeight: '500', margin: '0px' }}>{item.requisitioner_name_l10n}</p>}
                                            {item.requisitioner_type_l10n.toLowerCase() == 'paravet' && <p style={{ color: 'black', fontSize: 13, marginLeft: 5, fontWeight: '500', margin: '0px' }}>{item.approver_name_l10n}</p>}
                                        </div>
                                    </div>
                                    <div style={{ display: 'flex', marginTop: '5px' }}>
                                        <div>
                                            <p style={{ color: 'black', fontSize: 13, marginLeft: 5, fontWeight: 'bold', margin: '0px' }}>Paravet : </p>
                                        </div>
                                        <div>
                                            <p style={{ color: 'black', fontSize: 13, marginLeft: 5, fontWeight: '500', margin: '0px' }}>{item.requisitioner_name_l10n}</p>
                                        </div>
                                    </div>
                                    <div style={{ display: 'flex', marginTop: '5px' }}>
                                        <div>
                                            <p style={{ color: 'black', fontSize: 13, marginLeft: 5, fontWeight: 'bold', margin: '0px' }}>Date : </p>
                                        </div>
                                        <div>
                                            <p style={{ color: 'black', fontSize: 13, marginLeft: 5, fontWeight: '500', margin: '0px' }}>{item.created_at.split("T")[0]}</p>
                                        </div>
                                    </div>
                                </div>
                                <div style={{ alignSelf: 'flex-end' }}>
                                    <img src={require('./right.png')} style={{ position: 'relative', top: '-5px' }} />
                                </div>
                            </div>

                        </div>
                    )
                })}
                {filterList().length == 0 && (
                    <div style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center', paddingTop: '300px' }}>
                        <p>No Requisitions Found!!</p>
                    </div>
                )}

            </div>)}
            {(isMobile && load) && (<div style={{ background: 'white', height: '90vh', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <Loader />
            </div>)}

        </div>
    );
};

export default PharmacyList;
