import React from "react";
import "./PharmacyRequisitionDetails.css";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { BASE_URL, instance } from "../../../../Services/api.service";
import { clearUserToken } from "../../../user/action";
import {
  createPageParamsFromURLString,
  OCTable,
} from "../../../Common/AntTableHelper";
import { useState, useCallback } from "react";
import { Space, Checkbox, Table, Tag, Input } from "antd";
import ReactDOM from "react-dom";
import { QRCodeCanvas } from "qrcode.react";
import { Container, Modal, Row, Col } from "react-bootstrap";
import CloseIcon from "@mui/icons-material/Close";
import SearchIcon from "@mui/icons-material/Search";
import { useMediaQuery } from "react-responsive";
import Moment from "moment";
import { extractBasedOnLanguage } from "@krushal-it/common-core";
import Im<PERSON><PERSON>iewer from "react-images-viewer";
import ImageViewer from "react-simple-image-viewer";
import ToastersService from "../../../../Services/toasters.service";
import { LocalActivity } from "@mui/icons-material";
import { Button } from "antd";
import { extractBasedOnLanguageMod, formatedDate } from "../../../../Utilities/Common/utils";

const PharmacyRequisitionDetails = (props) => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userToken = useSelector((state) => state.user.userToken);
  const isMobile = useMediaQuery({ maxWidth: 768 });
  const userLanguage = useSelector((state) => state.user.userLanguage);

  const [requisitonId, setRequisitonId] = useState(true);

  const [details, setDetals] = useState({});
  const [inventoryKit, setInventoryKit] = useState("");
  const [load, setLoad] = useState(true);
  const [deliveryChekcer, setDeliveryChekcer] = useState(false);
  const [data, setData] = useState([]);

  const [invoiceModal, setInvoiceModal] = useState(false);
  const [showQr, setShowQr] = useState(false);
  const [showInvoice, setShowInvoice] = useState(false);
  const [showUploadFile, setShowUploadFile] = useState(false);
  const [files, setFiles] = useState(null);

  const [qrdata, setqrdata] = useState("");

  const [formData, setFormData] = useState([]);

  const [invoiceInput, setInvoiceInput] = useState([]);
  const [failedUpdate, setfailedUpdate] = useState(false);
  const [fileUploaded, setfileUploaded] = useState("");
  const [fileCount, setFileCount] = useState(0);
  const [pharmacyPath, setPharmacyPath] = useState("");

  const [reloadPage, setReloadPage] = useState();
  const [uploadButtonLoader, setUploadButtonLoader] = useState(false)

  useEffect(() => {
    // setDeliveryChekcer(false)
    // setShowQr(false)
    // setShowInvoice(false)
    // setShowUploadFile(false)
    // setload(true)
    // console.log("location detals", location)
    // if (location.pathname.includes('pharmacylist')) {
    //     setPharmacyPath('/pharmacylist')
    // }
    // else {
    //     setPharmacyPath('/pharmacyclosedlist')
    // }
    // const queryParams = new URLSearchParams(location.search);

    // const req_user_id = queryParams.get('req_user_id');
    // const req_id = queryParams.get('req_id');
    // setRequisitonId(req_id)
    // setqrdata(req_id + "|" + req_user_id)
    // console.log(req_id + "|" + req_user_id)
    // getInventoryKit(req_user_id)
    // getRequisitoinDetails({ "filters": { "requisition_id": req_id } })
    // setload(false)
    onload2();
  }, [fileUploaded, reloadPage]);

  const onload2 = () => {
    setDeliveryChekcer(false);
    setShowQr(false);
    setShowInvoice(false);
    setShowUploadFile(false);
    console.log(
      "location detals",
      location,
      location.pathname.includes("pharmacylist")
    );
    if (location.pathname.includes("pharmacylist")) {
      setPharmacyPath("/pharmacylist");
    } else {
      setPharmacyPath("/pharmacyclosedlist");
    }

    setload(true);
    const queryParams = new URLSearchParams(location.search);
    const req_user_id = queryParams.get("req_user_id");
    const req_id = queryParams.get("req_id");
    setRequisitonId(req_id);
    setqrdata(req_id + "|" + req_user_id);
    console.log(req_id + "|" + req_user_id);
    getInventoryKit(req_user_id);
    getRequisitoinDetails(req_id);
  };

  const getDownloadInvoice = async (requisiton_id) => {
    let reqBody = {};

    try {
      const response = await instance({
        // url: `/v3/downloadinvoice?id=${requisiton_id}`,
        url: `/v3/downloadinvoice?id=85c2315a-9e7f-11ee-a4a7-5b5e30d72687`,
        method: "GET",
        data: reqBody,
        responseType: "arraybuffer",
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status === 200) {
        console.log("download invoice is completed");
        return response;
      }
    } catch (error) {
      console.log(error);
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const getInventoryKit = async (req_user_id) => {
    let reqBody = {};

    try {
      const response = await instance({
        url: `/v3/inventorykit?id=${req_user_id}`,
        method: "GET",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status === 200) {
        console.log("invetory kit", response.data.data);
        response.data.data.map((item) => {
          item["t_qty"] = item.balance;
        });
        setInventoryKit(response.data.data);
      }
    } catch (error) {
      console.log(error);
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const getRequisitoinDetails = async (requisitionId) => {
    try {
      const response = await instance({
        url: `inventory/requests/request?requisition_id=${requisitionId}`,
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status === 200 || response.status === 201) {
        // console.log("requisition details======", response.data.data[0]);
        const array_list = { ...response.data};
        console.log(array_list)
        // Object.keys(array_list).forEach((key) => {
        //   if (array_list[key] && typeof array_list[key] === "object") {
        //     array_list[key] = extractBasedOnLanguage(
        //       array_list[key],
        //       userLanguage
        //     );
        //     array_list["aname"] =
        //       Moment(array_list.created_at).format("YYYYMMDD") +
        //       "/" +
        //       Moment(array_list.created_at).format("HH:mm");
        //   }
        // });
        console.log(array_list)
        // if (response.data.data[0].status_id == 1000820003) {
        //     console.log("deliver status check")
        //     setDeliveryChekcer(true)
        // }
        // else {
        //     setDeliveryChekcer(true)
        // }
        console.log("requisition details data", response.data);
        // response.data.data[0]["created_format"] =
        //   Moment(array_list.created_at).format("YYYY-MM-DD") +
        //   "/" +
        //   Moment(array_list.created_at).format("HH:mm");
        setData(array_list);
        setDetals(response.data);
        setRequisitionMedList(response.data);

        setFileCount(response?.data?.requisitiondocs?.length);

        // console.log("file count", response.data.data[0].files.length);
        setload(false);
      }
    } catch (error) {
      console.log(error);
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const setRequisitionMedList = (reqData) => {
    let arr = [];
    // console.log("setRequisitionMedList called", reqData.requisition_line_items)
    reqData.requisition_line_items?.map((json_data) => {
      arr.push({
        id: json_data.medicine_id,
        medicine: json_data?.medicine_name_l10n?.ul,
        unit: json_data?.unit || "-",
        pack: json_data?.medicine_pack_size,
        quantity: +json_data.quantity / +json_data.medicine_pack_size,
        total_size: json_data.quantity,
        requisition_line_items_id: json_data.requisition_line_items_id,
      });
    });

    // console.log(" setting requistion data", arr)
    if (reqData.status_id === 1000830001) {
      console.log("showing qr code for the status ", reqData.status_id);
      // setDeliveryChekcer(false)
      setShowQr(true);
    } else if (reqData.status_id === 1000830002) {
      console.log("dont shoe qr code for the status ", reqData.status_id);
      // setDeliveryChekcer(false)
      setShowInvoice(true);
    } else if (reqData.status_id === 1000840002) {
      console.log("dont shoe qr code for the status ", reqData.status_id);
      // setDeliveryChekcer(false)
      setShowUploadFile(true);
    } else if (reqData.status_id === 1000820003) {
      console.log("dont shoe qr code for the status ", reqData.status_id);
      setDeliveryChekcer(true);
      // setShowUploadFile(true)
    } else {
      console.log("dont shoe qr code for the status ", reqData.status_id);
      // setDeliveryChekcer(false)
      // setShowQr(false)
      // setShowInvoice(false)
      // setShowUploadFile(false)
    }

    console.log("this is the unit ", arr);
    setFormData(arr);
    setLoad(false);
    // console.log("form data is set check ?", formData)
  };

  const UpdateRequisition = async (params) => {
    // console.log("form data 1 ", formData)

    let reqBody = {};
    if (params !== {}) {
      reqBody = { ...reqBody, ...params };
    }
    try {
      const response = await instance({
        url: "v3/requisitions",
        method: "PUT",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status === 200 || response.status === 201) {
        console.log("Updated requisition ", response);
        // ToastersService.successToast("Updated requisition")
      } else {
        ToastersService.failureToast("Failed to update requisition");
      }
      return response;
    } catch (error) {
      console.log(error);
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const [loadpg, setload] = useState(true);

  const UploadInvoiceAttachment = async (formDataFile) => {
    // console.log("form data 1 ", formData)

    // let reqBody = {};
    // if (params !== {}) {
    //     reqBody = { ...reqBody, ...params };
    // }
    try {
      const response = await instance({
        url: `v3/uploadinvoiceattachment?id=${requisitonId}`,
        method: "POST",
        data: formDataFile,
        headers: {
          token: userToken.accessToken,
        },
      });
      if (response.status === 200 || response.status === 201) {
        console.log("file uploaded successfully ", response);
        // console.log("form data 2 ", formData)
      }
      return response;
    } catch (error) {
      console.log(error);
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const handleStatusUpdate = async (status) => {
    console.log("status selecttion", details);
    let requisition_id = details.requisition_id;
    let requisitioner_type_id = details.requisitioner_type_id;
    let requisitioner_uuid = details.requisitioner_uuid;
    let details_status = details.status_id;
    // console.log("this is formdata", formData)
    if (formData.length > 0) {
      console.log("save the changes", formData);

      let newArry = [];

      formData.map(function (item, index) {
        // console.log("item ", item)
        newArry.push({
          medicine_id: item.id,
          unit: item.unit,
          quantity: item.total_size,
          requisition_line_items_id: item.requisition_line_items_id,
          requisition_id: requisition_id,
        });
      });

      // console.log("updated body", newArry)

      const reqBody = {
        requisition_id: requisition_id,
        requisitioner_type_id: requisitioner_type_id,
        requisitioner_uuid: requisitioner_uuid,
        fullfiller_type_id: details.fullfiller_type_id,
        fullfiller_uuid: details.fullfiller_uuid,
        approver_type_id: details.approver_type_id,
        approver_uuid: details.approver_uuid,
        requisition_line_items: newArry,
        // "status_id": details_status/,
        status_id:
          status == "reject"
            ? 1000820005
            : status == "deliver"
              ? 1000830001
              : status == "invoice"
                ? 1000840002
                : "",
      };
      if (reqBody.status_id == 1000840002) {
        reqBody["pharmacy_invoice_number"] = invoiceInput;
      }

      console.log("post body", reqBody);
      setload(true);

      let response = await UpdateRequisition(reqBody);
      console.log("updated response data", response);

      if (response && response.status) {
        if (response.status === 200 || response.status === 201) {
          // navigate("/pharmacyclosedlist")
          if (status == "invoice") {
            setShowInvoice(false);
            onload2();
            ToastersService.successToast("Invoice created Successfully");
          } else {
            onload2();
            ToastersService.successToast("Success");
          }
          // setReloadPage(true)
        }
        // setload(false)
      } else if (response?.status === 500) {
        ToastersService.failureToast("Someting went wrong")
      } else {
        console.log("REQ ERROR ", response)
        setfailedUpdate(true);
        // setload(false)
        ToastersService.failureToast("Failed to update");
        console.log("Failed to updated");
      }
    } else {
      alert("no data to be added");
    }
  };

  const [modalVisible, setModalVisible] = useState(false);
  const [modalStaffVisible, setModalStaffVisible] = useState(false);

  const openModal = () => {
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
    setfailedUpdate(false);
    setInvoiceModal(false);
  };
  const handleInvoice = async () => {
    setInvoiceModal(true);
    // navigate(`invoice?req_id=${requisitonId}`)
  };

  const handleInvoiceInput = (event) => {
    console.log("invoice number", event.target.value);
    setInvoiceInput(event.target.value);
  };

  const handleSubmitInvoice = () => {
    if (invoiceInput.length > 3) {
      setload(true);
      handleStatusUpdate("invoice");
      setInvoiceModal(false);
      onload2();
    } else {
      ToastersService.failureToast("Enter a valid invoice number");
    }
  };

  const handleFileChange = (event) => {
    if (fileCount <= 5) {
      let newFileCount = fileCount + event.target.files.length;

      if (newFileCount > 5) {
        console.log("file upload limit reached");
        ToastersService.failureToast(
          "Uploaded Failed, maximum limit reached!(5)"
        );
      } else {
        setFileCount(fileCount + event.target.files.length);
        setFiles(event.target.files);
        console.log("this is file", event.target.files.length);
      }
    } else {
      console.log("max file reached");
    }
  };

  const handleUpload = async () => {
    console.log("upload clicked")
    setUploadButtonLoader(true)
    const formDataFile = new FormData();
    if (!files) {
      setUploadButtonLoader(false)
      ToastersService.failureToast("No file selected");
    } else {
      for (const file of files) {
        formDataFile.append("file", file);
      }
      setFiles(null)
      setUploadButtonLoader(true)
      const uploadResponse = await UploadInvoiceAttachment(formDataFile);

      if (uploadResponse.status === 200 || uploadResponse.status === 201) {
        console.log("file uploaded successfully ", uploadResponse);
        setfileUploaded("uploaded");
        ToastersService.successToast("Uploaded successfully!");
        onload2();
        setUploadButtonLoader(false)
        setFiles(null)
      } else if (uploadResponse.status === 413) {
        ToastersService.successToast("File size is too big!");
      }
      else {
        setUploadButtonLoader(false)
        console.log("error uploading file ");
        ToastersService.failureToast("Upload Failed, maximum limit reached!");
        onload2();
      }
      console.log("uploadResponse", uploadResponse);
    }
  };

  const shouldLoad = formData;


  if (loadpg) {
    return (
      <div className="loadingcircle">
        <div className="loader"></div>
      </div>
    );
  } else if (shouldLoad) {
    return (
      <div style={{ margin: "5px" }}>
        {!isMobile && (
          <div>
            <div>
              <div className="divtag">
                <div>
                  <button
                    className="goback"
                    onClick={() => {
                      console.log("pharmacyPath", pharmacyPath);
                      navigate(pharmacyPath);
                    }}
                  >
                    Go back
                  </button>
                  {/* <button className="goback" onClick={() => navigate("/dashboard/requisitionlist")}>Go back</button> */}
                  <div className="requsitiondetails">
                    Requisitons / Requistion Details
                  </div>
                  {/* <div className="requisitionname">Rahul P</div> */}
                </div>
                {true ? "" : ""}
                <div className="qrsetter">
                  {showQr ? (
                    <span style={{ marginTop: "23px", marginRight: "15px" }}>
                      <img
                        onClick={openModal}
                        src={require("../qr.png")}
                        style={{
                          height: "40px",
                          position: "relative",
                          marginTop: "-13px",
                          cursor: "pointer",
                        }}
                      />
                    </span>
                  ) : (
                    ""
                  )}
                  {showInvoice ? (
                    <button
                      style={{ marginTop: 10 }}
                      className="invoice"
                      onClick={handleInvoice}
                    >
                      <span style={{ width: "100%", marginLeft: -5 }}>
                        Create INVOICE
                      </span>
                    </button>
                  ) : (
                    ""
                  )}
                </div>
              </div>
            </div>

            <Modal
              show={invoiceModal}
              backdrop="static"
              centered
              // style={{ flex: 1, paddingTop: "300px" }}
            >
              <Modal.Title
                style={{
                  padding: "16px 16px 0px 16px",
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                <div>Create Invoice</div>
                <CloseIcon
                  onClick={() => {
                    closeModal();
                  }}
                />
              </Modal.Title>

              <Modal.Body>
                <Container>
                  <div
                    style={{
                      height: "15vh",
                      width: "100%",
                      // marginTop: 40,
                      alignItems: "center",
                      display:"flex",
                      justifyContent:"center",
                      flexDirection:"column"
                    }}
                  >
                    <div
                      style={{
                        textAlign:"center",
                        fontWeight: 400,
                        paddingBottom: 20
                      }}
                    >
                      Enter Invoice number
                    </div>
                    <div style={{display:"flex",justifyContent:"center"}}>
                    {/* <input
                      name="myInput"
                      onChange={handleInvoiceInput}
                      type="text"
                      min="3"
                      max="250"
                      // style={{marginLeft:50}}
                    /> */}
                    <Input type="text" min={3} max={250} onChange={handleInvoiceInput}/>
                    </div>

                    <div
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <Button type="primary" style={{ margin: 10, textAlign: "center" }} onClick={handleSubmitInvoice}>

                        Submit

                      </Button>
                    </div>
                  </div>
                </Container>
              </Modal.Body>
              {/* <Modal.Footer style={{ justifyContent: 'center' }}>
                    </Modal.Footer> */}
            </Modal>

            <div className="tabsinfo">
              <div
                className="notselected"
                style={{ marginLeft: "25px", width: "100px" }}
              >
                Details
              </div>
            </div>
            <div>
              <div className="detailsinfo">
                <div>
                  <div className="header-key">Requisitioner Name:</div>
                  <div className="header-value">
                    {extractBasedOnLanguageMod(
                      details?.requisitioner_name_l10n,
                      "en"
                    )}
                  </div>
                </div>
                <div>
                  <div className="header-key">Requisitioner Type:</div>
                  <div className="header-value">
                    {extractBasedOnLanguageMod(
                      details?.requisitioner_type_l10n,
                      "en"
                    )}
                  </div>
                </div>
                <div>
                  <div className="header-key">Fullfiller Name</div>
                  <div className="header-value">
                    {extractBasedOnLanguageMod(
                      details?.fullfiller_name_l10n,
                      "en"
                    )}
                  </div>
                </div>
              </div>
              <div className="detailsinfo">
                <div>
                  <div className="header-key">Fullfiller Type:</div>
                  <div className="header-value">
                    {/* {details?.fullfiller_type_l10n ? details.fullfiller_type_l10n : "NA"} */}
                    {extractBasedOnLanguageMod(details?.fullfiller_type_l10n,"en") || "NA"}
                  </div>
                </div>
                <div>
                  <div className="header-key">Status:</div>
                  <div className="header-value">{extractBasedOnLanguageMod(details?.status_l10n,"en")}</div>
                </div>
                <div>
                  <div className="header-key">Created Date</div>
                  <div className="header-value">
                    {formatedDate(details?.inserted_at)}
                    {/* {details?.created_at} */}
                  </div>
                </div>
              </div>
              <div className="detailsinfo">
                <div>
                  <div className="header-key">Pharmacy Invoice Number:</div>
                  <div className="header-value">
                    {/* {details?.fullfiller_type_l10n ? details.fullfiller_type_l10n : "NA"} */}
                    {details?.pharmacy_invoice_number || "NA"}
                  </div>
                </div>
                <div>
                  <div className="header-key">Zoho Bill Id</div>
                  <div className="header-value">
                    {details?.zoho_bill_id || " NA"}
                  </div>
                </div>
              </div>
            </div>

            <div className="tabsinfo">
              <div
                className="notselected"
                style={{ marginLeft: "25px", width: "100px" }}
              >
                Medicine
              </div>
            </div>
            <div>
              <div className="formdatatitle">
                <div className="medicineName">Medicine</div>
                <div className="medData">Unit</div>
                <div className="medData">Quantity</div>
                <div className="medData">Total Quantity</div>
              </div>
            </div>
            <div>
              <hr />
            </div>
            {formData.map((item, index) => (
              <div key={index} className="formdata">
                <div className="medicineName">{item.medicine}</div>
                <div className="medData">{item.unit}</div>
                <div className="medData">{item.quantity}</div>
                <div className="medData">{item.total_size}</div>
              </div>
            ))}
            {deliveryChekcer && (
              <div>
                <button
                  className="reject"
                  onClick={() => handleStatusUpdate("reject")}
                  style={{
                    opacity: formData.length > 0 ? 1 : 0.5,
                    textAlign: "center",
                    background: "#B02109",
                    color: "white",
                  }}
                >
                  <span style={{ width: "100%" }}>Reject</span>
                </button>
                <button
                  className="deliver"
                  onClick={() => handleStatusUpdate("deliver")}
                  style={{
                    opacity: formData.length > 0 ? 1 : 0.5,
                    textAlign: "center",
                  }}
                >
                  <span style={{ width: "100%" }}>Deliver</span>
                </button>
              </div>
            )}
          </div>
        )}

        {showUploadFile ? (
          <div>
            {/* <div>
                            <hr />
                        </div> */}
            <div className="tabsinfo" style={{ marginTop: "25px" }}>
              <div
                className="notselected"
                style={{ marginLeft: "25px", width: "300px" }}
              >
                Invoice Documents
              </div>
            </div>
            {fileCount < 5 ? (
              <div>
                <div>
                  <input
                    type="file"
                    accept=".pdf, image/*"
                    multiple
                    onChange={handleFileChange}
                  />{console.log("upload disable", uploadButtonLoader)}
                  <Button type="primary" onClick={handleUpload}>
                    Upload
                  </Button>
                </div>
                <div style={{ fontSize: 12, color: "red" }}>
                  * please select gif, png, jpeg, jpg, bmp or pdf
                </div>
              </div>
            ) : (
              <div>
                Max file reached
                <input
                  type="file"
                  accept=".pdf, image/*"
                  disabled
                  multiple
                  onChange={handleFileChange}
                />
                <Button type="primary" disabled={true} onClick={handleUpload}>
                  Upload
                </Button>
              </div>
            )}
          </div>
        ) : (
          ""
        )}

        <div>
          <hr />
        </div>

        {/* <Row>
                    <Col lg={4} md={6} sm={12}> */}

        {/* <div style={{ marginBottom: '20px' }}>
                    <strong >Invoice Documents</strong>
                </div> */}

        {details?.requisitiondocs?.length > 0 ? (
          <div>
            <Row>
              {details.requisitiondocs.map((doc) =>
                doc.document_information.url.includes(".pdf") ? (
                  <Col lg={4} md={6} sm={12}>
                    {/* <p>PDF</p> */}
                    <div
                      className="disease-img__div"
                      key={doc.document_id}
                    // style={{ border: "none" }}
                    >
                      <div
                        className="farmer-item__img-div"
                        style={{
                          borderRight: "1px solid silver",
                          alignItems: "center",
                          textAlign: "center",
                          width: "100%",
                          display: "flex",
                          justifyContent: "center",
                          flexDirection: "column",
                        }}
                        onClick={() =>
                          window.open(BASE_URL + "/media/" + doc.document_id)
                        }
                      >
                        <i
                          class="fa-solid fa-file-pdf"
                          style={{ fontSize: "60px" }}
                        ></i>
                      </div>
                      <div>
                        {!doc.document_information.description ||
                          doc.document_information.description.trim() === "" ? (
                          <p
                            style={{
                              color: "silver",
                              fontSize: "12px",
                            }}
                          >
                            <i
                              className="fa fa-exclamation-triangle"
                              aria-hidden="true"
                            ></i>
                            <i
                              className="fa-file-pdf-o"
                              aria-hidden="true"
                              color="pink"
                              size
                              style={{ background: "#000" }}
                            ></i>{" "}
                            No description!
                          </p>
                        ) : (
                          <div>
                            {/* <small>Description:</small> */}
                            <p>{doc.document_information.description}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </Col>
                ) : (
                  <Col lg={4} md={6} sm={12}>
                    <img
                      style={{ padding: 10, objectFit: "contain" }}
                      src={BASE_URL + "/media/" + doc.document_id}
                      alt=""
                      height={"100%"}
                      width={"100%"}
                    />
                  </Col>
                )
              )}
            </Row>
          </div>
        ) : (
          <p style={{ color: "#4c4c4c" }}>
            <i className="fa fa-exclamation-triangle" aria-hidden="true"></i> No
            Invoice files found!
          </p>
        )}

        {/* </Col>
                </Row> */}

        {isMobile && !load && (
          <div>
            <div style={{ maxheight: "75vh" }}>
              <div
                style={{
                  cursor: "pointer",
                  border: "1px solid #D9D9D9",
                  borderRadius: 15,
                  marginTop: 15,
                  padding: 15,
                  backgroundColor: "white",
                  marginRight: "10px",
                  marginLeft: "10px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <div style={{ width: "50%" }}>
                    <p
                      style={{
                        color: "black",
                        fontWeight: "bold",
                        fontSize: 14,
                        margin: "0px",
                      }}
                    >
                      {data.aname}
                    </p>
                    <p
                      style={{
                        color: "black",
                        fontWeight: "bold",
                        fontSize: 14,
                        margin: "5px 0px 20px 0px",
                      }}
                    >
                      {data.requisitioner_name_l10n}
                    </p>
                  </div>
                  {!deliveryChekcer && (
                    <img
                      onClick={openModal}
                      src={require("../qr.png")}
                      style={{
                        height: "40px",
                        position: "relative",
                        marginTop: "-13px",
                      }}
                    />
                  )}
                </div>
                <hr style={{ marginTop: "-5px" }} />

                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    width: "100%",
                  }}
                >
                  <div style={{ alignSelf: "flex-end" }}>
                    <div style={{ display: "flex", marginTop: "5px" }}>
                      <div>
                        <p
                          style={{
                            color: "black",
                            fontSize: 13,
                            marginLeft: 5,
                            fontWeight: "bold",
                            margin: "0px",
                          }}
                        >
                          Vet :{" "}
                        </p>
                      </div>
                      <div>
                        {data.requisitioner_type_l10n.toLowerCase() ==
                          "vet" && (
                            <p
                              style={{
                                color: "black",
                                fontSize: 13,
                                marginLeft: 5,
                                fontWeight: "500",
                                margin: "0px",
                              }}
                            >
                              {data.requisitioner_name_l10n}
                            </p>
                          )}
                        {data.requisitioner_type_l10n.toLowerCase() ==
                          "paravet" && (
                            <p
                              style={{
                                color: "black",
                                fontSize: 13,
                                marginLeft: 5,
                                fontWeight: "500",
                                margin: "0px",
                              }}
                            >
                              {data.approver_name_l10n}
                            </p>
                          )}
                      </div>
                    </div>
                    <div style={{ display: "flex", marginTop: "5px" }}>
                      <div>
                        <p
                          style={{
                            color: "black",
                            fontSize: 13,
                            marginLeft: 5,
                            fontWeight: "bold",
                            margin: "0px",
                          }}
                        >
                          Paravet :{" "}
                        </p>
                      </div>
                      <div>
                        <p
                          style={{
                            color: "black",
                            fontSize: 13,
                            marginLeft: 5,
                            fontWeight: "500",
                            margin: "0px",
                          }}
                        >
                          {data.requisitioner_name_l10n}
                        </p>
                      </div>
                    </div>
                    <div style={{ display: "flex", marginTop: "5px" }}>
                      <div>
                        <p
                          style={{
                            color: "black",
                            fontSize: 13,
                            marginLeft: 5,
                            fontWeight: "bold",
                            margin: "0px",
                          }}
                        >
                          Date :{" "}
                        </p>
                      </div>
                      <div>
                        <p
                          style={{
                            color: "black",
                            fontSize: 13,
                            marginLeft: 5,
                            fontWeight: "500",
                            margin: "0px",
                          }}
                        >
                          {data.created_at.split("T")[0]}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "flex-start",
                      width: "50%",
                    }}
                  >
                    <p
                      style={{
                        textAlign: "center",
                        color:
                          data.status_id == 1000820001
                            ? "#EC6237"
                            : data.status_id == 1000830003
                              ? "red"
                              : data.status_id == 1000820005
                                ? "red"
                                : data.status_id == 1000820004
                                  ? "red"
                                  : data.status_id == 1000820003
                                    ? "#029184"
                                    : data.status_id == 1000820002
                                      ? "orange"
                                      : data.status_id == 1000830002
                                        ? "green"
                                        : data.status_id == 1000830001
                                          ? "green"
                                          : "black",
                        fontWeight: "bold",
                        fontSize: 13,
                        position: "relative",
                        margin: "0px 0px 0px 50px",
                      }}
                    >
                      {data.status_l10n}
                    </p>
                  </div>
                </div>
                <div style={{ maxHeight: "50vh", overflow: "scroll" }}>
                  {formData.map((item, index) => {
                    return (
                      <div
                        key={index}
                        style={{
                          cursor: "pointer",
                          border: "1px solid #D9D9D9",
                          borderRadius: 15,
                          marginTop: 15,
                          padding: 15,
                          backgroundColor: "white",
                        }}
                      >
                        <p
                          style={{
                            color: "black",
                            fontSize: 13,
                            marginLeft: 5,
                            fontWeight: "bold",
                            margin: "0px",
                          }}
                        >
                          {item.medicine}
                        </p>
                        <div
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            marginTop: "10px",
                          }}
                        >
                          <p
                            style={{
                              color: "black",
                              fontSize: 13,
                              marginLeft: 5,
                              fontWeight: "500",
                              margin: "0px",
                            }}
                          >
                            {item.unit}
                          </p>
                          <p
                            style={{
                              color: "black",
                              fontSize: 13,
                              marginLeft: 5,
                              fontWeight: "500",
                              margin: "0px",
                            }}
                          >
                            {item.quantity}
                          </p>
                          <p
                            style={{
                              color: "black",
                              fontSize: 13,
                              marginLeft: 5,
                              fontWeight: "500",
                              margin: "0px",
                            }}
                          >
                            {item.total_size}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
            {deliveryChekcer ? (
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  width: "95%",
                  marginRight: "10px",
                  marginLeft: "10px",
                }}
              >
                <button
                  className="reject"
                  onClick={() => handleStatusUpdate("reject")}
                  style={{
                    opacity: formData.length > 0 ? 1 : 0.5,
                    textAlign: "center",
                    background: "#B02109",
                    color: "white",
                  }}
                >
                  <span style={{ width: "100%" }}>Reject</span>
                </button>
                <button
                  className="deliver"
                  onClick={() => handleStatusUpdate("deliver")}
                  style={{
                    opacity: formData.length > 0 ? 1 : 0.5,
                    textAlign: "center",
                  }}
                >
                  <span style={{ width: "100%" }}>Deliver</span>
                </button>
              </div>
            ) : (
              ""
            )}
          </div>
        )}

        <Modal
          show={modalVisible}
          backdrop="static"
          centered
          // style={{ flex: 1, paddingTop: "300px" }}
        >
          <Modal.Title
            style={{
              padding: "16px 16px 0px 16px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <div>QR code</div>
            <CloseIcon
              onClick={() => {
                closeModal();
              }}
            />
          </Modal.Title>

          <Modal.Body>
            <Container>
              <div
                style={{
                  height: "30vh",
                  width: "100%",
                  marginTop: 40,
                  alignItems: "flex-start",
                }}
              >
                <div>
                  <QRCodeCanvas value={qrdata} size={150} />
                </div>
              </div>
            </Container>
          </Modal.Body>
          {/* <Modal.Footer style={{ justifyContent: 'center' }}>
                    </Modal.Footer> */}
        </Modal>

        <Modal
          show={failedUpdate}
          backdrop="static"
          centered
          // style={{ flex: 1, paddingTop: "300px" }}
        >
          <Modal.Title
            style={{
              padding: "16px 16px 0px 16px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <div>Update Failed</div>
            <CloseIcon
              onClick={() => {
                closeModal();
              }}
            />
          </Modal.Title>

          <Modal.Body>
            <Container>
              <div
                style={{
                  height: "10vh",
                  width: "100%",
                  marginTop: 40,
                  alignItems: "flex-start",
                }}
              >
                <div>Invoice number already exist. Please check</div>
                {/* <button onClick={closeModal()}>
                                    Close
                                </button> */}
              </div>
            </Container>
          </Modal.Body>
          {/* <Modal.Footer style={{ justifyContent: 'center' }}>
                </Modal.Footer> */}
        </Modal>
      </div>
    );
  } else {
    return (
      <div className="loadingcircle">
        <div className="loader"></div>
      </div>
    );
  }
};

export default PharmacyRequisitionDetails;
