import { useState, useCallback, useEffect , useReducer} from "react";
import { useNavigate, useParams } from "react-router-dom";

import axios from "axios";
import Row from "react-bootstrap/Row";
import Col from "react-bootstrap/Col";
import Button from "react-bootstrap/Button";
import Modal from "react-bootstrap/Modal";
import resizeFile from "../../../../Services/file-resizer.service";
// import PDFViewer from "pdf-viewer-reactjs";
import ImageViewer from "react-simple-image-viewer";

import OutlinedInput from "@mui/material/OutlinedInput";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";

import {Image, Card, Table} from "antd";
import { formatedDate , globalReferencesMapper, parseAndAssign } from "../../../../Utilities/Common/utils";

import {
  BASE_URL,
  instance,
  tokenFailureHandler,
} from "../../../../Services/api.service";
import ToastersService from "../../../../Services/toasters.service";
import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  extractChangedValues,
} from "../../../Common/KrushalOCForm";

import Loader from "../../../Loader/Loader";
import CareCalendar from "../../../Common/CareCalendar/CareCalendar";
import FileNotFound from "../../../../Utilities/FileNotFound";
import { videoExtensions } from "../../../../Utilities/Common/utils";
import cattleAdditionalInfoRef from "../../../../Utilities/Common/cattleAdditionalInfoRef";

const {CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../../user/authorize')

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

const initialStateOfImageArray = {
  imageFiles : [],
  concatedData: [],
  cattleData: []
}
const reducer = (state,action)=>{
  switch (action.type) {
    case "SET_FILES":
      return {
        ...state,
        imageFiles : action.payload 
      }
  case "SET_CONCATED_DATA":
    return {
      ...state,
      concatedData: action.payload
    }
  case "SET_CATTLE_DATA":
    return {
      ...state,
      cattleData: action.payload
    }
    default:
      return state
  }
}
const cattleFormDefinitionTab0 = {
  formControls: {
    farmerName: {
      type: "text-l10n",
     
      label: { en: "Farmer Name", mr: "शेतकऱ्याचे नाव" },
      placeHolderText: "Enter Farmer's Name",
    },
    // cattleName: {
    //   type: "text",
    //   label: { en: "Cattle name", mr: "शेतकऱ्याचे नाव" },
    //   placeHolderText: "Enter cattle name",
    //   readOnly: 1,
    // },
    earTag: {
      type: "text",
      label: "Ear tag",
      placeHolderText: "Enter ear tag number",
      validations: [{ type: "Mandatory" }],
      errorMessage: "Ear tag is mandatory!",
    },
    cattleVisualId: {
      type: "text",
      label: "Cattle Visual Id",
      readOnly: 1,
    },
    cattleType: {
      type: "single-select-l10n",
      label: "Cattle Type*",
      errorMessage: "Cattle type is mandatory!",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select Cattle Type",
      validations: [{ type: "Mandatory" }],
    },
    cattleBreed: {
      type: "single-select-l10n",
      label: "Cattle Breed",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select Cattle Breed",
    },
    ageOfCattle: {
      type: "positive-decimal",
      label: "Age of cattle (in years)",
      placeHolderText: "Enter age of cattle (in years)",
      errorMessage: "Age should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    // cattleDateOfBirth: {
    //   type: "date",
    //   label: "DOB of Cattle",
    //   placeHolderText: "Cattle DOB",
    //   // errorMessage: "DOB is mandatory!",
    //   // validations: [{ type: "Mandatory" }],
    // },
    cattleWeight: {
      type: "positive-decimal",
      label: "Weight of cattle (in kgs)",
      placeHolderText: "Enter weight of cattle (in kgs)",
      errorMessage: "Weight should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    cattleMilkingStatus: {
      type: "single-select-l10n",
      label: "Cattle milking status",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select milking status",
    },
    cattleSubscriptionPlan: {
      type: "single-select-l10n",
      label: "Cattle subscription plan",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select subscription plan",
      readOnly : 0,
      validations: [],
      errorMessage : ""
    },
    subscriptionDate: {
      type: "date",
      label: "Subscription date",
      placeHolderText: "Subscription date",
      readOnly : 0,
      validations: [],
      errorMessage : ""
    },
  },
  visibleFormControls: [
    "farmerName",
    // "cattleName",
    "earTag",
    "cattleVisualId",
    "cattleType",
    "cattleBreed",
    "ageOfCattle",
    // "cattleDateOfBirth",
    "cattleWeight",
    "cattleMilkingStatus",
    "cattleSubscriptionPlan",
    "subscriptionDate",
  ],
  elementToDataMapping: {
    farmerName: "farmer_name",
    // cattleName: "animal_name_1",
    earTag: "ear_tag_1",
    cattleVisualId: "animal_visual_id",
    cattleType: "animal_type",
    cattleBreed: "animal_breed_1",
    ageOfCattle: "animal_age_1",
    // cattleDateOfBirth: "age_of_animal_as_of_1",
    cattleWeight: "animal_weight_1",
    cattleMilkingStatus: "animal_milking_1",
    cattleSubscriptionPlan: "subscription_plan_1",
    subscriptionDate: "healthcare_plan_subscription_date_1",
  },
  dataToElementMapping: {
    farmer_name: "farmerName",
    // animal_name_1: "cattleName",
    ear_tag_1: "earTag",
    animal_visual_id: "cattleVisualId",
    animal_type: "cattleType",
    animal_breed_1: "cattleBreed",
    animal_age_1: "ageOfCattle",
    // age_of_animal_as_of_1: "cattleDateOfBirth",
    animal_weight_1: "cattleWeight",
    animal_milking_1: "cattleMilkingStatus",
    subscription_plan_1: "cattleSubscriptionPlan",
    healthcare_plan_subscription_date_1: "subscriptionDate",
  },
};

const cattleFormDefinitionTab1 = {
  formControls: {
    noOfCalvings: {
      type: "positive-number",
      label: "No. of Calvings",
      placeHolderText: "Enter no. of calvings",
      errorMessage: "Number should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    monthsSinceLastCalving: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "No. of months since last calving",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select no. of months",
      // errorMessage: "Number should be positive and numeric!",
      // validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    lastCalvingDate: {
      type: "date",
      // readOnly: 1,
      label: "Last calving date",
      placeHolderText: "Select last calving date",
    },
    noOfMonthsPregnant: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "No. of Months Pregnant",
      placeHolderText: "Enter no. of months pregnant",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      errorMessage: "Number should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    pregnancyRange: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Pregnancy range of cattle",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select preganancy range",
    },
    lastPregnancyDeterminationDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Pregnancy Determination Date",
      placeHolderText: "Select last pregnancy determination date",
    },
  },
  visibleFormControls: [
    "noOfCalvings",
    "monthsSinceLastCalving",
    "lastCalvingDate",
    "noOfMonthsPregnant",
    "pregnancyRange",
    "lastPregnancyDeterminationDate",
  ],
  elementToDataMapping: {
    noOfCalvings: "number_of_calvings_1",
    monthsSinceLastCalving: "months_since_last_calving_1",
    lastCalvingDate: "last_calving_date_1",
    noOfMonthsPregnant: "number_of_months_pregnant_1",
    pregnancyRange: "pregnancy_range_of_animal_1",
    lastPregnancyDeterminationDate: "last_pregnancy_determination_date_1",
  },
  dataToElementMapping: {
    number_of_calvings_1: "noOfCalvings",
    months_since_last_calving_1: "monthsSinceLastCalving",
    last_calving_date_1: "lastCalvingDate",
    pregnancy_range_of_animal_1: "pregnancyRange",
    number_of_months_pregnant_1: "noOfMonthsPregnant",
    last_pregnancy_determination_date_1: "lastPregnancyDeterminationDate",
  },
};

const cattleFormDefinitionTab2 = {
  formControls: {
    lastBrucellosisVaccinationDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Brucellosis Vaccination Date",
      placeHolderText: "Select Last Brucellosis Vaccination Date",
    },
    brucellosisVaccinationDoneOrNot: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Brucellosis Vaccination Status",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },
    lastAIDate: {
      type: "date",
      // readOnly: 1,
      label: "Last AI date",
      placeHolderText: "Select last AI date",
    },
    lastAnthraxDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Anthrax date",
      placeHolderText: "Select last Anthrax date",
    },
    anthraxVaccinationStatus: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Anthrax Vaccination Status",
      placeHolderText: "Select Anthrax Vaccination Status",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },
    noOfMonthsSinceDeworming: {
      type: "positive-decimal",
      // readOnly: 1,
      label: "Number of Months since Last Deworming",
      placeHolderText: "Enter no. of months since last deworming",
      errorMessage: "Number should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    lastDewormingDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Deworming date",
      placeHolderText: "Select last Deworming date",
    },
    lastDewormingStatus: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Last Deworming Status",
      placeHolderText: "Select Last Deworming Status",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },

    lastDipCupsReplacementDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Dip Cups replacement date",
      placeHolderText: "Select Last Dip cups replacement date",
    },
    dipCupsStatus: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Dip Cups Status",
      placeHolderText: "Select Dip Cups Status",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },

    lastFMDHSBQDate: {
      type: "date",
      // readOnly: 1,
      label: "Last FMD/HS/BQ Vaccination Date",
      placeHolderText: "Select last FMD/HS/BQ vaccination date",
    },
    lastFMDHSBQDateRange: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Last FMD/HS/BQ Vaccination Date Range",
      placeHolderText: "Select Last FMD/HS/BQ Vaccination Date Range",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },

    lastFMDDate: {
      type: "date",
      // readOnly: 1,
      label: "Last FMD Vaccination Date",
      placeHolderText: "Select last FMD vaccination date",
    },
    lastFMDDateRange: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Last FMD Vaccination Date Range",
      placeHolderText: "Select Last FMD Vaccination Date Range",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },

    lastThileriosisVaccinationDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Thileriosis Vaccination Date",
      placeHolderText: "Select last thileriosis vaccination date",
    },
    lastThileriosisVaccinationStatus: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Last Thileriosis Vaccination Status",
      placeHolderText: "Select Last Thileriosis Vaccination Status",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },

    lastTickControlDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Tick Control Date",
      placeHolderText: "Select last tick control date",
    },
    lastTickControlStatus: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Last Tick Control Status",
      placeHolderText: "Select Last Tick Control Status",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },

    maxLPDofCattle: {
      type: "positive-decimal",
      label: "Max LPD of Cattle (not average)",
      placeHolderText: "Enter max LPD of cattle",
      errorMessage: "Number should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    // noOfMonthsSinceDipCups: {
    //   type: "positive-decimal",
    //   readOnly: 1,
    //   label: "Number of Months Since Last Dip Cups Replacement",
    //   placeHolderText: "Enter no. of months since last Dip Cups Replacement",
    //   errorMessage: "Number should be positive and numeric!",
    //   validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    // },
    // noOfMonthsSinceTickControl: {
    //   type: "positive-decimal",
    //   readOnly: 1,
    //   label: "Number of Months Since Last Tick Control",
    //   placeHolderText: "Enter no. of months since last tick control",
    //   errorMessage: "Number should be positive and numeric!",
    //   validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    // },
  },
  visibleFormControls: [
    "lastBrucellosisVaccinationDate",
    "brucellosisVaccinationDoneOrNot",

    "lastAIDate",
    "lastAnthraxDate",

    "anthraxVaccinationStatus",

    "noOfMonthsSinceDeworming",
    "lastDewormingDate",
    "lastDewormingStatus",

    "lastDipCupsReplacementDate",
    "dipCupsStatus",

    "lastFMDHSBQDate",
    "lastFMDHSBQDateRange",

    "lastFMDDate",
    "lastFMDDateRange",

    "lastThileriosisVaccinationDate",
    "lastThileriosisVaccinationStatus",

    "lastTickControlDate",
    "lastTickControlStatus",

    "maxLPDofCattle",

    // "noOfMonthsSinceDipCups",
    // "noOfMonthsSinceTickControl",
  ],
  elementToDataMapping: {
    lastBrucellosisVaccinationDate: "last_brucellosis_vaccination_date_1",
    brucellosisVaccinationDoneOrNot: "brucellosis_vaccination_status_1",

    lastAIDate: "last_ai_date_1",

    lastAnthraxDate: "last_anthrax_date_1",
    anthraxVaccinationStatus: "anthrax_vaccination_status_1",

    noOfMonthsSinceDeworming: "number_of_month_since_last_deworming_1",
    lastDewormingDate: "last_deworming_date_1",
    lastDewormingStatus: "last_deworming_status_1",

    lastDipCupsReplacementDate: "last_dip_cups_replacement_date_1",
    dipCupsStatus: "dip_cup_status_1",

    lastFMDHSBQDate: "last_fmd_or_hs_or_bq_vaccination_date_1",
    lastFMDHSBQDateRange: "last_fmd_hs_bq_date_range_1",

    lastFMDDate: "last_fmd_vaccination_date_1",
    lastFMDDateRange: "last_fmd_vaccination_date_range_1",

    lastThileriosisVaccinationDate: "last_theileriosis_vaccination_date_1",
    lastThileriosisVaccinationStatus: "theileriosis_vaccination_status_1",

    lastTickControlDate: "last_tick_control_date_1",
    lastTickControlStatus: "last_tick_control_status_1",

    maxLPDofCattle: "animal_average_lpd_1",

    // noOfMonthsSinceDipCups: "number_of_month_since_last_dip_cups_replacement_1",
    // noOfMonthsSinceTickControl: "number_of_month_since_last_tick_control_1",
  },
  dataToElementMapping: {
    last_brucellosis_vaccination_date_1: "lastBrucellosisVaccinationDate",
    brucellosis_vaccination_status_1: "brucellosisVaccinationDoneOrNot",

    last_ai_date_1: "lastAIDate",

    last_anthrax_date_1: "lastAnthraxDate",
    anthrax_vaccination_status_1: "anthraxVaccinationStatus",

    number_of_month_since_last_deworming_1: "noOfMonthsSinceDeworming",
    last_deworming_date_1: "lastDewormingDate",
    last_deworming_status_1: "lastDewormingStatus",

    last_dip_cups_replacement_date_1: "lastDipCupsReplacementDate",
    dip_cup_status_1: "dipCupsStatus",

    last_fmd_or_hs_or_bq_vaccination_date_1: "lastFMDHSBQDate",
    last_fmd_hs_bq_date_range_1: "lastFMDHSBQDateRange",

    last_fmd_vaccination_date_1: "lastFMDDate",
    last_fmd_vaccination_date_range_1: "lastFMDDateRange",

    last_theileriosis_vaccination_date_1: "lastThileriosisVaccinationDate",
    theileriosis_vaccination_status_1: "lastThileriosisVaccinationStatus",

    last_tick_control_date_1: "lastTickControlDate",
    last_tick_control_status_1: "lastTickControlStatus",

    animal_average_lpd_1: "maxLPDofCattle",
    // number_of_month_since_last_dip_cups_replacement_1: "noOfMonthsSinceDipCups",
    // number_of_month_since_last_tick_control_1: "noOfMonthsSinceTickControl",
  },
};

// const cattleFormDefinitionTab1 = {
//   formControls: {
//     pregnancyRange: {
//       type: "single-select-l10n",
//       readOnly: 1,
//       label: "Pregnancy range of cattle",
//       labelKeyInList: "reference_name_l10n",
//       valueKeyInList: "reference_id",
//       placeHolderText: "Select preganancy range",
//     },
//     noOfMonthsPregnant: {
//       type: "positive-decimal",
//       readOnly: 1,
//       label: "No. of Months Pregnant",
//       placeHolderText: "Enter no. of months pregnant",
//       errorMessage: "Number should be positive and numeric!",
//       validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
//     },
//     noOfCalvings: {
//       type: "positive-number",
//       label: "No. of Calvings",
//       placeHolderText: "Enter no. of calvings",
//       errorMessage: "Number should be positive and numeric!",
//       validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
//     },
//     monthsSinceLastCalving: {
//       type: "positive-number",
//       readOnly: 1,
//       label: "No. of months since last calving",
//       placeHolderText: "Enter no. of months",
//       errorMessage: "Number should be positive and numeric!",
//       validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
//     },
//     lastCalvingDate: {
//       type: "date",
//       readOnly: 1,
//       label: "Last calving date",
//       placeHolderText: "Select last calving date",
//     },
//   },
//   visibleFormControls: [
//     "pregnancyRange",
//     "noOfMonthsPregnant",
//     "noOfCalvings",
//     "monthsSinceLastCalving",
//     "lastCalvingDate",
//   ],
//   elementToDataMapping: {
//     pregnancyRange: "pregnancy_range_of_animal_1",
//     noOfMonthsPregnant: "number_of_months_pregnant_1",
//     noOfCalvings: "number_of_calvings_1",
//     monthsSinceLastCalving: "months_since_last_calving_1",
//     lastCalvingDate: "last_calving_date_1",
//   },
//   dataToElementMapping: {
//     pregnancy_range_of_animal_1: "pregnancyRange",
//     number_of_months_pregnant_1: "noOfMonthsPregnant",
//     number_of_calvings_1: "noOfCalvings",
//     months_since_last_calving_1: "monthsSinceLastCalving",
//     last_calving_date_1: "lastCalvingDate",
//   },
// };

// const cattleFormDefinitionTab2 = {
//   formControls: {
//     brucellosisVaccinationDoneOrNot: {
//       type: "single-select-l10n",
//       readOnly: 1,
//       label: "Brucellosis Vaccination Done or Not",
//       placeHolderText: "Select",
//       labelKeyInList: "reference_name_l10n",
//       valueKeyInList: "reference_id",
//     },
//     lastAIDate: {
//       type: "date",
//       readOnly: 1,
//       label: "Last AI date",
//       placeHolderText: "Select last AI date",
//     },
//     lastAnthraxDate: {
//       type: "date",
//       readOnly: 1,
//       label: "Last Anthrax date",
//       placeHolderText: "Select last Anthrax date",
//     },
//     lastDewormingDate: {
//       type: "date",
//       readOnly: 1,
//       label: "Last Deworming date",
//       placeHolderText: "Select last Deworming date",
//     },
//     lastDipCupsReplacementDate: {
//       type: "date",
//       readOnly: 1,
//       label: "Last dip cups replacement date",
//       placeHolderText: "Select last dip cups replacement date",
//     },
//     lastFMDHSBQDate: {
//       type: "date",
//       readOnly: 1,
//       label: "Last FMD/HS/BQ Vaccination Date",
//       placeHolderText: "Select last FMD/HS/BQ vaccination date",
//     },
//     lastFMDDate: {
//       type: "date",
//       readOnly: 1,
//       label: "Last FMD Vaccination Date",
//       placeHolderText: "Select last FMD vaccination date",
//     },
//     lastPregnancyDeterminationDate: {
//       type: "date",
//       readOnly: 1,
//       label: "Last Pregnancy Determination Date",
//       placeHolderText: "Select last pregnancy determination date",
//     },
//     lastThileriosisVaccinationDate: {
//       type: "date",
//       readOnly: 1,
//       label: "Last Thileriosis Vaccination Date",
//       placeHolderText: "Select last thileriosis vaccination date",
//     },
//     lastTickControlDate: {
//       type: "date",
//       readOnly: 1,
//       label: "Last Tick Control Date",
//       placeHolderText: "Select last tick control date",
//     },
//     maxLPDofCattle: {
//       type: "positive-decimal",
//       label: "Max LPD of Cattle (not average)",
//       placeHolderText: "Enter max LPD of cattle",
//       errorMessage: "Number should be positive and numeric!",
//       validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
//     },
//     noOfMonthsSinceDeworming: {
//       type: "positive-decimal",
//       readOnly: 1,
//       label: "Number of Months since Last Deworking",
//       placeHolderText: "Enter no. of months since last deworming",
//       errorMessage: "Number should be positive and numeric!",
//       validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
//     },
//     noOfMonthsSinceDipCups: {
//       type: "positive-decimal",
//       readOnly: 1,
//       label: "Number of Months Since Last Dip Cups Replacement",
//       placeHolderText: "Enter no. of months since last Dip Cups Replacement",
//       errorMessage: "Number should be positive and numeric!",
//       validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
//     },
//     noOfMonthsSinceTickControl: {
//       type: "positive-decimal",
//       readOnly: 1,
//       label: "Number of Months Since Last Tick Control",
//       placeHolderText: "Enter no. of months since last tick control",
//       errorMessage: "Number should be positive and numeric!",
//       validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
//     },
//   },
//   visibleFormControls: [
//     "brucellosisVaccinationDoneOrNot",
//     "lastAIDate",
//     "lastAnthraxDate",
//     "lastDewormingDate",
//     "lastDipCupsReplacementDate",
//     "lastFMDHSBQDate",
//     "lastFMDDate",
//     "lastPregnancyDeterminationDate",
//     "lastThileriosisVaccinationDate",
//     "lastTickControlDate",
//     "maxLPDofCattle",
//     "noOfMonthsSinceDeworming",
//     "noOfMonthsSinceDipCups",
//     "noOfMonthsSinceTickControl",
//   ],
//   elementToDataMapping: {
//     brucellosisVaccinationDoneOrNot: "brucellosis_vaccination_status_1",
//     lastAIDate: "last_ai_date_1",
//     lastAnthraxDate: "last_anthrax_vaccination_date_1",
//     lastDewormingDate: "last_deworming_date_1",
//     lastDipCupsReplacementDate: "last_dip_cups_replacement_date_1",
//     lastFMDHSBQDate: "last_fmd_or_hs_or_bq_vaccination_date_1",
//     lastFMDDate: "last_fmd_vaccination_date_1",
//     lastPregnancyDeterminationDate: "last_pregnancy_determination_date_1",
//     lastThileriosisVaccinationDate: "last_thileriosis_vaccination_date_1",
//     lastTickControlDate: "last_tick_control_date_1",
//     maxLPDofCattle: "max_lpd_of_animal_1",
//     noOfMonthsSinceDeworming: "number_of_month_since_last_deworming_1",
//     noOfMonthsSinceDipCups: "number_of_month_since_last_dip_cups_replacement_1",
//     noOfMonthsSinceTickControl: "number_of_month_since_last_tick_control_1",
//   },
//   dataToElementMapping: {
//     brucellosis_vaccination_status_1: "brucellosisVaccinationDoneOrNot",
//     last_ai_date_1: "lastAIDate",
//     last_anthrax_vaccination_date_1: "lastAnthraxDate",
//     last_deworming_date_1: "lastDewormingDate",
//     last_dip_cups_replacement_date_1: "lastDipCupsReplacementDate",
//     last_fmd_or_hs_or_bq_vaccination_date_1: "lastFMDHSBQDate",
//     last_fmd_vaccination_date_1: "lastFMDDate",
//     last_pregnancy_determination_date_1: "lastPregnancyDeterminationDate",
//     last_thileriosis_vaccination_date_1: "lastThileriosisVaccinationDate",
//     last_tick_control_date_1: "lastTickControlDate",
//     max_lpd_of_animal_1: "maxLPDofCattle",
//     number_of_month_since_last_deworming_1: "noOfMonthsSinceDeworming",
//     number_of_month_since_last_dip_cups_replacement_1: "noOfMonthsSinceDipCups",
//     number_of_month_since_last_tick_control_1: "noOfMonthsSinceTickControl",
//   },
// };

const cattleFormDefinitionTab3 = {
  formControls: {
    insuranceStatus: {
      type: "single-select-l10n",
      label: "Insurance Status of Cattle",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },
    threeMonthIllness: {
      type: "multi-select-l10n",
      label: "3 Month Cattle Illnesses",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },
    twoMonthHealthcare: {
      type: "multi-select-l10n",
      label: "2 Month Preventive Healthcare",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },
    anyDefectsInAnimal: {
      type: "single-select-l10n",
      label: "Any Defects In Animal",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    }
  },
  visibleFormControls: [
    "insuranceStatus",
    "threeMonthIllness",
    "twoMonthHealthcare",
    "anyDefectsInAnimal",
  ],
  elementToDataMapping: {
    insuranceStatus: "insurance_status_of_animal_1",
    threeMonthIllness: "three_month_animal_illness_1",
    twoMonthHealthcare: "two_month_preventive_healthcare_1",
    anyDefectsInAnimal: "any_defects_in_animal_1",
  },
  dataToElementMapping: {
    insurance_status_of_animal_1: "insuranceStatus",
    three_month_animal_illness_1: "threeMonthIllness",
    two_month_preventive_healthcare_1: "twoMonthHealthcare",
    any_defects_in_animal_1: "anyDefectsInAnimal",
  },
};

const cattleFormDefinitionTab4 = {
  formControls: {
    salesDoneBy: {
      type: "text-l10n",
      label: "Sales Done By",
      placeHolderText: "Select",
      // labelKeyInList: "reference_name_l10n",
      // valueKeyInList: "reference_id",
      readOnly: 1,
    },
    animalStage: {
      type: "single-select-l10n",
      label: "Animal Stage",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1,
    },
    noOfTeeth: {
      type: "text",
      label: "Number of Teeth",
      placeHolderText: "Number of Teeth",
      readOnly: 1,
    },
    temperatureOfAnimal: {
      type: "positive-decimal",
      label: "Temperature of Animal",
      placeHolderText: "Temperature of Animal",
      readOnly: 1,
    },
    monthPregnantDate: {
      type: "text",
      label: "Months Preganant As of Date",
      placeHolderText: "Months Preganant As of Date",
      readOnly: 1,
    },
    locoScore: {
      type: "single-select-l10n",
      label: "Animal Locomotion Score",
      placeHolderText: "Animal Locomotion Score",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1,
    },
    uCleft: {
      type: "single-select-l10n",
      label: "Udder Cleft",
      placeHolderText: "Udder Cleft",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1,
    },
    uDepth: {
      type: "single-select-l10n",
      label: "Udder Depth",
      placeHolderText: "Udder Depth",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1,
    },
    udderBalance: {
      type: "single-select-l10n",
      label: "Udder Balance",
      placeHolderText: "Udder Balance",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1,
    },
    udderTeatPlacement: {
      type: "single-select-l10n",
      label: "Udder Teat Placement",
      placeHolderText: "Udder Teat Placement",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1,
    },
    udderTeatLength: {
      type: "single-select-l10n",
      label: "Udder Teat Length",
      placeHolderText: "Udder Teat Length",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1,
    },
    animalHealthScore: {
      type: "text",
      label: "Animal Health Score",
      placeHolderText: "Animal Health Score",
      readOnly: 1,
    },
    animalBodyScore: {
      type: "text",
      label: "Animal Body Score",
      placeHolderText: "Animal Body Score",
      readOnly: 1,
    },
    animalUdderScore: {
      type: "text",
      label : "Animal Udder Score",
      placeHolderText: "Animal Udder Score",
      readOnly: 1,
    },
    bodyObservation1: {
      type: "single-select-l10n",
      label : "Body Score Observation Tail and Pin Bone",
      placeHolderText: "Body Score Observation Tail and Pin Bone",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1,
    },
    bodyObservation2: {
      type: "single-select-l10n",
      label : "body score Observation Back Bone",
      placeHolderText: "Body score Observation Back Bone",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1,
    },
    bodyObservation3: {
      type: "single-select-l10n",
      label : "Body Score Hip and Pin Bone",
      placeHolderText: "Body Score Hip and Pin Bone",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1,
    },
    bodyObservation4: {
      type: "single-select-l10n",
      label : "Body Score Observation 4",
      placeHolderText: "Body Score Observation 4",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1,
    },
    animalAverageLpd: {
      type: "text",
      label : "Animal Average LPD",
      placeHolderText: "Animal Average Lpd",
      readOnly: 1,
    },
    animalForeignBody: {
      type: "single-select-l10n",
      label : "Animal Foreign Body",
      placeHolderText: "Animal Foreign Body",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1,
    },
    gapInTeeth: {
      type: "single-select-l10n",
      label: "Animal Gap in Teeth",
      placeHolderText: "Animal Gap in Teeth",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1
    },
    hornsExist: {
      type: "single-select-l10n",
      label: "Animal Horns Exist or Not",
      placeHolderText: "Animal Horns Exist or Not",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1
    },
    spreadOfPatches: {
      type: "single-select-l10n",
      label: "Animal Spread of Patches",
      placeHolderText: "Animal Spread of Patches",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1
    },
    animalStretchabilityOfUdder: {
      type: "single-select-l10n",
      label: "Animal Stretchability of Udder",
      placeHolderText: "Animal Stretchability of Udder",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1
    },
    animalVenis: {
      type: "single-select-l10n",
      label: "Animal Veins of Udder",
      placeHolderText: "Animal Veins of Udder",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1
    },
    animalAdditionalTeats: {
      type: "single-select-l10n",
      label: "Animal Additional Teats",
      placeHolderText: "Animal Additional Teats",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      readOnly: 1
    }
  },
  visibleFormControls: [
    "salesDoneBy",
    "animalStage",
    "noOfTeeth",
    "temperatureOfAnimal",
    "monthPregnantDate", 
    "locoScore",
    "uCleft",
    "uDepth",
    "udderBalance",
    "udderTeatPlacement",
    "udderTeatLength",
    "animalHealthScore",
    "animalBodyScore",
    "animalUdderScore",
    "bodyObservation1",
    "bodyObservation2",
    "bodyObservation3",
    // "bodyObservation4",
    "animalAverageLpd",
    "animalForeignBody",
    "gapInTeeth",
    "hornsExist",
    "spreadOfPatches",
    "animalStretchabilityOfUdder",
    "animalVenis",
    "animalAdditionalTeats"
  ],
  elementToDataMapping: {
    salesDoneBy: "staff_name_l10n",
    animalStage: "animal_stage_1",
    noOfTeeth: "animal_number_of_permanent_teeth_1",
    temperatureOfAnimal: "animal_temperature_1",
    monthPregnantDate: "months_pregnant_as_of_date",
    locoScore: "animal_locomotion_score_1",
    uCleft: "udder_cleft",
    uDepth: "udder_depth",
    udderBalance: "udder_balance",
    udderTeatPlacement: "udder_teat_placement",
    udderTeatLength: "udder_teat_length",
    animalHealthScore: "animal_health_score",
    animalBodyScore: "animal_body_score_1",
    animalUdderScore: "animal_udder_score_1",
    bodyObservation1: "body_score_observation_tail_and_pin_bone",
    bodyObservation2: "body_score_observation_back_bone",
    bodyObservation3: "body_score_hip_and_pin_bone",
    bodyObservation4: "body_score_observation_4",
    animalAverageLpd: "animal_average_lpd_1",
    animalForeignBody: "animal_foreign_body_1",
    gapInTeeth: "animal_gap_in_teeth_1",
    hornsExist: "animal_horns_exist_or_not_1",
    spreadOfPatches: "animal_spread_of_patches_1",
    animalStretchabilityOfUdder: "animal_stretchability_of_udder_1",
    animalVenis: "animal_visbility_of_veins_1",
    animalAdditionalTeats: "animal_additional_teats_1"
  },
  dataToElementMapping:{
    staff_name_l10n: "salesDoneBy",
    animal_stage_1: "animalStage",
    animal_number_of_permanent_teeth_1: "noOfTeeth",
    animal_temperature_1: "temperatureOfAnimal",
    months_pregnant_as_of_date: "monthPregnantDate",
    animal_locomotion_score_1: "locoScore",
    udder_cleft: "uCleft",
    udder_depth: "uDepth",
    udder_balance: "udderBalance",
    udder_teat_placement: "udderTeatPlacement",
    udder_teat_length: "udderTeatLength",
    animal_health_score: "animalHealthScore",
    animal_body_score_1: "animalBodyScore",
    animal_udder_score_1: "animalUdderScore",
    body_score_observation_tail_and_pin_bone: "bodyObservation1",
    body_score_observation_back_bone: "bodyObservation2",
    body_score_hip_and_pin_bone: "bodyObservation3",
    body_score_observation_4: "bodyObservation4",
    animal_average_lpd_1 : "animalAverageLpd",
    animal_foreign_body_1: "animalForeignBody",
    animal_gap_in_teeth_1: "gapInTeeth",
    animal_horns_exist_or_not_1: "hornsExist",
    animal_spread_of_patches_1: "spreadOfPatches",
    animal_stretchability_of_udder_1: "animalStretchabilityOfUdder",
    animal_visbility_of_veins_1: "animalVenis",
    animal_additional_teats_1: "animalAdditionalTeats",
    
  }  
}

const initialFormStateTab0 = {
  farmerName: {},
  cattleName: {},
  earTag: {},
  cattleVisualId: {},
  cattleType: {},
  cattleBreed: {},
  ageOfCattle: {},
  cattleDateOfBirth: {},
  cattleWeight: {},
  cattleMilkingStatus: {},
  cattleSubscriptionPlan: {},
  subscriptionDate: {},
};

const initialFormStateTab1 = {
  noOfCalvings: {},
  monthsSinceLastCalving: {},
  lastCalvingDate: {},
  noOfMonthsPregnant: {},
  pregnancyRange: {},
  lastPregnancyDeterminationDate: {},
};

const initialFormStateTab2 = {
  lastBrucellosisVaccinationDate: {},
  brucellosisVaccinationDoneOrNot: {},

  lastAIDate: {},

  lastAnthraxDate: {},
  anthraxVaccinationStatus: {},

  noOfMonthsSinceDeworming: {},
  lastDewormingDate: {},
  lastDewormingStatus: {},

  lastDipCupsReplacementDate: {},
  dipCupsStatus: {},

  lastFMDHSBQDate: {},
  lastFMDHSBQDateRange: {},

  lastFMDDate: {},
  lastFMDDateRange: {},

  lastThileriosisVaccinationDate: {},
  lastThileriosisVaccinationStatus: {},

  lastTickControlDate: {},
  lastTickControlStatus: {},

  maxLPDofCattle: {},
  // noOfMonthsSinceDipCups: {},
  // noOfMonthsSinceTickControl: {},
};

// const initialFormStateTab1 = {
//   pregnancyRange: {},
//   noOfMonthsPregnant: {},
//   noOfCalvings: {},
//   monthsSinceLastCalving: {},
//   lastCalvingDate: {},
// };

// const initialFormStateTab2 = {
//   brucellosisVaccinationDoneOrNot: {},
//   lastAIDate: {},
//   lastAnthraxDate: {},
//   lastDewormingDate: {},
//   lastDipCupsReplacementDate: {},
//   lastFMDHSBQDate: {},
//   lastFMDDate: {},
//   lastPregnancyDeterminationDate: {},
//   lastThileriosisVaccinationDate: {},
//   lastTickControlDate: {},
//   maxLPDofCattle: {},
//   noOfMonthsSinceDeworming: {},
//   noOfMonthsSinceDipCups: {},
//   noOfMonthsSinceTickControl: {},
// };

const initialFormStateTab3 = {
  insuranceStatus: {},
  threeMonthIllness: {},
  twoMonthHealthcare: {},
  anyDefectsInAnimal: {},
};

const initialFormStateTab4 = {
  salesDoneBy: {value: {}},
  animalStage: {},
  noOfTeeth: {},
  temperatureOfAnimal: {},
  monthPregnantDate: {},
  locoScore: {},
  uCleft: {},
  uDepth: {},
  udderBalance: {},
  udderTeatPlacement: {},
  udderTeatLength: {},
  animalHealthScore: {},
  animalBodyScore: {},
  animalUdderScore: {},
  bodyObservation1: {},
  bodyObservation2: {},
  bodyObservation3: {},
  bodyObservation4: {},
  animalAverageLpd: {},
  animalForeignBody: {},
  gapInTeeth: {},
  hornsExist: {},
  spreadOfPatches: {},
  animalStretchabilityOfUdder: {},
  animalVenis: {},
  animalAdditionalTeats: {}
}
const externalMappingKeys = [
  'animal_locomotion_score_1',
  'body_score_observation_tail_and_pin_bone',
  'body_score_observation_back_bone',
  'body_score_hip_and_pin_bone',
  'body_score_observation_4',
  'udder_cleft',
  'udder_depth',
  'udder_balance',
  'udder_teat_placement',
  'udder_teat_length',
  'animal_foreign_body_1',
  'animal_gap_in_teeth_1',
  'animal_horns_exist_or_not_1',
  'animal_spread_of_patches_1',
  'animal_stretchability_of_udder_1',
  'animal_visbility_of_veins_1',
  'animal_additional_teats_1'
];
const getFormFieldConfigurationsAsLists = async (navigate) => {
  try {
    // const response = await axios.get(BASE_URL + "/references/animal", {
    //   headers: {
    //     token: localStorage.getItem("accessToken"),
    //     "X-App-Id": "123456789",
    //   },
    // });
    const response = await instance({
      url: "/references/animal",
      method: "GET",
      // data: JSON.stringify(reqBody),
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Form field configurations", response);
    console.log("hello",response.data.data.any_defects_in_animal_1.category_options)
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        throw new Error("Unauthorized");
      }
      const cattleTypeList = response.data.data?.animal_type?.category_options;
      const cattleBreedList =
        response.data.data?.animal_breed_1?.category_options;
      const milkingStatusList =
        response.data.data?.animal_milking_1?.category_options;
      const subscriptionPlanList =
        response.data.data?.subscription_plan_1?.category_options;
      // const preganancyRangeList =
      //   response.data.data.pregnancy_range_of_animal_1.category_options;

      const preganancyRangeList =
        response.data.data?.pregnancy_range_of_animal_1?.category_options;
      const monthsSinceLastCalving =
        response.data.data?.months_since_last_calving_1?.category_options;

      const brucellosisVaccinationList =
        response.data.data?.brucellosis_vaccination_status_1?.category_options;
      const anthraxVaccinationStatusList =
        response.data.data?.anthrax_vaccination_status_1?.category_options;
      const lastDewormingStatusList =
        response.data.data?.last_deworming_status_1?.category_options;
      const dipCupsStatusList =
        response.data.data?.dip_cup_status_1?.category_options;
      const lastFMDHSBQDateRangeList =
        response.data.data?.last_fmd_hs_bq_date_range_1?.category_options;
      const lastFMDVaccinationDateRangeList =
        response.data.data?.last_fmd_vaccination_date_range_1?.category_options;
      const lastTheileriosisVaccinationDateRangeList =
        response.data.data?.theileriosis_vaccination_status_1?.category_options;
      const lastTickControlStatusList =
        response.data.data?.last_tick_control_status_1?.category_options;

      const insuranceStatusList =
        response.data.data?.insurance_status_of_animal_1?.category_options;
      const threeMonthsIllnessList =
        response.data.data?.three_month_animal_illness_1?.category_options;
      const twoMonthsHealthcareList =
        response.data.data?.two_month_preventive_healthcare_1?.category_options;
      const anyDefectsInAnimalList =
        response.data.data?.any_defects_in_animal_1?.category_options;

      const animalStageList = response.data.data?.animal_stage_1?.category_options
      const noOfMonthsPregnantList = cattleAdditionalInfoRef.numberOfMonthsPregnant_1.category_options

      return [
        cattleTypeList,
        cattleBreedList,
        milkingStatusList,
        subscriptionPlanList,
        preganancyRangeList,
        monthsSinceLastCalving,

        brucellosisVaccinationList,
        anthraxVaccinationStatusList,
        lastDewormingStatusList,
        dipCupsStatusList,
        lastFMDHSBQDateRangeList,
        lastFMDVaccinationDateRangeList,
        lastTheileriosisVaccinationDateRangeList,
        lastTickControlStatusList,

        insuranceStatusList,
        threeMonthsIllnessList,
        twoMonthsHealthcareList,
        anyDefectsInAnimalList,

        animalStageList,
        noOfMonthsPregnantList
      ];
    }
  } catch (error) {
    console.log(error);
    // throw error;
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const setReferenceValueFromRefData = ()=>{

  const cattleTypeList = globalReferencesMapper(10001400);
  const cattleBreedList = globalReferencesMapper(10001500);
  const milkingStatusList = globalReferencesMapper(10001050);
      const subscriptionPlanList = globalReferencesMapper(10002400);
      // const preganancyRangeList =
      //   response.data.data.pregnancy_range_of_animal_1.category_options;

      const preganancyRangeList = globalReferencesMapper(10001900);
      const monthsSinceLastCalving =
        [];// need to check

      const brucellosisVaccinationList =
       []; // need to check
      const anthraxVaccinationStatusList =
        []; // need to check anthrax_vaccination_status_1
      const lastDewormingStatusList =
        []; // brucellosis_vaccination_done_or_not_1
      const dipCupsStatusList =
        []; // dip_cup_status_1
      const lastFMDHSBQDateRangeList =
        []; // last_fmd_hs_bq_date_range_1
      const lastFMDVaccinationDateRangeList =
        []; // last_fmd_vaccination_date_range_1
      const lastTheileriosisVaccinationDateRangeList =
        []; // theileriosis_vaccination_status_1
      const lastTickControlStatusList =
        []; // last_tick_control_status_1

      const insuranceStatusList = globalReferencesMapper(10002000);
      const threeMonthsIllnessList = globalReferencesMapper(10001700);
      const twoMonthsHealthcareList = globalReferencesMapper(10001800);
      const anyDefectsInAnimalList = globalReferencesMapper(10030000);

      const animalStageList = globalReferencesMapper(10010000);
      const noOfMonthsPregnantList = cattleAdditionalInfoRef.numberOfMonthsPregnant_1.category_options

      return [
        cattleTypeList,
        cattleBreedList,
        milkingStatusList,
        subscriptionPlanList,
        preganancyRangeList,
        monthsSinceLastCalving,

        brucellosisVaccinationList,
        anthraxVaccinationStatusList,
        lastDewormingStatusList,
        dipCupsStatusList,
        lastFMDHSBQDateRangeList,
        lastFMDVaccinationDateRangeList,
        lastTheileriosisVaccinationDateRangeList,
        lastTickControlStatusList,

        insuranceStatusList,
        threeMonthsIllnessList,
        twoMonthsHealthcareList,
        anyDefectsInAnimalList,

        animalStageList,
        noOfMonthsPregnantList
      ];
}

const getFarmerName = async (navigate, farmerId,setEnableButton) => {
  try {
    // const response = await axios.get(BASE_URL + "/customer/" + farmerId, {
    //   headers: {
    //     token: localStorage.getItem("accessToken"),
    //     "Content-Type": "application/json",
    //     "X-App-Id": "123456789",
    //   },
    // });
    const response = await instance({
      url: "/customer/" + farmerId,
      method: "GET",
      // data: JSON.stringify(reqBody),
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Farmer details", response);
    if (response.status === 200) {
      if (response.data.customer_type_id === **********) {
        setEnableButton(true)
      }
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        throw new Error("Unauthorized");
      }
      if (response.data.customer_tnc_status === "Not Accepted" && response.data.customer_type_id !== **********) {
        // cattleFormDefinitionTab0.formControls.subscriptionDate.readOnly = 1
        // cattleFormDefinitionTab0.formControls.cattleSubscriptionPlan.readOnly = 1
        ToastersService.failureToast("This farmer not accepted terms and conditions")
       }
      //  else{
      //    cattleFormDefinitionTab0.formControls.subscriptionDate.readOnly = 0
      //   cattleFormDefinitionTab0.formControls.cattleSubscriptionPlan.readOnly = 0
      //  }
      console.log("Farmer Name", response.customer_name_l10n);
      return response.data.customer_name_l10n;
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const updateCattle = async (
  navigate,
  customerId,
  cattleId,
  cattleDiseases,
  formStateTab0,
  setFormStateTab0,
  formStateTab1,
  setFormStateTab1,
  formStateTab2,
  setFormStateTab2,
  formStateTab3,
  setFormStateTab3,
  formStateTab4,
  setFormStateTab4,
  updatedThumbnailImage,
  updatedCattleImages,
  documentEntries,
  setDocumentEntries,
  autoSaveValue
) => {
  console.log("AF1 sF 0, formStateTab0 = ", formStateTab0);
  console.log("AF1 sF 1, formStateTab1 = ", formStateTab1);
  console.log("AF1 sF 2, formStateTab2 = ", formStateTab2);
  console.log("AF1 sF 3, formStateTab3 = ", formStateTab3);
  const [currentFormStateTab0, validationFailedTab0] = validateFormElements(
    cattleFormDefinitionTab0,
    formStateTab0,
    setFormStateTab0
  );
  const [currentFormStateTab1, validationFailedTab1] = validateFormElements(
    cattleFormDefinitionTab1,
    formStateTab1,
    setFormStateTab1
  );
  const [currentFormStateTab2, validationFailedTab2] = validateFormElements(
    cattleFormDefinitionTab2,
    formStateTab2,
    setFormStateTab2
  );
  const [currentFormStateTab3, validationFailedTab3] = validateFormElements(
    cattleFormDefinitionTab3,
    formStateTab3,
    setFormStateTab3
  );
  // const [currentFormStateTab4, validationFailedTab4] = validateFormElements(
  //   cattleFormDefinitionTab4,
  //   formStateTab4,
  //   setFormStateTab4
  // )
  // console.log("AF1 sF 2, currentFormStateTab0 = ", currentFormStateTab0);
  if (
    validationFailedTab0 ||
    validationFailedTab1 ||
    validationFailedTab2 ||
    validationFailedTab3
    // validationFailedTab4
  ) {
    // do something
    console.log("Validation Failed");
    ToastersService.failureToast("Validation Failed!");
  } else {
    console.log("you can save now");
    const extractedValuesTab0 = extractChangedValues(
      cattleFormDefinitionTab0,
      currentFormStateTab0
    );
    const extractedValuesTab1 = extractChangedValues(
      cattleFormDefinitionTab1,
      currentFormStateTab1
    );
    const extractedValuesTab2 = extractChangedValues(
      cattleFormDefinitionTab2,
      currentFormStateTab2
    );
    const extractedValuesTab3 = extractChangedValues(
      cattleFormDefinitionTab3,
      currentFormStateTab3
    );
    // const extractedValuesTab4 = extractChangedValues(
    //   cattleFormDefinitionTab4,
    //   currentFormStateTab4
    // )
    console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValuesTab0);
    console.log("AF1 sF 2, extractedValuesTab1 = ", extractedValuesTab1);
    console.log("AF1 sF 3, extractedValuesTab2 = ", extractedValuesTab2);
    console.log("AF1 sF 4, extractedValuesTab3 = ", extractedValuesTab3);
    let extractedValues = {};
    extractedValues = { ...extractedValues, ...extractedValuesTab1 };
    console.log("AF1 sF 5, extractedValues = ", extractedValues);
    extractedValues = { ...extractedValues, ...extractedValuesTab3 };
    console.log("AF1 sF 6, extractedValues = ", extractedValues);
    extractedValues = { ...extractedValues, ...extractedValuesTab2 };
    extractedValues = { ...extractedValues, ...extractedValuesTab0 };
    console.log("AF1 sF 7, extractedValues = ", extractedValues);
    // extractedValues = { ...extractedValues, ...extractedValuesTab4 };

    console.log("tempCattleDiseases, ", cattleDiseases);

    let tempCattleDiseases = [];
    for (let disease of cattleDiseases) {
      let diseaseItem = {};
      for (let [key, value] of Object.entries(disease)) {
        diseaseItem = {...diseaseItem, [key]: value.value}
      }
      console.log(diseaseItem);
      tempCattleDiseases.push(diseaseItem);
    }
    console.log("tempCattleDiseases, ", tempCattleDiseases);
    extractedValues = { ...extractedValues, disease: tempCattleDiseases };

    configureParams(
      extractedValues,
      navigate,
      cattleId,
      updatedThumbnailImage,
      updatedCattleImages,
      documentEntries,
      setDocumentEntries
    );
  }
};

const modifySelectValueFormat = (object, singleSelectKeys, multiSelectKeys) => {
  console.log(object, singleSelectKeys, multiSelectKeys);

  for (const singleSelectKey of singleSelectKeys) {
    // const existingValue = object[singleSelectKey];
    // if (existingValue && existingValue.length > 0) {
    //   if (Array.isArray(existingValue) && existingValue.length === 1) {
    //     object[singleSelectKey] = String(existingValue[0]);
    //   } else {
    //     console.log("Causing error", object[singleSelectKey]);
    //     throw new Error("Wrong formatting");
    //   }
    // }
    const existingValue = object[singleSelectKey];
    if (existingValue && existingValue.length > 0) {
      if (existingValue[0] !== undefined) {
        object[singleSelectKey] = String(existingValue);
      } else {
        object[singleSelectKey] = null;
      }
    }
  }

  for (const multiSelectKey of multiSelectKeys) {
    const existingValue = object[multiSelectKey];
    if (existingValue && existingValue.length > 0) {
      if (Array.isArray(existingValue) && existingValue.length > 0) {
        const updatedExistingValue = existingValue;
        object[multiSelectKey] = updatedExistingValue;
      } else {
        throw new Error("Wrong formatting");
      }
    }
  }
};

const configureParams = (
  extractedValues,
  navigate,
  cattleId,
  updatedThumbnailImage,
  updatedCattleImages,
  documentEntries,
  setDocumentEntries
) => {
  console.log("Configure Params", extractedValues);

  for (let key of Object.keys(extractedValues)) {
    if (extractedValues[key] !== null) {
      if (
        typeof extractedValues[key] === "object" &&
        !Array.isArray(extractedValues[key]) &&
        !(extractedValues[key] instanceof Date)
      ) {
        extractedValues[key] = extractedValues[key].ul;
        console.log(extractedValues[key]);
      }
    }
  }

  modifySelectValueFormat(
    extractedValues,
    [
      // "animal_type",
      // "animal_breed_1",
      // "milking_status_1",
      // "subscription_plan_1",
      // "months_since_last_calving_1",
      // "pregnancy_range_of_animal_1",

      // "brucellosis_vaccination_status_1",
      // "anthrax_vaccination_status_1",
      // "last_deworming_status_1",
      // "dip_cup_status_1",
      // "last_fmd_hs_bq_date_range_1",
      // "last_fmd_vaccination_date_range_1",
      // "theileriosis_vaccination_status_1",
      // "last_tick_control_status_1",

      // "insurance_status_of_animal_1",
    ],
    ["three_month_animal_illness_1", "two_month_preventive_healthcare_1"]
  );
  console.log("AF1 sF 8, extractedValues = ", extractedValues);

  // call update cattle API
  updateCattleDetails(
    extractedValues,
    // navigate,
    cattleId,
    // updatedThumbnailImage,
    // updatedCattleImages,
    // documentEntries,
    // setDocumentEntries
  );
};

const updateCattleDetails = async (
  params,
  cattleId,
  navigate,
  updatedThumbnailImage,
  updatedCattleImages,
  documentEntries,
  setDocumentEntries
) => {
  let reqBody = {
    ...params,
    page: 2,
    animal_id: cattleId,
  };
  console.log("Update Req body", reqBody);
  try {
    const response = await instance({
      url: "/create-or-update/animal",
      method: "POST",
      data: JSON.stringify(reqBody),
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Update cattle response", response);
    if (response.status == 201) {
      // if (response.data.key) {
      //   if (
      //     updatedThumbnailImage.length > 0 ||
      //     updatedCattleImages.length > 0
      //   ) {
      //     let files = [];

      //     for (let file of updatedThumbnailImage) {
      //       if (file.file.type.split("/")[0] == "image") {
      //         file.file = await resizeFile(file.file);
      //       }
      //     }
      //     for (let file of updatedCattleImages) {
      //       if (file.file.type.split("/")[0] == "image") {
      //         file.file = await resizeFile(file.file);
      //       }
      //     }

      //     if (updatedThumbnailImage.length > 0) {
      //       files.push({
      //         fieldName: "animal_thumbnail_1",
      //         images: updatedThumbnailImage,
      //       });
      //     }
      //     if (updatedCattleImages.length > 0) {
      //       files.push({
      //         fieldName: "animal_photo_any_angle_1",
      //         images: updatedCattleImages,
      //       });
      //     }
      //     configureFiles(
      //       navigate,
      //       response.data.key,
      //       files,
      //       documentEntries,
      //       setDocumentEntries
      //     );
      //   } else {
      //     ToastersService.successToast("Details updated successfully!");
      //     // window.location.reload();
      //   }
      // }
      ToastersService.successToast("Details updated successfully!");
      window.location.reload();
    }
  } catch (error) {
    console.log(error);
    ToastersService.failureToast(error);
  }
};

const configureFiles = (
  navigate,
  cattleId,
  files,
  documentEntries,
  setDocumentEntries
) => {
  let count = 0;
  for (let file of files) {
    for (let item of file.images) {
      count = count + 1;
      console.log("Files count", count);
    }
  }

  for (let file of files) {
    for (let item of file.images) {
      const formData = new FormData();
      formData.append("file", item.file);
      formData.append("entity", "animal");
      uploadImageToServer(
        navigate,
        formData,
        file.fieldName,
        cattleId,
        item.description,
        count,
        documentEntries,
        setDocumentEntries
      );
    }
  }
};

const uploadImageToServer = async (
  navigate,
  formData,
  documentType,
  cattleId,
  imgDes,
  filesCount,
  documentEntries,
  setDocumentEntries
) => {
  try {
    // const response = await axios.post(BASE_URL + "/document", formData, {
    //   headers: {
    //     token: localStorage.getItem("accessToken"),
    //     "X-App-Id": "123456789",
    //   },
    // });
    const response = await instance({
      url: "/document",
      method: "POST",
      data: formData,
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("File upload response", response);
    if (response.status === 201) {
      ToastersService.successToast("File uploaded to server!");
      documentEntries.push({
        document_type: documentType,
        entity: "animal",
        entity_1_id: cattleId,
        url: response.data.data.doc_key,
        document_information: imgDes,
      });
      setDocumentEntries([...documentEntries]);
      console.log(documentEntries.length, filesCount);
      if (documentEntries.length === filesCount) {
        postImages(navigate, documentEntries, cattleId);
      }
    } else {
      throw Error("Image not uploaded to server!");
    }
  } catch (error) {
    console.log(error);
    // ToastersService.failureToast(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
    if (error.response.status === 413) {
      ToastersService.failureToast(error.response.data.error ? error.response.data.error : 'File size limit exceeded!');
    }
  }
};

const postImages = async (navigate, documentEntries, cattleId) => {
  try {
    // const response = await axios.post(
    //   BASE_URL + "/document",
    //   { document_entries: documentEntries },
    //   {
    //     headers: {
    //       token: localStorage.getItem("accessToken"),
    //       "X-App-Id": "123456789",
    //     },
    //   }
    // );
    const response = await instance({
      url: "/document",
      method: "POST",
      data: { document_entries: documentEntries },
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Images to db", response);
    if (response.status === 201) {
      ToastersService.successToast("File saved to db!");
      window.location.reload();
    } else {
      throw Error("Images not uploaded to db!");
    }
  } catch (error) {
    console.log(error);
    // ToastersService.failureToast(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const CattleItem1 = () => {
  const params = useParams();
  const navigate = useNavigate();
  const {Meta} = Card;
  const [updateImageState, dispatch] = useReducer(reducer, initialStateOfImageArray);

  const [farmerId, setFarmerId] = useState();
  const [enableButton,setEnableButton] = useState(false)

  const [cattleDetailsLoader, setCattleDetailsLoader] = useState(true);
  const [formStateTab0, setFormStateTab0] = useState(initialFormStateTab0);
  const [formStateTab1, setFormStateTab1] = useState(initialFormStateTab1);
  const [formStateTab2, setFormStateTab2] = useState(initialFormStateTab2);
  const [formStateTab3, setFormStateTab3] = useState(initialFormStateTab3);
  const [formStateTab4, setFormStateTab4] = useState(initialFormStateTab4)

  const [addCattleDiseaseForm, setAddCattleDiseaseForm] = useState();
  const [diseaseMedication, setDiseaseMedication] = useState([]);
  let [cattleDiseases, setCattleDiseases] = useState([]);

  // const [activityConfig, setActivityConfig] = useState();
  // const [activities, setActivities] = useState([]);

  const [cattleThumbmailImages, setCattleThumbnailImages] = useState([]);
  const [cattleImages, setCattleImages] = useState([]);
  const [cattleThumbmailImages1, setCattleThumbnailImages1] = useState([]);
  const [cattleImages1, setCattleImages1] = useState([]);

  let [updatedThumbnailImage, setUpdatedThumbnailImage] = useState([]);
  let [updatedCattleImages, setUpdatedCattleImages] = useState([]);
  let [image, setImage] = useState();
  let [imageDes, setImageDes] = useState("");
  const [documentEntries, setDocumentEntries] = useState([]);
  const [imgValue,setImgValue] = useState("")

  const [currentThumbImage, setCurrentThumbImage] = useState(0);
  const [isThumbViewerOpen, setIsThumbViewerOpen] = useState(false);
  const openThumbImageViewer = useCallback((index) => {
    setCurrentThumbImage(index);
    setIsThumbViewerOpen(true);
  }, []);
  const closeThumbImageViewer = () => {
    setCurrentThumbImage(0);
    setIsThumbViewerOpen(false);
  };

  const [currentCattleImage, setCurrentCattleImage] = useState(0);
  const [isCattleViewerOpen, setIsCattleViewerOpen] = useState(false);
  const openCattleImageViewer = useCallback((index) => {
    setCurrentCattleImage(index);
    setIsCattleViewerOpen(true);
  }, []);
  const closeCattleImageViewer = () => {
    setCurrentCattleImage(0);
    setIsCattleViewerOpen(false);
  };

  const [show, setShow] = useState(false);
  const [imageType, setImageType] = useState();
  const handleClose = () => setShow(false);
  const handleShow = (imageType) => {
    console.log(imageType);
    setImageType(imageType);
    setShow(true);
  };

  const [addCattleTabs, setAddCattleTabs] = useState([
    {
      count: 1,
      isSelected: true,
      name: "Basic Info",
    },
    {
      count: 2,
      isSelected: false,
      name: "Calvings",
    },
    {
      count: 3,
      isSelected: false,
      name: "Vaccines",
    },
    {
      count: 4,
      isSelected: false,
      name: "Diseases",
    },
    {
      count: 5,
      isSelected: false,
      name: "Images",
    },
    {
      count: 6,
      isSelected: false,
      name: 'Additional Informations'
    }
  ]);

  const getCattleDetailsById = useCallback(
    async (
      navigate,
      modFormStateTab0,
      modFormStateTab1,
      modFormStateTab2,
      modFormStateTab3,
      modFormStateTab4,
      setFormStateTab0,
      setFormStateTab1,
      setFormStateTab2,
      setFormStateTab3,
      setFormStateTab4,
      setEnableButton
    ) => {
      try {
        // setFarmerDetailsLoader(true);
        setCattleDetailsLoader(true);
        // const [
        //   cattleTypeList,
        //   cattleBreedList,
        //   milkingStatusList,
        //   subscriptionPlanList,
        //   pregnancyRangeList,
        //   monthsSinceLastCalving,

        //   brucellosisVaccinationList,
        //   anthraxVaccinationStatusList,
        //   lastDewormingStatusList,
        //   dipCupsStatusList,
        //   lastFMDHSBQDateRangeList,
        //   lastFMDVaccinationDateRangeList,
        //   lastTheileriosisVaccinationDateRangeList,
        //   lastTickControlStatusList,

        //   insuranceStatusList,
        //   threeMonthsIllnessList,
        //   twoMonthsHealthcareList,
        //   anyDefectsInAnimalList,

        //   animalStageList
        // ] = await getFormFieldConfigurationsAsLists(navigate);
        const [
          cattleTypeList,
          cattleBreedList,
          milkingStatusList,
          subscriptionPlanList,
          pregnancyRangeList,
          monthsSinceLastCalving,

          brucellosisVaccinationList,
          anthraxVaccinationStatusList,
          lastDewormingStatusList,
          dipCupsStatusList,
          lastFMDHSBQDateRangeList,
          lastFMDVaccinationDateRangeList,
          lastTheileriosisVaccinationDateRangeList,
          lastTickControlStatusList,

          insuranceStatusList,
          threeMonthsIllnessList,
          twoMonthsHealthcareList,
          anyDefectsInAnimalList,

          animalStageList
        ] = setReferenceValueFromRefData();

        // const response = await axios.get(
        //   BASE_URL + "/animal/" + params.cattleId,
        //   {
        //     headers: {
        //       token: localStorage.getItem("accessToken"),
        //       "Content-Type": "application/json",
        //       "X-App-Id": "123456789",
        //     },
        //   }
        // );
        
        const response = await instance({
          url: "/animal/" + params.cattleId,
          method: "GET",
          // data: { document_entries: documentEntries },
          headers: {
            token: localStorage.getItem("accessToken"),
          },
        });
        let modifiedFormStateTab0 = modFormStateTab0;
        console.log(response);
    
        if (response.status === 200) {
          dispatch({type: "SET_CATTLE_DATA",payload: response.data})

          if (response.data.result === false) {
            tokenFailureHandler(response.data);
            navigate("/login");
          }
          console.log("FI gFDBI 2, Cattle Details", response.data);

          setFarmerId(response.data.owner[0].entity_1_entity_uuid);

          response.data.farmerName = await getFarmerName(
            navigate,
            response.data.owner[0].entity_1_entity_uuid,setEnableButton
          );

          if (response.data.disease && response.data.disease.length > 0) {
            let diseaseConfig = await getDiseasesConfigurations();
            for (let i = 0; i < response.data.disease.length; i++) {
              response.data.disease[i].count = i + 1;
              response.data.disease[i].animal_disease_date_1 = {
                ...diseaseConfig.animal_disease_date_1,
                value: response.data.disease[i].animal_disease_date_1,
              };
              response.data.disease[i].animal_disease_type_1 = {
                ...diseaseConfig.animal_disease_type_1,
                value: response.data.disease[i].animal_disease_type_1,
              };
              response.data.disease[i].animal_disease_medication_1 = {
                ...diseaseConfig.animal_disease_medication_1,
                value: response.data.disease[i].animal_disease_medication_1
                  ? response.data.disease[i].animal_disease_medication_1
                  : [],
              };
              response.data.disease[i].animal_disease_paravet_name_1 = {
                ...diseaseConfig.animal_disease_paravet_name_1,
                value: response.data.disease[i].animal_disease_paravet_name_1,
              };
              response.data.disease[i].animal_disease_vet_name_1 = {
                ...diseaseConfig.animal_disease_vet_name_1,
                value: response.data.disease[i].animal_disease_vet_name_1,
              };
            }
            console.log("Cattle diseases", response.data.disease);
            setCattleDiseases(response.data.disease);
          }

          if (response.data.files && response.data.files.length > 0) {
            for (let item of response.data.files) {
              // item.fileType = await getFileType(item.document_id); // this function is not called as of now we're showing image directly in img src
            }
            configureCattleImages(response.data.files);
          }

          const prunedResponse = {};
          const keyList = [
            "animal_visual_id",
            // "animal_name_1",
            "animal_age_1",
            "ear_tag_1",
            "animal_weight_1",
            "number_of_months_pregnant_1",
            "number_of_calvings_1",
            "number_of_month_since_last_deworming_1",
            "number_of_month_since_last_dip_cups_replacement_1",
            "number_of_month_since_last_tick_control_1",
            "animal_average_lpd_1",

            // "age_of_animal_as_of_1",
            "healthcare_plan_subscription_date_1",
            "last_brucellosis_vaccination_date_1",
            "last_calving_date_1",
            "last_ai_date_1",
            "last_anthrax_date_1",
            "last_deworming_date_1",
            "last_dip_cups_replacement_date_1",
            "last_fmd_or_hs_or_bq_vaccination_date_1",
            "last_fmd_vaccination_date_1",
            "last_pregnancy_determination_date_1",
            "last_theileriosis_vaccination_date_1",
            "last_tick_control_date_1",

            "animal_type",
            "animal_breed_1",
            "animal_milking_1",
            "subscription_plan_1",
            "pregnancy_range_of_animal_1",
            "months_since_last_calving_1",

            "brucellosis_vaccination_status_1",
            "anthrax_vaccination_status_1",
            "last_deworming_status_1",
            "dip_cup_status_1",
            "last_fmd_hs_bq_date_range_1",
            "last_fmd_vaccination_date_range_1",
            "theileriosis_vaccination_status_1",
            "last_tick_control_status_1",

            "insurance_status_of_animal_1",
            "three_month_animal_illness_1",
            "two_month_preventive_healthcare_1",
            "any_defects_in_animal_1",
             
             "staff_name_l10n",
              "animal_stage_1",
              "animal_number_of_permanent_teeth_1",
              "animal_temperature_1",
              "months_pregnant_as_of_date",
              "animal_locomotion_score_1",
              "udder_cleft",
              "udder_depth",
              "udder_balance",
              "udder_teat_placement",
              "udder_teat_length",
              "animal_health_score",
              "animal_body_score_1",
              "animal_udder_score_1",
              "body_score_observation_tail_and_pin_bone",
              "body_score_observation_back_bone",
              "body_score_hip_and_pin_bone",
              "body_score_observation_4",
              "animal_average_lpd_1",
              "animal_foreign_body_1",
              "animal_gap_in_teeth_1",
              "animal_horns_exist_or_not_1",
              "animal_spread_of_patches_1",
              "animal_stretchability_of_udder_1",
              "animal_visbility_of_veins_1",
              "animal_additional_teats_1"

          ];
          const keyList2 = [];
          const keyList3 = [];
          const keyList4 = [];

          temporaryExtraction(response.data, prunedResponse, keyList);
          temporaryExtraction2(response.data, prunedResponse, keyList2);
          temporaryExtraction3(response.data, prunedResponse, keyList3);
          temporaryExtraction4(response.data, prunedResponse, keyList4);

          prunedResponse["animal_type_list"] = cattleTypeList;
          prunedResponse["animal_breed_1_list"] = cattleBreedList;
          prunedResponse["animal_milking_1_list"] = milkingStatusList;
          prunedResponse["subscription_plan_1_list"] = subscriptionPlanList;
          prunedResponse["pregnancy_range_of_animal_1_list"] =
            pregnancyRangeList;

          prunedResponse["months_since_last_calving_1_list"] =
            monthsSinceLastCalving;

          prunedResponse["brucellosis_vaccination_status_1_list"] =
            brucellosisVaccinationList;
          prunedResponse["anthrax_vaccination_status_1_list"] =
            anthraxVaccinationStatusList;
          prunedResponse["last_deworming_status_1_list"] =
            lastDewormingStatusList;
          prunedResponse["dip_cup_status_1_list"] = dipCupsStatusList;
          prunedResponse["last_fmd_hs_bq_date_range_1_list"] =
            lastFMDHSBQDateRangeList;
          prunedResponse["last_fmd_vaccination_date_range_1_list"] =
            lastFMDVaccinationDateRangeList;
          prunedResponse["theileriosis_vaccination_status_1_list"] =
            lastTheileriosisVaccinationDateRangeList;
          prunedResponse["last_tick_control_status_1_list"] =
            lastTickControlStatusList;

          prunedResponse["insurance_status_of_animal_1_list"] =
            insuranceStatusList;
          prunedResponse["three_month_animal_illness_1_list"] =
            threeMonthsIllnessList;
          prunedResponse["two_month_preventive_healthcare_1_list"] =
            twoMonthsHealthcareList;
          prunedResponse["any_defects_in_animal_1_list"] =
          anyDefectsInAnimalList;  

          prunedResponse["animal_stage_1_list"] = animalStageList
          prunedResponse["animal_locomotion_score_1_list"] = globalReferencesMapper(10006500)
          prunedResponse["body_score_observation_tail_and_pin_bone_list"] = globalReferencesMapper(10006600)
          prunedResponse["body_score_observation_back_bone_list"] = globalReferencesMapper(10006700)
          prunedResponse["body_score_hip_and_pin_bone_list"] = globalReferencesMapper(10006800)
          prunedResponse["body_score_observation_4_list"] = cattleAdditionalInfoRef.body_score_observation_4.category_options
          prunedResponse["udder_cleft_list"] = globalReferencesMapper(10006200)
          prunedResponse["udder_depth_list"] = globalReferencesMapper(10005900)
          prunedResponse["udder_balance_list"] = globalReferencesMapper(10006100)
          prunedResponse["udder_teat_placement_list"] = globalReferencesMapper(10006300)
          prunedResponse["udder_teat_length_list"] = globalReferencesMapper(10006400)
          prunedResponse["animal_foreign_body_1_list"] = globalReferencesMapper(10008000)
          prunedResponse["animal_gap_in_teeth_1_list"] = globalReferencesMapper(10007700)
          prunedResponse["animal_horns_exist_or_not_1_list"] = globalReferencesMapper(10007800)
          prunedResponse["animal_spread_of_patches_1_list"] = globalReferencesMapper(10005600)
          prunedResponse["animal_stretchability_of_udder_1_list"] = globalReferencesMapper(10005700)
          prunedResponse["animal_visbility_of_veins_1_list"] = globalReferencesMapper(10005800)     
          prunedResponse["animal_additional_teats_1_list"] = globalReferencesMapper(10008100)
          prunedResponse["number_of_months_pregnant_1_list"] = cattleAdditionalInfoRef.numberOfMonthsPregnant_1.category_options

 
          console.log("FI gFDBI 6, prunedResponse = ", prunedResponse);
          if (response.data['healthcare_plan_subscription_date_1']
                && response.data['healthcare_plan_subscription_date_1'] !== ''
                && response.data['healthcare_plan_subscription_date_1'] !== null) {
            modFormStateTab0['cattleSubscriptionPlan'].readOnly = 1
            modFormStateTab0['subscriptionDate'].readOnly = 1
          }else{
            modFormStateTab0['cattleSubscriptionPlan'].readOnly = 0
            modFormStateTab0['subscriptionDate'].readOnly = 0
          }

          modifiedFormStateTab0 = assignDataToFormState(
            cattleFormDefinitionTab0,
            { ...prunedResponse, farmer_name: response.data.farmerName },
            modifiedFormStateTab0,
            setFormStateTab0
          );
          const modifiedFormStateTab1 = assignDataToFormState(
            cattleFormDefinitionTab1,
           {...prunedResponse, number_of_months_pregnant_1 : [prunedResponse.number_of_months_pregnant_1]},
            modFormStateTab1,
            setFormStateTab1
          );
          const modifiedFormStateTab2 = assignDataToFormState(
            cattleFormDefinitionTab2,
            prunedResponse,
            modFormStateTab2,
            setFormStateTab2
          );
          const modifiedFormStateTab3 = assignDataToFormState(
            cattleFormDefinitionTab3,
            prunedResponse,
            modFormStateTab3,
            setFormStateTab3
          );
          const monthsPregnantAsofDateFunc = ()=>{
            const monthSec = 1000 * 60 * 60 * 24 * 30
            return  parseInt(prunedResponse.number_of_months_pregnant_1 + ((new Date() - new Date(prunedResponse.months_pregnant_as_of_date)) / monthSec))
          }
          const modifiedFormStateTab4 = assignDataToFormState(
            cattleFormDefinitionTab4,
            {
              ...prunedResponse,
              months_pregnant_as_of_date: monthsPregnantAsofDateFunc(),
              ...parseAndAssign(prunedResponse, externalMappingKeys)
            },
            modFormStateTab4,
            setFormStateTab4
          );

          setCattleDetailsLoader(false);

          return modifiedFormStateTab0;
        }
      } catch (error) {
        console.log(error);
        setCattleDetailsLoader(false);
        if (error?.response?.status === 401) {
          console.log("token expired");
          localStorage.removeItem("accessToken");
          localStorage.removeItem("resetToken");
          ToastersService.failureToast("Session Expired!");
          navigate("/login");
          window.location.reload();
        }
      }
    },
    []
  );

  const getFileType = async (docId) => {
    try {
      // const response = await axios.get(BASE_URL + "/media/" + docId, {
      //   headers: {
      //     token: localStorage.getItem("accessToken"),
      //     "Content-Type": "application/json",
      //     "X-App-Id": "123456789",
      //   },
      // });
      const response = await instance({
        url: "/media/" + docId,
        method: "GET",
        // data: { document_entries: documentEntries },
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      // console.log("Task doc response", response);
      if (response.status === 200) {
        return response.headers["content-type"];
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  const configureCattleImages = (images) => {
    for (let image of images) {
      if (image.document_type === "animal_thumbnail_1") {
        setCattleThumbnailImages1((cattleThumbmailImages) => [
          ...cattleThumbmailImages,
          BASE_URL + "/media/" + image.document_id,
        ]);
        setCattleThumbnailImages((cattleThumbmailImages) => [
          ...cattleThumbmailImages,
          image,
        ]);
      } else if (image.document_type === "animal_photo_any_angle_1") {
        setCattleImages1((cattleImages) => [
          ...cattleImages,
          BASE_URL + "/media/" + image.document_id,
        ]);
        setCattleImages((cattleImages) => [...cattleImages, image]);
      }
    }
  };

  const getDiseasesConfigurations = useCallback(async () => {
    try {
      // const response = await axios.get(BASE_URL + "/references/disease", {
      //   headers: {
      //     token: localStorage.getItem("accessToken"),
      //     "X-App-Id": "123456789",
      //   },
      // });
      const response = await instance({
        url: "/references/disease",
        method: "GET",
        // data: { document_entries: documentEntries },
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      if (response.status === 200) {
        if (response.data.result === false) {
          tokenFailureHandler(response.data);
          navigate("/login");
        }
        console.log("Disease config", response);
        // response.data.data["animal_disease_medication_1"].value = {};
        // response.data.data["animal_disease_medication_1"].value.selected = [];
        // setAddCattleDiseaseForm(response.data.data);
        return response.data.data;
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  }, []);

  // const getCattleCareCalendar = useCallback(async () => {
  //   try {
  //     // const response = await axios.get(
  //     //   BASE_URL + "/activity/care-calendar?animal_id=" + params.cattleId,
  //     //   {
  //     //     headers: {
  //     //       token: localStorage.getItem("accessToken"),
  //     //       "X-App-Id": "123456789",
  //     //     },
  //     //   }
  //     // );
  //     const response = await instance({
  //       url: "/activity/care-calendar?animal_id=" + params.cattleId,
  //       method: "GET",
  //       // data: { document_entries: documentEntries },
  //       headers: {
  //         token: localStorage.getItem("accessToken"),
  //       },
  //     });
  //     console.log(response);
  //     if (response.status === 200) {
  //       if (response.data.result === false) {
  //         tokenFailureHandler(response.data);
  //         navigate("/login");
  //       }
  //       console.log("Cattle Care Calender", response.data);

  //       if (response.data.length > 0) {
  //         // getActivityConfig();
  //         const tempActivites = [];
  //         for (let item of response.data) {
  //           tempActivites.push({
  //             calendarId: item.care_calendar_id,
  //             title: item.activity_name_l10n.ul
  //               ? item.activity_name_l10n.ul
  //               : item.activity_name_l10n.en,
  //             status: item.calendar_activity_status,
  //             entity_type_id: item.entity_type_id,
  //             start: new Date(
  //               new Date(item.activity_date).getFullYear(),
  //               new Date(item.activity_date).getMonth(),
  //               new Date(item.activity_date).getDate()
  //             ),
  //             end: new Date(
  //               new Date(item.activity_date).getFullYear(),
  //               new Date(item.activity_date).getMonth(),
  //               new Date(item.activity_date).getDate()
  //             ),
  //           });
  //         }
  //         console.log("Temp activites", tempActivites);
  //         setActivities(tempActivites);
  //       }
  //     }
  //   } catch (error) {
  //     console.log(error);
  //     if (error.response.status === 401) {
  //       console.log("token expired");
  //       localStorage.removeItem("accessToken");
  //       localStorage.removeItem("resetToken");
  //       ToastersService.failureToast("Session Expired!");
  //       navigate("/login");
  //       window.location.reload();
  //     }
  //   }
  // }, []);

  useEffect(() => {
    const initializeForm = async () => {
      getCattleDetailsById(
        navigate,
        formStateTab0,
        formStateTab1,
        formStateTab2,
        formStateTab3,
        formStateTab4,
        setFormStateTab0,
        setFormStateTab1,
        setFormStateTab2,
        setFormStateTab3,
        setFormStateTab4,
        setEnableButton
      );
    };
    initializeForm();
    // getDiseasesConfigurations();
    // getCattleCareCalendar();
  }, []);

  const onChangeTab = (tabCount) => {
    for (let item of addCattleTabs) {
      if (item.count === tabCount) {
        item.isSelected = true;
      } else {
        item.isSelected = false;
      }
    }
    setAddCattleTabs([...addCattleTabs]);
  };

  const temporaryExtraction = (incomingData, outgoingData, keyList) => {
    for (const key of keyList) {
      if (incomingData[key]) {
        outgoingData[key] = incomingData[key];
      }
    }
  };

  const temporaryExtraction2 = (incomingData, outgoingData, keyList) => {
    for (const key of keyList) {
      if (incomingData[key] && incomingData[key].value) {
        outgoingData[key] = incomingData[key].value;
      }
    }
  };

  const temporaryExtraction3 = (incomingData, outgoingData, keyList) => {
    for (const key of keyList) {
      if (incomingData[key] && incomingData[key].selected) {
        outgoingData[key] = incomingData[key].selected;
      }
    }
  };

  const temporaryExtraction4 = (incomingData, outgoingData, keyList) => {
    for (const key of keyList) {
      if (incomingData[key] && incomingData[key].value) {
        outgoingData[key] = parseFloat(incomingData[key].value);
      }
    }
  };

  const removeDiseaseItem = (diseaseCount) => {
    console.log(diseaseCount);
    cattleDiseases = cattleDiseases.filter(
      (disease) => disease.count !== diseaseCount
    );
    for (let i = 0; i < cattleDiseases.length; i++) {
      cattleDiseases[i].count = i + 1;
    }
    setCattleDiseases([...cattleDiseases]);
  };

  const animalDiseaseParavetNameHandler1 = (diseaseCount, event) => {
    for (let disease of cattleDiseases) {
      if (disease.count === diseaseCount) {
        disease.animal_disease_paravet_name_1.value = event.target.value;
      }
    }
    console.log(cattleDiseases);
  };

  const animalDiseaseVetNameHandler1 = (diseaseCount, event) => {
    for (let disease of cattleDiseases) {
      if (disease.count === diseaseCount) {
        disease.animal_disease_vet_name_1.value = event.target.value;
      }
    }
    console.log(cattleDiseases);
  };

  const animalDiseaseDateHandler1 = (diseaseCount, event) => {
    for (let disease of cattleDiseases) {
      if (disease.count === diseaseCount) {
        disease.animal_disease_date_1.value = event.target.value;
      }
    }
    console.log(cattleDiseases);
  };

  const animalDiseaseTypeHandler1 = (diseaseCount, event) => {
    for (let disease of cattleDiseases) {
      if (disease.count === diseaseCount) {
        disease.animal_disease_type_1.value = parseInt(event.target.value);
      }
    }
    console.log(cattleDiseases);
  };

  const animalDiseaseMedicationHandler1 = (diseaseCount, event) => {
    console.log(diseaseCount, event.target.value);

    const {
      target: { value },
    } = event;
    setDiseaseMedication(typeof value === "string" ? value.split(",") : value);

    for (let disease of cattleDiseases) {
      if (disease.count === diseaseCount) {
        disease.animal_disease_medication_1.value = event.target.value;
      }
    }
    console.log(cattleDiseases);
  };

  const addDiseaseHandler = async () => {
    cattleDiseases = [
      ...cattleDiseases,
      {
        ...(await getDiseasesConfigurations()),
        count: cattleDiseases.length + 1,
      },
    ];
    console.log("Cattle diseases", cattleDiseases);
    setCattleDiseases(cattleDiseases);
  };

  const imageUploadHandler = async (event) => {
    console.log(event.target.files[0]);
    if (
      event.target.files[0].type.split("/")[0] == "image" ||
      event.target.files[0].type == "application/pdf" ||
      event.target.files[0].type.split("/")[0] == "video"
    ) {
      setImage(event.target.files[0]);
    } else {
      ToastersService.failureToast("Invalid file type!");
    }
  };

  const removeImageHandler = () => {
    image = undefined;
    console.log("Image", image);
    setImage(image);
  };

  const imageDescriptionChangeHandler = (event) => {
    console.log(event.target.value);
    setImageDes(event.target.value);
  };

  const saveImageHandler = () => {
    console.log(image, imageDes, imageType);
    if (image) {
      if (imageType === "thumbnail") {
        thumbnailImageUploadHandler(image, imageDes);
      } else if (imageType === "others") {
        cattleImagesUploadHandler(image, imageDes);
      }
      setImage(undefined);
      setImageDes("");
      handleClose();
    } else {
      ToastersService.failureToast("Please add file!")
    }
  };

  const thumbnailImageUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setUpdatedThumbnailImage([...updatedThumbnailImage, fileItem]);
  };

  const removeThumbnailImageHandler = (image) => {
    // console.log(image);
    for (let item of updatedThumbnailImage) {
      if (item.file.name === image) {
        updatedThumbnailImage = updatedThumbnailImage.filter(
          (i) => i.file.name !== image
        );
      }
    }
    setUpdatedThumbnailImage([...updatedThumbnailImage]);
  };

  const cattleImagesUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setUpdatedCattleImages([...updatedCattleImages, fileItem]);
  };

  const removeCattleImagesHandler = (image) => {
    // console.log(image);
    for (let item of updatedCattleImages) {
      if (item.file.name === image) {
        updatedCattleImages = updatedCattleImages.filter(
          (i) => i.file.name !== image
        );
      }
    }
    setUpdatedCattleImages([...updatedCattleImages]);
  };

  const imageChangeHandler = (event) => {
    const newImgValue = event.target.value;
    setImgValue(newImgValue);
  };
  // antd table implemented with old way , need to be change
  const columns = [
    {
      title: "Last Updated",
      dataIndex: "lastUpdated",
      key: "lastUpdated",
      width : 100,
      
    },
    {
      title: "Value",
      dataIndex: "value",
      key: "value",
      width : 200,
    }
  ]

  // Map your data for table
  const dataSource = () => {
    return updateImageState?.cattleData?.animalHealthScoreHistory
      ?.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
      ?.map((item, index) => ({
        key: index,
        lastUpdated: formatedDate(item.created_at),
        value: item.value_double,
      }));
  };
  

  const getDropdownOptions = () => {
    const documentKeys = Object.keys(updateImageState?.cattleData)
      .filter((key) => key?.endsWith('_document'))
      .map((key) => ({
        key,
        label: key?.replace(/_/g, " ")?.replace(" document", ""),
      }));
  
    // Add "files" as an option
    documentKeys.push({ key: "files", label: "Basic Cattle Images" });
  
    return documentKeys.map(({ key, label }) => (
      <MenuItem key={key} value={key}>
        {label}
      </MenuItem>
    ));
  };
  // Update the validations of cattleSubscriptionPlan based on subscriptionDate
if (formStateTab0.subscriptionDate.value) {
  cattleFormDefinitionTab0.formControls.cattleSubscriptionPlan.validations = [{ type: "Mandatory" }]
  cattleFormDefinitionTab0.formControls.cattleSubscriptionPlan.errorMessage = "Please Select Subscription plan or remove Subscription Date."
}else{
  cattleFormDefinitionTab0.formControls.cattleSubscriptionPlan.validations = []
  cattleFormDefinitionTab0.formControls.cattleSubscriptionPlan.errorMessage = ""
}
if (formStateTab0.cattleSubscriptionPlan.value) {
  cattleFormDefinitionTab0.formControls.subscriptionDate.validations = [{ type: "Mandatory" }]
  cattleFormDefinitionTab0.formControls.subscriptionDate.errorMessage = "Please Choose the Subscription Date"
}else{
  cattleFormDefinitionTab0.formControls.subscriptionDate.validations = []
  cattleFormDefinitionTab0.formControls.subscriptionDate.errorMessage = ""
}

  return (
    <>
      <div className="mb-4">
        <p
          className="farmer-details__back-btn"
          onClick={() => navigate("/farmer/" + params.farmerId)}
        >
          <i className="fa fa-angle-double-left" aria-hidden="true"></i> Back to
          Farmer Details
        </p>

        <p
          className="mb-0 mt-3"
          style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
        >
          Cattle / Cattle Details
        </p>
      </div>

      {cattleDetailsLoader == true ? (
        <div
          style={{
            margin: "2rem 0",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Loader />
        </div>
      ) : (
        <>
          <div className="add-farmer__floating-menu">
            {addCattleTabs.map((tab) => (
              <div
                key={tab.count}
                className={
                  tab.isSelected === true
                    ? "add-farmer__tab-selected"
                    : "add-farmer__tab-unselected"
                }
                onClick={() => onChangeTab(tab.count)}
              >
                <div>{tab.name}</div>
              </div>
            ))}
          </div>

          {/* Page 1 */}
          {addCattleTabs[0].isSelected === true && (
            <>
              <KrushalOCForm
                formState={formStateTab0}
                setFormState={setFormStateTab0}
                formDefinition={cattleFormDefinitionTab0}
              />
            </>
          )}

          {/* Page 2 */}
          {addCattleTabs[1].isSelected === true && (
            <>
              <KrushalOCForm
                formState={formStateTab1}
                setFormState={setFormStateTab1}
                formDefinition={cattleFormDefinitionTab1}
              />
            </>
          )}

          {/* Page 3 */}
          {(addCattleTabs[2].isSelected === true) && (
            <>
              <KrushalOCForm
                formState={formStateTab2}
                setFormState={setFormStateTab2}
                formDefinition={cattleFormDefinitionTab2}
              />
            </>
          )}
        {addCattleTabs[5].isSelected === true && (
          <>
            <KrushalOCForm
              formState={formStateTab4}
              setFormState={setFormStateTab4}
              formDefinition={cattleFormDefinitionTab4}
            />
            <p style={{fontSize:"18px",color:"grey",fontWeight:400}}>Animal Health Score History</p>
            {
              updateImageState?.cattleData?.animalHealthScoreHistory?.length > 0 ? (
                <Table columns={columns} dataSource={dataSource()} pagination={false}/>
              ): (<p>No data...</p>)
            }
          </>
        )}
          {/* Page 4 */}
          {addCattleTabs[3].isSelected === true && (
            <>
              <KrushalOCForm
                formState={formStateTab3}
                setFormState={setFormStateTab3}
                formDefinition={cattleFormDefinitionTab3}
              />

              <hr />
              {cattleDiseases.length > 0 &&
                cattleDiseases.map((disease) => (
                  <div key={disease.count}>
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <div>
                        <p className="mb-1">Disease {disease.count}</p>
                      </div>
                      <div style={{ marginLeft: "auto" }}>
                        <i
                          className="fa fa-trash"
                          style={{ cursor: "pointer" }}
                          onClick={() => removeDiseaseItem(disease.count)}
                        ></i>
                      </div>
                    </div>
                    <Row>
                      <Col lg={4} md={6} sm={12}>
                        <small>Animal Disease Date</small>
                        <input
                          className="input-field"
                          style={{ width: "100%" }}
                          type="date"
                          defaultValue={
                            disease.animal_disease_date_1.value
                              ? disease.animal_disease_date_1.value.split(
                                  "T"
                                )[0]
                              : ""
                          }
                          onChange={(event) =>
                            animalDiseaseDateHandler1(disease.count, event)
                          }
                        />
                      </Col>
                      <Col lg={4} md={6} sm={12}>
                        <small>Animal Disease Type</small>
                        <select
                          className="input-field"
                          style={{ width: "100%" }}
                          defaultValue={
                            disease["animal_disease_type_1"].value
                              ? disease["animal_disease_type_1"].value
                              : ""
                          }
                          onChange={(event) =>
                            animalDiseaseTypeHandler1(disease.count, event)
                          }
                        >
                          <option value="" disabled>
                            Select
                          </option>
                          {disease["animal_disease_type_1"].category_options.map((option) => (
                            <option
                              key={option.reference_id}
                              value={option.reference_id}
                            >
                              {option.reference_name_l10n.en
                                ? option.reference_name_l10n.en
                                : option.reference_name_l10n.ul}
                            </option>
                          ))}
                        </select>
                        {/* {disease["animal_disease_type_1"].value ? (
                          
                        ) : (
                          <select
                            className="input-field"
                            style={{ width: "100%" }}
                            defaultValue={"DEFAULT"}
                            onChange={(event) =>
                              animalDiseaseTypeHandler1(disease.count, event)
                            }
                          >
                            <option value="DEFAULT">Select</option>
                            {disease["animal_disease_type_1"].category_options.map(
                              (option) => (
                                <option
                                  key={option.reference_id}
                                  value={option.reference_id}
                                >
                                  {option.reference_name_l10n.en ? option.reference_name_l10n.en : option.reference_name_l10n.ul}
                                </option>
                              )
                            )}
                          </select>
                        )} */}
                      </Col>
                      <Col lg={4} md={6} sm={12}>
                        <small>Animal Disease Medication</small>
                        <FormControl
                          sx={{
                            mt: 1,
                            width: "100%",
                            backgroundColor: "#fff",
                          }}
                        >
                          <InputLabel id="demo-multiple-name-label">
                            Select
                          </InputLabel>
                          <Select
                            labelId="demo-multiple-name-label"
                            id="demo-multiple-name"
                            multiple
                            defaultValue={
                              disease.animal_disease_medication_1.value
                                ? disease.animal_disease_medication_1.value
                                : []
                            }
                            onChange={(event) =>
                              animalDiseaseMedicationHandler1(
                                disease.count,
                                event
                              )
                            }
                            input={<OutlinedInput label="Name" />}
                            MenuProps={MenuProps}
                          >
                            {disease[
                              "animal_disease_medication_1"
                            ].category_options.map((option) => (
                              <MenuItem
                                key={option.reference_id}
                                value={option.reference_id}
                              >
                                {option.reference_name_l10n.en
                                  ? option.reference_name_l10n.en
                                  : option.reference_name_l10n.ul}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Col>
                      <Col lg={4} md={6} sm={12}>
                        <small>Animal Disease Vet Name</small>
                        <input
                          className="input-field"
                          style={{ width: "100%" }}
                          type="text"
                          placeholder="Animal Disease Vet Name"
                          // disabled
                          onChange={(event) =>
                            animalDiseaseVetNameHandler1(disease.count, event)
                          }
                          defaultValue={
                            disease.animal_disease_vet_name_1.value
                              ? disease.animal_disease_vet_name_1.value
                              : ""
                          }
                        />
                      </Col>
                      <Col lg={4} md={6} sm={12}>
                        <small>Animal Disease Paravet Name</small>
                        <input
                          className="input-field"
                          style={{ width: "100%" }}
                          type="text"
                          placeholder="Enter paravet name here"
                          defaultValue={
                            disease.animal_disease_paravet_name_1.value
                              ? disease.animal_disease_paravet_name_1.value
                              : ""
                          }
                          onChange={(event) =>
                            animalDiseaseParavetNameHandler1(
                              disease.count,
                              event
                            )
                          }
                        />
                      </Col>
                    </Row>
                    <hr />
                  </div>
                ))}
              <div
                className="add-cattle__add-disease-btn"
                onClick={addDiseaseHandler}
              >
                + Add disease
              </div>
            </>
          )}
          {/* Page 5 */}
          {addCattleTabs[4].isSelected === true && (
            <>
              <div style={{width:"300px"}}>
              <FormControl fullWidth>
              <InputLabel id="demo-simple-select-label">Select Cattle Image</InputLabel>
                <Select
                  labelId="demo-simple-select-label"
                  id="demo-simple-select"
                  label="Select Cattle Image"
                  value={imgValue}
                  onChange={imageChangeHandler}
                >
                 {getDropdownOptions()}
                </Select>
            </FormControl>
              </div> 
                  <div style={{display:"flex",gap:"20px",marginTop:"20px",flexWrap:"wrap"}}>
                    {updateImageState?.cattleData[imgValue]?.map((item,index)=>{
                      const isVideo = videoExtensions.some(extension => item?.uri?.endsWith(extension));
                      const fileUrl = BASE_URL + "/media/" + item.document_id
                      const fallbackUrl = "https://www.independentmediators.co.uk/wp-content/uploads/2016/02/placeholder-image.jpg";
                      return (
                        <Card
                        key={index}
                        hoverable
                        style={{ width: 200 }}
                        cover={
                          isVideo ? (
                            <Image
                              width={200}
                              preview={{
                                imageRender: () => (
                                  <video
                                    muted
                                    width="600"
                                    height="400"
                                    controls
                                    src={fileUrl}
                                  />
                                ),
                                toolbarRender: () => null,
                              }}
                              src="https://th.bing.com/th/id/OIP.zxy3gaRsf4j2zrgH92TdUAAAAA?pid=ImgDet&rs=1"
                            />
                          ) : (
                            <Image
                              style={{ height: "200px", width: "200px" }}
                              src={fileUrl}
                              fallback={fallbackUrl}
                              alt="Cattle Images"
                            />
                          )
                        }
                      >
                        <Meta title="Updated At" description={formatedDate(item.updated_at)} />
                      </Card>
                      )
                    })}
                  </div>
            </>
          )}
          {/* Page 5 */}
          {(addCattleTabs[4]?.isSelected === true && false ) && ( // disabled this code for temp
            <>
              <Row>
                <Col lg={4} md={6} sm={12}>
                  <small>Cattle Thumbnail Image (max 1)</small>

                  {cattleThumbmailImages.length > 0 ? (
                    <div>
                      <Row className="mt-3">
                        {cattleThumbmailImages.map((doc, index) => (
                          <Col lg={6} md={6} sm={12}>
                            {doc.fileType ? (
                              <>
                                {/* {console.log("DOC", doc)} */}
                                {/* Image */}
                                {doc?.fileType?.split("/")[0] == "image" && (
                                  <div>
                                    <p>Image</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                      >
                                        {/* <img
                                          src={
                                            BASE_URL +
                                            "/media/" +
                                            doc.document_id
                                          }
                                          className="new-img"
                                          onClick={() =>
                                            openThumbImageViewer(index)
                                          }
                                          alt=""
                                        /> */}
                                        <Image width="200" src={BASE_URL + "/media/" + doc.document_id}/>
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                        doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                    {isThumbViewerOpen && (
                                      <ImageViewer
                                        src={cattleThumbmailImages1}
                                        currentIndex={currentThumbImage}
                                        onClose={closeThumbImageViewer}
                                        disableScroll={false}
                                        backgroundStyle={{
                                          backgroundColor: "rgba(0,0,0,0.9)",
                                          zIndex: 10,
                                        }}
                                        closeOnClickOutside={true}
                                      />
                                    )}
                                  </div>
                                )}

                                {/* PDF */}
                                {doc?.fileType == "application/pdf" && (
                                  <div>
                                    <p>PDF</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                        onClick={() =>
                                          window.open(
                                            BASE_URL +
                                              "/media/" +
                                              doc.document_id
                                          )
                                        }
                                      >
                                        {/* <PDFViewer
                                          style={{ height: "100%" }}
                                          document={{
                                            url: `${BASE_URL}/media/${doc.document_id}`,
                                          }}
                                        /> */}
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                        doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}

                                {/* Video */}
                                {doc?.fileType?.split("/")[0] == "video" && (
                                  <div>
                                    <p>Video</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                      >
                                        <video className="new-img" controls>
                                          <source
                                            src={
                                              BASE_URL +
                                              "/media/" +
                                              doc.document_id
                                            }
                                            type="video/mp4"
                                          />
                                        </video>
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                        doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </>
                            ) : (
                              <FileNotFound />
                            )}
                          </Col>
                        ))}
                      </Row>
                    </div>
                  ) : (
                    <p className="my-2" style={{ color: "#4c4c4c" }}>
                      <i
                        className="fa fa-exclamation-triangle"
                        aria-hidden="true"
                      ></i>{" "}
                      No files found!
                    </p>
                  )}

                  <button
                    className="images-btn"
                    onClick={() => handleShow("thumbnail")}
                  >
                    Add an image
                  </button>
                  <div
                    className="my-3"
                    style={{ display: "flex", flexWrap: "wrap" }}
                  >
                    <Row>
                      {updatedThumbnailImage.map((item) => (
                        <Col
                          lg={6}
                          md={6}
                          sm={12}
                          xs={12}
                          key={item.file.lastModified}
                        >
                          <div className="add-task__img-div">
                            <div
                              className="add-farmer__remove-img-btn"
                              style={{
                                fontSize: "11px",
                                backgroundColor: "red",
                              }}
                              onClick={() =>
                                removeThumbnailImageHandler(item.file.name)
                              }
                            >
                              <i className="fa fa-trash"></i>
                            </div>
                            <div
                              style={{
                                width: "100%",
                                height: "150px",
                                display: "flex",
                                alignItems: "center",
                              }}
                            >
                              <div
                                style={{
                                  width: "150px",
                                  height: "150px",
                                  marginRight: "1rem",
                                  borderRight: "1px solid silver",
                                }}
                              >
                                {/* Image */}
                                {item.file.type.split("/")[0] == "image" && (
                                  <img
                                    src={URL.createObjectURL(item.file)}
                                    alt=""
                                    className="new-img"
                                  />
                                )}
                                {/* Document */}
                                {item.file.type == "application/pdf" && (
                                  <div className="selected-file">
                                    <div className="text-center">
                                      <div>
                                        <i
                                          className="fa fa-file"
                                          aria-hidden="true"
                                        ></i>
                                      </div>
                                      <div style={{ fontSize: "12px" }}>
                                        {item.file.name}
                                      </div>
                                    </div>
                                  </div>
                                )}
                                {/* Video */}
                                {item.file.type.split("/")[0] == "video" && (
                                  <video className="new-img" controls>
                                    <source
                                      src={URL.createObjectURL(item.file)}
                                      type="video/mp4"
                                    />
                                  </video>
                                )}
                              </div>
                              <div>
                                <small>
                                  <i className="fa fa-file"></i>{" "}
                                  {item.file.type}
                                </small>
                                <p
                                  className="mt-2 mb-0"
                                  style={{ fontSize: "14px" }}
                                >
                                  {item.description}
                                </p>
                              </div>
                            </div>
                          </div>
                        </Col>
                      ))}
                    </Row>
                  </div>
                </Col>
                <Col lg={4} md={6} sm={12}>
                  <small>Pictures of the animal (max 4)</small>

                  {cattleImages.length > 0 ? (
                    <div>
                      <Row className="mt-3">
                        {cattleImages.map((doc, index) => (
                          <Col lg={6} md={6} sm={12}>
                            {doc.fileType ? (
                              <>
                                {/* Image */}
                                {doc?.fileType?.split("/")[0] == "image" && (
                                  <div>
                                    <p>Image</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                      >
                                        <img
                                          src={
                                            BASE_URL +
                                            "/media/" +
                                            doc.document_id
                                          }
                                          className="new-img"
                                          onClick={() =>
                                            openCattleImageViewer(index)
                                          }
                                          alt=""
                                        />
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                        doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                    {isCattleViewerOpen && (
                                      <ImageViewer
                                        src={cattleImages1}
                                        currentIndex={currentCattleImage}
                                        onClose={closeCattleImageViewer}
                                        disableScroll={false}
                                        backgroundStyle={{
                                          backgroundColor: "rgba(0,0,0,0.7)",
                                          zIndex: 10,
                                        }}
                                        closeOnClickOutside={true}
                                      />
                                    )}
                                  </div>
                                )}

                                {/* PDF */}
                                {doc?.fileType == "application/pdf" && (
                                  <div>
                                    <p>PDF</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                        onClick={() =>
                                          window.open(
                                            BASE_URL +
                                              "/media/" +
                                              doc.document_id
                                          )
                                        }
                                      >
                                        {/* <PDFViewer
                                          style={{ height: "100%" }}
                                          document={{
                                            url: `${BASE_URL}/media/${doc.document_id}`,
                                          }}
                                        /> */}
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                        doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}

                                {/* Video */}
                                {doc?.fileType?.split("/")[0] == "video" && (
                                  <div>
                                    <p>Video</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                      >
                                        <video className="new-img" controls>
                                          <source
                                            src={
                                              BASE_URL +
                                              "/media/" +
                                              doc.document_id
                                            }
                                            type="video/mp4"
                                          />
                                        </video>
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                        doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </>
                            ) : (
                              <FileNotFound />
                            )}
                          </Col>
                        ))}
                      </Row>
                    </div>
                  ) : (
                    <p className="my-2" style={{ color: "#4c4c4c" }}>
                      <i
                        className="fa fa-exclamation-triangle"
                        aria-hidden="true"
                      ></i>{" "}
                      No files found!
                    </p>
                  )}

                  <button
                    className="images-btn"
                    onClick={() => handleShow("others")}
                  >
                    Add images, PDFs or videos
                  </button>
                  <Row>
                    {updatedCattleImages.map((item) => (
                      <Col
                        lg={6}
                        md={6}
                        sm={12}
                        xs={12}
                        key={item.file.lastModified}
                      >
                        <div className="add-task__img-div">
                          <div
                            className="add-farmer__remove-img-btn"
                            style={{
                              fontSize: "11px",
                              backgroundColor: "red",
                            }}
                            onClick={() =>
                              removeCattleImagesHandler(item.file.name)
                            }
                          >
                            <i className="fa fa-trash"></i>
                          </div>
                          <div
                            style={{
                              width: "100%",
                              height: "150px",
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            <div
                              style={{
                                width: "150px",
                                height: "150px",
                                marginRight: "1rem",
                                borderRight: "1px solid silver",
                              }}
                            >
                              {/* Image */}
                              {item.file.type.split("/")[0] == "image" && (
                                <img
                                  src={URL.createObjectURL(item.file)}
                                  alt=""
                                  className="new-img"
                                />
                              )}
                              {/* Document */}
                              {item.file.type == "application/pdf" && (
                                <div className="selected-file">
                                  <div className="text-center">
                                    <div>
                                      <i
                                        className="fa fa-file"
                                        aria-hidden="true"
                                      ></i>
                                    </div>
                                    <div style={{ fontSize: "12px" }}>
                                      {item.file.name}
                                    </div>
                                  </div>
                                </div>
                              )}
                              {/* Video */}
                              {item.file.type.split("/")[0] == "video" && (
                                <video className="new-img" controls>
                                  <source
                                    src={URL.createObjectURL(item.file)}
                                    type="video/mp4"
                                  />
                                </video>
                              )}
                            </div>
                            <div>
                              <small>
                                <i className="fa fa-file"></i> {item.file.type}
                              </small>
                              <p
                                className="mt-2 mb-0"
                                style={{ fontSize: "14px" }}
                              >
                                {item.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      </Col>
                    ))}
                  </Row>
                </Col>
              </Row>
            </>
          )}

          {(addCattleTabs[4].isSelected === true || addCattleTabs[5].isSelected === true ) ? (""):(
            <div style={{ display: "flex", marginLeft: "auto" }}>
            <button
              className="btn add-farmer__submit-btn mt-0 mb-5"
              disabled={updateImageState.cattleData.active === 1000100002 ? true:false || enableButton}
              onClick={async () => {
                console.log("cattlediseases", cattleDiseases);
                await updateCattle(
                  navigate,
                  -1,
                  params.cattleId,
                  cattleDiseases,
                  formStateTab0,
                  setFormStateTab0,
                  formStateTab1,
                  setFormStateTab1,
                  formStateTab2,
                  setFormStateTab2,
                  formStateTab3,
                  setFormStateTab3,
                  updatedThumbnailImage,
                  updatedCattleImages,
                  documentEntries,
                  setDocumentEntries,
                  -1
                );
              }}
            >
              Update
            </button>
          </div>
          )}

          <hr />
          <div className="mb-4">
            <div
              style={{
                display: "flex",
                alignItems: "center",
                marginBottom: "1rem",
              }}
            >
              <div>
                <p
                  className="m-0"
                  style={{
                    fontSize: "18px",
                    fontWeight: "700",
                    color: "#222222",
                  }}
                >
                  Care Calendar
                </p>
              </div>
              <button
                disabled={updateImageState.cattleData.active === 1000100002 ? true:false || enableButton}
                className="btn add-farmer__submit-btn mt-0 mb-2"
                style={{width:150}}
                onClick={() => {
                  navigate(
                    "/farmer/" +
                      params.farmerId +
                      "/cattle/" +
                      params.cattleId +
                      "/tasks/add-task"
                  );
                }}
              >
                + Add Task
              </button>
            </div>

            <div>
              <CareCalendar type="cattle" />
            </div>
          </div>

          <hr />

          <Modal show={show} onHide={handleClose} centered>
            <Modal.Header closeButton>
              <Modal.Title>Add Image</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <small>Choose Image</small>
              <input
                className="input-field"
                style={{ width: "100%" }}
                type="file"
                onChange={imageUploadHandler}
              />
              {image && (
                <>
                  {/* Image */}
                  {image.type.split("/")[0] == "image" && (
                    <div className="add-farmer__img-div">
                      <div
                        className="add-farmer__remove-img-btn"
                        onClick={() => removeImageHandler()}
                      >
                        <i className="fa fa-times"></i>
                      </div>
                      <img
                        src={URL.createObjectURL(image)}
                        alt=""
                        className="new-img"
                      />
                    </div>
                  )}
                  {/* PDF */}
                  {image.type == "application/pdf" && (
                    <div className="add-farmer__img-div">
                      <div
                        className="add-farmer__remove-img-btn"
                        onClick={() => removeImageHandler()}
                      >
                        <i className="fa fa-times"></i>
                      </div>
                      <div className="selected-file">
                        <div className="text-center">
                          <div>
                            <i className="fa fa-file" aria-hidden="true"></i>
                          </div>
                          <div style={{ fontSize: "12px" }}>{image.name}</div>
                        </div>
                      </div>
                    </div>
                  )}
                  {/* Video */}
                  {image.type.split("/")[0] == "video" && (
                    <div className="add-farmer__img-div">
                      <div
                        className="add-farmer__remove-img-btn"
                        onClick={() => removeImageHandler()}
                      >
                        <i className="fa fa-times"></i>
                      </div>
                      <div className="selected-file">
                        <div className="text-center">
                          <div>
                            <i
                              className="fa fa-file-video"
                              aria-hidden="true"
                            ></i>
                          </div>
                          <div style={{ fontSize: "12px" }}>{image.name}</div>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}
              <small>Description</small>
              <textarea
                className="input-field"
                style={{ width: "100%" }}
                rows="3"
                type="text"
                defaultValue={imageDes}
                onChange={imageDescriptionChangeHandler}
              ></textarea>
            </Modal.Body>
            <Modal.Footer>
              <Button
                variant="primary"
                onClick={saveImageHandler}
                style={{ background: "#673AB7", color: "#fff", border: "none" }}
              >
                Save
              </Button>
            </Modal.Footer>
          </Modal>
        </>
      )}
    </>
  );
};

// export default CattleItem1;
export default withAuthorization(CattleItem1, [CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM])