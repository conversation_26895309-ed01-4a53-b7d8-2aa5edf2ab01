import { useState, useCallback, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";

import "./AddCattle.css";

// import axios from "axios";
import Row from "react-bootstrap/Row";
import Col from "react-bootstrap/Col";
import Button from "react-bootstrap/Button";
import Modal from "react-bootstrap/Modal";
import resizeFile from "../../../../Services/file-resizer.service";

import OutlinedInput from "@mui/material/OutlinedInput";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";

import {
  BASE_URL,
  instance,
  tokenFailureHandler,
} from "../../../../Services/api.service";
import ToastersService from "../../../../Services/toasters.service";
import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  extractChangedValues,
} from "../../../Common/KrushalOCForm";
import Loader from "../../../Loader/Loader";
import { Modal as AntdModal } from "antd";
import { globalReferencesMapper } from "../../../../Utilities/Common/utils";

const { extractBasedOnLanguage } = require("@krushal-it/common-core");

const {CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../../user/authorize')

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
const numberOfMonthsPragnantList = [
  // { reference_id: 0, reference_information: 0, reference_name_l10n: 'N/A' },
  { reference_id: 1, reference_information: 1, reference_name_l10n: 1 },
  { reference_id: 2, reference_information: 2, reference_name_l10n: 2 },
  { reference_id: 3, reference_information: 3, reference_name_l10n: 3 },
  { reference_id: 4, reference_information: 4, reference_name_l10n: 4 },
  { reference_id: 5, reference_information: 5, reference_name_l10n: 5 },
  { reference_id: 6, reference_information: 6, reference_name_l10n: 6 },
  { reference_id: 7, reference_information: 7, reference_name_l10n: 7 },
  { reference_id: 8, reference_information: 8, reference_name_l10n: 8 },
  { reference_id: 9, reference_information: 9, reference_name_l10n: 9 },
]

const cattleFormDefinitionTab0 = {
  formControls: {
    farmerName: {
      type: "text-l10n",
      readOnly: 1,
      label: { en: "Farmer Name", mr: "शेतकऱ्याचे नाव" },
      placeHolderText: "Enter Farmer's Name",
      // validations: [{ type: "Mandatory" }],
      // errorMessage: "Name is mandatory and should not have special characters",
    },
    cattleName: {
      type: "text",
      label: { en: "Cattle name", mr: "शेतकऱ्याचे नाव" },
      placeHolderText: "Enter cattle name",
    },
    earTag: {
      type: "text",
      label: "Ear tag",
      placeHolderText: "Enter ear tag number",
      // validations: [{ type: "Mandatory" }],
      // errorMessage: "Ear tag is mandatory!",
    },
    cattleType: {
      type: "single-select-l10n",
      label: "Type*",
      errorMessage: "Cattle type is mandatory!",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select Cattle Type",
      validations: [{ type: "Mandatory" }],
    },
    cattleBreed: {
      type: "single-select-l10n",
      label: "Breed*",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select Cattle Breed",
      validations: [{ type: "Mandatory" }],
      errorMessage: "Cattle Breed is mandatory!",
    },
    ageOfCattle: {
      type: "positive-decimal",
      label: "Age",
      placeHolderText: "Enter age of cattle (in years)",
      errorMessage: "Age should be positive and numeric!",
      validations: [{ type: "Mandatory", regexArray: ["PositiveDecimal"] }],
    },
    cattleDateOfBirth: {
      type: "date",
      label: "DOB",
      placeHolderText: "Cattle DOB",
      errorMessage: "DOB is mandatory!",
      validations: [{ type: "Mandatory" }],
      // validations: [{type: 'MinimumDate', value: new Date()}, {type:'MaximumDate', value: new Date()}]
      // validations: [{type:'MaximumDate', value: moment().subtract(18, 'years').toDate()}]
      // validations: [{type: 'Mandatory'}]
    },
    cattleWeight: {
      type: "positive-decimal",
      label: "Weight (in kg)",
      placeHolderText: "Enter weight of cattle (in kgs)",
      errorMessage: "Weight should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    cattleMilkingStatus: {
      type: "single-select-l10n",
      label: "Milking status*",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select milking status",
      errorMessage: "Cattle Milking Status is Mandatory!",
      validations: [{ type: "Mandatory" }],
    },
    cattleSubscriptionPlan: {
      type: "single-select-l10n",
      label: "Cattle subscription plan",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select subscription plan",
      readOnly: 0,
      validations: [],
      errorMessage : ""
    },
    subscriptionDate: {
      type: "date",
      label: "Subscription date",
      placeHolderText: "Subscription date",
      readOnly : 0,
      // errorMessage: "DOB is mandatory!",
      // validations: [{ type: "Mandatory" }],
      // validations: [{type: 'MinimumDate', value: new Date()}, {type:'MaximumDate', value: new Date()}]
      // validations: [{type:'MaximumDate', value: moment().subtract(18, 'years').toDate()}]
      validations: [],
      errorMessage : ""
    },
    noOfCalvings: {
      type: "positive-number",
      label: "Number of calvings",
      placeHolderText: "Enter no. of calvings",
      errorMessage: "Number should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    maxLPDofCattle: {
      type: "positive-decimal",
      label: "Max LPD",
      placeHolderText: "Enter max LPD of cattle",
      errorMessage: "Number should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    animalStage: {
      type: "single-select-l10n",
      label: "Stage*",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      errorMessage: "Animal Stage is Mandatory!",
      validations: [{ type: "Mandatory" }],
    },
    animalIsPreganant: {
      type: "single-select-l10n",
      label: "Is pregnant*",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      errorMessage: "Animal Is Preganant is Mandatory!",
      validations: [{ type: "Mandatory" }],
    },
    noOfMonthsPregnant: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Number of months pregnant",
      placeHolderText: "Enter no. of months pregnant",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      errorMessage: "",
      validations: [],
    },
  },
  visibleFormControls: [
    "farmerName",
    "cattleName",
    "earTag",
    "cattleType",
    "cattleBreed",
    "ageOfCattle",
    "cattleDateOfBirth",
    "cattleWeight",
    "cattleMilkingStatus",
    "cattleSubscriptionPlan",
    "subscriptionDate",
    "noOfCalvings",
    "maxLPDofCattle",
    "animalStage",
    "animalIsPreganant",
    "noOfMonthsPregnant",
  ],
  elementToDataMapping: {
    farmerName: "farmer_name",
    cattleName: "animal_name_1",
    earTag: "ear_tag_1",
    cattleType: "animal_type",
    cattleBreed: "animal_breed_1",
    ageOfCattle: "animal_age_1",
    cattleDateOfBirth: "animal_dob_1",
    cattleWeight: "animal_weight_1",
    cattleMilkingStatus: "animal_milking_1",
    cattleSubscriptionPlan: "subscription_plan_1",
    subscriptionDate: "healthcare_plan_subscription_date_1",
    noOfCalvings: "number_of_calvings_1",
    maxLPDofCattle: "animal_average_lpd_1",
    animalStage: "animal_stage_1",
    animalIsPreganant : "animal_preganancy_1",
    noOfMonthsPregnant: "number_of_months_pregnant_1",
  },
  dataToElementMapping: {
    farmer_name: "farmerName",
    animal_name_1: "cattleName",
    ear_tag_1: "earTag",
    animal_type: "cattleType",
    animal_breed_1: "cattleBreed",
    animal_age_1: "ageOfCattle",
    animal_dob_1: "cattleDateOfBirth",
    animal_weight_1: "cattleWeight",
    animal_milking_1: "cattleMilkingStatus",
    subscription_plan_1: "cattleSubscriptionPlan",
    healthcare_plan_subscription_date_1: "subscriptionDate",
    number_of_calvings_1: "noOfCalvings",
    animal_average_lpd_1: "maxLPDofCattle",
    animal_stage_1: "animalStage",
    animal_preganancy_1 : "animalIsPreganant",
    number_of_months_pregnant_1: "noOfMonthsPregnant",
  },
};

const cattleFormDefinitionTab1 = {
  formControls: {
    noOfCalvings: {
      type: "positive-number",
      label: "No. of Calvings",
      placeHolderText: "Enter no. of calvings",
      errorMessage: "Number should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    monthsSinceLastCalving: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "No. of months since last calving",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select no. of months",
      // errorMessage: "Number should be positive and numeric!",
      // validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    lastCalvingDate: {
      type: "date",
      // readOnly: 1,
      label: "Last calving date",
      placeHolderText: "Select last calving date",
    },
    noOfMonthsPregnant: {
      type: "positive-decimal",
      // readOnly: 1,
      label: "No. of Months Pregnant",
      placeHolderText: "Enter no. of months pregnant",
      errorMessage: "Number should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    pregnancyRange: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Pregnancy range of cattle",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select preganancy range",
    },
    lastPregnancyDeterminationDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Pregnancy Determination Date",
      placeHolderText: "Select last pregnancy determination date",
    },
  },
  visibleFormControls: [
    "noOfCalvings",
    "monthsSinceLastCalving",
    "lastCalvingDate",
    "noOfMonthsPregnant",
    "pregnancyRange",
    "lastPregnancyDeterminationDate",
  ],
  elementToDataMapping: {
    noOfCalvings: "number_of_calvings_1",
    monthsSinceLastCalving: "months_since_last_calving_1",
    lastCalvingDate: "last_calving_date_1",
    noOfMonthsPregnant: "number_of_months_pregnant_1",
    pregnancyRange: "pregnancy_range_of_animal_1",
    lastPregnancyDeterminationDate: "last_pregnancy_determination_date_1",
  },
  dataToElementMapping: {
    number_of_calvings_1: "noOfCalvings",
    months_since_last_calving_1: "monthsSinceLastCalving",
    last_calving_date_1: "lastCalvingDate",
    pregnancy_range_of_animal_1: "pregnancyRange",
    number_of_months_pregnant_1: "noOfMonthsPregnant",
    last_pregnancy_determination_date_1: "lastPregnancyDeterminationDate",
  },
};

const cattleFormDefinitionTab2 = {
  formControls: {
    lastBrucellosisVaccinationDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Brucellosis Vaccination Date",
      placeHolderText: "Select Last Brucellosis Vaccination Date",
    },
    brucellosisVaccinationDoneOrNot: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Brucellosis Vaccination Status",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },
    lastAIDate: {
      type: "date",
      // readOnly: 1,
      label: "Last AI date",
      placeHolderText: "Select last AI date",
    },
    lastAnthraxDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Anthrax date",
      placeHolderText: "Select last Anthrax date",
    },
    anthraxVaccinationStatus: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Anthrax Vaccination Status",
      placeHolderText: "Select Anthrax Vaccination Status",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },
    noOfMonthsSinceDeworming: {
      type: "positive-decimal",
      // readOnly: 1,
      label: "Number of Months since Last Deworming",
      placeHolderText: "Enter no. of months since last deworming",
      errorMessage: "Number should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    lastDewormingDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Deworming date",
      placeHolderText: "Select last Deworming date",
    },
    lastDewormingStatus: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Last Deworming Status",
      placeHolderText: "Select Last Deworming Status",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },

    lastDipCupsReplacementDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Dip Cups replacement date",
      placeHolderText: "Select Last Dip cups replacement date",
    },
    dipCupsStatus: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Dip Cups Status",
      placeHolderText: "Select Dip Cups Status",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },

    lastFMDHSBQDate: {
      type: "date",
      // readOnly: 1,
      label: "Last FMD/HS/BQ Vaccination Date",
      placeHolderText: "Select last FMD/HS/BQ vaccination date",
    },
    lastFMDHSBQDateRange: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Last FMD/HS/BQ Vaccination Date Range",
      placeHolderText: "Select Last FMD/HS/BQ Vaccination Date Range",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },

    lastFMDDate: {
      type: "date",
      // readOnly: 1,
      label: "Last FMD Vaccination Date",
      placeHolderText: "Select last FMD vaccination date",
    },
    lastFMDDateRange: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Last FMD Vaccination Date Range",
      placeHolderText: "Select Last FMD Vaccination Date Range",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },

    lastThileriosisVaccinationDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Thileriosis Vaccination Date",
      placeHolderText: "Select last thileriosis vaccination date",
    },
    lastThileriosisVaccinationStatus: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Last Thileriosis Vaccination Status",
      placeHolderText: "Select Last Thileriosis Vaccination Status",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },

    lastTickControlDate: {
      type: "date",
      // readOnly: 1,
      label: "Last Tick Control Date",
      placeHolderText: "Select last tick control date",
    },
    lastTickControlStatus: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Last Tick Control Status",
      placeHolderText: "Select Last Tick Control Status",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },

    maxLPDofCattle: {
      type: "positive-decimal",
      label: "Max LPD of Cattle (not average)",
      placeHolderText: "Enter max LPD of cattle",
      errorMessage: "Number should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    // noOfMonthsSinceDipCups: {
    //   type: "positive-decimal",
    //   readOnly: 1,
    //   label: "Number of Months Since Last Dip Cups Replacement",
    //   placeHolderText: "Enter no. of months since last Dip Cups Replacement",
    //   errorMessage: "Number should be positive and numeric!",
    //   validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    // },
    // noOfMonthsSinceTickControl: {
    //   type: "positive-decimal",
    //   readOnly: 1,
    //   label: "Number of Months Since Last Tick Control",
    //   placeHolderText: "Enter no. of months since last tick control",
    //   errorMessage: "Number should be positive and numeric!",
    //   validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    // },
  },
  visibleFormControls: [
    "lastBrucellosisVaccinationDate",
    "brucellosisVaccinationDoneOrNot",

    "lastAIDate",
    "lastAnthraxDate",

    "anthraxVaccinationStatus",

    "noOfMonthsSinceDeworming",
    "lastDewormingDate",
    "lastDewormingStatus",

    "lastDipCupsReplacementDate",
    "dipCupsStatus",

    "lastFMDHSBQDate",
    "lastFMDHSBQDateRange",

    "lastFMDDate",
    "lastFMDDateRange",

    "lastThileriosisVaccinationDate",
    "lastThileriosisVaccinationStatus",

    "lastTickControlDate",
    "lastTickControlStatus",

    "maxLPDofCattle",

    // "noOfMonthsSinceDipCups",
    // "noOfMonthsSinceTickControl",
  ],
  elementToDataMapping: {
    lastBrucellosisVaccinationDate: "last_brucellosis_vaccination_date_1",
    brucellosisVaccinationDoneOrNot: "brucellosis_vaccination_status_1",

    lastAIDate: "last_ai_date_1",

    lastAnthraxDate: "last_anthrax_date_1",
    anthraxVaccinationStatus: "anthrax_vaccination_status_1",

    noOfMonthsSinceDeworming: "number_of_month_since_last_deworming_1",
    lastDewormingDate: "last_deworming_date_1",
    lastDewormingStatus: "last_deworming_status_1",

    lastDipCupsReplacementDate: "last_dip_cups_replacement_date_1",
    dipCupsStatus: "dip_cup_status_1",

    lastFMDHSBQDate: "last_fmd_or_hs_or_bq_vaccination_date_1",
    lastFMDHSBQDateRange: "last_fmd_hs_bq_date_range_1",

    lastFMDDate: "last_fmd_vaccination_date_1",
    lastFMDDateRange: "last_fmd_vaccination_date_range_1",

    lastThileriosisVaccinationDate: "last_theileriosis_vaccination_date_1",
    lastThileriosisVaccinationStatus: "theileriosis_vaccination_status_1",

    lastTickControlDate: "last_tick_control_date_1",
    lastTickControlStatus: "last_tick_control_status_1",

    maxLPDofCattle: "max_lpd_of_animal_1",

    // noOfMonthsSinceDipCups: "number_of_month_since_last_dip_cups_replacement_1",
    // noOfMonthsSinceTickControl: "number_of_month_since_last_tick_control_1",
  },
  dataToElementMapping: {
    last_brucellosis_vaccination_date_1: "lastBrucellosisVaccinationDate",
    brucellosis_vaccination_status_1: "brucellosisVaccinationDoneOrNot",

    last_ai_date_1: "lastAIDate",

    last_anthrax_date_1: "lastAnthraxDate",
    anthrax_vaccination_status_1: "anthraxVaccinationStatus",

    number_of_month_since_last_deworming_1: "noOfMonthsSinceDeworming",
    last_deworming_date_1: "lastDewormingDate",
    last_deworming_status_1: "lastDewormingStatus",

    last_dip_cups_replacement_date_1: "lastDipCupsReplacementDate",
    dip_cup_status_1: "dipCupsStatus",

    last_fmd_or_hs_or_bq_vaccination_date_1: "lastFMDHSBQDate",
    last_fmd_hs_bq_date_range_1: "lastFMDHSBQDateRange",

    last_fmd_vaccination_date_1: "lastFMDDate",
    last_fmd_vaccination_date_range_1: "lastFMDDateRange",

    last_theileriosis_vaccination_date_1: "lastThileriosisVaccinationDate",
    theileriosis_vaccination_status_1: "lastThileriosisVaccinationStatus",

    last_tick_control_date_1: "lastTickControlDate",
    last_tick_control_status_1: "lastTickControlStatus",

    max_lpd_of_animal_1: "maxLPDofCattle",
    // number_of_month_since_last_dip_cups_replacement_1: "noOfMonthsSinceDipCups",
    // number_of_month_since_last_tick_control_1: "noOfMonthsSinceTickControl",
  },
};

const cattleFormDefinitionTab3 = {
  formControls: {
    insuranceStatus: {
      type: "single-select-l10n",
      label: "Insurance Status of Cattle",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },
    threeMonthIllness: {
      type: "multi-select-l10n",
      label: "3 Month Cattle Illnesses",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },
    twoMonthHealthcare: {
      type: "multi-select-l10n",
      label: "2 Month Preventive Healthcare",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    },
    anyDefectsInAnimal: {
      type: "single-select-l10n",
      label: "Any Defects In Animal",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
    }
  },
  visibleFormControls: [
    "insuranceStatus",
    "threeMonthIllness",
    "twoMonthHealthcare",
    "anyDefectsInAnimal",
  ],
  elementToDataMapping: {
    insuranceStatus: "insurance_status_of_animal_1",
    threeMonthIllness: "three_month_animal_illness_1",
    twoMonthHealthcare: "two_month_preventive_healthcare_1",
    anyDefectsInAnimal: "any_defects_in_animal_1",
  },
  dataToElementMapping: {
    insurance_status_of_animal_1: "insuranceStatus",
    three_month_animal_illness_1: "threeMonthIllness",
    two_month_preventive_healthcare_1: "twoMonthHealthcare",
    "any_defects_in_animal_1": "anyDefectsInAnimal",
  },
};

const initialFormStateTab0 = {
  farmerName: {},
  cattleName: {},
  earTag: {},
  cattleType: {},
  cattleBreed: {},
  ageOfCattle: {},
  cattleDateOfBirth: {},
  cattleWeight: {},
  cattleMilkingStatus: {},
  cattleSubscriptionPlan: {},
  subscriptionDate: {},
  noOfCalvings: {},
  maxLPDofCattle: {},
  animalStage: {},
  animalIsPreganant : {},
  noOfMonthsPregnant: {},
};

const initialFormStateTab1 = {
  noOfCalvings: {},
  monthsSinceLastCalving: {},
  lastCalvingDate: {},
  noOfMonthsPregnant: {},
  pregnancyRange: {},
  lastPregnancyDeterminationDate: {},
};

const initialFormStateTab2 = {
  lastBrucellosisVaccinationDate: {},
  brucellosisVaccinationDoneOrNot: {},

  lastAIDate: {},

  lastAnthraxDate: {},
  anthraxVaccinationStatus: {},

  noOfMonthsSinceDeworming: {},
  lastDewormingDate: {},
  lastDewormingStatus: {},

  lastDipCupsReplacementDate: {},
  dipCupsStatus: {},

  lastFMDHSBQDate: {},
  lastFMDHSBQDateRange: {},

  lastFMDDate: {},
  lastFMDDateRange: {},

  lastThileriosisVaccinationDate: {},
  lastThileriosisVaccinationStatus: {},

  lastTickControlDate: {},
  lastTickControlStatus: {},

  maxLPDofCattle: {},
  // noOfMonthsSinceDipCups: {},
  // noOfMonthsSinceTickControl: {},
};

const initialFormStateTab3 = {
  insuranceStatus: {},
  threeMonthIllness: {},
  twoMonthHealthcare: {},
  anyDefectsInAnimal: {},
};

const getFormFieldConfigurations = async (
  navigate,
  farmerId,
  setPageLoader,
  formStateTab0,
  setFormStateTab0,
  formStateTab1,
  setFormStateTab1,
  formStateTab2,
  setFormStateTab2,
  formStateTab3,
  setFormStateTab3
) => {
  setPageLoader(true);
  try {
    // const response = await axios.get(BASE_URL + "/references/animal", {
    //   headers: {
    //     token: localStorage.getItem("accessToken"),
    //     "X-App-Id": "123456789"
    //   },
    // });
    const response = await instance({
      url: "/references/animal",
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log(response);
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        throw new Error("Unauthorized");
      }
      response.data.data.farmerName = await getFarmerName(navigate, farmerId);
      console.log("Cattle Form Config", response.data);
      const farmerDetails = await getFarmerDetails(navigate,farmerId)
      console.log(farmerDetails)
      // getFarmerName(farmerId);
      // if (farmerDetails.customer_tnc_status === "Not Accepted" && farmerDetails.customer_type_id !== 1000220007) {
      //   cattleFormDefinitionTab0.formControls.cattleSubscriptionPlan.readOnly = 1;
      //   cattleFormDefinitionTab0.formControls.subscriptionDate.readOnly = 1;
      //   AntdModal.confirm({
      //     title: `Terms and Conditions are not accepted by ${extractBasedOnLanguage(farmerDetails.customer_name_l10n)}`,
      //     content: "Do you still want to continue ?",
      //     okText: "Ok",
      //     cancelText: "Cancel",
      //     onCancel: ()=>{
      //       if (farmerDetails.customer_id) {
      //         navigate(`/dashboard/farmers/${farmerDetails.customer_id}`);
      //       } else {
      //         navigate(-1)
      //       }
      //     }
      //   })
      // }
      const newFormState0 = assignDataToFormState(
        cattleFormDefinitionTab0,
        {
          animal_type_list: response?.data?.data.animal_type?.category_options,
          animal_breed_1_list:
            response?.data?.data?.animal_breed_1?.category_options,
            animal_milking_1_list:
            response?.data?.data?.animal_milking_1?.category_options,
          farmer_name: response.data.data.farmerName,
          subscription_plan_1_list:
            response?.data?.data?.subscription_plan_1?.category_options,
            animal_stage_1_list : response?.data?.data?.animal_stage_1?.category_options,
            animal_preganancy_1_list : response?.data?.data?.animal_preganancy_1?.category_options,
            number_of_months_pregnant_1_list : numberOfMonthsPragnantList
        },
        formStateTab0,
        setFormStateTab0
      );
      const newFormState1 = assignDataToFormState(
        cattleFormDefinitionTab1,
        {
          pregnancy_range_of_animal_1_list:
            response?.data?.data?.pregnancy_range_of_animal_1?.category_options,
          months_since_last_calving_1_list:
            response?.data?.data?.months_since_last_calving_1?.category_options,
        },
        formStateTab1,
        setFormStateTab1
      );
      const newFormState2 = assignDataToFormState(
        cattleFormDefinitionTab2,
        {
          brucellosis_vaccination_status_1_list:
            response?.data?.data?.brucellosis_vaccination_status_1
              ?.category_options,
          anthrax_vaccination_status_1_list:
            response?.data?.data?.anthrax_vaccination_status_1?.category_options,
          last_deworming_status_1_list:
            response?.data?.data?.last_deworming_status_1?.category_options,
          dip_cup_status_1_list:
            response?.data?.data?.dip_cup_status_1?.category_options,
          last_fmd_hs_bq_date_range_1_list:
            response?.data?.data?.last_fmd_hs_bq_date_range_1?.category_options,
          last_fmd_vaccination_date_range_1_list:
            response?.data?.data?.last_fmd_vaccination_date_range_1
              ?.category_options,
          theileriosis_vaccination_status_1_list:
            response?.data?.data?.theileriosis_vaccination_status_1
              ?.category_options,
          last_tick_control_status_1_list:
            response?.data?.data?.last_tick_control_status_1?.category_options,
        },
        formStateTab2,
        setFormStateTab2
      );
      const newFormState3 = assignDataToFormState(
        cattleFormDefinitionTab3,
        {
          insurance_status_of_animal_1_list:
            response?.data?.data?.insurance_status_of_animal_1?.category_options,
          three_month_animal_illness_1_list:
            response?.data?.data?.three_month_animal_illness_1?.category_options,
          two_month_preventive_healthcare_1_list:
            response?.data?.data?.two_month_preventive_healthcare_1
              ?.category_options,
          any_defects_in_animal_1_list:
            response?.data?.data?.any_defects_in_animal_1
              ?.category_options,
        },
        formStateTab3,
        setFormStateTab3
      );
      setPageLoader(false);
      return [newFormState0, newFormState1, newFormState2, newFormState3];
    }
  } catch (error) {
    console.log(error);
    if (error?.response?.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const setReferenceValueFromRefData = async (
  navigate,
  farmerId,
  setPageLoader,
  formStateTab0,
  setFormStateTab0,
  formStateTab1,
  setFormStateTab1,
  formStateTab2,
  setFormStateTab2,
  formStateTab3,
  setFormStateTab3
)=>{
  // getting farmer name from farmer detail api
  setPageLoader(true);
  const farmerName = await getFarmerName(navigate, farmerId);

  // loading reference data from global references 
  const newFormState0 = assignDataToFormState( // form 1
    cattleFormDefinitionTab0,
    {
      animal_type_list: globalReferencesMapper(10001400),
      animal_breed_1_list: globalReferencesMapper(10001500),
      animal_milking_1_list: globalReferencesMapper(10001050),
      farmer_name: farmerName,
      subscription_plan_1_list: globalReferencesMapper(10002400),
        animal_stage_1_list : globalReferencesMapper(10010000),
        animal_preganancy_1_list : globalReferencesMapper(10001050),
        number_of_months_pregnant_1_list : numberOfMonthsPragnantList
    },
    formStateTab0,
    setFormStateTab0
  );
  setPageLoader(false);
    return [newFormState0]
}

const getFarmerName = async (navigate, farmerId) => {
  try {
    // const response = await axios.get(BASE_URL + "/customer/" + farmerId, {
    //   headers: {
    //     token: localStorage.getItem("accessToken"),
    //     "Content-Type": "application/json",
    //     "X-App-Id": "123456789"
    //   },
    // });
    const response = await instance({
      url: "/customer/" + farmerId,
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Farmer details", response);
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        throw new Error("Unauthorized");
      }
      console.log("Farmer Name", response.customer_name_l10n);
      return response.data.customer_name_l10n;
    }
  } catch (error) {
    console.log(error);
    if (error?.response?.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};
const getFarmerDetails = async (navigate, farmerId) => {
  try {
    const response = await instance({
      url: "/customer/" + farmerId,
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Farmer details", response);
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        throw new Error("Unauthorized");
      }
      return response.data;
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};


const saveCattle = async (
  navigate,
  customerId,
  farmerId,
  formStateTab0,
  setFormStateTab0,
  formStateTab1,
  setFormStateTab1,
  formStateTab2,
  setFormStateTab2,
  formStateTab3,
  setFormStateTab3,
  cattleDiseases,
  thumbnailImage,
  cattleImages,
  documentEntries,
  setDocumentEntries,
  autoSaveValue,
  isPPU,
  ppuFarmerId,
  setRefresh,
  setShowAddCattleModal,
  staffId
) => {
  console.log("AF1 sF 0, formStateTab0 = ", formStateTab0);
  console.log("AF1 sF 1, formStateTab1 = ", formStateTab1);
  console.log("AF1 sF 1, formStateTab2 = ", formStateTab2);
  console.log("AF1 sF 2, formStateTab3 = ", formStateTab3);

  const [currentFormStateTab0, validationFailedTab0] = validateFormElements(
    cattleFormDefinitionTab0,
    formStateTab0,
    setFormStateTab0
  );
  const [currentFormStateTab1, validationFailedTab1] = validateFormElements(
    cattleFormDefinitionTab1,
    formStateTab1,
    setFormStateTab1
  );
  const [currentFormStateTab2, validationFailedTab2] = validateFormElements(
    cattleFormDefinitionTab2,
    formStateTab2,
    setFormStateTab2
  );
  const [currentFormStateTab3, validationFailedTab3] = validateFormElements(
    cattleFormDefinitionTab3,
    formStateTab3,
    setFormStateTab3
  );

  if (
    validationFailedTab0 ||
    validationFailedTab1 ||
    validationFailedTab2 ||
    validationFailedTab3
  ) {
    // validation failed
    console.log("Validation Failed");
    ToastersService.failureToast("Validation failed!");
  } else {
    console.log("you can save now");
    const extractedValuesTab0 = extractChangedValues(
      cattleFormDefinitionTab0,
      currentFormStateTab0
    );
    const extractedValuesTab1 = extractChangedValues(
      cattleFormDefinitionTab1,
      currentFormStateTab1
    );
    const extractedValuesTab2 = extractChangedValues(
      cattleFormDefinitionTab2,
      currentFormStateTab2
    );
    const extractedValuesTab3 = extractChangedValues(
      cattleFormDefinitionTab3,
      currentFormStateTab3
    );

    let extractedValues = {};
    extractedValues = { ...extractedValues, ...extractedValuesTab0 };
    extractedValues = { ...extractedValues, ...extractedValuesTab1 };
    extractedValues = { ...extractedValues, ...extractedValuesTab2 };
    extractedValues = { ...extractedValues, ...extractedValuesTab3 };
    extractedValues = { ...extractedValues, disease: cattleDiseases };

    configureParams(
      extractedValues,
      navigate,
      farmerId,
      thumbnailImage,
      cattleImages,
      documentEntries,
      setDocumentEntries,
      isPPU,
      ppuFarmerId,
      setRefresh,
      setShowAddCattleModal,
      staffId
    );
  }
};

const modifySelectValueFormat = (object, singleSelectKeys, multiSelectKeys) => {
  console.log(object, singleSelectKeys, multiSelectKeys);

  for (const singleSelectKey of singleSelectKeys) {
    const existingValue = object[singleSelectKey];
    if (existingValue && existingValue.length > 0) {
      if (existingValue[0] !== undefined) {
        object[singleSelectKey] = String(existingValue);
      } else {
        object[singleSelectKey] = null;
      }
    }
  }
  for (const multiSelectKey of multiSelectKeys) {
    const existingValue = object[multiSelectKey];
    if (existingValue && existingValue.length > 0) {
      if (Array.isArray(existingValue) && existingValue.length > 0) {
        const updatedExistingValue = existingValue;
        object[multiSelectKey] = updatedExistingValue;
      } else {
        throw new Error("Wrong formatting");
      }
    }
  }
};

const configureParams = (
  extractedValues,
  navigate,
  farmerId,
  thumbnailImage,
  cattleImages,
  documentEntries,
  setDocumentEntries,
  isPPUU,
  ppuFarmerId,
  setRefresh,
  setShowAddCattleModal,
  staffId
) => {
  console.log("Configure Params", extractedValues);

  // let objCopy = Object.assign({}, extractedValues);

  // for (let key in objCopy) {
  //   if (objCopy[key] === "") {
  //     delete extractedValues[key];
  //   }
  // }

  for (let key of Object.keys(extractedValues)) {
    if (extractedValues[key] !== null) {
      if (
        typeof extractedValues[key] === "object" &&
        !Array.isArray(extractedValues[key]) &&
        !(extractedValues[key] instanceof Date)
      ) {
        extractedValues[key] = extractedValues[key].ul;
        console.log(extractedValues[key]);
      }
    }
  }

  modifySelectValueFormat(
    extractedValues,
    [
      // "animal_breed_1",
      // "animal_type",
      // "milking_status_1",
      // "subscription_plan_1",
      // "pregnancy_range_of_animal_1",
      // "months_since_last_calving_1",
      // "brucellosis_vaccination_status_1",
      // "anthrax_vaccination_status_1",
      // "last_deworming_status_1",
      // "dip_cup_status_1",
      // "last_fmd_hs_bq_date_range_1",
      // "last_fmd_vaccination_date_range_1",
      // "theileriosis_vaccination_status_1",
      // "last_tick_control_status_1",
      // "insurance_status_of_animal_1",
    ],
    ["three_month_animal_illness_1", "two_month_preventive_healthcare_1"]
  );
  if (
    extractedValues["animal_type"] &&
    Array.isArray(extractedValues["animal_type"]) &&
    extractedValues["animal_type"].length > 0
  ) {
    extractedValues["animal_type_for_animal_table"] =
      extractedValues["animal_type"][0];
  }

  console.log("extractedValues = ", extractedValues);
//  if (!extractedValues.ear_tag_1) {
//   extractedValues.ear_tag_1 = Math.floor(1000 + Math.random() * 9000)
//  }
  

  // call create cattle API
  createCattle(
    extractedValues,
    navigate,
    farmerId,
    thumbnailImage,
    cattleImages,
    documentEntries,
    setDocumentEntries,
    isPPUU,
    ppuFarmerId,
    setRefresh,
    setShowAddCattleModal,
    staffId
  );
};

const createCattle = async (
  params,
  navigate,
  farmerId,
  thumbnailImage,
  cattleImages,
  documentEntries,
  setDocumentEntries,
  isPPUU,
  ppuFarmerId,
  setRefresh,
  setShowAddCattleModal,
  staffId
) => {
  console.log(params);
  let reqBody = {
    ...params,
    page: 1,
    customer_id: farmerId ? farmerId : ppuFarmerId,
    paravet_id : staffId
  };
  console.log("req body", reqBody);
  try {
    const response = await instance({
      url: isPPUU ? "v2/animal":"/create-or-update/animal",
      method: "POST",
      data: JSON.stringify(reqBody),
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Create cattle response", response);
    if (response.data.key) {
      setRefresh(Math.random())
      setShowAddCattleModal(false)
      ToastersService.successToast("Cattle created successfully!");

      if (thumbnailImage.length > 0 || cattleImages.length > 0) {
        let files = [];

        for (let file of thumbnailImage) {
          if (file.file.type.split("/")[0] == "image") {
            file.file = await resizeFile(file.file);
          }
        }
        for (let file of cattleImages) {
          if (file.file.type.split("/")[0] == "image") {
            file.file = await resizeFile(file.file);
          }
        }

        if (thumbnailImage.length > 0) {
          files.push({
            fieldName: "animal_thumbnail_1",
            images: thumbnailImage,
          });
        }
        if (cattleImages.length > 0) {
          files.push({
            fieldName: "animal_photo_any_angle_1",
            images: cattleImages,
          });
        }
        configureFiles(
          navigate,
          farmerId,
          response.data.key,
          files,
          documentEntries,
          setDocumentEntries
        );
      } else {
       if (!isPPUU) {
        navigate(
          "/farmer/" + farmerId + "/cattle/" + response.data.key
        );
       }
      }
    }
  } catch (error) {
    console.log(error);
    ToastersService.failureToast("Something went wrong! Try again.");
  }
};

const configureFiles = (
  navigate,
  farmerId,
  cattleId,
  files,
  documentEntries,
  setDocumentEntries
) => {
  console.log("Post images", cattleId, files);

  let count = 0;
  for (let file of files) {
    for (let item of file.images) {
      count = count + 1;
      console.log("Files count", count);
    }
  }
  for (let file of files) {
    for (let item of file.images) {
      const formData = new FormData();
      formData.append("file", item.file);
      formData.append("entity", "animal");
      uploadImageToServer(
        navigate,
        formData,
        file.fieldName,
        farmerId,
        cattleId,
        item.description,
        count,
        documentEntries,
        setDocumentEntries
      );
    }
  }
};

const uploadImageToServer = async (
  navigate,
  formData,
  documentType,
  farmerId,
  cattleId,
  imgDes,
  filesCount,
  documentEntries,
  setDocumentEntries
) => {
  try {
    // const response = await axios.post(BASE_URL + "/document", formData, {
    //   headers: {
    //     token: localStorage.getItem("accessToken"),
    //     "X-App-Id": "123456789"
    //   },
    // });
    const response = await instance({
      url: "/document",
      method: "POST",
      data: formData,
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("File upload response", response);
    if (response.status === 201) {
      ToastersService.successToast("File uploaded to server!");
      console.log(documentType, cattleId, imgDes);
      console.log({
        document_type: documentType,
        entity: "animal",
        entity_1_id: cattleId,
        url: response.data.data.doc_key,
        document_information: imgDes,
      });
      console.log(documentEntries, filesCount);
      documentEntries.push({
        document_type: documentType,
        entity: "animal",
        entity_1_id: cattleId,
        url: response.data.data.doc_key,
        document_information: imgDes,
      });
      setDocumentEntries([...documentEntries]);
      console.log(documentEntries.length, filesCount);
      if (documentEntries.length === filesCount) {
        postImages(navigate, documentEntries, farmerId, cattleId);
      }
    } else {
      throw Error("Image not uploaded to server!");
    }
  } catch (error) {
    console.log(error);
    // ToastersService.failureToast(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
    if (error.response.status === 413) {
      ToastersService.failureToast(
        error.response.data.error
          ? error.response.data.error
          : "File size limit exceeded!"
      );
    }
  }
};

const postImages = async (navigate, documentEntries, farmerId, cattleId) => {
  try {
    // const response = await axios.post(
    //   BASE_URL + "/document",
    //   { document_entries: documentEntries },
    //   {
    //     headers: {
    //       token: localStorage.getItem("accessToken"),
    //       "X-App-Id": "123456789"
    //     },
    //   }
    // );
    const response = await instance({
      url: "/document",
      method: "POST",
      data: { document_entries: documentEntries },
      headers: {
        method: "POST",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Images to db", response);
    if (response.status === 201) {
      ToastersService.successToast("File saved to db!");
      navigate("/farmer/" + farmerId + "/cattle/" + cattleId);
    } else {
      throw Error("Images not uploaded to db!");
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
    // ToastersService.failureToast(error);
    // navigate("/dashboard/farmers/" + farmerId + "/cattle/" + cattleId);
  }
};

const AddCattle1 = (props) => {
  console.log(props.isPPUU)
  const navigate = useNavigate();
  const params = useParams();
  console.log("Params", params.farmerId);
  console.log("propsFarmerId",props.ppuFarmerId)

  const [formStateTab0, setFormStateTab0] = useState(initialFormStateTab0);
  const [formStateTab1, setFormStateTab1] = useState(initialFormStateTab1);
  const [formStateTab2, setFormStateTab2] = useState(initialFormStateTab2);
  const [formStateTab3, setFormStateTab3] = useState(initialFormStateTab3);

  // const [farmerName, setFarmerName] = useState();
  const [addCattleFormData, setAddCattleFormData] = useState();
  const [addCattleDiseaseForm, setAddCattleDiseaseForm] = useState();
  const [addCattleTabs, setAddCattleTabs] = useState([
    {
      count: 1,
      isSelected: true,
      name: "Basic Info",
    },
    {
      count: 2,
      isSelected: false,
      name: "Calvings",
    },
    {
      count: 3,
      isSelected: false,
      name: "Vaccines",
    },
    {
      count: 4,
      isSelected: false,
      name: "Diseases",
    },
    {
      count: 5,
      isSelected: false,
      name: "Images",
    },
  ]);

  const [pageLoader, setPageLoader] = useState();

  let [thumbnailImage, setThumbnailImage] = useState([]);
  let [cattleImages, setCattleImages] = useState([]);
  let [image, setImage] = useState();
  let [imageDes, setImageDes] = useState("");
  const [documentEntries, setDocumentEntries] = useState([]);

  const [diseaseMedication, setDiseaseMedication] = useState([]);
  let [cattleDiseases, setCattleDiseases] = useState([]);

  const [show, setShow] = useState(false);
  const [imageType, setImageType] = useState();
  const handleClose = () => setShow(false);
  const handleShow = (imageType) => {
    console.log(imageType);
    setImageType(imageType);
    setShow(true);
  };

  const addDiseaseHandler = () => {
    cattleDiseases = [
      ...cattleDiseases,
      {
        count: cattleDiseases.length + 1,
        // animal_disease_duration_1: "",
        animal_disease_type_1: "",
        animal_disease_date_1: "",
        animal_disease_paravet_name_1: "",
        animal_disease_vet_name_1: "",
        animal_disease_medication_1: [],
      },
    ];
    console.log("Cattle diseases", cattleDiseases);
    setCattleDiseases(cattleDiseases);
  };

  const removeDiseaseItem = (diseaseCount) => {
    console.log(diseaseCount);
    cattleDiseases = cattleDiseases.filter(
      (disease) => disease.count !== diseaseCount
    );
    for (let i = 0; i < cattleDiseases.length; i++) {
      cattleDiseases[i].count = i + 1;
    }
    console.log([...cattleDiseases]);
    setCattleDiseases([...cattleDiseases]);
  };

  const animalDiseaseDateHandler1 = (diseaseCount, event) => {
    for (let disease of cattleDiseases) {
      if (disease.count === diseaseCount) {
        disease.animal_disease_date_1 = event.target.value;
      }
    }
    console.log(cattleDiseases);
  };

  const animalDiseaseTypeHandler1 = (diseaseCount, event) => {
    for (let disease of cattleDiseases) {
      if (disease.count === diseaseCount) {
        disease.animal_disease_type_1 = event.target.value;
      }
    }
    console.log(cattleDiseases);
  };

  const animalDiseaseVetNameHandler1 = (diseaseCount, event) => {
    for (let disease of cattleDiseases) {
      if (disease.count === diseaseCount) {
        disease.animal_disease_vet_name_1 = event.target.value;
      }
    }
    console.log(cattleDiseases);
  };

  const animalDiseaseParavetNameHandler1 = (diseaseCount, event) => {
    for (let disease of cattleDiseases) {
      if (disease.count === diseaseCount) {
        disease.animal_disease_paravet_name_1 = event.target.value;
      }
    }
    console.log(cattleDiseases);
  };

  const animalDiseaseMedicationHandler1 = (diseaseCount, event) => {
    console.log(diseaseCount, event.target.value);

    const {
      target: { value },
    } = event;
    setDiseaseMedication(typeof value === "string" ? value.split(",") : value);

    for (let disease of cattleDiseases) {
      if (disease.count === diseaseCount) {
        disease.animal_disease_medication_1 = event.target.value;
      }
    }
    console.log(cattleDiseases);
  };

  const thumbnailImageUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setThumbnailImage([...thumbnailImage, fileItem]);
  };

  const removeThumbnailImageHandler = (image) => {
    for (let item of thumbnailImage) {
      if (item.file.name === image) {
        thumbnailImage = thumbnailImage.filter((i) => i.file.name !== image);
      }
    }
    setThumbnailImage([...thumbnailImage]);
  };

  const cattleImagesUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setCattleImages([...cattleImages, fileItem]);
  };

  const removeCattleImagesHandler = (image) => {
    for (let item of cattleImages) {
      if (item.file.name === image) {
        cattleImages = cattleImages.filter((i) => i.file.name !== image);
      }
    }
    setCattleImages([...cattleImages]);
  };

  const imageUploadHandler = async (event) => {
    console.log(event.target.files[0]);
    if (
      event.target.files[0].type.split("/")[0] == "image" ||
      event.target.files[0].type == "application/pdf" ||
      event.target.files[0].type.split("/")[0] == "video"
    ) {
      setImage(event.target.files[0]);
    } else {
      ToastersService.failureToast("Invalid file type!");
    }
  };

  const removeImageHandler = () => {
    image = undefined;
    console.log("Image", image);
    setImage(image);
  };

  const imageDescriptionChangeHandler = (event) => {
    console.log(event.target.value);
    setImageDes(event.target.value);
  };

  const saveImageHandler = () => {
    console.log(image, imageDes, imageType);
    if (imageType === "thumbnail") {
      thumbnailImageUploadHandler(image, imageDes);
    } else if (imageType === "others") {
      cattleImagesUploadHandler(image, imageDes);
    }
    setImage(undefined);
    setImageDes("");
    handleClose();};

  const getDiseasesConfigurations = useCallback(async () => {
    try {
      // const response = await axios.get(BASE_URL + "/references/disease", {
      //   headers: {
      //     token: localStorage.getItem("accessToken"),
      //     "X-App-Id": "123456789"
      //   },
      // });
      const response = await instance({
        url: "/references/disease",
        method: "GET",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log(response);
      if (response.status === 200) {
        if (response.data.result === false) {
          tokenFailureHandler(response.data);
          navigate("/login");
        }
        console.log("Diseases Config", response.data.data);
        setAddCattleDiseaseForm(response.data.data);
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  }, []);

  useEffect(() => {
    if (props.isPPUU) {
      cattleFormDefinitionTab0.visibleFormControls = ["earTag","cattleType","cattleBreed","animalStage","ageOfCattle","cattleWeight","cattleMilkingStatus","maxLPDofCattle","noOfCalvings","animalIsPreganant","noOfMonthsPregnant"]
    }
    const initializeForm = async () => {
      // const updatedFormStateReturnValue = await getFormFieldConfigurations(
      //   navigate,
      //   params.farmerId ? params?.farmerId : props.ppuFarmerId,
      //   setPageLoader,
      //   formStateTab0,
      //   setFormStateTab0,
      //   formStateTab1,
      //   setFormStateTab1,
      //   formStateTab2,
      //   setFormStateTab2,
      //   formStateTab3,
      //   setFormStateTab3
      // );
      const updatedFormStateReturnValue = await setReferenceValueFromRefData(
        navigate,
        params.farmerId ? params?.farmerId : props.ppuFarmerId,
        setPageLoader,
        formStateTab0,
        setFormStateTab0,
        formStateTab1,
        setFormStateTab1,
        formStateTab2,
        setFormStateTab2,
        formStateTab3,
        setFormStateTab3
      )
    };
    initializeForm();
    getDiseasesConfigurations();
    // getFarmersList();
  }, []);
  
  const onChangeTab = (tabCount) => {
    for (let item of addCattleTabs) {
      if (item.count === tabCount) {
        item.isSelected = true;
      } else {
        item.isSelected = false;
      }
    }
    setAddCattleTabs([...addCattleTabs]);
  };

const { subscriptionDate, cattleSubscriptionPlan, animalIsPreganant } = formStateTab0;
const { cattleSubscriptionPlan: planControl, subscriptionDate: dateControl, noOfMonthsPregnant } = cattleFormDefinitionTab0.formControls;

const mandatoryValidation = { type: "Mandatory" };
const planErrorMessage = "Please Select Subscription plan or remove Subscription Date.";
const dateErrorMessage = "Please Choose the Subscription Date";
const monthErrorMessage = "please choose the month";

planControl.validations = subscriptionDate.value ? [mandatoryValidation] : [];
planControl.errorMessage = subscriptionDate.value ? planErrorMessage : "";

dateControl.validations = cattleSubscriptionPlan.value ? [mandatoryValidation] : [];
dateControl.errorMessage = cattleSubscriptionPlan.value ? dateErrorMessage : "";

noOfMonthsPregnant.validations = (animalIsPreganant?.value && animalIsPreganant?.value[0] === 1000105001) ? [mandatoryValidation] : [];
noOfMonthsPregnant.errorMessage = (animalIsPreganant?.value && animalIsPreganant?.value[0] === 1000105001) ? monthErrorMessage : "";

noOfMonthsPregnant.readOnly = (animalIsPreganant?.value && animalIsPreganant?.value[0] !== 1000105001) ? 1 : 0;

useEffect(()=>{
  if (formStateTab0.animalIsPreganant?.value && formStateTab0.animalIsPreganant?.value[0] === 1000105002) {
    setFormStateTab0(prevState => ({
      ...prevState,
      noOfMonthsPregnant: { value: [] }
    }));

  }
  const newFormState0 = assignDataToFormState(
    cattleFormDefinitionTab0,
    {
        number_of_months_pregnant_1_list : numberOfMonthsPragnantList
    },
    formStateTab0,
    setFormStateTab0
  );
},[formStateTab0.animalIsPreganant?.value])

  return (
    <>
      {pageLoader == true ? (
        <div
          style={{
            margin: "2rem 0",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Loader />
        </div>
      ) : (
        <div className={props.isPPUU ? "" :"pb-5"}>
          {
            props.isPPUU === true ? (""): 
            (
              <div className="mb-4">
            <p
              className="farmer-details__back-btn m-0"
              onClick={() => {
                navigate(-1);
              }}
            >
              <i className="fa fa-angle-double-left" aria-hidden="true"></i> Go
              back
            </p>

            <p
              className="mb-0 mt-3"
              style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
            >
              Cattle / Add New Cattle
            </p>
            <p
              className="m-0"
              style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
            >
              Add Cattle
            </p>
          </div>
            )
          }

          {
            props.isPPUU === true ? (""):
            (
              <div className="add-farmer__floating-menu">
            {addCattleTabs.map((tab) => (
              <div
                key={tab.count}
                className={
                  tab.isSelected === true
                    ? "add-farmer__tab-selected"
                    : "add-farmer__tab-unselected"
                }
                onClick={() => onChangeTab(tab.count)}
              >
                <div>{tab.name}</div>
              </div>
            ))}
          </div>
            )
          }

          {/* Page 1 */}
          {addCattleTabs[0].isSelected === true && (
            <>
              <KrushalOCForm
                formState={formStateTab0}
                setFormState={setFormStateTab0}
                formDefinition={cattleFormDefinitionTab0}
              />
            </>
          )}

          {/* Page 2 */}
          {addCattleTabs[1].isSelected === true && (
            <>
              <KrushalOCForm
                formState={formStateTab1}
                setFormState={setFormStateTab1}
                formDefinition={cattleFormDefinitionTab1}
              />
            </>
          )}

          {/* Page 3 */}
          {addCattleTabs[2].isSelected === true && (
            <>
              <KrushalOCForm
                formState={formStateTab2}
                setFormState={setFormStateTab2}
                formDefinition={cattleFormDefinitionTab2}
              />
            </>
          )}

          {/* Page 4 */}
          {addCattleTabs[3].isSelected === true && (
            <>
              <KrushalOCForm
                formState={formStateTab3}
                setFormState={setFormStateTab3}
                formDefinition={cattleFormDefinitionTab3}
              />

              <hr />

              {cattleDiseases.length > 0 &&
                cattleDiseases.map((disease) => (
                  <div key={disease.count}>
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <div>
                        <p className="mb-1">Disease {disease.count}</p>
                      </div>
                      <div style={{ marginLeft: "auto" }}>
                        <i
                          className="fa fa-trash"
                          style={{ cursor: "pointer" }}
                          onClick={() => removeDiseaseItem(disease.count)}
                        ></i>
                      </div>
                    </div>
                    <Row>
                      <Col lg={4} md={6} sm={12}>
                        <small>Animal Disease Date</small>
                        <input
                          className="input-field"
                          style={{ width: "100%" }}
                          type="date"
                          onChange={(event) =>
                            animalDiseaseDateHandler1(disease.count, event)
                          }
                        />
                      </Col>
                      <Col lg={4} md={6} sm={12}>
                        <small>Animal Disease Type</small>
                        <select
                          className="input-field"
                          style={{ width: "100%" }}
                          defaultValue={"DEFAULT"}
                          onChange={(event) =>
                            animalDiseaseTypeHandler1(disease.count, event)
                          }
                        >
                          <option disabled value="DEFAULT">
                            Select
                          </option>
                          {addCattleDiseaseForm[
                            "animal_disease_type_1"
                          ].category_options.map((option) => {
                            return (
                              <option
                                key={option.reference_id}
                                value={option.reference_id}
                              >
                                {option.reference_name_l10n.en
                                  ? option.reference_name_l10n.en
                                  : option.reference_name_l10n.ul}
                              </option>
                            );
                          })}
                        </select>
                      </Col>
                      <Col lg={4} md={6} sm={12}>
                        <small>Animal Disease Medication</small>
                        <FormControl
                          sx={{
                            mt: 1,
                            width: "100%",
                            backgroundColor: "#fff",
                          }}
                        >
                          <InputLabel id="demo-multiple-name-label">
                            Select
                          </InputLabel>
                          <Select
                            labelId="demo-multiple-name-label"
                            id="demo-multiple-name"
                            multiple
                            value={disease.animal_disease_medication_1}
                            onChange={(event) =>
                              animalDiseaseMedicationHandler1(
                                disease.count,
                                event
                              )
                            }
                            input={<OutlinedInput label="Name" />}
                            MenuProps={MenuProps}
                          >
                            {addCattleDiseaseForm[
                              "animal_disease_medication_1"
                            ].category_options.map((option) => (
                              <MenuItem
                                key={option.reference_id}
                                value={option.reference_id}
                              >
                                {option.reference_name_l10n.en
                                  ? option.reference_name_l10n.en
                                  : option.reference_name_l10n.ul}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Col>
                      <Col lg={4} md={6} sm={12}>
                        <small>Animal Disease Vet Name</small>
                        <input
                          className="input-field"
                          style={{ width: "100%" }}
                          type="text"
                          placeholder="Enter paravet name here"
                          onChange={(event) =>
                            animalDiseaseVetNameHandler1(disease.count, event)
                          }
                        />
                      </Col>
                      <Col lg={4} md={6} sm={12}>
                        <small>Animal Disease Paravet Name</small>
                        <input
                          className="input-field"
                          style={{ width: "100%" }}
                          type="text"
                          placeholder="Enter paravet name here"
                          onChange={(event) =>
                            animalDiseaseParavetNameHandler1(
                              disease.count,
                              event
                            )
                          }
                        />
                      </Col>
                    </Row>
                    <hr />
                  </div>
                ))}
              <div
                className="add-cattle__add-disease-btn"
                onClick={addDiseaseHandler}
              >
                + Add disease
              </div>
            </>
          )}

          {/* Page 5 */}
          {addCattleTabs[4].isSelected === true && (
            <>
              <Row>
                <Col lg={4} md={6} sm={12}>
                  <small>Cattle Thumbnail Image (max 1)</small>
                  <button
                    className="images-btn"
                    onClick={() => handleShow("thumbnail")}
                  >
                    Add an image
                  </button>
                  <div className="my-3">
                    <Row>
                      {thumbnailImage.map((item) => (
                        <Col
                          lg={6}
                          md={6}
                          sm={12}
                          xs={12}
                          key={item.file.lastModified}
                        >
                          <div className="add-task__img-div">
                            <div
                              className="add-farmer__remove-img-btn"
                              style={{
                                fontSize: "11px",
                                backgroundColor: "red",
                              }}
                              onClick={() =>
                                removeThumbnailImageHandler(item.file.name)
                              }
                            >
                              <i className="fa fa-trash"></i>
                            </div>
                            <div
                              style={{
                                width: "100%",
                                height: "150px",
                                display: "flex",
                                alignItems: "center",
                              }}
                            >
                              <div
                                style={{
                                  width: "150px",
                                  height: "150px",
                                  marginRight: "1rem",
                                  borderRight: "1px solid silver",
                                }}
                              >
                                {/* Image */}
                                {item.file.type.split("/")[0] == "image" && (
                                  <img
                                    src={URL.createObjectURL(item.file)}
                                    alt=""
                                    className="new-img"
                                  />
                                )}
                                {/* Document */}
                                {item.file.type == "application/pdf" && (
                                  <div className="selected-file">
                                    <div className="text-center">
                                      <div>
                                        <i
                                          className="fa fa-file"
                                          aria-hidden="true"
                                        ></i>
                                      </div>
                                      <div style={{ fontSize: "12px" }}>
                                        {item.file.name}
                                      </div>
                                    </div>
                                  </div>
                                )}
                                {/* Video */}
                                {item.file.type.split("/")[0] == "video" && (
                                  <video className="new-img" controls>
                                    <source
                                      src={URL.createObjectURL(item.file)}
                                      type="video/mp4"
                                    />
                                  </video>
                                )}
                              </div>
                              <div>
                                <small>
                                  <i className="fa fa-file"></i>{" "}
                                  {item.file.type}
                                </small>
                                <p
                                  className="mt-2 mb-0"
                                  style={{ fontSize: "14px" }}
                                >
                                  {item.description}
                                </p>
                              </div>
                            </div>
                          </div>
                        </Col>
                      ))}
                    </Row>
                  </div>
                </Col>
                <Col lg={4} md={6} sm={12}>
                  <small>Pictures of the animal (max 4)</small>
                  <button
                    className="images-btn"
                    onClick={() => handleShow("others")}
                  >
                    Add Images, PDFs and Videos
                  </button>
                  <div className="my-3">
                    <Row>
                      {cattleImages.map((item) => (
                        <Col
                          lg={6}
                          md={6}
                          sm={12}
                          xs={12}
                          key={item.file.lastModified}
                        >
                          <div className="add-task__img-div">
                            <div
                              className="add-farmer__remove-img-btn"
                              style={{
                                fontSize: "11px",
                                backgroundColor: "red",
                              }}
                              onClick={() =>
                                removeCattleImagesHandler(item.file.name)
                              }
                            >
                              <i className="fa fa-trash"></i>
                            </div>
                            <div
                              style={{
                                width: "100%",
                                height: "150px",
                                display: "flex",
                                alignItems: "center",
                              }}
                            >
                              <div
                                style={{
                                  width: "150px",
                                  height: "150px",
                                  marginRight: "1rem",
                                  borderRight: "1px solid silver",
                                }}
                              >
                                {/* Image */}
                                {item.file.type.split("/")[0] == "image" && (
                                  <img
                                    src={URL.createObjectURL(item.file)}
                                    alt=""
                                    className="new-img"
                                  />
                                )}
                                {/* Document */}
                                {item.file.type == "application/pdf" && (
                                  <div className="selected-file">
                                    <div className="text-center">
                                      <div>
                                        <i
                                          className="fa fa-file"
                                          aria-hidden="true"
                                        ></i>
                                      </div>
                                      <div style={{ fontSize: "12px" }}>
                                        {item.file.name}
                                      </div>
                                    </div>
                                  </div>
                                )}
                                {/* Video */}
                                {item.file.type.split("/")[0] == "video" && (
                                  <video className="new-img" controls>
                                    <source
                                      src={URL.createObjectURL(item.file)}
                                      type="video/mp4"
                                    />
                                  </video>
                                )}
                              </div>
                              <div>
                                <small>
                                  <i className="fa fa-file"></i>{" "}
                                  {item.file.type}
                                </small>
                                <p
                                  className="mt-2 mb-0"
                                  style={{ fontSize: "14px" }}
                                >
                                  {item.description}
                                </p>
                              </div>
                            </div>
                          </div>
                        </Col>
                      ))}
                    </Row>
                  </div>
                </Col>
              </Row>
            </>
          )}

          <div style={{ display: "flex", marginLeft: "auto" }}>
            <button
              className="btn add-farmer__submit-btn mt-0 mb-5"
              onClick={async () => {
                await saveCattle(
                  navigate,
                  -1,
                  params.farmerId,
                  formStateTab0,
                  setFormStateTab0,
                  formStateTab1,
                  setFormStateTab1,
                  formStateTab2,
                  setFormStateTab2,
                  formStateTab3,
                  setFormStateTab3,
                  cattleDiseases,
                  thumbnailImage,
                  cattleImages,
                  documentEntries,
                  setDocumentEntries,
                  -1,
                  props.isPPUU,
                  props.ppuFarmerId,
                  props.setRefresh,
                  props.setShowAddCattleModal,
                  props.staffId
                );
              }}
            >
              Submit
            </button>
          </div>

          <Modal show={show} onHide={handleClose} centered>
            <Modal.Header closeButton>
              <Modal.Title>Add Image</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <small>Choose Image</small>
              <input
                className="input-field"
                style={{ width: "100%" }}
                type="file"
                onChange={imageUploadHandler}
              />
              {image && (
                <>
                  {/* Image */}
                  {image.type.split("/")[0] == "image" && (
                    <div className="add-farmer__img-div">
                      <div
                        className="add-farmer__remove-img-btn"
                        onClick={() => removeImageHandler()}
                      >
                        <i className="fa fa-times"></i>
                      </div>
                      <img
                        src={URL.createObjectURL(image)}
                        alt=""
                        className="new-img"
                      />
                    </div>
                  )}
                  {/* PDF */}
                  {image.type == "application/pdf" && (
                    <div className="add-farmer__img-div">
                      <div
                        className="add-farmer__remove-img-btn"
                        onClick={() => removeImageHandler()}
                      >
                        <i className="fa fa-times"></i>
                      </div>
                      <div className="selected-file">
                        <div className="text-center">
                          <div>
                            <i className="fa fa-file" aria-hidden="true"></i>
                          </div>
                          <div style={{ fontSize: "12px" }}>{image.name}</div>
                        </div>
                      </div>
                    </div>
                  )}
                  {/* Video */}
                  {image.type.split("/")[0] == "video" && (
                    <div className="add-farmer__img-div">
                      <div
                        className="add-farmer__remove-img-btn"
                        onClick={() => removeImageHandler()}
                      >
                        <i className="fa fa-times"></i>
                      </div>
                      <div className="selected-file">
                        <div className="text-center">
                          <div>
                            <i
                              className="fa fa-file-video"
                              aria-hidden="true"
                            ></i>
                          </div>
                          <div style={{ fontSize: "12px" }}>{image.name}</div>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}
              <small>Description</small>
              <textarea
                className="input-field"
                style={{ width: "100%" }}
                rows="3"
                type="text"
                defaultValue={imageDes}
                onChange={imageDescriptionChangeHandler}
              ></textarea>
            </Modal.Body>
            <Modal.Footer>
              <Button
                variant="primary"
                onClick={saveImageHandler}
                style={{ background: "#673AB7", color: "#fff", border: "none" }}
              >
                Save
              </Button>
            </Modal.Footer>
          </Modal>
        </div>
      )}
    </>
  );
};

// export default AddCattle1;
export default withAuthorization(AddCattle1, [CS_TEAM])