import React from "react";
import { calculateHealthScore } from "router";
import { Button } from "antd";
import ToastersService from "../../../../../Services/toasters.service";
const handleCalculateHealthScoreClick = async (userToken, animalId, setRefreshHealthScore) => {
  try {
    console.log("OCAD gAD 1");
    const headers = {
      useCase: "Recalculate Health Score for Animals",
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken"),
    };
    const response = await calculateHealthScore(headers, {
      animal_ids: [animalId],
    });
    setRefreshHealthScore(Math.random())
    return response.data;
  } catch (error) {
    console.log("OCAD gAD 10, error");
    console.log("OCAD gAD 10a, error = ", error);
    throw error;
  }
};
const CalculateHealthScore = ({userToken , animalId, setRefreshHealthScore}) => {
  return (
    <Button
      onClick={async () => {
        const response = await handleCalculateHealthScoreClick(
          userToken,
          animalId,
          setRefreshHealthScore
        );
        if (response && response.return_code === 0) {
          ToastersService.successToast("Healthscore Successfully Calculated");
        } else {
          ToastersService.failureToast("Failed to Calculate Healthscore");
        }
      }}
      style={{background : "#673ab7", color:"#fff"}}
    >
      Recalculate Health Score
    </Button>
  );
};

export default CalculateHealthScore;
