import { useState, useCallback, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";

import "./AddCDPLCattle.css";
import {
  BASE_URL,
  instance,
  tokenFailureHandler,
} from "../../../../../Services/api.service";
import ToastersService from "../../../../../Services/toasters.service";
import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  extractChangedValues,
} from "../../../../Common/KrushalOCForm";
import Loader from "../../../../Loader/Loader";
import { globalReferencesMapper } from "../../../../../Utilities/Common/utils";

const {CS_TEAM, withAuthorization} = require('../../../../user/authorize')

const numberOfMonthsPragnantList = [
  // { reference_id: 0, reference_information: 0, reference_name_l10n: 'N/A' },
  { reference_id: 1, reference_information: 1, reference_name_l10n: 1 },
  { reference_id: 2, reference_information: 2, reference_name_l10n: 2 },
  { reference_id: 3, reference_information: 3, reference_name_l10n: 3 },
  { reference_id: 4, reference_information: 4, reference_name_l10n: 4 },
  { reference_id: 5, reference_information: 5, reference_name_l10n: 5 },
  { reference_id: 6, reference_information: 6, reference_name_l10n: 6 },
  { reference_id: 7, reference_information: 7, reference_name_l10n: 7 },
  { reference_id: 8, reference_information: 8, reference_name_l10n: 8 },
  { reference_id: 9, reference_information: 9, reference_name_l10n: 9 },
]

const cattleFormDefinitionTab0 = {
  formControls: {
    farmerName: {
      type: "text-l10n",
      readOnly: 1,
      label: { en: "Farmer Name", mr: "शेतकऱ्याचे नाव" },
      placeHolderText: "Enter Farmer's Name",
      // validations: [{ type: "Mandatory" }],
      // errorMessage: "Name is mandatory and should not have special characters",
    },
    cattleName: {
      type: "text",
      label: { en: "Cattle name", mr: "शेतकऱ्याचे नाव" },
      placeHolderText: "Enter cattle name",
    },
    earTag: {
      type: "text",
      label: "Ear tag",
      placeHolderText: "Enter ear tag number",
      // validations: [{ type: "Mandatory" }],
      // errorMessage: "Ear tag is mandatory!",
    },
    cattleType: {
      type: "single-select-l10n",
      label: "Type*",
      errorMessage: "Cattle type is mandatory!",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select Cattle Type",
      validations: [{ type: "Mandatory" }],
    },
    cattleBreed: {
      type: "single-select-l10n",
      label: "Breed*",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select Cattle Breed",
      validations: [{ type: "Mandatory" }],
      errorMessage: "Cattle Breed is mandatory!",
    },
    ageOfCattle: {
      type: "positive-decimal",
      label: "Age",
      placeHolderText: "Enter age of cattle (in years)",
      errorMessage: "Age should be positive and numeric!",
      validations: [{ type: "Mandatory", regexArray: ["PositiveDecimal"] }],
    },
    cattleDateOfBirth: {
      type: "date",
      label: "DOB",
      placeHolderText: "Cattle DOB",
      errorMessage: "DOB is mandatory!",
      validations: [{ type: "Mandatory" }],
      // validations: [{type: 'MinimumDate', value: new Date()}, {type:'MaximumDate', value: new Date()}]
      // validations: [{type:'MaximumDate', value: moment().subtract(18, 'years').toDate()}]
      // validations: [{type: 'Mandatory'}]
    },
    cattleWeight: {
      type: "positive-decimal",
      label: "Weight (in kg)",
      placeHolderText: "Enter weight of cattle (in kgs)",
      errorMessage: "Weight should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    cattleMilkingStatus: {
      type: "single-select-l10n",
      label: "Milking status*",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select milking status",
      errorMessage: "Cattle Milking Status is Mandatory!",
      validations: [{ type: "Mandatory" }],
    },
    cattleSubscriptionPlan: {
      type: "single-select-l10n",
      label: "Cattle subscription plan",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select subscription plan",
      readOnly: 0,
      validations: [],
      errorMessage : ""
    },
    subscriptionDate: {
      type: "date",
      label: "Subscription date",
      placeHolderText: "Subscription date",
      readOnly : 0,
      // errorMessage: "DOB is mandatory!",
      // validations: [{ type: "Mandatory" }],
      // validations: [{type: 'MinimumDate', value: new Date()}, {type:'MaximumDate', value: new Date()}]
      // validations: [{type:'MaximumDate', value: moment().subtract(18, 'years').toDate()}]
      validations: [],
      errorMessage : ""
    },
    noOfCalvings: {
      type: "positive-number",
      label: "Number of calvings",
      placeHolderText: "Enter no. of calvings",
      errorMessage: "Number should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    maxLPDofCattle: {
      type: "positive-decimal",
      label: "Max LPD",
      placeHolderText: "Enter max LPD of cattle",
      errorMessage: "Number should be positive and numeric!",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    animalStage: {
      type: "single-select-l10n",
      label: "Stage*",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      errorMessage: "Animal Stage is Mandatory!",
      validations: [{ type: "Mandatory" }],
    },
    animalIsPreganant: {
      type: "single-select-l10n",
      label: "Is pregnant*",
      placeHolderText: "Select",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      errorMessage: "Animal Is Preganant is Mandatory!",
      validations: [{ type: "Mandatory" }],
    },
    noOfMonthsPregnant: {
      type: "single-select-l10n",
      // readOnly: 1,
      label: "Number of months pregnant",
      placeHolderText: "Enter no. of months pregnant",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      errorMessage: "",
      validations: [],
    },
  },
  visibleFormControls: [
    "farmerName",
    "earTag",
    "cattleType",
    "cattleBreed", 
    "animalStage",
    "ageOfCattle",
    "cattleWeight",
    "cattleMilkingStatus",
    // "cattleSubscriptionPlan",
    // "subscriptionDate",
    // "noOfCalvings",
    // "maxLPDofCattle",
    // "animalIsPreganant",
    // "noOfMonthsPregnant",
  ],
  elementToDataMapping: {
    farmerName: "farmer_name",
    cattleName: "animal_name_1",
    earTag: "ear_tag_1",
    cattleType: "animal_type",
    cattleBreed: "animal_breed_1",
    ageOfCattle: "animal_age_1",
    cattleDateOfBirth: "animal_dob_1",
    cattleWeight: "animal_weight_1",
    cattleMilkingStatus: "animal_milking_1",
    cattleSubscriptionPlan: "subscription_plan_1",
    subscriptionDate: "healthcare_plan_subscription_date_1",
    noOfCalvings: "number_of_calvings_1",
    maxLPDofCattle: "animal_average_lpd_1",
    animalStage: "animal_stage_1",
    animalIsPreganant : "animal_preganancy_1",
    noOfMonthsPregnant: "number_of_months_pregnant_1",
  },
  dataToElementMapping: {
    farmer_name: "farmerName",
    animal_name_1: "cattleName",
    ear_tag_1: "earTag",
    animal_type: "cattleType",
    animal_breed_1: "cattleBreed",
    animal_age_1: "ageOfCattle",
    animal_dob_1: "cattleDateOfBirth",
    animal_weight_1: "cattleWeight",
    animal_milking_1: "cattleMilkingStatus",
    subscription_plan_1: "cattleSubscriptionPlan",
    healthcare_plan_subscription_date_1: "subscriptionDate",
    number_of_calvings_1: "noOfCalvings",
    animal_average_lpd_1: "maxLPDofCattle",
    animal_stage_1: "animalStage",
    animal_preganancy_1 : "animalIsPreganant",
    number_of_months_pregnant_1: "noOfMonthsPregnant",
  },
};


const initialFormStateTab0 = {
  farmerName: {},
  cattleName: {},
  earTag: {},
  cattleType: {},
  cattleBreed: {},
  ageOfCattle: {},
  cattleDateOfBirth: {},
  cattleWeight: {},
  cattleMilkingStatus: {},
  cattleSubscriptionPlan: {},
  subscriptionDate: {},
  noOfCalvings: {},
  maxLPDofCattle: {},
  animalStage: {},
  animalIsPreganant : {},
  noOfMonthsPregnant: {},
};

const setReferenceValueFromRefData = async (
  navigate,
  farmerId,
  setPageLoader,
  formStateTab0,
  setFormStateTab0,
)=>{
  // getting farmer name from farmer detail api
  setPageLoader(true);
  const farmerName = await getFarmerName(navigate, farmerId);

  // loading reference data from global references 
  const newFormState0 = assignDataToFormState( // form 1
    cattleFormDefinitionTab0,
    {
      animal_type_list: globalReferencesMapper(10001400),
      animal_breed_1_list: globalReferencesMapper(10001500),
      animal_milking_1_list: globalReferencesMapper(10001050),
      farmer_name: farmerName,
      subscription_plan_1_list: globalReferencesMapper(10002400),
        animal_stage_1_list : globalReferencesMapper(10010000),
        animal_preganancy_1_list : globalReferencesMapper(10001050),
        number_of_months_pregnant_1_list : numberOfMonthsPragnantList
    },
    formStateTab0,
    setFormStateTab0
  );
  setPageLoader(false);
    return [newFormState0]
}

const getFarmerName = async (navigate, farmerId) => {
  try {
    // const response = await axios.get(BASE_URL + "/customer/" + farmerId, {
    //   headers: {
    //     token: localStorage.getItem("accessToken"),
    //     "Content-Type": "application/json",
    //     "X-App-Id": "123456789"
    //   },
    // });
    const response = await instance({
      url: "/customer/" + farmerId,
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Farmer details", response);
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        throw new Error("Unauthorized");
      }
      console.log("Farmer Name", response.customer_name_l10n);
      return response.data.customer_name_l10n;
    }
  } catch (error) {
    console.log(error);
    if (error?.response?.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};
const getFarmerDetails = async (navigate, farmerId) => {
  try {
    const response = await instance({
      url: "/customer/" + farmerId,
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Farmer details", response);
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        throw new Error("Unauthorized");
      }
      return response.data;
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};


const saveCattle = async (
  navigate,
  farmerId,
  formStateTab0,
  setFormStateTab0,
) => {
  console.log("AF1 sF 0, formStateTab0 = ", formStateTab0);

  const [currentFormStateTab0, validationFailedTab0] = validateFormElements(
    cattleFormDefinitionTab0,
    formStateTab0,
    setFormStateTab0
  );

  if (
    validationFailedTab0
  ) {
    // validation failed
    console.log("Validation Failed");
    ToastersService.failureToast("Validation failed!");
  } else {
    console.log("you can save now");
    const extractedValuesTab0 = extractChangedValues(
      cattleFormDefinitionTab0,
      currentFormStateTab0
    )

    let extractedValues = {};
    extractedValues = { ...extractedValues, ...extractedValuesTab0 };

    configureParams(
      extractedValues,
      navigate,
      farmerId
    );
  }
};

const modifySelectValueFormat = (object, singleSelectKeys, multiSelectKeys) => {
  console.log(object, singleSelectKeys, multiSelectKeys);

  for (const singleSelectKey of singleSelectKeys) {
    const existingValue = object[singleSelectKey];
    if (existingValue && existingValue.length > 0) {
      if (existingValue[0] !== undefined) {
        object[singleSelectKey] = String(existingValue);
      } else {
        object[singleSelectKey] = null;
      }
    }
  }
  for (const multiSelectKey of multiSelectKeys) {
    const existingValue = object[multiSelectKey];
    if (existingValue && existingValue.length > 0) {
      if (Array.isArray(existingValue) && existingValue.length > 0) {
        const updatedExistingValue = existingValue;
        object[multiSelectKey] = updatedExistingValue;
      } else {
        throw new Error("Wrong formatting");
      }
    }
  }
};

const configureParams = (
  extractedValues,
  navigate,
  farmerId
) => {
  console.log("Configure Params", extractedValues);

  for (let key of Object.keys(extractedValues)) {
    if (extractedValues[key] !== null) {
      if (
        typeof extractedValues[key] === "object" &&
        !Array.isArray(extractedValues[key]) &&
        !(extractedValues[key] instanceof Date)
      ) {
        extractedValues[key] = extractedValues[key].ul;
        console.log(extractedValues[key]);
      }
    }
  }

  modifySelectValueFormat(
    extractedValues,
    [
      // "animal_breed_1",
      // "animal_type",
      // "milking_status_1",
      // "subscription_plan_1",
      // "pregnancy_range_of_animal_1",
      // "months_since_last_calving_1",
      // "brucellosis_vaccination_status_1",
      // "anthrax_vaccination_status_1",
      // "last_deworming_status_1",
      // "dip_cup_status_1",
      // "last_fmd_hs_bq_date_range_1",
      // "last_fmd_vaccination_date_range_1",
      // "theileriosis_vaccination_status_1",
      // "last_tick_control_status_1",
      // "insurance_status_of_animal_1",
    ],
    ["three_month_animal_illness_1", "two_month_preventive_healthcare_1"]
  );
  if (
    extractedValues["animal_type"] &&
    Array.isArray(extractedValues["animal_type"]) &&
    extractedValues["animal_type"].length > 0
  ) {
    extractedValues["animal_type_for_animal_table"] =
      extractedValues["animal_type"][0];
  }

  console.log("extractedValues = ", extractedValues);
  // call create cattle API
  createCattle(
    extractedValues,
    navigate,
    farmerId,
  );
};

const createCattle = async (
  params,
  navigate,
  farmerId
) => {
  console.log(params);
  let reqBody = {
    ...params,
    page: 1,
    customer_id: farmerId
  };
  console.log("req body", reqBody);
  try {
    const response = await instance({
      url: "/create-or-update/animal",
      method: "POST",
      data: JSON.stringify(reqBody),
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Create cattle response", response);
    if (response.data.key) {

      ToastersService.successToast("Cattle created successfully!");
        navigate(
          "/farmer/" + farmerId + "/cattle/" + response.data.key
        );
    }
  } catch (error) {
    console.log(error);
    ToastersService.failureToast("Something went wrong! Try again.");
  }
};


const AddCDPLCattle = () => {
  const navigate = useNavigate();
  const params = useParams();
  console.log("Params", params.farmerId);

  const [formStateTab0, setFormStateTab0] = useState(initialFormStateTab0);
  const [pageLoader, setPageLoader] = useState();

  useEffect(() => {
    
    const initializeForm = async () => {
      const updatedFormStateReturnValue = await setReferenceValueFromRefData(
        navigate,
        params.farmerId,
        setPageLoader,
        formStateTab0,
        setFormStateTab0
      )
    };
    initializeForm();
    // getFarmersList();
  }, []);

  return (
    <>
      {pageLoader == true ? (
        <div
          style={{
            margin: "2rem 0",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Loader />
        </div>
      ) : (
        <div className="pb-5">
          { 
            (
              <div className="mb-4">
            <p
              className="farmer-details__back-btn"
              onClick={() => {
                navigate(-1);
              }}
              style={{marginTop : 10}}
            >
              <i className="fa fa-angle-double-left" aria-hidden="true"></i> Go
              back
            </p>

            <p
              className="mb-0 mt-3"
              style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
            >
              Cattle / Add New Cattle
            </p>
            <p
              className="m-0"
              style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
            >
              Add Cattle
            </p>
          </div>
            )
          }

          {/* Page 1 */}
          
            <div style={{marginTop:  10}}>
              <KrushalOCForm
                formState={formStateTab0}
                setFormState={setFormStateTab0}
                formDefinition={cattleFormDefinitionTab0}
              />
            </div>
       

          <div style={{ display: "flex", marginLeft: "auto" }}>
            <button
              className="btn add-farmer__submit-btn mt-0 mb-5"
              style={{ backgroundColor : "#ec6237", color:"#fff", width :  150}}
              onClick={async () => {
                await saveCattle(
                  navigate,
                  params.farmerId,
                  formStateTab0,
                  setFormStateTab0,
                );
              }}
            >
              Add Cattle
            </button>
          </div>
        </div>
      )}
    </>
  );
};

// export default AddCDPLCattle;
export default withAuthorization(AddCDPLCattle, [CS_TEAM])