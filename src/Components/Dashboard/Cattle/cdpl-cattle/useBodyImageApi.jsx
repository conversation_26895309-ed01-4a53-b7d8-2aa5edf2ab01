import React, { useEffect, useState } from 'react'
import { instance } from "../../../../Services/api.service"
import ToastersService from '../../../../Services/toasters.service';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { clearUserToken } from 'Components/user/action';

const useBodyImageApi = ({animal_id}) => {
    const userToken = useSelector((state)=> state.user.userToken);
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [imageData, setImgaeData] = useState()
    const getBodyImages = React.useCallback(async ()=>{
        try {
            const response = await instance({
                url : `/v2/animals/animal-classification/${animal_id}`,
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    token: userToken.accessToken,
                  },
            })
            if (response.status === 201 || response.status === 200) {
                setImgaeData(response.data)
            }
        } catch (error) {
            if ('response' in error && error.response.status === 401) {
                dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
              }
        }
    },[])
    useEffect(()=>{
        getBodyImages()
    },[animal_id])
    
  return {imageData}
}

export default useBodyImageApi
