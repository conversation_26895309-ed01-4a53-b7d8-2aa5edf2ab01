import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom';
import { instance } from 'Services/api.service';
import resizeFile from '../../../../Services/file-resizer.service';
import ToastersService from 'Services/toasters.service';

// entity_1_id = animal_classification_id
// entity = entityType
// document_type = animal_classification_documents

const configureFiles = (
    navigate,
    files,
    animal_id,
    documentEntries,
    setDocumentEntries
  ) => {
  
    let count = 0;
    for (let file of files) {
      for (let item of file.images) {
        count = count + 1;
      }
    }
  
    for (let file of files) {
      for (let item of file.images) {
        const formData = new FormData();
        formData.append("file", item.file);
        formData.append("entity", "animal_classification");
        uploadImageToServer(
          navigate,
          formData,
          animal_id,
          count,
          documentEntries,
          setDocumentEntries
        );
      }
    }
  };
  
  const uploadImageToServer = async (
    navigate,
    formData,
    animal_id,
    filesCount,
    documentEntries,
    setDocumentEntries
  ) => {
    try {
      const response = await instance({
        url: "/document",
        method: "POST",
        data: formData,
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      
      if (response.status === 201) {
        ToastersService.successToast("File uploaded to server!")

        documentEntries.push({
          document_type: "animal_classification_documents",
          entity: "animal_classification",
          entity_1_id: animal_id,
          url: response.data.data.doc_key,
        });

        setDocumentEntries([...documentEntries]);
      
        if (documentEntries.length === filesCount) {
          postImages(navigate, documentEntries);
        }
      } else {
        throw Error("Image not uploaded to server!");
      }
    } catch (error) {
      // ToastersService.failureToast(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
      if (error.response.status === 413) {
        ToastersService.failureToast(error.response.data.error ? error.response.data.error : 'File size limit exceeded!');
      }
    }
  };
  const postImages = async (navigate, documentEntries) => {
    try {
      const response = await instance({
        url: "/document",
        method: "POST",
        data: { document_entries: documentEntries },
        headers: {
          "Content-Type": "application/json",
          token: localStorage.getItem("accessToken"),
        },
      });
      if (response.status === 201) {
        ToastersService.successToast("File saved to db!");
        window.location.reload()
      } else {
        throw Error("Images not uploaded to db!");
      }
    } catch (error) {
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };
  
const useBodyImages = (images, fieldName, animal_id, animal_classification_id ) => {
    console.log(animal_id)
    const navigate = useNavigate();
    const [documentEntries, setDocumentEntries] = useState([])
    const imageUploadHandler =  async ()=>{
        let files = [];

        for (let image of images) {
            if (image.type.split("/")[0] === "image") {
              image.file = await resizeFile(image.originFileObj);
            }
          }
        
          if (images.length > 0) {
            files.push({
              fieldName: fieldName,
              images: images,
            });
          }
          configureFiles(
            navigate,
            files,
            animal_classification_id,
            documentEntries,
            setDocumentEntries
          );
    }
  return {imageUploadHandler}
}

export default useBodyImages
