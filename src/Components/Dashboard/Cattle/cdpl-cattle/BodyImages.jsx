import React, { useState } from "react";
import { Grid, Box, Button, Autocomplete, TextField, Stack } from "@mui/material";
import { Card, Image, message, Modal, Upload } from "antd";
import { PlusOutlined } from "@ant-design/icons";
// import imageData from "./data.json";
import Meta from "antd/es/card/Meta";
import { formatedDate } from "Utilities/Common/utils";
import { videoExtensions } from "Utilities/Common/utils";
import { BASE_URL } from "Services/api.service";
import useBodyImages from "./useBodyImages";
import useBodyImageApi from "./useBodyImageApi";

const getBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });

const BodyImages = ({animal_id}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [imageName, setImageName] = useState();
  const { imageData } = useBodyImageApi({animal_id})
  const showModal = () => {
    if (imageName?.animal_classification_id) {
      setIsModalOpen(true);
    }else {
      message.warning("Select any body images")
    }
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const [fileList, setFileList] = useState([]);

  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewOpen(true);
  };

  const handleChange = ({ fileList: newFileList }) => setFileList(newFileList);

  const { imageUploadHandler } = useBodyImages(fileList, imageName?.label, animal_id, imageName?.animal_classification_id )

  const handleOk = async () => {
    setIsModalOpen(false);
   const data = await imageUploadHandler()
   console.log(data)
  };

  return (
    <Box>
      <Grid container spacing={2} alignItems="center">
        <Grid item md={3}>
          <Autocomplete
            disablePortal
            id="combo-box-demo"

            options={imageData ? Object.keys(imageData).filter((item) => imageData[item].animal_classification_id !== null).map((key) => ({
              label: imageData[key]?.classifier_name,
              value: key,
              animal_classification_id : imageData[key]?.animal_classification_id
            })) : []}

            sx={{ width: 300 }}
            onChange={(e, option) => setImageName(option)}
            renderInput={(params) => (
              <TextField {...params} label="Body images" />
            )}
          />
        </Grid>
        <Grid item md={7} alignItems="center" justifyContent="center">
          <div
            style={{
              display: "flex",
              gap: "20px",
              marginTop: "20px",
              flexWrap: "wrap",
              maxHeight: 300,
              overflowY: "scroll",
            }}
          >
            {imageData && imageData[imageName?.value]?.documents?.map((item, index) => {
              const isVideo = videoExtensions.some((extension) =>
                item?.uri?.endsWith(extension)
              );
              const fileUrl = BASE_URL + "/media/" + item.document_id;
              const fallbackUrl =
                "https://www.independentmediators.co.uk/wp-content/uploads/2016/02/placeholder-image.jpg";
              return (
                <Card
                  key={index}
                  hoverable
                  style={{ width: 200 }}
                  cover={
                    isVideo ? (
                      <Image
                        width={200}
                        preview={{
                          imageRender: () => (
                            <video
                              muted
                              width="600"
                              height="400"
                              controls
                              src={fileUrl}
                            />
                          ),
                          toolbarRender: () => null,
                        }}
                        src="https://th.bing.com/th/id/OIP.zxy3gaRsf4j2zrgH92TdUAAAAA?pid=ImgDet&rs=1"
                      />
                    ) : (
                      <Image
                        style={{ height: "200px", width: "200px" }}
                        src={fileUrl}
                        fallback={fallbackUrl}
                        alt="Cattle Images"
                      />
                    )
                  }
                >
                  <Meta
                    title="Updated At"
                    description={formatedDate(item.updated_at)}
                  />
                </Card>
              );
            })}
          </div>
        </Grid>
      </Grid>
      <Stack direction="row" justifyContent="flex-end">
          <Button
            variant="contained"
            sx={{ textTransform: "none" }}
            onClick={showModal}
          >
            Upload image
          </Button>
        </Stack>
      <Modal
        title={`Select the ${imageName?.label} images`}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Upload
          customRequest={({ onSuccess }) =>
            setTimeout(() => {
              onSuccess("ok", null);
            }, 0)
          }
          listType="picture-card"
          fileList={fileList}
          onPreview={handlePreview}
          onChange={handleChange}
        >
          {uploadButton}
        </Upload>
        {previewImage && (
          <Image
            wrapperStyle={{
              display: "none",
            }}
            preview={{
              visible: previewOpen,
              onVisibleChange: (visible) => setPreviewOpen(visible),
              afterOpenChange: (visible) => !visible && setPreviewImage(""),
            }}
            src={previewImage}
          />
        )}
      </Modal>
    </Box>
  );
};

export default BodyImages;

const uploadButton = (
  <button
    style={{
      border: 0,
      background: "none",
    }}
    type="button"
  >
    <PlusOutlined />
    <div
      style={{
        marginTop: 8,
      }}
    >
      Select
    </div>
  </button>
);
