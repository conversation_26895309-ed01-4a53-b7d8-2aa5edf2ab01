import { useEffect, useCallback, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Container, Row, Col } from "react-bootstrap";
import { useSelector, useDispatch } from "react-redux";
import "./Profile.css";
import axios from "axios";
import { BASE_URL, instance } from "../../../Services/api.service";
import { tokenFailureHandler } from "../../../Services/api.service";
import Loader from "../../Loader/Loader";
import ToastersService from "../../../Services/toasters.service";
import resizeFile from "../../../Services/file-resizer.service";
import { clearUserToken, getUserProfileData } from "../../user/action";
import packageJson from "../../../../package.json";

const Profile = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const inputRef = useRef(null);
  const userData = useSelector((state) => state.user.userData);
  const userToken = useSelector((state) => state.user.userToken);

  const changeProfilePic = async (reqBody) => {
    try {
      // const response = await axios.post(BASE_URL + "/document", reqBody, {
      //   headers: {
      //     token: localStorage.getItem("accessToken"),
      //     "X-App-Id": "123456789"
      //   },
      // });
      const response = await instance({
        url: "/document",
        method: "POST",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      console.log("Profile pic change", response);
      if (response.status === 201) {
        if (response.data.result === false) {
          throw Error(response.data.data);
        }
        ToastersService.successToast("Profile pic updated successfully!");
        dispatch(getUserProfileData(userToken.accessToken, navigate));
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error.message);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const openFileUploadHandler = () => {
    console.log("clicked");
    inputRef.current.click();
  };

  const handleImageChange = async (event) => {
    const fileObj =
      event.target.files && (await resizeFile(event.target.files[0]));
    if (!fileObj) {
      return;
    }
    console.log("fileObj is", fileObj);

    let formData = new FormData();
    // formData.append("staff_thumbnail_photo_1", fileObj);
    formData.append("file", fileObj);
    formData.append("entity", "staff");
    uploadImageToServer(formData);
  };

  const uploadImageToServer = async (formData) => {
    try {
      // const response = await axios.post(BASE_URL + "/document", formData, {
      //   headers: {
      //     token: localStorage.getItem("accessToken"),
      //     "X-App-Id": "123456789"
      //   },
      // });
      const response = await instance({
        url: "/document",
        method: "POST",
        data: formData,
        headers: {
          token: userToken.accessToken,
        },
      });
      console.log("File upload response", response);
      if (response.status === 201) {
        ToastersService.successToast("Image uploaded to server!");
        const reqBody = {
          document_entries: [
            {
              document_type: "staff_thumbnail_photo_1",
              entity: userData.user_type_resolved.type,
              entity_1_id: userData.staff_id,
              url: response.data.data.doc_key,
              document_information: "",
            },
          ],
        };
        console.log("ReqBody", reqBody);
        changeProfilePic(reqBody);

        // changeProfilePic(
        //   "document_type": "farm_photo_1",
        //   "entity": "farmer",
        //   "entity_1_id": 583,
        //   "url": " frshrTech/test/167272720397452cf3be8143fbcdf.jpg",
        //   "document_information": " test"
        // )
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
      if (error.response.status === 413) {
        ToastersService.failureToast(
          error.response.data.error
            ? error.response.data.error
            : "File size limit exceeded!"
        );
      }
      // setCreatingTaskLoader(false);
    }
  };

  return (
    <>
      {!userData ? (
        <div className="text-center my-5">
          <Loader />
        </div>
      ) : (
        <Container className="my-5">
          {userData && (
            <Row>
              <Col
                className="mb-4"
                md={6}
                sm={12}
                style={{ display: "flex", justifyContent: "center" }}
              >
                <div
                  style={{
                    border: "1px solid silver",
                    padding: "5px",
                    borderRadius: "50%",
                  }}
                >
                  <div className="profile__img-div">
                    <img
                      // src="https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460__340.png"
                      src={
                        userData.image
                          ? BASE_URL + "/media/" + userData.image
                          : "https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460__340.png"
                      }
                      alt=""
                      style={{ width: "100%", height: "100%" }}
                    />
                    <div
                      className="profile__edit-btn"
                      onClick={openFileUploadHandler}
                    >
                      <input
                        style={{ display: "none" }}
                        ref={inputRef}
                        type="file"
                        onChange={handleImageChange}
                      />
                      <i
                        className="fa fa-pencil-square"
                        aria-hidden="true"
                        style={{ backgroundColor: "#fff" }}
                      ></i>
                    </div>
                  </div>
                </div>
              </Col>
              <Col md={6} sm={12}>
                <p className="m-0" style={{ fontSize: "13px" }}>
                  Staff Id: {userData.staff_id}
                </p>
                <h4 style={{ fontWeight: "600", textTransform: "capitalize" }}>
                  {userData.name.ul}
                </h4>
                <h6 style={{ textTransform: "capitalize", color: "#4c4c4c" }}>
                  {userData.user_type_resolved.type}
                </h6>
                <p className="m-0">
                  <i className="fa fa-phone" aria-hidden="true"></i>{" "}
                  {userData.mobile}
                </p>
                <p className="m-0">
                  <i className="fa fa-envelope" aria-hidden="true"></i>{" "}
                  {userData.email}
                </p>
                <p className="m-0">
                  <span style={{ fontWeight: 700 }}>App version:</span>{" "}
                  {packageJson.version}
                </p>
              </Col>
            </Row>
          )}
        </Container>
      )}
    </>
  );
};

export default Profile;
