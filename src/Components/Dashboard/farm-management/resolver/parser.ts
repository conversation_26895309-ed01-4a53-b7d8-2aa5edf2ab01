/**
 * Types for the various data structures in your JSON
 */
type LocalizedValue = {
    label?: string;
    value?: string;
  } | string;
  
  type L10nField = {
    [language: string]: LocalizedValue;
  };
  
  interface FolderData {
    title?: string;
    title_l10n?: L10nField;
    status?: string;
    topic_priority?: number | null;
    question_name_l10n?: L10nField;
    folders?: FolderData[];
    question_id?: string;
    type?: string;
    topic_question_priority?: number | null;
    farmer_input_required?: number;
    topic_name_l10n?: L10nField;
    total_question?: number;
    total_answered?: number;
    [key: string]: any; // Allow for additional fields
  }
  
  /**
   * Deep resolves any stringified JSON values in an object
   * @param data - The data to resolve
   * @returns The resolved data with all stringified JSON parsed
   */
  const deepResolveJsonFields = (data: any): any => {
    // Handle null/undefined
    if (data == null) {
      return data;
    }
  
    // Handle strings that might be JSON
    if (typeof data === 'string') {
      try {
        const parsed = JSON.parse(data);
        // Recursively resolve the parsed data
        return deepResolveJsonFields(parsed);
      } catch {
        // If it's not JSO<PERSON>, return the original string
        return data;
      }
    }
  
    // Handle arrays
    if (Array.isArray(data)) {
      return data.map(item => deepResolveJsonFields(item));
    }
  
    // Handle objects
    if (typeof data === 'object') {
      const resolved: { [key: string]: any } = {};
  
      for (const [key, value] of Object.entries(data)) {
        resolved[key] = deepResolveJsonFields(value);
      }
  
      return resolved;
    }
  
    // Return primitives as is
    return data;
  };
  
  /**
   * Gets the localized value from an l10n field
   * @param field - The l10n field
   * @param language - Target language code
   * @returns The resolved localized string
   */
  const getLocalizedValue = (field: any, language: string = 'en'): string => {
    if (!field) return '';
  
    const resolvedField = deepResolveJsonFields(field);
  
    // Handle direct string value
    if (typeof resolvedField === 'string') {
      return resolvedField;
    }
  
    // Get language-specific value
    const langValue = resolvedField[language];
  
    if (!langValue) {
      // Fallback to first available language
      const firstLang = Object.values(resolvedField)[0];
      return getLocalizedValue(firstLang, language);
    }
  
    if (typeof langValue === 'string') {
      return langValue;
    }
  
    // Handle object with label/value
    if (typeof langValue === 'object' && langValue !== null) {
      return langValue.value || langValue.label || '';
    }
  
    return '';
  };
  
  /**
   * Process folder data resolving all stringified JSON fields
   * @param data - The folder data to process
   * @param language - Target language code
   * @returns Processed data with all JSON fields resolved
   */
  const processData = (data: FolderData, language: string = 'en'): FolderData => {
    // First, deep resolve any stringified JSON in the entire object
    const resolvedData = deepResolveJsonFields(data);
  
    // Then process specific l10n fields
    if (resolvedData.title_l10n) {
      resolvedData.resolvedTitle = getLocalizedValue(resolvedData.title_l10n, language);
    }
  
    if (resolvedData.question_name_l10n) {
      resolvedData.resolvedQuestionName = getLocalizedValue(resolvedData.question_name_l10n, language);
    }
  
    if (resolvedData.topic_name_l10n) {
      resolvedData.resolvedTopicName = getLocalizedValue(resolvedData.topic_name_l10n, language);
    }
  
    // Process nested folders recursively
    if (Array.isArray(resolvedData.folders)) {
      resolvedData.folders = resolvedData.folders.map(folder => 
        processData(folder, language)
      );
    }
  
    return resolvedData;
  };
  
  export { deepResolveJsonFields, processData, getLocalizedValue };