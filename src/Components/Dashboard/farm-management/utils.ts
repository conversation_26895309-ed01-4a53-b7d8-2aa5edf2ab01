import _ from 'lodash'
import moment from 'moment';

export const updateFormSelections = async (config: any, formData: any) => {
  // Debugging: Check if config is valid
  console.log('Config object:', config, formData);
  // Filter out the 'reference_media' item from form_fields
  
  const clonedConfig = _.cloneDeep(config)

   // Filter out the 'reference_media' item from form_fields
   clonedConfig.form_fields = clonedConfig.form_fields.filter((item: any) => item.key !== "reference_media");

  // Iterate over each form field in the config
  clonedConfig.form_fields.forEach((item: any) => {
    // Check if the formData has the key and update selections accordingly
    if (formData.hasOwnProperty(item.key)) {
      if (Array.isArray(formData[item.key])) {
        // Update the selections.values based on the formData value if it's an array
        item.selections.values = formData[item.key];
      } else if (item.key === "target_date") {
        // Format date if the key is "target_date"
        item.selections.values = [moment(formData[item.key]).format("YYYY-MM-DD")];
      } else {
        // Update the selections.values if it's not an array
        item.selections.values = [formData[item.key]];
      }
    } else {
      // If the key is not in formData, set selections.values to an empty array
      item.selections.values = [];
    }
  });


  // Debugging: Check the updated config
  console.log('Returned Config:', clonedConfig);
  return clonedConfig;
};


  export const updateFormDataFromConfig =(config: any) => {
    const formData: any = {}
  
    // Debugging: Check if config is valid
    console.log('Config object:', config)
  
    // Iterate over each form field in the config
    config?.form_fields?.forEach((item: any) => {
      // Check if the item has selections and update formData accordingly
      if (item.selections && item.selections.values) {
        if (item.type === 'advanced_multi_select' ) {
          formData[item.key] = item.selections.values
        }else if (item.type === "advanced_select") {
          formData[item.key] = item.selections.values[0]
        } else if (item.type == 'media') {
          formData[item.key] = item.selections.values[0]?.value
        } 
         else {
          // For other types like 'advanced_select' or 'text', handle accordingly
          formData[item.key] =  item.selections.values[0] || ""
        }
      }
    })
  
    // Debugging: Check the updated formData
    console.log('Returned formData:', formData)
    return formData
  }
