import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { BASE_URL } from "../../../../Services/api.service";
import { updateFormDataFromConfig } from "../utils";
import { fm_form_data } from "../components/form/formData";
import { updatedJsonResolver } from "@krushal-it/krushal-hook-form";
import ToastersService  from '../../../../Services/toasters.service';

const handleGlobalError = (error) => {
  if (error.status === 401) {
    ToastersService.failureToast("Session Expired!");
    localStorage.removeItem("accessToken");
    localStorage.removeItem("resetToken");
    window.location.href = "/login"; 
  }
};

export const farmManagementSlice = createApi({
  reducerPath: "farmManagementSlice",
  baseQuery: async (args, api, extraOptions) => {
    const result = await fetchBaseQuery({
      baseUrl: BASE_URL,
      prepareHeaders: (headers, { getState }: any) => {
        const token =getState().user.userToken.accessToken;
        if (token) {
          headers.set("token", token);
        }
        return headers;
      },
    })(args, api, extraOptions);
    if (result.error) {
      handleGlobalError(result.error);
      return; 
    }
    return result;
  },

  tagTypes: ["FM_SLICE"],
  endpoints: (builder) => ({
    getFMFarmersList: builder.query({
      query: () => ({
        url: "/customer/farm_management/list",
        method: "GET",
      }),
      keepUnusedDataFor: 0,
    }),
    getFmTaskList: builder.query({
      query: ({ customer_id }) => ({
        url: `customer/farm_management/${customer_id}`,
        method: "GET",
      }),
      providesTags: ["FM_SLICE"],
      keepUnusedDataFor: 0,
    }),
    getFmTopics: builder.query({
      query: ({ care_calendar_id }) => ({
        url: `/topic/farm-management/${care_calendar_id}`,
        method: "GET",
      }),
      providesTags: ["FM_SLICE"],
      keepUnusedDataFor: 0,
    }),
    getFmQuestions: builder.query({
      query: ({ question_id, calendar_id }) => ({
        url: `/question/${question_id}/${calendar_id}`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
      providesTags: ["FM_SLICE"],
      transformResponse: (response: any) => {
        const formData = updateFormDataFromConfig(response.form_configuration);
        const resolveFormData = fm_form_data(
          response.form_configuration,
          response,
          "en"
        );
        const config = updatedJsonResolver(
          response.form_configuration,
          resolveFormData
        );
        return {
          resolveData: formData,
          config,
          withoutTransformData: response.form_configuration,
        };
      },
      transformErrorResponse: (
        response: any | { status: number; data: unknown }
      ) => {
        return {
          status: "CUSTOM_ERROR",
          message:
            "data" in response
              ? response.data?.message || "An error occurred"
              : "An error occurred",
        };
      },
    }),
    saveFmQuestion: builder.mutation({
      query: ({ question_id, calendar_id, payload }) => ({
        url: `/question/${question_id}/${calendar_id}`,
        method: "POST",
        body: { form_configuration: payload },
      }),
      invalidatesTags: ["FM_SLICE"],
    }),
    completeForm: builder.mutation({          
      query: ({ calendarId, user_completion_date_time }) => ({
        url: `/task/farm-management/${calendarId}/complete`,
        method: "POST",
        body: { user_completion_date_time },
      }),
      invalidatesTags: ["FM_SLICE"],
    })
  }),
  
});

export const {
  useGetFMFarmersListQuery,
  useLazyGetFmQuestionsQuery,
  useGetFmTaskListQuery,
  useLazyGetFmTopicsQuery,
  useSaveFmQuestionMutation,
  useCompleteFormMutation,
} = farmManagementSlice;
