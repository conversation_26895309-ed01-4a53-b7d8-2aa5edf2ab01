import { Box, Chip, Typography,Button } from "@mui/material";
import FmTopics from "./FmTopics";
import { useLazyGetFmTopicsQuery } from "../../slice/farmManagement.slice";
import SearchQuestions from "../search/SearchQuestions";
import { useEffect, useState } from "react";
import Fuse from "fuse.js";
import { Spin } from "antd";
import { useTaskContext } from "../../TaskContext";

const fuseOptions = {
  keys: ["title"],
  threshold: 0.4,
};

type FarmManagementTopicsProps = {
  initialState: any;
  dispatch: any;
};

const FarmManagementTopics = ({
  dispatch,
  initialState,
}: FarmManagementTopicsProps) => {
 
  const [triggerFetchTopics, { data, isLoading, isError, isFetching }] = useLazyGetFmTopicsQuery();
  const { scrollToLastCompleted } = useTaskContext();

  useEffect(() => {
    if (initialState?.selectedTask?.taskId) {
      triggerFetchTopics({ care_calendar_id: initialState.selectedTask.taskId });
    }
  }, [initialState?.selectedTask?.taskId, triggerFetchTopics]);

  console.log("FarmManagementTopics", data, isLoading, isFetching);
  const [searchQuery, setSearchQuery] = useState("");

  const [filteredData, setFilteredData] = useState<any[]>();
  // initialState.selectedTask.taskId
  useEffect(() => {
    if (!data?.data) return;

    const allQuestions = extractQuestions(data.data);
    const fuse = new Fuse(allQuestions, fuseOptions);

    if (searchQuery) {
      const results = fuse.search(searchQuery);
      const items = results.map((result: any) => result.item);
      setFilteredData(items);
    } else {
      setFilteredData(data.data);
    }
  }, [searchQuery, data]);

  const extractQuestions = (folders: any) => {
    let questions: any = [];

    const traverse = (item: any) => {
      if (item.type === "question") {
        questions.push(item);
      } else if (item.folders) {
        item.folders.forEach(traverse);
      }
    };

    folders.forEach(traverse);
    return questions;
  };

  if (isLoading || isFetching)
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
        }}
      >
        <Spin size="large" />
      </Box>
    );

  if (!filteredData)
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection:'column',
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
        }}
      >
        <Typography variant="h5" color="#999" textAlign="center">
          Please select a task
        </Typography>
        <Button
          onClick={scrollToLastCompleted}
          variant="contained"
          size="small"
          sx={{
            ml: 1,
            mt:2,
            backgroundColor: 'purple',
            color: 'white',
            '&:hover': { backgroundColor: 'purple' }
          }}
        >
          Last Completed
        </Button>
      </Box>
    );

  if (isError)
    return (
      <Box
        sx={{ display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <Chip label="Something went wrong" color="error" />
      </Box>
    );

  return (
    <Box>
      <Box>
        <SearchQuestions
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
        />

        <FmTopics
          activityDetailsViewOnly={false}
          isFetching={isLoading}
          navigation={null}
          topics={filteredData || []}
          userLanguage="en"
          dispatch={dispatch}
        />
      </Box>
    </Box>
  );
};

export default FarmManagementTopics;
