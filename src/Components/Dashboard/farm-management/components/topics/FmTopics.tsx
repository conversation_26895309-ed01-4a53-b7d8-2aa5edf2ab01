import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  IconButton,
  Paper,
  Typography,
} from "@mui/material";
import React, { useLayoutEffect, useState } from "react";
import { extractBasedOnLanguageMod } from "../../../../../Utilities/Common/utils";

import { FarmManagementTypes } from "../../fm.types";
import {
  ArrowDropUp,
  ArrowDropDown,
  Visibility,
  Help,
  QuestionAnswer,
  CheckCircle,
} from "@mui/icons-material";

type FmTopicProps = {
  navigation: any;
  topics: FarmManagementTypes["data"];
  activityDetailsViewOnly: boolean;
  dispatch: any;
  isFetching: any;
  userLanguage: string;
};

const FmTopics = ({
  navigation,
  topics,
  dispatch,
  activityDetailsViewOnly,
  isFetching,
  userLanguage,
}: FmTopicProps) => {
  return (
    <Box>
      <Box>
        {topics?.map((folder: any, index: any) => (
          <Folder
            folder={folder}
            key={index}
            navigation={navigation}
            activityDetailsViewOnly={activityDetailsViewOnly}
            userLanguage={userLanguage}
            dispatch={dispatch}
          />
        ))}
      </Box>
    </Box>
  );
};

type FolderProps = {
  folder: any;
  level?: number;
  navigation: any;
  activityDetailsViewOnly: boolean;
  userLanguage: string;
  dispatch: any;
};

const Folder = ({
  folder,
  level = 0,
  navigation,
  activityDetailsViewOnly,
  userLanguage,
  dispatch,
}: FolderProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const selectedTaskHandler = (id: string, title: string) => {
    dispatch({ type: "SELECT_QUES", payload: { question_id: id, title } });
  };
  const hasChildren = folder?.folders && folder.folders.length > 0;

  return (
    <Box style={styles.container}>
      {folder?.type === "question" ? (
        <Box
          onClick={() => selectedTaskHandler(folder.question_id, folder.title)}
        >
          <QuestionListItem
            question={extractBasedOnLanguageMod(
              folder.question_name_l10n,
              userLanguage
            )}
            saved_at={folder?.saved_at}
            farmer_input_required={folder.farmer_input_required}
          />
        </Box>
      ) : (
        <Card
          onClick={toggleExpand}
          sx={{ backgroundColor: "#eee6fa", margin: 1 }}
          component={Paper}
          elevation={4}
        >
          <CardContent
            sx={{ display: "flex", justifyContent: "space-between" }}
          >
            <Box sx={{ flex: 1 }}>
              <Typography style={{ color: "#000" }}>
                {extractBasedOnLanguageMod(folder.title_l10n, userLanguage)}
              </Typography>
              <Typography style={{ color: "#6610f2", fontWeight: "bold" }}>
                {folder.status}
              </Typography>
            </Box>
            {hasChildren && (
              <IconButton size="medium" onClick={toggleExpand}>
                {!isExpanded ? (
                  <ArrowDropUp fontSize="inherit" />
                ) : (
                  <ArrowDropDown />
                )}
              </IconButton>
            )}
          </CardContent>
        </Card>
      )}
      {isExpanded && hasChildren && (
        <Box style={{ marginLeft: 10 }}>
          {folder.folders.map((childFolder: any, index: number) => (
            <Folder
              folder={childFolder}
              key={index}
              level={level + 1}
              navigation={navigation}
              activityDetailsViewOnly={activityDetailsViewOnly}
              userLanguage={userLanguage}
              dispatch={dispatch}
            />
          ))}
        </Box>
      )}
    </Box>
  );
};

type QuestionListItemProps = {
  question: string;
  saved_at: string;
  farmer_input_required: number;
};

const QuestionListItem = ({
  question,
  saved_at,
  farmer_input_required,
}: QuestionListItemProps) => {
  const iconFarmerInput =
    farmer_input_required === 0 ? (
      <Visibility />
    ) : farmer_input_required === null ? (
      <Help />
    ) : (
      <QuestionAnswer />
    );

  return (
    <Paper
      style={{
        borderRadius: 8,
        backgroundColor: "#f0f0f0",
        cursor: "pointer",
      }}
      elevation={4}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          padding: 1,
        }}
      >
        <IconButton size="medium">{iconFarmerInput}</IconButton>
        <Typography style={styles.questionText}>{question}</Typography>
        {saved_at && <CheckCircle fontSize="medium" color="success" />}
      </Box>
    </Paper>
  );
};

const styles = {
  container: {
    padding: 5,
    marginBottom: 5,
  },
  card: {
    marginBottom: 5,
  },
  cardContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  folderTitleContainer: {
    flex: 1,
  },
  childrenContainer: {
    marginLeft: 15,
  },
  surface: {
    // Light grey background
  },

  icon: {
    marginRight: 4,
  },
  questionText: {
    flex: 1,
    color: "#333", // Dark grey text
  },
  farmerCard: {
    backgroundColor: "#eee6fa",
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#d3cce3",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
    margin: 10,
  },
  label: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#5a2a8e",
    marginBottom: 5,
  },
  value: {
    fontSize: 16,
    color: "#333",
    marginBottom: 10,
  },
};

export default FmTopics;
