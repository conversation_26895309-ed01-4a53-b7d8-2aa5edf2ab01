import * as React from "react";
import Paper from "@mui/material/Paper";
import InputBase from "@mui/material/InputBase";
import Divider from "@mui/material/Divider";
import IconButton from "@mui/material/IconButton";
import SearchIcon from "@mui/icons-material/Search";

type SearchQuestionsProps = {
  searchQuery: string | undefined;
  setSearchQuery: React.Dispatch<React.SetStateAction<string>>;
};

export default function SearchQuestions({
  setSearchQuery,
  searchQuery,
}: SearchQuestionsProps) {
  const handleSearchChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (typeof setSearchQuery === "function") {
        setSearchQuery(e.target.value);
      } else {
        console.error("setSearchQuery is not a function:", setSearchQuery);
      }
    },
    [setSearchQuery]
  );

  return (
    <Paper
      component="form"
      sx={{
        p: "2px 4px",
        display: "flex",
        alignItems: "center",
        mb: 2,
      }}
    >
      <InputBase
        sx={{ ml: 1, flex: 1 }}
        placeholder="Search Questions"
        inputProps={{ "aria-label": "search questions" }}
        value={searchQuery}
        onChange={handleSearchChange}
      />
      <IconButton type="button" sx={{ p: "10px" }} aria-label="search">
        <SearchIcon />
      </IconButton>
      <Divider sx={{ height: 28, m: 0.5 }} orientation="vertical" />
    </Paper>
  );
}
