import { Box, Typography } from "@mui/material";
import { useGetFMFarmersListQuery } from "../../slice/farmManagement.slice";
import FMFarmersTable from "./FMFarmersTable";
import FmTopics from "../topics/FmTopics";
import config from "../topics/config.json";

const FMFarmersList = () => {
  const { data, isLoading } = useGetFMFarmersListQuery({});
  console.log("FMFarmersList", data, isLoading);
  return (
    <Box sx={{ mt: 2 }}>
      {/* <Typography>FMFarmersList</Typography> */}
      <FMFarmersTable data={data} isLoading={isLoading} />
    </Box>
  );
};

export default FMFarmersList;
