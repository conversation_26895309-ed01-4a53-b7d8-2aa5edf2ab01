
import React, { useState, KeyboardEvent, useMemo } from "react";
import { Table } from "antd";
import type { TableProps, ColumnType } from "antd/es/table";
import { FmCustomer } from "../../fm.types";
import { Link } from "react-router-dom";
import { extractBasedOnLanguageMod } from "../../../../../Utilities/Common/utils";
import { Typography, TextField, Button, InputAdornment } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import dayjs from "dayjs"; 

type FMFarmersTableProps = {
  data: FmCustomer[];
  isLoading: boolean;
};

const FMFarmersTable = ({ data, isLoading }: FMFarmersTableProps) => {
  const [filters, setFilters] = useState<Record<string, string[]>>({});
  
  console.log(data,'village')

  const columns: ColumnType<FmCustomer>[] = useMemo(() => [
    {
      title: "Visual Id",
      dataIndex: "customer_visual_id",
      key: "customer_visual_id",
    },
    {
      title: "Name",
      dataIndex: "customer_name_l10n",
      key: "customer_name_l10n",
      render: (text, record) => (
        <Link to={`/farm-management/task-list/${record.customer_id}`} state={record.village}>
          {extractBasedOnLanguageMod(record.customer_name_l10n)}
        </Link>
      ),
    },
    {
      title: "Mobile Number",
      dataIndex: "mobile_number",
      key: "mobile_number",
    },
    {
      title: "Alt Mobile Number",
      dataIndex: "alternate_mobile_number",
      key: "alternate_mobile_number",
    },
    {
      title: "Village",
      dataIndex: "village",
      key: "village",
      render: (text, record) => (
        <Typography variant="caption">
          {extractBasedOnLanguageMod(record.village)}
        </Typography>
      ),
    },
    {
      title: "Total Visits Completed",
      dataIndex: "total_visits_completed",
      key: "total_visits_completed",
      render: (text, record) => (
        <Typography variant="body2" sx={{ml:8}}>
         {record.total_visits_completed || 'N/A'}
        </Typography>
      ),
    },
    {
      title: "Last Visit Date",
      dataIndex: "last_visit_date",
      key: "last_visit_date",
      render: (text, record) => (
        <Typography variant="body2">
          {record.last_visit_date ? dayjs(record.last_visit_date).format('lll') : ''}
        </Typography>
      ),
    },
    {
      title: "Next Visit Date",
      dataIndex: "next_visit_date",
      key: "next_visit_date",
      render: (text, record) => (
        <Typography variant="body2">
          {record.next_visit_date ? dayjs(record.next_visit_date).format('lll') : ''}
        </Typography>
      ),
    },
  ], []); 

  const handleSearch = (selectedKeys: string[], confirm: () => void, dataIndex: string) => {
    confirm();
    setFilters(prev => ({ ...prev, [dataIndex]: selectedKeys }));
  };

  const handleReset = (clearFilters: () => void, dataIndex: string) => {
    clearFilters();
    setFilters(prev => ({ ...prev, [dataIndex]: [] }));
  };

  const getColumnSearchProps = (dataIndex: keyof FmCustomer): ColumnType<FmCustomer> => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8, width: 200 }}>
        <TextField
          autoFocus
          placeholder={`Search Name`}
          value={selectedKeys[0] || ''}
          onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onKeyDown={(e: KeyboardEvent<HTMLDivElement>) => {
            if (e.key === 'Enter') {
              handleSearch(selectedKeys as string[], confirm, dataIndex);
            }
          }}
          style={{ width: 188, marginBottom: 8, display: 'block' }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon style={{ fontSize: 16 }} />
              </InputAdornment>
            ),
          }}
          size="small"
        />
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button
            onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
            variant="contained"
            color="primary"
            size="small"
            style={{ width: 90 }}
          >
          <SearchIcon style={{ fontSize: 16 }} />  Search
          </Button>
          <Button
            onClick={() => {
              clearFilters && clearFilters();
              handleReset(clearFilters!, dataIndex);
            }}
            variant="outlined"
            size="small"
            style={{ width: 90 }}
          >
            Reset
          </Button>
        </div>
      </div>
    ),
    filterIcon: (filtered: boolean) => (
      <SearchIcon style={{ color: filtered ? '#1890ff' : undefined, fontSize: 14 }} />
    ),
    onFilter: (value: string | number | boolean, record: FmCustomer) => {
      if (dataIndex === 'customer_name_l10n') {
        const localizedName = extractBasedOnLanguageMod(record.customer_name_l10n);
        return localizedName.toLowerCase().includes(String(value).toLowerCase());
      }
      const recordValue = record[dataIndex];
      if (typeof value === 'string' && typeof recordValue === 'string') {
        return recordValue.toLowerCase().includes(value.toLowerCase());
      }
      return String(recordValue).toLowerCase().includes(String(value).toLowerCase());
    },
    filteredValue: filters[dataIndex] || null,
  });

  const searchableColumns = ["customer_visual_id", "customer_name_l10n", "mobile_number"];
  const enhancedColumns = useMemo(() => columns.map(col => {
    if (col.dataIndex && searchableColumns.includes(col.dataIndex.toString())) {
      return {
        ...col,
        ...getColumnSearchProps(col.dataIndex as keyof FmCustomer),
      };
    }
    return col;
  }), [columns, filters, searchableColumns]);

  return (
    <Table<FmCustomer>
      columns={enhancedColumns}
      dataSource={data}
      loading={isLoading}
    />
  );
};

export default FMFarmersTable;