import React, { useState } from "react";
import { Typography } from "@mui/material";
import { Table, Card, Popover, Radio } from "antd";
import type { TableColumnsType } from "antd";
import dayjs from "dayjs";
import useGetVisitCompletedData from "./useGetVisitCompletedData";

export interface DataType {
  question_uuid: string;
  question_name: string;
  topic_name: string;
  category: string;
  answered: boolean;
  data_available: boolean;
  weightage: number;
  score: string;
  current: any[];
  target: any[];
  reference_media?: ReferenceMedia;
}

export interface ReferenceMedia {
  [key: string]: ReferenceMediaInfo[];
}

export interface ReferenceMediaInfo {
  link: string;
  name: string;
  media_type: string;
  description: string;
  document_id: string;
}

interface VisitCompletedTableProps {
  task: any;
  dispatch: (action: any) => void;
  careCalendarId: any;
  completionDate: any;
}

const VisitCompletedTable: React.FC<VisitCompletedTableProps> = ({
  task,
  dispatch,
  careCalendarId,
  completionDate,
}) => {
  const { data, isLoading } = useGetVisitCompletedData({ careCalendarId }) as {
    data: DataType[];
    isLoading: boolean;
  };
  const [radioFilterType, setRadioFilterType] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  // Filter data based on radio button selection
  const filteredData =
    data?.filter((item) => {
      if (radioFilterType === "answered") {
        return item.answered === true;
      } else if (radioFilterType === "available") {
        return item.data_available === true;
      } else {
        return true;
      }
    }) || [];

  // Calculate data to display for the current page
  const paginatedData = filteredData.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // Add `key` property to each row for Ant Design Table
  const tableData = paginatedData.map((item) => ({
    ...item,
    key: item.question_uuid,
  }));

  const farmScore = data?.find((item) => item.score !== "-" && item.score)
    ? data?.find((item) => item.score !== "-" && item.score)?.score
    : "";

  const columns: TableColumnsType<DataType> = [
    {
      title: "Category",
      dataIndex: "category",
      key: "category",
      fixed: "left",
    },
    {
      title: "Topic",
      dataIndex: "topic_name",
      key: "topic_name",
      fixed: "left",
    },
    {
      title: "Evaluation Item",
      dataIndex: "question_name",
      key: "question_name",
    },
    {
      title: "Current Value",
      dataIndex: "current",
      key: "current",
      render: (current: string[]) => {
        if (current && current.length > 0) {
          if (current.length > 1) {
            const additionalCurrent = current.slice(1);
            return (
              <>
                <Typography variant="body2">{current[0]}</Typography>
                <Popover
                  content={
                    <div>
                      {additionalCurrent.map((adv, index) => (
                        <Typography key={index} variant="body2">
                          {adv}
                        </Typography>
                      ))}
                    </div>
                  }
                  title="Additional Current"
                  trigger="click"
                >
                  <a style={{ color: "purple", fontWeight: 600 }}>
                    +{additionalCurrent.length} more
                  </a>
                </Popover>
              </>
            );
          }
          return <Typography variant="body2">{current[0] || ""}</Typography>;
        }
      },
    },
    {
      title: "Target Value",
      dataIndex: "target",
      key: "target",
      render: (target: string[]) => {
        if (target && target.length > 0) {
          if (target.length > 1) {
            const additionalTarget = target.slice(1);
            return (
              <>
                <Typography variant="body2">{target[0]}</Typography>
                <Popover
                  content={
                    <div>
                      {additionalTarget.map((adv, index) => (
                        <Typography key={index} variant="body2">
                          {adv}
                        </Typography>
                      ))}
                    </div>
                  }
                  title="Additional Target"
                  trigger="click"
                >
                  <a style={{ color: "purple", fontWeight: 600 }}>
                    +{additionalTarget.length} more
                  </a>
                </Popover>
              </>
            );
          }
          return <Typography variant="body2">{target[0] || ""}</Typography>;
        }
      },
    },
    {
      title: "Target Date",
      dataIndex: "target_date",
      key: "target_date",
      render: (target_date: string[]) => target_date?.join(", "),
    },
    {
      title: "Advice Given",
      dataIndex: "advices",
      key: "advices",
      render: (advices: string[]) => {
        if (advices && advices.length > 0) {
          if (advices.length > 1) {
            const additionalAdvice = advices.slice(1);
            return (
              <>
                <Typography variant="body2">{advices[0]}</Typography>
                <Popover
                  content={
                    <div>
                      {additionalAdvice.map((adv, index) => (
                        <Typography key={index} variant="body2">
                          {adv}
                        </Typography>
                      ))}
                    </div>
                  }
                  title="Additional Advices"
                  trigger="click"
                >
                  <a style={{ color: "purple", fontWeight: 600 }}>
                    +{additionalAdvice.length} more
                  </a>
                </Popover>
              </>
            );
          }
          return <Typography variant="body2">{advices[0] || ""}</Typography>;
        }
      },
    },
    { title: "Notes", dataIndex: "notes", key: "notes" },
    // {
    //   title: "Image",
    //   dataIndex: "image",
    //   key: "image",
    //   render: (imageUrls: string[]) =>
    //     imageUrls?.map((imageUrl, index) => (
    //       <a
    //         key={index}
    //         href={imageUrl}
    //         target="_blank"
    //         rel="noopener noreferrer"
    //       >
    //         {imageUrl}
    //       </a>
    //     )),
    // },
    { title: "Weightage", dataIndex: "weightage", key: "weightage" },
    { title: "Score", dataIndex: "score", key: "score", fixed: "right" },
  ];

  return (
    <div>
      <Card style={{ marginBottom: 16, width: "fit-content" }}>
        <div style={{ display: "flex", alignItems: "center" }}>
          <div>
            <span style={{ fontWeight: "bold" }}>Visit Completed On:</span>{" "}
            {dayjs(completionDate).format("lll")}
          </div>
          <div style={{ marginLeft: 20 }}>
            <span style={{ fontWeight: "bold" }}>Farm Score: </span> {farmScore}
          </div>
        </div>
      </Card>
      <div>
        <Radio.Group
          value={radioFilterType}
          onChange={(e) => {
            setRadioFilterType(e.target.value);
            setCurrentPage(1); // Reset to the first page when filter changes
          }}
          style={{ marginBottom: 16 }}
        >
          <Radio value="all">Show All</Radio>
          <Radio value="answered">Show Answered Items</Radio>
          <Radio value="available">Show Available Items</Radio>
        </Radio.Group>
      </div>
      <Table
        loading={isLoading}
        columns={columns}
        dataSource={tableData}
        scroll={{ x: "max-content" }}
        pagination={{
          current: currentPage,
          pageSize,
          total: filteredData.length,
          onChange: (page, pageSize) => {
            setCurrentPage(page);
            setPageSize(pageSize);
          },
          showSizeChanger: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} items`,
        }}
      />
    </div>
  );
};

export default VisitCompletedTable;
