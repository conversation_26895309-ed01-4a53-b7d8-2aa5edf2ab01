import React, { useEffect, useState, useCallback } from "react";
import { useSelector } from "react-redux";
import { instance } from "../../../../../Services/api.service";

const useGetVisitCompletedData = ({ careCalendarId }) => {
  const userToken = useSelector((state) => state.user.userToken);
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const visitCompletedData = useCallback(async () => {
    if (!careCalendarId) {
      return;
    }
    setIsLoading(true);
    try {
      const response = await instance({
        url: `task-data/farm_management/${careCalendarId}`,
        method: "GET",
        headers: {
          token: userToken?.accessToken, // Use optional chaining for safety
        },
      });
      setData(response.data);
    } catch (error) {
      console.error(
        "Error while getting data",
        error.response || error.message
      );
    } finally {
      setIsLoading(false); // Ensure loading state is reset
    }
  }, [careCalendarId, userToken?.accessToken]);

  useEffect(() => {
    visitCompletedData();
  }, [visitCompletedData]);

  return { data, isLoading };
};

export default useGetVisitCompletedData;
