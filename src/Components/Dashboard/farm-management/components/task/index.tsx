import {
  <PERSON>,
  <PERSON><PERSON>c<PERSON><PERSON>,
  <PERSON>ton,
  Link,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import FmTaskList from "./FmTaskList";
import FmTopics from "../topics/FmTopics";
import FarmManagementForm from "../form";
import config from "../topics/config.json";
import { useEffect, useState, useReducer, useMemo } from "react";
import Fuse from "fuse.js";
import SearchQuestions from "../search/SearchQuestions";
import { useGetFmTaskListQuery } from "../../slice/farmManagement.slice";
import { useNavigate, useParams } from "react-router-dom";
import FarmManagementTopics from "../topics";
import PageLayout from "../../../../../Components/Common/PageLayout";
import { TaskProvider } from "../../TaskContext";
import VisitCompletedTable from "./VisitCompletedTable";

const initialState = {
  selectedTask: {},
  selectedQuestion: {},
};

const reducer = (state: any, action: any) => {
  switch (action.type) {
    case "SELECT_TASK":
      return { ...state, selectedTask: action.payload };

    case "SELECT_QUES":
      return { ...state, selectedQuestion: action.payload };

    default:
      return state;
  }
};

const FmTasks = () => {
  const { customerId } = useParams();

  const [state, dispatch] = useReducer(reducer, initialState);

  const { data, isLoading, refetch } = useGetFmTaskListQuery({
    customer_id: customerId,
  });

  const [isCompleted, setIsCompleted] = useState(false)
  const navigate = useNavigate();

  const selectedTask = data?.task?.find(
    task => task.care_calendar_id === state.selectedTask.taskId
  );

  useEffect(() => {
    if (selectedTask?.calendar_activity_status === **********) {
      setIsCompleted(true);
    } else {
      setIsCompleted(false);
    }
  }, [selectedTask]);

  useEffect(() => {
    refetch();
  }, []);

  const width = isCompleted ? "100%" : "30%"
  return (
    <>
      <TaskProvider>
        <Stack
          direction="row"
          // columnGap={2}
          sx={{ height: "100%", overflow: "auto" }}
        >
          <Box
            sx={{
              width: "30%",
              overflow: "auto",
              scrollBehavior: "smooth",
              padding: 2,
            }}
          >
            <FmTaskList
              dispatch={dispatch}
              initialState={state}
              data={data}
              isLoading={isLoading}
            />
          </Box>
          <Box
            sx={{
              width: width,
              overflow: "auto",
              scrollBehavior: "smooth",
              height: "100%",
              backgroundColor: "#eee",
              p: 2,
            }}
          >
            {isCompleted === true ? (<VisitCompletedTable task={selectedTask} dispatch={dispatch} careCalendarId={selectedTask?.care_calendar_id} completionDate={selectedTask?.completion_date}/>) : (
              <FarmManagementTopics dispatch={dispatch} initialState={state} />
            )}
          </Box>
          {!isCompleted && (<Box
            sx={{
              width: "40%",
              overflow: "auto",
              scrollBehavior: "smooth",
              height: "100%",
              backgroundColor: "#eee",
              p: 2,
            }}
          >
            <FarmManagementForm dispatch={dispatch} initialState={state} />
          </Box>)}
        </Stack>
      </TaskProvider>
    </>
  );
};

export default FmTasks;

