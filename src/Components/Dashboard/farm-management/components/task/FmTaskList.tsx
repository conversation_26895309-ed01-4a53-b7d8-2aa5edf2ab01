import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import ListItemAvatar from "@mui/material/ListItemAvatar";
import Avatar from "@mui/material/Avatar";
import WorkIcon from "@mui/icons-material/Work";
import Skeleton from "@mui/material/Skeleton";
import { FmTaskListType } from "../../fm.types";
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  ListItemIcon,
  MenuItem,
  MenuList,
  Stack,
  Typography,
  Modal,
  CircularProgress,
  Select,
} from "@mui/material";
import { useRef, useEffect } from "react";
import { Document, Page } from "react-pdf";
import {
  globalReferencesMapper,
  extractBasedOnLanguageMod,
} from "../../../../../Utilities/Common/utils";
import dayjs from "dayjs";
import { useNavigate } from "react-router-dom";
import { ArrowBack } from "@mui/icons-material";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import InfoIcon from "@mui/icons-material/Info";
import useDateTimeDialog from "../task-completion/useDateTimeDialog";
import { useSelector, useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import { BASE_URL } from "../../../../../Services/api.service";
import { useState } from "react";
import { instance } from "../../../../../Services/api.service";
import { useTaskContext } from "../../TaskContext";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import Pagination from "@mui/material/Pagination";
import { Collapse } from "@mui/material";
import Tooltip from "@mui/material/Tooltip";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";

import { DatePicker } from "antd";
const { RangePicker } = DatePicker;

type FMFarmersTableProps = {
  data: FmTaskListType;
  initialState: any;
  dispatch: any;
  isLoading: boolean;
};

const FmTaskList = ({
  dispatch,
  initialState,
  data,
  isLoading,
}: FMFarmersTableProps) => {
  const userLanguage = useSelector((state: any) => state.user.userLanguage);
  const location = useLocation();
  const village = extractBasedOnLanguageMod(location.state, userLanguage);

  const customerId = data?.customer.customer_id;
  const {
    selectedTaskId,
    setSelectedTaskId,
    shouldAutoOpen,
    setShouldAutoOpen,
    triggerScroll,
    setTriggerScroll,
  } = useTaskContext();
  const [selectedStatus, setSelectedStatus] = useState("");
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [isFiltering, setIsFiltering] = useState(false);
  const [isSorting, setIsSorting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [sortDirection, setSortDirection] = useState("asc");
  const [dateRange, setDateRange] = useState<any>([null, null]);
  const [dateFilterBy, setDateFilterBy] = useState("");
  const [sortBy, setSortBy] = useState("");

  const itemsPerPage = 6;
  const handleFilterToggle = () => {
    setIsFilterOpen((prevState) => !prevState);
  };
  console.log(data, "farm");

  const farmerName = extractBasedOnLanguageMod(
    data?.customer.customer_name_l10n,
    userLanguage
  );

  const getTooltipTitle = () => {
    if (!sortBy) return "Please select sort type first";
    return sortDirection === "asc" ? "Sort Descending" : "Sort Ascending";
  };

  const handleStatusChange = (event) => {
    const newStatus = event.target.value;
    setSelectedStatus(newStatus);

    if (newStatus === "") {
      handleReset();
      return;
    }
    setIsFiltering(true);
    let filtered = data?.task || [];

    if (newStatus === "pending") {
      filtered = filtered.filter(
        (task) => task.calendar_activity_status === 1000300001
      );
    } else if (newStatus === "completed") {
      filtered = filtered.filter(
        (task) => task.calendar_activity_status === 1000300050
      );
    } else if (newStatus === "lastVisited") {
      filtered = filtered
        .filter(
          (task) =>
            task.calendar_activity_status === 1000300050 && task.completion_date
        )
        .sort(
          (a, b) =>
            Number(new Date(a.completion_date)) -
            Number(new Date(b.completion_date))
        );
      filtered = filtered.length > 0 ? [filtered[filtered.length - 1]] : [];
    } else if (newStatus === "nextVisit") {
      const now = new Date();
      filtered = filtered
        .filter(
          (task) =>
            task.calendar_activity_status === 1000300001 &&
            new Date(task.activity_date) > now
        )
        .sort(
          (a, b) =>
            Number(new Date(b.activity_date)) -
            Number(new Date(a.activity_date))
        );
      filtered = filtered.length > 0 ? [filtered[0]] : [];
    }

    if (dateRange[0] && dateRange[1] && dateFilterBy) {
      filtered = filtered.filter((task) => {
        if (dateFilterBy === "completion_date" && !task.completion_date) {
          return false;
        }
        const taskDate = new Date(task[dateFilterBy]).getTime();
        const startTime = dateRange[0].startOf("day").valueOf();
        const endTime = dateRange[1].endOf("day").valueOf();

        return taskDate >= startTime && taskDate <= endTime;
      });
    }
    setFilteredTasks(filtered);
    setCurrentPage(1);
  };

  const handlePageChange = (event, value) => {
    setCurrentPage(value);
  };

  const handleDateRangeChange = (dates) => {
    if (!dates) {
      setDateRange([null, null]);
      if (!selectedStatus) {
        handleReset();
      } else {
        handleStatusChange({ target: { value: selectedStatus } });
      }
      return;
    }
    if (!dateFilterBy) {
      return;
    }
    setDateRange(dates);
    setIsFiltering(true);

    let filtered = selectedStatus ? filteredTasks : data?.task || [];
    filtered = filtered.filter((task) => {
      const dateToCheck = task[dateFilterBy];

      if (dateFilterBy === "completion_date" && !dateToCheck) {
        return false;
      }
      const taskDate = new Date(dateToCheck).getTime();
      const startTime = dates[0].startOf("day").valueOf();
      const endTime = dates[1].endOf("day").valueOf();

      return taskDate >= startTime && taskDate <= endTime;
    });

    setFilteredTasks(filtered);
    setCurrentPage(1);
  };

  const handleReset = () => {
    setSelectedStatus("");
    setDateFilterBy("");
    setSortBy("");
    setDateRange([null, null]);
    setIsFiltering(false);
    setIsSorting(false);
    setSortDirection("asc");
    setFilteredTasks([]);
    setCurrentPage(1);
  };

  const organizeTasksByStatus = (tasks) => {
    if (!tasks || tasks.length === 0) return [];

    if (isSorting && sortBy) {
      return filteredTasks;
    }
    const completed = tasks.filter(
      (task) => task.calendar_activity_status === 1000300050
    );
    const pending = tasks.filter(
      (task) => task.calendar_activity_status === 1000300001
    );
    completed.sort(
      (a, b) =>
        new Date(b.activity_date).getTime() -
        new Date(a.activity_date).getTime()
    );
    pending.sort(
      (a, b) =>
        new Date(a.activity_date).getTime() -
        new Date(b.activity_date).getTime()
    );
    return [...completed, ...pending];
  };

  const handleSort = (event) => {
    const newSortBy = event.target.value;
    if (!newSortBy) {
      setSortBy("");
      setIsSorting(false);
      setFilteredTasks([]);
      setSortDirection("asc");
      setCurrentPage(1);
      return;
    }
    const newDirection =
      newSortBy !== sortBy ? "asc" : sortDirection === "asc" ? "desc" : "asc";
    setSortBy(newSortBy);
    setSortDirection(newDirection);
    setIsSorting(true);
    let tasksToSort = isFiltering
      ? [...filteredTasks]
      : [...(data?.task || [])];

    if (newSortBy === "completion_date") {
      tasksToSort = tasksToSort.filter(
        (task) =>
          task.calendar_activity_status === 1000300050 && task.completion_date
      );
    }
    const sortedTasks = tasksToSort.sort((a, b) => {
      if (newSortBy === "completion_date") {
        const dateA = new Date(a.completion_date).getTime();
        const dateB = new Date(b.completion_date).getTime();
        return newDirection === "asc" ? dateA - dateB : dateB - dateA;
      } else {
        const dateA = new Date(a.activity_date).getTime();
        const dateB = new Date(b.activity_date).getTime();
        return newDirection === "asc" ? dateA - dateB : dateB - dateA;
      }
    });

    setFilteredTasks(sortedTasks);
    setCurrentPage(1);
  };

  const organizedTasks = organizeTasksByStatus(
    isFiltering ? filteredTasks : data?.task || []
  );
  const filteredOrganizedTasks = organizedTasks.filter((task) => {
    if (startDate && endDate) {
      const taskDate = new Date(task[dateFilterBy]).getTime();
      const start = new Date(startDate).getTime();
      const end = new Date(endDate).getTime();
      return taskDate >= start && taskDate <= end;
    }
    return true;
  });

  const totalPages = Math.ceil(filteredOrganizedTasks.length / itemsPerPage);
  const currentTasks = filteredOrganizedTasks.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const selectedTaskHandler = (
    id: string,
    calendar_activity_status: number
  ) => {
    dispatch({
      type: "SELECT_TASK",
      payload: { taskId: id, calendar_activity_status },
    });
    dispatch({
      type: "SELECT_QUES",
      payload: { question_id: null },
    });
  };

  const getStatusName = (id: number) => {
    const data = globalReferencesMapper(10003000)?.find(
      (item: any) => item.reference_id === id
    );
    const name = extractBasedOnLanguageMod(data?.reference_name_l10n, "en");
    return name || "N/A";
  };

  const taskRefs = useRef({});

  useEffect(() => {
    if (triggerScroll && data?.task) {
      const completedTasks = data.task.filter(
        (task) => task.calendar_activity_status === 1000300050
      );

      if (completedTasks.length > 0) {
        const lastCompletedTask = completedTasks[completedTasks.length - 1];

        taskRefs.current[lastCompletedTask.care_calendar_id]?.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });

        setTimeout(() => {
          setSelectedTaskId(lastCompletedTask.care_calendar_id);
          setShouldAutoOpen(true);

          dispatch({
            type: "SELECT_TASK",
            payload: {
              taskId: lastCompletedTask.care_calendar_id,
              calendar_activity_status:
                lastCompletedTask.calendar_activity_status,
            },
          });
        }, 1000);
      }
      setTriggerScroll(false);
    }
  }, [triggerScroll, data]);

  const navigate = useNavigate();

  return (

      <List sx={{ width: "100%" }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 2,
          }}
        >
          <Stack direction="row" spacing={2} justifyContent={"flex-start"}>
            <IconButton onClick={() => navigate(-1)} color="secondary">
              <ArrowBack />
            </IconButton>
            <Box>
              <Typography
                component="h5"
                variant="h5"
                sx={{ fontWeight: 700, fontSize: 24 }}
              >
                FM Task List
              </Typography>
              <Box
                sx={{ backgroundColor: "#ff6e1b", height: 5, width: 100 }}
              ></Box>
            </Box>
          </Stack>
          <Box sx={{ display: "flex", gap: 1 }}>
            <IconButton
              onClick={handleFilterToggle}
              sx={{
                backgroundColor: "purple",
                color: "white",
                "&:hover": { backgroundColor: "purple" },
              }}
            >
              <FilterAltIcon />
            </IconButton>
          </Box>
        </Box>

        <Card
          sx={{
            backgroundColor: "#e4d7f5",
            mb: 1,
            borderRadius: 6,
            width: "100%",
            maxWidth: "800px",
            padding: 1,
            boxShadow: 1,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <AccountCircleIcon
              sx={{
                fontSize: 40,
                color: "purple",
                mr: 3,
              }}
            />
            <Box>
              <Typography
                component="h5"
                variant="h5"
                sx={{
                  fontWeight: 500,
                  fontSize: 18,
                  color: "text.primary",
                }}
              >
                {farmerName}
              </Typography>
              <Typography
                component="h6"
                variant="subtitle1"
                sx={{
                  fontWeight: 300,
                  fontSize: 14,
                  color: "text.secondary",
                }}
              >
                {village}
              </Typography>
            </Box>
          </Box>
        </Card>
        <Collapse in={isFilterOpen} timeout="auto" unmountOnExit>
          <Card
            sx={{
              backgroundColor: "white",
              mb: 1,
              borderRadius: 6,
              width: "100%",
              maxWidth: "800px",
            }}
          >
            <CardContent sx={{ px: 3, py: 2 }}>
              <Box sx={{ mb: 1 }}>
                <Select
                  value={selectedStatus}
                  onChange={handleStatusChange}
                  displayEmpty
                  sx={{
                    height: "32px",
                    width: "188px",
                    fontSize: "14px",
                    "& .MuiSelect-select": {
                      padding: "6px 12px",
                    },
                  }}
                >
                  <MenuItem value="" disabled>
                    Select Status
                  </MenuItem>
                  <MenuItem value="pending">Visit Pending</MenuItem>
                  <MenuItem value="completed">Visit Completed</MenuItem>
                  <MenuItem value="lastVisited">Last Visit Date</MenuItem>
                  <MenuItem value="nextVisit">Next Visit Date</MenuItem>
                </Select>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  gap: 2,
                  mb: 2,
                  alignItems: "center",
                }}
              >
                <Select
                  value={dateFilterBy}
                  onChange={(e) => setDateFilterBy(e.target.value)}
                  displayEmpty
                  sx={{
                    height: "32px",
                    width: "270px",
                    fontSize: "14px",
                    "& .MuiSelect-select": {
                      padding: "6px 12px",
                    },
                  }}
                >
                  <MenuItem value="">Select Filter Type</MenuItem>
                  <MenuItem value="activity_date">Activity Date</MenuItem>
                  <MenuItem value="completion_date">Completion Date</MenuItem>
                </Select>

                <Box sx={{ width: "300px" }}>
                  <RangePicker
                    onChange={handleDateRangeChange}
                    placeholder={["Start Date", "End Date"]}
                    format="YYYY-MM-DD"
                    value={dateRange}
                    style={{
                      height: "32px",
                      fontSize: "14px",
                    }}
                  />
                </Box>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  gap: 2,
                  mb: 2,
                  alignItems: "center",
                }}
              >
                <Select
                  value={sortBy}
                  onChange={handleSort}
                  displayEmpty
                  sx={{
                    height: "32px",
                    width: "188px",
                    fontSize: "14px",
                    "& .MuiSelect-select": {
                      padding: "6px 12px",
                    },
                  }}
                >
                  <MenuItem value="">Select Sort Type</MenuItem>
                  <MenuItem value="activity_date">Activity Date</MenuItem>
                  <MenuItem value="completion_date">Completion Date</MenuItem>
                </Select>

                <Tooltip title={getTooltipTitle()}>
                  <span>
                    <IconButton
                      onClick={() => handleSort({ target: { value: sortBy } })}
                      sx={{
                        backgroundColor: "purple",
                        color: "white",
                        "&:hover": { backgroundColor: "purple" },
                        "&.Mui-disabled": {
                          backgroundColor: "rgba(128, 0, 128, 0.5)",
                          color: "white",
                        },
                        height: "36px",
                        width: "36px",
                        minWidth: "36px",
                      }}
                      disabled={!sortBy}
                    >
                      {sortDirection === "asc" ? (
                        <ArrowUpwardIcon sx={{ fontSize: 20 }} />
                      ) : (
                        <ArrowDownwardIcon sx={{ fontSize: 20 }} />
                      )}
                    </IconButton>
                  </span>
                </Tooltip>
              </Box>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-start",
                  pl: "0px",
                }}
              >
                <Button
                  onClick={handleReset}
                  variant="outlined"
                  size="small"
                  sx={{
                    height: "30px",
                    borderRadius: 1,
                    textTransform: "none",
                    fontSize: "14px",
                    minWidth: "188px",
                  }}
                >
                  Reset All
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Collapse>
        {isLoading ? (
          Array.from({ length: 5 }).map((_, index) => (
            <ListItem sx={{ borderRadius: 12 }} key={index}>
              <ListItemAvatar>
                <Skeleton variant="circular" width={40} height={40} />
              </ListItemAvatar>
              <ListItemText
                primary={<Skeleton variant="text" width="80%" />}
                secondary={<Skeleton variant="text" width="60%" />}
              />
            </ListItem>
          ))
        ) : (
          <>
            {currentTasks.map((item) => (
              <div
                key={item.care_calendar_id}
                ref={(el) => (taskRefs.current[item.care_calendar_id] = el)}
              >
                <TaskCard
                  initialState={initialState}
                  item={item}
                  selectedTaskHandler={selectedTaskHandler}
                  getStatusName={getStatusName}
                  calendarId={item.care_calendar_id}
                  customerId={customerId}
                />
              </div>
            ))}

            {filteredOrganizedTasks.length > itemsPerPage && (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  mt: 3,
                  mb: 2,
                }}
              >
                <Pagination
                  count={totalPages}
                  page={currentPage}
                  onChange={handlePageChange}
                  variant="outlined"
                  color="primary"
                  siblingCount={1}
                  boundaryCount={1}
                  size="large"
                />
              </Box>
            )}
          </>
        )}
      </List>
  );
};
export default FmTaskList;

const TaskCard = ({
  initialState,
  item,
  selectedTaskHandler,
  getStatusName,
  calendarId,
  customerId,
}) => {
  const { DialogComponent, openDialog, isClosed } = useDateTimeDialog({
    calendarId,
  });
  const userToken = useSelector((state: any) => state?.user?.userToken);
  const isActivityDetailsOnly = item.calendar_activity_status === 1000300050;
  const handleDownload = async (documentId) => {
    const fileUrl = BASE_URL + "/media/" + documentId;
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = "FarmManagement.pdf";
    document.body.appendChild(link);
    link.target = "_blank";
    link.click();
  };

  const [pdfData, setPdfData] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [numPages, setNumPages] = useState(null);
  const [isLoading, setLoading] = useState(false);

  const handleGeneratePDF = async () => {
    console.log(calendarId, "calenderId");
    setLoading(true);
    try {
      const response = await instance({
        url: `${BASE_URL}/customer/farm_management_summary_with_lang/en/${customerId}/${calendarId}`,
        method: "GET",
        headers: {
          token: userToken.accessToken,
        },
        responseType: "arraybuffer",
      });
      const pdfBlob = new Blob([response.data], { type: "application/pdf" });
      setPdfData(pdfBlob);
      setIsModalOpen(true);
      setLoading(false);
    } catch (error) {
      console.error("Error generating PDF:", error);
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSavePDF = () => {
    if (pdfData) {
      const url = URL.createObjectURL(pdfData);
      const link = document.createElement("a");
      link.href = url;
      link.download = "FarmManagement.pdf";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  };

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
  };

  return (
    <>
      <Card
        sx={{
          borderRadius: 6,
          mt: 2,
          cursor: "pointer",
          backgroundColor:
            initialState.selectedTask.taskId === item.care_calendar_id
              ? "#e6e7e8"
              : "#fff",
          ":hover": { backgroundColor: "#e6e7e8" },
        }}
        onClick={() =>
          selectedTaskHandler(
            item.care_calendar_id,
            item.calendar_activity_status
          )
        }
      >
        <CardHeader
          avatar={
            <Avatar sx={{ bgcolor: "purple" }} aria-label="recipe">
              <WorkIcon />
            </Avatar>
          }
          action={
            item.document_id ? (
              <Button
                aria-label="download"
                variant="contained"
                onClick={() => handleDownload(item.document_id)}
                sx={{
                  backgroundColor: "purple",
                  borderRadius: "30px",
                  fontSize: "10px",
                  fontWeight: "bold",
                  "&:hover": { backgroundColor: "purple" },
                  "&:active": { backgroundColor: "purple" },
                }}
              >
                Download
              </Button>
            ) : isActivityDetailsOnly || isClosed ? (
              <Button
                aria-label="generate-pdf"
                variant="contained"
                onClick={handleGeneratePDF}
                sx={{
                  backgroundColor: "purple",
                  borderRadius: "30px",
                  fontSize: "10px",
                  fontWeight: "bold",
                  "&:hover": { backgroundColor: "purple" },
                  "&:active": { backgroundColor: "purple" },
                }}
              >
                {isLoading ? (
                  <CircularProgress size={20} color="inherit" />
                ) : (
                  "Generate PDF"
                )}
              </Button>
            ) : (
              <Button
                aria-label="close"
                variant="contained"
                onClick={openDialog}
                sx={{
                  backgroundColor: "purple",
                  borderRadius: "30px",
                  fontSize: "10px",
                  fontWeight: "bold",
                  "&:hover": { backgroundColor: "purple" },
                  "&:active": { backgroundColor: "purple" },
                }}
              >
                Close
              </Button>
            )
          }
          title={item?.ticket_1}
          subheader={dayjs(item?.activity_date).format("lll")}
        />
        <Box>
          <MenuList>
            <MenuItem>
              <ListItemIcon>
                <InfoIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Status</ListItemText>
              <Typography
                variant="body2"
                sx={{
                  color: isActivityDetailsOnly ? "#236334" : "text.secondary",
                  fontWeight: isActivityDetailsOnly ? 600 : 400,
                }}
              >
                {getStatusName(item.calendar_activity_status)}
              </Typography>
            </MenuItem>
            <MenuItem>
              <ListItemIcon>
                <CalendarMonthIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Visit Date</ListItemText>
              <Typography variant="body2" sx={{ color: "text.secondary" }}>
                {dayjs(item?.activity_date).format("lll")}
              </Typography>
            </MenuItem>
          </MenuList>
        </Box>
        <DialogComponent />
      </Card>
      <Modal
        open={isModalOpen}
        onClose={handleCloseModal}
        aria-labelledby="pdf-modal-title"
        aria-describedby="pdf-modal-description"
      >
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            width: "50%",
            maxWidth: "700px",
            height: "90%",
            bgcolor: "background.paper",
            boxShadow: 24,
            p: 4,
            borderRadius: 2,
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Typography
            id="pdf-modal-title"
            variant="h6"
            component="h2"
            gutterBottom
            sx={{ textAlign: "center" }}
          >
            Farm Management PDF
          </Typography>
          <Box sx={{ flexGrow: 1, overflow: "auto", my: 2 }}>
            {pdfData && (
              <Document file={pdfData} onLoadSuccess={onDocumentLoadSuccess}>
                {Array.from(new Array(numPages), (el, index) => (
                  <Page
                    key={`page_${index + 1}`}
                    pageNumber={index + 1}
                    width={600}
                  />
                ))}
              </Document>
            )}
          </Box>
          <Box sx={{ mt: 2, display: "flex", justifyContent: "flex-end" }}>
            <Button onClick={handleCloseModal} sx={{ mr: 1 }}>
              Close
            </Button>
            <Button onClick={handleSavePDF} variant="contained" color="primary">
              Download
            </Button>
          </Box>
        </Box>
      </Modal>
    </>
  );
};
