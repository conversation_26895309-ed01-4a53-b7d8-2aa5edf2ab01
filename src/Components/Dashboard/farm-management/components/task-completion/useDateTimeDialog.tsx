
import React from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, But<PERSON>, TextField } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import { useSelector } from "react-redux";
import { farmManagementSlice } from '../../slice/farmManagement.slice';
interface UseDateTimeDialogProps {
  calendarId: string;
}

export const {useCompleteFormMutation} = farmManagementSlice;

const useDateTimeDialog = ({ calendarId }: UseDateTimeDialogProps) => {
  const [open, setOpen] = React.useState(false);
  const [selectedDate, setSelectedDate] = React.useState<Dayjs | null>(null);
  const [selectedTime, setSelectedTime] = React.useState<Dayjs | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isClosed, setIsClosed] = React.useState(false);
  const [showTimePicker, setShowTimePicker] = React.useState(false);
  const [completeForm] = useCompleteFormMutation();

  const handleDateChange = (newValue: Dayjs | null) => {
    setSelectedDate(newValue);
    if (newValue) {
      setShowTimePicker(true);
    }
  };

  const handleTimeChange = (newValue: Dayjs | null) => {
    if (newValue) {
      setSelectedTime(newValue);
      setShowTimePicker(false); 
    }
  };

  const openDialog = (event: React.MouseEvent) => {
    event.stopPropagation();
    setOpen(true);
    setSelectedDate(null);
    setSelectedTime(null);
    setShowTimePicker(false);
  };

  const closeDialog = () => {
    setOpen(false);
    setSelectedDate(null);
    setSelectedTime(null);
    setShowTimePicker(false);
  };

  const handleSubmit = async () => {
    setIsClosed(true);
    if (!selectedDate || !selectedTime) return;
    setIsLoading(true);

    const combinedDateTime = selectedDate
      .hour(selectedTime.hour())
      .minute(selectedTime.minute())
      .second(0);
    
    try {
      const response = await completeForm({
        calendarId,
        user_completion_date_time: combinedDateTime, 
      });
      closeDialog();
    }  catch (error) {
      console.error('error while completing form',error);
    }
  };
                         
  const formatDateTime = (date: Dayjs | null, time: Dayjs | null) => {
    if (!date) return '';
    const dateStr = date.format('YYYY-MM-DD');
    const timeStr = time ? time.format('HH:mm') : '';
    return timeStr ? `${dateStr} ${timeStr}` : dateStr;
  };

  const DialogComponent = () => (
    <Dialog
      open={open}
      onClose={closeDialog}
      onClick={(e) => e.stopPropagation()}
    >
      <DialogTitle>Select Date and Time</DialogTitle>
      <DialogContent sx={{
        minWidth: 300,
        minHeight: 200,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        gap: 2,
        position: 'relative'
      }}>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <div style={{ position: 'relative' }}>
            {!showTimePicker ? (
              <DatePicker
                label={selectedTime ? "Selected: " + formatDateTime(selectedDate, selectedTime) : "Date"}
                value={selectedDate}
                onChange={handleDateChange}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            ) : (
              <div style={{ position: 'absolute', top: 0, left: 0, right: 0 }}>
                <TimePicker
                  label="Select Time"
                  open={true}
                  onClose={() => setShowTimePicker(false)}
                  value={selectedTime}
                  onChange={handleTimeChange}
                  renderInput={(params) => <TextField {...params} fullWidth style={{ visibility: 'hidden', height: 0 }} />}
                />
              </div>
            )}
          </div>
        </LocalizationProvider>
      </DialogContent>
      <DialogActions>
        <Button onClick={closeDialog}>Cancel</Button>
        {selectedTime && (
          <Button onClick={() => setShowTimePicker(true)}>
            Change Time
          </Button>
        )}
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          disabled={!selectedDate || !selectedTime || isLoading}
        >
          {isLoading ? 'Submitting...' : 'Submit'}
        </Button>
      </DialogActions>
    </Dialog>
  );

  return { DialogComponent, openDialog, isClosed };
};

export default useDateTimeDialog;