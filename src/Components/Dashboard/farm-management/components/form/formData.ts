import { globalReferencesMapper, extractBasedOnLanguageMod } from '../../../../../Utilities/Common/utils';

const labelL10nMapper = {
    notes : {
        en : "Notes",
        ta : "குறிப்புகள்",
        mr : "नोट्स"
    },
    target : {
        en : "Target",
        ta : "இலக்கு",
        mr : "लक्ष्य"
    },
    target_date : {
        en : "Target Date",
        ta : "இலக்கு தேதி",
        mr :"लक्ष्य तारीख"
    },
    image : {
        en : "Image",
        ta : "ஊடகம்",
        mr : "मीडिया"
    },
    advices : {
        en : "Advices",
        ta : "ஆலோசனை",
        mr : "सल्ला"
    }
}

function capitalizeFirstLetter(str : string) {
    if (!str || typeof str !== 'string') return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
}

export const fm_form_data = (config: any, data : any, userLanguage: string) => {
    // Function to get options based on categoryId
    const getOptionsFormat = (categoryId: number) => {
    
        return globalReferencesMapper(categoryId)
        ?.sort((a : any, b : any) => a?.reference_information?.farm_management?.order_priority - b?.reference_information?.farm_management?.order_priority)
        ?.map((item: any) => ({
            value: item?.reference_id,
            label: extractBasedOnLanguageMod(item?.reference_name_l10n, userLanguage)
        }));
    };
    const getOptionsFormatForRaw = (options: []) => {
        return options
        ?.sort((a : any, b : any) => a?.order_priority - b?.order_priority)
        ?.map((item: any) => ({
            value: item?.value,
            label: extractBasedOnLanguageMod(item?.label_l10n, userLanguage)
        }));
    };
    // console.log("categoryId == fm", getOptionsFormat(10702100))
    // Extract options and map them to getOptionsFormat results
    const formattedOptions = config?.form_fields.reduce((acc: any, field: any) => {
        try {
            const optionsPath : [] = field.options; // e.g., "#/reference/options/10001500"
        const categoryId = field?.category_id;
        if (categoryId) {
            acc[field.key] = getOptionsFormat(categoryId);
        }
        if (Array.isArray(optionsPath)) {
            acc[field.key] = getOptionsFormatForRaw(optionsPath)  
        } 
        return acc;
        } catch (error) {
            console.log(error, "fm formData error")
        }
    }, {});

    // Extract labels and map them to getOptionsFormat results
    const localizationKeys: { [key: string]: any } = {
        notes: labelL10nMapper.notes,
        target: labelL10nMapper.target,
        target_date: labelL10nMapper.target_date,
        image: labelL10nMapper.image
    };
    
    // Extract labels and map them to getOptionsFormat results
    const formattedLabels = config?.form_fields.reduce((acc: any, field: any) => {
        const labelPath = field.label; // e.g., "#/reference/label/Breeds"
        const label = labelPath?.split('/').pop(); // Extract the last segment of the path, e.g., "Breeds"
        
        if (label) {
            if (field.key === "current") {
                acc[field.key] = extractBasedOnLanguageMod(data?.question_name_l10n, userLanguage);
            } else if (userLanguage !== "en" && localizationKeys[field.key]) {
                acc[field.key] = extractBasedOnLanguageMod(localizationKeys[field.key], userLanguage);
            } else {
                acc[field.key] = capitalizeFirstLetter(label); // Map the field key to the extracted label
            }
        }
    
        return acc;
    }, {});


    return {
        reference: {
            options: formattedOptions,
            label: formattedLabels
        },
    };
};
