import { <PERSON>, <PERSON><PERSON>, Chip, Typography, Stack } from "@mui/material";
import { useKHF } from "@krushal-it/krushal-hook-form";
import {
  webComponentMap,
  KrushalHookFormWeb,
} from "@krushal-it/krushal-hook-form/web";
import { FormProvider } from "react-hook-form";
import config from "./config.json";
import {
  useLazyGetFmQuestionsQuery,
  useSaveFmQuestionMutation,
} from "../../slice/farmManagement.slice";
import { DevTool } from "@hookform/devtools";
import { useEffect } from "react";
import { updateFormSelections } from "../../utils";
import { Spin } from "antd";
import { KHFImageV2 } from "./KHFImageV2";

type FarmManagementFormProps = {
  dispatch: any;
  initialState: any;
};
const statusId = [**********, **********, **********];
const FarmManagementForm = ({
  dispatch,
  initialState,
}: FarmManagementFormProps) => {
  const questionId = initialState?.selectedQuestion?.question_id;
  const questionTitle = initialState?.selectedQuestion?.title;
  const careCalendarId = initialState?.selectedTask?.taskId;
  const activityStatus = initialState?.selectedTask?.calendar_activity_status;
  const isVisitCompleted = initialState?.selectedTask?.calendar_activity_status===**********;

  const readOnly = statusId.includes(activityStatus);
  const [getFmQuestions, { data, isLoading, isError, isFetching }] = useLazyGetFmQuestionsQuery();

  useEffect(() => {
    if (questionId && careCalendarId) {
      getFmQuestions({ question_id: questionId, calendar_id: careCalendarId });
    }
  }, [questionId, careCalendarId, getFmQuestions]);

  
  console.log(questionId,'questionId');

  const [saveFmQuestion, { isLoading: isSaving }] = useSaveFmQuestionMutation();
  console.log("FarmManagementForm", data, isLoading);

  const methods = useKHF(
    {
      shouldUnregister: true,
      mode: "all",
    },
    {
      config: data?.config, // need to pass your json configuration
      readOnly: readOnly,
      componentMap: {...webComponentMap,
        media : KHFImageV2
      },
    }
  );
  const onSubmit = async (payload: any) => {
    console.log("data fm", payload);
    const updatedPayload = await updateFormSelections(
      data?.withoutTransformData,
      payload
    );
    console.log("updatedPayload", updatedPayload);
    saveFmQuestion({
      question_id: questionId,
      calendar_id: careCalendarId,
      payload: updatedPayload,
    });
  };

  useEffect(() => {
    methods.reset(data?.resolveData);
  }, [data, methods.reset]);

  if (isLoading || isSaving || isFetching)
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
        }}
      >
        <Spin size="large" />
      </Box>
    );

  if (!careCalendarId || !questionId) return null;

  if (!data?.config && !isVisitCompleted)
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
        }}
      >
        <Typography variant="h5" color="#999" textAlign="center">
          Please select a question
        </Typography>
      </Box>
    );

  if (isError)
    return (
      <Box
        sx={{ display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <Chip label="Something went wrong" color="error" />
      </Box>
    );
  
  return (
    <Box>
      <Typography variant="h5">{questionTitle}</Typography>
      <FormProvider {...methods}>
        <KrushalHookFormWeb />
      </FormProvider>
      {!isVisitCompleted && (
      <Stack justifyContent="center" direction="row">
        <Button
          sx={{ mt: 2 }}
          variant="contained"
          onClick={methods.handleSubmit(onSubmit)}
          disabled={readOnly}
        >
          Submit
        </Button>
      </Stack>
      )}
    </Box>
  );
};

export default FarmManagementForm;
