import { Box, Card, CardMedia, Chip, Dialog, DialogContent, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import React, { useState } from "react";

export const KHFImageV2 = ({ field }) => {
  const [open, setOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);

  
  const baseUrl = JSON.parse(process.env.REACT_APP_ENV_JSON);
  const url = baseUrl.SERVICE_END_POINTS.BACK_END;
  const value = field?.selections?.values;

  const handleClickOpen = (image) => {
    setSelectedImage(image);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSelectedImage(null);
  };

  return (
    <Box display="flex" justifyContent="flex-start" alignItems="flex-start">
      {value?.length > 0 && value[0].value?.length > 0 ? (
        value[0].value.map((item) => (
          <Card
            key={item.document_id}
            sx={{ maxWidth: 345, boxShadow: 3, margin: 1 }}
            onClick={() => handleClickOpen(item)}
          >
            <CardMedia
              component="img"
              image={`${url}/media/${item?.document_id}`}
              alt="Krushal Image"
              sx={{
                objectFit: "cover",
                height: "auto",
                maxHeight: 100,
                width: 100,
              }}
            />
          </Card>
        ))
      ) : (
        <Box p={1}>
          <Chip label="Upload media from mobile app" />
        </Box>
      )}

      {/* Modal Dialog to display the image in full size */}
      <Dialog open={open} onClose={handleClose} maxWidth="md">
        <DialogContent sx={{ position: 'relative' }}>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
          {selectedImage && (
            <CardMedia
              component="img"
              image={`${url}/media/${selectedImage.document_id}`}
              alt="Krushal Image"
              sx={{ width: "100%", height: "auto" }}
            />
          )}
        </DialogContent>
      </Dialog>
    </Box>
  );
};
