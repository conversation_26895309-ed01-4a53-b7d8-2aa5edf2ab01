import { createContext, useContext, useState } from 'react';

const TaskContext = createContext(null);

export const TaskProvider = ({ children }) => {
  const [selectedTaskId, setSelectedTaskId] = useState(null);
  const [shouldAutoOpen, setShouldAutoOpen] = useState(false);
  const [triggerScroll, setTriggerScroll] = useState(false);

  const scrollToLastCompleted = () => {
    setTriggerScroll(true);
  };

  return (
    <TaskContext.Provider value={{ 
      selectedTaskId, 
      setSelectedTaskId,
      shouldAutoOpen,
      setShouldAutoOpen,
      triggerScroll,
      setTriggerScroll,
      scrollToLastCompleted
    }}>
      {children}
    </TaskContext.Provider>
  );
};

export const useTaskContext = () => {
  const context = useContext(TaskContext);
  return context;
};
