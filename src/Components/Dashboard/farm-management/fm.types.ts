export interface FarmManagementTypes {
  data: Daum[]
  calendar_activity_status: number
  document: Document
}

export interface Daum {
  title: string
  title_l10n: TitleL10n
  status: string
  folders: Folder[]
}

export interface TitleL10n {
  en: string
}

export interface Folder {
  title: string
  title_l10n: TitleL10n2
  topic_priority: number
  question_name_l10n: QuestionNameL10n
  total_question: number
  total_answered: number
  status: string
  folders: Folder2[]
}

export interface TitleL10n2 {
  en: string
  mr: string
}

export interface QuestionNameL10n {
  en: string
  mr: string
}

export interface Folder2 {
  question_id: string
  title: string
  type: string
  topic_question_priority: number
  farmer_input_required: number
  question_name_l10n: QuestionNameL10n2
  topic_name_l10n: TopicNameL10n
}

export interface QuestionNameL10n2 {
  en: string
  mr: string
}

export interface TopicNameL10n {
  en: string
  mr: string
}

export interface Document {
  client_document_information: any
  document_information: DocumentInformation
}

export interface DocumentInformation {
  url: string
  fileName: string
  fileType: string
  entity_1_id: string
  entity_name: string
  document_type: string
  localFolderName: string
  document_type_id: number
  entity_1_type_id: number
  serverFolderName: string
} 

export type FmCustomerData = FmCustomer[]

export interface FmCustomer {
  customer_id: string
  customer_name_l10n: CustomerNameL10n
  mobile_number: string
  customer_visual_id: string
  alternate_mobile_number : string
  village : {
    en: string
    gu?: string
    mr?: string
  },
  total_visits_completed: string; 
  last_visit_date :string,
  next_visit_date:string

}
export interface FmVisitCompleted{
  category: string;
  topic_name: string;
  question_name: string;
  current: string[];
  target: string[];
  target_date: string[];
  advice: string[];
  notes: string;
  image: string[];
  score: number;
  answered: boolean;
}

export interface CustomerNameL10n {
  ul: string
}

export interface FmTaskListType {
  task: Task[]
  customer: Customer
}

export interface Task {
  calendar_activity_status: number
  completion_date: string
  activity_date: string
  care_calendar_id: string
  ticket_1: string
}

export interface Customer {
  customer_id: string
  customer_name_l10n: CustomerNameL10n
  mobile_number: string
  customer_visual_id: string
}

