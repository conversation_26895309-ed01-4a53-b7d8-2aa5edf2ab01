import { Input, Button, Space, Select, DatePicker } from "antd";
import {
  SearchOutlined,
  FilterFilled,
  CalendarOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";

export const getColumnSearchProps = (
  dataIndex,
  handleSearch,
  handleReset,
  searchInput,
  searchedColumn,
  options,
) => ({
  filterDropdown: ({
    setSelectedKeys,
    selectedKeys,
    confirm,
    clearFilters,
  }) => (
    <div
      style={{
        padding: 8,
      }}
    >
      {dataIndex === "category" ||
      dataIndex === "status" ||
      dataIndex === "role" || dataIndex === "activity_category_name" ? (
        <Select
          showSearch
          placeholder={`Select ${dataIndex}`}
          optionFilterProp="children"
          value={selectedKeys[0]}
          style={{ display: "block", marginBottom: "10px" }}
          virtual={false}
          onChange={(value) => setSelectedKeys(value ? [value] : [])}
          filterOption={(input, option) =>
            option?.children?.toLowerCase()?.indexOf(input?.toLowerCase()) >= 0
          }
        >
          {options?.map((option) => (
            <Select.Option key={option?.value} value={option?.text}>
              {option?.text}
            </Select.Option>
          ))}
        </Select>
      ) : (dataIndex === "activityDate" || dataIndex === "completionDate")? (
        <DatePicker.RangePicker
          value={
            selectedKeys[0] && selectedKeys[1]
              ? [dayjs(selectedKeys[0]), dayjs(selectedKeys[1])]
              : []
          }
          onChange={(dates) => setSelectedKeys([dates[0], dates[1]])}
          style={{ display: "block", marginBottom: "10px" }}
          format={"DD MMM, YYYY"}
        />
      ) : (
        <Input
          ref={searchInput}
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={(e) =>
            setSelectedKeys(e.target.value ? [e.target.value] : [])
          }
          onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
          style={{
            marginBottom: 8,
            display: "block",
          }}
        />
      )}
      <Space>
        <Button
          type="primary"
          onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
          icon={<SearchOutlined />}
          size="small"
          style={{
            width: 90,
          }}
        >
          Search
        </Button>
        <Button
          onClick={() =>
            clearFilters && handleReset(clearFilters, confirm, dataIndex)
          }
          size="small"
          style={{
            width: 90,
          }}
        >
          Reset
        </Button>
      </Space>
    </div>
  ),
  filterIcon: (filtered) =>
    dataIndex === "category" ||
    dataIndex === "status" ||
    dataIndex === "role" ? (
      <FilterFilled
        style={{
          color: filtered ? "#1677ff" : undefined,
        }}
      />
    ) : (dataIndex === "activityDate" || dataIndex === "completionDate") ? (
      <CalendarOutlined
        style={{
          color: filtered ? "#1677ff" : undefined,
        }}
      />
    ) : (
      <SearchOutlined
        style={{
          color: filtered ? "#1677ff" : undefined,
        }}
      />
    ),
  onFilter: (value, record) => {
    if ((dataIndex === "activityDate" || dataIndex === "completionDate") && record[dataIndex]) {
      const date = dayjs(record[dataIndex], "DD MMM, YYYY");
      const startDate = dayjs(value[0], "DD MMM, YYYY");
      const endDate = dayjs(value[1], "DD MMM, YYYY");

      return date.isBetween(startDate, endDate, null, "[]");
    } else {
      return (
        record[dataIndex]
          ?.toString()
          ?.toLowerCase()
          ?.includes(value.toLowerCase()) || false
      );
    }
  },
  onFilterDropdownOpenChange: (visible) => {
    if (visible) {
      setTimeout(() => searchInput?.current?.select(), 100);
    }
  },
  render: (text) => {
    return searchedColumn === dataIndex ? <span>{text}</span> : text;
  },
});
