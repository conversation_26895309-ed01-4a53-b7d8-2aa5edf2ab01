import React, { useState } from 'react';

const Modal = ({ isOpen, onClose, items }) => {
    if (!isOpen) {
        return null;
      }
    
      return (
        <div className="popup-overlay">
          <div className="popup">
            <button className="popup-close-button" onClick={onClose}>
              Close
            </button>
            <h2>This is a Pop-up</h2>
            <p>Pop-up content goes here.</p>
          </div>
        </div>
      );
    };

export default Modal
