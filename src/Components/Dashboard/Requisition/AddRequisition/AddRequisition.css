.goback {
    font-weight: 400;
    size: 15px;
    line-height: 17.7px;
    align-items: center;
    border: none;
    background-color: rgb(255, 254, 254);
    color: #757575;
    display: flex;
    justify-content: left;

}

.requsitiondetails {
    font-weight: 700;
    size: 20px;
    line-height: 27px;
    align-items: center;
    color: #808080;
}

.requsitionlist {
    display: flex;
    flex-direction: column;

}

.divtag {
    display: flex;
    margin-bottom: 25px;
}

.tabsrow {
    font-weight: 550;
    width: 100%;
    height: 52px;
    top: 203px;
    left: 246px;
    background-color: #252525;
    color: aliceblue;
    display: flex;
    flex-direction: row;
    align-items: center;

}

.tabinfo {
    height: 100%;
    border: none;
    color: aliceblue;
    background-color: #252525;
    margin-left: 50px;
    margin-right: 15px;
    justify-content: center;
    align-items: center;
    font-size: large;
}

.active {
    color: orange;
}

.addrequisition {
    font-weight: 700;
    font-size: 22px;
}

.requisitioninfo {
    display: flex;
    justify-content: space-between;
    /* margin: 25px; */
    margin-top: 25px;
    margin-bottom: 25px;
}

.nametags {
    font-weight: 500;
    font-size: large;
    color: rgb(40, 40, 41);
    display: flex;
    justify-content: left;

}

.valuetag {
    font-weight: 500;
    font-size: 20px;
    color: rgb(123, 123, 124);
    display: flex;
    justify-content: left;
}

.inputdata {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    margin-bottom: 5px;
}

.inputtagname {
    font-weight: 300;
    font-size: medium;
    color: rgb(40, 40, 41);
    display: flex;
    justify-content: left;
    margin-top: 5px;
    margin-bottom: 5px;

}

.inputfield {
    border-radius: 6px;
    border-style: solid;
    width: 400px;
    height: 40px;
    border-color: rgb(223, 223, 223) !important;
    padding-left: 15px;
    border-top-color: rgb(223, 223, 223);
    width: 200px;
}

/* .dropdown.requisitiontype{
    border-radius: 6px;
    border-style: solid;
    width: 400px;
    height: 40px;
    border-color: rgb(223, 223, 223) !important;
    padding-left: 15px;
    border-top-color: rgb(223, 223, 223);
    width: 200px;
    color: #252525;
} */

.requisitiontype {
    border: 1px solid rgb(102, 102, 102);
    padding: 10px 20px 10px 20px;

    border-radius: 10px;
}


.delete {
    margin-top: 35px;
    border-radius: 6px;
    border-style: solid;
    border-color: rgb(223, 223, 223) !important;
    padding-left: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.addmedicine {
    margin-top: 35px;
    border-radius: 6px;
    border-style: solid;
    border-color: rgb(223, 223, 223) !important;
    padding-left: 15px;
    display: flex;
    justify-content: left;
    align-items: center;
    background-color: rgb(111, 114, 111);
    width: 170px;
    height: 40px;
    color: aliceblue;
}

.save {
    margin-top: 35px;
    border-radius: 6px;
    border-style: solid;
    border-color: rgb(224, 116, 44) !important;
    padding-left: 15px;
    display: flex;
    justify-content: left;
    align-items: center;
    background-color: rgb(224, 116, 44);
    width: 170px;
    height: 40px;
    color: aliceblue;
}


.requsitionblocks {
    display: flex;
    flex-direction: row;
    flex: 1;
    justify-content: space-around;
    margin-top: 15px;

}

.requisitionitem {
    margin-bottom: 25px;
    width: 250px;
}


.delete-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    font-size: 16px;
    background-color: #ff5050;
    /* Optional: Add a background color */
    color: #fff;
    /* Optional: Set the text color */
    border: none;
    /* Optional: Remove the button border */
    cursor: pointer;
}

/* .modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  .modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    padding: 20px;
    text-align: center;
  } */