import { tab } from "@testing-library/user-event/dist/tab";
import React, { useEffect, useState } from "react";
import "./AddRequisition.css";
import { json, useNavigate } from "react-router-dom";
import { AutoComplete } from "antd";
import { useSelector, useDispatch } from "react-redux";
import { BASE_URL, instance } from "../../../../Services/api.service";
import { clearUserToken } from "../../../user/action";
import { Container, Modal, Row, Col } from "react-bootstrap";
import CloseIcon from "@mui/icons-material/Close";
import "bootstrap/dist/css/bootstrap.min.css"; // Import Bootstrap CSS
import SearchIcon from "@mui/icons-material/Search";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import Fab from "@mui/material/Fab";
import Box from "@mui/material/Box";
import { unitMappingByMedId} from "../../../../Utilities/Common/utils"

const AddRequisition = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userToken = useSelector((state) => state.user.userToken);

  const [tabColour, setTabColour] = useState("white");
  const [activeTab, setActiveTab] = useState("Details");
  const [requisitionname, setRequisitionname] = useState("");
  const [requisitiontype, setRequisitiontype] = useState("");
  const [requisitiontypeid, setRequisitiontypeid] = useState("");
  const [requisitiontypeuuid, setRequisitiontypeuuid] = useState("");
  const [fullfillername, setFullfillername] = useState("");
  const [staffList, setStaffList] = useState([]);
  const [medicineList, setMedicineList] = useState([]);
  const [selectedMed, setSelectedMed] = useState(true);
  const [filteredStaff, setFilteredStaffList] = useState(staffList);
  const [quantity, setQuantity] = useState("");
  const [alteredMedList, setAlteredMedList] = useState([]);
  const [searchmed, setsearchmed] = useState("");
  const [flag, setFlag] = useState(false)
  const [matchId, setMatchId] = useState("")

  const requisitionTypeData = ["Vet", "Paravet"];

  const [formData, setFormData] = useState([
    // { id: 1, medicine: '', unit: '', pack:'', quantity:''},
    // { id: 2, medicine: '', unit: '', pack:'', quantity:''},
  ]);

  const getStaffs = async (params) => {
    let reqBody = {};
    if (params !== {}) {
      reqBody = { ...reqBody, ...params };
    }
    try {
      const response = await instance({
        url: "/staffs/all",
        method: "POST",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      console.log(response);
      if (response.status === 201) {
        // console.log("Staff List", response.data.data);
        setStaffList(response.data.data);
      }

      // setFarmersListLoader(false);
    } catch (error) {
      console.log(error);
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const filterStaff = () => {
    console.log("requisitiontype", requisitiontype);

    if (requisitiontype === "Vet" || requisitiontype === "Paravet") {
      const updatedstaff = staffList.filter(
        (item) => item.staff_type_l10n.ul == requisitiontype
      );
      console.log("updatedstaff", updatedstaff);
      setFilteredStaffList(updatedstaff);
    } else if (requisitiontype === "Requisition Type") {
      setFilteredStaffList(staffList);
    } else {
      setFilteredStaffList(staffList);
    }
  };

  // const getMedicine = async (params) => {
  //   let reqBody = {};
  //   if (params !== {}) {
  //     reqBody = { ...reqBody, ...params };
  //   }
  //   try {
  //     const response = await instance({
  //       url: "/v3/medicines",
  //       method: "GET",
  //       data: reqBody,
  //       headers: {
  //         "Content-Type": "application/json",
  //         token: userToken.accessToken,
  //       },
  //     });
  //     if (response.status === 200) {
  //       setMedicineList(response.data.data);
  //       setAlteredMedList(response.data.data);
  //     }
  //   } catch (error) {
  //     console.log(error);
  //     if ("response" in error && error.response.status === 401) {
  //       dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
  //     }
  //   }
  // };

  const CreateRequisition = async (params) => {
    let reqBody = {};
    if (params !== {}) {
      reqBody = { ...reqBody, ...params };
    }
    try {
      const response = await instance({
        url: "v3/requisitions",
        method: "POST",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status === 200 || response.status === 201) {
        console.log("created requisitin ", response);
        navigate(-1);
      }
    } catch (error) {
      console.log(error);
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const handelRequisitionname = (e, index) => {
    const json_data = JSON.parse(e.target.value);
    const value = json_data.staff_name_l10n.en
      ? json_data.staff_name_l10n.en
      : json_data.staff_name_l10n.ul;
    // console.log("this is requisition name ",JSON.parse(e.target.value))
    console.log("new value", value);
    setRequisitionname(value);
    setRequisitiontypeid(json_data.staff_type_id);
    setRequisitiontypeuuid(json_data.staff_id);
  };

  const handelRequisitiontype = (e) => {
    // Access the event object (e) and extract the value
    console.log(e.target.value);
    const value = e.target.value;
    setRequisitiontype(value);
  };

  // const handleChange = (e) => {
  //     console.log("Function called")
  //     const json_data = JSON.parse(e.target.value)
  //     let new_item = formData.filter(item => item.id === json_data.reference_id)
  //     if (new_item.length === 0) {
  //         let arr = { id: json_data.reference_id, medicine: json_data.reference_name, unit: json_data.reference_information.unit, pack: json_data.reference_information.pack_size, quantity: 1, total_size: 1 * json_data.reference_information.pack_size }
  //         let arr2 = [...formData, arr]
  //         setFormData(arr2);
  //         let selectedRows = alteredMedList.filter(function (item2) {
  //             return !arr2.find(function (item) {
  //                 return item.medicine.toLowerCase() == item2.reference_name_l10n.ul.toLowerCase()
  //             })
  //         })
  //         setAlteredMedList(selectedRows)
  //         setModalVisible(false)

  //         //     setSelectedMed(false)

  //         //     let newarr = medicineList.filter(({ reference_id: id1 }) => !formData.some(({ id: id2 }) => id2 === id1));
  //         //     setAlteredMedList(newarr)
  //     }
  // };

  const handleNewChange = (e) => {
    console.log("Function called", e);
    const json_data = e;
    let new_item = formData.filter((item) => item.id === json_data.medicine_id);
    if (new_item.length === 0) {
      let arr = {
        id: json_data.medicine_id,
        medicine: json_data.medicine_name_l10n.ul,
        unit: unitMappingByMedId(json_data.medicine_id) || 'NA',
        pack: json_data.medicine_pack_size,
        quantity: 1,
        total_size: 1 * json_data.medicine_pack_size,
      };
      let arr2 = [...formData, arr];
      setFormData(arr2.reverse());
      let selectedRows = alteredMedList.filter(function (item2) {
        return !arr2.find(function (item) {
          return (
            item.medicine.toLowerCase() ==
            item2.medicine_name_l10n.ul.toLowerCase()
          );
        });
      });

      setAlteredMedList(selectedRows);
      setModalVisible(false);
    }
  };

  const handleDelete = (id) => {
    console.log("handle delete", id);
    const updatedData = formData.filter((item) => item.id != id);

    console.log("deleted data", updatedData);

    setFormData(updatedData);
    const updateMedList = medicineList.filter(
      (item) => item.medicine_id === id
    );

    console.log("altered arr", updateMedList);

    let newarr = alteredMedList;

    newarr.push(updateMedList[0]);
    setAlteredMedList(newarr);
    console.log("deletion complete", formData);
  };

  const handleCreateRequisition = () => {
    console.log("this is formdata", formData);
    if (formData.length > 0) {
      console.log("save the changes", formData);

      let newArry = [];

      formData.map(function (item, index) {
        newArry.push({
          medicine_id: item.id,
          unit: item.unit,
          quantity: item.total_size,
        });
      });

      const reqBody = {
        requisitioner_type_id: requisitiontypeid,
        requisitioner_uuid: requisitiontypeuuid,
        fullfiller_type_id: null,
        fullfiller_uuid: null,
        approver_type_id: null,
        approver_uuid: null,
        requisition_line_items: newArry,
        status_id: 1000820002,
      };

      console.log("post body", reqBody);

      CreateRequisition(reqBody);
    } else {
      alert("no data to be added");
    }
  };

  const handlequantity = (e, id, field) => {
    const value = e.target.value
    console.log(matchId,"matchedId")
    if (value && value > 0) {
      // need to do validation
      setFlag(false)
    }else{
      setMatchId(id)
      setFlag(true)
    }
    const updatedFormData = formData.map(
      (item) =>
        item.id === id
          ? {
              ...item,
              [field]: e.target.value,
              ["total_size"]: item.pack * e.target.value,
            }
          : item
      // item.id === id ? { ...item, ['total_size']: item.pack*e.target.value } : item
    );
    setFormData(updatedFormData);
  };

  const handelSelectedMedicine = (e, index) => {
    const json_data = JSON.parse(e.target.value);
    console.log("selected medine ", json_data);
  };

  const getMedicineFromGlobalRef = () => {
    if (
      global?.medicineReferences &&
      Object.keys(global?.medicineReferences).length > 0
    ) {
      const medicineArray = Object.keys(global?.medicineReferences).map(
        (key) => global?.medicineReferences[key]
      );
        setMedicineList(medicineArray);
        setAlteredMedList(medicineArray);
    }
  };

  useEffect(() => {
    getStaffs();
    // getMedicine();
    getMedicineFromGlobalRef()
  }, []);

  useEffect(() => {
    filterStaff();
  }, [requisitiontype]);

  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const openPopup = () => {
    setIsPopupOpen(true);
  };

  const closePopup = () => {
    setIsPopupOpen(false);
  };

  const [modalVisible, setModalVisible] = useState(false);

  const openModal = () => {
    setModalVisible(true);
    setsearchmed("");
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  const filterList = () => {
    let medlist = alteredMedList.filter((item) => item.medicine_pack_size);
    if (searchmed) {
      // let medlist = alteredMedList.filter(item => item.medicine_pack_size)
      return medlist.filter((listItem) =>
        listItem.medicine_name_l10n.ul
          .toLowerCase()
          .includes(searchmed.toLowerCase())
      );
    } else {
      return medlist;
    }
  };
  return (
    <div style={{ margin: "20px" }}>
      {/* <div style={{position:'fixed',height:'100%',width:'100%',alignItems:'flex-end'}}>
                <Fab color="primary" aria-label="add">
                    <AddIcon />
                </Fab>
            </div> */}
      <div style={{ zIndex: -1 }}>
        <div className="divtag">
          <div>
            <button className="goback" onClick={() => navigate(-1)}>
              Go back
            </button>
            <div className="requsitiondetails">
              Requisitons / Add New Requistion
            </div>
            <div className="addrequisition">ADD Requisition</div>
          </div>
        </div>

        <div className="tabsrow">
          <div style={{ marginLeft: "10px" }}>New Requsition</div>
        </div>
        <div>
          <div className="d-flex" style={{ marginTop: 20 }}>
            <div className="requisitionitem">
              <select
                className="requisitiontype"
                onChange={handelRequisitiontype}
              >
                <option>Requisition Type</option>
                {requisitionTypeData.map((option, index) => {
                  return <option key={index}>{option}</option>;
                })}
              </select>
            </div>

            <div className="requisitionitem">
              <select
                className="requisitiontype"
                onChange={handelRequisitionname}
              >
                <option>Requisition Name</option>
                {filteredStaff.map((reqData, index) => {
                  return (
                    <option key={index} value={JSON.stringify(reqData)}>
                      {reqData.staff_name_l10n.en
                        ? reqData.staff_name_l10n.en
                        : reqData.staff_name_l10n.ul}
                    </option>
                  );
                })}
              </select>
            </div>
          </div>
        </div>
        <div>
          <hr style={{ backgroundColor: "black" }} />
        </div>
        <div
          style={{
            width: "100%",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            height: "100px",
            flexDirection: "row",
          }}
        >
          <h4> Medicine List</h4>
          <button
            style={{ marginTop: -10 }}
            className="addmedicine"
            onClick={openModal}
          >
            <span style={{ width: "100%", marginLeft: -5 }}>
              + Add Medicine
            </span>
          </button>
        </div>
        {/* <div> */}
        {formData.map((item, index) => (
          <div className="inputdata" key={index}>
            <div>
              <div className="inputtagname">Medicine</div>
              <div
                className="inputfield"
                // type="text"
                style={{ width: 300 }}
              >
                {item.medicine}
              </div>
            </div>
            <div>
              <div className="inputtagname">Pack size/Unit</div>
              <div
                className="inputfield"
                type="text"
                style={{ width: 150 }}
                value={item.unit}
                // onChange={e => handleChange(e, item.id, 'unit')}
              >
                {item.pack} / {item.unit}
              </div>
            </div>
            <div>
              <div className="inputtagname">Qualntity</div>
              <input
                className="inputfield"
                type="text"
                style={{ width: 150 }}
                // defaultValue={item.quantity}
                value={item?.quantity}
                onChange={(e) => handlequantity(e, item.id, "quantity")}
                onKeyPress={(e) => {
                  if (!/[0-9]/.test(e.key)) {
                    e.preventDefault();
                  }
                }}
                min={1}
              />
              {flag && matchId === item.id && <span key={index} style={{color:"red", display:"block"}}>Please enter valid quantity</span>}
            </div>
            <div>
              <div className="inputtagname">Total pack size</div>
              <div
                className="inputfield"
                type="text"
                style={{ width: 150 }}
                value={item.total_size}
                // onChange={e => handleChange(e, item.id, 'unit')}
              >
                {item.total_size} {item.unit}
              </div>
            </div>
            <button className="delete" onClick={(e) => handleDelete(item.id)}>
              <DeleteIcon style={{ marginRight: 10 }} />
              Delete
            </button>
          </div>
        ))}

        {/* <select
                        className="requisitiontype"
                        onChange={e => handleChange(e)}
                        style={{width:300, marginTop:'25px'}}>
                        <option>
                            {selectedMed ? "Select Medicine" : ""}
                        </option>
                        {alteredMedList.map((each_med, index) => {
                            return (
                                <option key={index} value={JSON.stringify(each_med)}>
                                    {each_med.reference_name}
                                    
                                </option>
                            );
                        })}
                    </select> */}

        <button
          className="save"
          onClick={handleCreateRequisition}
          style={{
            opacity: formData.length > 0 ? 1 : 0.5,
            textAlign: "center",
          }}
          disabled={flag}
        >
          <span style={{ width: "100%" }}>Create</span>
        </button>
        {/* </div> */}
      </div>
      <div style={{ flex: 1, zIndex: 1 }}>
        <Modal show={modalVisible} backdrop="static" centered>
          <Modal.Title
            style={{
              padding: "16px 16px 0px 16px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <div>Select Medicine</div>

            <CloseIcon
              onClick={() => {
                closeModal();
              }}
            />
          </Modal.Title>
          <Modal.Body>
            <Container>
              {/* <Row>
                            <Col md={6}>
                            <select
                                className="requisitiontype"
                                onChange={e => handleChange(e)}
                                style={{width:300, marginTop:'25px'}}>
                                <option>
                                    {selectedMed ? "Select Medicine" : ""}
                                </option>
                                {alteredMedList.map((each_med, index) => {
                                    return (
                                        <option key={index} value={JSON.stringify(each_med)}>
                                            {each_med.reference_name}
                                            
                                        </option>
                                    );
                                })}
                            </select> 
                            </Col>
                        </Row> */}
              <div>
                <div style={{ width: "100%" }}>
                  <div
                    style={{
                      border: "1px solid black",
                      padding: 10,
                      borderRadius: 12,
                      marginTop: 20,
                    }}
                  >
                    <SearchIcon
                      onClick={() => {
                        closeModal();
                      }}
                    />
                    <input
                      onInput={(e) => setsearchmed(e.target.value)}
                      name="myInput"
                      placeholder="Search Medicine"
                      style={{ border: 0, width: "90%", outline: "none" }}
                    />
                  </div>
                </div>
                <div
                  style={{
                    height: "30vh",
                    width: "100%",
                    overflowY: "scroll",
                    marginTop: 40,
                    alignItems: "flex-start",
                  }}
                >
                  {filterList().map((each_med, index) => {
                    return (
                      <div
                        key={index}
                        onClick={() => handleNewChange(each_med)}
                        style={{
                          flex: 1,
                          marginTop: index == 0 ? 0 : -10,
                          marginBottom: index == 0 ? 0 : -10,
                          cursor: "pointer",
                        }}
                      >
                        <div
                          style={{
                            display: "flex",
                            alignItems: "flex-start",
                            justifyContent: "flex-start",
                          }}
                        >
                          {each_med.medicine_name_l10n.ul}
                        </div>
                        <hr />
                      </div>
                    );
                  })}
                </div>
              </div>
            </Container>
          </Modal.Body>
          {/* <Modal.Footer style={{justifyContent:'center'}}> */}
          {/* </Modal.Footer> */}
        </Modal>
      </div>
    </div>
  );
};

export default AddRequisition;
