import { useNavigate, useSearchParams } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useState, useCallback } from "react";
import { get_requisition_list } from "../../../router";
import { clearUserToken } from "../../user/action";
import requisitionFilters from "./requisition.filters.json";
import {
  createPageParamsFromURLString,
  OCTable,
} from "../../Common/AntTableHelper";
import { extractBasedOnLanguageMod } from "../../../Utilities/Common/utils";

const RequisitionList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const userLanguage = useSelector((state) => state.user.userLanguage);
  const userToken = useSelector((state) => state.user.userToken);

  const handlerequisitiondetailclick = (requisitioner_uuid,requisition_id) => {
    navigate(
      `/requisitionlist/requisitiondetails/${requisition_id}`,{state : {req_user_id : requisitioner_uuid}}
    );
  };

  const requisitionTableConfiguration = {
    enableSelection: false,
    defaultPageSize: 10,
    enableHorizontalScrolling: true,
    showTableHeader: false,
    omniSearchRowEnabled: false,
    enableColumnConfiguration: false,
    // enableOnChange: true,
    // getAllDataAtOnce: true,
    // disablePagination: true,
    dataTransformationRules: {
      // staff_filter_values: 'bdm_filter_values',
      // staff_name: 'bdm'
    },
    rowKey: "requisition_id",
    columns: [
      {
        title: "Requisition Id",
        key: "requisition_id",
      },
      {
        title: "Requisitioner Name",
        key: "requisitioner_name",
        render: (_, record) => {
          return (
            <div
              style={{ color: "blue", cursor: "pointer" }}
              onClick={() =>
                handlerequisitiondetailclick(
                  record?.requisitioner_uuid,
                  record?.requisition_id
                )
              }
            >
              {record?.requisitioner_name}
            </div>
          );
        },
      },
      {
        key: "requisitioner_type_l10n",
        title: "Requisitioner Type",
        render: (_, record) => {
          return (
            <div>
              {extractBasedOnLanguageMod(
                record?.requisitioner_type_l10n,
                userLanguage
              )}
            </div>
          );
        },
      },
      {
        title: "Fulfiller Name",
        key: "fullfiller_name"
      },
      {
        title: "Fulfiller Type",
        key: "fullfiller_type_l10n",
        render: (_, record) => {
          return (
            <div>
              {extractBasedOnLanguageMod(
                record?.fullfiller_type_l10n,
                userLanguage
              )}
            </div>
          );
        },
      },
      {
        title: "Approver Name",
        key: "approver_name"
      },
      {
        title: "Approver Type",
        key: "approver_type_l10n",
        render: (_, record) => {
          return (
            <div>
              {extractBasedOnLanguageMod(
                record?.approver_type_l10n,
                userLanguage
              )}
            </div>
          );
        },
      },
      {
        title: "Process status",
        key: "prescription_process_status_l10n",
        render: (_, record) => {
          return (
            <div>
              {extractBasedOnLanguageMod(
                record?.prescription_process_status_l10n,
                userLanguage
              )}
            </div>
          );
        },
      },
      {
        title: "Medicine delivery status",
        key: "prescription_medicine_delivery_status_l10n",
        render: (_, record) => {
          return (
            <div>
              {extractBasedOnLanguageMod(
                record?.prescription_medicine_delivery_status_l10n,
                userLanguage
              )}
            </div>
          );
        },
      },
      {
        title: "Invoice status",
        key: "prescription_invoice_status_l10n",
        render: (_, record) => {
          return (
            <div>
              {extractBasedOnLanguageMod(
                record?.prescription_invoice_status_l10n,
                userLanguage
              )}
            </div>
          );
        },
      },
      {
        title: "Created At",
        key: "inserted_at",
      },

      {
        title: "updated_at",
        key: "updated_at",
      },
    ],
    columnConfiguration: {
      requisition_id: {
        headingText: "Requisition Id",
        // enableFilter: true,
        type: "string",
      },
      requisitioner_name: {
        headingText: "Requisitioner Name",
        enableFilter: true,
        type: "string",
        includeInOmnisearch: true,
      },
      requisitioner_type_l10n: {
        headingText: "Requisitioner Type",
        type: "single_select_array",
        dataType: "int",
        enableFilter: true,
        includeInOmnisearch: true,
        filterValuesKey: "staff_filter_values",
      },
      fullfiller_name: {
        headingText: "Fulfiller Name",
        type: "string",
        enableFilter: true,
        includeInOmnisearch: true,
      },
      fullfiller_type_l10n: {
        headingText: "fulfiller_type Type",
        type: "single_select_array",
        dataType: "int",
        enableFilter: true,
        includeInOmnisearch: true,
        filterValuesKey: "fullfiller_type_filter_values",
      },
      approver_name: {
        headingText: "Approver Name",
        type: "string",
        enableFilter: true,
        includeInOmnisearch: true,
      },
      approver_type_l10n: {
        headingText: "Approver Type",
        type: "single_select_array",
        dataType: "int",
      },
      prescription_invoice_status_l10n: {
        headingText: "Requisitioner Type",
        type: "single_select_array",
        dataType: "int",
        enableFilter: true,
        filterValuesKey: "prescription_invoice_status_filter_values",
        includeInOmnisearch: true,
      },
      prescription_medicine_delivery_status_l10n: {
        headingText: "Requisitioner Type",
        type: "single_select_array",
        dataType: "int",
        enableFilter: true,
        includeInOmnisearch: true,
        filterValuesKey: "prescription_medicine_delivery_status_filter_values",
      },
      prescription_process_status_l10n: {
        headingText: "Requisitioner Type",
        type: "single_select_array",
        dataType: "int",
        enableFilter: true,
        includeInOmnisearch: true,
        filterValuesKey: "prescription_process_status_filter_values",
      },
      inserted_at: {
        headingText: "Created At",
        enableFilter: true,
        type: "dayjs_range",
        formatType: "date",
        format: "h:mm a, DD-MMM-YYYY",
      },
      updated_at: {
        headingText: "Updated At",
        enableFilter: true,
        type: "dayjs_range",
        formatType: "date",
        format: "h:mm a, DD-MMM-YYYY",
      },
    },
  };

  const loadFilters = async () => {
    const filter_data = {
      return_code: 0,
      tableFilters: requisitionFilters,
    };
    return filter_data;
  }

  const loadReport = async (userToken, queryParams) => {
    try {
      const headers = {
        token: userToken.accessToken, //authToken
      };
      const response = await get_requisition_list(headers, queryParams);
      if (response?.status === 201) {
        return response.data;
      }
    } catch (error) {
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const [searchParams] = useSearchParams();
  const pageParams = createPageParamsFromURLString(
    requisitionTableConfiguration,
    searchParams
  );
  const [reportData, setReportData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const loadInitialFilterDataCB = () => {
    return loadFilters();
  };

  const loadReportCB = (reportQueryParams, additionalParams) => {
    return loadReport(userToken, reportQueryParams, additionalParams);
  };

  return (
    <>
      <div className="my-3" style={{ display: "flex", alignItems: "center" }}>
        <div>
          <p
            className="m-0"
            style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
          >
            Requisition
          </p>
          <p
            className="m-0"
            style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
          >
            Requisition List
          </p>
        </div>
        <div
          className="farmers__add-farmer-btn"
          onClick={() => navigate("addrequisition")}
        >
          + Add Requisition
        </div>
      </div>

      {/* farmer table  */}
      {userToken && userToken !== null && (
        <OCTable
          reportData={reportData}
          setReportData={setReportData}
          tableConfiguration={requisitionTableConfiguration}
          selectedRowKeys={selectedRowKeys}
          setSelectedRowKeys={setSelectedRowKeys}
          loadReportCallback={loadReportCB}
          loadInitialFilterDataCallback={loadInitialFilterDataCB}
        />
      )}
    </>
  );
};

// export default Farmers;
export default RequisitionList;
