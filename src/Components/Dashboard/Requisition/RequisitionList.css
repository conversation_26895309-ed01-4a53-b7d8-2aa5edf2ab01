/* styles.css */

/* Custom styles for the table header */
.custom-header {
    background-color: #DFDFDF;
    font-weight: bold;
}

/* Custom styles for table rows */
.custom-row {
    /* Add your row styles here */
    background-color: #f9f9f9;
}

/* Alternate row styles (optional) */
.custom-row:nth-child(even) {
    background-color: #f9f9f9;
}

#table-style {
    background-color: #AFF1FF;
    display: flex;
    flex: 1;
}


.custom-table .ant-table-cell {
    /* Add custom styles for table cells */
    background-color: #31e1f8;
    /* border: 1px solid #1cbfdb; */
    /* Example border style */
}

/* Add more styles as needed */


.ant-table-thead .ant-table-cell {
    background-color: #31e1f8 !important;
    /* display: flex; */

}

.requisitionesHeader {
    display: flex;
    flex: 1;
    justify-content: space-between;
}



.requisitionList {
    font-weight: 700;
    size: 20px;
    line-height: 27px;
    align-items: center;
}

.addRequistion {
    background-color: #e76c3b;
    border-radius: 5px;
    border: none;
    color: #f9f9f9;
    width: 176px;
    height: 46px;
}

.filter {
    background-color: #e76c3b;
    border-radius: 5px;
    border: none;
    color: #f9f9f9;
    width: 100px;
    height: 46px;
}

.searchbox {
    border-radius: 20%;
    border: #080101;

}

.data {
    color: #080101;
}

.data:hover {
    cursor: pointer;
    text-decoration: underline;
}

.updated_status {
    border: none;
    border-radius: 5px;
    background-color: #e76c3b;
    padding: 10px 20px 10px 20px;
    color: #f9f9f9;
    font-weight: bold;
}