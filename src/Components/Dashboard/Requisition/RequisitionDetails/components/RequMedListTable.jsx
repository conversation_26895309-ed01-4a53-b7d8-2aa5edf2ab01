import {Table} from "antd"
import { unitMappingByMedId } from "Utilities/Common/utils";
const RequMedListTable = ({tableData}) => {
    const columns = [
        {
          title: 'Medicine',
          dataIndex: 'medicine',
          key: 'medicine',
        },
        {
          title: 'Pack Size/Unit',
          dataIndex: 'unit',
          key: 'pack',
        },
        {
          title: 'Quantity',
          dataIndex: 'quantity',
          key: 'quantity',
        },
        {
          title: 'Total pack size',
          dataIndex: 'total_size',
          key: 'unit',
        },
      ];
      const dataSource = ()=>{
        return tableData?.map((item,index)=>({
            index,
            medicine : item.medicine_name,
            unit : item.medicine_pack_size + "/"+ unitMappingByMedId(item?.medicine_id),
            quantity : +item.quantity / +item.medicine_pack_size,
            total_size :  item?.quantity + " " + unitMappingByMedId(item?.medicine_id)
        }))
      }
  return (
    <Table columns={columns} dataSource={dataSource()} pagination={false}/>
  )
}

export default RequMedListTable;