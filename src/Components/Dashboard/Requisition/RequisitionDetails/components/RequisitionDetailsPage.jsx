import {Grid, Box, Typography, Paper, Stack, Divider, Chip} from "@mui/material";
import RequMedListTable from './RequMedListTable';
import { extractBasedOnLanguageMod, formatedDate } from "../../../../../Utilities/Common/utils";
import Each from "../../../../../Utilities/Each";

const RequisitionDetailsPage = ({data}) => {
    const initialData = [
        { label : "Requisitioner Name", value  : data?.requisitioner_name},
        { label : "Requisitioner Type", value  : extractBasedOnLanguageMod(data?.requisitioner_type_l10n,"en")},
        { label : "Fullfiller Name", value  : data?.fullfiller_name },
        { label : "Fullfiller Type", value  : extractBasedOnLanguageMod(data?.fullfiller_type_l10n,"en")},
        { label : "Status", value  : <Chip label={extractBasedOnLanguageMod(data?.status_l10n,"en")}/>},
        { label : "Created At", value  : formatedDate(data?.inserted_at)}, // created_at to   inserted_at 
    ]
    const {requisition_line_items} = data;
  return (
    <Box>
        <Paper elevation={4} sx={{ padding: 2, borderRadius: 4, backgroundColor:"#E9E3F8" }}>
            <Grid container spacing={2}>
            <Each of={initialData} render={(item,index)=>(
                <Grid item xs={6} sm={4} md={4} key={index}>
                    <Stack spacing={1}>
                        <Typography sx={{ fontWeight: 600, color: '#2e2e2e', fontSize: '1.1rem' }}>{item.label}</Typography>
                        <Typography sx={{ color: '#777', fontSize: '0.9rem' }}>{item.value}</Typography>
                    </Stack>
                </Grid>
                )}/>
            </Grid>
        </Paper>
        <Typography sx={{mt: 2, fontWeight: 700}} variant='h6'>Medicine List</Typography>
        <Divider/>
        <RequMedListTable tableData={requisition_line_items}/>
    </Box>
  )
}

export default RequisitionDetailsPage;