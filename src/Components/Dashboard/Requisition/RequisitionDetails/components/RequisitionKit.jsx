import React, { useEffect, useState, useRef } from "react";
import {Form, Popconfirm, Table, Typography, Button} from "antd";
import {unitMappingByMedId} from "../../../../../Utilities/Common/utils"
import useRequisitionKit from "../hook/useRequisitionKit";
import useRequisitionKitTable from "../hook/useRequisitionKitTable";
import StaffListModal from "./StaffListModal";

const RequisitionKit = ({req_user_id}) => {
  const {kitData, kitLoading} = useRequisitionKit(req_user_id)
  const {EditableCell} = useRequisitionKitTable()
  const requiredMappedData = ()=>{
    return kitData?.map((item,index)=>({
        key : index,
        requisition_id : item?.requisition_id,
        medicine_id : item?.medicine_id,
        medicine_name : item?.medicine_name,
        medicine_unit : item?.medicine_unit,
        balance : item?.balance,
        quantity: item?.quantity,
        medicine_pack_size: item.medicine_pack_size,
        action: index
    }))
  }
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState('');
  const [selectedRowMedInfo, setselectedRowMedInfo] = useState([])
  const ref = useRef();
  const isEditing = (record) => record.key === editingKey;
  const edit = (record) => {
    form.setFieldsValue({
      quantity: '',
      ...record,
    });
    setEditingKey(record.key);
  };
  const cancel = () => {
    setEditingKey('');
  };
  const save = async (key) => {
    try {
      const row = await form.validateFields();
      const newData = [...data];
      const index = newData.findIndex((item) => key === item.key);
      if (index > -1) {
        const item = newData[index];
        newData.splice(index, 1, {
          ...item,
          ...row,
        });
        setData(newData);
        setEditingKey('');
      } else {
        newData.push(row);
        setData(newData);
        setEditingKey('');
      }
    } catch (errInfo) {
      console.log('Validate Failed:', errInfo);
    }
  };

  const start = async () => {
    ref?.current?.openModal()
    ref?.current?.getLineItems(selectedRowMedInfo)
    // await kitTransferHandler({})
  };
  const onSelectChange = (newSelectedRowKeys, rowInfo) => {
    setSelectedRowKeys(newSelectedRowKeys);
    const filteredInfo = rowInfo?.map((item)=>{
        return ({
           medicine_id: item?.medicine_id,
           unit : item?.unit || "",
           quantity :  item?.quantity
        })
    })
    setselectedRowMedInfo(filteredInfo)
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };
  const hasSelected = selectedRowKeys.length > 0;
  const columns = [
    {
      title: 'Medicine',
      dataIndex: 'medicine_name',
      key: 'medicine_name',
    },
    {
      title: 'Pack Size/Unit',
      dataIndex: 'medicine_unit',
      key: 'medicine_unit',
      render: (_, record) => (
        < div>{`${record.medicine_pack_size}/${unitMappingByMedId(record?.medicine_id)}`}</div>
      ),
    },
    {
      title: 'Balance Quantity',
      dataIndex: 'balance',
      key: 'balance',
      render: (_, record) => (
        < div>{record.balance ? record.balance : "NA"}</div>
      ),
    },
    {
      title: 'Change Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      editable : true
    },
    {
        title : "Action",
        dataIndex : "action",
        key : "action",
        render: (_, record) => {
            const editable = isEditing(record);
            return editable ? (
              <span>
                <Typography.Link
                  onClick={() => save(record.key)}
                  style={{
                    marginRight: 8,
                  }}
                >
                  Save
                </Typography.Link>
                <Popconfirm title="Sure to cancel?" onConfirm={cancel}>
                  <a>Cancel</a>
                </Popconfirm>
              </span>
            ) : (
              <Typography.Link disabled={editingKey !== ''} onClick={() => edit(record)}>
                Edit
              </Typography.Link>
            );
          },
    }
  ];
  const mergedColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: col.dataIndex === 'quantity' ? 'text' : 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

 useEffect(()=>{
    setData(requiredMappedData())
 },[kitData])
 
  return (
    <div>
      <div
        style={{
          marginBottom: 16,
        }}
      >
        <Button
          type="primary"
          onClick={start}
          disabled={!hasSelected}
          loading={loading}
        >
          Transfer
        </Button>
        <span
          style={{
            marginLeft: 8,
          }}
        >
          {hasSelected ? `Selected ${selectedRowKeys.length} kit` : ""}
        </span>
      </div>
      <Form form={form} component={false}>
      <Table
        rowSelection={rowSelection}
        components={{
          body: {
            cell: EditableCell,
          },
        }}
        bordered
        dataSource={data}
        columns={mergedColumns}
        rowClassName="editable-row"
        pagination={{
          onChange: cancel,
          pageSize: 5
        }}
        loading={kitLoading}
        rowKey={(record) => record.key}
      />
    </Form>
    <StaffListModal ref={ref} req_user_id={req_user_id} kitData={kitData}/>
    </div>
  );
};
export default RequisitionKit;