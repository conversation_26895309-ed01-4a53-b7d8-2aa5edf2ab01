import React, { useState , forwardRef, useImperativeHandle} from 'react'
import {But<PERSON>, Modal} from "antd"
import {Autocomplete, Divider, TextField, Typography} from "@mui/material"
import Stack from '@mui/material/Stack';
import useRequisitionKit from '../hook/useRequisitionKit';
import { extractBasedOnLanguageMod } from 'Utilities/Common/utils';
import ToastersService from '../../../../../Services/toasters.service';
const StaffListModal = forwardRef(({req_user_id, kitData},ref) => {
    const {getStaffs, staffList, kitTransferHandler} = useRequisitionKit(req_user_id);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [value, setValue] = useState()
    const [inputValue, setInputValue] = useState()
    const [lineItems, setLineItems] = useState([])
    const showModal = (selectedRowMedInfo) => {
        setLineItems(selectedRowMedInfo)
        getStaffs()
        setIsModalOpen(true);
    };
    const handleOk = () => {
        setIsModalOpen(false);
    };
    const handleCancel = () => {
    setIsModalOpen(false);
  };
  useImperativeHandle(ref,()=>({
    openModal(){
      showModal()
    },
    getLineItems(meds){
      setLineItems(meds)
    }
  }))
  const handleSubmit = async ()=>{
    const reqBody = {
      "requisitioner_type_id": value?.type_id,
      "requisitioner_uuid": value?.value,
      "fullfiller_type_id": kitData[0]?.requisitioner_type_id,
      "fullfiller_uuid": kitData[0]?.requisitioner_uuid,
      "approver_type_id": null,
      "approver_uuid": null,
      "requisition_line_items": lineItems,
      "status_id": 1000830002
    }
    if (value && Object.keys(value).length !== 0) {
      kitTransferHandler(reqBody)
    }else{
      ToastersService.warningToast("please select the staff")
    }
  }
  const modifiedList = staffList?.map((item)=>{
    return ({
      label : extractBasedOnLanguageMod(item?.staff_name_l10n,"en"),
      value : item?.staff_id,
      type_id : item?.staff_type_id
    })
  })
  return (
    <Modal  open={isModalOpen} onOk={handleOk} onCancel={handleCancel} footer={null}>
      <Typography>Select Staff to transfer the kit</Typography>
      <Divider sx={{mt:2}}/>
      <Autocomplete
        style={{width:"100%"}}
        value={value}
        onChange={(event, newValue) => {
          setValue(newValue);
        }}
        inputValue={inputValue}
        onInputChange={(event, newInputValue) => {
          setInputValue(newInputValue);
        }}
        id="controllable-states-demo"
        options={modifiedList}
        sx={{ width: 300 }}
        renderInput={(params) => <TextField {...params} label="Staffs" />}
      />
      <Divider sx={{mb:2}}/>
      <Stack justifyContent="flex-end" direction="row">
        <Button style={{background:"#ec6237", color:"white"}} onClick={handleSubmit}>Submit</Button>
      </Stack>
    </Modal>
  );
})
export default StaffListModal;