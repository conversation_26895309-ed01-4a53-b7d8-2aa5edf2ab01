import React from 'react'
import {Form, Input, InputNumber} from "antd"

const useRequisitionKitTable = () => {
      const EditableCell = ({
        editing,
        dataIndex,
        title,
        inputType,
        record,
        index,
        children,
        ...restProps
      }) => {
        const inputNode = inputType === 'number' ? <InputNumber /> : <Input />;
        return (
          <td {...restProps}>
          {editing ? (
            <Form.Item
              name={dataIndex}
              style={{
                margin: 0,
              }}
              rules={[
                {
                  required: true,
                  message: `Please Input ${title}!`,
                },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue(dataIndex) > record.balance) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('Quantity should be greater than balance!'));
                  },
                }),
              ]}
            >
              {inputNode}
            </Form.Item>
          ) : (
            children
          )}
        </td>
        );
      };
      
    return {EditableCell}
}

export default useRequisitionKitTable