import { clearUserToken } from "Components/user/action";
import { instance } from "Services/api.service";
import {useState, useEffect, useCallback} from "react"
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import ToastersService from "../../../../../Services/toasters.service";


const useRequisitionKit = (req_user_id)=>{
    const navigate = useNavigate()
    const dispatch = useDispatch();
    const userToken = useSelector((state) => state.user.userToken);
    const [kitData, setKitData] = useState([])
    const [staffList, setStaffList] = useState([])
    const [kitLoading, setKitLoading] = useState(false)
    const getInventoryKit = useCallback(async () => {    
        try {
          setKitLoading(true)
          const response = await instance({
            url: `/v3/inventorykit?id=${req_user_id}`,
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              token: userToken.accessToken,
            },
          });
          if (response.status === 200) {
            setKitLoading(false)
            response.data.data.map(item => {
              item['quantity'] = item.balance
            })
            setKitData(response.data.data)
    
          }
        } catch (error) {
          setKitLoading(false)
          console.log(error);
          if ('response' in error && error.response.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
          }
        }
      },[])
      const kitTransferHandler = async (payload) => {
        try {
          
          const response = await instance({
            url: "v3/requisitions",
            method: "POST",
            data: payload,
            headers: {
              "Content-Type": "application/json",
              token: userToken.accessToken,
            },
          });
          if (response.status === 200 || response.status === 201) {
            ToastersService.successToast("Transfer completed")
            console.log("created requisitin ", response);
            navigate("/requisitionlist")
          }
    
        } catch (error) {
          console.log(error);
          ToastersService.failureToast("failed to transer!");
          if ('response' in error && error.response.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
            ToastersService.failureToast("failed to transer!");
          }
        }
      }
      const getStaffs = useCallback(async() => {
        try {
          const response = await instance({
            url: "/staffs/all",
            method: "POST",
            data: {},
            headers: {
              "Content-Type": "application/json",
              token: userToken.accessToken,
            },
          });
          if (response.status === 201) {
            let arr = response?.data?.data?.filter(item => item?.staff_id != req_user_id && (item?.staff_type_id == 1000230004 || item?.staff_type_id == 1000230003))
            setStaffList(arr);
          }
        } catch (error) {
          console.log(error);
          if ('response' in error && error.response.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
          }
        }
      },[])
    
    useEffect(()=>{
        getInventoryKit()
    },[])

    return {kitData, kitTransferHandler, getStaffs, staffList, kitLoading}
}

export default useRequisitionKit;