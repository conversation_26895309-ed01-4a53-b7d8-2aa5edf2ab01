.requisitioninfo {
    display: flex;
    justify-content: space-between;
    /* margin: 25px; */
    margin-top: 25px;
    margin-bottom: 25px;
}

.nametags {
    font-weight: 500;
    font-size: large;
    color: rgb(40, 40, 41);
    display: flex;
    justify-content: left;

}

.valuetag {
    font-weight: 500;
    font-size: 20px;
    color: rgb(123, 123, 124);
    display: flex;
    justify-content: left;
}

.inputdata {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    margin-bottom: 5px;

}

.inputtagname {
    font-weight: 300;
    font-size: medium;
    color: rgb(40, 40, 41);
    display: flex;
    justify-content: left;
    margin-top: 5px;
    margin-bottom: 5px;

}

.inputfield {
    border-radius: 6px;
    border-style: solid;
    width: 200px;
    height: 40px;
    border-color: rgb(223, 223, 223) !important;
    padding-left: 15px;
    border-top-color: rgb(223, 223, 223);

}

.delete {
    margin-top: 35px;
    border-radius: 6px;
    border-style: solid;
    border-color: rgb(223, 223, 223) !important;
    padding-left: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.goback {
    font-weight: 400;
    size: 15px;
    line-height: 17.7px;
    align-items: center;
    border: none;
    background-color: rgb(255, 254, 254);
    color: #757575;
    display: flex;
    justify-content: left;
    margin-top: 10px;
    margin-bottom: 10px;

}

.requsitiondetails {
    font-weight: 700;
    size: 20px;
    line-height: 27px;
    align-items: center;
    color: #808080;
}

.requisitionname {
    font-weight: 700;
    font-size: 22px;
}

.transfer {
    background-color: #e76c3b;
    border-radius: 5px;
    border: none;
    color: #f9f9f9;
    width: 126px;
    height: 36px;
    margin-bottom: 25px;
}


.tabsinfo {
    font-weight: 550;
    width: 100%;
    height: 52px;
    top: 203px;
    left: 246px;
    background-color: #252525;
    color: aliceblue;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 15px;

}

.notselected {
    border: none;
    background-color: #252525;
    color: #f9f9f9;
    font-size: 20px;
    font-weight: 400;
}

.selected {
    border: none;
    background-color: #252525;
    color: #df5819;
    font-size: 20px;
    font-weight: 400;
}


.goback {
    font-weight: 400;
    size: 15px;
    line-height: 17.7px;
    align-items: center;
    border: none;
    background-color: rgb(255, 254, 254);
    color: #757575;
    display: flex;
    justify-content: left;

}

.requsitiondetails {
    font-weight: 700;
    size: 20px;
    line-height: 27px;
    align-items: center;
    color: #808080;
}

.requsitionlist {
    display: flex;
    flex-direction: column;

}

.divtag {
    display: flex;
    margin-bottom: 25px;
}

.tabsrow {
    font-weight: 550;
    width: 100%;
    height: 52px;
    top: 203px;
    left: 246px;
    background-color: #252525;
    color: aliceblue;
    display: flex;
    flex-direction: row;
    align-items: center;

}

.tabinfo {
    height: 100%;
    border: none;
    color: aliceblue;
    background-color: #252525;
    margin-left: 50px;
    margin-right: 15px;
    justify-content: center;
    align-items: center;
    font-size: large;
}

.active {
    color: orange;
}

.addrequisition {
    font-weight: 700;
    font-size: 22px;
}

.requisitioninfo {
    display: flex;
    justify-content: space-between;
    /* margin: 25px; */
    margin-top: 25px;
    margin-bottom: 25px;
}

.nametags {
    font-weight: 500;
    font-size: large;
    color: rgb(40, 40, 41);
    display: flex;
    justify-content: left;

}

.valuetag {
    font-weight: 500;
    font-size: 20px;
    color: rgb(123, 123, 124);
    display: flex;
    justify-content: left;
}

.inputdata {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    margin-bottom: 5px;
}

.inputtagname {
    font-weight: 300;
    font-size: medium;
    color: rgb(40, 40, 41);
    display: flex;
    justify-content: left;
    margin-top: 5px;
    margin-bottom: 5px;

}

.inputfield {
    border-radius: 6px;
    border-style: solid;
    width: 400px;
    height: 40px;
    border-color: rgb(223, 223, 223) !important;
    padding-left: 15px;
    border-top-color: rgb(223, 223, 223);
    width: 200px;
}

.delete {
    margin-top: 35px;
    border-radius: 6px;
    border-style: solid;
    border-color: rgb(223, 223, 223) !important;
    padding-left: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.addmedicine {
    margin-top: 35px;
    border-radius: 6px;
    border-style: solid;
    border-color: rgb(223, 223, 223) !important;
    padding-left: 15px;
    display: flex;
    justify-content: left;
    align-items: center;
    background-color: rgb(111, 114, 111);
    width: 170px;
    height: 40px;
    color: aliceblue;
}


.requsitionblocks {
    display: flex;
    flex: 1;
    justify-content: space-between;
    margin-top: 15px;

}

.requisitionitem {
    width: 250px;
}

.detailsinfo {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;

}

.header-key {
    width: 248px;
    height: 19px;
    top: 296px;
    left: 274px;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 800;
}

.header-value {
    top: 325px;
    left: 274px;
    font-size: 16px;
    /* font-weight: 600; */


}

.loader {
    border: 6px solid #515151;
    border-top: 6px solid #3498db;
    border-radius: 50%;
    width: 150px;
    height: 150px;
    animation: spin 1s linear infinite;
    /* flex: 1;
    display: flex;
    justify-content: center;
    align-items: center; */
    /* height: 100vh; */
}

.loadingcircle {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.savebuttons {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;

}

.submit {
    margin-top: 35px;
    border-radius: 6px;
    border-style: solid;
    border-color: rgb(223, 223, 223) !important;
    padding-left: 15px;
    display: flex;
    justify-content: left;
    align-items: center;
    background-color: rgb(21, 185, 21);
    /* width: 170px; */
    height: 40px;
    color: aliceblue;
}