import React from "react";
import './RequisitionDetails.css'
import { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Space, Checkbox, Table, Tag, Input } from 'antd';
import { Unstable_Popup as Popup } from '@mui/base/Unstable_Popup';
import { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { BASE_URL, instance } from "../../../../Services/api.service";
import { clearUserToken } from "../../../user/action";
import { Container, Modal, Row, Col } from "react-bootstrap";
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import ToastersService from "../../../../Services/toasters.service";
import { extractBasedOnLanguage } from "@krushal-it/common-core";
import { extractBasedOnLanguageMod } from "../../../../Utilities/Common/utils";
import { formatedDate, globalReferencesMapper, unitMappingByMedId } from "../../../../Utilities/Common/utils";

const RequisitionDetails = (props) => {

  const location = useLocation()
  const navigate = useNavigate()
  const dispatch = useDispatch();
  const userToken = useSelector((state) => state.user.userToken);

  const [detailtab, setDetailstab] = useState(true)
  const [kittab, setKitstab] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const [details, setDetals] = useState({});
  const [inventoryKit, setInventoryKit] = useState('');
  const [load, setLoad] = useState(true);
  const [quantityTranser, setQuantityTranser] = useState(0);
  const [staffList, setStaffList] = useState([]);
  const [medicineList, setMedicineList] = useState([]);
  const [selectedMed, setSelectedMed] = useState(true)
  const [alteredMedList, setAlteredMedList] = useState([])
  const [searchmed, setsearchmed] = useState("")
  const [searchstaff, setsearchstaff] = useState("")
  const [transferData, setTransferData] = useState([])
  const [error, setError] = useState('');
  const [deleteData, setDeleteData] = useState(0)
  const [showSaveButton, setShowSaveButton] = useState(true)
  const [showSubmitButton, setShowSubmitButton] = useState(false)


  const [formData, setFormData] = useState([
    // { id: 1, medicine: '', unit: '',quantity:'',pack_size:'' },
    // { id: 2, medicine: '', unit: '',quantity:'',pack_size:'' },
  ]);



  const filterList = () => {
    if (searchmed) {
      return alteredMedList.filter((listItem) => listItem.reference_name.toLowerCase().includes(searchmed.toLowerCase()))
    } else {
      return alteredMedList
    }
  }

  const filterStaffList = () => {
    const queryParams = new URLSearchParams(location.search);

    let arr = staffList.filter(item => item.staff_id != queryParams.get('req_user_id') && (item.staff_type_id == 1000230004 || item.staff_type_id == 1000230003))
    if (searchmed) {
      return arr.filter((listItem) => (extractBasedOnLanguageMod(listItem.staff_name_l10n,'en')).toLowerCase().includes(searchmed.toLowerCase()))
    } else {
      return arr
    }
  }

  const getMedicineFromGlobalRef = () => {
    if (
      global?.medicineReferences &&
      Object.keys(global?.medicineReferences).length > 0
    ) {
      const medicineArray = Object.keys(global?.medicineReferences).map(
        (key) => global?.medicineReferences[key]
      );
        setMedicineList(medicineArray);
        setAlteredMedList(medicineArray);
        setLoad(false)
    }
  };

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const req_user_id = queryParams.get('req_user_id');
    const req_id = queryParams.get('req_id');

    console.log('req_user_id:', req_user_id);
    console.log('req_id:', req_id);
    console.log("selectedRowKeys", selectedRowKeys)
    getInventoryKit(req_user_id)
    getRequisitoinDetails({ "filters": { "requisition_id": req_id } })
    getStaffs()
    // getMedicine()
  }, [])

  const getStaffs = async (params) => {
    let reqBody = {};
    if (params !== {}) {
      reqBody = { ...reqBody, ...params };
    }
    try {
      const response = await instance({
        url: "/staffs/all",
        method: "POST",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      console.log(response);
      if (response.status === 201) {
        console.log("Staff List", response.data.data);
        setStaffList(response.data.data);
      }

      // setFarmersListLoader(false);
    } catch (error) {
      console.log(error);
      if ('response' in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
      }
    }
  };

  // const getMedicine = async (params) => {
  //   let reqBody = {};
  //   if (params !== {}) {
  //     reqBody = { ...reqBody, ...params };
  //   }
  //   try {
  //     const response = await instance({
  //       url: "/v3/medicines",
  //       method: "GET",
  //       data: reqBody,
  //       headers: {
  //         "Content-Type": "application/json",
  //         token: userToken.accessToken,
  //       },
  //     });
  //     if (response.status === 200) {
  //       console.log("Medicine List", response.data.data);
  //       const updatedData = response.data.data
  //       setMedicineList(updatedData)
  //       setAlteredMedList(updatedData)
  //       setLoad(false)
  //     }
  //   } catch (error) {
  //     console.log(error);
  //     if ('response' in error && error.response.status === 401) {
  //       dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
  //     }
  //   }
  // };

  const getInventoryKit = async (req_user_id) => {
    let reqBody = {};

    try {
      const response = await instance({
        url: `/v3/inventorykit?id=${req_user_id}`,
        method: "GET",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status === 200) {
        console.log("invetory kit", response.data.data);
        response.data.data.map(item => {
          item['t_qty'] = item.balance
        })
        setInventoryKit(response.data.data)

      }
    } catch (error) {
      console.log(error);
      if ('response' in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
      }
    }
  };

  const getRequisitoinDetails = async (reqBody) => {
    // let reqBody = {};
    // if (params !== {}) {
    //     reqBody = { ...reqBody, ...params };
    //   }
    try {
      const response = await instance({
        url: `v3/requisitionlist`,
        method: "POST",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status === 200 || response.status === 201) {
        // console.log("requisition details", response.data) 
        console.log("requisition details", response.data.data[0]);
        setDetals(response.data.data[0])
        // console.log("this is details", details)
        setRequisitionMedList(response.data.data[0])
      }
    } catch (error) {
      console.log(error);
      if ('response' in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
      }
    }
  };

  const CreateRequisition = async (params) => {
    let reqBody = {};
    if (params !== {}) {
      reqBody = { ...reqBody, ...params };
    }
    try {
      const response = await instance({
        url: "v3/requisitions",
        method: "POST",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status === 200 || response.status === 201) {
        console.log("created requisitin ", response);
      }

    } catch (error) {
      console.log(error);
      if ('response' in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
      }
    }
  };

  const UpdateRequisition = async (params) => {
    console.log("form data 1 ", formData)

    let reqBody = {};
    if (params !== {}) {
      reqBody = { ...reqBody, ...params };
    }
    try {
      const response = await instance({
        url: "v3/requisitions",
        method: "PUT",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status === 200 || response.status === 201) {
        console.log("created requisitin ", response);
        console.log("form data 2 ", formData)
      }

    } catch (error) {
      console.log(error);
      if ('response' in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
      }
    }
  };

  const setRequisitionMedList = (detailss) => {
    // console.log("requsition details of medicine============== ",details)
    let arr = []
    if (detailss.status_id == 1000820001) {
      setShowSaveButton(true)
    }
    else {
      setShowSaveButton(false)
    }

    detailss.requisition_line_items?.map(json_data => {
      arr.push({
        id: json_data.medicine_id,
        medicine: extractBasedOnLanguageMod(json_data.medicine_name_l10n,'en'),
        unit: unitMappingByMedId(json_data.medicine_id),
        pack: json_data.medicine_pack_size,
        quantity: +json_data.quantity / +json_data.medicine_pack_size,
        total_size: json_data.quantity,
        requisition_line_items_id: json_data.requisition_line_items_id
      })

    }
    )
    // let arr2 = [...formData, arr]
    // setAlteredMedList(arr2)
    // console.log("formdata ", arr2)
    setFormData(arr);;
    setLoad(false)
  }

  // const handleChange = (e) => {
  //   const json_data = JSON.parse(e.target.value)
  //   console.log("Function called", json_data)

  //   let new_item = formData.filter(item => item.id === json_data.reference_id)
  //   if (new_item.length === 0) {
  //     let arr = {
  //       id: json_data.reference_id,
  //       medicine: json_data.reference_name,
  //       unit: json_data.reference_information.unit,
  //       pack: json_data.reference_information.pack_size,
  //       quantity: 1,
  //       total_size: 1 * json_data.reference_information.pack_size
  //     }
  //     let arr2 = [...formData, arr]
  //     setFormData(arr2);
  //     let selectedRows = alteredMedList.filter(function (item2) {
  //       return !arr2.find(function (item) {
  //         return item.medicine.toLowerCase() == item2.reference_name_l10n.ul.toLowerCase()
  //       })
  //     })
  //     setAlteredMedList(selectedRows)
  //     // setModalVisible(false)
  //     console.log("updated form data", formData)
  //     //     setSelectedMed(false)

  //     //     let newarr = medicineList.filter(({ reference_id: id1 }) => !formData.some(({ id: id2 }) => id2 === id1));
  //     //     setAlteredMedList(newarr)
  //   }
  // };

  // rowSelection={{
  //   type: 'checkbox',
  //   ...rowSelection,}} 

  const handletranser = async (e) => {
    console.log("Function called", e)
    console.log("list of selected med", selectedRowKeys)

    let arr = []


    selectedRowKeys.map(item => {
      arr.push({
        "medicine_id": item.medicine_id,
        "unit": extractBasedOnLanguageMod(item.medicine_unit,'en'),
        "quantity": item.t_qty,
      })
    })

    console.log("transfer quantity", arr)
    const reqBody = {
      "requisitioner_type_id": e.staff_type_id,
      "requisitioner_uuid": e.staff_id,
      "fullfiller_type_id": selectedRowKeys[0].requisitioner_type_id,
      "fullfiller_uuid": selectedRowKeys[0].requisitioner_uuid,
      "approver_type_id": null,
      "approver_uuid": null,
      "requisition_line_items": arr,
      "status_id": 1000830002
    }

    console.log("req body", reqBody)

    try {
      const response = await instance({
        url: "v3/requisitions",
        method: "POST",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status === 200 || response.status === 201) {
        ToastersService.successToast("Transfer completed")
        console.log("created requisitin ", response);
        navigate("/requisitionlist")
      }

    } catch (error) {
      console.log(error);
      ToastersService.failureToast("failed to transer!");
      if ('response' in error && error.response.status === 401) {
        // dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
        ToastersService.failureToast("failed to transer!");
      }
    }


    setModalStaffVisible(false)
  }



  const handleNewChange = (e) => {
    console.log("Function called", e)
    const json_data = e
    let new_item = formData.filter(item => item.id === json_data.reference_id)
    if (new_item.length === 0) {
      let arr = { id: json_data.medicine_id, medicine: extractBasedOnLanguageMod(json_data.medicine_name_l10n,'en'), unit: unitMappingByMedId(json_data.medicine_id), pack: json_data.medicine_pack_size, quantity: 1, total_size: 1 * json_data.medicine_pack_size, requisition_line_items_id: json_data.requisition_line_items_id }
      console.log("arr", arr)
      let arr2 = [...formData, arr]
      setFormData(arr2.reverse());
      let selectedRows = alteredMedList.filter(function (item2) {
        return !arr2.find(function (item) {
          return item.medicine.toLowerCase() == extractBasedOnLanguageMod(item2.reference_name_l10n,'en').toLowerCase()
        })
      })

      setAlteredMedList(selectedRows)
      setModalVisible(false)
    }
    else {
      console.log("handle change else condition")
    }
  };



  const columns = [

    {
      title: 'select',
      dataIndex: 'checkbox',
      key: 'checkbox',
      width: 50,
      render: (_, record) => (
        <input
          className="checkbox"
          type="checkbox"
          value={quantityTranser}
          onChange={(e) => handleCheckboxChange(e, record)}
        />
      ),
    },
    {
      title: 'Medicine',
      dataIndex: 'Medicine',
      width: 250,
      render: (_, record) => (
        < div>{extractBasedOnLanguageMod(record.medicine_name_l10n,'en')}</div>
      ),
    },
    {
      title: 'Pack Size/Units',
      dataIndex: 'unit',
      width: 250,
      render: (_, record) => (
        < div>{`${record.medicine_pack_size}/${record.unit}`}</div>
      ),
    },
    {
      title: 'Balance Quantity',
      dataIndex: 'requisition_qty',
      width: 250,
      render: (_, record) => (
        < div>{record.balance ? record.balance : "NA"}</div>
      ),
    },
    {
      title: 'Change Quantity',
      dataIndex: 'requisition_qty',
      width: 250,
      render: (_, record) => (
        <div>
          <Input
            placeholder="Quantity transfer"
            defaultValue={record.t_qty}
            onChange={e => {
              console.log(("value", e.target.value))
              handelQuantityTransfer(e, record)
            }}
          />
          {error && <div style={{ color: 'red' }}>{error}</div>}
        </div>

      ),
    }
  ];

  const handleCheckboxChange = (event, key) => {
    console.log("ckecked item ", event.target.checked)
    if (event.target.checked) {
      setSelectedRowKeys([...selectedRowKeys, key]);
      console.log("checked", [...selectedRowKeys, key])
    } else {
      setSelectedRowKeys(selectedRowKeys.filter((k) => k !== key));
      console.log("checked", selectedRowKeys.filter((k) => k !== key))
    }

  };

  const handelQuantityTransfer = (event, record) => {
    console.log("transfer quantity", event.target.value)
    if (record.balance) {
      let quantity = parseInt(record.balance);
      // let pack_size = parseInt(record.pack_size);
      if (event.target.value <= quantity) {
        let arr = []
        inventoryKit.map(item => {
          if (item.medicine_id == record.medicine_id) {
            item.t_qty = parseInt(event.target.value)
            arr.push(item)
          }
          else {
            arr.push(item)
          }
        })
        setInventoryKit(arr)
        setError("")
      }
      else {
        setError('quantity is greater than balance')
        // setQuantityTranser(0)
        console.log("out of range")

      }
    }
    else {
      console.log("")
    }
  };

  const handleDetailsclick = () => {
    console.log("details clicked")
    setDetailstab(true)
    setKitstab(false)
  }

  const handelRequisitioner = () => {
    console.log("requisitoin kit clicked")
    setDetailstab(false)
    setKitstab(true)
  }

  const handleDelete = (id) => {
    setDeleteData(deleteData + 1)
    console.log("handle delete", id)
    const updatedData = formData.filter(item => item.id != id);

    setFormData(updatedData);
    const updateMedList = medicineList.filter(item =>
      item.medicine_id === id
    )

    console.log("altered arr", updateMedList)

    let newarr = alteredMedList

    newarr.push(updateMedList[0])
    setAlteredMedList(newarr)

    setShowSaveButton(true)


    console.log("deletion complete", formData)

  };

  const handleDetailsQuantity = (e, id, field) => {
    const updatedFormData = formData.map(item =>
      item.id === id ? { ...item, [field]: e.target.value, ['total_size']: item.pack * e.target.value } : item
      // item.id === id ? { ...item, ['total_size']: item.pack*e.target.value } : item
    );
    setFormData(updatedFormData);
  }


  // const handleCreateRequisition = (status) => {

  //   let requisitioner_type_id = details.requisitioner_type_id
  //   let requisitioner_uuid = details.requisitioner_uuid
  //   console.log("this is formdata", formData)
  //   if (formData.length > 0) {
  //     console.log("save the changes", formData)


  //     let newArry = []

  //     formData.map(function (item, index) {
  //       newArry.push({
  //         "medicine_id": item.id,
  //         "unit": item.unit,
  //         "quantity": item.total_size
  //       })
  //     });

  //     console.log("updated body", newArry)

  //     const reqBody = {
  //       "requisitioner_type_id": requisitioner_type_id,
  //       "requisitioner_uuid": requisitioner_uuid,
  //       "fullfiller_type_id": null,
  //       "fullfiller_uuid": null,
  //       "approver_type_id": null,
  //       "approver_uuid": null,
  //       "requisition_line_items": newArry,
  //       "status_id": status == 'pending' ? 1010000001 : status == 'approve' ? 1010000002 : status == 'reject' ? 1010000003 : status == 'received' ? 1010000006 : status == 'delivered' ? 1010000005 : '',
  //     }

  //     console.log("post body", reqBody)

  //     CreateRequisition(reqBody)
  //     navigate(-1)
  //   }
  //   else {
  //     alert("no data to be added")
  //   }
  // }


  const handleUpdateRequisition = (status) => {
    if (deleteData > 0) {
      let requisition_id = details.requisition_id
      let requisitioner_type_id = details.requisitioner_type_id
      let requisitioner_uuid = details.requisitioner_uuid
      let details_status = details.status_id
      console.log("this is formdata", formData)
      if (formData.length > 0) {
        console.log("save the changes", formData)


        let newArry = []

        // formData.map(function (item, index) {
        //   console.log("item ", item)
        //   newArry.push({
        //     "medicine_id": item.id,
        //     "unit": item.unit,
        //     "quantity": item.total_size,
        //     "requisition_line_items_id": item.requisition_line_items_id,
        //     "requisition_id": requisition_id,
        //   })
        // });

        formData.map(function (item, index) {
          newArry.push({
            "medicine_id": item.id,
            "unit": item.unit,
            "quantity": item.total_size
          })
        });

        console.log("updated body", newArry)

        const reqBody = {
          "requisition_id": requisition_id,
          "requisitioner_type_id": requisitioner_type_id,
          "requisitioner_uuid": requisitioner_uuid,
          "fullfiller_type_id": null,
          "fullfiller_uuid": null,
          "approver_type_id": null,
          "approver_uuid": null,
          "requisition_line_items": newArry,
          "status_id": status == 'save' ? 1000820001 : status == 'approval' ? 1000820002 : status == 'reject' ? 1010000003 : status == 'received' ? 1010000006 : status == 'delivered' ? 1010000005 : status == 'open' ? 1010000004 : status == 'same' ? details.status_id : "",

        }

        console.log("post body", reqBody)

        UpdateRequisition(reqBody)
        navigate("/requisitionlist")
      }
      else {
        ToastersService.failureToast("no data to be added")
      }
    }
    else {
      ToastersService.failureToast("Nothting to save")
    }

  }


  const [modalVisible, setModalVisible] = useState(false);
  const [modalStaffVisible, setModalStaffVisible] = useState(false);


  const openstaffModal = () => {
    setModalStaffVisible(true);
    setsearchmed("")

  };

  const closestaffModal = () => {
    setModalStaffVisible(false);
  };

  const openModal = () => {
    setModalVisible(true);
    setsearchmed("")

  };

  const closeModal = () => {
    setModalVisible(false);
  };

  const shouldLoad = details
  if (shouldLoad) {
    return (
      <div style={{ margin: '5px' }}>
        <div>
          <div className="divtag">
            <div >
              <button className="goback" onClick={() => navigate("/requisitionlist")}>Go back</button>
              <div className="requsitiondetails">Requisitons / Requistion Details</div>
              {/* <div className="requisitionname">Rahul P</div> */}
            </div>
          </div>
        </div>

        <div className="tabsinfo">
          <button className={detailtab ? "selected" : "notselected"} style={{ marginLeft: '25px', width: '100px' }} onClick={handleDetailsclick}>
            Details
          </button>
          <button className={kittab ? "selected" : "notselected"} style={{ marginLeft: '55px', }} onClick={handelRequisitioner}>
            Requisitioner Kit
          </button>
        </div>
        {/* rowSelection={rowSelection} */}
        {kittab ? inventoryKit.length > 0 ?
          <div>
            {selectedRowKeys.length > 0 ?
              <button className='transfer' onClick={openstaffModal}>
                Transfer {selectedRowKeys.length > 0 ? selectedRowKeys.length : ""}
              </button> : ""}
            <Table
              columns={columns}
              dataSource={inventoryKit}
              pagination={false} />
            <div style={{ flex: 1, zIndex: 1 }}>
              <Modal show={modalStaffVisible} backdrop="static" centered style={{ flex: 1, paddingTop: '300px' }}  >
                <Modal.Title style={{ padding: '16px 16px 0px 16px', display: 'flex', justifyContent: 'space-between' }}>
                  <div>Select Prarvet</div>
                  <CloseIcon onClick={() => { closestaffModal() }} />
                </Modal.Title>

                <Modal.Body>
                  <Container>
                    <div>
                      <div style={{ width: '100%' }}>
                        <div style={{ border: '1px solid black', padding: 10, borderRadius: 12, marginTop: 20 }}>
                          <SearchIcon onClick={() => { closestaffModal() }} />
                          <input onInput={(e) => setsearchstaff(e.target.value)} name="myInput" placeholder="Search Medicine" style={{ border: 0, width: '90%', outline: 'none' }} />
                        </div>
                      </div>
                      <div style={{ height: '30vh', width: '100%', overflowY: 'scroll', marginTop: 40, alignItems: 'flex-start' }}>
                        {filterStaffList().map((each_staff, index) => {
                          return (
                            <div key={index} onClick={() => handletranser(each_staff)} style={{ flex: 1, marginTop: index == 0 ? 0 : -10, marginBottom: index == 0 ? 0 : -10, cursor: 'pointer' }}>
                              <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                                {/* {each_staff.staff_name_l10n.en ? each_staff.staff_name_l10n.en : each_staff.staff_name_l10n.ul} */}
                                {extractBasedOnLanguageMod(each_staff.staff_name_l10n,'en')}
                              </div>
                              <hr />
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </Container>
                </Modal.Body>
                <Modal.Footer style={{ justifyContent: 'center' }}>
                </Modal.Footer>
              </Modal>
            </div>
          </div>
          :
          <div
            style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', fontSize: '400' }}
          >
            KIT NOT FOUND
          </div>

          :
          <div>
            <div>
              <div className="detailsinfo">
                <div>
                  <div className="header-key">
                    Requisitioner Name:
                  </div>
                  <div className="header-value">
                    {extractBasedOnLanguageMod(details?.requisitioner_name_l10n,"en")}
                  </div>
                </div>
                <div>
                  <div className="header-key">
                    Requisitioner Type:
                  </div>
                  <div className="header-value">
                    {extractBasedOnLanguageMod(details?.requisitioner_type_l10n,"en")}
                  </div>
                </div>
                <div>
                  <div className="header-key">
                    Fullfiller Name
                  </div>
                  <div className="header-value">
                    {details?.fullfiller_name_l10n ? extractBasedOnLanguageMod(details?.fullfiller_name_l10n,"en") : "NA"}
                  </div>
                </div>
              </div>
              <div className="detailsinfo">
                <div>
                  <div className="header-key">
                    Fullfiller Type:
                  </div>
                  <div className="header-value">
                    {/* {details?.fullfiller_type_l10n ? details.fullfiller_type_l10n : "NA"} */}
                    {details?.fullfiller_type_l10n ? extractBasedOnLanguageMod(details?.fullfiller_type_l10n,"en") : "NA"}
                  </div>
                </div>
                <div>
                  <div className="header-key">
                    Status:
                  </div>
                  <div className="header-value">
                    {extractBasedOnLanguageMod(details?.status_l10n,"en")}

                  </div>
                </div>
                <div>
                  <div className="header-key">
                    Created Date
                  </div>
                  <div className="header-value">
                    {formatedDate(details?.inserted_at)}
                  </div>
                </div>
              </div>
            </div>

            <div>
              <hr style={{ backgroundColor: 'black' }} />
            </div>
            <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center', height: '100px', flexDirection: 'row' }}>
              <h4> Medicine List</h4>
              {extractBasedOnLanguageMod(details?.status_l10n,'en') === 'Open' ?
                <button style={{ marginTop: -10 }} className="addmedicine" onClick={openModal}>
                  <span style={{ width: '100%', marginLeft: -5 }}>
                    + Add Medicine
                  </span>

                </button>
                : ""
              }

            </div>
            {formData.map((item, index) => (
              <div className="inputdata" key={index}>
                <div>
                  <div className="inputtagname">
                    Medicine
                  </div>
                  <div
                    className="inputfield"
                    // type="text"
                    style={{ width: 300 }}
                  >
                    {item.medicine}
                  </div>
                </div>
                <div>
                  <div className="inputtagname">
                    Pack size/Unit
                  </div>
                  <div
                    className="inputfield"
                    type="text"
                    style={{ width: 150 }}
                    value={item.unit}
                  // onChange={e => handleChange(e, item.id, 'unit')}
                  >
                    {item.pack} / {item.unit}
                  </div>
                </div>
                <div>
                  <div className="inputtagname">
                    Quantity
                  </div>
                  <input
                    className="inputfield"
                    type="text"
                    style={{ width: 150 }}
                    defaultValue={item.quantity}
                    disabled
                    onChange={e => handleDetailsQuantity(e, item.id, 'quantity')}
                  />
                </div>
                <div>
                  <div className="inputtagname">
                    Total pack size
                  </div>
                  <div
                    className="inputfield"
                    type="text"
                    style={{ width: 150 }}
                    value={item.total_size}
                  // onChange={e => handleChange(e, item.id, 'unit')}
                  >

                    {item.total_size}  {item.unit}
                  </div>
                </div>
                {/* <button className="delete" onClick={e => handleDelete(item.id)}>Delete</button> */}
                {item.status_id == 1000820001 && (
                  <button className="delete" onClick={e => handleDelete(item.id)}>Delete</button>
                )}

              </div>
            ))}
            {/* {details.status_l10n.ul === 'Open' ? */}
            <div className="savebuttons">
              {(showSaveButton || deleteData) ?
                <button className="save" disabled onClick={() => handleUpdateRequisition("save")} style={{ opacity: (formData.length > 0 || deleteData) ? 1 : 0.5, textAlign: 'center' }}>
                  <span style={{ width: '100%' }}>
                    Save
                  </span>
                </button>
                :
                ""}
              {showSubmitButton ?
                <button className="submit" onClick={() => handleUpdateRequisition('approval')} style={{ opacity: formData.length > 0 ? 1 : 0.5, textAlign: 'center', marginLeft: 20 }}>
                  <span style={{ width: '100%' }}>
                    Submit for Approval
                  </span>
                </button>
                :
                ""}

            </div>
            {/* : ""} */}

            <div style={{ flex: 1, zIndex: 1 }}>
              <Modal show={modalVisible} backdrop="static" centered style={{ flex: 1, paddingTop: '300px' }}  >
                <Modal.Title style={{ padding: '16px 16px 0px 16px', display: 'flex', justifyContent: 'space-between' }}>
                  <div>Select Medicine</div>
                  <CloseIcon onClick={() => { closeModal() }} />
                </Modal.Title>

                <Modal.Body>
                  <Container>
                    {/* <Row>
                            <Col md={6}>
                              <select
                                  className="requisitiontype"
                                  onChange={e => handleChange(e)}
                                  style={{width:300, marginTop:'25px'}}>
                                  <option>
                                      {selectedMed ? "Select Medicine" : ""}
                                  </option>
                                  {alteredMedList.map((each_med, index) => {
                                      return (
                                          <option key={index} value={JSON.stringify(each_med)}>
                                              {each_med.reference_name}
                                              
                                          </option>
                                      );
                                  })}
                              </select> 
                            </Col>
                          </Row> */}
                    <div>
                      <div style={{ width: '100%' }}>
                        <div style={{ border: '1px solid black', padding: 10, borderRadius: 12, marginTop: 20 }}>
                          <SearchIcon onClick={() => { closeModal() }} />
                          <input onInput={(e) => setsearchmed(e.target.value)} name="myInput" placeholder="Search Medicine" style={{ border: 0, width: '90%', outline: 'none' }} />
                        </div>
                      </div>
                      <div style={{ height: '30vh', width: '100%', overflowY: 'scroll', marginTop: 40, alignItems: 'flex-start' }}>
                        {filterList().map((each_med, index) => {
                          return (
                            <div key={index} onClick={() => handleNewChange(each_med)} style={{ flex: 1, marginTop: index == 0 ? 0 : -10, marginBottom: index == 0 ? 0 : -10, cursor: 'pointer' }}>
                              <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                                {extractBasedOnLanguageMod(each_med.medicine_name_l10n,'en')}
                              </div>
                              <hr />
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </Container>
                </Modal.Body>
                <Modal.Footer style={{ justifyContent: 'center' }}>
                </Modal.Footer>
              </Modal>
            </div>


          </div>}
      </div>
    )
  }
  else {
    return (
      <div className="loadingcircle">
        <div className="loader"></div>
      </div>
    )
  }
}


export default RequisitionDetails