import { useEffect, useState } from "react";
import { Box, Typography, Tabs, Tab } from "@mui/material";
import { TabContext, <PERSON>b<PERSON><PERSON>, TabPanel } from "@mui/lab";
import RequisitionDetailsPage from "./components/RequisitionDetailsPage";
import RequisitionKit from "./components/RequisitionKit";
import useRequisition from "./useRequisition";
import {useParams, useNavigate, useLocation} from "react-router-dom"
import InfoIcon from '@mui/icons-material/Info';
import HomeRepairServiceIcon from '@mui/icons-material/HomeRepairService';

const RequisitionDetailsNew = () => {
  const params = useParams()
  const navigate = useNavigate();
  const location = useLocation();
  const {state} = location
  const [value, setValue] = useState("1");

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  const {data} = useRequisition(params?.id);
  return (
    <Box>
      <Typography variant="button" sx={{p:1, cursor:"pointer", textTransform:"none"}} onClick={()=>navigate(-1)}>Go back</Typography>
      <Typography variant="h6" sx={{p:1}}>Requisition Details</Typography>

      <Box sx={{ width: "100%", typography: "body1" }}>
        <TabContext value={value}>
          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <TabList onChange={handleChange} aria-label="requisition">
              <Tab icon={<InfoIcon/>} iconPosition="start" label="Details" value="1" />
              <Tab icon={<HomeRepairServiceIcon/>} iconPosition="start" label="Requisition Kit" value="2" />
            </TabList>
          </Box>
          <TabPanel value="1">
            <RequisitionDetailsPage data={data}/>
          </TabPanel>
          <TabPanel value="2">
              <RequisitionKit req_user_id={state?.req_user_id}/>
          </TabPanel>
        </TabContext>
      </Box>
    </Box>
  );
};

export default RequisitionDetailsNew;
