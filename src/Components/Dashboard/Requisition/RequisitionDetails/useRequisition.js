import { clearUserToken } from "Components/user/action";
import { instance } from "Services/api.service";
import {useEffect, useState} from "react"
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";


const useRequisition = (requisitionId)=>{
    const navigate = useNavigate()
    const dispatch = useDispatch();
    const userToken = useSelector((state) => state.user.userToken);
    const [data, setData] = useState([])
    
    const getRequisitoinDetails = async () => {
        try {
          const response = await instance({
            url: `inventory/requests/request?requisition_id=${requisitionId}`,
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              token: userToken.accessToken,
            },
          });
          if (response.status === 200 || response.status === 201) {
            setData(response.data)
          }
        } catch (error) {
          console.log(error);
          if ('response' in error && error.response.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
          }
        }
      }
    useEffect(()=>{
        getRequisitoinDetails()
    },[])
    return {data}
}
export default useRequisition;