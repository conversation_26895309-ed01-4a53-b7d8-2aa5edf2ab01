import * as React from "react";
import Paper from "@mui/material/Paper";
import InputBase from "@mui/material/InputBase";
import IconButton from "@mui/material/IconButton";
import SearchIcon from "@mui/icons-material/Search";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import { instance } from "../../../../Services/api.service";
import { clearUserToken } from "../../../user/action";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { Popover } from "antd";
import PlaceIcon from "@mui/icons-material/Place";
import NorthWestIcon from '@mui/icons-material/NorthWest';
import ClearIcon from '@mui/icons-material/Clear';
import { Box, Divider } from "@mui/material";
import _ from "lodash"
import "./popover.css"

const CustomVillageSearch = React.forwardRef(({ villageChange<PERSON><PERSON><PERSON>, setFilterParams }, ref) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userToken = useSelector((state) => state.user.userToken);
  const [options, setOptions] = React.useState([]);
  const [searchedText, setSearchedText] = React.useState("")

  const getVillagesBasedOnSearachedText = async (searchText) => {
    try {
      const response = await instance({
        url: `v1/geography?type=village&searchtext=${searchText}`,
        method: "GET",
        headers: {
          token: userToken.accessToken,
        },
      });
      if (response.status === 200) {
        if (!response?.data?.code) {
          setOptions(response?.data);
        } else {
          setOptions([]);
        }
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };
  const onClikHandler = (e,value) => {
    let newValue = [value]
    setSearchedText(value?.village_name)
    villageChangeHandler(newValue)
  }
  
  const debouncedGetVillages = _.debounce((value) => {
    if (value.length >= 3) {
      getVillagesBasedOnSearachedText(value);
    }else{
        setOptions([]);
    }
  }, 500);
  
  const onChangeHandler = (e) => {
    debouncedGetVillages(e.target.value);
    setSearchedText(e.target?.value);
  };
  const clearButtonHandler = ()=>{
    let newValue = [{village_id: []}]
    villageChangeHandler(newValue)
    setSearchedText("")
    setFilterParams("")
    setOptions([])
  }
  React.useImperativeHandle(ref,()=>({
    resetSearchText(){
        clearButtonHandler()
    }
  }))
  const content = (
    <List sx={{ width: 360, maxHeight: 300, overflowY: "scroll" }} >
      {options?.length > 0 ? (
        options.map((option, index) => (
          <div key={index}>
            <ListItem
              sx={{
                display: "flex",
                justifyContent: "space-between",
                cursor: "pointer",
              }}
              
              onClick={(e)=>onClikHandler(e,option)}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <PlaceIcon sx={{ color: "gray", marginRight: 1 }} />
                <Box>
                  <span>{option?.village_name}</span>
                  <span
                    style={{
                      color: "gray",
                      fontSize: "12px",
                      display: "block",
                      marginLeft: 2,
                    }}
                  >
                    {option?.taluk_name}, {option?.district_name},{" "}
                    {option?.state_name}.
                  </span>
                </Box>
              </Box>
              <NorthWestIcon sx={{ color: "gray" }} />
            </ListItem>
            <Divider variant="middle" component="li" />
          </div>
        ))
      ) : (
        <ListItem>No options</ListItem>
      )}
    </List>
  );

  return (
    <div>
      <Paper
        component="form"
        sx={{display: "flex", alignItems: "center", width: 424 }}
      >
        <IconButton type="button" sx={{ p: "10px" }} aria-label="search">
          <SearchIcon />
        </IconButton>
        <Popover
          arrow={false}
          placement="bottom"
          content={content}
          trigger="click"
        >
          <InputBase
            sx={{ ml: 1, flex: 1 }}
            placeholder="Search by villages"
            inputProps={{ "aria-label": "search by villages" }}
            value={searchedText}
            onChange={onChangeHandler}
          />
        </Popover>
        {searchedText?.length > 0 && 
        <IconButton onClick={clearButtonHandler} type="button" sx={{ p: "10px" }} aria-label="search">
          <ClearIcon />
        </IconButton>}
      </Paper>
    </div>
  );
})


export default CustomVillageSearch;