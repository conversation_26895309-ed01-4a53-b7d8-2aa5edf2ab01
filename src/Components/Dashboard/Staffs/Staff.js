import { Col, Row } from "react-bootstrap";
import { useSelector, useDispatch } from "react-redux";
import "./Staff.css";
import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import { useEffect } from "react";
import { instance } from "../../../Services/api.service";
import { useNavigate } from "react-router-dom";
import ToastersService from "../../../Services/toasters.service";
import { useState, useRef } from "react";
import { clearUserToken } from "../../user/action";
import { Button, Table } from "antd";
import { getColumnSearchProps } from "../TableAndUtils/ColumnSearchInputUtil";
import PlaceIcon from "@mui/icons-material/Place";
import CustomVillageSearch from "./helperComponents/CustomVillageSearch";
import { Box, Stack, Typography } from "@mui/material";

const {
  CS_TEAM,
  MARKETING_TEAM,
  SALES_TEAM,
  FINANCE_TEAM,
  OPS_TEAM,
  CENTRAL_VET,
  VET_OPS,
  SALES_OPS,
  IT_ADMIN,
  withAuthorization,
} = require("../../user/authorize");

const Staffs = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userToken = useSelector((state) => state.user.userToken);
  const [staffList, setStaffList] = useState();
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef(null);
  const [loading, setLoading] = useState(false);
  let [filterParams, setFilterParams] = useState({});
  let [showFilters, setShowFilters] = useState(false);
  const ref = useRef();

  const [filterConfig, setFilterConfig] = useState({
    statusConfig: [],
    roleConfig: [],
    villageConfig: [],
    stateConfig: [],
    districtConfig: [],
    talukaConfig: [],
  });

  const updateFilterConfig = (newConfig) => {
    setFilterConfig((prevConfig) => ({
      ...prevConfig,
      ...newConfig,
    }));
  };

  const getStaffs = async (params) => {
    let reqBody = {};
    if (params !== null || params !== undefined) {
      reqBody = { ...reqBody, ...params };
    }
    try {
      setLoading(true);
      const response = await instance({
        url: "/staffs/all",
        method: "POST",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      console.log(response);
      if (response?.status === 201) {
        console.log("Staff List", response?.data?.data);
        setStaffList(response?.data?.data);
        setLoading(false);
      }

      // setFarmersListLoader(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const getState = async (navigate) => {
    try {
      const response = await instance({
        url: "/location/state",
        method: "GET",
        headers: {
          token: userToken.accessToken,
        },
      });
      console.log("state", response?.data?.data);
      if (response.status === 200) {
        console.log("hello");
        updateFilterConfig({ stateConfig: response?.data?.data });
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const getDistrictsBasedOnState = async (navigate, stateId) => {
    try {
      const response = await instance({
        url: "/location/district?state_id=" + stateId,
        method: "GET",
        headers: {
          token: userToken.accessToken,
        },
      });
      console.log("getDistrictsBasedOnState", response);
      if (response?.status === 200) {
        updateFilterConfig({ districtConfig: response.data.data });
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const getTaluksBasedOnDistrict = async (navigate, districtId) => {
    try {
      const response = await instance({
        url: "/location/taluka?district_id=" + districtId,
        method: "GET",
        headers: {
          token: userToken.accessToken,
        },
      });
      console.log("getTaluksBasedOnDistrict", response);
      if (response?.status === 200) {
        updateFilterConfig({ talukaConfig: response?.data?.data });
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const getVillagesBasedOnTaluk = async (navigate, talukId) => {
    try {
      const response = await instance({
        url: "/location/village?taluka_id=" + talukId,
        method: "GET",
        headers: {
          token: userToken.accessToken,
        },
      });
      console.log("getVillagesBasedOnTaluk", response);
      if (response?.status === 200) {
        updateFilterConfig({ villageConfig: response?.data?.data });
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const getStaffFilterConfig = async () => {
    try {
      const response = await instance({
        url: "/filters",
        method: "POST",
        data: {
          filterList: [
            { key: "f_staff_village", match_literal: false },
            { key: "f_staff_type", match_literal: false },
            { key: "f_active_status", match_literal: false },
          ],
        },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      console.log("getStaffFilterConfig", response);
      if (response?.status === 201) {
        console.log("getStaffFilterConfig", {
          ...filterConfig,
          statusConfig: response?.data?.data?.f_active_status,
          roleConfig: response?.data?.data?.f_staff_type,
          villageConfig: response?.data?.data?.f_staff_village,
        });
        updateFilterConfig({
          statusConfig: response?.data?.data?.f_active_status,
          roleConfig: response?.data?.data?.f_staff_type,
        });
      }
      console.log("ldldldl", filterConfig);
      // setFarmersListLoader(false);
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const staffNameFilter = (params) => {
    filterParams = { ...filterParams, f_staff_name: params };
    setFilterParams(filterParams);
    getStaffs(filterParams);
  };

  const staffStatusFilter = (params) => {
    filterParams = { ...filterParams, f_active_status: params };
    setFilterParams(filterParams);
    getStaffs(filterParams);
  };

  const staffRoleFilter = (params) => {
    filterParams = { ...filterParams, f_staff_type: params };
    setFilterParams(filterParams);
    getStaffs(filterParams);
  };

  const staffMobileNumberFilter = (params) => {
    filterParams = { ...filterParams, f_staff_mobile: params };
    setFilterParams(filterParams);
    getStaffs(filterParams);
  };

  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    if (dataIndex === "name") {
      staffNameFilter(selectedKeys[0]);
    } else if (dataIndex === "status") {
      const value = filterConfig?.statusConfig?.find(
        (item) =>
          item?.name_l10n?.ul === selectedKeys[0] ||
          item?.name_l10n?.en === selectedKeys[0]
      )?.id;
      staffStatusFilter(value);
    } else if (dataIndex === "role") {
      const value = filterConfig?.roleConfig?.find(
        (item) =>
          item?.name_l10n?.ul === selectedKeys[0] ||
          item?.name_l10n?.en === selectedKeys[0]
      )?.id;
      staffRoleFilter(value);
    } else if (dataIndex === "mobileNo") {
      staffMobileNumberFilter(selectedKeys[0]);
    }
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
    confirm();
  };

  const handleReset = (clearFilters, confirm, dataIndex) => {
    if (dataIndex === "name") {
      staffNameFilter("");
    } else if (dataIndex === "status") {
      staffStatusFilter("");
    } else if (dataIndex === "role") {
      staffRoleFilter("");
    } else if (dataIndex === "mobileNo") {
      staffMobileNumberFilter("");
    }
    clearFilters();
    setSearchText("");
    confirm();
  };

  const tableData = staffList?.map((staff) => ({
    key: staff?.staff_id,
    name: staff?.staff_name_l10n?.ul || staff?.staff_name_l10n?.en,
    status: staff?.active === 1000100001 ? "Active" : "Inactive",
    role: staff?.staff_type_l10n
      ? staff?.staff_type_l10n?.ul || staff?.staff_type_l10n?.en
      : "-",
    mobileNo: staff?.mobile_number,
  }));

  const tableColumns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "25%",
      ...getColumnSearchProps(
        "name",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
      render: (text, record) => (
        <a
          className="farmers__farmer-name"
          // onClick={() => navigateToStaffDetails(record.key)}
          href={`/staffs/${record?.key}`}
        >
          {text}
        </a>
      ),
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: "25%",
      ...getColumnSearchProps(
        "status",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn,
        filterConfig?.statusConfig?.map((option) => ({
          value: option?.id,
          text: option?.name_l10n?.ul || option?.name_l10n?.en,
        }))
      ),
      render: (text) => (
        <span
          className="staff__badge"
          style={
            text === "Active"
              ? { backgroundColor: "#D1FAE5", color: "#059691" }
              : { backgroundColor: "#FFE4E6", color: "#E11D48" }
          }
        >
          {text}
        </span>
      ),
    },
    {
      title: "Role",
      dataIndex: "role",
      key: "role",
      width: "25%",
      ...getColumnSearchProps(
        "role",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn,
        filterConfig?.roleConfig?.map((option) => ({
          value: option?.id,
          text: option?.name_l10n?.ul || option?.name_l10n?.en,
        }))
      ),
    },
    {
      title: "Mobile No.",
      dataIndex: "mobileNo",
      key: "mobileNo",
      width: "25%",
      ...getColumnSearchProps(
        "mobileNo",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
  ];

  useEffect(() => {
    // getStaffs();
    getStaffs(filterParams);
    getState(navigate);
    getStaffFilterConfig();

    // const initializeForm = async () => {
    //   const updatedFormStateReturnValue = await getStaffConfigurationsAsLists(
    //     navigate,
    //     formState,
    //     setFormState
    //   );
    // };
    // initializeForm();
  }, [filterParams, userToken]);

  const villageChangeHandler = (value) => {
    console.log(value, "clicked from new comp");

    let tempArr = [];
    for (let item of value) {
      tempArr.push(item.village_id);
    }
    console.log("clicked from new comp -> ", tempArr);
    filterParams = {
      ...filterParams,
      f_staff_village: tempArr.length > 0 ? tempArr : undefined,
    };
    setFilterParams(filterParams);
    getStaffs(filterParams);
  };

  const stateChangeHandler = (value) => {
    console.log(value, "state v");
    if (value == null) {
      console.log("hello vv");
      delete filterParams.f_staff_state;
      setFilterParams(filterParams);
      getStaffs(filterParams);
    } else {
      filterParams = {
        ...filterParams,
        f_staff_state: [value?.state_id],
      };
      setFilterParams(filterParams);
      getStaffs(filterParams);
      getDistrictsBasedOnState(navigate, value?.state_id);
    }
  };

  const disdrictChangeHandler = (value) => {
    if (value == null) {
      console.log("hello vv");
      delete filterParams.f_staff_district;
      setFilterParams(filterParams);
      getStaffs(filterParams);
    } else {
      filterParams = {
        ...filterParams,
        f_staff_district: [value?.district_id],
      };
      setFilterParams(filterParams);
      getStaffs(filterParams);
      getTaluksBasedOnDistrict(navigate, value?.district_id);
    }
  };

  const talukaChangeHandler = (value) => {
    console.log(value);
    if (value == null) {
      console.log("hello vv");
      delete filterParams.f_staff_taluk;
      setFilterParams(filterParams);
      getStaffs(filterParams);
    } else {
      filterParams = {
        ...filterParams,
        f_staff_taluk: [value?.taluk_id],
      };
      setFilterParams(filterParams);
      getStaffs(filterParams);
      getVillagesBasedOnTaluk(navigate, value?.taluk_id);
    }
  };

  const navigateToStaffDetails = (staffId) => {
    navigate("/staffs/" + staffId);
  };

  const villageChangeHandlerCustomSearch = (value) => {
    let tempArr = [];
    for (let item of value) {
      tempArr.push(item.village_id);
    }
    filterParams = {
      f_staff_village: tempArr.length > 0 ? tempArr : undefined,
    };
    getStaffs(filterParams);
  };

  return (
    <>
      <Stack
        sx={{ width: "100%", height: 50, marginTop: 2, padding: 1 }}
        direction="row"
        justifyContent="space-between"
        alignItems="center"
      >
        <Box>
          <Typography component="p" variant="p" sx={{ color: "gray" }}>
            Staffs
          </Typography>
          <Typography component="h6" variant="h6">
            Staff List
          </Typography>
        </Box>
        <CustomVillageSearch
          ref={ref}
          villageChangeHandler={villageChangeHandlerCustomSearch}
          setFilterParams={setFilterParams}
        />
        <Button
          onClick={() => navigate("/staffs/add-staff")}
          style={{ background: "#ec6237", color: "#fff" }}
        >
          + Add Staff
        </Button>
      </Stack>
      <hr />
      <div className="mb-2" style={{ display: "flex" }}>
        <div>
          <Button
            variant="primary"
            onClick={() => {
              showFilters = !showFilters;
              setShowFilters(showFilters);
            }}
            style={{ background: "#ec6237", color: "#fff", border: "none" }}
          >
            Filters{" "}
            <i
              className={`fa ${
                showFilters == true ? "fa-angle-up" : "fa-angle-down"
              }`}
              aria-hidden="true"
            ></i>
          </Button>
        </div>

        <div style={{ marginLeft: "auto" }}>
          {Object.keys(filterParams).length !== 0 && (
            <Button
              variant="primary"
              onClick={() => {
                setFilterParams("");
                setShowFilters(false);
                ref?.current?.resetSearchText();
              }}
              style={{ background: "#4c4c4c", color: "#fff", border: "none" }}
            >
              Clear filters
            </Button>
          )}
        </div>
      </div>
      {showFilters == true && (
        <div className="mb-3 p-3" style={{ border: "1px solid #ececec" }}>
          <Row>
            <Col>
              <Autocomplete
                style={{ minWidth: "200px" }}
                disableCloseOnSelect
                filterSelectedOptions
                options={filterConfig.stateConfig.sort((a, b) => {
                  const stateNameA =
                    a.state_name_l10n.en || a.state_name_l10n.ul;
                  const stateNameB =
                    b.state_name_l10n.en || b.state_name_l10n.ul;
                  return stateNameA.localeCompare(stateNameB);
                })}
                onChange={(event, value) => stateChangeHandler(value)}
                getOptionLabel={(option) => {
                  return option.state_name_l10n.en
                    ? option.state_name_l10n.en
                    : option.state_name_l10n.ul;
                }}
                renderInput={(params) => {
                  return (
                    <TextField
                      {...params}
                      label="Select State"
                      placeholder="Search..."
                    />
                  );
                }}
              />
            </Col>

            <Col>
              <Autocomplete
                style={{ minWidth: "200px" }}
                disableCloseOnSelect
                filterSelectedOptions
                options={filterConfig.districtConfig}
                onChange={(event, value) => disdrictChangeHandler(value)}
                getOptionLabel={(option) => {
                  return option.district_name_l10n.en
                    ? option.district_name_l10n.en
                    : option.district_name_l10n.ul;
                }}
                renderInput={(params) => {
                  return (
                    <TextField
                      {...params}
                      label="Select District"
                      placeholder="Search..."
                    />
                  );
                }}
              />
            </Col>
          </Row>
          <Row className="mt-3">
            <Col>
              <Autocomplete
                style={{ minWidth: "200px" }}
                disableCloseOnSelect
                filterSelectedOptions
                options={filterConfig.talukaConfig?.sort((a, b) => {
                  const talukaNameA =
                    a.taluk_name_l10n.en || a.taluk_name_l10n.ul;
                  const talukaNameB =
                    b.taluk_name_l10n.en || b.taluk_name_l10n.ul;
                  return talukaNameA.localeCompare(talukaNameB);
                })}
                onChange={(event, value) => talukaChangeHandler(value)}
                getOptionLabel={(option) => {
                  return option.taluk_name_l10n.en
                    ? option.taluk_name_l10n.en
                    : option.taluk_name_l10n.ul;
                }}
                renderInput={(params) => {
                  return (
                    <TextField
                      {...params}
                      label="Select Taluka"
                      placeholder="Search..."
                    />
                  );
                }}
              />
            </Col>

            <Col>
              <Autocomplete
                style={{ minWidth: "200px" }}
                multiple
                disableCloseOnSelect
                filterSelectedOptions
                options={[
                  ...(filterConfig.villageConfig &&
                    filterConfig.villageConfig?.sort((a, b) => {
                      const villageNameA =
                        a.village_name_l10n.en || a.village_name_l10n.ul;
                      const villageNameB =
                        b.village_name_l10n.en || b.village_name_l10n.ul;
                      return villageNameA.localeCompare(villageNameB);
                    })),
                ]}
                renderOption={(props, option) => (
                  <li {...props} key={option?.village_id}>
                    {option?.village_name_l10n?.en ||
                      option?.village_name_l10n?.ul}
                  </li>
                )}
                onChange={(event, value) => {
                  console.log(value, "auto comp");
                  villageChangeHandler(value);
                }}
                value={filterParams.f_staff_village}
                getOptionLabel={(option) => {
                  return option.village_name_l10n?.en
                    ? option.village_name_l10n.en
                    : option.village_name_l10n.ul;
                }}
                renderInput={(params) => {
                  return (
                    <TextField
                      {...params}
                      label="Filter by village"
                      placeholder="Search..."
                    />
                  );
                }}
              />
            </Col>
            {/* <Col>
              <Box>
                <FormControl fullWidth>
                  <InputLabel id="demo-simple-select-label">
                    Filter by status
                  </InputLabel>
                  <Select
                    onChange={statusChangeHandler}
                    value={
                      filterParams.f_active_status
                        ? filterParams.f_active_status
                        : ""
                    }
                  >
                    {filterConfig.statusConfig.map((option) => (
                      <MenuItem key={option.id} value={option.id}>
                        {option.name_l10n.ul
                          ? option.name_l10n.ul
                          : option.name_l10n.en}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Col> */}
            {/* <Col>
              <Box>
                <FormControl fullWidth>
                  <InputLabel id="demo-simple-select-label">
                    Filter by role
                  </InputLabel>
                  <Select
                    onChange={roleChangeHandler}
                    // value={[filterParams.f_activity_category]}
                    value={
                      filterParams.f_staff_type ? filterParams.f_staff_type : ""
                    }
                  >
                    {filterConfig.roleConfig.map((option) => (
                      <MenuItem key={option.id} value={option.id}>
                        {option.name_l10n.ul
                          ? option.name_l10n.ul
                          : option.name_l10n.en}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Col> */}
          </Row>
        </div>
      )}
      <Table columns={tableColumns} dataSource={tableData} loading={loading} />
    </>
  );
};

// export default Staffs;
export default withAuthorization(Staffs, [
  IT_ADMIN,
  OPS_TEAM,
  VET_OPS,
  SALES_OPS,
]);
