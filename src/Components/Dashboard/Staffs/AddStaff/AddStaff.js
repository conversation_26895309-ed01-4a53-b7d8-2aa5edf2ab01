import { <PERSON>, useNavigate } from "react-router-dom";

import "./AddStaff.css";
import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  extractChangedValues,
} from "../../../Common/KrushalOCForm";
import { instance } from "../../../../Services/api.service";
import ToastersService from "../../../../Services/toasters.service";
import { useEffect, useState } from "react";
import { Button } from "react-bootstrap";
import ReasonModal from "../../../../Utilities/Common/ModalComponents/ReasonModal";
import { notesPattern, globalReferencesMapper } from "../../../../Utilities/Common/utils";
import {useSelector, useDispatch} from "react-redux";
import {UserAddOutlined} from "@ant-design/icons";
import PpuuFinance from "../../PPU/PpuuFinance";
import ImageUploaderAntd from "../../../../Utilities/Common/ImageUploaderAntd";
import { setFinanceTabVisible } from "../../../../redux/slices/ppuu/reducers";

const {CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, IT_ADMIN, withAuthorization} = require('../../../user/authorize')

const staffFormDefinition = {
  formControls: {
    staffName: {
      type: "text-l10n",
      label: { en: "Staff Name", mr: "शेतकऱ्याचे नाव" },
      placeHolderText: "Enter Staff Name",
      validations: [
        { type: "Mandatory" },
        {type: "regex",regexArray: ['Name']}
      ],
      errorMessage: "Name is mandatory and enter valid name",
    },
    // staffVisualId: {
    //   type: "text",
    //   label: "Staff Visual Id",
    //   readOnly: 1,
    // },
    staffEmailAddress: {
      type: "text",
      label: { en: "Staff Email", mr: "शेतकऱ्याचे नाव" },
      placeHolderText: "Enter Staff's Email",
      validations: [
        {type: "regex",regexArray: ['Email']}
      ],
      errorMessage: "Please enter valid email address",
    },
    staffMobile: {
      type: "text-numeric",
      label: "Staff Mobile Number",
      placeHolderText: "Enter Staff Mobile Number",
      validations: [
        { type: "Mandatory" },
        { type: "regex", regexArray: ["IndiaMobileNumber"] },
      ],
      errorMessage: "Enter a valid mobile number!",
    },
    // staffStatus: {
    //   type: "single-select-l10n",
    //   //   allowOthers: 1,
    //   //   othersLabelInDropdown: { en: "Others" },
    //   //   othersLabel: { en: "Other Type of Status" },
    //   labelKeyInList: "name_l10n",
    //   valueKeyInList: "id",
    //   label: "Status",
    //   placeHolderText: "Select Status",
    // },
    staffType: {
      type: "single-select-l10n",
      //   allowOthers: 1,
      //   othersLabelInDropdown: { en: "Others" },
      //   othersLabel: { en: "Other Type of Role" },
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      label: "Staff Role*",
      placeHolderText: "Select Staff Role",
      validations: [{ type: "Mandatory" }],
      errorMessage: "Select a valid role!",
      readOnly : 0
    },
    staffPAN: {
      type: "text-numeric",
      label: "PAN",
      placeHolderText: "Enter PAN number",
      validations: [
        { type: "regex", regexArray: ["PanNumber"] },
      ],
      errorMessage: "Enter a valid PAN number!",
    },
    staffAadhar: {
      type: "text-numeric",
      label: "Aadhar Number",
      placeHolderText: "Enter Aadhar Number",
      validations: [
        { type: "regex", regexArray: ["AadhaarNumber"] },
      ],
      errorMessage: "Enter a valid Aadhar number!",
    },
    subscriptionPlan : {
      type : "single-select-l10n",
      label : "Select Subscription*",
      placeHolderText : "Select Subscription",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      errorMessage: "Subscription plan is mandatory !",
      validations: [{ type: "Mandatory" }],
    },
    subscriptionDate: {
      type: "date",
      label: "Subscription date",
      placeHolderText: "Subscription date",
      readOnly : 0,
      errorMessage: "Subscription date is mandatory !",
      validations: [{ type: "Mandatory" }],
      // validations: [{type: 'MinimumDate', value: new Date()}, {type:'MaximumDate', value: new Date()}]
      // validations: [{type:'MaximumDate', value: moment().subtract(18, 'years').toDate()}]
    },
    // userType: {
    //   type: "single-select-l10n",
    //   label: "Do you want to make as mobile user",
    //   placeHolderText: "Select User Type",
    //   labelKeyInList : "name_l10n",
    //   valueKeyInList : "id"
    // },
    // villagesList: {
    //   type: "multi-select-l10n",
    //   labelKeyInList: "name_l10n",
    //   valueKeyInList: "id",
    //   label: "Villages allotted",
    //   placeHolderText: "Select Villages allotted",
    // },
  },
  visibleFormControls: [
    "staffName",
    // "staffVisualId",
    "staffEmailAddress",
    "staffMobile",
    // "staffStatus",
    "staffType",
    // "staffPAN",
    // "staffAadhar"
    // "villagesList",
    // "userType"
  ],
  elementToDataMapping: {
    staffName: "staff_name_l10n",
    // staffVisualId: "staff_visual_id",
    staffEmailAddress: "email_address",
    staffMobile: "mobile_number",
    // staffStatus: "active_status_id",
    staffType: "staff_type_id",
    staffPAN : "staff_pan",
    staffAadhar: "staff_aadhar",
    subscriptionPlan : "staff_plan",
    subscriptionDate : "subscription_plan_start_date"
    // villagesList: "geo",
    // userType : "user_type_id"
  },
  dataToElementMapping: {
    staff_name_l10n: "staffName",
    // staff_visual_id: "staffVisualId",
    email_address: "staffEmailAddress",
    mobile_number: "staffMobile",
    // active_status_id: "staffStatus",
    staff_type_id: "staffType",
    staff_pan : "staffPAN",
    staff_aadhar : "staffAadhar",
    staff_plan : "subscriptionPlan",
    subscription_plan_start_date: "subscriptionDate"
    // geo: "villagesList",
    // user_type_id : "userType"
  },
};

const initialFormState = {
  staffName: { value: {} },
  // staffVisualId: {},
  staffEmailAddress: {},
  staffMobile: {},
  // staffStatus: {},
  staffType: {},
  // villagesList: {},
  // userType : {
  //   value : [1000105002]
  // }
  staffPAN: {},
  staffAadhar: {},
  subscriptionPlan : {},
  subscriptionDate : {}
};
const items = [
  {
    title: 'Basic Details',
    status: 'finish',
    icon: <UserAddOutlined />,
  }
]
let financeTab = 0
let staffIdGolbal = ""
const uploadStaffProfileImage = async (payload,navigate) => {
  try {
    // const response = await axios.post(BASE_URL + "/document", formData, {
    //   headers: {
    //     token: localStorage.getItem("accessToken"),
    //     "X-App-Id": "123456789",
    //   },
    // });
    const response = await instance({
      url: "/document",
      method: "POST",
      data: payload,
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("File upload response", response);
    if (response.status === 201) {
      ToastersService.successToast("File uploaded to server!");
    } else {
      throw Error("Image not uploaded to server!");
    }
  } catch (error) {
    console.log(error);
    // ToastersService.failureToast(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
    if (error.response.status === 413) {
      ToastersService.failureToast(
        error.response.data.error
          ? error.response.data.error
          : "File size limit exceeded!"
      );
    }
    // setCreatingTaskLoader(false);
  }
};
const getStaffConfigurationsAsLists = async (
  navigate,
  formState,
  setFormState,
  isPPUU
) => {
  try {
    const response = await instance({
      url: "/references/staff",
      method: "GET",
      // data: {
      //   filterList: [
      //     // { key: "f_staff_village", match_literal: false },
      //     { key: "f_staff_type", match_literal: false },
      //     // { key: "f_active_status", match_literal: false },
      //   ],
      // },
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Staff configurations", response);
    if (response.status === 200) {
      console.log("hello");
      // const staffTypeList = response.data.data.f_staff_type;

      // removing freelancer staff while creating normal 
      const withoutFreelancer = response?.data?.data?.staff?.filter((item) => item.id !== 1000230006)
      const newFormState = assignDataToFormState(
        staffFormDefinition,
        {
          staff_type_id_list: isPPUU ? response?.data?.data?.staff : withoutFreelancer,
          staff_type_id: isPPUU ? [1000230006] : [],
          staff_plan_list : response?.data?.data?.staff_plan
        },
        formState,
        setFormState
      );
      console.log(formState)
      return [newFormState];
    }
  } catch (error) {
    console.log(error);
    // throw error;
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};
const setReferenceValueFromRefData = (formState,setFormState,isPPUU)=>{

  // removing freelancer staff while creating normal
  const withoutFreelancer = globalReferencesMapper(10002300).filter((item) => item.reference_id !== 1000230006)
  const newFormState = assignDataToFormState(
    staffFormDefinition,
    {
      staff_type_id_list: isPPUU ? globalReferencesMapper(10002300) : withoutFreelancer,
      staff_type_id: isPPUU ? [1000230006] : [],
      staff_plan_list : globalReferencesMapper(10008600)
    },
    formState,
    setFormState
  );
  return [newFormState];
}

const createStaffBtnClickHandler = async (
  navigate,
  formState,
  setFormState,
  reason,
  isPPU,
  setStaffId,
  setIsDone,
  profileUrl,
  setShowAddStaffModal,
  setRefresh,
  setCurrent,
  dispatch
) => {
  console.log("AF1 sF 1, formState = ", formState);
  const [currentFormState, validationFailed] = validateFormElements(
    staffFormDefinition,
    formState,
    setFormState
  );
  // console.log("AF1 sF 2, currentFormStateTab0 = ", currentFormStateTab0);
  if (validationFailed) {
    // do something
    console.log("Validation Failed");
    ToastersService.failureToast("Validation failed!");
  } else {
    console.log("you can save now");
    const extractedValues1 = extractChangedValues(
      staffFormDefinition,
      currentFormState
    );
    console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValues1);
    let extractedValues = {};
    extractedValues = { ...extractedValues, ...extractedValues1 };

    console.log("AF1 sF 7, extractedValues = ", extractedValues);
    configureParams(extractedValues, navigate,reason,isPPU,setStaffId,setIsDone,profileUrl,setShowAddStaffModal,setRefresh,setCurrent,dispatch);
  }
};

const modifySelectValueFormat = (object, singleSelectKeys, multiSelectKeys) => {
  console.log(object, singleSelectKeys, multiSelectKeys);

  for (const singleSelectKey of singleSelectKeys) {
    // const existingValue = object[singleSelectKey];
    // object[singleSelectKey] = String(existingValue);
    const existingValue = object[singleSelectKey];
    if (existingValue && existingValue.length > 0) {
      if (existingValue[0] !== undefined) {
        object[singleSelectKey] = String(existingValue);
      } else {
        object[singleSelectKey] = null;
      }
    }
  }

  for (const multiSelectKey of multiSelectKeys) {
    const existingValue = object[multiSelectKey];
    if (existingValue && existingValue.length > 0) {
      if (Array.isArray(existingValue) && existingValue.length > 0) {
        // const updatedExistingValue = { selected: existingValue };
        const updatedExistingValue = existingValue;
        object[multiSelectKey] = updatedExistingValue;
      } else {
        throw new Error("Wrong formatting");
      }
    }
  }
};

const configureParams = (extractedValues, navigate,reason,isPPU,setStaffId,setIsDone,profileUrl,setShowAddStaffModal,setRefresh,setCurrent,dispatch) => {
  console.log("Configure Params", extractedValues);

  // for (let key of Object.keys(extractedValues)) {
  //   if (extractedValues[key] !== null) {
  //     if (
  //       typeof extractedValues[key] === "object" &&
  //       !Array.isArray(extractedValues[key]) &&
  //       !(extractedValues[key] instanceof Date)
  //     ) {
  //       extractedValues[key] = extractedValues[key].ul;
  //       console.log(extractedValues[key]);
  //     }
  //   }
  // }

  // console.log('Extracted valuesss', extractedValues);

  modifySelectValueFormat(
    extractedValues,
    [
      // "type_of_dairy_farm_1",
      // "farmer_district_1",
      // "farmer_taluk_1",
      // "farmer_village_1",
    ],
    [
      // "foliage_crops_grown_1",
      // "assets_owned_1",
      // "current_loan_1"
    ]
  );
  console.log("AF1 sF 8, extractedValues = ", extractedValues);
  console.log("params1", extractedValues);
  
  // adding notes 
  const extractedValuesWithNotes = isPPU ? {...extractedValues, staff_type_id:[1000230006],notes : reason ? reason : null} : {...extractedValues , notes : reason ? reason : null}
  // call create staff API
  createStaff(extractedValuesWithNotes, navigate,isPPU,setStaffId,setIsDone,profileUrl,setShowAddStaffModal,setRefresh,setCurrent,dispatch);
  // dispatch(setPpuData({st1:JSON.stringify(extractedValues)}))

};

const createStaff = async (params, navigate,isPPU,setStaffId,setIsDone,profileUrl,setShowAddStaffModal,setRefresh,setCurrent,dispatch) => {
  let reqBody = {
    ...params,
    // page: 1,
    // farmer_taluka_1: params["farmer_taluk_1"],
  };
  console.log("Req body", reqBody);
  try {
    const response = await instance({
      url: "/staffs/create",
      method: "POST",
      data: reqBody,
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Create Staff Response", response);
    if (response.status == 201) {
      ToastersService.successToast("Staff created successfully!");
      if (isPPU) {
        setRefresh(Math.random())
        if (response?.data?.staff_type_id === 1000230006) {
          // setCurrent(1)
          financeTab = 1
          dispatch(setFinanceTabVisible(1))
          // items.push(
          //   {
          //     title: 'Finance',
          //     status: 'finish',
          //     icon: <PaymentIcon />,
          //   },
          // ) 
          // setIsDone(true)
        }else{
          setShowAddStaffModal(false)
        }
      }
      if (profileUrl) {
        const reqBody = {
          document_entries: [
            {
              document_type: 'staff_thumbnail_photo_1',
              entity: "pay_per_use_paravet",
              entity_1_id: response.data.data,
              entity_1_type: 1000230006,
              url: profileUrl,
              document_information: "",
            },
          ],
        };
        uploadStaffProfileImage(reqBody,navigate)
      }
      setStaffId(response.data?.staff_id)
      staffIdGolbal = response?.data?.staff_id
      if (!isPPU) {
        navigate("/staffs/" + response.data?.staff_id);
      }
    }
  } catch (error) {
    console.log(error);
    ToastersService.failureToast("Staff is not created!");
    if (error?.response?.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const AddStaff = (props) => {
  console.log(props)
  const navigate = useNavigate();
  const [formState, setFormState] = useState(initialFormState);
  const dispatch = useDispatch();
  const [current,setCurrent] = useState(0);
  const [staffId,setStaffId] = useState("")
  const [isDone,setIsDone] = useState(false);
  const [profileUrl,setProfileUrl] = useState();
  const financeTabVisible = useSelector(state => state.ppuu.financeTabVisible)

  // const dispatch=useDispatch();
  // const PPuState=useSelector((state)=>state?.ppu?.PpuData);
  // console.log(PPuState)
  
  const[reason,setReason] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const showModal = () => {
    
    const [currentFormState, validationFailed] = validateFormElements(
      staffFormDefinition,
      formState,
      setFormState
    );
    console.log(currentFormState)
  
    if (validationFailed) {
      // do something
      console.log("Validation Failed")
      ToastersService.failureToast("Validation failed!");
    }else{
      setIsModalOpen(true);
      console.log(formState)
    }
  };
  const handleCancel = () => {
    setIsModalOpen(false);
    setReason("")
  };
  const reasonCreateHandler = ()=>{
    if (!reason || (reason && notesPattern.test(reason))) {
      createStaffBtnClickHandler(navigate,formState, setFormState,reason,props.isPPUU,setStaffId,setIsDone,profileUrl,props.setShowAddStaffModal,props.setRefresh,setCurrent,dispatch);
      setIsModalOpen(false)
    } else {
      ToastersService.failureToast("please give valid notes")
    }
}
 
  
  // useEffect(() => {
  //   if (props.isPPUU) {
  //     staffFormDefinition.visibleFormControls = [
  //       ...staffFormDefinition.visibleFormControls,
  //       "staffPAN",
  //       "staffAadhar"
  //     ]
  //   } else {
  //     staffFormDefinition.visibleFormControls = staffFormDefinition.visibleFormControls.filter(item => item !== "staffPAN" && item !== "staffAadhar");
  //   }    
  //   const initializeForm = async () => {
  //     const updatedFormStateReturnValue = await getStaffConfigurationsAsLists(
  //       navigate,
  //       formState,
  //       setFormState
  //     );
  //   };
  //   initializeForm();
  // }, []);
  useEffect(() => {
    if (props.isPPUU) {
      if (!staffFormDefinition.visibleFormControls.includes("staffPAN")) {
        staffFormDefinition.visibleFormControls.push("staffPAN");
      }
      if (!staffFormDefinition.visibleFormControls.includes("staffAadhar")) {
        staffFormDefinition.visibleFormControls.push("staffAadhar");
      }
      
      const mandatoryValidationIndex = staffFormDefinition.formControls.staffEmailAddress.validations.findIndex(validation => validation.type === 'Mandatory');
    
    // If it exists, remove it
    if (mandatoryValidationIndex !== -1) {
        staffFormDefinition.formControls.staffEmailAddress.validations.splice(mandatoryValidationIndex, 1);
    }
      if (!staffFormDefinition.visibleFormControls.includes("subscriptionPlan")) {
        staffFormDefinition.visibleFormControls.push("subscriptionPlan");
      }
      if (!staffFormDefinition.visibleFormControls.includes("subscriptionDate")) {
        staffFormDefinition.visibleFormControls.push("subscriptionDate")
      }
   
      staffFormDefinition.formControls.staffType.readOnly = 1
    } else {
      staffFormDefinition.visibleFormControls = staffFormDefinition.visibleFormControls.filter(item => item !== "staffPAN" && item !== "staffAadhar" && item !== "subscriptionPlan" && item !== "subscriptionDate");
      const mandatoryValidationIndex = staffFormDefinition.formControls.staffEmailAddress.validations.findIndex(validation => validation.type === 'Mandatory');
      // if (!staffFormDefinition.visibleFormControls.includes("staffType")) {
      //   staffFormDefinition.visibleFormControls.push("staffType")
      // }
      // If it doesn't exist, add it
      if (mandatoryValidationIndex === -1) {
          staffFormDefinition.formControls.staffEmailAddress.validations.push({type:"Mandatory"});
      }
      staffFormDefinition.formControls.staffType.readOnly = 0
      // Filter out 'staffPAN' and 'staffAadhar'
      // staffFormDefinition.visibleFormControls = staffFormDefinition.visibleFormControls.filter(item => item !== "staffPAN" && item !== "staffAadhar");
    }    
    // if (isDone) {
    //   setCurrent(1)
    //   props.setRefresh(Math.random())
    // }
    const initializeForm = () => {
      // const updatedFormStateReturnValue = await getStaffConfigurationsAsLists(
      //   navigate,
      //   formState,
      //   setFormState
      // );
      // console.log("staff=form",staffFormDefinition)
      // assignReferencesAsListToForm(staffFormDefinition)
      // assignDataToFormState(staffFormDefinition,{},formState,setFormState)
      setReferenceValueFromRefData(formState,setFormState,props.isPPUU)
    };
    initializeForm();
  }, []);
  
  const onChange = (value) => {
    console.log('onChange:', value);
    setCurrent(value);
  };
  useEffect(() => {
    setFormState((prevState) => {
      let staffPANValue = prevState.staffPAN.displayValue;
      let updatedValue = typeof staffPANValue === 'string' ? staffPANValue.toUpperCase() : null;
      return {
        ...prevState,
        staffPAN: { displayValue: updatedValue }
      };
    });
  }, [formState.staffPAN?.displayValue]);
  
  return (
    <>
      {props.isPPUU === undefined ?
      (<div>
        <Link className="farmer-details__back-btn" to="/staffs">
          <i className="fa fa-angle-double-left" aria-hidden="true"></i> Back to
          staffs list
        </Link>
 
        <p
          className="mb-0 mt-3"
          style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
        >
          Staffs
        </p>
        <p
          className="m-0"
          style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
        >
          Add New Staff
        </p>
      </div>) : (<p style={{fontWeight:"bold"}}>Add Staff</p>)
      }
      <hr />
      {/* {
        (formState.staffType.value?.[0] === 1000230007 || formState.staffType.value?.[0] === 1000230006) && (
          <div style={{width:"50%"}}><Steps current={current} onChange={onChange} items={items}/></div>
        )
      } */}
      {/* <div style={{width:"50%"}}><Steps current={financeTabVisible} items={items}/></div> */}
      {financeTabVisible === 0 && (
        <div>
          <KrushalOCForm
            formState={formState}
            setFormState={setFormState}
            formDefinition={staffFormDefinition}
          />
        {props.isPPUU && (
          <>
        <ImageUploaderAntd setProfileUrl={setProfileUrl}/>
        <p style={{textAlign:"center", color: "#333", fontSize: "16px", fontWeight: "600"}}>
        <span style={{color : "red"}}>Note* : </span> 
        Please enter at least Aadhaar or PAN number.
    </p>
    </>
        )
      }

      <div style={{ display: "flex" }}>
        <div style={{ marginLeft: "auto" }}>
          <Button
            variant="primary"
            onClick={()=>{
              showModal()
            }
            }
            style={{ background: "#ec6237", color: "#fff", border: "none" }}
          >
            Create
          </Button>
        </div>
      </div>
        </div>
      )}
      
      {financeTabVisible === 1 && (
        <PpuuFinance staffId={staffIdGolbal} AddStaffPage={true} setShowAddStaffModal={props.setShowAddStaffModal} setCurrent={financeTab} />
      )}
      <ReasonModal 
       isMandatory={false}
       isModalOpen={isModalOpen}
       handleCancel={handleCancel}
       setReason={setReason}
       reasonCreateHandler={reasonCreateHandler}
      />
    </>
  );
};

// export default AddStaff;
export default withAuthorization(AddStaff, [OPS_TEAM, IT_ADMIN])