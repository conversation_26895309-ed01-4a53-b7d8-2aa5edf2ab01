.manualCss {
  width: 70rem;
  /* border: none !important; */
}
.manualCss fieldset {
  border: none !important;
}

/* .MuiTextField-root.manualCss .MuiOutlinedInput-notchedOutline {
    border: none !important;
    background-color: black;
  } */

.manualCss svg[data-testid="ArrowDropDownIcon"] {
  display: none;
}

.villageAllot {
  padding: 15px;
  border: 1px solid gray;
  width: fit-content;
  border-radius: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 5px;
  height: 32px;
  color: rgba(0, 0, 0, 0.87);
  background-color: rgba(0, 0, 0, 0.08);
  font-size: 1rem;
}
.cross {
  margin-left: 8px;
  border: none;
  font-size: 12px;
  outline: 1px solid grey;
  padding-bottom: 1px;
  background: transparent;
  font-weight: 100;
  border-radius: 50%;
}

.staff-tab {
  font-weight: 600;
  font-size: 17px;
  display: flex;
  align-items: center;
  /* border: 1px solid silver; */
  margin-right: 1rem;
  cursor: pointer;
  color: #bdbdbd;
  padding: 0.5rem;
}

.staff-tab.selected {
  color: #ec6237;
  border-bottom: 3px solid #ec6237;
}

.staff-tab.disabled {
  pointer-events: none;
  opacity: 0.5;
}

.error-box{
  border: 1px solid red;
}