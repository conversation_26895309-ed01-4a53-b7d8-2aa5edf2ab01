import { useCallback, useState } from "react";
import {
  Link,
  useNavigate,
  useParams,
  unstable_usePrompt,
  useLocation,
} from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import ToastersService from "../../../../Services/toasters.service";
import { useEffect } from "react";
import { BASE_URL, instance } from "../../../../Services/api.service";
import Loader from "../../../Loader/Loader";
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";
import <PERSON>Viewer from "react-simple-image-viewer";
import OutlinedInput from "@mui/material/OutlinedInput";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import "./StaffItem.css";

import { <PERSON><PERSON>, Col, Row } from "react-bootstrap";

import Checkbox from "@mui/material/Checkbox";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import { clearUserToken } from "../../../user/action";

import { Table, message, notification, Modal, Select as AntdSelect } from "antd";
import TimeLine from "../../../../Utilities/Common/TimeLine";

import ReasonModal from "../../../../Utilities/Common/ModalComponents/ReasonModal";
import { globalReferencesMapper, notesPattern } from "../../../../Utilities/Common/utils";
import {
  nameRegex,
  emailRegex,
  mobileRegex,
  aadharRegex,
  panRegex,
} from "../../../../Utilities/Common/TempCommonRegEx";
import _ from 'lodash';

import TransactionHistory from "../../PPU/TransactionHistory";

const {CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, IT_ADMIN, withAuthorization} = require('../../../user/authorize')

const { extractBasedOnLanguage } = require("@krushal-it/common-core");

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;
const userTypeList = [
  {
    id: 1000105001,
    name_l10n: {
      ul: "Online",
    },
  },
  {
    id: 1000105002,
    name_l10n: {
      ul: "Offline",
    },
  },
];

const accessGroupOptionFormat = () => {
  return globalReferencesMapper(10011500).map(item => ({
    label: item.reference_name_l10n,
    value: item.reference_id
  }));
}

const StaffItem = (props) => {
  console.log(props.isPPUU);
  const params = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userData = useSelector((state) => state.user.userData);
  const userToken = useSelector((state) => state.user.userToken);
  const userLanguage = useSelector((state) => state.user.userLanguage);
  const location = useLocation();
  const navigationStateValue = location.state;

  const [staffDetailsLoader, setStaffDetailsLoader] = useState(true);
  const [selectAll, setSelectAll] = useState(false);
  const [staffDetails, setStaffDetails] = useState([]);
  const [statusList, setStatusList] = useState([]);
  const [staffType, setStaffType] = useState([]);
  const [statesList, setStatesList] = useState([]);
  const [districtsList, setDistrictsList] = useState([]);
  const [talukasList, setTalukasList] = useState([]);
  const [villagesList, setVillagesList] = useState([]);
  const [geoList, setGeoList] = useState([]);
  const [selectedState, setSelectedState] = useState(null);
  const [selectedDistrict, setSelectedDistrict] = useState(null);
  const [selectedTaluk, setSelectedTaluk] = useState(null);

  const [selectedValue, setSelectedValue] = useState([]);
  const [thumbnailImage, setThumbnailImage] = useState();
  const [currentThumbnailImage, setCurrentThumbnailImage] = useState(0);
  const [isThumbnailViewerOpen, setIsThumbnailViewerOpen] = useState(false);
  const [showDuplicatePopup, setShowDuplicatePopup] = useState(false);
  let [updateStaffForm, setUpdateStaffForm] = useState({});
  const [village, setVillage] = useState([]);

  const [accessRole, setAccessRole] = useState({});
  const [identifier, setIdentifier] = useState("");
  const [isDisabled, setIsDisabled] = useState(false);
  const [modal, contextHolder] = Modal.useModal();
  const [staticData, setStaticData] = useState([]);
  const [refresh, setRefresh] = useState(1);
  const [staticGeoList, setStaticGeoList] = useState([]);
  const [accessGroupList, setAccessGroupList] = useState([]);
  const appGroups = useSelector(state => state.user?.appGroups)
  const vehicleTypeList = globalReferencesMapper(10012400)

// if (appGroups.includes(EXEC_TEAM,IT_ADMIN)) {
// your logic to show this extra row
// }

  const openThumbnailImageViewer = useCallback((index) => {
    setCurrentThumbnailImage(index);
    setIsThumbnailViewerOpen(true);
  }, []);
  const closeThumbnailImageViewer = () => {
    setCurrentThumbnailImage(0);
    setIsThumbnailViewerOpen(false);
  };
  // elementToDataMapping is using to map the data with appropriate key
  const elementToDataMapping = {
    Status: "active_status_id",
    Role: "staff_type_id",
    "Online/Offline": "app_user_type",
    "Access Group" : "access_group"
  };
  // common modal confirmation
  const getModalConfirmation = (title, content, onCancel, onOk) => {
    modal.confirm({
      title,
      content,
      okText: "ok",
      cancelText: "cancel",
      onCancel: () => {
        setAccessRole("");
        onCancel();
      },
      onOk,
    });
  };

  const functionMapping = {
    Status: (onCancel) => {
      const isActive =
        staffDetails.active_status_id[0] == 1000100001 ||
        staffDetails.active_status_id == 1000100001;
      const content = isActive
        ? "Changing the staff status to inactive will revoke its access from the app. Staff will not be able to login, all the village assignments will be removed. Do you want to continue?"
        : "Please provide required access like assignment of village, changing of role, type after activating the staff.";
      getModalConfirmation("Confirm", content, onCancel, () => {
        // if (!isActive) {
        //   modal.info({
        //     title: 'Info',
        //     content: 'Staff activated successfully. Please provide the required access.'
        //   });
        // }
      });
    },
    Role: (onCancel) => {
      const content =
        "Changing the role may impact on the existing user and the existing user should synchronize, logout and login for a seamless error-free experience. Do you want to continue?";
      getModalConfirmation("Confirm", content, onCancel, () => {
        // modal.info({
        //   title: 'Info',
        //   content: `Please ask ${staffDetails.staff_name_l10n.ul} to synchronize, logout and login for a seamless error-free experience.`
        // });
      });
    },
    "Online/Offline": (onCancel) => {
      const content =
        "Changing the type may impact on the existing user and the existing user should synchornize , logout and login for a seamless error free experience. Do you want to continue?";
      getModalConfirmation("Confirm", content, onCancel, () => {
        // modal.info({
        //   title: 'Info',
        //   content: `Please ask ${staffDetails.staff_name_l10n.ul} to synchornize , logout and login for a seamless error free experience.`
        // });
      });
    },
  };

  const accessGroupChangeHandler = (value)=>{
    console.log(value)
    setAccessGroupList(value)
  }

  const returnIsDisabledForAccessGroup = ()=>{
    const arr1 = staticData?.staff_app_access_groups_1
    const arr2 = accessGroupList
    const returnValue = staticData ?  _.isEqual(arr1,arr2) : undefined
    if (!(appGroups?.includes(1001150001) || appGroups?.includes(1001150002)) || isDisabled || returnValue) {
      return true
    }
  }

  const accessRoleHandler = (event, name) => {
    const dropdownValue = event.target.value;
    const key = elementToDataMapping[name];

    // Store the previous value
    const prevValue = staffDetails[key];

    setStaffDetails((prevDetails) => {
      const updatedDetails = {
        ...prevDetails,
        [key]:
          dropdownValue === 1000105001 && key === "app_user_type"
            ? ["ONLINE"]
            : dropdownValue === 1000105002 && key === "app_user_type"
            ? ["OFFLINE"]
            : dropdownValue,
      };

      setAccessRole((prevState) => ({
        ...prevState,
        [key]: [dropdownValue],
      }));

      const func = functionMapping[name];
      if (
        (updatedDetails[key][0] || updatedDetails[key]) !==
        (staticData[key][0] || staticData[key])
      ) {
        if (func) {
          func(() => {
            // Revert to the previous value when the user cancels
            setStaffDetails((prevDetails) => ({
              ...prevDetails,
              [key]: prevValue,
            }));
          });
        }
      }

      return updatedDetails;
    });
  };

  const dataSource = [
    {
      key: "1",
      label: "Status",
      options: statusList,
    },
    {
      key: "2",
      label: "Role",
      options: staffType,
    },
    {
      key: "3",
      label: "Online/Offline",
      options: userTypeList,
    },
    {
      key : "4",
      label : "Access Group",
      options : accessGroupOptionFormat(),
      isMulti : true
    }
  ];
  const columns = [
    {
      title: "Label",
      dataIndex: "label",
      key: "label",
    },
    {
      title: "Options",
      dataIndex: "options",
      key: "options",
      render: (options, record) => {
        let initialValue;
        if (record.label === "Status") {
          initialValue = staffDetails.active_status_id;
        } else if (record.label === "Role") {
          initialValue = staffDetails.staff_type_id;
        } else if (record.label === "Online/Offline") {
          if (
            staffDetails.app_user_type[0] === "ONLINE" ||
            staffDetails.app_user_type === "ONLINE"
          ) {
            initialValue = [1000105001];
          }
          if (
            staffDetails.app_user_type[0] === "OFFLINE" ||
            staffDetails.app_user_type === "OFFLINE"
          ) {
            initialValue = [1000105002];
          }
        }
        // Add more conditions as needed
        return record?.isMulti === true  ?
        (
          <AntdSelect
            mode="multiple"
            allowClear
            showSearch
            style={{
              width: '300px',
            }}
            placeholder="Please select"
            onChange={accessGroupChangeHandler}
            options={options}
            value={accessGroupList}
            virtual={false}
            filterOption={(input, option) =>
              option?.label?.toLowerCase()?.indexOf(input?.toLowerCase()) >= 0
            }
            disabled={isDisabled}
        />

        ) :
         (
          <Select
            value={initialValue}
            style={{ width: 300, height: 30 }}
            onChange={(event) => accessRoleHandler(event, record.label)}
            disabled={
              (isDisabled && record.label !== "Status") ||
              (record.label === "Online/Offline" &&
                [1000230005, 1000230002].includes(staticData?.staff_type_id[0]))
            }
          >
            {options.map((option, index) => (
              <MenuItem key={option.id} value={option.id}>
                {option.name_l10n.ul}
              </MenuItem>
            ))}
          </Select>
        );
      },
    },
    {
      title: "Action",
      key: "action",
      render: (text, record) => {
        const nameMap = {
          Status: "Change Status",
          Role: "Change Role",
          "Online/Offline": "Change Type",
          "Access Group" : "Change Access"

        };
        // const functionNameMapping = {
        //   'Staff Status': updateStaffStatus,
        //   'Staff Role': updateStaffRole,
        //   'Online/Offline': updateAppUserType
        // }
        const name = nameMap[record.label];
        const key = elementToDataMapping[record.label];
        // const accessRoleFunction = functionNameMapping[record.label]
        //  onClick={()=>accessRoleFunction()}
        // (isDisabled && record.label !== 'Status') &&
        return record?.isMulti === true  ?
         (
          <Button
            onClick={() => {
              showModal();
              setIdentifier(name);
            }}
            style={{
              width: "150px",
              backgroundColor: "#673AB7",
              border: "none",
            }}
            type="primary"
            // disabled={!(appGroups?.includes(1001150001) || appGroups?.includes(1001150002)) || isDisabled}
            disabled={returnIsDisabledForAccessGroup()}
          >
            {name}
            </Button>
         )
         : (
          <Button
            onClick={() => {
              showModal();
              setIdentifier(name);
            }}
            style={{
              width: "150px",
              backgroundColor: "#673AB7",
              border: "none",
            }}
            type="primary"
            disabled={
              (isDisabled && record.label !== "Status") ||
              (staffDetails[key][0] || staffDetails[key]) ===
                (staticData[key][0] || staticData[key])
            }
          >
            {name}
          </Button>
        );
      },
    },
  ];

  const [staffTabs, setStaffTabs] = useState([
    {
      count: 1,
      isSelected: navigationStateValue ? false : true,
      isDisabled: false,
      name: "Basic",
    },
    {
      count: 2,
      isSelected: false,
      isDisabled: false,
      name: "Access & Roles",
    },
    {
      count: 3,
      isSelected: false,
      name: "Villages",
    },
    {
      count: 4,
      isSelected: false,
      name: "TimeLine",
    },
    // {
    //   count: 5,
    //   isSelected: false,
    //   name : "Finance"
    // }
  ]);

  const getStaffById = async () => {
    try {
      const [statusTypeList, staffTypeList, villageList] =
        await getStaffConfigurationsAsLists(navigate);
      const response = await instance({
        url: "/staffs/" + params.staffId,
        method: "POST",
        data: { thumbnail: 1 },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status === 201) {
        console.log("Staff Details", response.data.data);

        if (response.data.data == -1) {
          ToastersService.failureToast("Staff details not found!");
        } else {
          setStaffDetails(response.data.data);
          setStaticData(response.data.data);
          setGeoList(response.data.data.geo);
          setAccessGroupList(response?.data?.data?.staff_app_access_groups_1)
          if (response.data.data.staff_type_id[0] === 1000230006) {
            setStaffTabs((prevState) => {
              // Check if the object already exists in the array
              const alreadyExists = prevState.some(
                (tab) => tab.name === "Finance"
              );

              // If the object doesn't exist, add it to the array
              if (!alreadyExists) {
                return [
                  ...prevState,
                  {
                    count: 5,
                    isSelected: navigationStateValue
                      ? navigationStateValue
                      : false,
                    name: "Finance",
                  },
                ];
              }

              // If the object already exists, return the previous state
              return prevState;
            });
          }

          if (response.data.data.staff_thumbnail_id) {
            setThumbnailImage(
              BASE_URL + "/media/" + response.data.data.staff_thumbnail_id
            );
          }
        }
        setStaffDetailsLoader(false);
        if (response?.data?.data?.active_status_l10n?.ul === "Inactive") {
          setIsDisabled(true);
        } else {
          setIsDisabled(false);
        }
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const getStaffConfigurationsAsLists = async (navigate) => {
    try {
      const response = await instance({
        url: "/filters",
        method: "POST",
        data: {
          filterList: [
            { key: "f_staff_village", match_literal: false },
            { key: "f_staff_type", match_literal: false },
            { key: "f_active_status", match_literal: false },
          ],
        },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      console.log("Staff configurations", response);
      if (response.status === 201) {
        const statusTypeList = setStatusList(
          response.data.data.f_active_status
        );
        const staffTypeList = setStaffType(response.data.data.f_staff_type);
        const villageList = setVillage(response.data.data.f_staff_village);
        return [statusTypeList, staffTypeList, villageList];
      }
    } catch (error) {
      console.log(error);
      // throw error;
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  console.log(geoList, "geo");
  useEffect(() => {
    getStaffById();
    getStatesList();
    // if (navigationStateValue && staffTabs.some(item => item.count === 4)) {
    //   setStaffTabs(prevState => prevState.map((item, index) =>
    //     index === 4 ? { ...item, isSelected: true } : item
    //   ));
    // }
  }, [refresh, isDisabled]);

  const onChangeTab = (tabCount) => {
    console.log(tabCount);
    for (let item of staffTabs) {
      if (item.count === tabCount) {
        item.isSelected = true;
      } else {
        item.isSelected = false;
      }
    }
    setStaffTabs([...staffTabs]);
  };

  const getStatesList = async () => {
    try {
      const response = await instance({
        url: "/location/state",
        method: "GET",
        headers: {
          token: userToken.accessToken,
        },
      });
      if (response.status === 200) {
        setStatesList(response.data.data);
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const getDistrictsList = async (stateId) => {
    try {
      const response = await instance({
        url: "/location/district?state_id=" + stateId,
        method: "GET",
        headers: {
          token: userToken.accessToken,
        },
      });
      if (response.status === 200) {
        setDistrictsList(response.data.data);
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const getTalukaList = async (districtId) => {
    try {
      const response = await instance({
        url: "/location/taluka?district_id=" + districtId,
        method: "GET",
        headers: {
          token: userToken.accessToken,
        },
      });
      if (response.status === 200) {
        setTalukasList(response.data.data);
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const getVillagesList = async (talukId, geoList) => {
    try {
      const response = await instance({
        url: "/location/village?taluka_id=" + talukId,
        method: "GET",
        headers: {
          token: userToken.accessToken,
        },
      });
      if (response.status === 200) {
        console.log("geoListttt", geoList);
        const filteredVillages = response.data.data.filter(
          (village) =>
            !geoList.some((geo) => geo.village_id === village.village_id)
        );
        setVillagesList(filteredVillages);
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const stateInputChangeHandler = (event, value) => {
    console.log(value);
    if (value !== null) {
      getDistrictsList(value);
      setSelectedDistrict(null);
    }
    if (districtsList?.length > 0) {
      setVillagesList([]);
      setTalukasList([]);
      setSelectedDistrict(null);
      setSelectedTaluk(null);
    }
  };

  const districtInputChangeHandler = (event, value) => {
    console.log(value);
    if (value !== null) {
      getTalukaList(value);
      setSelectedTaluk(null);
    }
    if (villagesList?.length > 0) {
      setVillagesList([]);
    }
  };

  const talukaInputChangeHandler = (event, value) => {
    console.log(value);
    if (value !== null) {
      getVillagesList(value, geoList);
    }
  };

  const villageInputChangeHandler = (event, value) => {
    if (value.some((option) => option.village_name_l10n.en === "Select All")) {
      if (selectAll) {
        setSelectAll(false);
        setSelectedValue([]);
      } else {
        setSelectAll(true);
        setSelectedValue(villagesList);
      }
    } else {
      setSelectAll(false);
      setSelectedValue(value);
    }
  };

  const addVillageHandler = () => {
    if (selectedValue.length > 0) {
      const selectedVillageIds = selectedValue.map((value) =>
        typeof value === "number" ? value : value.village_id
      );

      const isVillageAlreadyAdded = selectedVillageIds.some((villageId) =>
        geoList.some((option) => option.village_id === villageId)
      );

      if (isVillageAlreadyAdded) {
        ToastersService.failureToast("Village(s) is already added!");
        setSelectedValue([]);
      } else {
        setGeoList((prevList) => [...prevList, ...selectedValue]);
        setSelectedValue([]);
      }
    }
  };

  const removeVillageHandler = (index) => {
    setGeoList((prevList) => {
      const updatedList = [...prevList];
      updatedList.splice(index, 1);
      return updatedList;
    });
  };

  const removeAllVillageHandler = () => {
    setGeoList([]);
  };

  const handleSelectAllChange = (event) => {
    const checked = event.target.checked;
    setSelectAll(checked);
    setSelectedValue(checked ? villagesList : []);
  };

  const nameChangeHandler = (event) => {
    setStaffDetails((prevDetails) => ({
      ...prevDetails,
      staff_name_l10n: { ul: event.target.value },
    }));
    setUpdateStaffForm({
      ...updateStaffForm,
      staff_name_l10n: { ul: event.target.value },
    });
  };

  const handleActiveStatusChange = (event) => {
    const selectedValue = event.target.value;
    setStaffDetails((prevDetails) => ({
      ...prevDetails,
      active_status_id: selectedValue,
    }));
    setUpdateStaffForm({
      ...updateStaffForm,
      active_status_id: [selectedValue],
    });
  };

  const handleEmailChange = (event) => {
    const selectedValue = event.target.value;
    setStaffDetails((prevDetails) => ({
      ...prevDetails,
      email_address: selectedValue,
    }));
    setUpdateStaffForm({
      ...updateStaffForm,
      email_address: selectedValue,
    });
  };

  const handleMobileChange = (event) => {
    const selectedValue = event.target.value;
    setStaffDetails((prevDetails) => ({
      ...prevDetails,
      mobile_number: selectedValue,
    }));
    setUpdateStaffForm({
      ...updateStaffForm,
      mobile_number: selectedValue,
    });
  };

  const handlePanChange = (event) => {
    const selectedValue = event.target.value?.toUpperCase();
    setStaffDetails((prevDetails) => ({
      ...prevDetails,
      staff_pan: selectedValue,
    }));
    setUpdateStaffForm({
      ...updateStaffForm,
      staff_pan: selectedValue,
    });
  };

  const handleAadharChange = (event) => {
    const selectedValue = event.target.value;
    setStaffDetails((prevDetails) => ({
      ...prevDetails,
      staff_aadhar: selectedValue,
    }));
    setUpdateStaffForm({
      ...updateStaffForm,
      staff_aadhar: selectedValue,
    });
  };

  const vehicleTypeChangeHandler = (event)=>{
    const value = event.target.value
    setStaffDetails((prevDetails) => ({
      ...prevDetails,
      staff_vehicle_type_1: value,
    }));
    setUpdateStaffForm({
      ...updateStaffForm,
      staff_vehicle_type_1: value,
    });
  }

  const handleStaffTypeChange = (event) => {
    console.log(event.target.value);
    const selectedValue = event.target.value;
    setStaffDetails((prevDetails) => ({
      ...prevDetails,
      staff_type_id: selectedValue,
    }));

    setUpdateStaffForm({
      ...updateStaffForm,
      staff_type_id: [selectedValue],
    });
  };

  console.log(params.staffId, "params");
  const updateStaff = async () => {
    try {
      const response = await instance({
        url: "/staffs/update",
        method: "POST",
        data: {
          ...updateStaffForm,
          staff_id: params.staffId,
          notes: reason,
        },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      console.log("Update Staff Response", response);
      if (response.status == 201) {
        if (response.data.data !== -1) {
          ToastersService.successToast("Details updated successfully!");
          // window.location.reload();
          setRefresh(refresh + 1);
          setUpdateStaffForm("");
        } else {
          ToastersService.failureToast("Something went wrong!");
        }
      }
    } catch (error) {
      console.log(error);

      if (error.response.status === 409) {
        console.log(error.response.data.id, "id1");
        ToastersService.failureToast(error.response.data.message);
        // const idList = error.response.data.id;
        // const newList = geoList.filter((value) => !idList.includes(value.village_id));
        // setGeoList([...newList]);
      } else if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      } else {
        ToastersService.failureToast("Staff not updated!");
      }
    }
  };

  const updateStaffStatus = async () => {
    try {
      const response = await instance({
        url: "/staffs/status/update",
        method: "POST",
        data: {
          staff_id: params.staffId,
          active_status_id: accessRole.active_status_id,
          notes: reason,
        },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status == 201) {
        setRefresh(refresh + 1);
        if (response.data.data !== -1) {
          // ToastersService.successToast("Status updated successfully!");
          ToastersService.successToast("Staff status changed successfully!");
          modal.info({
            title: "Info",
            content: `Staff ${
              staffDetails.active_status_id === 1000100001
                ? "Activated"
                : "Inactive"
            } successfully. Please provide the required access.`,
          });
          // setIsDisabled(true)
          // window.location.reload();
        } else {
          ToastersService.failureToast("Something went wrong!");
        }
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 409) {
        console.log(error.response.data.id, "id1");
        ToastersService.failureToast(error.response.data.message);
        // const idList = error.response.data.id;
        // const newList = geoList.filter((value) => !idList.includes(value.village_id));
        // setGeoList([...newList]);
      } else if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      } else {
        ToastersService.failureToast("Staff not updated!");
      }
    }
  };

  const updateStaffRole = async () => {
    try {
      const response = await instance({
        url: "/staffs/role/update",
        method: "POST",
        data: {
          staff_id: params.staffId,
          staff_type_id: accessRole.staff_type_id,
          notes: reason,
        },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status == 201) {
        if (response.data.data !== -1) {
          ToastersService.successToast("Details updated successfully!");
          setRefresh(refresh + 1);
          // window.location.reload();
          if (selectedTaluk) {
            getVillagesList(selectedTaluk.taluk_id, []);
          }
          modal.info({
            title: "Info",
            content: `Please ask ${extractBasedOnLanguage(
              staticData.staff_name_l10n,
              userLanguage
            )} to synchronize, logout and login for a seamless error-free experience.`,
          });
        } else {
          ToastersService.failureToast("Something went wrong!");
        }
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 409) {
        console.log(error.response.data.id, "id1");
        ToastersService.failureToast(error.response.data.message);
        // const idList = error.response.data.id;
        // const newList = geoList.filter((value) => !idList.includes(value.village_id));
        // setGeoList([...newList]);
      } else if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      } else {
        ToastersService.failureToast("Staff not updated!");
      }
    }
  };

  const updateAppUserType = async () => {
    try {
      const response = await instance({
        url: "/staffs/app-type/update",
        method: "POST",
        data: {
          staff_id: params.staffId,
          app_user_type:
            accessRole.app_user_type[0] === 1000105001 &&
            accessRole.app_user_type
              ? "ONLINE"
              : accessRole.app_user_type[0] === 1000105002 &&
                accessRole.app_user_type
              ? "OFFLINE"
              : null,
          notes: reason,
        },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status == 201) {
        if (response.data.data !== -1) {
          ToastersService.successToast("Details updated successfully!");
          //window.location.reload();
          setRefresh(refresh + 1);
          modal.info({
            title: "Info",
            content: `Please ask ${extractBasedOnLanguage(
              staticData.staff_name_l10n,
              userLanguage
            )} to synchornize , logout and login for a seamless error free experience.`,
          });
        } else {
          ToastersService.failureToast("Something went wrong!");
        }
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 409) {
        console.log(error.response.data.id, "id1");
        ToastersService.failureToast(error.response.data.message);
        // const idList = error.response.data.id;
        // const newList = geoList.filter((value) => !idList.includes(value.village_id));
        // setGeoList([...newList]);
      } else if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      } else {
        ToastersService.failureToast("Staff not updated!");
      }
    }
  };
  const updateStaffGroup = async () => {
    try {
      const response  = await instance({
        url : "/staffs/groups/update",
        method : "POST",
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
        data : {
          staff_id : params?.staffId,
          app_access_groups : accessGroupList,
          notes : reason
        }
      })
      if (response.status == 201) {
        if (response.data.data !== -1) {
          ToastersService.successToast("Details updated successfully!");
          setRefresh(refresh + 1);
        } else {
          ToastersService.failureToast("Something went wrong!");
        }
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 409) {
        console.log(error.response.data.id, "id1");
        ToastersService.failureToast(error.response.data.message);
      } else if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      } else {
        ToastersService.failureToast("Groups not updated!");
      }
    }
    }
  const updateStaffVillages = async () => {
    try {
      const response = await instance({
        url: "/staffs/geo/update",
        method: "POST",
        data: {
          staff_id: params.staffId,
          geo: geoList.map((option) => option.village_id),
          notes: reason,
        },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status == 201) {
        if (response.data.data !== -1) {
          ToastersService.successToast("village updated successfully!");
          setRefresh(refresh + 1);
          // window.location.reload();
          // if (villagesList.length > 0) {
          //   const filteredVillages = villagesList.filter(
          //     (village) => !geoList.some((geo) => geo.village_id === village.village_id)
          //   );
          //   setVillagesList(filteredVillages);
          // }
          if (selectedTaluk) {
            getVillagesList(selectedTaluk.taluk_id, geoList);
          }
        } else {
          ToastersService.failureToast("Something went wrong!");
        }
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 409) {
        console.log(error.response.data.id, "id1");
        ToastersService.failureToast(error.response.data.message);
        const idList = error.response.data.id;
        const newList = geoList.filter(
          (value) => !idList.includes(value.village_id)
        );
        setGeoList([...newList]);
      } else if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      } else {
        ToastersService.failureToast("Staff not updated!");
      }
    }
  };

  const [reason, setReason] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);

  const arraysEqual = (a, b) => {
    return (
      a.length === b.length &&
      a.every((val, index) => JSON.stringify(val) === JSON.stringify(b[index]))
    );
  };
  
  const showModal = () => {
    // if (staffTabs[0].isSelected === true) {
    //   if (Object.keys(updateStaffForm).length === 0) {
    //     notification.error({
    //       type : 'error',
    //       description : "You didn't modify anything!"
    //     })
    //   } else if ((updateStaffForm.staff_name_l10n && updateStaffForm.staff_name_l10n['ul'].trim()) === '' ||
    //             (updateStaffForm.email_address && updateStaffForm.email_address.trim()) === '' ||
    //             (updateStaffForm.mobile_number && updateStaffForm.mobile_number.trim()) === '') {
    //               notification.error({
    //                 type : 'error',
    //                 description : "Please fill all mandatory fields!"
    //               })
    //   }
    //    else {
    //     setIsModalOpen(true);
    //   }
    // }
    if (staffTabs[0].isSelected === true) {
      if (Object.keys(updateStaffForm).length === 0) {
        notification.error({
          type: "error",
          description: "You didn't modify anything!",
        });
      } else {
        if (
          updateStaffForm.staff_name_l10n &&
          !nameRegex.test(updateStaffForm.staff_name_l10n["ul"].trim())
        ) {
          notification.error({
            type: "error",
            description: "Invalid name!",
          });
        } else if (
          updateStaffForm.email_address &&
          !emailRegex.test(updateStaffForm.email_address.trim())
        ) {
          notification.error({
            type: "error",
            description: "Invalid email address!",
          });
        } else if (
          updateStaffForm.mobile_number &&
          !mobileRegex.test(updateStaffForm.mobile_number.trim())
        ) {
          notification.error({
            type: "error",
            description: "Invalid mobile number!",
          });
        } else if (
          (updateStaffForm.staff_name_l10n &&
            updateStaffForm.staff_name_l10n["ul"].trim()) === "" ||
          (updateStaffForm.email_address &&
            updateStaffForm.email_address.trim()) === "" ||
          (updateStaffForm.mobile_number &&
            updateStaffForm.mobile_number.trim()) === ""
        ) {
          notification.error({
            type: "error",
            description: "Please fill all mandatory fields!",
          });
        } else if (
          updateStaffForm.staff_aadhar &&
          !aadharRegex.test(updateStaffForm.staff_aadhar.trim())
        ) {
          notification.error({
            type: "error",
            description: "Invalid Aadhar Number!",
          });
        } else if (
          updateStaffForm.staff_pan &&
          !panRegex.test(updateStaffForm.staff_pan.trim())
        ) {
          notification.error({
            type: "error",
            description: "Invalid PAN Number!",
          });
        } else {
          setIsModalOpen(true);
        }
      }
    }

    if (staffTabs[1].isSelected === true) {
      setIsModalOpen(true)
      // if (Object.keys(accessRole).length === 0) {
      //   notification.error({
      //     type: "error",
      //     description: "You didn't modify anything!",
      //   });
      // } else {
      //   setIsModalOpen(true);
      // }
    }
    if (staffTabs[2].isSelected === true) {
      // arraysEqual(geoList, staticData.geo)
      if (arraysEqual(geoList, staticData.geo)) {
        notification.error({
          type: "error",
          description: "You didn't modify anything!",
        });
      } else {
        setIsModalOpen(true);
      }
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setReason("");
  };

  const functionMap = {
    "Change Status": updateStaffStatus,
    "Change Role": updateStaffRole,
    "Change Type": updateAppUserType,
    "Change Access" :updateStaffGroup
  };

  const reasonCreateHandler = () => {
    if (!reason || !notesPattern.test(reason)) {
      notification.error({
        type: "error",
        message: `Please enter the ${!reason ? "notes" : "valid notes"}`,
      });
    } else {
      setIsModalOpen(false);
      setUpdateStaffForm({
        ...updateStaffForm,
        notes: reason,
      });

      if (staffTabs[0].isSelected) {
        updateStaff();
        setReason("");
      } else if (staffTabs[1].isSelected) {
        const updateFunction = functionMap[identifier];
        if (updateFunction) {
          updateFunction();
          setReason("");
        }
      } else if (staffTabs[2].isSelected) {
        updateStaffVillages();
        setReason("");
      }
    }
  };
  return (
    <>
      <div className="mb-4">
        <div className="farmer-details__back-btn" onClick={() => navigate(-1)}>
          <i className="fa fa-angle-double-left" aria-hidden="true"></i> Go back
        </div>

        <p
          className="mb-0 mt-3"
          style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
        >
          Staffs / Staff Details
        </p>
      </div>

      {staffDetailsLoader == true ? (
        <div
          style={{
            margin: "2rem 0",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Loader />
        </div>
      ) : (
        <div className="mb-5">
          <div className="add-farmer__floating-menu mt-3">
            {staffTabs.map((tab) => (
              <div
                key={tab.count}
                className={`staff-tab ${
                  tab.isSelected === true && "selected"
                } ${tab.isDisabled === true && "disabled"}`}
                onClick={() => onChangeTab(tab.count)}
              >
                <div>{tab.name}</div>
              </div>
            ))}
          </div>

          {/* Page 1 */}
          {staffTabs[0].isSelected === true && (
            <>
              <Row style={{ padding: "10px" }}>
                <Col lg={4} md={6} sm={12}>
                  <TextField
                    id="outlined-basic"
                    value={
                      staffDetails && staffDetails.staff_name_l10n
                        ? staffDetails.staff_name_l10n.en
                          ? staffDetails.staff_name_l10n.en
                          : staffDetails.staff_name_l10n.ul
                        : null
                    }
                    label="Staff Name*"
                    variant="outlined"
                    sx={{
                      width: "100%",
                      marginTop: "5px",
                      background: "#fff",
                    }}
                    onChange={nameChangeHandler}
                    disabled={isDisabled}
                  />
                </Col>
                <Col lg={4} md={6} sm={12}>
                  <TextField
                    id="outlined-basic"
                    disabled
                    label="Staff Visual Id"
                    value={staffDetails && staffDetails?.staff_visual_id}
                    variant="outlined"
                    sx={{
                      width: "100%",
                      marginTop: "5px",
                      background: "#fff",
                    }}
                  />
                </Col>
                <Col lg={4} md={6} sm={12}>
                  <TextField
                    id="outlined-basic"
                    label={`Staff Email${
                      staticData.staff_type_id[0] === 1000230006 ? "" : "*"
                    }`}
                    value={staffDetails && staffDetails?.email_address}
                    variant="outlined"
                    sx={{
                      width: "100%",
                      marginTop: "5px",
                      background: "#fff",
                    }}
                    onChange={handleEmailChange}
                    disabled={isDisabled}
                  />
                </Col>
              </Row>
              <Row style={{ padding: "10px" }}>
                <Col lg={4} md={6} sm={12}>
                  <TextField
                    id="outlined-basic"
                    label="Staff Mobile*"
                    variant="outlined"
                    value={staffDetails && staffDetails?.mobile_number}
                    sx={{
                      width: "100%",
                      marginTop: "5px",
                      background: "#fff",
                    }}
                    onChange={handleMobileChange}
                    disabled={isDisabled}
                  />
                </Col>
                {staticData.staff_type_id[0] === (1000230006 || 1000230007) && (
                  <Col lg={4} md={6} sm={12}>
                    <TextField
                      id="outlined-basic"
                      label="PAN"
                      variant="outlined"
                      value={staffDetails && staffDetails?.staff_pan}
                      sx={{
                        width: "100%",
                        marginTop: "5px",
                        background: "#fff",
                      }}
                      onChange={handlePanChange}
                      disabled={isDisabled}
                    />
                  </Col>
                )}
                {staticData.staff_type_id[0] === (1000230006 || 1000230007) && (
                  <Col lg={4} md={6} sm={12}>
                    <TextField
                      id="outlined-basic"
                      label="Aadhar"
                      variant="outlined"
                      value={staffDetails && staffDetails?.staff_aadhar}
                      sx={{
                        width: "100%",
                        marginTop: "5px",
                        background: "#fff",
                      }}
                      onChange={handleAadharChange}
                      disabled={isDisabled}
                    />
                  </Col>
                )}
                <Col lg={4} md={6} sm={12}>
                <FormControl fullWidth 
                 sx={ staticData?.staff_type_id[0] === (1000230006 || 1000230007) ? {
                  width: "100%",
                  marginTop: "15px",
                  background: "#fff",
                 } : {
                  width: "100%",
                  marginTop: "5px",
                  background: "#fff",
                 }}
                >
                  <InputLabel id="demo-simple-select-label">Select vehicle type</InputLabel>
                  <Select
                    labelId="demo-simple-select-label"
                    id="demo-simple-select"
                    value={staffDetails && staffDetails?.staff_vehicle_type_1}
                    label="Select vehicle type"
                    onChange={vehicleTypeChangeHandler}
                  >
                    {vehicleTypeList?.map((item)=>{
                      return(
                        <MenuItem  key={item?.reference_id} value={item?.reference_id}>{item?.reference_name_l10n}</MenuItem>
                      )
                    })}
                  </Select>
                </FormControl>
                  </Col>
              </Row>
            </>
          )}
          {staffTabs[1].isSelected === true && (
            <Table
              dataSource={dataSource}
              columns={columns}
              pagination={false}
            />
          )}
          {/* Page 2 */}
          {staffTabs[2].isSelected === true && (
            <>
              <div className="d-flex ps-2">Allotted Villages</div>
              <div
                className="d-flex"
                style={{ width: "90%", flexWrap: "wrap" }}
              >
                {geoList.length > 0 ? (
                  geoList
                    .sort((a, b) =>
                      a.village_name_l10n.en.localeCompare(
                        b.village_name_l10n.en
                      )
                    )
                    .map((option, index) => {
                      return (
                        <div key={index} className="villageAllot">
                          {console.log(option, "option")}
                          {option.village_name_l10n.en}
                          {/* {villageName ? villageName : null} */}
                          <button
                            className="cross"
                            onClick={() => removeVillageHandler(index)}
                          >
                            &times;
                          </button>
                        </div>
                      );
                    })
                ) : (
                  <div
                    className="ps-2 mt-3"
                    style={{ color: "gray", fontSize: "12px" }}
                  >
                    no villages allotted yet!
                  </div>
                )}
              </div>
              <div
                className="add-cattle__add-disease-btn mt-3"
                onClick={removeAllVillageHandler}
              >
                <i className="fa fa-trash"></i> Remove All
              </div>

              <hr />
              <Row>
                <Col lg={6} md={6} sm={12}>
                  <Autocomplete
                    options={
                      statesList &&
                      statesList.sort((a, b) => {
                        const stateNameA =
                          a.state_name_l10n.en || a.state_name_l10n.ul;
                        const stateNameB =
                          b.state_name_l10n.en || b.state_name_l10n.ul;
                        return stateNameA.localeCompare(stateNameB);
                      })
                    }
                    getOptionLabel={(option) => option?.state_name_l10n?.en}
                    //   disabled={activityList.length === 0}
                    sx={{
                      width: "100%",
                      marginTop: "10px",
                      background: "#fff",
                    }}
                    renderInput={(params) => (
                      <TextField {...params} label="Select State..." />
                    )}
                    onChange={(event, value) => {
                      setSelectedState(value);
                      stateInputChangeHandler(
                        event,
                        value ? value?.state_id : null
                      );
                      if (!value) {
                        console.log("harry");
                        setDistrictsList([]);
                      }
                    }}
                    value={selectedState}
                    disabled={isDisabled}
                  />
                </Col>
                <Col lg={6} md={6} sm={12}>
                  <Autocomplete
                    options={districtsList}
                    getOptionLabel={(option) => option?.district_name_l10n?.en}
                    disabled={districtsList?.length === 0}
                    sx={{
                      width: "100%",
                      marginTop: "10px",
                      background: "#fff",
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select District..."
                        disabled={isDisabled}
                      />
                    )}
                    onChange={(event, value) => {
                      setSelectedDistrict(value);
                      districtInputChangeHandler(
                        event,
                        value ? value?.district_id : null
                      );
                      if (!value) {
                        setTalukasList([]);
                      }
                    }}
                    value={selectedDistrict}
                  />
                </Col>
                <Col lg={6} md={6} sm={12}>
                  <Autocomplete
                    options={
                      talukasList &&
                      talukasList?.sort((a, b) => {
                        const talukaNameA =
                          a.taluk_name_l10n.en || a.taluk_name_l10n.ul;
                        const talukaNameB =
                          b.taluk_name_l10n.en || b.taluk_name_l10n.ul;
                        return talukaNameA.localeCompare(talukaNameB);
                      })
                    }
                    getOptionLabel={(option) => option?.taluk_name_l10n?.en}
                    disabled={talukasList?.length === 0}
                    sx={{
                      width: "100%",
                      marginTop: "10px",
                      background: "#fff",
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Taluka..."
                        disabled={isDisabled}
                      />
                    )}
                    onChange={(event, value) => {
                      console.log(value);
                      setSelectedTaluk(value);
                      talukaInputChangeHandler(
                        event,
                        value ? value?.taluk_id : null
                      );
                      if (!value) {
                        console.log("harry");
                        setVillagesList([]);
                      }
                    }}
                    value={selectedTaluk}
                  />
                </Col>
                <Col lg={6} md={6} sm={12}>
                  <Autocomplete
                    multiple
                    options={[
                      { village_name_l10n: { en: "Select All" } },
                      ...villagesList,
                    ]}
                    disableCloseOnSelect
                    id="checkboxes-tags-demo"
                    getOptionLabel={(option) => option?.village_name_l10n?.en || option?.village_name_l10n?.ul}
                    disabled={villagesList?.length === 0}
                    renderOption={(props, option, { selected }) => (
                      <li {...props} key={option?.village_id}>
                        <Checkbox
                          icon={icon}
                          checkedIcon={checkedIcon}
                          style={{ marginRight: 8 }}
                          checked={selected}
                        />
                        {option?.village_name_l10n?.en ||
                          option?.village_name_l10n?.ul}
                      </li>
                    )}
                    sx={{
                      width: "100%",
                      marginTop: "10px",
                      background: "#fff",
                      maxHeight: "100px",
                      overflowY: "auto",
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Village..."
                        disabled={isDisabled}
                      />
                    )}
                    onChange={(event, value) =>
                      villageInputChangeHandler(event, value)
                    }
                    value={selectedValue}
                  />
                </Col>
              </Row>

              <div
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "flex-end",
                }}
              >
                <div
                  className="add-village-btn mt-3"
                  onClick={addVillageHandler}
                >
                  + Add villages
                </div>
              </div>
            </>
          )}
          {staffTabs[3].isSelected === true && (
            <TimeLine
              timeLine={staffDetails.timeline}
              staffName={staffDetails.staff_name_l10n}
              staffRole={staffDetails.staff_type_l10n}
            />
          )}
          {staffTabs[4]?.isSelected === true && (
            // <PpuuFinance staffId={staffDetails?.staff_id}/>
            <TransactionHistory staffId={staffDetails?.staff_id} />
          )}
          {/* Page 3 */}
          {/* {staffTabs[2].isSelected === true && (
            <>
              {thumbnailImage ? (
                <>
                  <div
                    className="farmer-item__img-div"
                    style={{
                      borderRight: "1px solid silver",
                    }}
                  >
                    <img
                      src={thumbnailImage}
                      className="new-img"
                      // onClick={() => openFarmerImageViewer(index)}
                      onClick={() => openThumbnailImageViewer(0)}
                      alt=""
                    />
                  </div>
                  {isThumbnailViewerOpen && (
                    <ImageViewer
                      src={[thumbnailImage]}
                      currentIndex={currentThumbnailImage}
                      onClose={closeThumbnailImageViewer}
                      backgroundStyle={{
                        backgroundColor: "rgba(0, 0, 0, 0.9)",
                        zIndex: 10,
                      }}
                      closeOnClickOutside={true}
                    />
                  )}
                </>
              ) : (
                <div className="text-center my-5" style={{ color: "#4c4c4c" }}>
                  <i
                    className="fa fa-exclamation-triangle"
                    aria-hidden="true"
                  ></i>{" "}
                  No image!
                </div>
              )}
            </>
          )} */}

          {(staffTabs[0].isSelected || staffTabs[2].isSelected) === true && (
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <div>
                <Button
                  variant="primary"
                  onClick={showModal}
                  style={{
                    background: "#ec6237",
                    color: "#fff",
                    border: "none",
                  }}
                  disabled={
                    isDisabled ||
                    (staticData.staff_type_id[0] === 1000230006 &&
                      staffTabs[2].isSelected)
                  }
                >
                  Save changes
                </Button>
              </div>
            </div>
          )}
          <ReasonModal
            isMandatory={true}
            isModalOpen={isModalOpen}
            handleCancel={handleCancel}
            setReason={setReason}
            reasonCreateHandler={reasonCreateHandler}
          />
          {contextHolder}
        </div>
      )}
    </>
  );
};

// export default StaffItem;
export default withAuthorization(StaffItem, [IT_ADMIN, OPS_TEAM, VET_OPS, SALES_OPS])