import { useNavigate, useSearchParams } from "react-router-dom";
// import "./Farmers.css";
import { useSelector, useDispatch } from "react-redux";
import { useState, useCallback, useEffect } from "react";
import {
  get_requisition_list,
  get_all_requisition,
  get_ledger_list,
} from "../../../router";
import { clearUserToken } from "../../user/action";
import {
  createPageParamsFromURLString,
  OCTable,
} from "../../Common/AntTableHelper";
import { extractBasedOnLanguage } from "@krushal-it/common-core";
import { extractBasedOnLanguageMod } from "../../../Utilities/Common/utils";
import React from "react";
import { useMediaQuery } from "react-responsive";
import Loader from "../../Loader/Loader";
import Moment from "moment";

const LedgerList = () => {
  const isMobile = useMediaQuery({ maxWidth: 768 });
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const userLanguage = useSelector((state) => state.user.userLanguage);
  const [data, setData] = useState([]);
  const [load, setLoad] = useState(true);
  // const [searchParams] = useSearchParams()
  // const pageParams = createPageParamsFromURLString(farmersTableConfiguration, searchParams)
  const userToken = useSelector((state) => state.user.userToken);
  const [reportData, setReportData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [searchPin, setSearchPin] = useState("");
  const [medFilter, setMedfilter] = useState([]);

  const ledgerTableConfiguration = {
    enableSelection: false,
    defaultPageSize: 10,
    enableHorizontalScrolling: true,
    showTableHeader: false,
    omniSearchRowEnabled: false,
    enableColumnConfiguration: false,
    // enableOnChange: true,
    // getAllDataAtOnce: true,
    // disablePagination: true,
    dataTransformationRules: {
      // staff_filter_values: 'bdm_filter_values',
      // staff_name: 'bdm'
    },
    rowKey: "inventory_ledger_id",
    columns: [
      {
        title: "Inventory Ledger Id",
        key: "inventory_ledger_id",
      },
      {
        title: "Warehouse type",
        key: "warehouse_type_l10n",
        render: (_, record) => {
          return (
            <div>
              {extractBasedOnLanguageMod(
                record?.warehouse_type_l10n,
                userLanguage
              )}
            </div>
          );
        },
      },
      {
        key: "warehouse_name",
        title: "Warehouse Name",
        render: (_, record) => {
          return (
            <div>
              {extractBasedOnLanguageMod(
                record?.warehouse_name_l10n,
                userLanguage
              )}
            </div>
          );
        },
      },
      {
        title: "Medicine",
        key: "medicine_name_l10n",
        render: (_, record) => {
          return (
            <div>
              {extractBasedOnLanguageMod(
                record?.medicine_name_l10n,
                userLanguage
              )}
            </div>
          );
        },
      },
      {
        title: "Credit QTY",
        key: "credit_qty",
        render: (_, record) => (
          <div className="data" style={{ color: "green" }}>
            {extractBasedOnLanguageMod(record?.credit_qty, userLanguage) +
              " ML"}
          </div>
        ),
      },
      {
        title: "Debit QTY",
        key: "debit_qty",

        render: (_, record) => (
          <div className="data" style={{ color: "red" }}>
            {extractBasedOnLanguageMod(record?.debit_qty, userLanguage) + " ML"}
          </div>
        ),
      },
      {
        title: "Balance",
        key: "balance",
        render: (_, record) => (
          <div className="data">
            {extractBasedOnLanguageMod(record?.balance, userLanguage) + " ML"}
          </div>
        ),
      },
      {
        title: "Updated at",
        key: "updated_at",
      },
    ],
    columnConfiguration: {
      inventory_ledger_id: {
        headingText: "Inventory Ledger Id",
      },
      warehouse_type_l10n: {
        headingText: "Warehouse Type",
        type: "default_array",
        dataType: "int",
        enableFilter: true,
        filters: [
          { text: "Vet", value: "1000230003" },
          { text: "Paravet", value: "1000230004" },
        ],
        includeInOmnisearch: true,
      },
      warehouse_name: {
        headingText: "Warehouse Name",
        enableFilter: true,
        type: "string",
        includeInOmnisearch: true,
      },
      medicine_name_l10n: {
        headingText: "Medicine Name",
        type: "single_select_array",
        dataType: "int",
        enableFilter: true,
        filterValuesKey: "medicine_filter_values",
        includeInOmnisearch: true,
      },
      created_at: {
        headingText: "Updated At",
        format: "h:mm a, DD-MMM-YYYY",
      },
      updated_at: {
        headingText: "Updated At",
        enableFilter: true,
        type: "dayjs_range",
        formatType: "date",
        format: "h:mm a, DD-MMM-YYYY",
      },
      fullfiller_type_l10n: {
        headingText: "fulfiller_type Type",
        type: "string",
      },
      status: {
        headingText: "status",
        type: "string",
        enableFilter: true,
        includeInOmnisearch: true,
      },
    },
  };

  const loadFilters = useCallback(
    async (userToken) => {
      // try {
      //     const headers = {
      //         useCase: 'Get Farmers With Location Recorded Report Filter Data',
      //         token: userToken.accessToken, //authToken
      //     }
      //     const response = await getFarmersFilterData(headers)
      //     return response.data
      // } catch (error) {
      //     if (error.response.status === 401) {
      //         dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
      //     }
      // }
      return [];
    },
    [dispatch, navigate]
  );

  const loadReport = useCallback(
    async (userToken, queryParams) => {
      try {
        const headers = {
          token: userToken.accessToken, //authToken
        };
        const response = await get_ledger_list(headers, queryParams);
        // const response = await get_ledger_list(userToken, queryParams);
        // const temp_arr = response.data['data']['mappedreqlist']

        console.log("medInventory", response);

        const array_list = [];

        response.data["report"].map((item) => {
          array_list.push(item);
        });

        response.data["medicine_filter_values"].map((item) => {
          item["medicine_id"] = item.value;
        });

        setMedfilter(response.data["medicine_filter_values"]);

        // array_list.map(item => {
        //     Object.keys(item).forEach(key => {
        //         if (item[key] && typeof item[key] === 'object') {
        //             item[key] = extractBasedOnLanguage(item[key], userLanguage);
        //         }
        //     })
        // })

        // let new_arr = {
        //     report: array_list
        // }

        return response.data;
      } catch (error) {
        if (error?.response?.status === 401) {
          dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
        }
      }
    },
    [dispatch, navigate]
  );

  const loadInitialFilterDataCB = (additionalParams) => {
    console.log("load filter is called  ");
    return loadFilters(userToken, additionalParams);
  };

  const loadReportCB = (reportQueryParams, additionalParams) => {
    console.log("loadReportCB is called ", reportQueryParams);
    return loadReport(userToken, reportQueryParams, additionalParams);
  };

  return (
    <div>
      <div className="my-3" style={{ display: "flex", alignItems: "center" }}>
        <div>
          <p
            className="m-0"
            style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
          >
            Ledger List
          </p>
        </div>
      </div>

      {/* farmer table  */}
      <OCTable
        /* tableKey={tableKey}
                setTableKey={setTableKey} */
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={ledgerTableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        // postInitialLoadCallback={postInitialLoadCB}
        loadReportCallback={loadReportCB}
        loadInitialFilterDataCallback={loadInitialFilterDataCB}
        // parentPageParams={pageParams}
        // showTableHeader={true}
        // omniSearchRowEnabled={true}
        // omniSearchRowBeginning={backToQueueListLinkUI}
        // omniSearchRowMiddle={viewRelatedAnimalButtons}
      />
    </div>
  );
};

export default LedgerList;
