import {  useState } from "react";
import { Spin, Segmented } from "antd";
import { useGetActivityQuery } from "../reducer/activityManagementSlice";
import { useNavigate, useParams } from "react-router";
import style from "../activitymanage.module.css";
import PageLayout from "../../../Common/PageLayout";
import {Box,Breadcrumbs,Stack,Typography} from "@mui/material";
import CreateJSON from "./CreateJSON";
import EditActivityForm from "./EditActivityForm";
import _ from 'lodash'
const returnActivityDetails = (activityId, data) => {
  const value = data?.find((item) => item?.activity_id == activityId);
  return value;
};

const EditActivity = () => {

  const { data, isLoading } = useGetActivityQuery();
  const [value, setValue] = useState("Edit activity");
  const navigate = useNavigate();
  const { activity_id } = useParams();
  const activityDetails = returnActivityDetails(activity_id, data?.report);
  // const options = data?.activity_category_filter_values;
  let newArray =data?.report?.length > 0 &&  _.map(data?.report, obj => ({
    value: obj.activity_category,
    text: obj.activity_category_name
  }));
  
  newArray = _.uniqBy(newArray, 'value');
  return (
    <PageLayout>
      <Stack direction="row" spacing={2} justifyContent={"space-between"}>
        <Box mt={2}>
          <Typography
            component="h5"
            variant="h5"
            sx={{ fontWeight: 700, fontSize: 24 }}
          >
            Update Activity Details
          </Typography>

          <Box sx={{ backgroundColor: "#ff6e1b", height: 5, width: 100 }}></Box>
          <Box mt={2}>
            <Breadcrumbs aria-label="breadcrumb">
              <Typography
                component="a"
                variant="a"
                onClick={() => navigate("/activity-management")}
              >
                Activity Management
              </Typography>
              <Typography component="p" variant="p">
                {activityDetails?.activity_name}
              </Typography>
            </Breadcrumbs>
          </Box>
        </Box>
      </Stack>

      {isLoading ? (
        <div className={style.center_align}>
          <Spin />
        </div>
      ) : (
        <Stack justifyContent="center" alignItems="center">
          <Stack justifyContent="center" alignItems="center">
            <Segmented
              size="large"
              style={{ marginTop: "10px" }}
              options={["Edit activity", "Form"]}
              value={value}
              onChange={setValue}
            />
          </Stack>
          {value === "Edit activity" && (
            <EditActivityForm activityDetails={activityDetails} options={newArray}/>
          )}
          {value === "Form" && (<CreateJSON activity_id={activity_id}/>)}
        </Stack>
      )}
    </PageLayout>
  );
};
export default EditActivity;
