import {KrushalHookFormWeb, useKHF, webComponentMap} from "krushal-hook-form";
import {FormProvider} from "react-hook-form"


const RenderFormComp = ({config}) =>{
   
    const methods = useKHF(config,{
        config : config,
        readOnly : false,
        componentMap : webComponentMap
    })
    return (
        <FormProvider {...methods}>
            <KrushalHookFormWeb />
        </FormProvider>
    )
}

export default RenderFormComp;