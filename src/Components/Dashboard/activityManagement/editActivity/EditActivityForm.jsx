import {
  Box,
  Grid,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Stack,
} from "@mui/material";
import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { message } from "antd";
import {
  useUpdateActivityMutation,
  useGetActivityQuery,
} from "../reducer/activityManagementSlice";
import { useDispatch } from "react-redux";
import { clearUserToken } from "../../../user/action";
import { useNavigate } from "react-router-dom";
const EditActivityForm = ({ activityDetails, options }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [updateActivity, { isSuccess, isError }] = useUpdateActivityMutation();

  const applicableOnOptions = [
    { value: 1000460001, label: "Farm" },
    { value: 1000460002, label: "Cattle" },
  ];

  const statusHandlers = {
    500: () => {
      console.error("Server error");
      message.error("Server error");
    },
    401: () => {
      dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
    },
    default: () => {
      console.error("Something went wrong");
      message.error("Something went wrong");
    },
  };
  const {
    reset,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      activity_id: "",
      activity_name: "",
      activity_category: "",
      activity_information: {
        entity_mapping: [],
      },
    },
  });
  useEffect(() => {
    if (activityDetails) {
      reset({
        activity_id: activityDetails?.activity_id || 0,
        activity_name: activityDetails?.activity_name || "",
        activity_category: activityDetails?.activity_category || "",
        activity_information: {
          entity_mapping:
            activityDetails?.activity_information?.entity_mapping || [],
        },
      });
    }
  }, [reset, activityDetails]);

  const onSubmit = (data) => {
    const payload = {
      activity_id: data.activity_id,
      activity_name: data.activity_name,
      activity_category: data.activity_category,
      activity_information: {
        entity_mapping: data.activity_information.entity_mapping,
      },
    };

    updateActivity(payload)
      .then((response) => {
        if (response.error) {
          const handler =
            statusHandlers[response.error.status] || statusHandlers.default;
          handler();
        } else if (response?.data) {
          message.success("Activity updated");
        }
      })
      .catch((error) => {
        console.error(error);
      });
  };
  return (
    <Box sx={{ width: 500, mt: 3 }}>
      <Grid container spacing={2}>
        <Grid item md={12}>
          <Controller
            name="activity_id"
            control={control}
            defaultValue=""
            render={({ field }) => (
              <TextField
                {...field}
                disabled
                label="Activity id"
                variant="outlined"
                sx={{ width: "100%" }}
                value={field?.value}
              />
            )}
          />
        </Grid>
        <Grid item md={12}>
          <Controller
            name="activity_name"
            control={control}
            defaultValue=""
            rules={{ required: true }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Activity Name"
                variant="outlined"
                sx={{ width: "100%" }}
                value={field.value}
                error={errors["activity_name"]}
              />
            )}
          />
          {errors["activity_name"] && (
            <span style={{ color: "red" }}>Activity name is required</span>
          )}
        </Grid>
        <Grid item md={12}>
          <Controller
            name="activity_category"
            control={control}
            defaultValue=""
            rules={{ required: true }}
            render={({ field }) => (
              <FormControl fullWidth>
                <InputLabel id="demo-simple-select-label">
                  Select category
                </InputLabel>
                <Select
                  {...field} // This spreads the field object properties onto the Select component
                  sx={{ width: "100%" }}
                  value={field.value}
                >
                  {options?.map((item, index) => (
                    <MenuItem key={index} value={item?.value}>
                      {item.text}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
        </Grid>
        <Grid item md={12}>
          <Controller
            name="activity_information.entity_mapping"
            control={control}
            defaultValue={[]}
            rules={{ required: true }}
            render={({ field }) => (
              <FormControl fullWidth>
                <InputLabel id="demo-multiple-select-label">
                  Applicable on
                </InputLabel>
                <Select
                  {...field}
                  sx={{ width: "100%" }}
                  value={field.value || []} // Ensure the value is always an array
                  multiple
                >
                  {applicableOnOptions?.map((item, index) => (
                    <MenuItem key={index} value={item?.value}>
                      {item.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
          {errors["applicable_on"] && (
            <span style={{ color: "red" }}>This field is required</span>
          )}
        </Grid>
      </Grid>
      <Stack
        justifyContent="center"
        alignItems="center"
        sx={{ width: 100, mt: 2 }}
      >
        <Button
          sx={{ textTransform: "none" }}
          variant="contained"
          onClick={handleSubmit(onSubmit)}
        >
          Update
        </Button>
      </Stack>
    </Box>
  );
};
export default EditActivityForm;
