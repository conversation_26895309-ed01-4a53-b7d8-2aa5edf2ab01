import { Box } from "@mui/material";
import { useEffect, useRef, useState } from "react";
import * as ace from "brace";
import "brace/mode/json";
import "brace/theme/monokai";
import { useCreateFormMutation } from "../reducer/activityManagementSlice";
import defaultConfig from "./default.config.json";
import ModalFromView from "./modalFormView/ModalFromView";
import ToastersService from "../../../../Services/toasters.service";
import { Button } from "@mui/material";
import { useGetActivityDetailsByActivityIdQuery } from "../reducer/activityManagementSlice";

const CreateJSON = ({ activity_id }) => {
  const [createForm, { data, reset, isError }] = useCreateFormMutation();
  const { data: formClassification, refetch, isLoading } = useGetActivityDetailsByActivityIdQuery(activity_id);
  const [formContent, setFormContent] = useState(formClassification?.classification?.activity_form_config || defaultConfig);
  const editorRef = useRef(null);

  const handleGetContent = () => {
    if (editorRef.current) {
      const jsonContent = editorRef.current.getValue();
      setFormContent(JSON.parse(jsonContent));
      if (jsonContent) {
        const payload = {
          activity_id: activity_id,
          jsonContent: {
            classifiers: {
              activity_form_config: JSON.parse(jsonContent),
            },
          },
        };
        createForm(payload);
      }
    }
  };

  const previewForm = () => {
    if (editorRef?.current) {
      const jsonContent = editorRef.current.getValue();
      setFormContent(JSON.parse(jsonContent));
    }
  };

  useEffect(() => {
    if (formClassification?.classification?.activity_form_config) {
      setFormContent(formClassification?.classification?.activity_form_config);
    } else {
      setFormContent(defaultConfig);
    }
  }, [formClassification]);

  useEffect(() => {
    const editor = ace?.edit("javascript-editor");
    editorRef.current = editor;
    editor?.getSession()?.setMode("ace/mode/json");
    editor?.setTheme("ace/theme/monokai");
    editor?.setValue(JSON.stringify(formContent, null, 2));

    return () => {
      if (editor) {
        editor?.destroy();
      }
    };
  }, [formContent]);

  useEffect(() => {
    if (data === true) {
      ToastersService.successToast("Form created successfully");
      reset();
    }
    if (isError) {
      ToastersService.failureToast("Form not created");
    }
    refetch();
  }, [data, isError, reset, refetch]);

  return (
    <Box sx={{ mt: 3, width: "100%" }}>
      <div
        id="javascript-editor"
        style={{ width: "auto", height: "800px" }}
      ></div>
      <Box mt={2} sx={{ display: "flex", justifyContent: "center" }}>
        <ModalFromView previewForm={previewForm} formContent={formContent} />
        <Button variant="contained" sx={{ textTransform: "none", marginLeft: 1 }} onClick={handleGetContent}>Save form config</Button>
      </Box>
    </Box>
  );
};

export default CreateJSON;
