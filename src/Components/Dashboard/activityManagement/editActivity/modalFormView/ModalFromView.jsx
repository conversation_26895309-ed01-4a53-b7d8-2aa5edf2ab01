import React from "react";
import {
  Di<PERSON>,
  But<PERSON>,
  A<PERSON><PERSON><PERSON>,
  IconButton,
  Tool<PERSON>,
  Typography,
  Slide,
  Box,
  createTheme,
} from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import RenderFormComp from "../RenderFormComp";
import {ThemeProvider} from '@mui/material'

const theme = createTheme({
  components:{
    "MuiTextField":{
      defaultProps:{
       
      },
      styleOverrides:{
        root:({theme})=>theme.unstable_sx({
          mb:2,
          maxWidth:"33.33%"
        })
      }
    }
  }
});

const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
  });
const ModalFromView = ({previewForm, formContent}) => {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
    previewForm()
  };

  const handleClose = () => {
    setOpen(false); 
  };
  return (
    <React.Fragment>
      <Button variant="outlined" sx={{textTransform:"none"}} onClick={handleClickOpen}>
        Preview form
      </Button>
      <Dialog
        fullScreen
        open={open}
        onClose={handleClose}
        TransitionComponent={Transition}
      >
        <AppBar sx={{ position: "relative" }}>
          <Toolbar>
            <IconButton
              edge="start"
              color="inherit"
              onClick={handleClose}
              aria-label="close"
            >
              <CloseIcon />
            </IconButton>
            <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
              Form Preview
            </Typography>
            {/* <Button autoFocus color="inherit" sx={{textTransform:"none"}} onClick={()=>handleClose("save")}>
              Create Form
            </Button> */}
          </Toolbar>
        </AppBar>
        <Box sx={{padding: 2}}>
          <ThemeProvider theme={theme}>
            <RenderFormComp config={formContent}/>
            </ThemeProvider>
        </Box>
      </Dialog>
    </React.Fragment>
  );
};

export default ModalFromView;
