

.main{
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;    
}

.heading_1{
    font-size: 18px;
    font-weight: 600;
    color: black;
    margin: 0 0 10px 10px;
}

.recurrence_menu{
    margin-bottom: 10px;
}

.center_align{
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.box_style{
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

.calendar_circle{
    height: 5px;
    width: 100%;
    background: rgb(58, 171, 232);
}

.date_header{
    font-size: 18px;
    color: gray;
    font-weight: 600;
    text-align: end;
    padding: 10px;
}

.calendar_circle_a{
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    position: absolute;
    width: 25px;
    height: 25px;
    background-color: rgb(126, 180, 225);
    opacity: 0.5;
    border-radius: 50%;
}