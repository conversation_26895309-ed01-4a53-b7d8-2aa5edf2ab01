import React from "react";
import { Button, Form, Input, Spin, message, Select } from "antd";
import { useAddActivityMutation } from "../reducer/activityManagementSlice";
import { clearUserToken } from "../../../user/action";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router";
import { Box } from "@mui/material";
const onFinishFailed = (errorInfo) => {
  console.log("Failed:", errorInfo);
};
const config = {
  form: {
    name: "basic",
    labelCol: {
      span: 8,
    },
    wrapperCol: {
      span: 16,
    },
    style: {
      maxWidth: 700,
    },
    initialValues: {
      remember: true,
    },
    autoComplete: "off",
    layout: "vertical",
    items: [
      {
        label: "Activity name",
        name: "activity_name",
        rules: [
          {
            required: true,
            message: "Please input activity name!",
          },
        ],
        input: {
          type: "text",
        },
      },
      {
        label: "Select category",
        name: "activity_category",
        rules: [
          {
            required: true,
            message: "Please select an option!",
          },
        ],
        select: {
          options: [],
        },
      },
      {
        label: "Applicable on",
        name: "applicable_on",
        rules: [
          {
            required: true,
            message: "Please select an option!",
          },
        ],
        select: {
          options: [
            { value: 1000460001, label: "Farm" },
            { value: 1000460002, label: "Cattle" },
          ],
        },
      },
    ],
    submit: {
      wrapperCol: {
        offset: 8,
        span: 16,
      },
      button: {
        type: "primary",
        htmlType: "submit",
        text: "Submit",
      },
    },
  },
};

const AddActivity = ({ handleCancel, setRefresh, filters }) => {
  const [addActivity, { isLoading }] = useAddActivityMutation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const statusHandlers = {
    500: () => {
      console.error("Server error");
      message.error("Server error");
    },
    401: () => {
      dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
    },
    400: () => {
      message.error("Activity name already exist!");
    },
    default: () => {
      console.error("Something went wrong");
      message.error("Something went wrong");
    },
  };

  const onFinish = (values) => {
    const payload = {
      activity_name: values.activity_name,
      activity_category: values.activity_category,
      activity_information: {
        entity_mapping: values.applicable_on,
      },
    };
    addActivity(payload)
      .then((response) => {
        if (response.error) {
          const handler =
            statusHandlers[response.error.status] || statusHandlers.default;
          handler();
        } else if (response?.data) {
          handleCancel();
          setRefresh(Math.random());
          message.success("Activity added");
        }
      })
      .catch((error) => {
        console.error(error);
      });
  };

  return (
    <React.Fragment>
      {isLoading ? (
        <div style={{ display: "flex", justifyContent: "center" }}>
          <Spin />
        </div>
      ) : (
        <Box sx={{ display: "flex", justifyContent: "center" }}>
          <Form
            {...config.form}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            style={{ width: "100%", marginLeft: 80 }}
          >
            {config.form.items.map((item, index) => (
              <Form.Item key={index} {...item}>
                {item.input ? (
                  <Input {...item.input} />
                ) : item.select ? (
                  item.select.options.length !== 0 ? (
                    <Select mode="multiple">
                      {item.select.options?.map((option, optionIndex) => (
                        <Select.Option key={optionIndex} value={option.value}>
                          {option.label}
                        </Select.Option>
                      ))}
                    </Select>
                  ) : (
                    <Select>
                      {filters?.map((option, optionIndex) => (
                        <Select.Option key={optionIndex} value={option.value}>
                          {option.text}
                        </Select.Option>
                      ))}
                    </Select>
                  )
                ) : null}
              </Form.Item>
            ))}
            <Form.Item {...config.form.submit.wrapperCol}>
              <Button style={{ float: "left" }} {...config.form.submit.button}>
                {config.form.submit.button.text}
              </Button>
            </Form.Item>
          </Form>
        </Box>
      )}
    </React.Fragment>
  );
};
export default AddActivity;
