import React, { useState, useRef, useCallback, useEffect } from "react";
import { Modal, Spin, Tag } from "antd";
import { Button, Popover, Table } from "antd";
import { clearUserToken } from "../../user/action";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router";
import AddActivity from "./addActivity/AddActivity";
import { useGetActivityQuery } from "./reducer/activityManagementSlice";
import { PlusOutlined } from "@ant-design/icons";
import PageLayout from "../../Common/PageLayout";
import { Box, Stack, Typography } from "@mui/material";
import _ from 'lodash'
import { getColumnSearchProps } from "../TableAndUtils/ColumnSearchInputUtil";
import { globalReferencesMapper } from "../../../../src/Utilities/Common/utils";
const {CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../user/authorize')

const ActivityManagement = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [refresh, setRefresh] = useState(0);
  const [categoryOptions, setCategoryOptions] = useState([])

  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef(null);
  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
    confirm();
  };

  const handleReset = (clearFilters, confirm, dataIndex) => {
    clearFilters();
    setSearchText("");
    confirm();
  };

  const { data, isLoading } = useGetActivityQuery();

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  

  const dataSource = data?.report?.map((item) => ({
    key : item?.activity_id,
    activity_id : item?.activity_id,
    activity_name: item?.activity_name,
    activity_category: item?.activity_category,
    activity_category_name : item?.activity_category_name
  }));
  const columns = [
    {
      title: "Activity id",
      key: "activity_id",
      dataIndex : "activity_id",
      ...getColumnSearchProps(
        "activity_id",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
      render: (_, record) => {
        return (
          <Popover content="Click">
            <div
              style={{ cursor: "pointer" }}
              onClick={() =>
                navigate(`/activity-management/${record?.activity_id}`)
              }
            >
              <Tag color="purple">{record?.activity_id}</Tag>
            </div>
          </Popover>
        );
      },
    },
    {
      title: "Activity name",
      key: "activity_name",
      dataIndex : "activity_name",
      ...getColumnSearchProps(
        "activity_name",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
    {
      title: "Category Name",
      key: "activity_category_name",
      dataIndex : "activity_category_name",
      ...getColumnSearchProps(
        "activity_category_name",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn,
        categoryOptions
      )
    },
  ]
  useEffect(()=>{

  const categories = globalReferencesMapper(10002700)?.map((item)=>{
      return ({
        value : item?.reference_id,
        text : item?.reference_name_l10n
      })
    })
    setCategoryOptions(categories)
  },[data?.report])
  return (
    <PageLayout>
      <Stack direction="row" spacing={2} justifyContent={"space-between"}>
        <Box>
          <Typography
            component="h5"
            variant="h5"
            sx={{ fontWeight: 700, fontSize: 24 }}
          >
            Activity Management
          </Typography>
          <Box sx={{ backgroundColor: "#ff6e1b", height: 5, width: 100 }}></Box>
        </Box>
        <Button
          style={{ backgroundColor: "#ec6237", color: "white", float: "right" }}
          onClick={showModal}
          icon={<PlusOutlined />}
        >
          Add Activity
        </Button>
      </Stack>
      <Box mt={4}>
        {isLoading ? (
          <div style={{display:"flex",justifyContent:"center",alignItems:"center"}}>
            <Spin />
          </div>
        ) : (
          <Table columns={columns} dataSource={dataSource} loading={isLoading}/>
        )}
        <Modal
          title="Add Activity"
          open={isModalOpen}
          footer={false}
          onCancel={handleCancel}
        >
          <AddActivity
            key={new Date()}
            handleCancel={handleCancel}
            setRefresh={setRefresh}
            filters={categoryOptions}
          />
        </Modal>
      </Box>
    </PageLayout>
  );
};

export default withAuthorization(ActivityManagement, [CS_TEAM, VET_OPS])
