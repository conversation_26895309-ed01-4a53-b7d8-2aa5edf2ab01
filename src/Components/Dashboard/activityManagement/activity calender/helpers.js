import moment from "moment";

const formatStartDate = (date) => {
  // Convert the date to the format: yyyy-mm-dd
  let parts = date.split("/");
  let formattedDate = new Date(parts[2], parts[1] - 1, parts[0], 10, 15, 55);

  // Format the date to a string: yyyy-mm-ddTHH:MM:SSZ
  let outputDate =
    formattedDate.getFullYear() +
    "-" +
    ("0" + (formattedDate.getMonth() + 1)).slice(-2) +
    "-" +
    ("0" + formattedDate.getDate()).slice(-2) +
    "T" +
    ("0" + formattedDate.getHours()).slice(-2) +
    ":" +
    ("0" + formattedDate.getMinutes()).slice(-2) +
    ":" +
    ("0" + formattedDate.getSeconds()).slice(-2) +
    "Z";
  return outputDate;
};

// Function to generate calendar data based on the provided data
export const generateCalendarDataSan = (data) => {
  // Destructuring to get the startDate and frequency from data
  const { startDate, frequency } = data;
  // Destructuring to get the type and weekDaysRepetition from frequency
  const { type, weekDaysRepetition } = frequency;

  // format the start date
  const formattedStartDate = formatStartDate(startDate);
  // Creating a new Date object with the startDate
  let dateObj = new Date(startDate);
  // Initializing an empty array to store the list items
  let listItems = [];
  // Looping through the next 30 days
  for (let index = 0; index < 30; index++) {
    // Incrementing the date by 1 day
    dateObj.setDate(dateObj.getDate() + 1);
    // Formatting the date using moment.js
    const formatted = moment(dateObj.toISOString());
    // Pushing the formatted date, day of the week, and full date string into the listItems array
    listItems.push({
      date: formatted.format("DD MMM YYYY"), // The date in 'DD MMM YYYY' format
      day: formatted.day(), // The day of the week as a number (0 for Sunday, 1 for Monday, etc.)
      formatted: formatted.format("DD MMM dddd YYYY"), // The full date string
    });
  }

  // Initializing an empty array to store the matched dates
  let matchedDateWithFrequency = [];
  // Checking if the listItems array is not empty
  if (listItems?.length > 0) {
    // Looping through each item in the listItems array
    listItems?.forEach((item) => {
      // Checking if the day of the week for the current item is included in the weekDaysRepetition array
      if (weekDaysRepetition.includes(item.day)) {
        // If it is, pushing the full date string into the matchedDateWithFrequency array
        matchedDateWithFrequency.push(item.formatted);
      }
    });
  }

  // Logging the matched dates
  console.log(matchedDateWithFrequency);
  return matchedDateWithFrequency;
};

export const generateCalendarDataMod = (data) => {
  // Destructuring to get the startDate and frequency from data
  const { startDate, startTime, frequency } = data;
  // Destructuring to get the type and weekDaysRepetition from frequency
  const { type, weekDaysRepetition, numberOfRepetitions } = frequency;

  // format the start date
  const formattedStartDate = formatStartDate(startDate);
  // Creating a new Date object with the startDate
  let dateObj = new Date(formattedStartDate);
  // Initializing an empty array to store the list items
  let listItems = [];
  // Looping through the next 31 days
  for (let index = 0; index < 31; index++) {
    // Formatting the date using moment.js
    const formatted = moment(dateObj.toISOString());
    // Pushing the formatted date, day of the week, and full date string into the listItems array
    listItems.push({
      date: formatted.format("DD MMM YYYY"), // The date in 'DD MMM YYYY' format
      day: formatted.day(), // The day of the week as a number (0 for Sunday, 1 for Monday, etc.)
      formatted: formatted.format("DD MMM dddd YYYY"), // The full date string
    });
    // Incrementing the date by 1 day
    dateObj.setDate(dateObj.getDate() + 1);
  }

  // Initializing an empty array to store the matched dates
  let matchedDateWithFrequency = [];
  // Checking if the listItems array is not empty

  console.log(listItems);
  if (listItems?.length > 0) {
    // Looping through each item in the listItems array
    listItems?.map((item, index) => {
      if (index % numberOfRepetitions === 0) {
        switch (type) {
          case "daily":
            matchedDateWithFrequency.push(item?.formatted);
            return matchedDateWithFrequency;
          case "weekly":
            if (weekDaysRepetition.includes(item.day)) {
              // If it is, pushing the full date string into the matchedDateWithFrequency array
              matchedDateWithFrequency.push(item.formatted);
            }
            return matchedDateWithFrequency;
          case "monthly":
            if (
              item?.date ===
              moment(startDate, "DD/MM/YYYY").format("DD MMM YYYY")
            ) {
              matchedDateWithFrequency.push(item.formatted);
            }
            return matchedDateWithFrequency;
          case "none":
            return [];
          default:
            return [];
        }
      }
    });
  }
  return matchedDateWithFrequency;
};

export const generateCalendarData = (data) => {
  const { startDate, frequency, endingCondition } = data;
  // const {numberOfRepetitions } = frequency;

  const formattedStartDate = moment(startDate, "DD/MM/YYYY").toISOString();
  const formattedStart = moment(startDate, "DD/MM/YYYY");
  const formattedEnd = moment(endingCondition?.endDate, "DD/MM/YYYY");
  const diffInDays = formattedEnd.diff(formattedStart, "days");

  let dateObj = new Date(formattedStartDate);
  let listItems = [];
  let key = -1;
  let week = 0;
  let noOfDays =
    diffInDays + 1 || endingCondition?.endingOccurrencesNumber || 31;
  for (let index = 0; index < noOfDays; index++) {
    const formatted = moment(dateObj);
    const currentWeek = formatted.week();

    if (week !== currentWeek) {
      week = currentWeek;
      key++;
    }
    listItems.push({
      date: formatted.format("DD MMM YYYY"),
      day: formatted.day(),
      week: formatted.week(),
      key: key,
      formatted: formatted.format("DD MMM dddd YYYY"),
      month : formatted.month()
    });
    dateObj.setDate(dateObj.getDate() + 1);
  }
  localStorage.setItem("monthArray",JSON.stringify(listItems))
  let matchedDateWithFrequency = [];
  let dateOnly =  moment(startDate, "DD MMM YYYY").date();
  if (listItems?.length > 0) {
    listItems?.forEach((item, index) => {
      if (frequency?.type === "daily" && index % frequency?.numberOfRepetitions === 0) {
        // For "daily" events, include every numberOfRepetitions days
        matchedDateWithFrequency.push(item.formatted);
      } else if (
        frequency?.type === "weekly" &&
        item.key % frequency?.numberOfRepetitions === 0 &&
        frequency?.weekDaysRepetition?.includes(item?.day)
      ) {
        // For "weekly" events, include every numberOfRepetitions weeks on specified weekdays
        matchedDateWithFrequency.push(item.formatted);
      } else if (
        frequency?.type === "monthly" && dateOnly === moment(item?.date ,"DD MMM YYYY").date() && item?.month % frequency?.numberOfRepetitions === 0
      ) {
        matchedDateWithFrequency.push(item.formatted);
      } else if (frequency?.type === "none") {
        // Handle "none" events
        // matchedDateWithFrequency.push(item.formatted);
      }
    });
  }
  return matchedDateWithFrequency;
};
