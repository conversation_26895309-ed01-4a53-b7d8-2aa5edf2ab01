import { Calendar, theme, Typography, Button } from "antd";
import {LeftOutlined, RightOutlined} from "@ant-design/icons"
import moment from "moment";
import { generateCalendarData } from "./helpers";
import style from "../activitymanage.module.css";
import { Box } from "@mui/material";

const ActivityCalendar = ({
  activity_information,
  handleHeader,
}) => {
  const data = generateCalendarData(activity_information);
  const dateCellRender = (date) => {
    const currentDate = date.format("DD MMM dddd YYYY");
    const dayEvents = data.filter((event) => event === currentDate);

    return (
      <div>
        {dayEvents.map((event, index) => (
          <Box className={style.calendar_circle_a} key={index}></Box>
        ))}
      </div>
    );
  };
  
  const clickOnButton = (e) => {
    return undefined;
  };

  const { token } = theme.useToken();
  const wrapperStyle = {
    // width: 400,
    // marginLeft : 40,
    border: `1px solid ${token.colorBorderSecondary}`,
    borderRadius: token.borderRadiusLG,
  };

  var startDate = moment(activity_information?.startDate, "DD/MM/YYYY"); // parse date
  // var formattedDate = startDate.format("DD MMM YYYY dddd");
  const headerRenderCell=({ value, type, onChange, onTypeChange }) => {
   
    const onPrev = () => {
      const newValue = value.clone().subtract(1, 'month');
      onChange(newValue);
    };
    
    const onNext = () => {
      const newValue = value.clone().add(1, 'month');
      onChange(newValue);
    };
    // console.log(month)
    const monthName = value.format('MMMM');  // Get the current month name
    const presentYear = value.format("YYYY") // Get the current year
    return (
      <div style={{ padding: 8, display:"flex", justifyContent:"space-between", alignItems:"center" }}>
       <Button icon={<LeftOutlined />} type="primary" style={{backgroundColor : "#ff6e1b"}} size="middle" shape="circle" onClick={onPrev}/>
        <Typography>{monthName}-{presentYear}</Typography>
      <Button icon={<RightOutlined/>} type="primary" style={{backgroundColor : "#ff6e1b"}} size="middle" shape="circle" onClick={onNext}/>
      </div>
    );
  }
  return (
    <div style={wrapperStyle}>
      <Calendar
        fullscreen={false}
        cellRender={dateCellRender}
        className={style.box_style}
        headerRender={headerRenderCell}
        onSelect={clickOnButton}
        date
      />
    </div>
  );
};

export default ActivityCalendar;
