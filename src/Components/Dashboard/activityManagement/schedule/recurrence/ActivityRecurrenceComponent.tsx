import React, { useCallback, useEffect, useState } from "react";
import { Grid } from "@mui/material";

import { LocalizationProvider } from "@mui/x-date-pickers-pro";
import { AdapterDayjs } from "@mui/x-date-pickers-pro/AdapterDayjs";
import dayjs from "dayjs";
// import {
//   EndingConditionType,
//   FrequencyType,
//   RecurrenceType,
//   Recurrence,
// } from "..";
import Recurrence from "./Recurrence";
import { FrequencyType, RecurrenceType, EndingConditionType } from "./types";
import { useDispatch, useSelector } from "react-redux";
// import { WeekDays } from "./types";
// import { customRecurranceToStringCreator } from "./recurranceToStringCreator";

import { setSchedulePreview } from "../../../Tasks/ScheduleTask/scheduleTaskSlice/scheduleTaskSlice";
type CustomRecurrenceComponentProps = {
  setSchedule: (recurrence: any) => void;
};

interface StateType {
  scheduleTask : any
}

const ActivityRecurrenceComponent: React.FC<CustomRecurrenceComponentProps> = ({
  setSchedule,
}) => {
  const dispatch = useDispatch();
  const schedulePreviewData = useSelector((state : StateType)=> state?.scheduleTask) 
  const defaultEndingOccurrencesNumber = 1;
  const today = dayjs(new Date());
  const tommorrow = today.add(1, 'day');
  const defaultRecurrence = {
    startDate: today,
    endDate: tommorrow,
    frequency: FrequencyType.Daily,
    numberOfRepetitions: 1,
    weekDaysRepetition: [],
    endingCondition: EndingConditionType.EndDate,
    endingOccurrencesNumber: defaultEndingOccurrencesNumber,
    isAllDay: true,
    startTime: today,
    endTime: tommorrow,
  };
  const preScheduleData = schedulePreviewData?.schedulePreview
  
  const [recurrence, setRecurrence] =
    useState<RecurrenceType>(preScheduleData || defaultRecurrence);

  const handleRecurrenceChange = useCallback((updatedRecurrence: RecurrenceType) => {
    setRecurrence(updatedRecurrence);
    dispatch(setSchedulePreview(updatedRecurrence))
    setSchedule(updatedRecurrence);
  },[dispatch,setSchedule]);

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Recurrence recurrence={recurrence} onChange={handleRecurrenceChange}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={12}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Recurrence.StartDateSelector />
              </Grid>
              <Grid item xs={12} md={6}>
                <Recurrence.EndingConditionSelector />
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12} md={12}>
            <Recurrence.FrequencySelector />
          </Grid>
        </Grid>
      </Recurrence>
    </LocalizationProvider>
  );
};

export default ActivityRecurrenceComponent;
