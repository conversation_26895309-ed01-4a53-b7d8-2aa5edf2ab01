import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
} from "@mui/material";
import CustomRecurrenceComponent from "./CustomRecurrenceComponent";
import { useDispatch } from "react-redux";
import { updateSchedule } from "../../../../../redux/slices/scheduleSlice";
import { EndingConditionType, FrequencyType, RecurrenceType } from "./types";
import { customRecurranceToStringCreator } from "./recurranceToStringCreator";
import { useUpdateActivityMutation } from "../../reducer/activityManagementSlice";
import { clearUserToken } from "../../../../user/action";
import { useNavigate } from "react-router-dom";
import { useAppDispatch } from "../../../../../../src/redux/hook";
import { message } from "antd";

type CustomRecurrenceDialogProps = {
  setOpen: (open: boolean) => void;
  open: boolean;
  handleRepeatChange?: (value: string | null, dateString: string) => void;
  activity_id : Number;
  headerName : string;
};

const CustomRecurrenceDialog: React.FC<CustomRecurrenceDialogProps> = ({
  setOpen,
  open,
  handleRepeatChange,
  activity_id,
  headerName,
}) => {
  const [recurrance, setRecurrance] = useState<RecurrenceType>();
  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  const dispatch = useDispatch();
  const dispatch_app = useAppDispatch()
  const [updateActivity] = useUpdateActivityMutation();
  const navigate = useNavigate()

  const handleSaveSchedule = () => {
    if (!recurrance) return;
    const schedule = {
      displayString: "",
      startDate: recurrance?.startDate?.format("DD/MM/YYYY") || "",
      startTime: recurrance?.startTime?.toISOString() || "",
      frequency: {
        numberOfRepetitions: recurrance?.numberOfRepetitions || 0,
        type: recurrance?.frequency || FrequencyType.None,
        weekDaysRepetition: recurrance?.weekDaysRepetition || [],
      },
      endingCondition: {
        type: recurrance?.endingCondition || EndingConditionType.None,
        endTime: recurrance?.endTime?.toISOString() || "",
        endDate: recurrance?.endDate?.format("DD/MM/YYYY") || "",
        endingOccurrencesNumber: recurrance?.endingOccurrencesNumber || 0,
      },
      isAllDay: recurrance.isAllDay,
    };
    dispatch(
      updateSchedule({
        ...schedule,
        displayString: customRecurranceToStringCreator(schedule),
      })
    );
    const customPayload = {
      activity_id: activity_id,
      activity_information: {
        ...schedule,
        displayString: customRecurranceToStringCreator(schedule)
      }
    };
    updateActivity(customPayload)
    .then((response: any) => {
      if (response?.data) {
        message.success("Activity scheduled !")
      }
      if (response?.error?.status === 401) {
        dispatch_app(clearUserToken(navigate, "/login", "Session Expired!"))
      }
    })
    .catch((error) => {
      console.error(error);
    });

    handleClose();
  };

  return (
    <div>
      <Dialog open={open} onClose={handleClose} maxWidth="xs" fullWidth>
        <DialogTitle>{headerName}</DialogTitle>
        <DialogContent>
          <Box mt={2}>
            <CustomRecurrenceComponent setSchedule={setRecurrance} />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleSaveSchedule} color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default CustomRecurrenceDialog;
