import { Dayjs } from "dayjs";

export enum EndingConditionType {
  None = "none",
  EndDate = "end_date",
  OccurrencesNumber = "occurrences_number",
}

export enum FrequencyType {
  None = "none",
  Hourly = "hourly",
  Daily = "daily",
  Weekly = "weekly",
  Monthly = "monthly",
  Annually = "annually",
}

export enum WeekDays {
  Sunday = 0,
  Monday = 1,
  Tuesday = 2,
  Wednesday = 3,
  Thursday = 4,
  Friday = 5,
  Saturday = 6,
}

export interface ScheduleType {
  displayString: string;
  startDate: string;
  startTime: string | null;
  frequency: {
    numberOfRepetitions: number;
    type: FrequencyType;
    weekDaysRepetition: Array<number>;
  };
  endingCondition: {
    type: EndingConditionType;
    endTime: string | null;
    endDate: string | null;
    endingOccurrencesNumber: number;
  };
  isAllDay: boolean;
}

export interface RecurrenceType {
  startDate: Dayjs;
  frequency: FrequencyType;
  numberOfRepetitions?: number;
  weekDaysRepetition: Array<number>;
  endingCondition: EndingConditionType;
  endingOccurrencesNumber?: number;
  endDate?: Dayjs;
  isAllDay: boolean;
  startTime?: Dayjs;
  endTime?: Dayjs;
}

export interface RecurrenceDay {
  key: number;
  title: string;
  symbol: string;
}

export interface Option {
  key: string;
  title: string;
}
