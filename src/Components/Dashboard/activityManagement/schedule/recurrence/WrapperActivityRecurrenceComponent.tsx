import React, {
  useState,
  useImperative<PERSON>andle,
  forwardRef,
  useEffect,
} from "react";
import { Box } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { EndingConditionType, FrequencyType, RecurrenceType } from "./types";
import { customRecurranceToStringCreator } from "./recurranceToStringCreator";
import { useUpdateActivityMutation } from "../../reducer/activityManagementSlice";
import { useNavigate } from "react-router-dom";
import ActivityRecurrenceComponent from "./ActivityRecurrenceComponent";
// import ScheduleTask from "Components/Dashboard/Tasks/ScheduleTask/ScheduleTask";
import { setTaskSchedule } from "../../../Tasks/ScheduleTask/scheduleTaskSlice/scheduleTaskSlice";
import ToastersService from "../../../../../Services/toasters.service";

type CustomRecurrenceDialogProps = {
  setOpen: (open: boolean) => void;
  open: boolean;
  handleRepeatChange?: (value: string | null, dateString: string) => void;
  activity_id: Number;
  headerName: string;
  ref: any;
};
interface StateType {
  scheduleTask: any;
}

const WrapperActivityRecurrenceComponent = forwardRef<
  any,
  CustomRecurrenceDialogProps
>(({ setOpen, open, handleRepeatChange, activity_id, headerName }, ref) => {
  const [recurrance, setRecurrance] = useState<RecurrenceType>();

  const dispatch = useDispatch();
  // const dispatch_app = useAppDispatch();
  const handleSaveSchedule = () => {
    if (!recurrance) return;
    const schedule = {
      displayString: "",
      startDate: recurrance?.startDate?.format("DD/MM/YYYY") || "",
      startTime: recurrance?.startTime?.toISOString() || "",
      frequency: {
        numberOfRepetitions: recurrance?.numberOfRepetitions || 0,
        type: recurrance?.frequency || FrequencyType.None,
        weekDaysRepetition: recurrance?.weekDaysRepetition || [],
      },
      endingCondition: {
        type: recurrance?.endingCondition || EndingConditionType.None,
        endTime: recurrance?.endTime?.toISOString() || "",
        endDate: recurrance?.endDate?.format("DD/MM/YYYY") || "",
        endingOccurrencesNumber: recurrance?.endingOccurrencesNumber || 0,
      },
      isAllDay: recurrance.isAllDay,
    };
    dispatch(
      setTaskSchedule({
        ...schedule,
        displayString: customRecurranceToStringCreator(schedule),
      })
    );
  };
  useImperativeHandle(ref, () => ({
    log() {
      handleSaveSchedule();
    },
  }));

  useEffect(() => {
    handleSaveSchedule();
  }, [recurrance]);

  return (
    <Box sx={{ paddingTop: "18px", marginTop: "18px" }}>
      <Box mt={2}>
        <ActivityRecurrenceComponent setSchedule={setRecurrance} />
      </Box>
    </Box>
  );
});

export default WrapperActivityRecurrenceComponent;
