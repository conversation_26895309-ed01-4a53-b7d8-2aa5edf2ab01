import React, { useState } from "react";
import { Grid } from "@mui/material";

import { LocalizationProvider } from "@mui/x-date-pickers-pro";
import { AdapterDayjs } from "@mui/x-date-pickers-pro/AdapterDayjs";
import dayjs from "dayjs";
// import {
//   EndingConditionType,
//   FrequencyType,
//   RecurrenceType,
//   Recurrence,
// } from "..";
import Recurrence from "./Recurrence";
import { FrequencyType, RecurrenceType, EndingConditionType } from "./types";
import { WeekDays } from "./types";
import { customRecurranceToStringCreator } from "./recurranceToStringCreator";

type CustomRecurrenceComponentProps = {
  setSchedule: (recurrence: any) => void;
};

const CustomRecurrenceComponent: React.FC<CustomRecurrenceComponentProps> = ({
  setSchedule,
}) => {
  const defaultEndingOccurrencesNumber = 1;
  const today = dayjs(new Date());
  const defaultRecurrence = {
    startDate: today,
    endDate: today,
    frequency: FrequencyType.Daily,
    numberOfRepetitions: 1,
    weekDaysRepetition: [],
    endingCondition: EndingConditionType.EndDate,
    endingOccurrencesNumber: defaultEndingOccurrencesNumber,
    isAllDay: true,
    startTime: today,
    endTime: today,
  };
  const [recurrence, setRecurrence] =
    useState<RecurrenceType>(defaultRecurrence);

  const handleRecurrenceChange = (updatedRecurrence: RecurrenceType) => {    
    setRecurrence(updatedRecurrence);
    setSchedule(updatedRecurrence);
  };

  //   console.log(recurrence);

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Recurrence recurrence={recurrence} onChange={handleRecurrenceChange}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={12}>
            <Recurrence.StartDateSelector />
          </Grid>
          <Grid item xs={12} md={12}>
            <Recurrence.FrequencySelector />
          </Grid>
          <Grid item sm={12}>
            <Recurrence.EndingConditionSelector />
          </Grid>
          {/* <Grid item sm={12}>
            <Recurrence.TimeSelector />
          </Grid> */}
        </Grid>
      </Recurrence>
    </LocalizationProvider>
  );
};

export default CustomRecurrenceComponent;
