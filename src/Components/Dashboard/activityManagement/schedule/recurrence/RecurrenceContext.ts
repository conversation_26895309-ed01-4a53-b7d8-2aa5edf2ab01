import React from "react";
import { EndingConditionType, FrequencyType, RecurrenceType } from "./types";
import dayjs from "dayjs";

interface RecurrenceContextType {
  recurrence: RecurrenceType;
  onFieldChange: (key: string, value: any) => void;
  onFieldsChange: (object: any) => void;
}
const today = dayjs(new Date());
const contextInitValue: RecurrenceContextType = {
  recurrence: {
    startDate: today,
    endDate: today,
    isAllDay: false,
    frequency: FrequencyType.Weekly,
    numberOfRepetitions: 2,
    weekDaysRepetition: [],
    endingCondition: EndingConditionType.EndDate,
    endingOccurrencesNumber: 1,
    startTime: today,
    endTime: today,
  },
  onFieldChange: () => {},
  onFieldsChange: () => {},
};

const RecurrenceContext =
  React.createContext<RecurrenceContextType>(contextInitValue);

export default RecurrenceContext;
