import dayjs from "dayjs";
import { ScheduleType, WeekDays } from "./types";

export const customRecurranceToStringCreator = (recurrence: ScheduleType) => {
  const { startDate, startTime, frequency, endingCondition } = recurrence;
  let frequencyString = "";

  switch (frequency.type) {
    case "daily":
      frequencyString = `Every ${frequency.numberOfRepetitions} Day(s)`;
      break;
    case "weekly":
      frequencyString = `Every ${frequency.numberOfRepetitions} Week(s)`;
      if (frequency.weekDaysRepetition.length > 0) {
        const repetitationDaystring = frequency.weekDaysRepetition.map(
          (day) => WeekDays[day]
        );
        const weekDays = repetitationDaystring.join(", ");
        frequencyString += ` On ${weekDays}`;
      }
      break;
    case "monthly":
      frequencyString = `Every ${
        frequency.numberOfRepetitions
      } Month(s) on the ${dayjs(startTime).startOf("day").date()} day`;
      break;
    case "annually":
      frequencyString = `Every ${
        frequency.numberOfRepetitions
      } Year(s) on the ${dayjs(startTime).startOf("day").date()} day of ${dayjs(
        startTime
      )
        .startOf("day")
        .format("MMMM")}`;
      break;
    default:
      frequencyString = "Invalid Frequency";
      break;
  }

  if (endingCondition.type === "occurrences_number") {
    frequencyString += ` until ${endingCondition.endingOccurrencesNumber} occurrences`;
  } else if (endingCondition.type === "end_date") {
    frequencyString += ` until ${dayjs(endingCondition.endDate).format(
      "MMMM DD, YYYY"
    )}`;
  }

  return frequencyString;
};
