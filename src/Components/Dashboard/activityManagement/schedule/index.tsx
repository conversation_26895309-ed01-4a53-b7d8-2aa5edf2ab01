import React, { useState } from "react";
import { <PERSON>u<PERSON><PERSON>, Button, Menu } from "@mui/material";
import CustomRecurrenceDialog from "./recurrence/CustomRecurrenceDialog";
import dayjs from "dayjs";
import { WeekDays } from "./recurrence/types";

import advancedFormat from "dayjs/plugin/advancedFormat";

import { updateSchedule } from "../../../../redux/slices/scheduleSlice";

import { useAppDispatch, useAppSelector } from "../../../../redux/hook";
import { useUpdateActivityMutation } from "../reducer/activityManagementSlice";
import { clearUserToken } from "../../../user/action";
import { useNavigate } from "react-router-dom";
import { message } from "antd";
import WrapperActivityRecurrenceComponent from "./recurrence/WrapperActivityRecurrenceComponent";

dayjs.extend(advancedFormat);

const defaultValues = {
  displayString: "Schedule",
  startDate: dayjs(new Date()).format("DD/MM/YYYY"),
  startTime: dayjs(new Date()).toISOString(),
  frequency: {
    numberOfRepetitions: 0,
    type: "none",
    weekDaysRepetition: [],
  },
  endingCondition: {
    type: "none",
    endTime: null,
    endDate: null,
    endingOccurrencesNumber: null,
  },
  isAllDay: true,
};

const FREQUENCY_OPTIONS = {
  none: {
    ...defaultValues,
    displayString: "Don't Repeat",
  },
  daily: {
    ...defaultValues,
    frequency: {
      numberOfRepetitions: 1,
      type: "daily",
    },
    displayString: "Daily",
  },
  weekly: {
    ...defaultValues,
    frequency: {
      numberOfRepetitions: 1,
      type: "weekly",
      weekDaysRepetition: [dayjs(new Date()).day()],
    },
    displayString: `Weekly on ${WeekDays[dayjs(new Date()).day()]}`,
  },
  monthly: {
    ...defaultValues,
    frequency: {
      numberOfRepetitions: 1,
      type: "monthly",
      weekDaysRepetition: [],
    },
    displayString: `Monthly on ${dayjs(new Date()).format("Do")}`,
  },
};

const RecurrenceMenu = ({ activity_id }: { activity_id: Number }) => {
  const dispatch = useAppDispatch();
  const schedule = useAppSelector((state) => state.schedule);
  const navigate = useNavigate();

  const [open, setOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  
  const [updateActivity] = useUpdateActivityMutation();

  const handleRepeatChange = (value: string | null) => {
    if (value !== null) {
      dispatch(
        updateSchedule(
          FREQUENCY_OPTIONS[value as keyof typeof FREQUENCY_OPTIONS]
        )
      );
      const customPayload = {
        activity_id: activity_id,
        activity_information : FREQUENCY_OPTIONS[value as keyof typeof FREQUENCY_OPTIONS],
      };
      updateActivity(customPayload)
      .then((response: any) => {
        if (response?.data) {
          message.success("Activity scheduled !")
        }
        if (response?.error?.status === 401) {
          dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
        }
      })
      .catch((error) => {
        console.error(error);
      });
    }
    handleClose();
  };

  const handleOpen = () => {
    setOpen(true);
  };

  return (
    <div>
      <Button
        aria-controls="simple-menu"
        aria-haspopup="true"
        onClick={handleClick}
        variant="outlined"
      >
        {schedule.displayString || "Schedule"}
      </Button>
      <Menu
        id="simple-menu"
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
      >
        {/* <MenuItem onClick={() => handleRepeatChange("none")}>
          Dont Repeat
        </MenuItem>
        <MenuItem onClick={() => handleRepeatChange("daily")}>Daily</MenuItem>
        <MenuItem onClick={() => handleRepeatChange("weekly")}>
          Weekly on {WeekDays[dayjs(new Date()).day()]}
        </MenuItem>
        <MenuItem onClick={() => handleRepeatChange("monthly")}>
          Monthly on {dayjs(new Date()).format("Do")}
        </MenuItem> */}
        <MenuItem onClick={() => handleOpen()}>Custom</MenuItem>
        {/* Add more recurrence options here */}
      </Menu>
      <CustomRecurrenceDialog open={open} setOpen={setOpen} activity_id={activity_id} headerName="Custom Recurrence" />
    </div>
  );
};

export default RecurrenceMenu;
