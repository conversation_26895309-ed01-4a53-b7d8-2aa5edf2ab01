import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { BASE_URL } from "../../../../Services/api.service";

export const activityManagementApi = createApi({
  reducerPath: "activityManagement",
  tagTypes:["Activity"],
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().user.userToken.accessToken;
      if (token) {
        headers.set("token", token);
      }
      return headers;
    },
  
  }),
  endpoints: (builder) => ({
    getActivity : builder.query({
      keepUnusedDataFor:1000000,
      query : ()=>({
        url : "/v2/activities",
        method : "GET"
      }),
      serializeQueryArgs:({endpointName}
      )=>{
        return endpointName
      },
      providesTags:["Activity"]
    }),
    getActivityDetailsByActivityId : builder.query({
      query : (activity_id)=>({
        url : `/v2/activities/${activity_id}?form=activity_form_config`,
        method : "GET"
      })
    }),
    addActivity: builder.mutation({
      query: (payload) => ({
        url: "/v2/activities",
        method: "POST",
        body: payload,
      }),
      invalidatesTags:["Activity"]
    }),
    updateActivity: builder.mutation({
      query: (data) => {
        const { activity_id, ...restData } = data;
        return {
          url: `/v2/activities/${activity_id}`,
          method: "PUT",
          body: restData
        };
      },
      invalidatesTags: ["Activity"]
    }),
    scheduleTask : builder.mutation({
      query : (payload)=>({
        url : '/schedule/tasks/animal',
        method : "POST",
        body : payload
      }),
      invalidatesTags: ["Activity"]
    }),
    getCustomersList : builder.mutation({
      query : ()=>({
        url : "/customer-listingantd",
        method : "POST",
      })
    }),
    createForm : builder.mutation({
      query : (payload) =>({
        url : `/v2/activities/${payload?.activity_id}`,
        method : "PATCH",
        body: payload?.jsonContent
      })
    })
  }),

});

export const { useAddActivityMutation, useUpdateActivityMutation, useGetActivityQuery , useScheduleTaskMutation, useGetCustomersListMutation, useCreateFormMutation, useGetActivityDetailsByActivityIdQuery} = activityManagementApi;
