import { useNavigate, useSearchParams } from "react-router-dom";
import "./Farmers.css";
import { useSelector, useDispatch } from "react-redux";
import { useState, useCallback } from "react";
import { getFarmersFilterData, getFarmersReport } from "../../../router";
import { clearUserToken } from "../../user/action";
import {
  createPageParamsFromURLString,
  OCTable,
} from "../../Common/AntTableHelper";
import { setFreelancerMobNumber } from "../../../redux/slices/ppuu/reducers";
import { Typography } from "antd";

const { Paragraph, Text } = Typography;

const {
  CS_TEAM,
  MARKETING_TEAM,
  SALES_TEAM,
  FINANCE_TEAM,
  OPS_TEAM,
  VET_OPS,
  SALES_OPS,
  withAuthorization,
} = require("../../user/authorize");

const Farmers = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const userLanguage = useSelector((state) => state.user.userLanguage);
  const userToken = useSelector((state) => state.user.userToken);

  const navigateToAddFarmer = () => {
    navigate("/add-farmer1");
    dispatch(setFreelancerMobNumber(""));
  };

  const farmersTableConfiguration = {
    enableSelection: false,
    defaultPageSize: 10,
    enableHorizontalScrolling: true,
    showTableHeader: false,
    omniSearchRowEnabled: false,
    enableColumnConfiguration: false,
    enableOnChange: true,
    // getAllDataAtOnce: true,
    // disablePagination: true,
    dataTransformationRules: {
      // staff_filter_values: 'bdm_filter_values',
      // staff_name: 'bdm'
    },
    rowKey: "customer_id",
    columns: [
      {
        title: "Visual Id",
        key: "customer_visual_id",
      },
      {
        title: "Name",
        key: "customer_name",
        render: (text, record) => (
          <a
            className="farmers__farmer-name"
            href={`/farmer/${record?.customer_id}`}
          >
            {text ? text : "-"}
          </a>
        ),
      },
      {
        title: "Mobile",
        key: "mobile_number",
        width: 170,
        render: (text, record) => {
          const formattedText =
            text && text.startsWith("+91") ? text.slice(3) : text;
          return (
            formattedText !== null && (
              <Paragraph copyable>{formattedText}</Paragraph>
            )
          );
        },
      },
      {
        title: "Alt Contact Number",
        key: "alt_contact_number",
        width: 160,
        render: (text, record) => {
          const formattedText =
            text && text.startsWith("+91") ? text.slice(3) : text;
          return (
            formattedText !== null && (
              <Paragraph copyable>{formattedText}</Paragraph>
            )
          );
        },
      },
      {
        title: "Taluk",
        key: "taluk",
      },
      {
        title: "Village",
        key: "village",
      },
      {
        title: "LSS/Paravet",
        key: "staff_name",
      },
      {
        title: "T&C",
        key: "terms",
      },
      {
        title: "Number of Cattle Pending Subscription",
        key: "active_unsubscribed_animals",
      },
    ],
    columnConfiguration: {
      customer_visual_id: {
        headingText: "Visual Id",
        enableFilter: true,
        type: "string",
      },
      customer_name: {
        headingText: "Customer Name",
        enableFilter: true,
        type: "string",
        includeInOmnisearch: true,
      },
      mobile_number: {
        headingText: "Mobile",
        type: "string",
        enableFilter: true,
        includeInOmnisearch: true,
      },
      alt_contact_number: {
        headingText: "Alt Contact Number",
        type: "string",
        enableFilter: true,
        includeInOmnisearch: true,
      },
      taluk: {
        headingText: "Taluk",
        enableFilter: true,
        type: "default_array",
        dataType: "int",
        altColumn: "taluk_id",
        filterValuesKey: "taluk_filter_values",
      },
      village: {
        headingText: "Village",
        enableFilter: true,
        type: "default_array",
        dataType: "string",
        altColumn: "village_id",
      },
      staff_name: {
        headingText: "LSS/Paravet",
        enableFilter: true,
        type: "single_select_array",
        dataType: "string",
        filterValuesKey: "staff_filter_values",
        altColumn: "staff_id",
      },
      terms: {
        headingText: "T&C",
        enableFilter: true,
        type: "single_select_array",
        dataType: "string",
        filterValuesKey: "terms_filter_values",
      },
      active_unsubscribed_animals: {
        headingText: "Number of Cattle Pending Subscription",
        enableFilter: true,
        type: "number_range",
        dataType: "int",
      },
    },
  };

  const loadFilters = useCallback(
    async (userToken) => {
      // if (userToken) {
      try {
        const headers = {
          useCase: "Get Farmers With Location Recorded Report Filter Data",
          token: userToken.accessToken, //authToken
        };
        const response = await getFarmersFilterData(headers);
        return response.data;
      } catch (error) {
        if (error.response.status === 401) {
          dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
        }
      }
      // }
    },
    [dispatch, navigate]
  );

  const loadReport = useCallback(
    async (userToken, queryParams) => {
      try {
        const headers = {
          useCase: "Get Farmers With Location Recorded Report",
          token: userToken.accessToken, //authToken
        };
        const response = await getFarmersReport(headers, queryParams);
        if (response.status === 201) {
          return response.data;
        }
      } catch (error) {
        if (error?.response?.status === 401) {
          dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
        }
      }
    },
    [dispatch, navigate]
  );

  const [searchParams] = useSearchParams();
  const pageParams = createPageParamsFromURLString(
    farmersTableConfiguration,
    searchParams
  );
  const [reportData, setReportData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const loadInitialFilterDataCB = (additionalParams) => {
    return loadFilters(userToken, additionalParams);
  };

  const loadReportCB = (reportQueryParams, additionalParams) => {
    return loadReport(userToken, reportQueryParams, additionalParams);
  };
  const navigateToFarmerDetails = (customerId) => {
    navigate("/farmer/" + customerId);
  };

  return (
    <>
      <div className="my-3" style={{ display: "flex", alignItems: "center" }}>
        <div>
          <p
            className="m-0"
            style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
          >
            Farmers
          </p>
          <p
            className="m-0"
            style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
          >
            Farmers List
          </p>
        </div>
        <div className="farmers__add-farmer-btn" onClick={navigateToAddFarmer}>
          + Add Farmer
        </div>
      </div>

      {/* farmer table  */}
      {userToken && userToken !== null && (
        <OCTable
          /* tableKey={tableKey}
      setTableKey={setTableKey} */
          reportData={reportData}
          setReportData={setReportData}
          tableConfiguration={farmersTableConfiguration}
          selectedRowKeys={selectedRowKeys}
          setSelectedRowKeys={setSelectedRowKeys}
          // postInitialLoadCallback={postInitialLoadCB}
          loadReportCallback={loadReportCB}
          loadInitialFilterDataCallback={loadInitialFilterDataCB}
          // parentPageParams={pageParams}
          // showTableHeader={true}
          // omniSearchRowEnabled={true}
          // omniSearchRowBeginning={backToQueueListLinkUI}
          // omniSearchRowMiddle={viewRelatedAnimalButtons}
        />
      )}
    </>
  );
};

// export default Farmers;
export default withAuthorization(Farmers, [
  CS_TEAM,
  MARKETING_TEAM,
  SALES_TEAM,
  FINANCE_TEAM,
  VET_OPS,
  SALES_OPS,
]);
