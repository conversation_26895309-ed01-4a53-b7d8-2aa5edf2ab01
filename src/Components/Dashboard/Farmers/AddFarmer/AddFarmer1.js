import { useEffect, useState } from "react";
import axios from "axios";

import Row from "react-bootstrap/Row";
import Col from "react-bootstrap/Col";
import Button from "react-bootstrap/Button";
import Modal from "react-bootstrap/Modal";

import "./AddFarmer.css";
import {
  BASE_URL,
  instance,
  tokenFailureHandler,
} from "../../../../Services/api.service";

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  extractChangedValues,
  assignReferencesAsListToForm
} from "../../../Common/KrushalOCForm";
import { Link, useNavigate } from "react-router-dom";
import ToastersService from "../../../../Services/toasters.service";
import resizeFile from "../../../../Services/file-resizer.service";
import { useSelector } from "react-redux";
import { globalReferencesMapper } from "../../../../Utilities/Common/utils";

const {CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../../user/authorize')

const farmerFormDefinitionTab0 = {
  formControls: {
    farmerName: {
      type: "text-l10n",
      label: { en: "Name", mr: "शेतकऱ्याचे नाव" },
      placeHolderText: "Enter Farmer's Name",
      validations: [
        { type: "Mandatory" },
        { type: "regex", regexArray: ["Name"] }
    ],
      errorMessage: "Name is mandatory and should not have special characters",
    },
    farmerAddress: {
      type: "text-l10n",
      label: { en: "Address", mr: "शेतकऱ्याचे नाव" },
      placeHolderText: "Enter Farmer's Address",
    },
    farmerMobile: {
      type: "text-numeric",
      label: "Mobile Number",
      placeHolderText: "Enter Farmer's Mobile  Number",
      validations: [
        { type: "Mandatory" },
        { type: "regex", regexArray: ["IndiaMobileNumber"] },
      ],
      errorMessage: "Enter a valid mobile number!",
    },
    farmerAdhaar: {
      type: "text-numeric",
      label: "Aadhaar Number",
      placeHolderText: "Enter Farmer's Aadhaar Number",
      validations: [
        // { type: "Mandatory" },
        { type: "regex", regexArray: ["AadhaarNumber"] },
      ],
      errorMessage: "A 12 digit number is mandatory",
    },
    farmerPan: {
      type: "text-numeric",
      label: "PAN no.",
      placeHolderText: "Enter PAN no.",
      validations: [
        // { type: "Mandatory" },
        { type: "regex", regexArray: ["PanNumber"] },
      ],
      errorMessage: "Please enter valid PAN number",
    },
    farmerDateOfBirth: {
      type: "date",
      label: "DOB",
      placeHolderText: "Click To Set Farmer's Date of Birth",
    },
    farmerAge: {
      type: "text-numeric",
      label: "Age (in years)",
      placeHolderText: "Enter Farmer's Age",
    },

    farmerState: {
      type: "single-select-l10n",
      label: "State*",
      labelKeyInList: "state_name_l10n",
      valueKeyInList: "state_id",
      placeHolderText: "Select Farmer State*",
      errorMessage: "Farmer State is mandatory",
      // validations: [{ type: "Mandatory" }, { type: "regex", regexArray: ["PositiveDecimal"] }],
      onSetValue: async (
        userLanguage,
        newFormState,
        setFormState,
        stateIdInArray
      ) => {
        let updatedFormState = newFormState;
        if(stateIdInArray.length > 0) {
          updatedFormState = await getDistrictBasedOnState(
            newFormState,
            setFormState,
            stateIdInArray[0]
          );
        }
        console.log("AF fDT0 fD oSV 2, updatedFormState = ", updatedFormState);
        return updatedFormState;
      },
    },

    farmerDistrict: {
      type: "single-select-l10n",
      label: "District*",
      labelKeyInList: "district_name_l10n",
      valueKeyInList: "district_id",
      placeHolderText: "Select Farmer District*",
      errorMessage: "Farmer District is mandatory",
      // validations: [{ type: "Mandatory" }, { type: "regex", regexArray: ["PositiveDecimal"] }],
      onSetValue: async (
        userLanguage,
        newFormState,
        setFormState,
        districtIdInArray
      ) => {
        console.log("AF fDT0 fD oSV 1, newFormState = ", newFormState);
        let updatedFormState = newFormState;
        if(districtIdInArray.length > 0) {
         updatedFormState = await getTaluksBasedOnDistrict(
          newFormState,
          setFormState,
          districtIdInArray[0]
        );
        }
        console.log("AF fDT0 fD oSV 2, updatedFormState = ", updatedFormState);
        return updatedFormState;
      },
    },
    farmerTaluk: {
      type: "single-select-l10n",
      label: "Taluk*",
      placeHolderText: "Select Farmer Taluk*",
      errorMessage: "Farmer Taluk is mandatory",
      labelKeyInList: "taluk_name_l10n",
      valueKeyInList: "taluk_id",
      onSetValue: async (
        userLanguage,
        newFormState,
        setFormState,
        talukaIdInArray
      ) => {
        // console.log("talukaIdInArray = ", talukaIdInArray);
        let updatedFormState = newFormState;
        if(talukaIdInArray.length > 0) {
          updatedFormState = await getVillagesBasedOnTaluk(
          newFormState,
          setFormState,
          talukaIdInArray[0]
        );
        }
        // console.log("Updated form state", updatedFormState);
        return updatedFormState;
      },
      // validations: [{ type: "Mandatory" }, { type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    farmerVillage: {
      type: "single-select-l10n",
      label: "Village*",
      placeHolderText: "Select Farmer Village*",
      errorMessage: "Farmer Village is mandatory",
      labelKeyInList: "village_name_l10n",
      valueKeyInList: "village_id",
      validations: [
        { type: "Mandatory" },
        { type: "regex", regexArray: ["PositiveDecimal"] },
      ],
    },
  },

  visibleFormControls: [
    "farmerName",
    "farmerMobile",
    "farmerAdhaar",
    "farmerDateOfBirth",
    "farmerAge",
    "farmerAddress",
    "farmerState",
    "farmerDistrict",
    "farmerTaluk",
    "farmerVillage",
  ],
  elementToDataMapping: {
    farmerName: "customer_name_l10n",
    farmerMobile: "mobile_number",
    farmerAdhaar: "aadhaar_1",
    farmerDateOfBirth: "dob_1",
    farmerAge: "age_1",
    farmerAddress: "farmer_address_1",
    farmerState:"farmer_state_1",
    farmerDistrict: "farmer_district_1",
    farmerTaluk: "farmer_taluk_1",
    farmerVillage: "farmer_village_1",
    farmerPan : "farmer_pan_1"
  },
  dataToElementMapping: {
    customer_name_l10n: "farmerName",
    mobile_number: "farmerMobile",
    aadhaar_1: "farmerAdhaar",
    dob_1: "farmerDateOfBirth",
    age_1: "farmerAge",
    farmer_address_1: "farmerAddress",
    farmer_state_1:"farmerState",
    farmer_district_1: "farmerDistrict",
    farmer_taluk_1: "farmerTaluk",
    farmer_village_1: "farmerVillage",
    farmer_pan_1: "farmerPan"
  },
};

const farmerFormDefinitionTab1 = {
  formControls: {
    typeOfDairyFarm: {
      type: "single-select-l10n",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      label: "Type of Dairy Farm",
      placeHolderText: "Select Type of Dairy Farm",
      referenceCategoryId: 10001100,
    },
    acreageForFoliageCrops: {
      type: "positive-decimal",
      // readOnly: 1,
      label: "Acreage for foliage crops",
      placeHolderText: "Enter Acreage for foliage crops",
      errorMessage: "Input should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    // farmerTotalLandIncludingFoliageCrops: {
    //   type: "positive-decimal",
    //   // readOnly: 1,
    //   label: "Total land including foliage crop(in Acres)",
    //   placeHolderText:
    //     "Enter Farmer's Total land including foliage crop(in Acres)",
    //   errorMessage: "Input should be positive and numeric",
    //   validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    // },
    noOfLabourersUsedForDairy: {
      type: "positive-decimal",
      label: "Number of Labourers Used For Dairy",
      placeHolderText: "Enter Number of Labourers Used For Dairy",
      errorMessage: "Input should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    monthlyLabourCost: {
      type: "positive-decimal",
      label: "Monthly labour cost",
      placeHolderText: "Enter Monthly labour cost",
      errorMessage: "Cost should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    farmerTotalDairyIncome: {
      type: "positive-decimal",
      label: "Total Dairy Income",
      placeHolderText: "Enter Total Dairy Income",
      errorMessage: "Income should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    foliageCropsGrown: {
      type: "multi-select-l10n",
      label: "Foliage Crops Grown",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select Foliage Crops Grown",
      referenceCategoryId: 10001300,
    },
  },
  visibleFormControls: [
    "typeOfDairyFarm",
    "acreageForFoliageCrops",
    // "farmerTotalLandIncludingFoliageCrops",
    "noOfLabourersUsedForDairy",
    "monthlyLabourCost",
    "farmerTotalDairyIncome",
    "foliageCropsGrown",
  ],
  elementToDataMapping: {
    typeOfDairyFarm: "type_of_dairy_farm_1",
    acreageForFoliageCrops: "acreage_for_foliage_crops_1",
    // farmerTotalLandIncludingFoliageCrops: "total_land_inc_foilage_1",
    noOfLabourersUsedForDairy: "num_of_lab_used_for_cow_farm_1",
    monthlyLabourCost: "monthly_labour_cost_1",
    farmerTotalDairyIncome: "total_dairy_income_1",
    foliageCropsGrown: "foliage_crops_grown_1",
  },
  dataToElementMapping: {
    type_of_dairy_farm_1: "typeOfDairyFarm",
    acreage_for_foliage_crops_1: "acreageForFoliageCrops",
    // total_land_inc_foilage_1: "farmerTotalLandIncludingFoliageCrops",
    num_of_lab_used_for_cow_farm_1: "noOfLabourersUsedForDairy",
    monthly_labour_cost_1: "monthlyLabourCost",
    total_dairy_income_1: "farmerTotalDairyIncome",
    foliage_crops_grown_1: "foliageCropsGrown",
  },
};

const farmerFormDefinitionTab2 = {
  formControls: {
    totalNoOfCows: {
      type: "positive-number",
      label: "Total No Of Cows",
      placeHolderText: "Enter Total No Of Cows",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfBuffaloes: {
      type: "positive-number",
      label: "Total No Of Buffaloes",
      placeHolderText: "Enter Total No Of Buffaloes",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfCowCalves: {
      type: "positive-number",
      label: "Total No Of Cow Calves",
      placeHolderText: "Enter Total No Of Cow Calves",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfBuffaloCalves: {
      type: "positive-number",
      label: "Total No Of Buffalo Calves",
      placeHolderText: "Enter Total No Of Buffalo Calves",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfCowsBoughtInLast3Years: {
      type: "positive-number",
      // readOnly: 1,
      label: "Total Number of Cows Bought In Last 3 Years",
      placeHolderText:
        "Enter Total Number of Adult Animals Bought In Last 3 Years",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfCowsSoldInLast3Years: {
      type: "positive-number",
      // readOnly: 1,
      label: "Total Number of Cows Sold In Last 3 Years",
      placeHolderText:
        "Enter Total Number of Adult Animals Sold In Last 3 Years",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfFemaleCalvesPurchasedInLast3Years: {
      type: "positive-number",
      label: "Total Number of Female Calves Purchased In Last 3 years",
      placeHolderText:
        "Enter Total Number of Female Calves Purchased In Last 3 years",
      // readOnly: 1,
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfFemaleCalvesSoldInLast3Years: {
      type: "positive-number",
      label: "Total Number of Female Calves Sold In Last 3 years",
      placeHolderText:
        "Enter Total Number of Female Calves Sold In Last 3 years",
      // readOnly: 1,
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    averagePriceOfCowPurchasedInLast3Years: {
      type: "positive-decimal",
      // readOnly: 1,
      label: "Average Price of Cow Purchased in the last 3 years",
      placeHolderText:
        "Enter Average Price of Cow Purchased in the last 3 years",
      errorMessage: "Price should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    averagePriceOfCowSoldInLast3Years: {
      type: "positive-decimal",
      // readOnly: 1,
      label: "Average Price of Cow Sold in the last 3 years",
      placeHolderText: "Enter Average Price of Cow Sold in the last 3 years",
      errorMessage: "Price should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    averagePriceOfFemaleCalvesPurchasedInLast3Years: {
      type: "positive-decimal",
      // readOnly: 1,
      label: "Average Price of Female Calves Purchased in the last 3 years",
      placeHolderText:
        "Enter Average Price of Female Calves Purchased in the last 3 years",
      errorMessage: "Price should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    averagePriceOfFemaleCalvesSoldInLast3Years: {
      type: "positive-decimal",
      // readOnly: 1,
      label: "Average Price of Female Calves Sold in the last 3 years",
      placeHolderText:
        "Enter Average Price of Female Calves Sold in the last 3 years",
      errorMessage: "Price should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
  },
  visibleFormControls: [
    "totalNoOfCows",
    "totalNoOfBuffaloes",
    "totalNoOfCowCalves",
    "totalNoOfBuffaloCalves",
    "totalNoOfCowsBoughtInLast3Years",
    "totalNoOfCowsSoldInLast3Years",
    "totalNoOfFemaleCalvesPurchasedInLast3Years",
    "totalNoOfFemaleCalvesSoldInLast3Years",
    "averagePriceOfCowPurchasedInLast3Years",
    "averagePriceOfCowSoldInLast3Years",
    "averagePriceOfFemaleCalvesPurchasedInLast3Years",
    "averagePriceOfFemaleCalvesSoldInLast3Years",
  ],
  elementToDataMapping: {
    totalNoOfCows: "total_num_of_cow_1",
    totalNoOfBuffaloes: "total_num_of_buffalo_1",
    totalNoOfCowCalves: "total_num_of_cow_calves_1",
    totalNoOfBuffaloCalves: "total_num_of_buff_calves_1",
    totalNoOfCowsBoughtInLast3Years:
      "total_number_of_cow_purchased_in_last_3_years_1",
    totalNoOfCowsSoldInLast3Years: "total_number_of_cow_sold_in_last_3_years_1",
    totalNoOfFemaleCalvesPurchasedInLast3Years:
      "total_number_of_female_calves_purchased_in_last_3_years_1",
    totalNoOfFemaleCalvesSoldInLast3Years:
      "total_number_of_female_calves_sold_in_last_3_years_1",
    averagePriceOfCowPurchasedInLast3Years:
      "average_price_of_cow_purchased_in_last_3_years_1",
    averagePriceOfCowSoldInLast3Years:
      "average_price_of_cow_sold_in_last_3_years_1",
    averagePriceOfFemaleCalvesPurchasedInLast3Years:
      "average_price_of_female_calf_purchased_in_last_3_years_1",
    averagePriceOfFemaleCalvesSoldInLast3Years:
      "average_price_of_female_calves_sold_in_last_3_years_1",
  },
  dataToElementMapping: {
    total_num_of_cow_1: "totalNoOfCows",
    total_num_of_buffalo_1: "totalNoOfBuffaloes",
    total_num_of_cow_calves_1: "totalNoOfCowCalves",
    total_num_of_buff_calves_1: "totalNoOfBuffaloCalves",
    total_number_of_cow_purchased_in_last_3_years_1:
      "totalNoOfCowsBoughtInLast3Years",
    total_number_of_cow_sold_in_last_3_years_1: "totalNoOfCowsSoldInLast3Years",
    total_number_of_female_calves_purchased_in_last_3_years_1:
      "totalNoOfFemaleCalvesPurchasedInLast3Years",
    total_number_of_female_calves_sold_in_last_3_years_1:
      "totalNoOfFemaleCalvesSoldInLast3Years",
    average_price_of_cow_purchased_in_last_3_years_1:
      "averagePriceOfCowPurchasedInLast3Years",
    average_price_of_cow_sold_in_last_3_years_1:
      "averagePriceOfCowSoldInLast3Years",
    average_price_of_female_calf_purchased_in_last_3_years_1:
      "averagePriceOfFemaleCalvesPurchasedInLast3Years",
    average_price_of_female_calves_sold_in_last_3_years_1:
      "averagePriceOfFemaleCalvesSoldInLast3Years",
  },
};

const farmerFormDefinitionTab3 = {
  formControls: {
    farmerTotalIncome: {
      type: "positive-decimal",
      label: "Farmer Total Income",
      placeHolderText: "Enter Farmer's Total Income",
      errorMessage: "Income should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    totalNoOfAdultsInHousehold: {
      type: "positive-number",
      label: "Total Number of Adults in household (18 to 60)",
      placeHolderText: "Enter Total Number of Adults in household (18 to 60)",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfAgedDependents: {
      type: "positive-number",
      label: "Total Number of Aged Dependents (age 61 and above) in household",
      placeHolderText:
        "Enter Total Number of Aged Dependents (age 61 and above) in household",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfChildrenInHousehold: {
      type: "positive-number",
      label: "Total Number of Children in household (below 18)",
      placeHolderText: "Enter Total Number of Children in household (below 18)",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    farmerTotalEMIPerMonth: {
      type: "positive-decimal",
      label: "Total EMI per month",
      placeHolderText: "Enter Total EMI per month",
      errorMessage: "EMI should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    farmerCurrentLoanAmount: {
      type: "multi-select-l10n",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      label: "Current loans",
      placeHolderText: "Select Current loans",
      referenceCategoryId : 10003700,
    },
    assetsOwned: {
      type: "multi-select-l10n",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      label: "Assets owned",
      placeHolderText: "Select Assests Owned by Farmer",
    },
  },
  visibleFormControls: [
    "farmerTotalIncome",
    "totalNoOfAdultsInHousehold",
    "totalNoOfAgedDependents",
    "totalNoOfChildrenInHousehold",
    "farmerTotalEMIPerMonth",
    "farmerCurrentLoanAmount",
    "assetsOwned",
  ],
  elementToDataMapping: {
    farmerTotalIncome: "total_income_1",
    totalNoOfAdultsInHousehold: "total_number_of_adults_in_household_1",
    totalNoOfAgedDependents: "total_number_of_aged_dependents_1",
    totalNoOfChildrenInHousehold: "total_number_of_children_in_household_1",
    farmerTotalEMIPerMonth: "total_emi_per_month_1",
    farmerCurrentLoanAmount: "current_loan_1",
    assetsOwned: "assets_owned_1",
  },
  dataToElementMapping: {
    total_income_1: "farmerTotalIncome",
    total_number_of_adults_in_household_1: "totalNoOfAdultsInHousehold",
    total_number_of_aged_dependents_1: "totalNoOfAgedDependents",
    total_number_of_children_in_household_1: "totalNoOfChildrenInHousehold",
    total_emi_per_month_1: "farmerTotalEMIPerMonth",
    current_loan_1: "farmerCurrentLoanAmount",
    assets_owned_1: "assetsOwned",
  },
};

const initialFormStateTab0 = {
  farmerName: { value: {} },
  farmerMobile: {},
  farmerAdhaar: {},
  farmerDateOfBirth: {},
  farmerAge: {},
  farmerAddress: { value: {} },
  farmerState:{},
  farmerDistrict: {},
  farmerTaluk: {},
  farmerVillage: {},
  formSnackbar: {},
  farmerPan: {}
};

const initialFormStateTab1 = {
  typeOfDairyFarm: {},
  acreageForFoliageCrops: {},
  // farmerTotalLandIncludingFoliageCrops: {},
  noOfLabourersUsedForDairy: {},
  monthlyLabourCost: {},
  farmerTotalIncomeIncludingAll: {},
  farmerTotalDairyIncome: {},
  foliageCropsGrown: {},
  formSnackbar: {},
};

const initialFormStateTab2 = {
  formSnackbar: {},
  totalNoOfCows: {},
  totalNoOfBuffaloes: {},
  totalNoOfCowCalves: {},
  totalNoOfBuffaloCalves: {},
  totalNoOfCowsBoughtInLast3Years: {},
  totalNoOfCowsSoldInLast3Years: {},
  totalNoOfFemaleCalvesPurchasedInLast3Years: {},
  totalNoOfFemaleCalvesSoldInLast3Years: {},
  averagePriceOfCowPurchasedInLast3Years: {},
  averagePriceOfCowSoldInLast3Years: {},
  averagePriceOfFemaleCalvesPurchasedInLast3Years: {},
  averagePriceOfFemaleCalvesSoldInLast3Years: {},
};

const initialFormStateTab3 = {
  formSnackbar: {},
  farmerTotalIncome: {},
  totalNoOfAdultsInHousehold: {},
  totalNoOfAgedDependents: {},
  totalNoOfChildrenInHousehold: {},
  farmerTotalEMIPerMonth: {},
  farmerCurrentLoanAmount: {},
  assetsOwned: {},
};

const getStateList = async (
  navigate,
  formStateTab0,
  setFormStateTab0,
  // stateId
) => {
  try {
  
    const response = await instance({
      url: "/location/state",
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    // console.log(response);
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        // navigate("/login");
      }
console.log("state",response)
      const newFormState = assignDataToFormState(
        farmerFormDefinitionTab0,
        { farmer_state_1_list: response.data.data },
        formStateTab0,
        setFormStateTab0
      );
      return newFormState;
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const getDistrictBasedOnState = async (
  formStateTab0,
  setFormStateTab0,
  stateId
) => {
  try {
    // const response = await axios.get(
    //   BASE_URL + "/location/district?state_id=" + stateId,
    //   {
    //     headers: {
    //       token: localStorage.getItem("accessToken"),
    //       "X-App-Id": "123456789"
    //     },
    //   }
    // );
    const response = await instance({
      url: "/location/district?state_id=" + stateId,
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    // console.log(response);
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        // navigate("/login");
      }

      const newFormState = assignDataToFormState(
        farmerFormDefinitionTab0,
        { farmer_district_1_list: response.data.data },
        formStateTab0,
        setFormStateTab0
      );
      return newFormState;
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      // navigate("/login");
      window.location.reload();
    }
  }
};

const getTaluksBasedOnDistrict = async (
  formStateTab0,
  setFormStateTab0,
  districtId
) => {
  try {
    // const response = await axios.get(
    //   BASE_URL + "/location/taluka?district_id=" + districtId,
    //   {
    //     headers: {
    //       token: localStorage.getItem("accessToken"),
    //       "X-App-Id": "123456789"
    //     },
    //   }
    // );
    const response = await instance({
      url: "/location/taluka?district_id=" + districtId,
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        // navigate("/login");
      }
      console.log("AF1 gTBOD 1, formStateTab0 = ", formStateTab0);
      const updatedFormState = assignDataToFormState(
        farmerFormDefinitionTab0,
        { farmer_taluk_1_list: response.data.data },
        formStateTab0,
        setFormStateTab0
      );
      console.log("AF1 gTBOD 2, updatedFormState = ", updatedFormState);
      return updatedFormState;
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      // navigate('/login');
    }
  }
};

const getVillagesBasedOnTaluk = async (
  formStateTab0,
  setFormStateTab0,
  talukId
) => {
  try {
    // const response = await axios.get(
    //   BASE_URL + "/location/village?taluka_id=" + talukId,
    //   {
    //     headers: {
    //       token: localStorage.getItem("accessToken"),
    //       "X-App-Id": "123456789"
    //     },
    //   }
    // );
    const response = await instance({
      url: "/location/village?taluka_id=" + talukId,
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        // navigate("/login");
      }
      const updatedFormState = assignDataToFormState(
        farmerFormDefinitionTab0,
        { farmer_village_1_list: response.data.data },
        formStateTab0,
        setFormStateTab0
      );
      return updatedFormState;
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      // navigate('/login');
    }
  }
};

const getFormFieldConfigurations = async (
  navigate,
  formStateTab1,
  setFormStateTab1,
  formStateTab3,
  setFormStateTab3
) => {
  try {
    // const response = await axios.get(BASE_URL + "/references/customer", {
    //   headers: {
    //     token: localStorage.getItem("accessToken"),
    //     "X-App-Id": "123456789"
    //   },
    // });
    const response = await instance({
      url: "/references/customer",
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Form field configurations", response);
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        // navigate("/login");
        throw new Error("Unauthorized");
      }
      
      const newFormState1 = assignDataToFormState(
        farmerFormDefinitionTab1,
        {
          type_of_dairy_farm_1_list:
            response.data.data.type_of_dairy_farm_1.category_options,
          foliage_crops_grown_1_list:
            response.data.data.foliage_crops_grown_1.category_options,
        },
        formStateTab1,
        setFormStateTab1
      );
      const newFormState3 = assignDataToFormState(
        farmerFormDefinitionTab3,
        {
          current_loan_1_list:
            response.data.data.current_loan_1.category_options,
          assets_owned_1_list:
            response.data.data.assets_owned_1.category_options,
        },
        formStateTab3,
        setFormStateTab3
      );
      return [newFormState1, newFormState3];
    }
  } catch (error) {
    console.log(error);
    // throw error;
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const setReferenceValueFromRefData = (formStateTab1,setFormStateTab1,formStateTab3,setFormStateTab3)=>{
  const newFormState1 = assignDataToFormState(
    farmerFormDefinitionTab1,
    {
      type_of_dairy_farm_1_list:
      globalReferencesMapper(10001100),
      foliage_crops_grown_1_list:
      globalReferencesMapper(10001300),
    },
    formStateTab1,
    setFormStateTab1
  );
  const newFormState3 = assignDataToFormState(
    farmerFormDefinitionTab3,
    {
      current_loan_1_list:
        globalReferencesMapper(10003700),
      assets_owned_1_list:
        globalReferencesMapper(10003600)
    },
    formStateTab3,
    setFormStateTab3
  );
  return [newFormState1, newFormState3]
}

const saveFarmer = async (
  navigate,
  customerId,
  formStateTab0,
  setFormStateTab0,
  formStateTab1,
  setFormStateTab1,
  formStateTab2,
  setFormStateTab2,
  formStateTab3,
  setFormStateTab3,
  farmerThumbnailImage,
  farmerImages,
  dairyFarmImages,
  aadharImages,
  documentEntries,
  setDocumentEntries,
  autoSaveValue,
  isPPUU,
  staffId,
  setppuFarmerId,
  setShowAddFarmerModal,
  setRefresh,
  freelancerMobNumber
) => {
  console.log("AF1 sF 1, formStateTab0 = ", formStateTab0);
  console.log("AF1 sF 2, formStateTab1 = ", formStateTab1);
  console.log("AF1 sF 3, formStateTab3 = ", formStateTab3);
  console.log(
    "Images",
    farmerThumbnailImage,
    farmerImages,
    dairyFarmImages,
    aadharImages
  );
  const [currentFormStateTab0, validationFailedTab0] = validateFormElements(
    farmerFormDefinitionTab0,
    formStateTab0,
    setFormStateTab0
  );

  const [currentFormStateTab1, validationFailedTab1] = validateFormElements(
    farmerFormDefinitionTab1,
    formStateTab1,
    setFormStateTab1
  );
  const [currentFormStateTab2, validationFailedTab2] = validateFormElements(
    farmerFormDefinitionTab2,
    formStateTab2,
    setFormStateTab2
  );
  const [currentFormStateTab3, validationFailedTab3] = validateFormElements(
    farmerFormDefinitionTab3,
    formStateTab3,
    setFormStateTab3
  );
  // console.log("AF1 sF 2, currentFormStateTab0 = ", currentFormStateTab0);
  if (
    validationFailedTab0 ||
    validationFailedTab1 ||
    validationFailedTab2 ||
    validationFailedTab3
  ) {
    // do something
    console.log("Validation Failed");
    ToastersService.failureToast("Validation failed!");
  } else {
    console.log("you can save now");
    const extractedValuesTab0 = extractChangedValues(
      farmerFormDefinitionTab0,
      currentFormStateTab0
    );
    const extractedValuesTab1 = extractChangedValues(
      farmerFormDefinitionTab1,
      currentFormStateTab1
    );
    const extractedValuesTab2 = extractChangedValues(
      farmerFormDefinitionTab2,
      currentFormStateTab2
    );
    const extractedValuesTab3 = extractChangedValues(
      farmerFormDefinitionTab3,
      currentFormStateTab3
    );
   
    console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValuesTab0);
    console.log("AF1 sF 2, extractedValuesTab1 = ", extractedValuesTab1);
    console.log("AF1 sF 3, extractedValuesTab2 = ", extractedValuesTab2);
    console.log("AF1 sF 4, extractedValuesTab3 = ", extractedValuesTab3);
    let extractedValues = {};
    extractedValues = { ...extractedValues, ...extractedValuesTab0 };
    extractedValues = { ...extractedValues, ...extractedValuesTab1 };
    console.log("AF1 sF 5, extractedValues = ", extractedValues);
    extractedValues = { ...extractedValues, ...extractedValuesTab3 };
    console.log("AF1 sF 6, extractedValues = ", extractedValues);
    extractedValues = { ...extractedValues, ...extractedValuesTab2 };
    console.log("AF1 sF 7, extractedValues = ", extractedValues);

    configureParams(
      extractedValues,
      navigate,
      farmerThumbnailImage,
      farmerImages,
      dairyFarmImages,
      aadharImages,
      documentEntries,
      setDocumentEntries,
      isPPUU,
      staffId,
      setppuFarmerId,
      setShowAddFarmerModal,
      setRefresh,
      freelancerMobNumber
    );
  }
};

const modifySelectValueFormat = (object, singleSelectKeys, multiSelectKeys) => {
  console.log(object, singleSelectKeys, multiSelectKeys);

  for (const singleSelectKey of singleSelectKeys) {
    // const existingValue = object[singleSelectKey];
    // object[singleSelectKey] = String(existingValue);
    const existingValue = object[singleSelectKey];
    if (existingValue && existingValue.length > 0) {
      if (existingValue[0] !== undefined) {
        object[singleSelectKey] = String(existingValue);
      } else {
        object[singleSelectKey] = null;
      }
    }
  }

  for (const multiSelectKey of multiSelectKeys) {
    const existingValue = object[multiSelectKey];
    if (existingValue && existingValue.length > 0) {
      if (Array.isArray(existingValue) && existingValue.length > 0) {
        // const updatedExistingValue = { selected: existingValue };
        const updatedExistingValue = existingValue;
        object[multiSelectKey] = updatedExistingValue;
      } else {
        throw new Error("Wrong formatting");
      }
    }
  }
};

const configureParams = (
  extractedValues,
  navigate,
  farmerThumbnailImage,
  farmerImages,
  dairyFarmImages,
  aadharImages,
  documentEntries,
  setDocumentEntries,
  isPPUU,
  staffId,
  setppuFarmerId,
  setShowAddFarmerModal,
  setRefresh,
  freelancerMobNumber
) => {
  console.log("Configure Params", extractedValues);

  for (let key of Object.keys(extractedValues)) {
    if (extractedValues[key] !== null && key !== "farmer_address_1") {
      if (
        typeof extractedValues[key] === "object" &&
        !Array.isArray(extractedValues[key]) &&
        !(extractedValues[key] instanceof Date)
      ) {
        extractedValues[key] = extractedValues[key].en ? extractedValues[key].en : extractedValues[key].ul;
        console.log(extractedValues[key]);
      }
    }
  }

  // console.log('Extracted valuesss', extractedValues);

  modifySelectValueFormat(
    extractedValues,
    [
      // "type_of_dairy_farm_1",
      // "farmer_district_1",
      // "farmer_taluk_1",
      // "farmer_village_1",
    ],
    ["foliage_crops_grown_1", "assets_owned_1", "current_loan_1"]
  );
  console.log("AF1 sF 8, extractedValues = ", extractedValues);
  console.log("params1", extractedValues);
    const extractedValuesWithStaffId = (isPPUU && freelancerMobNumber) ?  {...extractedValues,"staff_id" : staffId, mobile_number : freelancerMobNumber} : extractedValues
  // call create farmer API
  createFarmer(
    extractedValuesWithStaffId,
    navigate,
    farmerThumbnailImage,
    farmerImages,
    dairyFarmImages,
    aadharImages,
    documentEntries,
    setDocumentEntries,
    setppuFarmerId,
    isPPUU,
    setShowAddFarmerModal,
    setRefresh
  );
};

const createFarmer = async (
  params,
  navigate,
  farmerThumbnailImage,
  farmerImages,
  dairyFarmImages,
  aadharImages,
  documentEntries,
  setDocumentEntries,
  setppuFarmerId,
  isPPUU,
  setShowAddFarmerModal,
  setRefresh
) => {
  let reqBody = {
    ...params,
    page: 1,
    farmer_taluka_1: params["farmer_taluk_1"],
  };
  console.log(
    "Create farmer Images",
    farmerThumbnailImage,
    farmerImages,
    dairyFarmImages,
    aadharImages
  );
  console.log("Req body", reqBody);
  try {
    // const response = await fetch(BASE_URL + "/create-or-update/customer", {
    //   method: "POST",
    //   body: JSON.stringify(reqBody),
    //   mode: "cors",
    //   headers: {
    //     "Content-type": "application/json",
    //     token: localStorage.getItem("accessToken"),
    //     "X-App-Id": "123456789"
    //   },
    // });
    const response = await instance({
      url: isPPUU ? "/ppu/farmer" : "/create-or-update/customer",
      method: "POST",
      data: JSON.stringify(reqBody),
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Create Farmer Response", response);
    if (response.status == 201) {
      if (response.data.customer_id) {
        // setFormStateTab0(initialFormStateTab0Reset)
        if (isPPUU) {
          setRefresh(Math.random())
          setppuFarmerId(response?.data?.customer_id)
          setShowAddFarmerModal(false)
        }
        ToastersService.successToast("Farmer created successfully!");
        // setCreatingFarmerLoader(false);
        console.log(
          response.data.customer_id,
          farmerThumbnailImage,
          farmerImages,
          dairyFarmImages,
          aadharImages
        );

        if (
          farmerImages.length > 0 ||
          dairyFarmImages.length > 0 ||
          aadharImages.length > 0 ||
          farmerThumbnailImage.length > 0
        ) {
          // setUploadingImagesLoader(true);
          let files = [];

          for (let file of farmerImages) {
            if (file.file.type.split("/")[0] == "image") {
              file.file = await resizeFile(file.file);
            }
          }
          for (let file of dairyFarmImages) {
            if (file.file.type.split("/")[0] == "image") {
              file.file = await resizeFile(file.file);
            }
          }
          for (let file of aadharImages) {
            if (file.file.type.split("/")[0] == "image") {
              file.file = await resizeFile(file.file);
            }
          }
          for (let file of farmerThumbnailImage) {
            if (file.file.type.split("/")[0] == "image") {
              file.file = await resizeFile(file.file);
            }
          }

          if (farmerImages.length > 0) {
            files.push({
              fieldName: "farmer_photo_1",
              images: farmerImages,
            });
          }
          if (dairyFarmImages.length > 0) {
            files.push({
              fieldName: "farm_photo_1",
              images: dairyFarmImages,
            });
          }
          if (aadharImages.length > 0) {
            files.push({
              fieldName: "farmer_aadhaar_photo_1",
              images: aadharImages,
            });
          }
          if (farmerThumbnailImage.length > 0) {
            files.push({
              fieldName: "customer_thumbnail_photo_1",
              images: farmerThumbnailImage,
            });
          }
          configureFiles(
            navigate,
            response.data.customer_id,
            files,
            documentEntries,
            setDocumentEntries
          );
        } else {
          if (!isPPUU) {
            navigate("/farmer/" + response.data.customer_id);
          }
        }
      }else if (response.data?.return_code === -1011) {
        ToastersService.failureToast("This number already exist !")
      }
    }
  } catch (error) {
    console.log(error);
    ToastersService.failureToast("Farmer not created!");
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const configureFiles = (
  navigate,
  farmerId,
  files,
  documentEntries,
  setDocumentEntries
) => {
  console.log("Post images", farmerId, files);

  let count = 0;
  for (let file of files) {
    for (let item of file.images) {
      count = count + 1;
      // filesCount = filesCount + 1;
      console.log("Files count", count);
    }
  }

  for (let file of files) {
    console.log(file);
    for (let item of file.images) {
      const formData = new FormData();
      formData.append("file", item.file);
      formData.append("entity", "farmer");
      uploadImageToServer(
        navigate,
        formData,
        file.fieldName,
        farmerId,
        item.description,
        count,
        documentEntries,
        setDocumentEntries
      );
    }
  }
};

const uploadImageToServer = async (
  navigate,
  formData,
  documentType,
  farmerId,
  imgDes,
  filesCount,
  documentEntries,
  setDocumentEntries
) => {
  try {
    // const response = await axios.post(BASE_URL + "/document", formData, {
    //   headers: {
    //     token: localStorage.getItem("accessToken"),
    //     "X-App-Id": "123456789",
    //   },
    // });
    // console.log(formData);
    const response = await instance({
      url: "/document",
      method: "POST",
      data: formData,
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("File upload response", response);
    if (response.status === 201) {
      ToastersService.successToast("File uploaded to server!");
      // return response.data.data.doc_key;
      console.log(documentType, farmerId, imgDes);
      console.log({
        document_type: documentType,
        entity: "farmer",
        entity_1_id: farmerId,
        url: response.data.data.doc_key,
        document_information: imgDes,
      });
      console.log(documentEntries, filesCount);
      documentEntries.push({
        document_type: documentType,
        entity: "farmer",
        entity_1_id: farmerId,
        url: response.data.data.doc_key,
        document_information: imgDes,
      });
      setDocumentEntries([...documentEntries]);
      console.log(documentEntries.length, filesCount);
      if (documentEntries.length === filesCount) {
        postImages(navigate, documentEntries, farmerId);
      }
    } else {
      throw Error("Image not uploaded to server!");
    }
  } catch (error) {
    console.log(error);
    // ToastersService.failureToast(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
    if (error.response.status === 413) {
      ToastersService.failureToast(error.response.data.error ? error.response.data.error : 'File size limit exceeded!');
    }
    // setCreatingTaskLoader(false);
  }
};

const postImages = async (navigate, documentEntries, farmerId) => {
  try {
    // const response = await axios.post(
    //   BASE_URL + "/document",
    //   { document_entries: documentEntries },
    //   {
    //     headers: {
    //       token: localStorage.getItem("accessToken"),
    //       "X-App-Id": "123456789",
    //     },
    //   }
    // );
    const response = await instance({
      url: "/document",
      method: "POST",
      data: { document_entries: documentEntries },
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Images to db", response);
    if (response.status === 201) {
      ToastersService.successToast("File saved to db!");
      navigate("/farmer/" + farmerId);
    } else {
      throw Error("Images not uploaded to db!");
    }
  } catch (error) {
    console.log(error);
    // ToastersService.failureToast(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
    // setCreatingTaskLoader(false);
  }
};

const AddFarmer1 = (props) => {
  console.log(props)
  const navigate = useNavigate();
  const freelancerMobNumber = useSelector((state)=> state.ppuu.freelancerMobNumber)
  console.log(freelancerMobNumber)
  const [addFarmerTabs, setAddFarmerTabs] = useState([
    {
      count: 1,
      isSelected: true,
      name: "Personal Details",
    },
    {
      count: 2,
      isSelected: false,
      name: "Farm / Land",
    },
    {
      count: 3,
      isSelected: false,
      name: "Cattle",
    },
    {
      count: 4,
      isSelected: false,
      name: "Other Info",
    },
    {
      count: 5,
      isSelected: false,
      name: "Pictures",
    },
  ]);
  const [formStateTab0, setFormStateTab0] = useState(initialFormStateTab0);
  const [formStateTab1, setFormStateTab1] = useState(initialFormStateTab1);
  const [formStateTab2, setFormStateTab2] = useState(initialFormStateTab2);
  const [formStateTab3, setFormStateTab3] = useState(initialFormStateTab3);
  
  let [farmerThumbnailImage, setFarmerThumbnailImage] = useState([]);
  let [farmerImages, setFarmerImages] = useState([]);
  let [dairyFarmImages, setDairyFarmImages] = useState([]);
  let [aadharImages, setAadharImages] = useState([]);
  let [image, setImage] = useState();
  let [imageDes, setImageDes] = useState("");
  const [documentEntries, setDocumentEntries] = useState([]);
  const [refresh,setRefresh] = useState(0)

  const [show, setShow] = useState(false);
  const [imageType, setImageType] = useState();
  const handleClose = () => setShow(false);
  const handleShow = (imageType) => {
    console.log(imageType);
    setImageType(imageType);
    setShow(true);
  };
  useEffect(() => {
    if (freelancerMobNumber) {
      assignDataToFormState(farmerFormDefinitionTab0,{mobile_number : freelancerMobNumber}, formStateTab0, setFormStateTab0)
      farmerFormDefinitionTab0.formControls.farmerMobile.readOnly = 1
      farmerFormDefinitionTab0.formControls.farmerMobile.validations = []
    }else{
      farmerFormDefinitionTab0.formControls.farmerMobile.readOnly = 0
      farmerFormDefinitionTab0.formControls.farmerMobile.validations = [
        { type: "Mandatory" },
        { type: "regex", regexArray: ["IndiaMobileNumber"] },
      ]
    }

    if (props.isPPUU) {
      if (!farmerFormDefinitionTab0.visibleFormControls.includes('farmerPan')) {
        farmerFormDefinitionTab0.visibleFormControls.push('farmerPan')
      }
    } else {
      farmerFormDefinitionTab0.visibleFormControls = farmerFormDefinitionTab0.visibleFormControls.filter(item => item !== "farmerPan");
    }
    const initializeForm = async () => {
      // const updatedFormStateReturnValue = await getFormFieldConfigurations(
      //   navigate,
      //   formStateTab1,
      //   setFormStateTab1,
      //   formStateTab3,
      //   setFormStateTab3
      // );
      setReferenceValueFromRefData(formStateTab1,setFormStateTab1,formStateTab3,setFormStateTab3)
      // assignReferencesAsListToForm(farmerFormDefinitionTab1)
      // assignDataToFormState(farmerFormDefinitionTab1,formStateTab1,formStateTab1,setFormStateTab1)
      getStateList(navigate, formStateTab0, setFormStateTab0)
      // getDistrictsList(navigate, formStateTab0, setFormStateTab0, 1026);
    };
    initializeForm();
  }, [refresh]);

  const onChangeTab = (tabCount) => {
    console.log(tabCount);
    for (let item of addFarmerTabs) {
      if (item.count === tabCount) {
        item.isSelected = true;
      } else {
        item.isSelected = false;
      }
    }
    // console.log(addFarmerTabs);
    setAddFarmerTabs([...addFarmerTabs]);
  };

  const farmerImagesUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setFarmerImages([...farmerImages, fileItem]);
  };

  const removeFarmerImageHandler = (image) => {
    for (let item of farmerImages) {
      if (item.file.name === image) {
        farmerImages = farmerImages.filter((i) => i.file.name !== image);
      }
    }
    setFarmerImages([...farmerImages]);
  };

  const dairyFarmImagesUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setDairyFarmImages([...dairyFarmImages, fileItem]);
  };

  const removeDairyFarmImageHandler = (image) => {
    for (let item of dairyFarmImages) {
      if (item.file.name === image) {
        dairyFarmImages = dairyFarmImages.filter((i) => i.file.name !== image);
      }
    }
    setDairyFarmImages([...dairyFarmImages]);
  };

  const aadharImagesUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setAadharImages([...aadharImages, fileItem]);
  };

  const removeAadharImageHandler = (image) => {
    console.log(image);
    for (let item of aadharImages) {
      if (item.file.name === image) {
        aadharImages = aadharImages.filter((i) => i.file.name !== image);
      }
    }
    setAadharImages([...aadharImages]);
  };

  const farmerThumbnailUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setFarmerThumbnailImage([...farmerThumbnailImage, fileItem]);
  };

  const removeThumbnailImageHandler = (image) => {
    console.log(image);
    for (let item of farmerThumbnailImage) {
      if (item.file.name === image) {
        console.log(item);
        farmerThumbnailImage = farmerThumbnailImage.filter(
          (i) => i.file.name !== image
        );
      }
    }
    setFarmerThumbnailImage([...farmerThumbnailImage]);
  };

  const imageUploadHandler = async (event) => {
    console.log(event.target.files[0]);
    if (
      event.target.files[0].type.split("/")[0] == "image" ||
      event.target.files[0].type == "application/pdf" ||
      event.target.files[0].type.split("/")[0] == "video"
    ) {
      setImage(event.target.files[0]);
    } else {
      ToastersService.failureToast("Invalid file type!");
    }
  };

  const removeImageHandler = () => {
    image = undefined;
    console.log("Image", image);
    setImage(image);
  };

  const imageDescriptionChangeHandler = (event) => {
    console.log(event.target.value);
    setImageDes(event.target.value);
  };

  const saveImageHandler = () => {
    console.log(image, imageDes, imageType);
    if (imageType === "thumbnail") {
      farmerThumbnailUploadHandler(image, imageDes);
    } else if (imageType === "farmer") {
      farmerImagesUploadHandler(image, imageDes);
    } else if (imageType === "farm") {
      dairyFarmImagesUploadHandler(image, imageDes);
    } else if (imageType === "adhaar") {
      aadharImagesUploadHandler(image, imageDes);
    }
    setImage(undefined);
    setImageDes("");
    handleClose();
  };
  
  return (
    <>
      {
        props.isPPUU === true ? (
        // <p
        //   className="mb-0 mt-3"
        //   style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
        // >
        //   Farmers / Add New Farmer
        // </p>
        <></>
        ) : 
        (<div className="mb-4">
        <Link className="farmer-details__back-btn" to="/farmer">
          <i className="fa fa-angle-double-left" aria-hidden="true"></i> Back to
          farmers list
        </Link>

        <p
          className="mb-0 mt-3"
          style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
        >
         Add New Farmer
        </p>
        <p
          className="m-0"
          style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
        >
          Add Farmer
        </p>
      </div>)
      }

     {
      props.isPPUU === true ? (""):
      (
        <div className="add-farmer__floating-menu mt-3">
        {addFarmerTabs.map((tab) => (
          <div
            key={tab.count}
            className={
              tab.isSelected === true
                ? "add-farmer__tab-selected"
                : "add-farmer__tab-unselected"
            }
            onClick={() => onChangeTab(tab.count)}
          >
            <div>{tab.name}</div>
          </div>
        ))}
      </div>
      )
     }

      {/* Page 1 */}
      {addFarmerTabs[0].isSelected === true && (
        <>
          <KrushalOCForm
            formState={formStateTab0}
            setFormState={setFormStateTab0}
            formDefinition={farmerFormDefinitionTab0}
          />
        </>
      )}

      {/* Page 2 */}
      {addFarmerTabs[1].isSelected === true && (
        <>
          <KrushalOCForm
            formState={formStateTab1}
            setFormState={setFormStateTab1}
            formDefinition={farmerFormDefinitionTab1}
          />
        </>
      )}

      {/* Page 3 */}
      {addFarmerTabs[2].isSelected === true && (
        <>
          <KrushalOCForm
            formState={formStateTab2}
            setFormState={setFormStateTab2}
            formDefinition={farmerFormDefinitionTab2}
          />
        </>
      )}

      {/* Page 4 */}
      {addFarmerTabs[3].isSelected === true && (
        <>
          <KrushalOCForm
            formState={formStateTab3}
            setFormState={setFormStateTab3}
            formDefinition={farmerFormDefinitionTab3}
          />
        </>
      )}

      {/* Page 5 */}
      {addFarmerTabs[4].isSelected === true && (
        <Row>
          <Col lg={4} md={6} sm={12}>
            <small>Thumbnail Image</small>
            <button
              className="images-btn"
              onClick={() => handleShow("thumbnail")}
            >
              Add an Image
            </button>
            <div className="my-3">
              <Row>
                {farmerThumbnailImage.map((item) => (
                  // <div key={item.url} className="add-farmer__img-div">
                  //   <div
                  //     className="add-farmer__remove-img-btn"
                  //     onClick={() => removeThumbnailImageHandler(item.url)}
                  //   >
                  //     <i className="fa fa-times"></i>
                  //   </div>
                  //   <img src={item.url} alt="" className="new-img" />
                  // </div>
                  <Col
                    lg={6}
                    md={6}
                    sm={12}
                    xs={12}
                    key={item.file.lastModified}
                  >
                    <div className="add-task__img-div">
                      <div
                        className="add-farmer__remove-img-btn"
                        style={{
                          fontSize: "11px",
                          backgroundColor: "red",
                        }}
                        onClick={() =>
                          removeThumbnailImageHandler(item.file.name)
                        }
                      >
                        <i className="fa fa-trash"></i>
                      </div>
                      <div
                        style={{
                          width: "100%",
                          height: "150px",
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <div
                          style={{
                            width: "150px",
                            height: "150px",
                            marginRight: "1rem",
                            borderRight: "1px solid silver",
                          }}
                        >
                          {/* Image */}
                          {item.file.type.split("/")[0] == "image" && (
                            <img
                              src={URL.createObjectURL(item.file)}
                              alt=""
                              className="new-img"
                            />
                          )}
                          {/* Document */}
                          {item.file.type == "application/pdf" && (
                            <div className="selected-file">
                              <div className="text-center">
                                <div>
                                  <i
                                    className="fa fa-file"
                                    aria-hidden="true"
                                  ></i>
                                </div>
                                <div style={{ fontSize: "12px" }}>
                                  {item.file.name}
                                </div>
                              </div>
                            </div>
                          )}
                          {/* Video */}
                          {item.file.type.split("/")[0] == "video" && (
                            <video className="new-img" controls>
                              <source
                                src={URL.createObjectURL(item.file)}
                                type="video/mp4"
                              />
                            </video>
                          )}
                        </div>
                        <div>
                          <small>
                            <i className="fa fa-file"></i> {item.file.type}
                          </small>
                          <p className="mt-2 mb-0" style={{ fontSize: "14px" }}>
                            {item.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </div>
          </Col>
          <Col lg={4} md={6} sm={12}>
            <small>Pictures of the farmer</small>
            <button className="images-btn" onClick={() => handleShow("farmer")}>
              Add images or videos
            </button>
            <div className="my-3">
              <Row>
                {farmerImages.map((item) => (
                  <Col
                    lg={6}
                    md={6}
                    sm={12}
                    xs={12}
                    key={item.file.lastModified}
                  >
                    <div className="add-task__img-div">
                      <div
                        className="add-farmer__remove-img-btn"
                        style={{
                          fontSize: "11px",
                          backgroundColor: "red",
                        }}
                        onClick={() => removeFarmerImageHandler(item.file.name)}
                      >
                        <i className="fa fa-trash"></i>
                      </div>
                      <div
                        style={{
                          width: "100%",
                          height: "150px",
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <div
                          style={{
                            width: "150px",
                            height: "150px",
                            marginRight: "1rem",
                            borderRight: "1px solid silver",
                          }}
                        >
                          {/* Image */}
                          {item.file.type.split("/")[0] == "image" && (
                            <img
                              src={URL.createObjectURL(item.file)}
                              alt=""
                              className="new-img"
                            />
                          )}
                          {/* Document */}
                          {item.file.type == "application/pdf" && (
                            <div className="selected-file">
                              <div className="text-center">
                                <div>
                                  <i
                                    className="fa fa-file"
                                    aria-hidden="true"
                                  ></i>
                                </div>
                                <div style={{ fontSize: "12px" }}>
                                  {item.file.name}
                                </div>
                              </div>
                            </div>
                          )}
                          {/* Video */}
                          {item.file.type.split("/")[0] == "video" && (
                            <video className="new-img" controls>
                              <source
                                src={URL.createObjectURL(item.file)}
                                type="video/mp4"
                              />
                            </video>
                          )}
                        </div>
                        <div>
                          <small>
                            <i className="fa fa-file"></i> {item.file.type}
                          </small>
                          <p className="mt-2 mb-0" style={{ fontSize: "14px" }}>
                            {item.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </div>
          </Col>
          <Col lg={4} md={6} sm={12}>
            <small>Pictures of the cow farm</small>
            <button className="images-btn" onClick={() => handleShow("farm")}>
              Add images or videos
            </button>
            <div className="my-3">
              {/* {dairyFarmImages.map((item) => (
                <div key={item.url} className="add-farmer__img-div">
                  <div
                    className="add-farmer__remove-img-btn"
                    onClick={() => removeDairyFarmImageHandler(item.url)}
                  >
                    <i className="fa fa-times"></i>
                  </div>
                  <img src={item.url} alt="" className="new-img" />
                </div>
              ))} */}
              <Row>
                {dairyFarmImages.map((item) => (
                  // <div key={item.url} className="add-farmer__img-div">
                  //   <div
                  //     className="add-farmer__remove-img-btn"
                  //     onClick={() => removeFarmerImageHandler(item.url)}
                  //   >
                  //     <i className="fa fa-times"></i>
                  //   </div>
                  //   <img src={item.url} alt="" className="new-img" />
                  // </div>
                  <Col
                    lg={6}
                    md={6}
                    sm={12}
                    xs={12}
                    key={item.file.lastModified}
                  >
                    <div className="add-task__img-div">
                      <div
                        className="add-farmer__remove-img-btn"
                        style={{
                          fontSize: "11px",
                          backgroundColor: "red",
                        }}
                        onClick={() =>
                          removeDairyFarmImageHandler(item.file.name)
                        }
                      >
                        <i className="fa fa-trash"></i>
                      </div>
                      <div
                        style={{
                          width: "100%",
                          height: "150px",
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <div
                          style={{
                            width: "150px",
                            height: "150px",
                            marginRight: "1rem",
                            borderRight: "1px solid silver",
                          }}
                        >
                          {/* Image */}
                          {item.file.type.split("/")[0] == "image" && (
                            <img
                              src={URL.createObjectURL(item.file)}
                              alt=""
                              className="new-img"
                            />
                          )}
                          {/* Document */}
                          {item.file.type == "application/pdf" && (
                            <div className="selected-file">
                              <div className="text-center">
                                <div>
                                  <i
                                    className="fa fa-file"
                                    aria-hidden="true"
                                  ></i>
                                </div>
                                <div style={{ fontSize: "12px" }}>
                                  {item.file.name}
                                </div>
                              </div>
                            </div>
                          )}
                          {/* Video */}
                          {item.file.type.split("/")[0] == "video" && (
                            <video className="new-img" controls>
                              <source
                                src={URL.createObjectURL(item.file)}
                                type="video/mp4"
                              />
                            </video>
                          )}
                        </div>
                        <div>
                          <small>
                            <i className="fa fa-file"></i> {item.file.type}
                          </small>
                          <p className="mt-2 mb-0" style={{ fontSize: "14px" }}>
                            {item.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </div>
          </Col>
          <Col lg={4} md={6} sm={12}>
            <small>Scan copy of Aadhar card from both sides</small>
            <button className="images-btn" onClick={() => handleShow("adhaar")}>
              Add images or PDF
            </button>
            <div className="my-3">
              {/* {aadharImages.map((item) => (
                <div key={item.url} className="add-farmer__img-div">
                  <div
                    className="add-farmer__remove-img-btn"
                    onClick={() => removeAadharImageHandler(item.url)}
                  >
                    <i className="fa fa-times"></i>
                  </div>
                  <img src={item.url} alt="" className="new-img" />
                </div>
              ))} */}
              <Row>
                {aadharImages.map((item) => (
                  // <div key={item.url} className="add-farmer__img-div">
                  //   <div
                  //     className="add-farmer__remove-img-btn"
                  //     onClick={() => removeFarmerImageHandler(item.url)}
                  //   >
                  //     <i className="fa fa-times"></i>
                  //   </div>
                  //   <img src={item.url} alt="" className="new-img" />
                  // </div>
                  <Col
                    lg={6}
                    md={6}
                    sm={12}
                    xs={12}
                    key={item.file.lastModified}
                  >
                    <div className="add-task__img-div">
                      <div
                        className="add-farmer__remove-img-btn"
                        style={{
                          fontSize: "11px",
                          backgroundColor: "red",
                        }}
                        onClick={() => removeAadharImageHandler(item.file.name)}
                      >
                        <i className="fa fa-trash"></i>
                      </div>
                      <div
                        style={{
                          width: "100%",
                          height: "150px",
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <div
                          style={{
                            width: "150px",
                            height: "150px",
                            marginRight: "1rem",
                            borderRight: "1px solid silver",
                          }}
                        >
                          {/* Image */}
                          {item.file.type.split("/")[0] == "image" && (
                            <img
                              src={URL.createObjectURL(item.file)}
                              alt=""
                              className="new-img"
                            />
                          )}
                          {/* Document */}
                          {item.file.type == "application/pdf" && (
                            <div className="selected-file">
                              <div className="text-center">
                                <div>
                                  <i
                                    className="fa fa-file"
                                    aria-hidden="true"
                                  ></i>
                                </div>
                                <div style={{ fontSize: "12px" }}>
                                  {item.file.name}
                                </div>
                              </div>
                            </div>
                          )}
                          {/* Video */}
                          {item.file.type.split("/")[0] == "video" && (
                            <video className="new-img" controls>
                              <source
                                src={URL.createObjectURL(item.file)}
                                type="video/mp4"
                              />
                            </video>
                          )}
                        </div>
                        <div>
                          <small>
                            <i className="fa fa-file"></i> {item.file.type}
                          </small>
                          <p className="mt-2 mb-0" style={{ fontSize: "14px" }}>
                            {item.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </div>
          </Col>
        </Row>
      )}

      <div style={{ display: "flex", marginLeft: "auto" }}>
        <button
          className="btn add-farmer__submit-btn mt-0 mb-5"
          onClick={async () => {
            await saveFarmer(
              navigate,
              -1,
              formStateTab0,
              setFormStateTab0,
              formStateTab1,
              setFormStateTab1,
              formStateTab2,
              setFormStateTab2,
              formStateTab3,
              setFormStateTab3,
              farmerThumbnailImage,
              farmerImages,
              dairyFarmImages,
              aadharImages,
              documentEntries,
              setDocumentEntries,
              -1,
              props?.isPPUU,
              props?.staffId,
              props?.setppuFarmerId,
              props?.setShowAddFarmerModal,
              setRefresh,
              freelancerMobNumber
            );
          }}
        >
          Submit
        </button>
      </div>

      <Modal show={show} onHide={handleClose}>
        <Modal.Header closeButton>
          <Modal.Title>Add Image</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <small>Choose Image</small>
          <input
            className="input-field"
            style={{ width: "100%" }}
            type="file"
            onChange={imageUploadHandler}
          />
          {image && (
            <>
              {/* Image */}
              {image.type.split("/")[0] == "image" && (
                <div className="add-farmer__img-div">
                  <div
                    className="add-farmer__remove-img-btn"
                    onClick={() => removeImageHandler()}
                  >
                    <i className="fa fa-times"></i>
                  </div>
                  <img
                    src={URL.createObjectURL(image)}
                    alt=""
                    className="new-img"
                  />
                </div>
              )}
              {/* PDF */}
              {image.type == "application/pdf" && (
                <div className="add-farmer__img-div">
                  <div
                    className="add-farmer__remove-img-btn"
                    onClick={() => removeImageHandler()}
                  >
                    <i className="fa fa-times"></i>
                  </div>
                  <div className="selected-file">
                    <div className="text-center">
                      <div>
                        <i className="fa fa-file" aria-hidden="true"></i>
                      </div>
                      <div style={{ fontSize: "12px" }}>{image.name}</div>
                    </div>
                  </div>
                </div>
              )}
              {/* Video */}
              {image.type.split("/")[0] == "video" && (
                <div className="add-farmer__img-div">
                  <div
                    className="add-farmer__remove-img-btn"
                    onClick={() => removeImageHandler()}
                  >
                    <i className="fa fa-times"></i>
                  </div>
                  <div className="selected-file">
                    <div className="text-center">
                      <div>
                        <i className="fa fa-file-video" aria-hidden="true"></i>
                      </div>
                      <div style={{ fontSize: "12px" }}>{image.name}</div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
          <small>Description</small>
          <textarea
            className="input-field"
            style={{ width: "100%" }}
            rows="3"
            type="text"
            defaultValue={imageDes}
            onChange={imageDescriptionChangeHandler}
          ></textarea>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="primary"
            onClick={saveImageHandler}
            style={{ background: "#673AB7", color: "#fff", border: "none" }}
          >
            Save
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

// export default AddFarmer1;
export default withAuthorization(AddFarmer1, [CS_TEAM])