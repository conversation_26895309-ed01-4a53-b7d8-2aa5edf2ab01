import { useEffect, useState, useCallback } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import axios from "axios";
import <PERSON><PERSON>iewer from "react-simple-image-viewer";
import resizeFile from "../../../../Services/file-resizer.service";
import Row from "react-bootstrap/Row";
import Col from "react-bootstrap/Col";
import Button from "react-bootstrap/Button";
import Modal from "react-bootstrap/Modal";
import Switch from "@mui/material/Switch";
import "./FarmerItem.css";
import {
  BASE_URL,
  instance,
  tokenFailureHandler,
} from "../../../../Services/api.service";
import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  extractChangedValues,
} from "../../../Common/KrushalOCForm";
import ToastersService from "../../../../Services/toasters.service";
import Moment from "react-moment";
// import Table from "react-bootstrap/Table";
import { Table } from "antd";
import Loader from "../../../Loader/Loader";
import CareCalendar from "../../../Common/CareCalendar/CareCalendar";
import moment from "moment";
import { toast } from "react-toastify";
import FileNotFound from "../../../../Utilities/FileNotFound";
import { Troubleshoot } from "@mui/icons-material";
import { globalReferencesMapper } from "../../../../Utilities/Common/utils";
import { Modal as AtndModal } from "antd";

const { CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, OPS_TEAM, VET_OPS, SALES_OPS, withAuthorization } = require('../../../user/authorize')

const farmerFormDefinitionTab0 = {
  formControls: {
    farmerName: {
      type: "text-l10n",
      label: { en: "Farmer Name", mr: "शेतकऱ्याचे नाव" },
      placeHolderText: "Enter Farmer's Name",
      validations: [{ type: "Mandatory" }],
      errorMessage: "Name is mandatory and should not have special characters",
    },
    farmerVisualId: {
      type: "text",
      label: "Farmer Visual Id",
      readOnly: 1,
    },
    farmerAddress: {
      type: "text",
      label: { en: "Farmer Address", mr: "शेतकऱ्याचे नाव" },
      placeHolderText: "Enter Farmer's Address",
    },
    farmerMobile: {
      type: "text-numeric",
      label: "Farmer Mobile Number",
      placeHolderText: "Enter Farmer's Mobile  Number",
      validations: [
        { type: "Mandatory" },
        { type: "regex", regexArray: ["IndiaMobileNumber"] },
      ],
      errorMessage: "Enter a valid mobile number!",
    },
    farmerAdhaar: {
      type: "text-numeric",
      label: "Farmer Aadhaar Number",
      placeHolderText: "Enter Farmer's Aadhaar Number",
      validations: [
        { type: "Mandatory" },
        { type: "regex", regexArray: ["AadhaarNumber"] },
      ],
      errorMessage: "A 12 digit number is mandatory",
    },
    farmerDateOfBirth: {
      type: "date",
      label: "DOB of Farmer",
      placeHolderText: "Click To Set Farmer's Date of Birth",
    },
    farmerAge: {
      type: "text-numeric",
      label: "Farmer age (in years)",
      placeHolderText: "Enter Farmer's Age",
    },
    farmerState: {
      readOnly: 1,
      type: "single-select-l10n",
      label: "Farmer State*",
      labelKeyInList: "state_name_l10n",
      valueKeyInList: "state_id",
      placeHolderText: "Select Farmer State*",
      errorMessage: "Farmer State is mandatory",
      onSetValue: async (
        userLanguage,
        newFormState,
        setFormState,
        stateIdInArray
      ) => {
        const updatedFormState = await getStateLists(
          newFormState,
          setFormState,
          stateIdInArray[0]
        );
        return updatedFormState;
      },
    },
    farmerDistrict: {
      readOnly: 1,
      type: "single-select-l10n",
      label: "Farmer District*",
      labelKeyInList: "district_name_l10n",
      valueKeyInList: "district_id",
      placeHolderText: "Select Farmer District*",
      errorMessage: "Farmer District is mandatory",
      onSetValue: async (
        userLanguage,
        newFormState,
        setFormState,
        districtIdInArray
      ) => {
        const updatedFormState = await getTaluksBasedOnDistrict(
          newFormState,
          setFormState,
          districtIdInArray[0]
        );
        return updatedFormState;
      },
    },
    farmerTaluk: {
      readOnly: 1,
      type: "single-select-l10n",
      label: "Farmer Taluk*",
      placeHolderText: "Select Farmer Taluk*",
      errorMessage: "Farmer Taluk is mandatory",
      labelKeyInList: "taluk_name_l10n",
      valueKeyInList: "taluk_id",
      onSetValue: async (
        userLanguage,
        newFormState,
        setFormState,
        talukaIdInArray
      ) => {
        const updatedFormState = await getVillagesBasedOnTaluk(
          newFormState,
          setFormState,
          talukaIdInArray[0]
        );
        return updatedFormState;
      },
    },
    farmerVillage: {
      readOnly: 1,
      type: "single-select-l10n",
      label: "Farmer Village*",
      placeHolderText: "Select Farmer Village*",
      errorMessage: "Farmer Village is mandatory",
      labelKeyInList: "village_name_l10n",
      valueKeyInList: "village_id",
      validations: [{ type: "Mandatory" }],
    },
    farmerPan: {
      type: "text-numeric",
      label: "PAN Number",
      placeHolderText: "Enter PAN Number.",
      validations: [
        // { type: "Mandatory" },
        { type: "regex", regexArray: ["PanNumber"] },
      ],
      errorMessage: "Please enter valid PAN number",
    },
  },
  visibleFormControls: [
    "farmerName",
    "farmerVisualId",
    "farmerMobile",
    "farmerAdhaar",
    "farmerDateOfBirth",
    "farmerAge",
    "farmerAddress",
    "farmerState",
    "farmerDistrict",
    "farmerTaluk",
    "farmerVillage",
  ],
  elementToDataMapping: {
    farmerName: "customer_name_l10n",
    farmerVisualId: "customer_visual_id",
    farmerMobile: "mobile_number",
    farmerAdhaar: "aadhaar_1",
    farmerDateOfBirth: "dob_1",
    farmerAge: "age_1",
    farmerAddress: "farmer_address_1",
    farmerState: "farmer_state_1",
    farmerDistrict: "farmer_district_1",
    farmerTaluk: "farmer_taluk_1",
    farmerVillage: "farmer_village_1",
    farmerPan: "farmer_pan_1",
  },
  dataToElementMapping: {
    customer_name_l10n: "farmerName",
    customer_visual_id: "farmerVisualId",
    mobile_number: "farmerMobile",
    aadhaar_1: "farmerAdhaar",
    dob_1: "farmerDateOfBirth",
    age_1: "farmerAge",
    farmer_address_1: "farmerAddress",
    farmer_state_1: "farmerState",
    farmer_district_1: "farmerDistrict",
    farmer_taluk_1: "farmerTaluk",
    farmer_village_1: "farmerVillage",
    farmer_pan_1: "farmerPan",
  },
};

const farmerFormDefinitionTab1 = {
  formControls: {
    typeOfDairyFarm: {
      type: "single-select-l10n",
      allowOthers: 1,
      othersLabelInDropdown: { en: "Others" },
      othersLabel: { en: "Others Types of Dairy Farm" },
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      label: "Type of Dairy Farm",
      placeHolderText: "Select Type of Dairy Farm",
    },
    acreageForFoliageCrops: {
      type: "positive-decimal",
      label: "Acreage for foliage crops",
      placeHolderText: "Enter Acreage for foliage crops",
      errorMessage: "Input should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    noOfLabourersUsedForDairy: {
      type: "positive-decimal",
      label: "Number of Labourers Used For Dairy",
      placeHolderText: "Enter Number of Labourers Used For Dairy",
      errorMessage: "Input should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    monthlyLabourCost: {
      type: "positive-decimal",
      label: "Monthly labour cost",
      placeHolderText: "Enter Monthly labour cost",
      errorMessage: "Cost should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    farmerTotalDairyIncome: {
      type: "positive-decimal",
      label: "Total Dairy Income",
      placeHolderText: "Enter Total Dairy Income",
      errorMessage: "Income should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    foliageCropsGrown: {
      type: "multi-select-l10n",
      allowOthers: 1,
      othersLabelInDropdown: { en: "Others" },
      othersLabel: { en: "Others Types of Foliage Crops" },
      label: "Foliage Crops Grown",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      placeHolderText: "Select Foliage Crops Grown",
    },
  },
  visibleFormControls: [
    "typeOfDairyFarm",
    "acreageForFoliageCrops",
    "noOfLabourersUsedForDairy",
    "monthlyLabourCost",
    "farmerTotalDairyIncome",
    "foliageCropsGrown",
  ],
  elementToDataMapping: {
    typeOfDairyFarm: "type_of_dairy_farm_1",
    acreageForFoliageCrops: "acreage_for_foliage_crops_1",
    noOfLabourersUsedForDairy: "num_of_lab_used_for_cow_farm_1",
    monthlyLabourCost: "monthly_labour_cost_1",
    farmerTotalDairyIncome: "total_dairy_income_1",
    foliageCropsGrown: "foliage_crops_grown_1",
  },
  dataToElementMapping: {
    type_of_dairy_farm_1: "typeOfDairyFarm",
    acreage_for_foliage_crops_1: "acreageForFoliageCrops",
    num_of_lab_used_for_cow_farm_1: "noOfLabourersUsedForDairy",
    monthly_labour_cost_1: "monthlyLabourCost",
    total_dairy_income_1: "farmerTotalDairyIncome",
    foliage_crops_grown_1: "foliageCropsGrown",
  },
};

const farmerFormDefinitionTab2 = {
  formControls: {
    totalNoOfCows: {
      type: "positive-number",
      label: "Total No Of Cows",
      placeHolderText: "Enter Total No Of Cows",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfBuffaloes: {
      type: "positive-number",
      label: "Total No Of Buffaloes",
      placeHolderText: "Enter Total No Of Buffaloes",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfCowCalves: {
      type: "positive-number",
      label: "Total No Of Cow Calves",
      placeHolderText: "Enter Total No Of Cow Calves",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfBuffaloCalves: {
      type: "positive-number",
      label: "Total No Of Buffalo Calves",
      placeHolderText: "Enter Total No Of Buffalo Calves",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfCowsBoughtInLast3Years: {
      type: "positive-number",
      label: "Total Number of Cows Bought In Last 3 Years",
      placeHolderText:
        "Enter Total Number of Adult Animals Bought In Last 3 Years",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfCowsSoldInLast3Years: {
      type: "positive-number",
      label: "Total Number of Cows Sold In Last 3 Years",
      placeHolderText:
        "Enter Total Number of Adult Animals Sold In Last 3 Years",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfFemaleCalvesPurchasedInLast3Years: {
      type: "positive-number",
      label: "Total Number of Female Calves Purchased In Last 3 years",
      placeHolderText:
        "Enter Total Number of Female Calves Purchased In Last 3 years",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfFemaleCalvesSoldInLast3Years: {
      type: "positive-number",
      label: "Total Number of Female Calves Sold In Last 3 years",
      placeHolderText:
        "Enter Total Number of Female Calves Sold In Last 3 years",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    averagePriceOfCowPurchasedInLast3Years: {
      type: "positive-decimal",
      label: "Average Price of Cow Purchased in the last 3 years",
      placeHolderText:
        "Enter Average Price of Cow Purchased in the last 3 years",
      errorMessage: "Price should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    averagePriceOfCowSoldInLast3Years: {
      type: "positive-decimal",
      label: "Average Price of Cow Sold in the last 3 years",
      placeHolderText: "Enter Average Price of Cow Sold in the last 3 years",
      errorMessage: "Price should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    averagePriceOfFemaleCalvesPurchasedInLast3Years: {
      type: "positive-decimal",
      label: "Average Price of Female Calves Purchased in the last 3 years",
      placeHolderText:
        "Enter Average Price of Female Calves Purchased in the last 3 years",
      errorMessage: "Price should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    averagePriceOfFemaleCalvesSoldInLast3Years: {
      type: "positive-decimal",
      label: "Average Price of Female Calves Sold in the last 3 years",
      placeHolderText:
        "Enter Average Price of Female Calves Sold in the last 3 years",
      errorMessage: "Price should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
  },
  visibleFormControls: [
    "totalNoOfCows",
    "totalNoOfBuffaloes",
    "totalNoOfCowCalves",
    "totalNoOfBuffaloCalves",
    "totalNoOfCowsBoughtInLast3Years",
    "totalNoOfCowsSoldInLast3Years",
    "totalNoOfFemaleCalvesPurchasedInLast3Years",
    "totalNoOfFemaleCalvesSoldInLast3Years",
    "averagePriceOfCowPurchasedInLast3Years",
    "averagePriceOfCowSoldInLast3Years",
    "averagePriceOfFemaleCalvesPurchasedInLast3Years",
    "averagePriceOfFemaleCalvesSoldInLast3Years",
  ],
  elementToDataMapping: {
    totalNoOfCows: "total_num_of_cow_1",
    totalNoOfBuffaloes: "total_num_of_buffalo_1",
    totalNoOfCowCalves: "total_num_of_cow_calves_1",
    totalNoOfBuffaloCalves: "total_num_of_buff_calves_1",
    totalNoOfCowsBoughtInLast3Years:
      "total_number_of_cow_purchased_in_last_3_years_1",
    totalNoOfCowsSoldInLast3Years: "total_number_of_cow_sold_in_last_3_years_1",
    totalNoOfFemaleCalvesPurchasedInLast3Years:
      "total_number_of_female_calves_purchased_in_last_3_years_1",
    totalNoOfFemaleCalvesSoldInLast3Years:
      "total_number_of_female_calves_sold_in_last_3_years_1",
    averagePriceOfCowPurchasedInLast3Years:
      "average_price_of_cow_purchased_in_last_3_years_1",
    averagePriceOfCowSoldInLast3Years:
      "average_price_of_cow_sold_in_last_3_years_1",
    averagePriceOfFemaleCalvesPurchasedInLast3Years:
      "average_price_of_female_calf_purchased_in_last_3_years_1",
    averagePriceOfFemaleCalvesSoldInLast3Years:
      "average_price_of_female_calves_sold_in_last_3_years_1",
  },
  dataToElementMapping: {
    total_num_of_cow_1: "totalNoOfCows",
    total_num_of_buffalo_1: "totalNoOfBuffaloes",
    total_num_of_cow_calves_1: "totalNoOfCowCalves",
    total_num_of_buff_calves_1: "totalNoOfBuffaloCalves",
    total_number_of_cow_purchased_in_last_3_years_1:
      "totalNoOfCowsBoughtInLast3Years",
    total_number_of_cow_sold_in_last_3_years_1: "totalNoOfCowsSoldInLast3Years",
    total_number_of_female_calves_purchased_in_last_3_years_1:
      "totalNoOfFemaleCalvesPurchasedInLast3Years",
    total_number_of_female_calves_sold_in_last_3_years_1:
      "totalNoOfFemaleCalvesSoldInLast3Years",
    average_price_of_cow_purchased_in_last_3_years_1:
      "averagePriceOfCowPurchasedInLast3Years",
    average_price_of_cow_sold_in_last_3_years_1:
      "averagePriceOfCowSoldInLast3Years",
    average_price_of_female_calf_purchased_in_last_3_years_1:
      "averagePriceOfFemaleCalvesPurchasedInLast3Years",
    average_price_of_female_calves_sold_in_last_3_years_1:
      "averagePriceOfFemaleCalvesSoldInLast3Years",
  },
};

const farmerFormDefinitionTab3 = {
  formControls: {
    farmerTotalIncome: {
      type: "positive-decimal",
      label: "Farmer Total Income",
      placeHolderText: "Enter Farmer's Total Income",
      errorMessage: "Income should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    totalNoOfAdultsInHousehold: {
      type: "positive-number",
      label: "Total Number of Adults in household (18 to 60)",
      placeHolderText: "Enter Total Number of Adults in household (18 to 60)",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfAgedDependents: {
      type: "positive-number",
      label: "Total Number of Aged Dependents (age 61 and above) in household",
      placeHolderText:
        "Enter Total Number of Aged Dependents (age 61 and above) in household",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    totalNoOfChildrenInHousehold: {
      type: "positive-number",
      label: "Total Number of Children in household (below 18)",
      placeHolderText: "Enter Total Number of Children in household (below 18)",
      errorMessage: "Count should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveNumber"] }],
    },
    farmerTotalEMIPerMonth: {
      type: "positive-decimal",
      label: "Total EMI per month",
      placeHolderText: "Enter Total EMI per month",
      errorMessage: "EMI should be positive and numeric",
      validations: [{ type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    farmerCurrentLoanAmount: {
      type: "multi-select-l10n",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      label: "Current loans",
      placeHolderText: "Select Current loans",
    },
    assetsOwned: {
      type: "multi-select-l10n",
      labelKeyInList: "reference_name_l10n",
      valueKeyInList: "reference_id",
      label: "Assets owned",
      placeHolderText: "Select Assests Owned by Farmer",
    },
  },
  visibleFormControls: [
    "farmerTotalIncome",
    "totalNoOfAdultsInHousehold",
    "totalNoOfAgedDependents",
    "totalNoOfChildrenInHousehold",
    "farmerTotalEMIPerMonth",
    "farmerCurrentLoanAmount",
    "assetsOwned",
  ],
  elementToDataMapping: {
    farmerTotalIncome: "total_income_1",
    totalNoOfAdultsInHousehold: "total_number_of_adults_in_household_1",
    totalNoOfAgedDependents: "total_number_of_aged_dependents_1",
    totalNoOfChildrenInHousehold: "total_number_of_children_in_household_1",
    farmerTotalEMIPerMonth: "total_emi_per_month_1",
    farmerCurrentLoanAmount: "current_loan_1",
    assetsOwned: "assets_owned_1",
  },
  dataToElementMapping: {
    total_income_1: "farmerTotalIncome",
    total_number_of_adults_in_household_1: "totalNoOfAdultsInHousehold",
    total_number_of_aged_dependents_1: "totalNoOfAgedDependents",
    total_number_of_children_in_household_1: "totalNoOfChildrenInHousehold",
    total_emi_per_month_1: "farmerTotalEMIPerMonth",
    current_loan_1: "farmerCurrentLoanAmount",
    assets_owned_1: "assetsOwned",
  },
};

const initialFormStateTab0 = {
  farmerName: { value: {} },
  farmerVisualId: {},
  farmerMobile: {},
  farmerAdhaar: {},
  farmerDateOfBirth: {},
  farmerAge: {},
  farmerAddress: { value: {} },
  farmerState: {},
  farmerDistrict: {},
  farmerTaluk: {},
  farmerVillage: {},
  formSnackbar: {},
  farmerPan: {},
};

const initialFormStateTab1 = {
  typeOfDairyFarm: {},
  acreageForFoliageCrops: {},
  noOfLabourersUsedForDairy: {},
  monthlyLabourCost: {},
  farmerTotalIncomeIncludingAll: {},
  farmerTotalDairyIncome: {},
  foliageCropsGrown: {},
  formSnackbar: {},
};

const initialFormStateTab2 = {
  formSnackbar: {},
  totalNoOfCows: {},
  totalNoOfBuffaloes: {},
  totalNoOfCowCalves: {},
  totalNoOfBuffaloCalves: {},
  totalNoOfCowsBoughtInLast3Years: {},
  totalNoOfCowsSoldInLast3Years: {},
  totalNoOfFemaleCalvesPurchasedInLast3Years: {},
  totalNoOfFemaleCalvesSoldInLast3Years: {},
  averagePriceOfCowPurchasedInLast3Years: {},
  averagePriceOfCowSoldInLast3Years: {},
  averagePriceOfFemaleCalvesPurchasedInLast3Years: {},
  averagePriceOfFemaleCalvesSoldInLast3Years: {},
};

const initialFormStateTab3 = {
  formSnackbar: {},
  farmerTotalIncome: {},
  totalNoOfAdultsInHousehold: {},
  totalNoOfAgedDependents: {},
  totalNoOfChildrenInHousehold: {},
  farmerTotalEMIPerMonth: {},
  farmerCurrentLoanAmount: {},
  assetsOwned: {},
};

const getStateLists = async (navigate) => {
  try {
    const response = await instance({
      url: "/location/state",
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("state", response);
    if (response.status === 200) {
      return response.data.data;
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const getDistrictsListOnly = async (navigate, stateId) => {
  try {
    const response = await instance({
      url: "/location/district?state_id=" + stateId,
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        navigate("/login");
      }
      return response.data.data;
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const getTaluksBasedOnDistrictListOnly = async (navigate, districtId) => {
  try {
    const response = await instance({
      url: "/location/taluka?district_id=" + districtId,
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        navigate("/login");
      }
      return response.data.data;
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const getTaluksBasedOnDistrict = async (
  formStateTab0,
  setFormStateTab0,
  districtId
) => {
  try {
    const response = await instance({
      url: "/location/taluka?district_id=" + districtId,
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
      }
      const updatedFormState = assignDataToFormState(
        farmerFormDefinitionTab0,
        { farmer_taluk_1_list: response.data.data },
        formStateTab0,
        setFormStateTab0
      );
      return updatedFormState;
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
    }
  }
};

const getVillagesBasedOnTalukListOnly = async (navigate, talukId) => {
  try {
    const response = await instance({
      url: "/location/village?taluka_id=" + talukId,
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        navigate("/login");
      }
      return response.data.data;
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const getVillagesBasedOnTaluk = async (
  formStateTab0,
  setFormStateTab0,
  talukId
) => {
  try {
    const response = await instance({
      url: "/location/village?taluka_id=" + talukId,
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
      }
      const updatedFormState = assignDataToFormState(
        farmerFormDefinitionTab0,
        { farmer_village_1_list: response.data.data },
        formStateTab0,
        setFormStateTab0
      );
      return updatedFormState;
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
    }
  }
};

const getFormFieldConfigurationsAsLists = async (navigate) => {
  try {
    const response = await instance({
      url: "/references/customer",
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Form field configurations", response);
    if (response.status === 200) {
      if (response.data.result === false) {
        tokenFailureHandler(response.data);
        throw new Error("Unauthorized");
      }

      const typeOfDairyFarmsList =
        response.data.data.type_of_dairy_farm_1.category_options;
      const currentLoanList =
        response.data.data.current_loan_1.category_options;
      const foliageCropsList =
        response.data.data.foliage_crops_grown_1.category_options;
      const farmerAssetsList =
        response.data.data.assets_owned_1.category_options;
      return [
        typeOfDairyFarmsList,
        currentLoanList,
        foliageCropsList,
        farmerAssetsList,
      ];
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const setReferenceValueFromRefData = () => {
  const typeOfDairyFarmsList = globalReferencesMapper(10001100);
  const currentLoanList = globalReferencesMapper(10003700);
  const foliageCropsList = globalReferencesMapper(10001300);
  const farmerAssetsList = globalReferencesMapper(10003600);
  return [
    typeOfDairyFarmsList,
    currentLoanList,
    foliageCropsList,
    farmerAssetsList,
  ];
};

const getCattleConfigurations = async (navigate) => {
  try {
    const response = await instance({
      url: "/references/animal",
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Cattle config", response);
    if (response.status === 200) {
      console.log(response.data.data.animal_inactive_reason_1.category_options);
      return response.data.data.animal_inactive_reason_1.category_options;
    }
  } catch (error) {
    console.log(error);
    // throw error;
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const updateFarmer = async (
  navigate,
  customerId,
  formStateTab0,
  setFormStateTab0,
  formStateTab1,
  setFormStateTab1,
  formStateTab2,
  setFormStateTab2,
  formStateTab3,
  setFormStateTab3,
  updateFarmerThumbnailImage,
  updateFarmerImages,
  updateDairyFarmImages,
  updateAadharImages,
  documentEntries,
  setDocumentEntries,
  autoSaveValue,
  farmerId
) => {
  console.log("AF1 sF 1, formStateTab0 = ", formStateTab0);
  console.log("AF1 sF 2, formStateTab1 = ", formStateTab1);
  console.log("AF1 sF 3, formStateTab2 = ", formStateTab2);
  console.log("AF1 sF 4, formStateTab3 = ", formStateTab3);
  const [currentFormStateTab0, validationFailedTab0] = validateFormElements(
    farmerFormDefinitionTab0,
    formStateTab0,
    setFormStateTab0
  );
  const [currentFormStateTab1, validationFailedTab1] = validateFormElements(
    farmerFormDefinitionTab1,
    formStateTab1,
    setFormStateTab1
  );
  const [currentFormStateTab2, validationFailedTab2] = validateFormElements(
    farmerFormDefinitionTab2,
    formStateTab2,
    setFormStateTab2
  );
  const [currentFormStateTab3, validationFailedTab3] = validateFormElements(
    farmerFormDefinitionTab3,
    formStateTab3,
    setFormStateTab3
  );
  if (
    validationFailedTab0 ||
    validationFailedTab1 ||
    validationFailedTab2 ||
    validationFailedTab3
  ) {
    console.log("Validation Failed");
    ToastersService.failureToast("Validation Failed!");
  } else {
    console.log("you can save now");
    const extractedValuesTab0 = extractChangedValues(
      farmerFormDefinitionTab0,
      currentFormStateTab0
    );
    const extractedValuesTab1 = extractChangedValues(
      farmerFormDefinitionTab1,
      currentFormStateTab1
    );
    const extractedValuesTab2 = extractChangedValues(
      farmerFormDefinitionTab2,
      currentFormStateTab2
    );
    const extractedValuesTab3 = extractChangedValues(
      farmerFormDefinitionTab3,
      currentFormStateTab3
    );
    console.log("AF1 sF 1, extractedValuesTab0 = ", extractedValuesTab0);
    console.log("AF1 sF 2, extractedValuesTab1 = ", extractedValuesTab1);
    console.log("AF1 sF 3, extractedValuesTab2 = ", extractedValuesTab2);
    console.log("AF1 sF 4, extractedValuesTab3 = ", extractedValuesTab3);
    let extractedValues = {};
    extractedValues = { ...extractedValues, ...extractedValuesTab1 };
    console.log("AF1 sF 5, extractedValues = ", extractedValues);
    extractedValues = { ...extractedValues, ...extractedValuesTab3 };
    console.log("AF1 sF 6, extractedValues = ", extractedValues);
    extractedValues = { ...extractedValues, ...extractedValuesTab2 };
    extractedValues = { ...extractedValues, ...extractedValuesTab0 };
    console.log("AF1 sF 7, extractedValues = ", extractedValues);
    configureParams(
      extractedValues,
      navigate,
      updateFarmerThumbnailImage,
      updateFarmerImages,
      updateDairyFarmImages,
      updateAadharImages,
      documentEntries,
      setDocumentEntries,
      farmerId
    );
  }
};

const modifySelectValueFormat = (object, singleSelectKeys, multiSelectKeys) => {
  console.log(object, singleSelectKeys, multiSelectKeys);

  for (const singleSelectKey of singleSelectKeys) {
    const existingValue = object[singleSelectKey];
    if (existingValue && existingValue.length > 0) {
      if (existingValue[0] !== undefined) {
        object[singleSelectKey] = String(existingValue);
      } else {
        object[singleSelectKey] = null;
      }
    }
  }

  for (const multiSelectKey of multiSelectKeys) {
    const existingValue = object[multiSelectKey];
    if (existingValue && existingValue.length > 0) {
      if (Array.isArray(existingValue) && existingValue.length > 0) {
        const updatedExistingValue = existingValue;
        object[multiSelectKey] = updatedExistingValue;
      } else {
        throw new Error("Wrong formatting");
      }
    }
  }
};

const configureParams = (
  extractedValues,
  navigate,
  updateFarmerThumbnailImage,
  updateFarmerImages,
  updateDairyFarmImages,
  updateAadharImages,
  documentEntries,
  setDocumentEntries,
  farmerId
) => {
  console.log("Configure Params", extractedValues);

  for (let key of Object.keys(extractedValues)) {
    if (extractedValues[key] !== null && key !== "farmer_address_1") {
      if (
        typeof extractedValues[key] === "object" &&
        !Array.isArray(extractedValues[key]) &&
        !(extractedValues[key] instanceof Date)
      ) {
        extractedValues[key] = extractedValues[key].en
          ? extractedValues[key].en
          : extractedValues[key].ul;
        console.log(extractedValues[key]);
      }
    }
  }

  modifySelectValueFormat(
    extractedValues,
    [
      // "type_of_dairy_farm_1",
      // "farmer_district_1",
      // "farmer_taluk_1",
      // "farmer_village_1",
    ],
    ["foliage_crops_grown_1", "assets_owned_1", "current_loan_1"]
  );
  console.log("AF1 sF 8, extractedValues = ", extractedValues);

  // return
  // call create farmer API
  updateFarmerDetails(
    extractedValues,
    navigate,
    updateFarmerThumbnailImage,
    updateFarmerImages,
    updateDairyFarmImages,
    updateAadharImages,
    documentEntries,
    setDocumentEntries,
    farmerId
  );
};

const updateFarmerDetails = async (
  params,
  navigate,
  updateFarmerThumbnailImage,
  updateFarmerImages,
  updateDairyFarmImages,
  updateAadharImages,
  documentEntries,
  setDocumentEntries,
  farmerId
) => {
  let reqBody = {
    ...params,
    page: 2,
    customer_id: farmerId,
    farmer_taluka_1: params["farmer_taluk_1"],
  };
  try {
    const response = await instance({
      url: "/create-or-update/customer",
      method: "POST",
      data: JSON.stringify(reqBody),
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    if (response.status == 201) {
      if (response.data.customer_id) {
        console.log(
          response.data.customer_id,
          updateFarmerThumbnailImage,
          updateFarmerImages,
          updateDairyFarmImages,
          updateAadharImages
        );

        if (
          updateFarmerImages.length > 0 ||
          updateDairyFarmImages.length > 0 ||
          updateAadharImages.length > 0 ||
          updateFarmerThumbnailImage.length > 0
        ) {
          let files = [];

          for (let file of updateFarmerImages) {
            if (file.file.type.split("/")[0] == "image") {
              file.file = await resizeFile(file.file);
            }
          }
          for (let file of updateDairyFarmImages) {
            if (file.file.type.split("/")[0] == "image") {
              file.file = await resizeFile(file.file);
            }
          }
          for (let file of updateAadharImages) {
            if (file.file.type.split("/")[0] == "image") {
              file.file = await resizeFile(file.file);
            }
          }
          for (let file of updateFarmerThumbnailImage) {
            if (file.file.type.split("/")[0] == "image") {
              file.file = await resizeFile(file.file);
            }
          }

          if (updateFarmerImages.length > 0) {
            files.push({
              fieldName: "farmer_photo_1",
              images: updateFarmerImages,
            });
          }
          if (updateDairyFarmImages.length > 0) {
            files.push({
              fieldName: "farm_photo_1",
              images: updateDairyFarmImages,
            });
          }
          if (updateAadharImages.length > 0) {
            files.push({
              fieldName: "farmer_aadhaar_photo_1",
              images: updateAadharImages,
            });
          }
          if (updateFarmerThumbnailImage.length > 0) {
            files.push({
              fieldName: "customer_thumbnail_photo_1",
              images: updateFarmerThumbnailImage,
            });
          }
          configureFiles(
            navigate,
            response.data.customer_id,
            files,
            documentEntries,
            setDocumentEntries
          );
        } else {
          ToastersService.successToast("Details updated successfully!");
        }
      }
    }
  } catch (error) {
    console.log(error);
    ToastersService.failureToast(error);
  }
};

const configureFiles = (
  navigate,
  farmerId,
  files,
  documentEntries,
  setDocumentEntries
) => {
  console.log("Post images", farmerId, files);

  let count = 0;
  for (let file of files) {
    for (let item of file.images) {
      count = count + 1;
      console.log("Files count", count);
    }
  }

  for (let file of files) {
    for (let item of file.images) {
      const formData = new FormData();
      formData.append("file", item.file);
      formData.append("entity", "farmer");
      uploadImageToServer(
        navigate,
        formData,
        file.fieldName,
        farmerId,
        item.description,
        count,
        documentEntries,
        setDocumentEntries
      );
    }
  }
};

const uploadImageToServer = async (
  navigate,
  formData,
  documentType,
  farmerId,
  imgDes,
  filesCount,
  documentEntries,
  setDocumentEntries
) => {
  try {
    const response = await instance({
      url: "/document",
      method: "POST",
      data: formData,
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("File upload response", response);
    if (response.status === 201) {
      ToastersService.successToast("File uploaded to server!");
      console.log(documentType, farmerId, imgDes);
      console.log({
        document_type: documentType,
        entity: "farmer",
        entity_1_id: farmerId,
        url: response.data.data.doc_key,
        document_information: imgDes,
      });
      console.log(documentEntries, filesCount);
      documentEntries.push({
        document_type: documentType,
        entity: "farmer",
        entity_1_id: farmerId,
        url: response.data.data.doc_key,
        document_information: imgDes,
      });
      setDocumentEntries([...documentEntries]);
      console.log(documentEntries.length, filesCount);
      if (documentEntries.length === filesCount) {
        postImages(navigate, documentEntries, farmerId);
      }
    } else {
      throw Error("Image not uploaded to server!");
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
    if (error.response.status === 413) {
      ToastersService.failureToast(
        error.response.data.error
          ? error.response.data.error
          : "File size limit exceeded!"
      );
    }
  }
};

const postImages = async (navigate, documentEntries, farmerId) => {
  try {
    const response = await instance({
      url: "/document",
      method: "POST",
      data: { document_entries: documentEntries },
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Images to db", response);
    if (response.status === 201) {
      ToastersService.successToast("File saved to db!");
      window.location.reload();
    } else {
      throw Error("Images not uploaded to db!");
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const FarmerItem1 = () => {
  const params = useParams(); console.log(params.farmerId)
  const navigate = useNavigate();
  const userData = useSelector((state) => state.user.userData);
  const [enableButton, setEnableButton] = useState(false);
  const [addFarmerTabs, setAddFarmerTabs] = useState([
    {
      count: 1,
      isSelected: true,
      name: "Personal Details",
    },
    {
      count: 2,
      isSelected: false,
      name: "Farm / Land",
    },
    {
      count: 3,
      isSelected: false,
      name: "Cattle",
    },
    {
      count: 4,
      isSelected: false,
      name: "Other Info",
    },
    {
      count: 5,
      isSelected: false,
      name: "Pictures",
    },
  ]);
  const [formStateTab0, setFormStateTab0] = useState(initialFormStateTab0);
  const [formStateTab1, setFormStateTab1] = useState(initialFormStateTab1);
  const [formStateTab2, setFormStateTab2] = useState(initialFormStateTab2);
  const [formStateTab3, setFormStateTab3] = useState(initialFormStateTab3);

  const [farmerDetailsLoader, setFarmerDetailsLoader] = useState(true);
  const [showCalendar, setShowCalendar] = useState(true);
  const [activeCattleList, setActiveCattleList] = useState([]);
  const [inactiveCattleList, setInctiveCattleList] = useState([]);

  const [farmerThumbnailImages, setFarmerThumbnailImages] = useState([]);
  const [farmerImages, setFarmerImages] = useState([]);
  const [farmImages, setFarmImages] = useState([]);
  const [aadharImages, setAadharImages] = useState([]);

  const [farmerThumbnailImages1, setFarmerThumbnailImages1] = useState([]);
  const [farmerImages1, setFarmerImages1] = useState([]);
  const [farmImages1, setFarmImages1] = useState([]);
  const [aadharImages1, setAadharImages1] = useState([]);

  let [updateFarmerThumbnailImage, setUpdateFarmerThumbnailImage] = useState(
    []
  );
  let [updateFarmerImages, setUpdateFarmerImages] = useState([]);
  let [updateDairyFarmImages, setUpdateDairyFarmImages] = useState([]);
  let [updateAadharImages, setUpdateAadharImages] = useState([]);
  let [image, setImage] = useState();
  let [imageDes, setImageDes] = useState("");
  const [documentEntries, setDocumentEntries] = useState([]);

  const [cancelReasonArr, setCancelReasonArr] = useState();
  let [inactiveCattleReqBody, setInactiveCattleReqBody] = useState({
    active_status: "",
    animal_id: [],
    note: "",
  });

  let [inactiveReasonOther, setInactiveReasonOther] = useState({
    active: false,
    value: "",
  });

  const [showConfirmAllModal, setShowConfirmAllModal] = useState(false);
  const handleConfirmAllModalClose = () => {
    setShowConfirmAllModal(false);
  };
  let [selectedActiveCattleArr, setSelectedActiveCattleArr] = useState([]);
  let [selectedInactiveCattleArr, setSelectedInactiveCattleArr] = useState([]);

  const [currentFarmerThumbnailImage, setCurrentFarmerThumbnailImage] =
    useState(0);
  const [isFarmerThumbnailViewerOpen, setIsFarmerThumbnailViewerOpen] =
    useState(false);
  const openFarmerThumbnailImageViewer = useCallback((index) => {
    setCurrentFarmerThumbnailImage(index);
    setIsFarmerThumbnailViewerOpen(true);
  }, []);
  const closeFarmerThumbnailImageViewer = () => {
    setCurrentFarmerThumbnailImage(0);
    setIsFarmerThumbnailViewerOpen(false);
  };

  const [currentFarmerImage, setCurrentFarmerImage] = useState(0);
  const [isFarmerViewerOpen, setIsFarmerViewerOpen] = useState(false);
  const openFarmerImageViewer = useCallback((index) => {
    setCurrentFarmerImage(index);
    setIsFarmerViewerOpen(true);
  }, []);
  const closeFarmerImageViewer = () => {
    setCurrentFarmerImage(0);
    setIsFarmerViewerOpen(false);
  };

  const [currentFarmImage, setCurrentFarmImage] = useState(0);
  const [isFarmViewerOpen, setIsFarmViewerOpen] = useState(false);
  const openFarmImageViewer = useCallback((index) => {
    setCurrentFarmImage(index);
    setIsFarmViewerOpen(true);
  }, []);
  const closeFarmImageViewer = () => {
    setCurrentFarmImage(0);
    setIsFarmViewerOpen(false);
  };

  const [currentAdhaarImage, setCurrentAdhaarImage] = useState(0);
  const [isAdhaarViewerOpen, setIsAdhaarViewerOpen] = useState(false);
  const openAdhaarImageViewer = useCallback((index) => {
    setCurrentAdhaarImage(index);
    setIsAdhaarViewerOpen(true);
  }, []);
  const closeAdhaarImageViewer = () => {
    setCurrentAdhaarImage(0);
    setIsAdhaarViewerOpen(false);
  };

  const [show, setShow] = useState(false);
  const [imageType, setImageType] = useState();
  const handleClose = () => setShow(false);
  const handleShow = (imageType) => {
    console.log(imageType);
    setImageType(imageType);
    setShow(true);
  };

  const [cattleListSwitch, setCattleListSwitch] = useState({
    active: true,
    inactive: false,
  });

  const [isPPU, setIsPPU] = useState(false);
  const [modal, contextHolder] = AtndModal.useModal();

  const getCattleListByFarmerId = useCallback(async () => {
    try {
      const response = await instance({
        url: "/animal?customer_id=" + params.farmerId,
        method: "GET",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log("Cattle list by farmer id", response);
      if (response.status === 200) {
        if (response.data.result === false) {
          tokenFailureHandler(response.data);
          navigate("/login");
        }
        response.data.data = response.data.data.filter(
          (item) => item.animal_id !== null
        );

        let tempActiveCattleList = [];
        let tempInactiveCattleList = [];
        for (let cattle of response.data.data) {
          if (cattle.active == 1000100001) {
            tempActiveCattleList.push(cattle);
            cattle.isActive = true;
          } else if (cattle.active == 1000100002) {
            cattle.isActive = false;
            tempInactiveCattleList.push(cattle);
          }
          cattle.isSelected = false;
        }

        console.log("Active cattle", tempActiveCattleList);
        console.log("In-active cattle", tempInactiveCattleList);
        setActiveCattleList(tempActiveCattleList);
        setInctiveCattleList(tempInactiveCattleList);
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  }, []);

  const temporaryExtraction = (incomingData, outgoingData, keyList) => {
    for (const key of keyList) {
      if (incomingData[key]) {
        if (key === "farmer_address_1") {
          if (typeof incomingData[key] === "object") {
            if ("ul" in incomingData[key]) {
              outgoingData[key] = incomingData[key]["ul"];
            } else if ("en" in incomingData[key]) {
              outgoingData[key] = incomingData[key]["en"];
            }
          } else if (typeof incomingData[key] === "string") {
            outgoingData[key] = incomingData[key];
          }
        } else {
          outgoingData[key] = incomingData[key];
        }
      }
    }
  };

  const temporaryExtraction2 = (incomingData, outgoingData, keyList) => {
    for (const key of keyList) {
      if (incomingData[key] && incomingData[key].value) {
        outgoingData[key] = incomingData[key].value;
      }
    }
  };

  const temporaryExtraction3 = (incomingData, outgoingData, keyList) => {
    for (const key of keyList) {
      if (incomingData[key] && incomingData[key].selected) {
        outgoingData[key] = incomingData[key].selected;
      }
    }
  };

  const temporaryExtraction4 = (incomingData, outgoingData, keyList) => {
    for (const key of keyList) {
      if (incomingData[key] && incomingData[key].value) {
        outgoingData[key] = parseFloat(incomingData[key].value);
      }
    }
  };

  const getFarmersDetailsById = useCallback(
    async (
      navigate,
      modFormStateTab0,
      modFormStateTab1,
      modFormStateTab2,
      modFormStateTab3,
      setFormStateTab0,
      setFormStateTab1,
      setFormStateTab2,
      setFormStateTab3
    ) => {
      try {
        setFarmerDetailsLoader(true);
        // const [
        //   typeOfDairyFarmsList,
        //   currentLoanList,
        //   foliageCropsList,
        //   farmerAssetsList,
        // ] = await getFormFieldConfigurationsAsLists(navigate);
        const [
          typeOfDairyFarmsList,
          currentLoanList,
          foliageCropsList,
          farmerAssetsList,
        ] = setReferenceValueFromRefData();
        const districtList = await getDistrictsListOnly(navigate, 1026);
        const stateList = await getStateLists(navigate);
        console.log("FI gFDBI 1, districtList = ", districtList);
        const response = await instance({
          url: "/customer/" + params.farmerId,
          method: "GET",
          headers: {
            token: localStorage.getItem("accessToken"),
          },
        });
        let modifiedFormStateTab0 = modFormStateTab0;
        console.log(response);
        if (response.status === 200) {
          if (response?.data?.customer_type_id === 1000220007) {
            setEnableButton(true);
            if (
              !farmerFormDefinitionTab0.visibleFormControls.includes(
                "farmerPan"
              )
            ) {
              farmerFormDefinitionTab0.visibleFormControls.push("farmerPan");
            } else {
              farmerFormDefinitionTab0.visibleFormControls =
                farmerFormDefinitionTab0.visibleFormControls.filter(
                  (item) => item !== "farmerPan"
                );
            }
          } else {
            farmerFormDefinitionTab0.visibleFormControls =
              farmerFormDefinitionTab0.visibleFormControls.filter(
                (item) => item !== "farmerPan"
              );
          }
          if (response.data.result === false) {
            tokenFailureHandler(response.data);
            navigate("/login");
          }
          setIsPPU(
            response.data.customer_id === "87ceaf39-48af-11ee-b459-b7de6701b862"
          );
          console.log("FI gFDBI 2, Farmer Details", response);

          const prunedResponse = {};
          const keyList = [
            "customer_name_l10n",
            "customer_visual_id",
            "mobile_number",
            "aadhaar_1",
            "farmer_state_1",
            "farmer_district_1",
            "farmer_taluka_1",
            "farmer_village_1",
            "farmer_address_1",
            "age_1",
            "dob_1",
            "farmer_pan_1",

            "type_of_dairy_farm_1",
            "monthly_labour_cost_1",
            "acreage_for_foliage_crops_1",
            "num_of_lab_used_for_cow_farm_1",
            "total_dairy_income_1",
            "foliage_crops_grown_1",
            "assets_owned_1",

            "total_num_of_buff_calves_1",
            "total_num_of_buffalo_1",
            "total_num_of_cow_1",
            "total_num_of_cow_calves_1",
            "average_price_of_cow_purchased_in_last_3_years_1",
            "average_price_of_cow_sold_in_last_3_years_1",
            "average_price_of_female_calf_purchased_in_last_3_years_1",
            "average_price_of_female_calves_sold_in_last_3_years_1",
            "total_number_of_cow_purchased_in_last_3_years_1",
            "total_number_of_cow_sold_in_last_3_years_1",
            "total_number_of_female_calves_purchased_in_last_3_years_1",
            "total_number_of_female_calves_sold_in_last_3_years_1",

            "current_loan_1",

            "total_number_of_adults_in_household_1",

            "total_income_1",
            "total_number_of_aged_dependents_1",
            "total_number_of_children_in_household_1",
            "total_emi_per_month_1",
          ];
          const keyList2 = [];

          const keyList3 = [];

          const keyList4 = [];
          temporaryExtraction(response.data, prunedResponse, keyList);
          temporaryExtraction2(response.data, prunedResponse, keyList2);
          temporaryExtraction3(response.data, prunedResponse, keyList3);
          temporaryExtraction4(response.data, prunedResponse, keyList4);
          console.log("FI gFDBI 2a, prunedResponse", prunedResponse);
          if (prunedResponse["farmer_taluka_1"]) {
            prunedResponse["farmer_taluk_1"] =
              prunedResponse["farmer_taluka_1"];
          }

          console.log(
            "FI gFDBI 3, prunedResponse = ",
            prunedResponse,
            ", modifiedFormStateTab0 = ",
            modifiedFormStateTab0
          );
          prunedResponse["farmer_state_1_list"] = stateList;
          prunedResponse["farmer_district_1_list"] = districtList;
          prunedResponse["current_loan_1_list"] = currentLoanList;
          prunedResponse["type_of_dairy_farm_1_list"] = typeOfDairyFarmsList;
          prunedResponse["foliage_crops_grown_1_list"] = foliageCropsList;
          prunedResponse["assets_owned_1_list"] = farmerAssetsList;

          if (prunedResponse["farmer_district_1"]) {
            const talukList = await getTaluksBasedOnDistrictListOnly(
              navigate,
              prunedResponse["farmer_district_1"][0]
            );
            prunedResponse["farmer_taluk_1_list"] = talukList;
          }
          console.log(
            "FI gFDBI 4, modifiedFormStateTab0 = ",
            modifiedFormStateTab0
          );
          if (prunedResponse["farmer_taluk_1"]) {
            const villageList = await getVillagesBasedOnTalukListOnly(
              navigate,
              prunedResponse["farmer_taluk_1"][0]
            );
            prunedResponse["farmer_village_1_list"] = villageList;
          }

          console.log(
            "FI gFDBI 5, modifiedFormStateTab0 = ",
            modifiedFormStateTab0
          );
          console.log("FI gFDBI 6, prunedResponse = ", prunedResponse);
          modifiedFormStateTab0 = assignDataToFormState(
            farmerFormDefinitionTab0,
            prunedResponse,
            modifiedFormStateTab0,
            setFormStateTab0
          );
          const modifiedFormStateTab1 = assignDataToFormState(
            farmerFormDefinitionTab1,
            prunedResponse,
            modFormStateTab1,
            setFormStateTab1
          );
          const modifiedFormStateTab2 = assignDataToFormState(
            farmerFormDefinitionTab2,
            prunedResponse,
            modFormStateTab2,
            setFormStateTab2
          );
          const modifiedFormStateTab3 = assignDataToFormState(
            farmerFormDefinitionTab3,
            prunedResponse,
            modFormStateTab3,
            setFormStateTab3
          );
          console.log(
            "FI gFDBI 7, modifiedFormStateTab0 = ",
            modifiedFormStateTab0
          );
          setFarmerDetailsLoader(false);

          if (response.data.files && response.data.files.length > 0) {
            for (let item of response.data.files) {
              item.fileType = await getFileType(item.document_id);
            }
            configureFarmerImages(response.data.files);
          }

          return modifiedFormStateTab0;
        }
      } catch (error) {
        console.log(error);
        setFarmerDetailsLoader(false);
        if (error.response.status === 401) {
          console.log("token expired");
          localStorage.removeItem("accessToken");
          localStorage.removeItem("resetToken");
          ToastersService.failureToast("Session Expired!");
          navigate("/login");
          window.location.reload();
        }
      }
    },
    []
  );

  const getFileType = async (docId) => {
    try {
      const response = await instance({
        url: "/media/" + docId,
        method: "GET",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log("getFileType", response);
      if (response.status === 200) {
        return response.headers["content-type"];
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  const changeActiveStatusHandler = async (cattleIds, actionType) => {
    const isEnableAction = actionType === "enable";
    const isDisableAction = actionType === "disable";
    const isInvalidFields =
      (isDisableAction &&
        (!inactiveCattleReqBody.animal_inactive_reason_1 ||
          inactiveCattleReqBody.animal_inactive_reason_1.trim() === "" ||
          !inactiveCattleReqBody.note ||
          inactiveCattleReqBody.note.trim() === "" ||
          (inactiveCattleReqBody.animal_inactive_reason_1 == "1002000004" &&
            (!inactiveCattleReqBody.animal_inactive_reason_other_1 ||
              inactiveCattleReqBody.animal_inactive_reason_other_1.trim() ===
              "")) ||
          !inactiveCattleReqBody.animal_inactive_date_1 ||
          inactiveCattleReqBody.animal_inactive_date_1.trim() === "")) ||
      (isEnableAction &&
        (!inactiveCattleReqBody.animal_active_reason_1 ||
          inactiveCattleReqBody.animal_active_reason_1 === "" ||
          inactiveCattleReqBody.note.trim() === "" ||
          (inactiveCattleReqBody.animal_active_reason_1 == "1002000004" &&
            (inactiveCattleReqBody?.animal_active_reason_other_1 ===
              undefined ||
              inactiveCattleReqBody?.animal_active_reason_other_1?.trim() ===
              "")) ||
          !inactiveCattleReqBody.animal_activation_date ||
          inactiveCattleReqBody.animal_activation_date?.trim() === ""));

    if (isInvalidFields) {
      ToastersService.failureToast("All fields are mandatory!");
      return;
    }

    try {
      const response = await instance({
        url: "/animal/change-status",
        method: "POST",
        data: {
          ...inactiveCattleReqBody,
          active_status: isEnableAction ? 1000100001 : 1000100002,
          animal_id: cattleIds,
        },
        headers: {
          "Content-Type": "application/json",
          token: localStorage.getItem("accessToken"),
        },
      });

      console.log("Change active status", response);

      if (response.status === 201 && response.data.result === true) {
        ToastersService.successToast("Status changed successfully!");

        selectedActiveCattleArr = [];
        selectedInactiveCattleArr = [];
        setSelectedActiveCattleArr([...selectedActiveCattleArr]);
        setSelectedInactiveCattleArr([...selectedInactiveCattleArr]);
        setShowConfirmAllModal(false);

        setInactiveCattleReqBody({
          active_status: "",
          animal_id: [],
          note: "",
        });
        setInactiveReasonOther({ active: false });

        getCattleListByFarmerId();
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  useEffect(() => {
    const initializeForm = async () => {
      getFarmersDetailsById(
        navigate,
        formStateTab0,
        formStateTab1,
        formStateTab2,
        formStateTab3,
        setFormStateTab0,
        setFormStateTab1,
        setFormStateTab2,
        setFormStateTab3
      );
      getCattleListByFarmerId();
    };
    initializeForm();
    localStorage.removeItem("taskNavigation");
  }, []);

  const configureFarmerImages = (images) => {
    console.log("farmer images1", images);
    for (let image of images) {
      if (image.document_type === "farmer_photo_1") {
        setFarmerImages1((farmerImages) => [
          ...farmerImages,
          BASE_URL + "/media/" + image.document_id,
        ]);
        setFarmerImages((farmerImages) => [...farmerImages, image]);
      } else if (image.document_type === "farm_photo_1") {
        setFarmImages1((farmImages) => [
          ...farmImages,
          BASE_URL + "/media/" + image.document_id,
        ]);
        setFarmImages((farmImages) => [...farmImages, image]);
      } else if (image.document_type === "farmer_aadhaar_photo_1") {
        setAadharImages1((aadharImages) => [
          ...aadharImages,
          BASE_URL + "/media/" + image.document_id,
        ]);
        setAadharImages((aadharImages) => [...aadharImages, image]);
      } else if (image.document_type === "customer_thumbnail_photo_1") {
        setFarmerThumbnailImages1((farmerThumbnailImages) => [
          ...farmerThumbnailImages,
          BASE_URL + "/media/" + image.document_id,
        ]);
        setFarmerThumbnailImages((farmerThumbnailImages) => [
          ...farmerThumbnailImages,
          image,
        ]);
      }
    }
    console.log("farmer images2", images);
  };

  const farmerImagesUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setUpdateFarmerImages([...updateFarmerImages, fileItem]);
  };

  const removeFarmerImageHandler = (image) => {
    for (let item of updateFarmerImages) {
      if (item.file.name === image) {
        updateFarmerImages = updateFarmerImages.filter(
          (i) => i.file.name !== image
        );
      }
    }
    setUpdateFarmerImages([...updateFarmerImages]);
  };

  const dairyFarmImagesUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setUpdateDairyFarmImages([...updateDairyFarmImages, fileItem]);
  };

  const removeDairyFarmImageHandler = (image) => {
    for (let item of updateDairyFarmImages) {
      if (item.file.name === image) {
        updateDairyFarmImages = updateDairyFarmImages.filter(
          (i) => i.file.name !== image
        );
      }
    }
    setUpdateDairyFarmImages([...updateDairyFarmImages]);
  };

  const aadharImagesUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setUpdateAadharImages([...updateAadharImages, fileItem]);
  };

  const removeAadharImageHandler = (image) => {
    console.log(image);
    for (let item of updateAadharImages) {
      if (item.file.name === image) {
        updateAadharImages = updateAadharImages.filter(
          (i) => i.file.name !== image
        );
      }
    }
    setUpdateAadharImages([...updateAadharImages]);
  };

  const farmerThumbnailUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setUpdateFarmerThumbnailImage([...updateFarmerThumbnailImage, fileItem]);
  };

  const removeThumbnailImageHandler = (image) => {
    console.log(image);
    for (let item of updateFarmerThumbnailImage) {
      if (item.file.name === image) {
        updateFarmerThumbnailImage = updateFarmerThumbnailImage.filter(
          (i) => i.file.name !== image
        );
      }
    }
    setUpdateFarmerThumbnailImage([...updateFarmerThumbnailImage]);
  };

  const imageUploadHandler = async (event) => {
    console.log(event.target.files[0]);
    if (
      event.target.files[0].type.split("/")[0] == "image" ||
      event.target.files[0].type == "application/pdf" ||
      event.target.files[0].type.split("/")[0] == "video"
    ) {
      setImage(event.target.files[0]);
    } else {
      ToastersService.failureToast("Invalid file type!");
    }
  };

  const removeImageHandler = () => {
    image = undefined;
    console.log("Image", image);
    setImage(image);
  };

  const imageDescriptionChangeHandler = (event) => {
    console.log(event.target.value);
    setImageDes(event.target.value);
  };

  const saveImageHandler = () => {
    console.log(image, imageDes, imageType);
    if (image) {
      if (imageType === "thumbnail") {
        farmerThumbnailUploadHandler(image, imageDes);
      } else if (imageType === "farmer") {
        farmerImagesUploadHandler(image, imageDes);
      } else if (imageType === "farm") {
        dairyFarmImagesUploadHandler(image, imageDes);
      } else if (imageType === "adhaar") {
        aadharImagesUploadHandler(image, imageDes);
      }
      setImage(undefined);
      setImageDes("");
      handleClose();
    } else {
      ToastersService.failureToast("Please add file!");
    }
  };

  const onChangeTab = (tabCount) => {
    console.log(tabCount);
    for (let item of addFarmerTabs) {
      if (item.count === tabCount) {
        item.isSelected = true;
      } else {
        item.isSelected = false;
      }
    }
    setAddFarmerTabs([...addFarmerTabs]);
  };

  const addCattleHandler = () => {
    navigate("/farmer/" + params.farmerId + "/cattle/add-cattle");
  };

  const navigateToCattleDetails = (cattleId) => {
    navigate("/farmer/" + params.farmerId + "/cattle/" + cattleId);
  };

  const cancelReasonSelectHandler = (event) => {
    console.log(event.target.value);
    delete inactiveCattleReqBody.animal_active_reason_1;
    delete inactiveCattleReqBody.animal_inactive_reason_other_1;
    delete inactiveCattleReqBody.animal_active_reason_other_1;

    if (event.target.value == 1002000004) {
      console.log("Found!");
      inactiveReasonOther = { ...inactiveReasonOther, active: true };
      setInactiveReasonOther({ ...inactiveReasonOther });
    } else {
      console.log("Not Found!");
      inactiveReasonOther = { ...inactiveReasonOther, active: false };
      setInactiveReasonOther({ ...inactiveReasonOther });
    }

    setInactiveCattleReqBody({
      ...inactiveCattleReqBody,
      animal_inactive_reason_1: event.target.value,
    });
  };

  const otherReasonTextHandler = (event) => {
    delete inactiveCattleReqBody.animal_active_reason_other_1;
    setInactiveCattleReqBody({
      ...inactiveCattleReqBody,
      animal_inactive_reason_other_1: event.target.value,
    });
  };
  const activeOtherReasonTextHandler = (event) => {
    delete inactiveCattleReqBody.animal_inactive_reason_other_1;
    setInactiveCattleReqBody({
      ...inactiveCattleReqBody,
      animal_active_reason_other_1: event.target.value,
    });
  };

  const cancelDescriptionHandler = (event) => {
    setInactiveCattleReqBody({
      ...inactiveCattleReqBody,
      note: event.target.value,
    });
  };

  const deactivationDateHandler = (event) => {
    console.log(event);

    setInactiveCattleReqBody({
      ...inactiveCattleReqBody,
      animal_inactive_date_1: event.target.value,
    });
  };

  const activationDateHandler = (event) => {
    console.log(event);
    delete inactiveCattleReqBody.animal_inactive_reason_1;

    setInactiveCattleReqBody({
      ...inactiveCattleReqBody,
      animal_activation_date: event.target.value,
      animal_active_reason_1: 1002000004,
    });
  };
  const selectActiveCattleHandler = (event) => {
    console.log(event.target.value, event.target.checked);

    if (event.target.checked == true) {
      selectedActiveCattleArr.push(event.target.value);
    } else if (event.target.checked == false) {
      selectedActiveCattleArr = selectedActiveCattleArr.filter(
        (item) => item !== event.target.value
      );
    }

    for (let cattle of activeCattleList) {
      if (cattle.animal_id == event.target.value) {
        if (event.target.checked == true) {
          cattle.isSelected = true;
        } else if (event.target.checked == false) {
          cattle.isSelected = false;
        }
      }
    }

    setSelectedActiveCattleArr([...selectedActiveCattleArr]);
    console.log("selectedActiveCattleArr", selectedActiveCattleArr);
  };

  const selectAllActiveCattleHandler = (event) => {
    selectedActiveCattleArr = [];
    if (event.target.checked == true) {
      for (let cattle of activeCattleList) {
        if (cattle.isActive == true) {
          cattle.isSelected = true;
          selectedActiveCattleArr.push(cattle.animal_id);
        }
      }
    } else if (event.target.checked == false) {
      for (let cattle of activeCattleList) {
        if (cattle.isActive == true) {
          cattle.isSelected = false;
        }
      }
    }
    setSelectedActiveCattleArr([...selectedActiveCattleArr]);
    console.log("selectedActiveCattleArr", selectedActiveCattleArr);
  };

  const selectInactiveCattleHandler = (event) => {
    console.log(event.target.value, event.target.checked);

    if (event.target.checked == true) {
      selectedInactiveCattleArr.push(event.target.value);
    } else if (event.target.checked == false) {
      selectedInactiveCattleArr = selectedInactiveCattleArr.filter(
        (item) => item !== event.target.value
      );
    }

    for (let cattle of inactiveCattleList) {
      if (cattle.animal_id == event.target.value) {
        if (event.target.checked == true) {
          cattle.isSelected = true;
        } else if (event.target.checked == false) {
          cattle.isSelected = false;
        }
      }
    }

    setSelectedInactiveCattleArr([...selectedInactiveCattleArr]);
    console.log("selectedInactiveCattleArr", selectedInactiveCattleArr);
  };

  const selectAllInactiveCattleHandler = (event) => {
    selectedInactiveCattleArr = [];
    if (event.target.checked == true) {
      for (let cattle of inactiveCattleList) {
        cattle.isSelected = true;
        selectedInactiveCattleArr.push(cattle.animal_id);
      }
    } else if (event.target.checked == false) {
      for (let cattle of inactiveCattleList) {
        cattle.isSelected = false;
      }
    }
    setSelectedInactiveCattleArr([...selectedInactiveCattleArr]);
    console.log("selectedInactiveCattleArr", selectedInactiveCattleArr);
  };

  const cattleListSwitchHandler = (switchType) => {
    console.log(switchType);
    if (switchType == "active") {
      let tempCattleListSwitch = {
        active: true,
        inactive: false,
      };
      setCattleListSwitch(tempCattleListSwitch);
    } else if (switchType == "inactive") {
      let tempCattleListSwitch = {
        active: false,
        inactive: true,
      };
      setCattleListSwitch(tempCattleListSwitch);
    }

    // reset selected values
    setSelectedActiveCattleArr([]);
    setSelectedInactiveCattleArr([]);
    for (let cattle of activeCattleList) {
      cattle.isSelected = false;
    }
    for (let cattle of inactiveCattleList) {
      cattle.isSelected = false;
    }
  };

  const addFarmerTaskHandler = () => {
    if (activeCattleList.length === 0) {
      modal.warning({
        title: "Can't create a task !",
        content: "You have no active cattles to create a task.",
      });
    } else {
      navigate("/farmer/" + params.farmerId + "/tasks/add-task");
    }
  };

  const onSelectChangeActive = (newSelectedRowKeys) => {
    console.log("selectedRowKeys changed active: ", newSelectedRowKeys);
    setSelectedActiveCattleArr(newSelectedRowKeys);
  };

  const onSelectChangeInactive = (newSelectedRowKeys) => {
    console.log("selectedRowKeys changed inactive: ", newSelectedRowKeys);
    setSelectedInactiveCattleArr(newSelectedRowKeys);
  };

  const rowSelectionActive = {
    selectedRowKeys: selectedActiveCattleArr,
    onChange: onSelectChangeActive,
  };

  const rowSelectionInactive = {
    selectedRowKeys: selectedInactiveCattleArr,
    onChange: onSelectChangeInactive,
  };

  const tableDataActive = activeCattleList?.map((cattle) => ({
    key: cattle?.animal_id,
    visualId: cattle?.animal_visual_id,
    name: cattle?.animal_name_1,
    earTag: cattle?.ear_tag_1,
    type: cattle?.animal_type?.en || cattle?.animal_type?.ul,
    breed: cattle?.animal_breed_1?.en || cattle?.animal_breed_1?.ul,
    subscriptionType:
      cattle?.subscription_plan_1?.en || cattle?.subscription_plan_1?.ul,
    subscriptionDate: cattle?.healthcare_plan_subscription_date_1,
  }));

  const tableDataInactive = inactiveCattleList?.map((cattle) => ({
    key: cattle?.animal_id,
    visualId: cattle?.animal_visual_id,
    name: cattle?.animal_name_1,
    earTag: cattle?.ear_tag_1,
    type: cattle?.animal_type?.en || cattle?.animal_type?.ul,
    breed: cattle?.animal_breed_1?.en || cattle?.animal_breed_1?.ul,
    subscriptionType:
      cattle?.subscription_plan_1?.en || cattle?.subscription_plan_1?.ul,
    subscriptionDate: cattle?.healthcare_plan_subscription_date_1,
  }));

  const tableColumnsActive = [
    {
      title: "Visual Id",
      dataIndex: "visualId",
      key: "visualId",
      width: 250,
      render: (text, record) => (
        <span
          className="farmers__farmer-name"
          onClick={() => navigateToCattleDetails(record?.key)}
        >
          {text || "-"}
        </span>
      ),
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      align: "center",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Ear Tag",
      dataIndex: "earTag",
      key: "earTag",
      align: "center",
      width: 200,
      render: (text, record) => (
        <span
          className="farmers__farmer-name"
          onClick={() => navigateToCattleDetails(record?.key)}
        >
          {text || "-"}
        </span>
      ),
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      align: "center",
      width: 150,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Breed",
      dataIndex: "breed",
      key: "breed",
      align: "center",
      width: 150,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Subscription Type",
      dataIndex: "subscriptionType",
      key: "subscriptionType",
      align: "center",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Subscription Date",
      dataIndex: "subscriptionDate",
      key: "subscriptionDate",
      align: "center",
      width: 200,
      render: (text) =>
        text !== null && text !== undefined ? (
          <Moment format="DD MMM, YYYY">{text}</Moment>
        ) : (
          "-"
        ),
    },
  ];

  const tableColumnsInactive = [
    {
      title: "Visual Id",
      dataIndex: "visualId",
      key: "visualId",
      width: 250,
      render: (text, record) => (
        <span
          className="farmers__farmer-name"
          onClick={() => navigateToCattleDetails(record?.key)}
        >
          {text || "-"}
        </span>
      ),
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      align: "center",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Ear Tag",
      dataIndex: "earTag",
      key: "earTag",
      align: "center",
      width: 200,
      render: (text, record) => (
        <span
          className="farmers__farmer-name"
          onClick={() => navigateToCattleDetails(record?.key)}
        >
          {text || "-"}
        </span>
      ),
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      align: "center",
      width: 150,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Breed",
      dataIndex: "breed",
      key: "breed",
      align: "center",
      width: 150,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Subscription Type",
      dataIndex: "subscriptionType",
      key: "subscriptionType",
      align: "center",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Subscription Date",
      dataIndex: "subscriptionDate",
      key: "subscriptionDate",
      align: "center",
      width: 200,
      render: (text) =>
        text !== null && text !== undefined ? (
          <Moment format="DD MMM, YYYY">{text}</Moment>
        ) : (
          "-"
        ),
    },
  ];

  return (
    <>
      {contextHolder}
      {farmerDetailsLoader == true ? (
        <div
          style={{
            margin: "2rem 0",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Loader />
        </div>
      ) : (
        <>
          <div className="mb-4">
            <Link className="farmer-details__back-btn" to="/farmer">
              <i className="fa fa-angle-double-left" aria-hidden="true"></i> Go
              back
            </Link>

            <p
              className="mb-0 mt-3"
              style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
            >
              Farmers / Farmer Details
            </p>
          </div>

          <div className="add-farmer__floating-menu mt-3">
            {addFarmerTabs.map((tab) => (
              <div
                key={tab.count}
                className={
                  tab.isSelected === true
                    ? "add-farmer__tab-selected"
                    : "add-farmer__tab-unselected"
                }
                onClick={() => onChangeTab(tab.count)}
              >
                <div>{tab.name}</div>
              </div>
            ))}
          </div>

          {/* Page 1 */}
          {addFarmerTabs[0].isSelected === true && (
            <>
              <KrushalOCForm
                formState={formStateTab0}
                setFormState={setFormStateTab0}
                formDefinition={farmerFormDefinitionTab0}
              />
            </>
          )}

          {/* Page 2 */}
          {addFarmerTabs[1].isSelected === true && (
            <>
              <KrushalOCForm
                formState={formStateTab1}
                setFormState={setFormStateTab1}
                formDefinition={farmerFormDefinitionTab1}
              />
            </>
          )}

          {/* Page 3 */}
          {addFarmerTabs[2].isSelected === true && (
            <>
              <KrushalOCForm
                formState={formStateTab2}
                setFormState={setFormStateTab2}
                formDefinition={farmerFormDefinitionTab2}
              />
            </>
          )}

          {/* Page 4 */}
          {addFarmerTabs[3].isSelected === true && (
            <>
              <KrushalOCForm
                formState={formStateTab3}
                setFormState={setFormStateTab3}
                formDefinition={farmerFormDefinitionTab3}
              />
            </>
          )}

          {/* Page 5 */}
          {addFarmerTabs[4].isSelected === true && (
            <>
              <Row>
                <Col lg={4} md={6} sm={12}>
                  <small>Farmer thumbnail image</small>

                  {farmerThumbnailImages.length > 0 ? (
                    <div>
                      <Row className="mt-3">
                        {farmerThumbnailImages.map((doc) => (
                          <Col lg={6} md={6} sm={12}>
                            {doc.fileType ? (
                              <>
                                {/* Image */}
                                {doc?.fileType?.split("/")[0] == "image" && (
                                  <div>
                                    <p>Image</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                      >
                                        <img
                                          src={
                                            BASE_URL +
                                            "/media/" +
                                            doc.document_id
                                          }
                                          className="new-img"
                                          onClick={() =>
                                            openFarmerThumbnailImageViewer(0)
                                          }
                                          alt=""
                                        />
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                          doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                    {isFarmerThumbnailViewerOpen && (
                                      <ImageViewer
                                        src={[
                                          BASE_URL +
                                          "/media/" +
                                          doc.document_id,
                                        ]}
                                        currentIndex={
                                          currentFarmerThumbnailImage
                                        }
                                        onClose={
                                          closeFarmerThumbnailImageViewer
                                        }
                                        backgroundStyle={{
                                          backgroundColor: "rgba(0, 0, 0, 0.9)",
                                          zIndex: 10,
                                        }}
                                        closeOnClickOutside={true}
                                      />
                                    )}
                                  </div>
                                )}

                                {/* PDF */}
                                {doc?.fileType == "application/pdf" && (
                                  <div>
                                    <p>PDF</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                        onClick={() =>
                                          window.open(
                                            BASE_URL +
                                            "/media/" +
                                            doc.document_id
                                          )
                                        }
                                      >
                                        {/* <PDFViewer
                                          style={{ height: "100%" }}
                                          document={{
                                            url: `${BASE_URL}/media/${doc.document_id}`,
                                          }}
                                        /> */}
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                          doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}

                                {/* Video */}
                                {doc?.fileType?.split("/")[0] == "video" && (
                                  <div>
                                    <p>Video</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                      >
                                        <video className="new-img" controls>
                                          <source
                                            src={
                                              BASE_URL +
                                              "/media/" +
                                              doc.document_id
                                            }
                                            type="video/mp4"
                                          />
                                        </video>
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                          doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </>
                            ) : (
                              <FileNotFound />
                            )}
                          </Col>
                        ))}
                      </Row>
                    </div>
                  ) : (
                    <p style={{ color: "#4c4c4c" }}>
                      <i
                        className="fa fa-exclamation-triangle"
                        aria-hidden="true"
                      ></i>{" "}
                      No files found!
                    </p>
                  )}

                  <button
                    className="images-btn"
                    onClick={() => handleShow("thumbnail")}
                  >
                    Add an image
                  </button>
                  <Row>
                    {updateFarmerThumbnailImage.map((item) => (
                      <Col
                        lg={6}
                        md={6}
                        sm={12}
                        xs={12}
                        key={item.file.lastModified}
                      >
                        <div className="add-task__img-div">
                          <div
                            className="add-farmer__remove-img-btn"
                            style={{
                              fontSize: "11px",
                              backgroundColor: "red",
                            }}
                            onClick={() =>
                              removeThumbnailImageHandler(item.file.name)
                            }
                          >
                            <i className="fa fa-trash"></i>
                          </div>
                          <div
                            style={{
                              width: "100%",
                              height: "150px",
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            <div
                              style={{
                                width: "150px",
                                height: "150px",
                                marginRight: "1rem",
                                borderRight: "1px solid silver",
                              }}
                            >
                              {/* Image */}
                              {item.file.type.split("/")[0] == "image" && (
                                <img
                                  src={URL.createObjectURL(item.file)}
                                  alt=""
                                  className="new-img"
                                />
                              )}
                              {/* Document */}
                              {item.file.type == "application/pdf" && (
                                <div className="selected-file">
                                  <div className="text-center">
                                    <div>
                                      <i
                                        className="fa fa-file"
                                        aria-hidden="true"
                                      ></i>
                                    </div>
                                    <div style={{ fontSize: "12px" }}>
                                      {item.file.name}
                                    </div>
                                  </div>
                                </div>
                              )}
                              {/* Video */}
                              {item.file.type.split("/")[0] == "video" && (
                                <video className="new-img" controls>
                                  <source
                                    src={URL.createObjectURL(item.file)}
                                    type="video/mp4"
                                  />
                                </video>
                              )}
                            </div>
                            <div>
                              <small>
                                <i className="fa fa-file"></i> {item.file.type}
                              </small>
                              <p
                                className="mt-2 mb-0"
                                style={{ fontSize: "14px" }}
                              >
                                {item.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      </Col>
                    ))}
                  </Row>
                </Col>
                <Col lg={4} md={6} sm={12}>
                  <small>Pictures of the farmer</small>

                  {farmerImages.length > 0 ? (
                    <div>
                      <Row className="mt-3">
                        {farmerImages.map((doc, index) => (
                          <Col lg={6} md={6} sm={12}>
                            {doc.fileType ? (
                              <>
                                {/* Image */}
                                {doc?.fileType?.split("/")[0] == "image" && (
                                  <div>
                                    <p>Image</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                      >
                                        <img
                                          src={
                                            BASE_URL +
                                            "/media/" +
                                            doc.document_id
                                          }
                                          className="new-img"
                                          onClick={() =>
                                            openFarmerImageViewer(index)
                                          }
                                          alt=""
                                        />
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                          doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                    {isFarmerViewerOpen && (
                                      <ImageViewer
                                        src={farmerImages1}
                                        currentIndex={currentFarmerImage}
                                        onClose={closeFarmerImageViewer}
                                        disableScroll={false}
                                        backgroundStyle={{
                                          backgroundColor: "rgba(0,0,0,0.9)",
                                          zIndex: 10,
                                        }}
                                        closeOnClickOutside={true}
                                      />
                                    )}
                                  </div>
                                )}

                                {/* PDF */}
                                {doc?.fileType == "application/pdf" && (
                                  <div>
                                    <p>PDF</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                        onClick={() =>
                                          window.open(
                                            BASE_URL +
                                            "/media/" +
                                            doc.document_id
                                          )
                                        }
                                      >
                                        {/* <PDFViewer
                                          style={{ height: "100%" }}
                                          document={{
                                            url: `${BASE_URL}/media/${doc.document_id}`,
                                          }}
                                        /> */}
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                          doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}

                                {/* Video */}
                                {doc?.fileType?.split("/")[0] == "video" && (
                                  <div>
                                    <p>Video</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                      >
                                        <video className="new-img" controls>
                                          <source
                                            src={
                                              BASE_URL +
                                              "/media/" +
                                              doc.document_id
                                            }
                                            type="video/mp4"
                                          />
                                        </video>
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                          doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </>
                            ) : (
                              <FileNotFound />
                            )}
                          </Col>
                        ))}
                      </Row>
                    </div>
                  ) : (
                    <p className="my-2" style={{ color: "#4c4c4c" }}>
                      <i
                        className="fa fa-exclamation-triangle"
                        aria-hidden="true"
                      ></i>{" "}
                      No files found!
                    </p>
                  )}

                  <button
                    className="images-btn"
                    onClick={() => handleShow("farmer")}
                  >
                    Add images or videos
                  </button>
                  <Row>
                    {updateFarmerImages.map((item) => (
                      <Col
                        lg={6}
                        md={6}
                        sm={12}
                        xs={12}
                        key={item.file.lastModified}
                      >
                        <div className="add-task__img-div">
                          <div
                            className="add-farmer__remove-img-btn"
                            style={{
                              fontSize: "11px",
                              backgroundColor: "red",
                            }}
                            onClick={() =>
                              removeFarmerImageHandler(item.file.name)
                            }
                          >
                            <i className="fa fa-trash"></i>
                          </div>
                          <div
                            style={{
                              width: "100%",
                              height: "150px",
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            <div
                              style={{
                                width: "150px",
                                height: "150px",
                                marginRight: "1rem",
                                borderRight: "1px solid silver",
                              }}
                            >
                              {/* Image */}
                              {item.file.type.split("/")[0] == "image" && (
                                <img
                                  src={URL.createObjectURL(item.file)}
                                  alt=""
                                  className="new-img"
                                />
                              )}
                              {/* Document */}
                              {item.file.type == "application/pdf" && (
                                <div className="selected-file">
                                  <div className="text-center">
                                    <div>
                                      <i
                                        className="fa fa-file"
                                        aria-hidden="true"
                                      ></i>
                                    </div>
                                    <div style={{ fontSize: "12px" }}>
                                      {item.file.name}
                                    </div>
                                  </div>
                                </div>
                              )}
                              {/* Video */}
                              {item.file.type.split("/")[0] == "video" && (
                                <video className="new-img" controls>
                                  <source
                                    src={URL.createObjectURL(item.file)}
                                    type="video/mp4"
                                  />
                                </video>
                              )}
                            </div>
                            <div>
                              <small>
                                <i className="fa fa-file"></i> {item.file.type}
                              </small>
                              <p
                                className="mt-2 mb-0"
                                style={{ fontSize: "14px" }}
                              >
                                {item.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      </Col>
                    ))}
                  </Row>
                </Col>
                <Col lg={4} md={6} sm={12}>
                  <small>Pictures of the cow farm</small>

                  {farmImages.length > 0 ? (
                    <div>
                      <Row className="mt-3">
                        {farmImages.map((doc, index) => (
                          <Col lg={6} md={6} sm={12}>
                            {doc.fileType ? (
                              <>
                                {/* Image */}
                                {doc?.fileType?.split("/")[0] == "image" && (
                                  <div>
                                    <p>Image</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                      >
                                        <img
                                          src={
                                            BASE_URL +
                                            "/media/" +
                                            doc.document_id
                                          }
                                          className="new-img"
                                          onClick={() => {
                                            console.log("==========================", BASE_URL +
                                              "/media/" +
                                              doc.document_id);
                                            openFarmImageViewer(index)
                                          }
                                          }
                                          alt=""
                                        />
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                          doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                    {isFarmViewerOpen && (
                                      <ImageViewer
                                        src={farmImages1}
                                        currentIndex={currentFarmImage}
                                        onClose={closeFarmImageViewer}
                                        disableScroll={false}
                                        backgroundStyle={{
                                          backgroundColor: "rgba(0,0,0,0.7)",
                                          zIndex: 10,
                                        }}
                                        closeOnClickOutside={true}
                                      />
                                    )}
                                  </div>
                                )}

                                {/* PDF */}
                                {doc?.fileType == "application/pdf" && (
                                  <div>
                                    <p>PDF</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                        onClick={() =>
                                          window.open(
                                            BASE_URL +
                                            "/media/" +
                                            doc.document_id
                                          )
                                        }
                                      >
                                        {/* <PDFViewer
                                          style={{ height: "100%" }}
                                          document={{
                                            url: `${BASE_URL}/media/${doc.document_id}`,
                                          }}
                                        /> */}
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                          doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}

                                {/* Video */}
                                {doc?.fileType?.split("/")[0] == "video" && (
                                  <div>
                                    <p>Video</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                      >
                                        <video className="new-img" controls>
                                          <source
                                            src={
                                              BASE_URL +
                                              "/media/" +
                                              doc.document_id
                                            }
                                            type="video/mp4"
                                          />
                                        </video>
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                          doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </>
                            ) : (
                              <FileNotFound />
                            )}
                          </Col>
                        ))}
                      </Row>
                    </div>
                  ) : (
                    <p className="my-2" style={{ color: "#4c4c4c" }}>
                      <i
                        className="fa fa-exclamation-triangle"
                        aria-hidden="true"
                      ></i>{" "}
                      No files found!
                    </p>
                  )}

                  <button
                    className="images-btn"
                    onClick={() => handleShow("farm")}
                  >
                    Add images or videos
                  </button>

                  <Row>
                    {updateDairyFarmImages.map((item) => (
                      <Col
                        lg={6}
                        md={6}
                        sm={12}
                        xs={12}
                        key={item.file.lastModified}
                      >
                        <div className="add-task__img-div">
                          <div
                            className="add-farmer__remove-img-btn"
                            style={{
                              fontSize: "11px",
                              backgroundColor: "red",
                            }}
                            onClick={() =>
                              removeDairyFarmImageHandler(item.file.name)
                            }
                          >
                            <i className="fa fa-trash"></i>
                          </div>
                          <div
                            style={{
                              width: "100%",
                              height: "150px",
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            <div
                              style={{
                                width: "150px",
                                height: "150px",
                                marginRight: "1rem",
                                borderRight: "1px solid silver",
                              }}
                            >
                              {/* Image */}
                              {item.file.type.split("/")[0] == "image" && (
                                <img
                                  src={URL.createObjectURL(item.file)}
                                  alt=""
                                  className="new-img"
                                />
                              )}
                              {/* Document */}
                              {item.file.type == "application/pdf" && (
                                <div className="selected-file">
                                  <div className="text-center">
                                    <div>
                                      <i
                                        className="fa fa-file"
                                        aria-hidden="true"
                                      ></i>
                                    </div>
                                    <div style={{ fontSize: "12px" }}>
                                      {item.file.name}
                                    </div>
                                  </div>
                                </div>
                              )}
                              {/* Video */}
                              {item.file.type.split("/")[0] == "video" && (
                                <video className="new-img" controls>
                                  <source
                                    src={URL.createObjectURL(item.file)}
                                    type="video/mp4"
                                  />
                                </video>
                              )}
                            </div>
                            <div>
                              <small>
                                <i className="fa fa-file"></i> {item.file.type}
                              </small>
                              <p
                                className="mt-2 mb-0"
                                style={{ fontSize: "14px" }}
                              >
                                {item.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      </Col>
                    ))}
                  </Row>
                </Col>
                <Col lg={4} md={6} sm={12}>
                  <small>Scan copy of Aadhar card from both sides</small>

                  {aadharImages.length > 0 ? (
                    <div>
                      <Row className="mt-3">
                        {aadharImages.map((doc, index) => (
                          <Col lg={6} md={6} sm={12}>
                            {doc.fileType ? (
                              <>
                                {/* Image */}
                                {doc?.fileType?.split("/")[0] == "image" && (
                                  <div>
                                    <p>Image</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                      >
                                        <img
                                          src={
                                            BASE_URL +
                                            "/media/" +
                                            doc.document_id
                                          }
                                          className="new-img"
                                          onClick={() =>
                                            openAdhaarImageViewer(index)
                                          }
                                          alt=""
                                        />
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                          doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                    {isAdhaarViewerOpen && (
                                      <ImageViewer
                                        src={aadharImages1}
                                        currentIndex={currentAdhaarImage}
                                        onClose={closeAdhaarImageViewer}
                                        disableScroll={false}
                                        backgroundStyle={{
                                          backgroundColor: "rgba(0,0,0,0.7)",
                                          zIndex: 10,
                                        }}
                                        closeOnClickOutside={true}
                                      />
                                    )}
                                  </div>
                                )}

                                {/* PDF */}
                                {doc?.fileType == "application/pdf" && (
                                  <div>
                                    <p>PDF</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                        onClick={() =>
                                          window.open(
                                            BASE_URL +
                                            "/media/" +
                                            doc.document_id
                                          )
                                        }
                                      >
                                        {/* <PDFViewer
                                          style={{ height: "100%" }}
                                          document={{
                                            url: `${BASE_URL}/media/${doc.document_id}`,
                                          }}
                                        /> */}
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                          doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}

                                {/* Video */}
                                {doc?.fileType?.split("/")[0] == "video" && (
                                  <div>
                                    <p>Video</p>
                                    <div
                                      className="disease-img__div"
                                      key={doc.document_id}
                                    >
                                      <div
                                        className="farmer-item__img-div"
                                        style={{
                                          borderRight: "1px solid silver",
                                        }}
                                      >
                                        <video className="new-img" controls>
                                          <source
                                            src={
                                              BASE_URL +
                                              "/media/" +
                                              doc.document_id
                                            }
                                            type="video/mp4"
                                          />
                                        </video>
                                      </div>
                                      <div>
                                        {!doc.document_information
                                          .description ||
                                          doc.document_information.description.trim() ===
                                          "" ? (
                                          <p
                                            style={{
                                              color: "silver",
                                              fontSize: "12px",
                                            }}
                                          >
                                            <i
                                              className="fa fa-exclamation-triangle"
                                              aria-hidden="true"
                                            ></i>{" "}
                                            No description!
                                          </p>
                                        ) : (
                                          <div>
                                            <small>Description:</small>
                                            <p>
                                              {
                                                doc.document_information
                                                  .description
                                              }
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </>
                            ) : (
                              <FileNotFound />
                            )}
                          </Col>
                        ))}
                      </Row>
                    </div>
                  ) : (
                    <p className="my-2" style={{ color: "#4c4c4c" }}>
                      <i
                        className="fa fa-exclamation-triangle"
                        aria-hidden="true"
                      ></i>{" "}
                      No files found!
                    </p>
                  )}

                  <button
                    className="images-btn"
                    onClick={() => handleShow("adhaar")}
                  >
                    Add images or PDF
                  </button>
                  <Row>
                    {updateAadharImages.map((item) => (
                      <Col
                        lg={6}
                        md={6}
                        sm={12}
                        xs={12}
                        key={item.file.lastModified}
                      >
                        <div className="add-task__img-div">
                          <div
                            className="add-farmer__remove-img-btn"
                            style={{
                              fontSize: "11px",
                              backgroundColor: "red",
                            }}
                            onClick={() =>
                              removeAadharImageHandler(item.file.name)
                            }
                          >
                            <i className="fa fa-trash"></i>
                          </div>
                          <div
                            style={{
                              width: "100%",
                              height: "150px",
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            <div
                              style={{
                                width: "150px",
                                height: "150px",
                                marginRight: "1rem",
                                borderRight: "1px solid silver",
                              }}
                            >
                              {/* Image */}
                              {item.file.type.split("/")[0] == "image" && (
                                <img
                                  src={URL.createObjectURL(item.file)}
                                  alt=""
                                  className="new-img"
                                />
                              )}
                              {/* Document */}
                              {item.file.type == "application/pdf" && (
                                <div className="selected-file">
                                  <div className="text-center">
                                    <div>
                                      <i
                                        className="fa fa-file"
                                        aria-hidden="true"
                                      ></i>
                                    </div>
                                    <div style={{ fontSize: "12px" }}>
                                      {item.file.name}
                                    </div>
                                  </div>
                                </div>
                              )}
                              {/* Video */}
                              {item.file.type.split("/")[0] == "video" && (
                                <video className="new-img" controls>
                                  <source
                                    src={URL.createObjectURL(item.file)}
                                    type="video/mp4"
                                  />
                                </video>
                              )}
                            </div>
                            <div>
                              <small>
                                <i className="fa fa-file"></i> {item.file.type}
                              </small>
                              <p
                                className="mt-2 mb-0"
                                style={{ fontSize: "14px" }}
                              >
                                {item.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      </Col>
                    ))}
                  </Row>
                </Col>
              </Row>
            </>
          )}

          <div style={{ display: "flex", marginLeft: "auto" }}>
            <button
              className="btn add-farmer__submit-btn mt-0 mb-5"
              disabled={enableButton}
              onClick={async () => {
                await updateFarmer(
                  navigate,
                  -1,
                  formStateTab0,
                  setFormStateTab0,
                  formStateTab1,
                  setFormStateTab1,
                  formStateTab2,
                  setFormStateTab2,
                  formStateTab3,
                  setFormStateTab3,
                  updateFarmerThumbnailImage,
                  updateFarmerImages,
                  updateDairyFarmImages,
                  updateAadharImages,
                  documentEntries,
                  setDocumentEntries,
                  -1,
                  params?.farmerId
                );
              }}
            >
              Update
            </button>
          </div>

          <Modal show={show} onHide={handleClose}>
            <Modal.Header closeButton>
              <Modal.Title>Add Image</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <small>Choose Image</small>
              <input
                className="input-field"
                style={{ width: "100%" }}
                type="file"
                onChange={imageUploadHandler}
              />
              {image && (
                <>
                  {/* Image */}
                  {image.type.split("/")[0] == "image" && (
                    <div className="add-farmer__img-div">
                      <div
                        className="add-farmer__remove-img-btn"
                        onClick={() => removeImageHandler()}
                      >
                        <i className="fa fa-times"></i>
                      </div>
                      <img
                        src={URL.createObjectURL(image)}
                        alt=""
                        className="new-img"
                      />
                    </div>
                  )}
                  {/* PDF */}
                  {image.type == "application/pdf" && (
                    <div className="add-farmer__img-div">
                      <div
                        className="add-farmer__remove-img-btn"
                        onClick={() => removeImageHandler()}
                      >
                        <i className="fa fa-times"></i>
                      </div>
                      <div className="selected-file">
                        <div className="text-center">
                          <div>
                            <i className="fa fa-file" aria-hidden="true"></i>
                          </div>
                          <div style={{ fontSize: "12px" }}>{image.name}</div>
                        </div>
                      </div>
                    </div>
                  )}
                  {/* Video */}
                  {image.type.split("/")[0] == "video" && (
                    <div className="add-farmer__img-div">
                      <div
                        className="add-farmer__remove-img-btn"
                        onClick={() => removeImageHandler()}
                      >
                        <i className="fa fa-times"></i>
                      </div>
                      <div className="selected-file">
                        <div className="text-center">
                          <div>
                            <i
                              className="fa fa-file-video"
                              aria-hidden="true"
                            ></i>
                          </div>
                          <div style={{ fontSize: "12px" }}>{image.name}</div>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}
              <small>Description</small>
              <textarea
                className="input-field"
                style={{ width: "100%" }}
                rows="3"
                type="text"
                defaultValue={imageDes}
                onChange={imageDescriptionChangeHandler}
              ></textarea>
            </Modal.Body>
            <Modal.Footer>
              <Button
                variant="primary"
                onClick={saveImageHandler}
                style={{ background: "#673AB7", color: "#fff", border: "none" }}
              >
                Save
              </Button>
            </Modal.Footer>
          </Modal>

          <hr />

          {/* Cattle list */}
          <div className="mb-4">
            <div
              style={{
                display: "flex",
                alignItems: "center",
                marginBottom: "1rem",
                width: 700,
              }}
            >
              <div>
                <p
                  className="m-0"
                  style={{
                    fontSize: "18px",
                    fontWeight: "700",
                    color: "#222222",
                  }}
                >
                  Cattle List
                </p>
              </div>
              {selectedActiveCattleArr.length > 0 && (
                <Button
                  variant="primary"
                  onClick={async () => {
                    let tempArr = globalReferencesMapper(10020000);
                    setCancelReasonArr([...tempArr]);
                    setShowConfirmAllModal(true);
                  }}
                  style={{
                    background: "#ec6237",
                    color: "#fff",
                    border: "none",
                    marginLeft: "1rem",
                  }}
                >
                  Disable ({selectedActiveCattleArr.length}) cattle
                </Button>
              )}
              {selectedInactiveCattleArr.length > 0 && (
                <Button
                  variant="primary"
                  onClick={async () => {
                    let tempArr = globalReferencesMapper(10020000);
                    setCancelReasonArr([...tempArr]);
                    setShowConfirmAllModal(true);
                  }}
                  style={{
                    background: "#ec6237",
                    color: "#fff",
                    border: "none",
                    marginLeft: "1rem",
                  }}
                >
                  Enable ({selectedInactiveCattleArr.length}) cattle
                </Button>
              )}
              <div className="farmerItem__switch-div">
                <div
                  className={`farmerItem__switch-btn me-2 ${cattleListSwitch.active == true && "selected"
                    }`}
                  onClick={() => cattleListSwitchHandler("active")}
                >
                  Active
                </div>
                <div
                  className={`farmerItem__switch-btn ${cattleListSwitch.inactive == true && "selected"
                    }`}
                  onClick={() => cattleListSwitchHandler("inactive")}
                >
                  Inactive
                </div>
              </div>
            </div>

            {/* Active cattle */}
            {cattleListSwitch.active == true && (
              <>
                {activeCattleList.length > 0 ? (
                  <>
                    <div className="farmers__farmers-table">
                      <Table
                        rowSelection={rowSelectionActive}
                        columns={tableColumnsActive}
                        dataSource={tableDataActive}
                        pagination={false}
                      />
                    </div>
                  </>
                ) : (
                  <>
                    <div
                      className="text-center my-5"
                      style={{ color: "#4c4c4c" }}
                    >
                      <i
                        className="fa fa-exclamation-triangle"
                        aria-hidden="true"
                      ></i>{" "}
                      No active cattles!
                    </div>
                  </>
                )}
              </>
            )}

            {/* Inactive cattle */}
            {cattleListSwitch.inactive == true && (
              <>
                {inactiveCattleList.length > 0 ? (
                  <>
                    <div className="farmers__farmers-table">
                      <Table
                        rowSelection={rowSelectionInactive}
                        columns={tableColumnsInactive}
                        dataSource={tableDataInactive}
                        pagination={false}
                      />
                    </div>
                  </>
                ) : (
                  <>
                    <div
                      className="text-center my-5"
                      style={{ color: "#4c4c4c" }}
                    >
                      <i
                        className="fa fa-exclamation-triangle"
                        aria-hidden="true"
                      ></i>{" "}
                      No inactive cattles!
                    </div>
                  </>
                )}
              </>
            )}
          </div>

          <hr />

          {/* Care Calendar */}
          <div className="mb-4">
            <div
              style={{
                display: "flex",
                alignItems: "center",
                marginBottom: "1rem",
                width: "100%",
              }}
            >
              <div
                className="accordion-down-btn me-3"
                onClick={() => setShowCalendar(!showCalendar)}
              >
                <i
                  className={`fa ${showCalendar === true ? "fa-chevron-up" : "fa-chevron-down"
                    }`}
                  aria-hidden="true"
                ></i>
              </div>
              <div>
                <p
                  className="m-0"
                  style={{
                    fontSize: "18px",
                    fontWeight: "700",
                    color: "#222222",
                  }}
                >
                  Care Calendar
                </p>
              </div>

              {userData.user_type_resolved.type === "back_office_person" && (
                <button
                  className={
                    enableButton
                      ? "farmers__add-farmer-btn_disabled"
                      : "farmers__add-farmer-btn"
                  }
                  disabled={enableButton}
                  onClick={addFarmerTaskHandler}
                >
                  + Add Farmer Task
                </button>
              )}
            </div>
            {showCalendar === true && (
              <div>
                <CareCalendar type="farmer" />
              </div>
            )}
          </div>

          {/* Confirmation Modal */}
          {/* <Modal
            show={showConfirmationModal}
            centered
            backdrop="static"
            onHide={handleConfirmationModalClose}
          >
            <Modal.Header closeButton>
              <Modal.Title>Deactivate this cattle?</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              Are you sure about making this cattle as inactive?
            </Modal.Body>
            <Modal.Footer>
              <Button
                variant="primary"
                style={{ background: "#673AB7", color: "#fff", border: "none" }}
                onClick={() => changeActiveStatusHandler(selectedCattle)}
              >
                Confirm
              </Button>
            </Modal.Footer>
          </Modal> */}

          {/* Confirm All Modal */}
          <Modal
            show={showConfirmAllModal}
            centered
            backdrop="static"
            onHide={handleConfirmAllModalClose}
          >
            <Modal.Header closeButton>
              <Modal.Title>
                {selectedActiveCattleArr.length > 0 && (
                  <>Deactivate {selectedActiveCattleArr.length} cattle?</>
                )}
                {selectedInactiveCattleArr.length > 0 && (
                  <>Activate {selectedInactiveCattleArr.length} cattle?</>
                )}
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              {selectedActiveCattleArr.length > 0 && (
                <>
                  Are you sure about making {selectedActiveCattleArr.length}{" "}
                  cattle as inactive?
                </>
              )}
              {selectedInactiveCattleArr.length > 0 && (
                <>
                  Are you sure about making {selectedInactiveCattleArr.length}{" "}
                  cattle as active?
                </>
              )}

              {cancelReasonArr && (
                <div className="my-2">
                  {selectedActiveCattleArr.length > 0 ? (
                    <>
                      <div>
                        <small>
                          Select reason
                          <span style={{ color: "red" }}>*</span>
                        </small>
                        <select
                          className="input-field"
                          defaultValue=""
                          style={{ width: "100%" }}
                          onChange={cancelReasonSelectHandler}
                        >
                          <option selected disabled value="">
                            Select
                          </option>
                          {cancelReasonArr.map((option) => (
                            <option
                              key={option.reference_id}
                              value={option.reference_id}
                            >
                              {option.reference_name_l10n.en
                                ? option.reference_name_l10n.en
                                : option.reference_name_l10n.ul
                                  ? option.reference_name_l10n.ul
                                  : option.reference_name_l10n}
                            </option>
                          ))}
                        </select>
                        {inactiveReasonOther.active == true && (
                          <input
                            type="text"
                            className="input-field"
                            style={{ width: "100%" }}
                            placeholder="Enter other reason here...*"
                            onChange={otherReasonTextHandler}
                          />
                        )}
                      </div>
                    </>
                  ) : (
                    <>
                      <div>
                        <small>
                          Select reason
                          <span style={{ color: "red" }}>*</span>
                        </small>
                        <select
                          className="input-field"
                          defaultValue={1002000004} // Set the default value to the reference ID 1002000004
                          style={{ width: "100%" }}
                          onChange={cancelReasonSelectHandler}
                          disabled // Add the disabled attribute to disable the dropdown
                        >
                          {cancelReasonArr.map(
                            (option) =>
                              option.reference_id === 1002000004 && ( // Only show the option with reference ID 1002000004
                                <option
                                  key={option.reference_id}
                                  value={1002000004}
                                >
                                  {option.reference_name_l10n}
                                </option>
                              )
                          )}
                        </select>
                        <input
                          type="text"
                          className="input-field"
                          style={{ width: "100%" }}
                          placeholder="Enter other reason here...*"
                          onChange={activeOtherReasonTextHandler}
                        />
                      </div>
                    </>
                  )}
                  <div>
                    <small>
                      Date
                      <span style={{ color: "red" }}>*</span>
                    </small>
                    {selectedInactiveCattleArr.length > 0 ? (
                      <>
                        <input
                          type="date"
                          className="input-field"
                          style={{ width: "100%" }}
                          onChange={activationDateHandler}
                        ></input>
                      </>
                    ) : (
                      <>
                        <input
                          type="date"
                          className="input-field"
                          style={{ width: "100%" }}
                          onChange={deactivationDateHandler}
                        ></input>
                      </>
                    )}
                  </div>
                  <div>
                    <small>
                      Notes
                      <span style={{ color: "red" }}>*</span>
                    </small>
                    <textarea
                      className="input-field"
                      style={{ width: "100%" }}
                      placeholder="Description..."
                      rows="3"
                      onChange={cancelDescriptionHandler}
                    ></textarea>
                  </div>
                </div>
              )}
            </Modal.Body>
            <Modal.Footer>
              {selectedActiveCattleArr.length > 0 && (
                <>
                  <Button
                    variant="primary"
                    disabled={!cancelReasonArr}
                    style={{
                      background: "#673AB7",
                      color: "#fff",
                      border: "none",
                    }}
                    onClick={() =>
                      changeActiveStatusHandler(
                        selectedActiveCattleArr,
                        "disable"
                      )
                    }
                  >
                    Disable
                  </Button>
                </>
              )}
              {selectedInactiveCattleArr.length > 0 && (
                <>
                  <Button
                    variant="primary"
                    disabled={!cancelReasonArr}
                    style={{
                      background: "#673AB7",
                      color: "#fff",
                      border: "none",
                    }}
                    onClick={() =>
                      changeActiveStatusHandler(
                        selectedInactiveCattleArr,
                        "enable"
                      )
                    }
                  >
                    Enable
                  </Button>
                </>
              )}
            </Modal.Footer>
          </Modal>

          <hr className="mb-5" />
        </>
      )}
    </>
  );
};

// export default FarmerItem1;
export default withAuthorization(FarmerItem1, [CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM])