.farmer-details__back-btn {
    font-size: 13px;
    color: #4c4c4c;
    cursor: pointer;
    text-decoration: none;
}

.farmer-details__back-btn:hover {
    color: #4c4c4c;
}

.farmer-item__img-div {
    height: 100px;
    width: 150px;
    overflow: hidden;
    margin: 0 10px 0 0;
    border-radius: 5px;
    cursor: pointer;
    position: relative;
}

.farmer-item__img {
    display: flex;
    border: 1px solid silver;
    margin-bottom: 1rem;
    background-color: #fff;
    border-radius: 5px;
    font-size: 14px;
    width: 100%;
}

.accordion-down-btn {
    /* margin-left: auto; */
    cursor: pointer;
    height: 35px;
    width: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #4c4c4c;
    color: #fff;
}

.farmerItem__switch-div {
    border: 1px solid #ececec;
    margin-left: auto;
    display: flex;
    padding: 8px;
    border-radius: 5px;
    background-color: white;
    font-size: 14px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    /* font-weight: 600; */
}

.farmerItem__switch-btn {
    /* border: 1px solid silver; */
    padding: 5px 8px;
    color: #4c4c4c;
    cursor: pointer;
}

.farmerItem__switch-btn.selected {
    background-color: #ec6237;
    color: white;
    border-radius: 5px;
}
.react-datepicker-wrapper{
    width: 100%;
}
.input-width{
    width: 100%;
}