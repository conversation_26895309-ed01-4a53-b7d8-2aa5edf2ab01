import axios from "axios";
import { useEffect, useState, Suspense } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  BASE_URL,
  instance,
  tokenFailureHandler,
} from "../../../../../Services/api.service";
import { Col, Row, Table } from "react-bootstrap";
import { Autocomplete, TextField } from "@mui/material";
import ToastersService from "../../../../../Services/toasters.service";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import moment from "moment";
import { globalReferencesMapper } from "../../../../../Utilities/Common/utils";
import Loader from "../../../../Loader/Loader";
import { extractBasedOnLanguageMod } from "../../../../../Utilities/Common/utils";

const {CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../../../user/authorize')

const AddFarmerTask = () => {
  const params = useParams();
  const navigate = useNavigate();
  const [farmerName, setFarmerName] = useState();
  const [activityList, setActivityList] = useState();
  const [initialDate, setInitialDate] = useState(null);
  let [cattleListReqBody, setCattleListReqBody] = useState({
    activity_id: "",
    farmer_id: params.farmerId,
    calendar_activity_status: 1000300001,
    activity_date: "",
    activity_date_gmt: "",
    // animal_ids: [],
  });
  // const [eligibleCattleList, setEligibleCattleList] = useState();
  // let [selectedMedicines, setSelectedMedicines] = useState([]);
  // const [medicines, setMedicines] = useState([]);
  const [statusOptions, setStatusOptions] = useState([]);
  const [creatingTaskLoader, setCreatingTaskLoader] = useState(false);
  // const [showCheckboxes, setShowCheckboxes] = useState(false);

  const getFarmerDetailsById = async (farmerId) => {
    try {
      // const response = await axios.get(BASE_URL + "/customer/" + farmerId, {
      //   headers: {
      //     token: localStorage.getItem("accessToken"),
      //     "Content-Type": "application/json",
      //     "X-App-Id": "123456789"
      //   },
      // });
      const response = await instance({
        url: "/customer/" + farmerId,
        method: "GET",
        // data: JSON.stringify(reqBody),
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log("getFarmerDetailsById", response);
      if (response.status === 200) {
        setFarmerName(
          response.data.customer_name_l10n.en
            ? response.data.customer_name_l10n.en
            : response.data.customer_name_l10n.ul
        );
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  const getActivityConfig = async () => {
    try {
      // const response = await axios.get(BASE_URL + "/references/care-calendar", {
      //   headers: {
      //     token: localStorage.getItem("accessToken"),
      //     "X-App-Id": "123456789"
      //   },
      // });
      const response = await instance({
        url: "/references/care-calendar",
        method: "GET",
        // data: JSON.stringify(reqBody),
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      if (response.data.result === true) {
        console.log("Activity Config", response.data);
        // setMedicines(response.data.data.medicine_config);
        console.log(response.data.data.activity_config);
        setStatusOptions(response.data.data.status_config);
        // response.data.data.activity_config.Preventive.options.map((item) => {
        //   if (item.activity_id === 1000180011) {
        //     console.log([item]);
        //     setActivityList([item]);
        //   }
        // });
        let activityIds = [1000180011, 1000180015];
        const options =
          response.data.data.activity_config.Preventive.options.filter(
            (item) => {
              if (activityIds.includes(item.activity_id)) {
                return item;
              }
            }
          );
        setActivityList(options);
        // setActivityList(response.data.data.activity_config.Preventive.options);
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  const setActivityConfigurationsFromGlobalRef = () => {
    setStatusOptions(globalReferencesMapper(10003000));
   // 1000270001 this is preventive category id
    let activityIds = [1000180011, 1000180015, 1000180013];
    const options =
      global.activityReferences[1000270001]?.filter(
        (item) => {
          if (activityIds?.includes(item?.activity_id)) {
            return item;
          }
        }
      );
    setActivityList(options);
  };
  // const getEligibleCattleList = async () => {
  //   try {
  //     const response = await axios.post(
  //       BASE_URL + "/farmtask-animal-list",
  //       { ...cattleListReqBody },
  //       {
  //         headers: {
  //           token: localStorage.getItem("accessToken"),
  //         },
  //       }
  //     );
  //     if (response.status === 201) {
  //       console.log("Eligible cattle list", response.data.result);
  //       setEligibleCattleList(response.data.result);
  //     }
  //   } catch (error) {
  //     console.log(error);
  //     // ToastersService.failureToast(error);
  //     if (error.response.status === 401) {
  //       console.log("token expired");
  //       localStorage.removeItem('accessToken');
  //       localStorage.removeItem('resetToken');
  //       ToastersService.failureToast('Session Expired!');
  //       navigate('/login');
  //     }
  //   }
  // };

  const createTask = async () => {
    // console.log(createTaskForm);

    // if (selectedMedicines.length > 0) {
    //   selectedMedicines.forEach((item) => {
    //     const key1 = `medicine_${item.count}`;
    //     const value1 = item.medicineId;
    //     const key2 = `medicine_${item.count}_quantity`;
    //     const value2 = item.quantity;
    //     const newKeyValuePair1 = { [key1]: value1 };
    //     const newKeyValuePair2 = { [key2]: value2 };
    //     cattleListReqBody = {
    //       ...cattleListReqBody,
    //       ...newKeyValuePair1,
    //       ...newKeyValuePair2,
    //     };
    //   });
    // }
    console.log("Create task form", cattleListReqBody);

    setCreatingTaskLoader(true);
    try {
      // const response = await axios.post(
      //   BASE_URL + "/activity/create-task",
      //   { ...cattleListReqBody, entity_uuid: params.farmerId },
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "123456789"
      //     },
      //   }
      // );
      const response = await instance({
        url: "/activity/create-task",
        method: "POST",
        data: { ...cattleListReqBody, entity_uuid: params.farmerId },
        headers: {
          "Content-Type": "application/json",
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log(response);
      if (response.status === 201) {
        setCreatingTaskLoader(false);
        ToastersService.successToast("Task created successfully!");
        sendAutoGeneratedNote(response.data.id);
        navigate("/tasks/" + response.data.id);
      } else {
        throw Error("Task not created! Try again.");
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error);
      setCreatingTaskLoader(false);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  const addTaskNote = async (noteItem, calendarId) => {
    try {
      // const response = await axios.post(
      //   BASE_URL + "/common/add-task-note",
      //   {
      //     note: noteItem,
      //     calendar_id: calendarId,
      //   },
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "123456789"
      //     },
      //   }
      // );
      const response = await instance({
        url: "/common/add-task-note",
        method: "POST",
        data: {
          note: noteItem,
          calendar_id: calendarId,
        },
        headers: {
          "Content-Type": "application/json",
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log("Add task response", response);
      if (response.status === 201) {
      } else {
        throw Error("Note not sent! Try again.");
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  useEffect(() => {
    getFarmerDetailsById(params.farmerId);
    // getActivityConfig();
    setActivityConfigurationsFromGlobalRef();
  }, []);

  const sendAutoGeneratedNote = (calendarId) => {
    let noteItem = "";
    for (let item of statusOptions) {
      if (item.reference_id == cattleListReqBody.calendar_activity_status) {
        noteItem =
          item?.reference_name_l10n?.en !== undefined ||
          item?.reference_name_l10n?.ul !== undefined
            ? `New Status: ${
                item.reference_name_l10n.en
                  ? item.reference_name_l10n.en
                  : item.reference_name_l10n.ul
              }`
            : `New Status: ${
                item?.reference_name_l10n === null ||
                item?.reference_name_l10n === undefined
                  ? ""
                  : item?.reference_name_l10n
              }`;
      }
    }

    // if (selectedMedicines.length > 0) {
    //   console.log(selectedMedicines);
    //   for (let item of selectedMedicines) {
    //     if (item.medicineId !== null) {
    //       for (let medicine of medicines) {
    //         if (medicine.reference_id == item.medicineId) {
    //           console.log("Found!", item);
    //           noteItem = noteItem.concat(
    //             `, Medicine(${item.count}): ${
    //               medicine.reference_name_l10n.ul
    //                 ? medicine.reference_name_l10n.ul
    //                 : medicine.reference_name_l10n.en
    //             }`
    //           );
    //         }
    //       }
    //     }
    //     if (item.quantity !== null) {
    //       noteItem = noteItem.concat(
    //         `, Quantity(${item.count}): ` + item.quantity
    //       );
    //     }
    //   }
    // }

    console.log(noteItem);

    addTaskNote(noteItem, calendarId);
  };
  const dateChangeHandler = (date) => {
    if (date && !date.getHours() && !date.getMinutes()) {
      date.setHours(0);
      date.setMinutes(0);
    }
    setInitialDate(date);
    const inputDate = new Date(date);
    const utcDateString = inputDate.toISOString();
    setCattleListReqBody({
      ...cattleListReqBody,
      activity_date: moment(date).format("YYYY-MM-DD"),
      activity_date_gmt: utcDateString,
    });
  };

  const activitySelectHandler = (event, activityId) => {
    console.log(activityId);
    setCattleListReqBody({
      ...cattleListReqBody,
      activity_id: activityId,
      for_specific_cattle: 1000105002
    });
  };

  // const showCattleHandler = () => {
  //   if (cattleListReqBody.activity_date === "") {
  //     ToastersService.failureToast("Date is mandatory!");
  //   } else if (cattleListReqBody.activity_id === "") {
  //     ToastersService.failureToast("Select an activity!");
  //   } else {
  //     console.log(cattleListReqBody);
  //     getEligibleCattleList();
  //   }
  // };

  // const addMedicineHandler = () => {
  //   if (selectedMedicines.length > 9) {
  //     ToastersService.failureToast("Maximum 10 medicines are allowed!");
  //   } else {
  //     setSelectedMedicines([
  //       ...selectedMedicines,
  //       {
  //         count: selectedMedicines.length + 1,
  //         medicineId: null,
  //         quantity: null,
  //       },
  //     ]);
  //   }
  // };

  // const chooseMedicineHandler = (value, count) => {
  //   console.log(value, count);

  //   for (let item of selectedMedicines) {
  //     if (item.count == count) {
  //       item.medicineId = value.reference_id;
  //     }
  //   }

  //   console.log(selectedMedicines);
  //   setSelectedMedicines(selectedMedicines);
  // };

  // const medicineQuantityHandler = (event, count) => {
  //   console.log(event.target.value, count);
  //   for (let item of selectedMedicines) {
  //     if (item.count == count) {
  //       item.quantity = event.target.value;
  //     }
  //   }

  //   console.log(selectedMedicines);
  //   setSelectedMedicines(selectedMedicines);
  // };

  // const statusSelectHandler = (event) => {
  //   console.log({
  //     ...cattleListReqBody,
  //     calendar_activity_status: event.target.value,
  //   });
  //   if (event.target.value == "1000300050") {
  //     console.log("found");
  //     setShowCheckboxes(true);
  //   } else {
  //     setShowCheckboxes(false);
  //   }
  //   setCattleListReqBody({
  //     ...cattleListReqBody,
  //     calendar_activity_status: event.target.value,
  //   });
  // };

  const createTaskHandler = () => {
    if (!cattleListReqBody.activity_id) {
      ToastersService.failureToast("Select activity!");
    } else if (!cattleListReqBody.calendar_activity_status) {
      ToastersService.failureToast("Select activity status!");
      // } else if (
      //   cattleListReqBody.calendar_activity_status == "1000300050" &&
      //   cattleListReqBody.animal_ids.length <= 0
      // ) {
      //   ToastersService.failureToast("Select cattle for completed activity!");
    } else if (!cattleListReqBody.activity_date) {
      ToastersService.failureToast("Select activity date");
    } else {
      console.log(cattleListReqBody);
      createTask();
    }
  };

  // const checkboxChangeHandler = (event) => {
  //   console.log(event.target.value, event.target.checked);
  //   if (event.target.checked == true) {
  //     cattleListReqBody.animal_ids.push(event.target.value);
  //     console.log(cattleListReqBody);
  //     setCattleListReqBody(cattleListReqBody);
  //   } else if (event.target.checked == false) {
  //     cattleListReqBody.animal_ids = cattleListReqBody.animal_ids.filter(
  //       (item) => item !== event.target.value
  //     );
  //     console.log(cattleListReqBody);
  //     setCattleListReqBody(cattleListReqBody);
  //   }
  // };

  return (
    <Suspense fallback={<Loader />}>
      <div className="mb-4">
        <p className="farmer-details__back-btn" onClick={() => navigate(-1)}>
          <i className="fa fa-angle-double-left" aria-hidden="true"></i> Go back
        </p>

        <p
          className="mb-0 mt-3"
          style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
        >
          Farmer Tasks / Add New Task
        </p>
        <p
          className="m-0"
          style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
        >
          Add Farmer Task
        </p>
      </div>

      <div className="add-task__form pb-5">
        <div className="add-task__form-header">Add Task</div>
        <div>
          <Row>
            <Col lg={4} md={6} sm={12}>
              <small>Farmer Name</small>
              <input
                type="text"
                style={{
                  width: "100%",
                  textTransform: "capitalize",
                  cursor: "not-allowed",
                }}
                className="input-field"
                defaultValue={farmerName}
                disabled
              />
            </Col>
            <Col lg={4} md={6} sm={12}>
              <small>Select Date and Time*</small>
              <br />
              <DatePicker
                style={{ width: "100%" }}
                className="input-field input-width"
                selected={initialDate}
                onChange={dateChangeHandler}
                showTimeSelect
                dateFormat="Pp"
                timeIntervals={5}
                timeCaption="Time"
                placeholderText="Please select a date and time"
              />
            </Col>
            <Col lg={4} md={6} sm={12}>
              <small>Select Activity*</small>
              <Autocomplete
                options={activityList}
                getOptionLabel={(option) => extractBasedOnLanguageMod(option.activity_name_l10n,'en')}
                //   disabled={activityList.length === 0}
                sx={{
                  width: "100%",
                  marginTop: "5px",
                  background: "#fff",
                }}
                renderInput={(params) => (
                  <TextField {...params} label="Search..." />
                )}
                onChange={(event, value) =>
                  activitySelectHandler(event, value.activity_id)
                }
              />
            </Col>
          </Row>
          {/* <div className="add-task__create-btn-2" onClick={showCattleHandler}>
            Get eligible cattle
          </div>
          <hr />
          {eligibleCattleList && (
            <>
              {eligibleCattleList.length === 0 ? (
                <div
                  className="my-5 text-center"
                  style={{ color: "gray", fontSize: "14px" }}
                >
                  <i className="fa fa-exclamation-triangle"></i> No eligible
                  cattle found!
                </div>
              ) : (
                <div>
                  <small>Eligible cattle:</small>
                  <Table className="my-3" striped bordered>
                    <thead>
                      <tr>
                        <th className="text-center">#</th>
                        <th>Visual Id</th>
                        <th>Ear Tag</th>
                        <th>Cattle type</th>
                      </tr>
                    </thead>
                    <tbody>
                      {eligibleCattleList.map((item) => (
                        <tr key={item.animal_id}>
                          <td className="text-center">
                            {showCheckboxes == false ? (
                              ""
                            ) : (
                              <input
                                type="checkbox"
                                style={{ cursor: "pointer" }}
                                value={item.animal_id}
                                onChange={checkboxChangeHandler}
                              />
                            )}
                          </td>
                          <td>{item.animal_visual_id}</td>
                          <td>{item.ear_tag}</td>
                          <td>
                            {item.animal_type_l10n.en
                              ? item.animal_type_l10n.en
                              : item.animal_type_l10n.ul}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              )}
            </>
          )} */}

          {/* <Row>
            <Col lg={4} md={6} sm={12}>
              <small>Select Status*</small>
              <select
                className="input-field"
                style={{ width: "100%" }}
                defaultValue={"DEFAULT"}
                onChange={statusSelectHandler}
              >
                <option disabled value="DEFAULT">
                  Select
                </option>
                {statusOptions.map((option) => {
                  return (
                    <option
                      key={option.reference_id}
                      value={option.reference_id}
                    >
                      {option.reference_name_l10n.en
                        ? option.reference_name_l10n.en
                        : option.reference_name_l10n.ul}
                    </option>
                  );
                })}
              </select>
            </Col>
          </Row> */}

          {/* <small>Medicines:</small>
          {selectedMedicines.length > 0 &&
            selectedMedicines.map((medicine) => (
              <Row key={medicine.count} className="mt-2">
                <Col lg={4} md={6} sm={12}>
                  <small style={{ fontWeight: "normal" }}>
                    Medicine {medicine.count}
                  </small>
                  <Autocomplete
                    options={medicines}
                    getOptionLabel={(option) =>
                      option.reference_name_l10n.en
                        ? option.reference_name_l10n.en
                        : option.reference_name_l10n.ul
                    }
                    sx={{
                      width: "100%",
                      marginTop: "5px",
                      background: "#fff",
                    }}
                    renderInput={(params) => (
                      <TextField {...params} label="Search..." />
                    )}
                    onChange={(event, value) =>
                      chooseMedicineHandler(value, medicine.count)
                    }
                  />
                </Col>
                <Col lg={4} md={6} sm={12}>
                  <small style={{ fontWeight: "normal" }}>
                    Quantity {medicine.count}
                  </small>
                  <input
                    className="input-field"
                    style={{ width: "100%" }}
                    type="number"
                    placeholder="eg: 2"
                    onChange={(event) =>
                      medicineQuantityHandler(event, medicine.count)
                    }
                  />
                </Col>
              </Row>
            ))}

          <div
            className="add-cattle__add-disease-btn mt-3"
            onClick={addMedicineHandler}
          >
            + Add medicine
          </div> */}

          <div
            className="add-task__create-btn mb-5"
            onClick={createTaskHandler}
          >
            Create
          </div>
        </div>
      </div>
    </Suspense>
  );
};

// export default AddFarmerTask;
export default withAuthorization(AddFarmerTask, [CS_TEAM])