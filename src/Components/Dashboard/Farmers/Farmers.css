.farmers__add-farmer-btn {
  margin-left: auto;
  background: #ec6237;
  border-radius: 4px;
  color: #fff;
  font-size: 16px;
  padding: 10px 15px;
  cursor: pointer;
}
.farmers__add-farmer-btn_disabled {
  margin-left: auto;
  background: transparent;
  border-radius: 4px;
  color: gray;
  font-size: 16px;
  padding: 10px 15px;
  cursor: not-allowed;
}

select {
  border: none;
  cursor: pointer;
}

.farmers__farmers-table {
  width: calc(100vw - 15rem);
  overflow-x: scroll;
  margin-bottom: 5rem;
  /* border: 1px solid silver; */
}

@media (max-width: 768px) {
  .farmers__farmers-table {
    width: 100%;
    overflow-x: scroll;
    /* border: 1px solid silver; */
  }
}

.farmers__farmer-name {
  font-weight: 600;
  text-transform: capitalize;
}

.farmers__farmer-name:hover {
  text-decoration: underline;
  cursor: pointer;
}

.styles-module_navigation__1pqAE{
  display: none !important;
}

.primary-filter-item {
  padding: 5px 10px;
  margin-right: 10px;
  cursor: pointer;
  background-color: #ececec;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 600;
  color: #4c4c4c;
}

/* .primary-filter-item:hover {
  border: 1px solid #ececec;
} */

.primary-filter-item.selected {
  background-color: #673ab7;
  color: #fff;
}

/* .farmers__farmers-table .table-head {
    background-color: #FFC6F9;
} */

