import React from "react";
import "./MedicineInventory.css";
import { DatePicker, } from "antd";
import { useSelector, useDispatch } from "react-redux";
import {  instance } from "../../../Services/api.service";
import { clearUserToken } from "../../user/action";
import { get_medicine_inventory } from "../../../router";

import { useNavigate, useSearchParams } from "react-router-dom";
import { useState, useCallback } from "react";
import {
  createPageParamsFromURLString,
  OCTable,
} from "../../Common/AntTableHelper";
import CloseIcon from "@mui/icons-material/Close";
import { Container, Modal } from "react-bootstrap";
import { extractBasedOnLanguageMod, unitMappingByMedId } from "../../../Utilities/Common/utils";

const MedicineInventory = () => {
  const { RangePicker } = DatePicker;
  const dispatch = useDispatch();
  const userLanguage = useSelector((state) => state.user.userLanguage);
  const userToken = useSelector((state) => state.user.userToken);
  const navigate = useNavigate();

  const [modalVisible, setModalVisible] = useState(false);

  const getMedicineId = async (medId) => {
    let reqBody = {};
    console.log("med is", medId);
    try {
      const response = await instance({
        url: `/v3/medinventory?id=${medId}`,
        method: "POST",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status === 200) {
        // console.log("medicine list based on the id", response.data.data);
      }
      return response.data.report;
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };
  const [staffForMed, setStaffforMed] = useState([]);

  const handleMedcineIdClick = async (prop) => {
    console.log("clicked the link", prop, prop.medicine_id);
    let response = await getMedicineId(prop.medicine_id);
    console.log("med==resp",response)
    setStaffforMed(response);
    console.log("response of the medicine id ", response);
    setModalVisible(true);
  };

  const medicineTableConfiguration = {
    enableSelection: false,
    defaultPageSize: 10,
    enableHorizontalScrolling: true,
    showTableHeader: false,
    omniSearchRowEnabled: false,
    enableColumnConfiguration: false,
    enableOnChange: true,
    // getAllDataAtOnce: true,
    // disablePagination: true,
    dataTransformationRules: {
      // staff_filter_values: 'bdm_filter_values',
      // staff_name: 'bdm'
    },
    // rowKey: 'requisition_id',
    columns: [
      {
        title: "Medicine id",
        key: "medicine_id",
        render: (_, record) => (
          <div className="data" style={{cursor:"pointer",color:"blue"}} onClick={() => handleMedcineIdClick(record)}>
            {record.medicine_id}
          </div>
        ),
      },
      {
        title: "Medicine Name",
        key: "medicine_name_l10n",
        render: (_, record) => (
          <div className="data">{extractBasedOnLanguageMod(record?.medicine_name_l10n)}</div>
        ),
      },
      {
        title: "MRP",
        key: "medicine_mrp",
        render: (_, record) => (
          <div className="data">{record?.medicine_mrp}</div>
        ),
      },
      {
        title: "Pack Size",
        key: "pack_size",
        render: (_, record) => (
          <div className="data">
            {record?.medicine_pack_size ? (record?.medicine_pack_size + " " + unitMappingByMedId(record?.medicine_id)):"-"}
          </div>
        ),
      },
      {
        title: "Total Quantity",
        key: "total_qty",
        render: (_, record) => (
          <div className="data">
            {record?.total_qty + " " + unitMappingByMedId(record?.medicine_id)}
          </div>
        ),
      },
    ],
    columnConfiguration: {
      medicine_id: {
        headingText: "Requisition Id",
      },
      medicine_name_l10n: {
        headingText: "Medicine Name",
        type: "single_select_array",
        dataType: "int",
        enableFilter: true,
        filterValuesKey: "medicine_filter_values",
        includeInOmnisearch: true,
      },
      mrp: {
        headingText: "Requisitioner Name",
      },
      pack_size: {
        headingText: "Requisitioner Name",
      },
      total_qty: {
        headingText: "Requisitioner Name",
      },
    },
  };

  const loadFilters = useCallback(
    async (userToken) => {
      // try {
      //     const headers = {
      //         useCase: 'Get Farmers With Location Recorded Report Filter Data',
      //         token: userToken.accessToken, //authToken
      //     }
      //     const response = await getFarmersFilterData(headers)
      //     return response.data
      // } catch (error) {
      //     if (error.response.status === 401) {
      //         dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
      //     }
      // }
      return [];
    },
    [dispatch, navigate]
  );

  const loadReport = useCallback(
    async (userToken, queryParams) => {
      try {
        const headers = {
          token: userToken.accessToken, //authToken
        };
        const response = await get_medicine_inventory(headers, queryParams);
        return response?.data;
      } catch (error) {
        if (error?.response?.status === 401) {
          dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
        }
      }
    },
    [dispatch, navigate]
  );

  const [searchParams] = useSearchParams();
  const pageParams = createPageParamsFromURLString(
    medicineTableConfiguration,
    searchParams
  );
  const [reportData, setReportData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const loadInitialFilterDataCB = (additionalParams) => {
    console.log("load filter is called  ");
    return loadFilters(userToken, additionalParams);
  };

  const loadReportCB = (reportQueryParams, additionalParams) => {
    console.log("loadReportCB is called ", reportQueryParams);
    return loadReport(userToken, reportQueryParams, additionalParams);
  };

  const openModal = () => {
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  if (true) {
    return (
      <>
        <div className="app" style={{ margin: "25px" }}>
          <div
            className="requisitionesHeader"
            style={{ marginTop: "25px", marginBottom: "25px" }}
          >
            <div style={{ marginLeft: "10px" }}>
              <div style={{fontSize:"18px",fontWeight:600,color:"grey"}}>Medicine Inventory</div>
            </div>
          </div>
          <div>
            <hr style={{ backgroundColor: "black" }} />
          </div>
          <OCTable
            reportData={reportData}
            setReportData={setReportData}
            tableConfiguration={medicineTableConfiguration}
            selectedRowKeys={selectedRowKeys}
            setSelectedRowKeys={setSelectedRowKeys}
            loadReportCallback={loadReportCB}
            // loadInitialFilterDataCallback={loadInitialFilterDataCB}
          />
        </div>
        <Modal
          size="lg"
          show={modalVisible}
          backdrop="static"
          centered
        >
          <Modal.Title
            style={{
              padding: "16px 16px 0px 16px",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <div>{extractBasedOnLanguageMod(staffForMed[0]?.medicine_name_l10n,userLanguage)}</div>
            <CloseIcon
              onClick={() => {
                closeModal();
              }}
            />
          </Modal.Title>

          <Modal.Body>
            <Container>
              <div style={{ height: "30vh", width: "100%", marginTop: 20 }}>
                <div style={{ fontWeight: "bold", display: "flex" }}>
                  <div style={{ flex: "1" }}>Staff Name</div>
                  <div style={{ flex: "1" }}>Staff Type</div>
                  <div style={{ flex: "1" }}>Balance</div>
                </div>
                {staffForMed.map((option,index) => (
                  <div key={index}>
                    <div style={{ display: "flex" }}>
                      <div style={{ flex: "1", margin: "10px 0px 10px 0px" }}>
                        {extractBasedOnLanguageMod(option?.staff_name_l10n,userLanguage)}
                      </div>
                      <div style={{ flex: "1", margin: "10px 0px 10px 0px" }}>
                        {extractBasedOnLanguageMod(option?.staff_type_l10n,userLanguage)}
                      </div>
                      <div style={{ flex: "1", margin: "10px 0px 10px 0px" }}>
                        {option?.balance + " " + unitMappingByMedId(option?.medicine_id)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Container>
          </Modal.Body>
          {/* <Modal.Footer style={{ justifyContent: 'center' }}>
                </Modal.Footer> */}
        </Modal>
      </>
    );
  } else {
    return <></>;
  }
};

export default MedicineInventory;
