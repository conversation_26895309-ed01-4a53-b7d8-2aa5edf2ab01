import {<PERSON><PERSON>, Spin, Modal, Table, Card} from "antd";
import {useState} from "react";
import { instance } from "../../../Services/api.service";
import { useDispatch, useSelector } from "react-redux";
import { clearUserToken } from "../../user/action";
import { useNavigate } from "react-router";
import ToastersService from "../../../Services/toasters.service";
import { setFreelancerMobNumber } from "../../../redux/slices/ppuu/reducers";
import { TextField, Box } from "@mui/material";
import { extractBasedOnLanguage } from "@krushal-it/common-core";
import { commonRegexes } from "../../../Utilities/Common/validation";



const CheckExistFarmer = (props)=>{
    const [mobileNumber, setMobileNumber] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const userToken = useSelector((state)=> state.user.userToken)
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [modal,contextHolder] = Modal.useModal();
    const [error, setError] = useState(false);

    const cardContentStyle = {"fontSize":"18px","color":"grey","fontWeight":600}

    // const columns = ()  =>{
    //     return [
    //     {
    //         title: "Visual Id",
    //         key: "customer_visual_id",
    //         render: (text, record) => (
    //           <div style={{color: "black" }} >
    //             {record.customer_visual_id || '-'}
    //           </div>
    //         )
    //     },
    //   {
    //     title: "Name",
    //     key: "customer_name_l10n",
    //     render: (text, record) => (
    //       <div style={{ cursor: "pointer", color: "royalblue" }}>
    //         {record?.customer_name_l10n ? record?.customer_name_l10n : "-"}
    //       </div>
    //     ),
    //   },
    //   {
    //     title: "Mobile",
    //     key: "mobile_number",
    //     render: (text, record) => (
    //       <div style={{color: "black" }} >
    //         {record.mobile_number || '-'}
    //       </div>
    //     )
    //   },
    //   {
    //     title: "Taluka",
    //     key: "taluk_name_l10n",
    //     render: (text, record) => (
    //       <div style={{color: "black" }} >
    //         {record.taluk_name_l10n || '-'}
    //       </div>
    //     )
    //   },
    //   {
    //     title: "Village",
    //     key: "village_name_l10n",
    //     render: (text, record) => (
    //       <div style={{color: "black" }} >
    //         {record.village_name_l10n || '-'}
    //       </div>
    //     )
    //   }
    // ]}

    // const dataSource = () => {
    //     return mockFarmerDetails.customer?.map((item, index) => ({
    //       key: item.customer_id,
    //       customer_visual_id: item.customer_visual_id,
    //       customer_name_l10n :extractBasedOnLanguage(item.customer_name_l10n,'ul'),
    //       mobile_number: item.mobile_number,
    //       taluk_name_l10n: extractBasedOnLanguage(item.taluk_name_l10n,'ul'),
    //       village_name_l10n :extractBasedOnLanguage(item.village_name_l10n,'ul')
    //     }));
    //   };

    // const farmerDetails =()=>{
    //     modal.warning({
    //         title: "Farmer Details",
    //         content : (
    //             <Table columns={columns()} dataSource={dataSource()} pagination={false}/>
    //         ),
    //         width : 800
    //     })
    // }

    const assignCutomerToFreelancer = async(customerId)=>{
        const payload = {
            paravet_id : props.staffId,
            farmer_id : customerId
        }
        try {
            const response = await instance({
                url : '/v2/farmer/enroll',
                method : "POST",
                data : payload,
                headers: {
                    "Content-Type": "application/json",
                    token: userToken.accessToken,
                },
            })
            if (response.status === 201) {
                if (response?.data === true) {
                    props.setRefresh(Math.random())
                    modal.success({
                        title : "Successfully Assigned !",
                        content : `This farmer is assigned to the freelancer staff, ${props?.staffName}`
                    })
                    props.setShowAddFarmerModal(false)
                }else{
                    ToastersService.failureToast("Farmer is not assigned !")
                }
            }
        } catch (error) {
            if (error?.response?.status === 401) {
                dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
            }
        }
    }

    const verifyMobileNumber = async (data)=>{
        try {
            const response = await instance({
                url : `ppu/check_farmer_exist?mobile_number=${data}&staff_id=${props.staffId}`,
                method : "GET",
                headers: {
                    "Content-Type": "application/json",
                    token: userToken.accessToken,
                }, 
            })
            setIsLoading(true)
            if (response?.status === 200) {
                setIsLoading(false)
                if (response?.data?.farmer_exist === true || response?.data?.farmer_enrolled === true) {
                    modal.confirm({
                        title : response.data?.farmer_enrolled === true ? `Farmer already enrolled` :"Farmer already exist !" ,
                        content : (
                            <div>
                                {response?.data?.farmer_enrolled === true  ?
                                `This farmer already enrolled for this staff, ${props.staffName}` :
                                 `This number already exists as a Krushal customer. Would you like to provide freelancer services for this number?`
                                  }
                                {/* <Table columns={columns()} dataSource={dataSource()} pagination={false}/> */}
                                <Card
                                    bordered={false}
                                    style={{
                                    width: 400,
                                    boxShadow: "rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px",
                                    marginTop:"7px"
                                    }}
                                >
                                {response.data.customer.map((item,index)=>{
                                    return (
                                        <div key={index} >
                                        <p style={cardContentStyle}>Farmer Details</p>
                                        <div><span style={{fontWeight:"bold"}}>Visual Id : </span> {item.customer_visual_id}</div>
                                        <div><span style={{fontWeight:"bold"}}>Name  : </span> {extractBasedOnLanguage(item.customer_name_l10n,'en')}</div>
                                        <div><span style={{fontWeight:"bold"}}>Mobile Number : </span> {item.mobile_number}</div>
                                        <div> <span style={{fontWeight:"bold"}}>Taluka : </span> {extractBasedOnLanguage(item.taluk_name_l10n,'en')}</div>
                                        <div><span style={{fontWeight:"bold"}}>Village : </span>  {extractBasedOnLanguage(item.village_name_l10n,'en')}</div>
                                        </div>
                                    )
                                })}
                                </Card>
                                {/* <div type="primary" style={{color:"blue",cursor:"pointer"}} onClick={farmerDetails}>See Farmer Details</div> */}
                                
                            </div>
                        ),
                        width : 500,
                        okText: response?.data?.farmer_enrolled === true ? "Ok" : "Assign",
                        onOk: ()=>{
                           if (response?.data?.farmer_enrolled === true) {
                            props.setShowAddFarmerModal(false)
                           } else {
                            assignCutomerToFreelancer(response?.data?.customer[0]?.customer_id)
                           }
                        }
                    })
                    // setIsModalOpen(true)
                }else{
                    props.setFarmerSteps(1)
                }
            }
        } catch (error) {
            if (error?.response?.status === 401) {
                dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
            }
        }
    }

    const verifyHandler = ()=>{
        if (!mobileNumber || mobileNumber.trim().length !== 10 || !(new RegExp(commonRegexes.IndiaMobileNumber).test(mobileNumber)) ) {
            let msg = !mobileNumber ? "Mobile Number is required !" :  "Please enter valid mobile number !"
            setError(true)
            ToastersService.failureToast(msg)
        }else{
            dispatch(setFreelancerMobNumber(mobileNumber))
            verifyMobileNumber(mobileNumber)
            setError(false)
        }
    }
    return (
        <div style={{display:"flex",justifyContent:"center",alignItems:"center"}}>
        <>
            {isLoading ? 
            (<Spin size="large" style={{marginLeft:"80px"}} />) : 
            (
            <div style={{width:"400px", display:"flex", justifyContent:"space-between", alignItems:"center"}}>
                <Box
                 component="form"
                 sx={{
                   '& .MuiTextField-root': { m: 1, width: '40ch' },
                 }}
                 noValidate={true}
                 autoComplete="off"
                >
                <TextField
                    required
                    error={error}
                    id="outlined-required"
                    label="Enter Mobile Number"
                    value={mobileNumber}
                    onChange={e =>setMobileNumber(e.target.value)}
                    type="number"
                />
                </Box>
                <Button size="large" style={{backgroundColor:"#ec6237",color:"white"}} onClick={verifyHandler}>Next</Button>
            </div>
            )}
        </>
        {contextHolder}
        </div>
    )
}

export default CheckExistFarmer;