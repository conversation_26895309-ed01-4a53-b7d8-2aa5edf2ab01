import style from "./ppu.module.css";
import { FormControl, InputLabel, Grid, Box } from "@mui/material";
import {
  OutlinedInput,
  InputAdornment,
  Select,
  MenuItem,
  TextField,
  Checkbox,
  ListItemText,
} from "@mui/material";
import { Button } from "antd";
import { useCallback, useEffect, useState } from "react";
import { createFinance, createFinanceForStaff } from "./ppuApiService";
import {useDispatch, useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import { clearUserToken } from "../../user/action";
import ToastersService from "../../../Services/toasters.service";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
const names = [
  { label: "UPI", value: "UPI"},
  { label: "Net Banking", value: "Net Banking"},
  { label: "Cash", value: "Cash" },
  { label: "Other", value: "Other" },
];
const PpuuFinance = (props) => {
  console.log("props from trans->",props)
  const [paymentMethod, setPaymentMethod] = useState([]);
  const [amount, setAmount] = useState();
  const [date, setDate] = useState();
  const [transactionId, setTransactionId] = useState();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const userToken = useSelector((state)=> state.user.userToken);
  // const createdAt = new Date("2024-01-02T05:32:37.564Z").toISOString().split("T")[0]
  // const isValidDate = new Date(createdAt).getTime() <= new Date(date).getTime()
  const handleChange = (event) => {
    const {
      target: { value },
    } = event;
    setPaymentMethod(
      // On autofill we get a stringified value.
      // typeof value === "string" ? value.split(",") : value
      value
    );
  };
  // const renderValue = (selected) => {
  //   // Map selected values to their corresponding labels
  //   const selectedLabels = selected.map((value) => {
  //     const selectedOption = names.find((option) => option.value === value);
  //     return selectedOption ? selectedOption.label : "";
  //   });

  //   return selectedLabels.join(", ");
  // };
  const resetState = () => {
    setAmount("");
    setPaymentMethod([]);
    setDate("");
    setTransactionId("");
  };
  
  // const onSubmitButtonHandler = () => {
  //   if (amount && (amount > 0 && amount?.toString().length < 16) && paymentMethod.length > 0 && transactionId && date && props.staffId && isValidDate) {
  //     const payload = {
  //       staff_id: props.staffId,
  //       amount: amount,
  //       transaction_date: date,
  //       transaction_description: {
  //         transaction_type : paymentMethod,
  //         transaction_id: transactionId,
  //       },
  //     };
  //     if (props.isTransPage) {
  //       createFinance(payload,userToken,dispatch,clearUserToken,navigate,props?.setRefresh,props?.setShowModal,resetState)
  //     }
  //     if(props.AddStaffPage){
  //     createFinanceForStaff(payload,userToken,dispatch,clearUserToken,navigate,props?.setShowAddStaffModal,resetState,props.setCurrent)
  //     }
  //   } else {
  //       if ((amount && !(amount > 0)) || !(amount && amount.toString().length < 16))  {
  //         ToastersService.failureToast("Please enter valid amount !")
  //       }else if (!isValidDate) {
  //         ToastersService.failureToast("Date should be after the staff creation date!")
  //       }
  //       else{
  //         ToastersService.failureToast("Please fill all required fields !")
  //       }
  //   }
  // };
  const createPayload = (amount, date, paymentMethod, transactionId, staffId) => {
    return {
      staff_id: staffId,
      amount: amount,
      transaction_date: date,
      transaction_description: {
        transaction_type: paymentMethod,
        transaction_id: transactionId,
      },
    };
  };
  
  const handleFinanceCreation = (payload, isTransPage, AddStaffPage, props) => {
    if (isTransPage) {
      createFinance(payload, userToken, dispatch, clearUserToken, navigate, props?.setRefresh, props?.setShowModal, resetState);
    }
    if (AddStaffPage) {
      createFinanceForStaff(payload, userToken, dispatch, clearUserToken, navigate, props?.setShowAddStaffModal, resetState, props.setCurrent);
    }
  };
  
  const onSubmitButtonHandler = () => {
    if (amount && (amount > 0 && amount?.toString().length < 16) && paymentMethod.length > 0 && transactionId && date && props.staffId) {
      const payload = createPayload(amount, date, paymentMethod, transactionId, props.staffId);
      handleFinanceCreation(payload, props.isTransPage, props.AddStaffPage, props);
    } else {
      if ((amount && !(amount > 0)) || !(amount && amount.toString().length < 16)) {
        ToastersService.failureToast("Please enter valid amount !");
      } else {
        ToastersService.failureToast("Please fill all required fields !");
      }
    }
  };

  return (
    <div className={style.main}>
      <div>
        {/* <h4 style={{ padding: "10px" }}>Finance</h4> */}
        {/* <div style={{ textAlign: "center", marginTop: "20px" }}>
          <div style={{ fontSize: "2rem", fontWeight: 800, color: "purple" }}>
            Your Wallet Balance
          </div>
          <div style={{ fontSize: "1.5rem", fontWeight: 800 }}>
            <span style={{ fontSize: "2rem", fontWeight: 600 }}>₹</span>{balance?.balance ? balance?.balance : 0 }
          </div>
        </div> */}
        <div style={{padding:"10px",fontSize:"18px",color:"grey",fontWeight:600,textAlign:"center"}}>Add Money to Wallet</div>
        <Box sx={{ width: "100%", padding: "10px" }}>
          <Grid
            container
            rowSpacing={3}
            columnSpacing={{ xs: 1, sm: 2, md: 3 }}
          >
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel htmlFor="outlined-adornment-amount">
                  Amount*
                </InputLabel>
                <OutlinedInput
                  id="outlined-adornment-amount"
                  startAdornment={
                    <InputAdornment position="start">₹</InputAdornment>
                  }
                  label="Amount"
                  onChange={(e) => setAmount(e.target.value)}
                  type="number"
                  value={amount}
                />
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              {/* <FormControl fullWidth>
                <InputLabel id="demo-multiple-checkbox-label">
                  Choose Payment Method
                </InputLabel>
                <Select
                  labelId="demo-multiple-checkbox-label"
                  id="demo-multiple-checkbox"
                  multiple
                  value={paymentMethod}
                  onChange={handleChange}
                  input={<OutlinedInput label="Tag" />}
                  renderValue={renderValue}
                  MenuProps={MenuProps}
                >
                  {names.map((name) => (
                    <MenuItem key={name.label} value={name.value}>
                      <Checkbox
                        checked={paymentMethod.indexOf(name.value) > -1}
                      />
                      <ListItemText primary={name.label} />
                    </MenuItem>
                  ))}
                </Select>
              </FormControl> */}
              <FormControl fullWidth>
                <InputLabel id="demo-simple-select-label">Choose Payment Method*</InputLabel>
                <Select
                  labelId="demo-simple-select-label"
                  id="demo-simple-select"
                  value={paymentMethod}
                  label="Choose Payment Method"
                  onChange={handleChange}
                >
                  {names.map((item,index)=>{
                    return (
                      <MenuItem key={index} value={item.value}>{item.label}</MenuItem>
                    )
                  })}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <TextField label="Transaction Ref ID*" id="fullWidth" fullWidth value={transactionId} onChange={e =>setTransactionId(e.target.value)} />
            </Grid>
            <Grid item xs={6}>
              <TextField label="Select Transaction Date*" type="date" focused value={date} fullWidth onChange={e =>setDate(e.target.value)} />
            </Grid>
          </Grid>
        </Box>
        <div className={style.main}>
          <Button type="primary" style={{ marginTop: "20px" }} onClick={onSubmitButtonHandler}>
            Submit
          </Button>
        </div>
      </div>
    </div>
  );
};
export default PpuuFinance;
