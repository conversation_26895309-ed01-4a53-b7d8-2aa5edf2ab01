// import { createColumnsFromTableConfiguration, createPageParamsFromURLString, createURLQueryStringFromPageParams, OCTable, OCTableHeader, initialLoad, omniSearch, reset, setUpColumnConfiguration } from "../../Common/AntTableHelper";
import React, { useEffect, useState } from "react";
import { extractBasedOnLanguage } from "@krushal-it/common-core";
import { useNavigate } from "react-router-dom";
import { Tag, Table, Spin } from "antd";

const columns = (props, navigateStaffDetails) => {
  return [
    {
      title: "Name",
      key: "staff_name_l10n",
      render: (text, record) => (
        <div
          style={{ cursor: "pointer", color: "royalblue" }}
          onClick={() => {
            if (record && record.key) {
              if (props && props.checkStaffInfo) {
                props.checkStaffInfo(record.key);
              } else {
                navigateStaffDetails(record.key);
              }
            }
          }}
        >
          {record.staff_name_l10n || "-"}
        </div>
      ),
    },
    {
      title: "Email",
      key: "email_address",
      render: (text, record) => (
        <div style={{ color: "black" }}>{record.email_address || "-"}</div>
      ),
    },
    {
      title: "Role",
      key: "staff_type_l10n",
      render: (text, record) => (
        <div style={{ color: "black" }}>{record.staff_type_l10n || "-"}</div>
      ),
    },
    {
      title: "Mobile",
      key: "mobile_number",
      render: (text, record) => (
        <div style={{ color: "black" }}>{record.mobile_number || "-"}</div>
      ),
    },
    {
      title: "PAN",
      key: "staff_pan",
      render: (text, record) => (
        <div style={{ color: "black" }}>{record.staff_pan || "-"}</div>
      ),
    },
    {
      title: "Aadhar No.",
      key: "staff_aadhar",
      render: (text, record) => (
        <div style={{ color: "black" }}>{record.staff_aadhar || "-"}</div>
      ),
    },
    {
      title: "Status",
      key: "active",
      render: (text, record) => {
        let color;
        if (record?.active === 1000100001) {
          color = "green";
        } else {
          color = "volcano";
        }
        return (
          <Tag color={color}>
            {/* {record.active === 1000100001 ? "ACTIVE":"INACTIVE"} */}
            {record?.active === 1000100001 ? "ACTIVE" : ""}
            {record?.active === 1000100002 ? "INACTIVE" : ""}
          </Tag>
        );
      },
    },
  ];
};
const PpuListTable = (props) => {
  console.log(props);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  // const [searchParams] = useSearchParams()
  // const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  // const userToken = useSelector((state)=>state.user.userToken);
  // const [currentPage, setCurrentPage] = useState(tableConfiguration.getAllDataAtOnce === true ? -1 : 1);
  // const [pageSize, setPageSize] = useState(tableConfiguration.disablePagination === true ? - 1 : tableConfiguration.defaultPageSize);
  // const [reportData, setReportData] = useState([]);
  // const [reportRowCount, setReportRowCount] = useState(-1);
  // const [columnFilters, setColumnFilters] = useState({});
  // const [globalSearch, setGlobalSearch] = useState('');
  // const [sorting, setSorting] = useState({});
  // const [tableKey, setTableKey] = useState(0)
  // const [selectedRowKeys, setSelectedRowKeys] = useState([])
  // const [filterValuesByKey, setFilterValuesByKey] = useState({})

  // tableConfiguration.pageParams = pageParams

  // tableConfiguration.resetPaginationPageNumberFunction = setCurrentPage
  // tableConfiguration.filterValuesByKey = filterValuesByKey
  // tableConfiguration.loadReport = props?.loadReport
  // tableConfiguration.loadFilters = []
  // // tableConfiguration.userToken = userToken

  // tableConfiguration.setTableKey = setTableKey
  // tableConfiguration.tableKey = tableKey

  // tableConfiguration.setColumnFilters = setColumnFilters
  // tableConfiguration.columnFilters = columnFilters
  // tableConfiguration.setSorting = setSorting
  // tableConfiguration.sorting = sorting
  // tableConfiguration.setGlobalSearch = setGlobalSearch
  // tableConfiguration.globalSearch = globalSearch
  // tableConfiguration.setCurrentPage = setCurrentPage
  // tableConfiguration.currentPage = currentPage
  // tableConfiguration.setPageSize = setPageSize
  // tableConfiguration.pageSize = pageSize

  // tableConfiguration.setReportData = setReportData
  // tableConfiguration.reportData = reportData
  // tableConfiguration.setReportRowCount = setReportRowCount
  // tableConfiguration.reportRowCount = reportRowCount

  // tableConfiguration.filterValuesByKey = filterValuesByKey
  // tableConfiguration.setFilterValuesByKey = setFilterValuesByKey

  // useEffect(()=>{
  //   initialLoad(tableConfiguration())
  // },[])
  const navigateStaffDetails = (staffId) => {
    navigate(`/dashboard/staffs/${staffId}`);
  };

  const dataSource = () => {
    return props.loadReport?.map((item) => {
      const dataSourceItem = {
        key: item?.staff_id,
        staff_name_l10n: item?.staff_name_l10n
          ? extractBasedOnLanguage(item?.staff_name_l10n, "ul")
          : "-",
        email_address: item.email_address ? item.email_address : "N/A",
        staff_type_l10n: item?.staff_type_l10n
          ? extractBasedOnLanguage(item?.staff_type_l10n, "ul")
          : "-",
        mobile_number: item?.mobile_number || "-",
        staff_pan: item?.staff_pan || "-",
        staff_aadhar: item?.staff_aadhar || "-",
        active: item?.active,
      };
      return dataSourceItem;
    });
  };
  useEffect(() => {
    if (dataSource().length >= 0) {
      setLoading(false);
    }
  }, []);
  return (
    <div>
      <div
        style={{
          padding: "10px",
          fontSize: "18px",
          color: "grey",
          fontWeight: 600,
        }}
      >
        Count : {props?.loadReport?.length} Staffs
      </div>
      <Table
        columns={columns(props, navigateStaffDetails)}
        dataSource={dataSource()}
        loading={loading}
      />
    </div>
  );
};

export default PpuListTable;
