import { Box, TextField, Button, IconButton } from "@mui/material";
import { useState, useCallback } from "react";
import ToastersService from "../../../Services/toasters.service";
import { instance } from "../../../Services/api.service";
import { useSelector, useDispatch } from "react-redux";
import { clearUserToken } from "../../user/action";
import { useNavigate } from "react-router-dom";
import AddFarmer1 from "../Farmers/AddFarmer/AddFarmer1";
import AddCattle1 from "../Cattle/AddCattle/AddCattle1";
// import {
//   createColumnsFromTableConfiguration,
//   OCTable,
// } from "../../Common/AntTableHelper";
import { extractBasedOnLanguage } from "@krushal-it/common-core";
import PpuFarmerTable from "./PpuFarmerTable";
import CattleAntdTable from "./CattleAntdTable";
import AddTask from "../Tasks/AddTask/AddTask";
import { Modal, Tag, Steps, Button as AntdButton, Table } from "antd";
import AddIcon from "@mui/icons-material/Add";
import { UserOutlined, PlusOutlined } from "@ant-design/icons";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
// import {Table} from "react-bootstrap";
import moment from "moment";
import PastActivitiesPpu from "./PastActivitesPpu";
import CheckExistFarmer from "./CheckExistFarmer";

const {CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, withAuthorization} = require('../../user/authorize')

const tableConfiguration = (loadPPUUFarmers) => ({
  enableSelection: false,
  defaultPageSize: 10,
  enableHorizontalScrolling: false,
  // getAllDataAtOnce: true,
  disablePagination: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: "staff_id",
  columns: [
    {
      title: "Name",
      key: "staff_name_l10n",
      render: (text, record) => (
        <div
          style={{ cursor: "pointer", color: "royalblue" }}
          onClick={loadPPUUFarmers}
        >
          {text ? text : "-"}
        </div>
      ),
    },
    {
      title: "Email",
      key: "email_address",
    },
    {
      title: "Role",
      key: "staff_type_l10n",
    },
    {
      title: "Mobile",
      key: "mobile_number",
    },
    {
      title: "PAN",
      key: "staff_pan",
    },
    {
      title: "Aadhar No.",
      key: "staff_aadhar",
    },
    // {
    //   title: "Status",
    //   key: "active",
    // },
  ],
  columnConfiguration: {},
})

const columns = (loadPPUUFarmers) => {
  return [
    {
      title: "Name",
      key: "staff_name_l10n",
      render: (text, record) => (
        <div
          style={{ cursor: "pointer", color: "royalblue" }}
          onClick={loadPPUUFarmers}
        >
          {record?.staff_name_l10n ? record?.staff_name_l10n : "-"}
        </div>
      ),
    },
    {
      title: "Email",
      key: "email_address",
      render: (text, record) => (
        <div style={{ color: "black" }}>{record.email_address || "-"}</div>
      ),
    },
    {
      title: "Role",
      key: "staff_type_l10n",
      render: (text, record) => (
        <div style={{ color: "black" }}>{record.staff_type_l10n || "-"}</div>
      ),
    },
    {
      title: "Mobile",
      key: "mobile_number",
      render: (text, record) => (
        <div style={{ color: "black" }}>{record.mobile_number || "-"}</div>
      ),
    },
    {
      title: "PAN",
      key: "staff_pan",
      render: (text, record) => (
        <div style={{ color: "black" }}>{record.staff_pan || "-"}</div>
      ),
    },
    {
      title: "Aadhar No.",
      key: "staff_aadhar",
      render: (text, record) => (
        <div style={{ color: "black" }}>{record.staff_aadhar || "-"}</div>
      ),
    },
    {
      title: "Status",
      key: "active",
      render: (text, record) => {
        let color;
        if (record?.active === 1000100001) {
          color = "green";
        } else {
          color = "volcano";
        }
        return (
          <Tag color={color}>
            {/* {record.active === 1000100001 ? "ACTIVE":"INACTIVE"} */}
            {record?.active === 1000100001 ? "ACTIVE" : ""}
            {record?.active === 1000100002 ? "INACTIVE" : ""}
          </Tag>
        );
      },
    },
  ];
};

const PpuRaiseSr = () => {
  const [mobileNumber, setMobileNumber] = useState();
  const [userList, setUserList] = useState([]);
  const [showTable, setShowTable] = useState(true);
  const [showFarmerTab, setShowFarmerTab] = useState(false);
  const [showCattleTab, setShowCattleTab] = useState(false);
  const [showTaskTab, setShowTaskTab] = useState(false);
  const [showAddFarmerModal, setShowAddFarmerModal] = useState(false);
  const [showAddCattleModal, setShowAddCattleModal] = useState(false);
  const [refresh, setRefresh] = useState(0);
  const [farmerName, setFarmerName] = useState("");
  const [current, setCurrent] = useState(0);

  const [ppuFarmerId, setppuFarmerId] = useState();
  const [cattleId, setCattleId] = useState();

  const [farmerSteps, setFarmerSteps] = useState(0);

  const dispatch = useDispatch();
  const userToken = useSelector((state) => state.user.userToken);
  const navigate = useNavigate();
  const items = [
    {
      title: "Select Staff",
    },
    {
      title: "Select Farmer",
    },
    {
      title: "Select Cattle",
    },
    {
      title: "Add Task",
    },
  ];
  const addFarmerItems = [
    {
      title: "Mobile Number",
    },
    {
      title: "Personal Details",
    },
  ];
  const getPpuStaff = useCallback(
    async (params) => {
      try {
        const response = await instance({
          url: "/staffs/all",
          method: "POST",
          data: params,
          headers: {
            "Content-Type": "application/json",
            token: userToken.accessToken,
          },
        });
        if (response?.status === 201) {
          setUserList(response?.data?.data);
          setMobileNumber("");
          if (response?.data?.data?.length === 0) {
            ToastersService.failureToast("No staff was found for this number.");
          }
        }

        // setFarmersListLoader(false);
      } catch (error) {
        console.log(error);
        if (error?.response?.status === 401) {
          dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
        }
      }
    },
    [userToken]
  );

  const findBtnHandler = () => {
    if (!mobileNumber || mobileNumber.trim().length !== 10) {
      ToastersService.failureToast("Enter a valid mobile number!");
    } else {
      let ppuFilterParams = {
        f_staff_mobile: mobileNumber,
        f_staff_type: 1000230006,
      };
      getPpuStaff(ppuFilterParams);
    }
  };

  const loadPPUUFarmers = () => {
    setShowFarmerTab(true);
    setShowTable(false);
    setCurrent(1);
  };
  const handleCancelAddFarmerModal = () => {
    setShowAddFarmerModal(false);
  };
  const handleCancelAddCattleModal = () => {
    setShowAddCattleModal(false);
  };
  const modifiedDataSource = userList?.map((item) => {
    Object.keys(item).forEach((key) => {
      if (item[key] && typeof item[key] === "object") {
        item[key] = extractBasedOnLanguage(item[key], "ul");
      }
    });
    return item;
  });

  const dataSource = () => {
    return userList?.map((item, index) => {
      const dataSourceItem = {
        key: item?.staff_id,
        staff_name_l10n: item?.staff_name_l10n
          ? extractBasedOnLanguage(item?.staff_name_l10n, "ul")
          : "-",
        email_address: item.email_address ? item.email_address : "N/A",
        staff_type_l10n: item?.staff_type_l10n
          ? extractBasedOnLanguage(item?.staff_type_l10n, "ul")
          : "-",
        mobile_number: item?.mobile_number || "-",
        staff_pan: item?.staff_pan || "-",
        staff_aadhar: item?.staff_aadhar || "-",
        active: item?.active,
      };
      return dataSourceItem;
    });
  };
  console.log(ppuFarmerId);
  return (
    <>
      <div className="my-3" style={{ display: "flex", alignItems: "center" }}>
        <div>
          <p
            className="m-0"
            style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
          >
            Raise Service Request
          </p>
        </div>
      </div>

      <Steps items={items} current={current} />
      <hr />
      {showTable && (
        <div style={{ display: "flex", marginTop: "20px" }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              marginLeft: "250px",
            }}
          >
            <Box style={{ minWidth: "160px" }}></Box>
            <TextField
              style={{ minWidth: "300px" }}
              value={mobileNumber}
              onChange={(e) => setMobileNumber(e.target.value)}
              placeholder="Enter Mobile Number"
            />
            <div className="ms-4">
              <Button
                variant="primary"
                style={{
                  background: "#673AB7",
                  color: "#fff",
                  border: "none",
                  height: "54px",
                }}
                onClick={findBtnHandler}
              >
                <i className="fa fa-search" aria-hidden="true"></i> Find
              </Button>
            </div>
          </div>
        </div>
      )}

      {userList.length > 0 && showTable && (
        <div style={{ marginTop: "50px" }}>
          <Table columns={columns(loadPPUUFarmers)} dataSource={dataSource()} />
        </div>
      )}

      {showFarmerTab === true && (
        <div>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <IconButton
              color="primary"
              aria-label="back"
              onClick={() => {
                setShowFarmerTab(false);
                setShowTable(true);
                setCurrent(0);
              }}
            >
              <ArrowBackIcon />
            </IconButton>

            <Tag icon={<UserOutlined />} color="#55acee">
              {userList[0]?.staff_name_l10n}
            </Tag>

            <AntdButton
              style={{ backgroundColor: "#ec6237", color: "white" }}
              icon={<PlusOutlined />}
              size="large"
              onClick={() => {
                setShowAddFarmerModal(true);
                setFarmerSteps(0);
              }}
            >
              Add Farmer
            </AntdButton>
          </div>

          <PpuFarmerTable
            staffId={userList[0]?.staff_id}
            setShowCattleTab={setShowCattleTab}
            setShowFarmerTab={setShowFarmerTab}
            setppuFarmerId={setppuFarmerId}
            ppuFarmerId={ppuFarmerId}
            setFarmerName={setFarmerName}
            setCurrent={setCurrent}
            refresh={refresh}
          />
        </div>
      )}
      {showCattleTab && (
        <div>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <IconButton
              color="primary"
              aria-label="back"
              onClick={() => {
                setShowCattleTab(false);
                setShowFarmerTab(true);
              }}
            >
              <ArrowBackIcon />
            </IconButton>
            <Tag icon={<UserOutlined />} color="#55acee">
              {farmerName}
            </Tag>
            <AntdButton
              style={{ backgroundColor: "#ec6237", color: "white" }}
              icon={<PlusOutlined />}
              size="large"
              onClick={() => setShowAddCattleModal(true)}
            >
              Add Cattle
            </AntdButton>
          </div>

          <CattleAntdTable
            ppuFarmerId={ppuFarmerId}
            setCattleId={setCattleId}
            setShowCattleTab={setShowCattleTab}
            setShowTaskTab={setShowTaskTab}
            refresh={refresh}
            setCurrent={setCurrent}
            staffId={userList[0]?.staff_id}
          />
        </div>
      )}
      {showTaskTab && (
        <div>
          <IconButton
            color="primary"
            aria-label="back"
            onClick={() => {
              setShowTaskTab(false);
              setShowCattleTab(true);
            }}
          >
            <ArrowBackIcon />
          </IconButton>
          <AddTask
            isPPUU={true}
            farmerId={ppuFarmerId}
            cattleId={cattleId}
            staffId={userList[0]?.staff_id}
          />
          <hr />
          <PastActivitiesPpu
            cattleId={cattleId}
            staffId={userList[0]?.staff_id}
          />
        </div>
      )}
      <Modal
        open={showAddFarmerModal}
        onCancel={handleCancelAddFarmerModal}
        footer={null}
        width={1000}
      >
        <div
          style={{
            padding: "10px",
            fontSize: "18px",
            color: "grey",
            fontWeight: 600,
            textAlign: "start",
          }}
        >
          Add Farmer
        </div>
        <div style={{ width: "50%" }}>
          <Steps items={addFarmerItems} current={farmerSteps} />
        </div>
        <br />
        {farmerSteps === 0 && (
          <CheckExistFarmer
            key={Date.now()}
            setFarmerSteps={setFarmerSteps}
            setShowAddFarmerModal={setShowAddFarmerModal}
            staffId={userList[0]?.staff_id}
            staffName={userList[0]?.staff_name_l10n}
            setRefresh={setRefresh}
          />
        )}
        {farmerSteps === 1 && (
          <AddFarmer1
            key={Date.now()}
            isPPUU={true}
            staffId={userList[0]?.staff_id}
            setppuFarmerId={setppuFarmerId}
            setShowAddFarmerModal={setShowAddFarmerModal}
          />
        )}
      </Modal>
      <Modal
        title="Add Cattle"
        open={showAddCattleModal}
        onCancel={handleCancelAddCattleModal}
        footer={null}
        width={1000}
      >
        {showCattleTab === true && (
          <AddCattle1
            key={Date.now()}
            isPPUU={true}
            ppuFarmerId={ppuFarmerId}
            setRefresh={setRefresh}
            setShowAddCattleModal={setShowAddCattleModal}
            staffId={userList[0]?.staff_id}
          />
        )}
      </Modal>
    </>
  );
}

// export default PpuRaiseSr
export default withAuthorization(PpuRaiseSr, [CS_TEAM, VET_OPS, CENTRAL_VET])