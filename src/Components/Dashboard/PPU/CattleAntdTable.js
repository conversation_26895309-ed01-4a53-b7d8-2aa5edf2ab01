// import { OCTable, createColumnsFromTableConfiguration } from "../../Common/AntTableHelper";
import {useDispatch, useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import { clearUserToken } from "../../user/action";
import {useEffect, useState, useRef} from "react";
import { getCattlesByFarmerId } from "./ppuApiService";
import { extractBasedOnLanguage } from "@krushal-it/common-core";
import { Table, Modal, Button, Tag} from "antd";
import { instance } from "../../../Services/api.service";
import getColumnSearchProps from "../../../Utilities/Common/TableUtils";

function extractBasedOnLanguageMod(e, t) {
  if (t === undefined) {
    t = "en";
  }
  if (e === null || e === undefined) {
    return "";
  }
  if (typeof e === "object") {
    if (e.ul !== undefined && e.ul !== null) {
      return e.ul;
    } else if (e[t] !== undefined && e[t] !== null) {
      return e[t];
    } else if (e.en !== undefined && e.en !== null) {
      return e.en;
    } else {
      return "";
    }
  } else {
    return e;
  }
}


const CattleAntdTable = (props)=>{
    const navigate = useNavigate();
    const userToken = useSelector((state)=> state.user.userToken);
    const dispatch = useDispatch();
    const [cattleList, setCattleList] = useState([]);
    const [modal,contextHolder] = Modal.useModal();
    const [refresh, setRefresh] = useState(0);

     // search filter functions starts here
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef(null);

  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters, confirm, dataIndex) => {
    clearFilters();
    confirm()
  };

    const cattleEnrollement = async (id)=>{
      const data = {
        customer_id : props.ppuFarmerId,
        paravet_id : props.staffId,
        animal_id : id
      }
      try {
        const response = await instance({
          url : "v2/animal/enroll",
          data : data,
          method : "POST",
          headers: {
            "Content-Type": "application/json",
            token: userToken.accessToken,
          },
        })
        if (response?.status === 201) {
          modal.success({
            title : "Cattle enrollement has been done !",
            content : "Now you can continue freelancer service for this cattle"
          })
          setRefresh(Math.random())
          return response?.data
        }
      } catch (error) {
        if (error?.response?.status === 401) {
          dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
        }
      }
    }
    const enrollHandler = (id)=>{
      modal.confirm({
        title : "Enroll Cattle !",
        content : "Do you want to enroll this cattle ?",
        okText : "Ok",
        onOk : ()=>{
          cattleEnrollement(id)
        }
      })
    }
    useEffect(()=>{
      props.setCurrent(2)
        getCattlesByFarmerId(props.ppuFarmerId,userToken,dispatch,clearUserToken,navigate,setCattleList)
    },[props.refresh, refresh])

    const onClickHandler = (cattleId,associated_paravet_id)=>{
        if (associated_paravet_id !== null) {
          props.setCattleId(cattleId)
          props.setShowCattleTab(false)
          props.setShowTaskTab(true)
          props.setCurrent(3)
        }else{
          modal.warning({
            title : "Cattle is not enrolled !",
            content : "This cattle is not enrolled for this freelancer customer please enroll to continue services."
          })
        }
    }
    const columns = (onClickHandler,enrollHandler)=>{
      return [
        {
            title: "Visual Id",
            key: "animal_visual_id",
            render: (text, record) => (
                <div style={{ cursor: "pointer", color: "royalblue" }} onClick={()=>onClickHandler(record.key,record.associated_paravet_id)}>
                  {record?.animal_visual_id ? record?.animal_visual_id : "-"}
                </div>
              ),
        },
      {
        title: "Ear Tag",
        key: "ear_tag_1",
        ...getColumnSearchProps("ear_tag_1",handleSearch,handleReset,searchedColumn,setSearchedColumn,searchInput),
        render: (text, record) => (
          <div style={{color: "black" }} >
            {record.ear_tag_1 || '-'}
          </div>
        )
        
      },
      {
        title: "Type",
        key: "animal_type",
        render: (text, record) => (
          <div style={{color: "black" }} >
            {record.animal_type || '-'}
          </div>
        )
      },
      {
        title: "Breed",
        key: "animal_breed_1",
        render: (text, record) => (
          <div style={{color: "black" }} >
            {record.animal_breed_1 || '-'}
          </div>
        )
      },
      {
        title: "Subscription Plan",
        key: "subscription_plan_1",
        render: (text, record) => {
          let color = record.subscription_plan_1 ? "geekblue" : "volcano"
          return (
          <Tag color={color} >
            {/* {text} */}
            {record?.subscription_plan_1 ? record?.subscription_plan_1 : "No subscription plan" }
          </Tag>
        )}
      },
      {
        title: "Action",
        key: "",
        render: (text, record) => (
          <Button onClick={()=>enrollHandler(record.key)} type="primary" disabled={record?.associated_paravet_id !== null}>
            Enroll
          </Button>
        )
      }
    ]
    }
    // const columns = createColumnsFromTableConfiguration(tableConfiguration(onClickHandler));
    const modifiedDataSource = cattleList?.map((item) => {
        Object.keys(item).forEach((key) => {
          if (item[key] && typeof item[key] === "object") {
            item[key] = extractBasedOnLanguage(item[key], "ul");
          }
        });
        return item;
      });
    const dataSource = ()=>{
      return cattleList?.map((item,index)=>({
        key : item?.animal_id,
        animal_visual_id: item?.animal_visual_id,
        ear_tag_1 : item?.ear_tag_1,
        animal_type : item?.animal_type,
        animal_breed_1 : item?.animal_breed_1,
        subscription_plan_1 : extractBasedOnLanguageMod(item?.subscription_plan_1,'en'),
        associated_paravet_id : item?.associated_paravet_id

      }))
    }
    return (
        <>
          <div style={{padding:"10px",fontSize:"18px",color:"grey",fontWeight:600}}>Count : {cattleList?.length} Cattles</div>
            <Table  columns={columns(onClickHandler,enrollHandler)} dataSource={dataSource()}/>
            {contextHolder}
        </>
    )
}

export default CattleAntdTable;