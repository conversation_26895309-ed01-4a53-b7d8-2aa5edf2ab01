.main {
    display: flex;
    justify-content: center;
    align-items: center;
}
.box_container {
    height: 380px;
    width: 60%;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    border-radius: 16px;
}


.trans_main{
    /* height: 100vh; */
    width: 100%;
}
.divider_1{
    height: 150px;
    background: purple;
    position: relative;
}

.trans_card{
    position: absolute;
    height: 130px;
    width: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    left: 30%;
    top: 60%;
}

.divider_2{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 100px;
}
.credit_his{
    height: 500px;
    width: 90%;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    border-radius: 12px;
    overflow-y: scroll;
}
.trans_add_btn{
    position: absolute;
    right: 0;
    bottom: 0;
    padding: 10px;
}
.inner_trans_card{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 60%;
    /* height: 50px; */
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    padding: 20px;
    margin-top: 10px;
    border-radius: 6px;
}

.trans_round {
    height: 50px;
    width: 50px;
    border-radius: 50%;
    background-color: cyan;
    text-align: center;
}

.font_balance {
    font-size: 1.5rem;
    font-weight: 800;
}
.font_balance_low_balance {
    font-size: 1.5rem;
    font-weight: 800;
    color: red;
}
