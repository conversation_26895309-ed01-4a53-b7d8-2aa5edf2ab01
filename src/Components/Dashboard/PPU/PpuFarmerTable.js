// import {createColumnsFromTableConfiguration,OCTable} from "../../Common/AntTableHelper";
import { extractBasedOnLanguage } from "@krushal-it/common-core";
import { getPpuFarmerByStaffId } from "./ppuApiService";
import {useEffect, useState, useRef} from "react";
import {useDispatch, useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import { clearUserToken } from "../../user/action";
import {Table} from "antd";
import getColumnSearchProps from "../../../Utilities/Common/TableUtils";


// const tableConfiguration = (onClickHandler)=> ({
//     enableSelection: false,
//     defaultPageSize: 10,
//     enableHorizontalScrolling: false,
//     // getAllDataAtOnce: true,
//     disablePagination: true,
//     dataTransformationRules: {
//       // staff_filter_values: 'bdm_filter_values',
//       // staff_name: 'bdm'
//     },
//     rowKey: "customer_id",
//     columns: [
//         {
//             title: "Visual Id",
//             key: "customer_visual_id",
//         },
//       {
//         title: "Name",
//         key: "customer_name_l10n",
//         render: (text, record) => (
//           <div style={{ cursor: "pointer", color: "royalblue" }} onClick={()=>onClickHandler(record.key,record.customer_name_l10n)}>
//             {text ? text : "-"}
//           </div>
//         ),
//       },
//       {
//         title: "Mobile",
//         key: "mobile_number",
//       },
//       {
//         title: "Taluka",
//         key: "taluk_name_l10n",
//       },
//       {
//         title: "Village",
//         key: "village_name_l10n",
//       }
//     ],
//     columnConfiguration: {},
//   });

//   const columns = (onClickHandler)  =>{
//     return [
//     {
//         title: "Visual Id",
//         key: "customer_visual_id",
//         render: (text, record) => (
//           <div style={{color: "black" }} >
//             {record.customer_visual_id || '-'}
//           </div>
//         )
//     },
//   {
//     title: "Name",
//     key: "customer_name_l10n",
//     ...getColumnSearchProps("customer_name_l10n",handleSearch,handleReset,searchedColumn,setSearchedColumn,searchInput),
//     render: (text, record) => (
//       <div style={{ cursor: "pointer", color: "royalblue" }} onClick={()=>onClickHandler(record.key,record.customer_name_l10n)}>
//         {record?.customer_name_l10n ? record?.customer_name_l10n : "-"}
//       </div>
//     ),
//   },
//   {
//     title: "Mobile",
//     key: "mobile_number",
//     render: (text, record) => (
//       <div style={{color: "black" }} >
//         {record.mobile_number || '-'}
//       </div>
//     )
//   },
//   {
//     title: "Taluka",
//     key: "taluk_name_l10n",
//     render: (text, record) => (
//       <div style={{color: "black" }} >
//         {record.taluk_name_l10n || '-'}
//       </div>
//     )
//   },
//   {
//     title: "Village",
//     key: "village_name_l10n",
//     render: (text, record) => (
//       <div style={{color: "black" }} >
//         {record.village_name_l10n || '-'}
//       </div>
//     )
//   },
//   {
//     title : "Cattle Count",
//     key : "cattle_count",
//     render : (text,record)=>(
//       <div style={{color: "black" }} >
//         {record.cattle_count || '-'}
//       </div>
//     )
//   }
// ]}

const PpuFarmerTable = (props)=>{
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const userToken = useSelector((state)=> state.user.userToken);
    const [farmerList, setFarmerList] = useState([]);

    // search filter functions starts here
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef(null);

  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters, confirm, dataIndex) => {
    clearFilters();
    confirm()
  };

    useEffect(()=>{
      props.setCurrent(1)
      getPpuFarmerByStaffId(
        props.staffId,
        userToken,
        dispatch,
        clearUserToken,
        navigate,
        setFarmerList
      );
    },[props.ppuFarmerId,props.refresh])

    const onClickHandler = (farmerId,name)=>{
      props.setShowCattleTab(true)
      props.setShowFarmerTab(false)
      props.setppuFarmerId(farmerId)
      props.setFarmerName(name)
      props.setCurrent(2)
    }

    const columns = ()  =>{
      return [
      {
          title: "Visual Id",
          key: "customer_visual_id",
          render: (text, record) => (
            <div style={{color: "black" }} >
              {record.customer_visual_id || '-'}
            </div>
          )
      },
    {
      title: "Name",
      key: "customer_name_l10n",
      ...getColumnSearchProps("customer_name_l10n",handleSearch,handleReset,searchedColumn,setSearchedColumn,searchInput),
      render: (text, record) => (
        <div style={{ cursor: "pointer", color: "royalblue" }} onClick={()=>onClickHandler(record.key,record.customer_name_l10n)}>
          {record?.customer_name_l10n ? record?.customer_name_l10n : "-"}
        </div>
      ),
    },
    {
      title: "Mobile",
      key: "mobile_number",
      ...getColumnSearchProps("mobile_number",handleSearch,handleReset,searchedColumn,setSearchedColumn,searchInput),
      render: (text, record) => (
        <div style={{color: "black" }} >
          {record.mobile_number || '-'}
        </div>
      )
    },
    {
      title: "Taluka",
      key: "taluk_name_l10n",
      render: (text, record) => (
        <div style={{color: "black" }} >
          {record.taluk_name_l10n || '-'}
        </div>
      )
    },
    {
      title: "Village",
      key: "village_name_l10n",
      render: (text, record) => (
        <div style={{color: "black" }} >
          {record.village_name_l10n || '-'}
        </div>
      )
    },
    {
      title : "Cattle Count",
      key : "cattle_count",
      render : (text,record)=>(
        <div style={{color: "black" }} >
          {record.cattle_count || '-'}
        </div>
      )
    }
  ]}
    // const columns = createColumnsFromTableConfiguration(tableConfiguration(onClickHandler));
    const modifiedDataSource = farmerList?.map((item) => {
        Object.keys(item).forEach((key) => {
          if (item[key] && typeof item[key] === "object") {
            item[key] = extractBasedOnLanguage(item[key], "ul");
          }
        });
        return item;
      });
      const dataSource = () => {
        return farmerList?.map((item, index) => ({
          key: item.customer_id,
          customer_visual_id: item.customer_visual_id,
          customer_name_l10n :extractBasedOnLanguage(item.customer_name_l10n,'ul'),
          mobile_number: item.mobile_number,
          taluk_name_l10n: extractBasedOnLanguage(item.taluk_name_l10n,'ul'),
          village_name_l10n :extractBasedOnLanguage(item.village_name_l10n,'ul'),
          cattle_count : item?.cattle_count
        }));
      };
    return (
        <div>
            {/* <OCTable
                // tableKey={tableKey}
            columns={columns}
            reportData={modifiedDataSource}
            tableConfiguration={tableConfiguration}
            // selectedRowKeys={selectedRowKeys}
            // setSelectedRowKeys={setSelectedRowKeys}
            /> */}
            <div style={{padding:"10px",fontSize:"18px",color:"grey",fontWeight:600}}>Count : {farmerList?.length} Farmers</div>
            <Table columns={columns(onClickHandler)} dataSource={dataSource()} />
        </div>
    )
}

export default PpuFarmerTable;
