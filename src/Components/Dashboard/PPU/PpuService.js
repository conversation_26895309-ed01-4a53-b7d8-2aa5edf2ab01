import { useState, useEffect, useCallback, createContext } from "react";
import ToastersService from "../../../Services/toasters.service";
import PpuListTable from "./PpuListTable";
import { <PERSON><PERSON>, But<PERSON> } from "antd";
import { ExclamationCircleOutlined, UserAddOutlined } from "@ant-design/icons";
import AddStaff from "../Staffs/AddStaff/AddStaff";
import { instance } from "../../../Services/api.service";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { clearUserToken } from "../../user/action";
import { IconButton, TextField, Box } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import PersonAddAlt1RoundedIcon from "@mui/icons-material/PersonAddAlt1Rounded";
import { setFinanceTabVisible } from "../../../redux/slices/ppuu/reducers";

const {CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, CENTRAL_VET, VET_LEADERSHIP, VET_OPS, SALES_OPS, withAuthorization} = require('../../user/authorize')

const PpuService = () => {
  const [mobileNumber, setMobileNumber] = useState(null);
  const [userList, setUserList] = useState([]);
  const dispatch = useDispatch();
  const userToken = useSelector((state) => state.user.userToken);
  const financeTabVisible = useSelector(
    (state) => state.ppuu.financeTabVisible
  );
  const navigate = useNavigate();
  const [showAddStaffModal, setShowAddStaffModal] = useState(false);
  const [refresh, setRefresh] = useState(0);

  const findBtnHandler = () => {
    let ppuFilterParams = {
      f_staff_mobile: mobileNumber,
      f_staff_type: 1000230006,
    };
    getPpuStaff(ppuFilterParams);
  };

  const getPpuStaff = async (params) => {
    try {
      const response = await instance({
        url: "/staffs/all",
        method: "POST",
        data: params,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response?.status === 201) {
        setUserList(response?.data?.data);
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  useEffect(() => {
    let ppuFilterParams = {
      f_staff_type: 1000230006,
    };
    getPpuStaff(ppuFilterParams);
  }, [refresh, userToken]);

  const checkStaffInfo = (id) => {
    navigate(`/staffs/${id}`);
  };

  const handleCancelAddStaffModal = () => {
    setShowAddStaffModal(false);
    dispatch(setFinanceTabVisible(0));
  };
  return (
    <>
      {userToken !== null && (
        <>
          <div
            className="my-3"
            style={{ display: "flex", alignItems: "center" }}
          >
            <div>
              <p
                className="m-0"
                style={{
                  fontSize: "18px",
                  fontWeight: "700",
                  color: "#222222",
                }}
              >
                Users
              </p>
            </div>
          </div>

          <hr />

          <div style={{ display: "flex", marginLeft: "0px" }}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                margin: "auto",
              }}
            >
              <TextField
                style={{ minWidth: "300px" }}
                value={mobileNumber}
                placeholder="Mobile number"
                onChange={(e) => {
                  setMobileNumber(e.target.value);
                }}
                type="number"
              />
              <div className="ms-4">
                <Button
                  variant="primary"
                  style={{
                    background: "#673AB7",
                    color: "#fff",
                    border: "none",
                    height: "54px",
                  }}
                  onClick={findBtnHandler}
                >
                  <i className="fa fa-search" aria-hidden="true"></i> Find
                </Button>
              </div>
            </div>
          </div>

          <div>
            <div style={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                style={{ backgroundColor: "#ec6237", color: "white" }}
                icon={<UserAddOutlined />}
                size="large"
                onClick={() => {
                  setShowAddStaffModal(true);
                  dispatch(setFinanceTabVisible(0));
                }}
              >
                Onboard
              </Button>
            </div>
            <PpuListTable
              loadReport={userList}
              checkStaffInfo={checkStaffInfo}
            />
          </div>

          <Modal
            footer={null}
            open={showAddStaffModal}
            onCancel={handleCancelAddStaffModal}
            width={1000}
          >
            <AddStaff
              key={Date.now()}
              isPPUU={true}
              setRefresh={setRefresh}
              setShowAddStaffModal={setShowAddStaffModal}
            />
          </Modal>
        </>
      )}
    </>
  );
};

// export default PpuService
export default withAuthorization(PpuService, [CS_TEAM, VET_OPS, CENTRAL_VET])