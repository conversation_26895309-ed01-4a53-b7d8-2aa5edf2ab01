import style from "./ppu.module.css";
import {Modal,Tag, Typography} from "antd";
import {useEffect, useState} from "react";
import PpuuFinance from "./PpuuFinance";
import {CreditCardOutlined,PlusOutlined, EyeOutlined, ArrowRightOutlined, ExportOutlined } from "@ant-design/icons";
import { getTransactionHistory, getWalletBalance } from "./ppuApiService";
import {useNavigate} from "react-router-dom";
import {useSelector, useDispatch} from "react-redux";
import {extractBasedOnLanguageMod} from "../../../Utilities/Common/utils";
import { getWalletBalanceRedux } from "../../../redux/slices/ppuu/action";
import {Button} from "antd";
import ScrollupButton from "../../../Utilities/Common/ScrollupButton";
import {formatedDate} from "../.../../../../Utilities/Common/utils"


const TransactionHistory = (props)=>{
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const userToken = useSelector((state)=> state.user.userToken);
    const walletBalance = useSelector((state)=> state.ppuu.walletBalance)
    const [showModal, setShowModal] = useState(false);
    const [data,setData] = useState([]);
    const [refresh, setRefresh] = useState(0);
    const onCancelHandler = ()=>{
        setShowModal(false)
    }
    useEffect(()=>{
      getTransactionHistory(props.staffId,{},userToken,dispatch,navigate,setData);
    //   getWalletBalance(props.staffId,userToken,dispatch,navigate,setBalance)
      dispatch(getWalletBalanceRedux(props.staffId,userToken,navigate))
      localStorage.removeItem("taskNavigation");
    },[refresh])
    
    const transactionStatusMapper = (type) =>{
        switch (type) {
            case 1000880001:
                return <Tag color="green">Confirmed</Tag>
            case 1000880002:
                return <Tag color="orange">Blocked</Tag>
            case 1000880003:
                return <Tag color="valcano">Cancelled</Tag>
            case null:
                return <Tag color="lime">N/A</Tag>
            default:
                break;
        }
    }
    const goToActivityDetails = (id) =>{
        navigate(`/tasks/${id}`, {state : {staff_id : props?.staffId}})
    }
    
    return (
        <>
            <div className={style.trans_main}>
                <div className={style.divider_1}>
                    <div className={style.trans_add_btn}>
                       <Button title="Add Money" type="primary" shape="circle" icon={<PlusOutlined />} size="large" onClick={()=>setShowModal(true)} />
                    </div>
                    <div className={style.trans_card}>
                    <div style={{ textAlign: "center", marginTop: "20px" }}>
                        <div style={{ fontSize: "2rem", fontWeight: 800, color: "purple" }}>
                            Your Wallet Balance
                        </div>
                        <div className={walletBalance?.balance < 250 ? style.font_balance_low_balance : style.font_balance}>
                            <span style={{ fontSize: "2rem", fontWeight: 600 }}>₹</span>{walletBalance?.balance ? walletBalance?.balance : 0 }
                        </div>
                        </div>
                    </div>
                </div>
                <div className={style.divider_2}>
                    <div style={{width:"100%"}}>
                        <div style={{padding:"10px",fontSize:"18px",color:"grey",fontWeight:600}}>Transaction History</div>
                        {data?.map((item,index)=>{
                            return (
                                <div key={index} style={{display:"flex",justifyContent:"center",alignItems:"center",textAlign:"center"}}>
                                    <div className={style.inner_trans_card}>
                                        <div className={style.trans_round}><CreditCardOutlined  style={{marginTop:"17px"}}/></div>
                                        <div style={{display:"flex",flexDirection:"column"}}>
                                        <div style={{ fontWeight: "bold", color: "#333" }}>
                                            {extractBasedOnLanguageMod(item?.farmer_name)}
                                        </div>
                                        <div style={{ color: "#666" }}>
                                            {item?.sr_no && item?.entity_uuid ? <Button type="link" icon={<ExportOutlined />} title="Click to view activity"  onClick={()=>goToActivityDetails(item?.entity_uuid)}>{item?.sr_no }</Button> : null}
                                        </div>
                                        <div style={{ color: "#666" }}>
                                            {item.transaction_description?.transaction_id}
                                        </div>
                                        <p>Transaction Type: {item.transaction_description?.transaction_type || 'N/A'}</p>
                                        </div>
                                        <div style={item.transaction_type === 1000790001 ? {color:"green",fontSize:"1rem",fontWeight:600}:{color:"red",fontSize:"1rem",fontWeight:600}}>
                                            <span style={{ fontSize: "1.3rem", fontWeight: 600,marginRight:"5px" }}>{item.transaction_type === 1000790001 ? "+":"-"}₹</span>
                                            {+item.amount}
                                        </div>
                                        <div>
                                            {transactionStatusMapper(item?.transaction_status)}
                                        </div>
                                        <div>
                                            {/* <span style={{color:"gray", fontSize:"16px"}}>Transaction Date : </span> */}
                                            {formatedDate(item?.transaction_date)}
                                        </div>
                                    </div>
                                </div>
                            )
                        })}
                    </div>
                </div>
                <ScrollupButton/>
            </div>
            <Modal footer={null} width={1000} open={showModal} onCancel={onCancelHandler}>
                <PpuuFinance key={Date.now()}  staffId={props.staffId} isTransPage={true} setShowModal={setShowModal} setRefresh={setRefresh}/>
            </Modal>
        </>       
    )
}
export default TransactionHistory;