import { instance } from "../../../Services/api.service";
import ToastersService from "../../../Services/toasters.service";
import { clearUserToken } from "../../user/action";
import { setFinanceTabVisible } from "../../../redux/slices/ppuu/reducers";

export const getPpuFarmerByStaffId = async (staffId,userToken,dispatch,clearUserToken,navigate,setFarmerList) => {
  try {
    const response = await instance({
      url: `ppu/farmerlist?staff_id=${staffId}`,
      method: "POST",
      data: {
          page_number:null,
          page_limit:null
      },
      headers: {
        "Content-Type": "application/json",
        token: userToken.accessToken,
      },
    });
    if (response.status === 201) {
        console.log(response.data.result)
        setFarmerList(response.data.result)
    }
  } catch (error) {
    if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
  }
};

export const createFinance = async(payload,userToken,dispatch,clearUserToken,navigate,setRefresh,resetState)=>{
  try {
    const response = await instance({
      url : "/ppu/transaction_ledge",
      method : "POST",
      data : payload,
      headers: {
        "Content-Type": "application/json",
        token: userToken.accessToken,
      }, 
    });
    if (response.status === 201) {
      ToastersService.successToast("successfully done!")
      // setShowAddStaffModal(false)
      // window.location.reload()
      setRefresh(Math.random())
      resetState()
      // return response.data
    }
  } catch (error) {
    if (error?.response?.status === 401) {
      dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
    }
  }
}
export const createFinanceForStaff = async(payload,userToken,dispatch,clearUserToken,navigate,setShowAddStaffModal,resetState,setCurrent)=>{
  try {
    const response = await instance({
      url : "/ppu/transaction_ledge",
      method : "POST",
      data : payload,
      headers: {
        "Content-Type": "application/json",
        token: userToken.accessToken,
      }, 
    });
    if (response.status === 201) {
      ToastersService.successToast("successfully done!")
      setShowAddStaffModal(false)
      resetState()
      dispatch(setFinanceTabVisible(0))
      setCurrent = 0
      // setCurrent(0)
      // return response.data
    }
  } catch (error) {
    if (error?.response?.status === 401) {
      dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
    }
  }
}

export const getCattlesByFarmerId = async (farmerId,userToken,dispatch,clearUserToken,navigate,setCattleList)=>{
  try {
    const response = await instance({
      url : `v2/customers/${farmerId}/animals`,
      method : "GET",
      headers: {
        "Content-Type": "application/json",
        token: userToken.accessToken,
      }, 
    });
    if (response.status === 200) {
      setCattleList(response?.data?.result)
    }
  } catch (error) {
    if (error?.response?.status === 401) {
      dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
    }
  }
}

export const getWalletBalance = async (staffId,userToken,dispatch,navigate,setBalance)=>{
  try {
    const response = await instance({
      url : `/ppu/get_transaction_ledge/${staffId}`,
      method : "GET",
      headers: {
        "Content-Type": "application/json",
        token: userToken.accessToken,
      }, 
    })
    if (response.status === 200) {
      setBalance(response.data)
    }
  } catch (error) {
    if (error?.response?.status === 401) {
      dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
    }
  }
}

export const getTransactionHistory = async (staffId,payload,userToken,dispatch,navigate,setData)=>{
  try {
    const response = await instance({
      url : `ppu/get_transaction_ledge?staff_id=${staffId}`,
      method : "POST",
      data : payload,
      headers: {
        "Content-Type": "application/json",
        token: userToken.accessToken,
      }, 
    })
    if (response.status === (201 || 200)) {
      setData(response.data.result)
    }
  } catch (error) {
    if (error?.response?.status === 401) {
      dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
    }
  }
}
