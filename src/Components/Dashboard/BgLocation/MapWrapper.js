/* eslint-disable eqeqeq */
/* eslint-disable camelcase */
/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { DrawMap, MarkerIcon } from './MapComponents'
import React, { useState, useEffect, createRef } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import axios from 'axios'
// import { Atom } from 'react-loading-indicators'
import moment from 'moment'
import dayjs from 'dayjs'

import { extractBasedOnLanguage } from '@krushal-it/common-core'
import { set } from 'lodash'
import { BASE_URL, instance } from '../../../Services/api.service'
import ToastersService from '../../../Services/toasters.service'
import MultiSelectDropdown from './MultiSelectDropdown'
import { DatePicker, Space, Button } from 'antd'

const _ = require('lodash')
const {
  configurationJSON
} = require('@krushal-it/common-core')
const { RangePicker } = DatePicker

const {CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../user/authorize')

const { getDairyFarmLocationReport, getPartnerReport } = require('../../../router')
const { KMReport } = require('./KMReport')

const [markers, polylines] = [[], []] // initialize empty arrays

const locationMarkerConfig = {
  meta: { name: 'agents', layerControl: true, show: true, type: 'located' },
  iconAttr: 'staff_id',
  iconAttrMapping: true,
  iconBlinkAttr: 'fixed',
  iconBlinkAttrMapping: true,
  iconBlinkDataFixed: 'blinking',
  titleAttr: 'tooltip',
  titleAttrMapping: false,
  colorAttr: 'staff_id',
  colorAttrMapping: true

}

const stayPtMarkersConfig = {
  meta: { name: 'stay points', layerControl: true, show: true, type: 'awesome' },
  iconAttr: 'staff_id',
  iconAttrMapping: true,
  iconBlinkAttr: 'staff_id',
  iconBlinkAttrMapping: true,
  iconBlinkDataFixed: '',
  titleAttr: 'tooltip',
  titleAttrMapping: false,
  colorAttr: 'staff_id',
  colorAttrMapping: true

}

const userEventMarkerConfig = {
  meta: { name: 'user events', layerControl: true, show: true, type: 'awesome' },
  iconAttr: 'staff_id',
  iconAttrMapping: true,
  iconBlinkAttr: 'staff_id',
  iconBlinkAttrMapping: true,
  iconBlinkDataFixed: '',
  titleAttr: 'tooltip',
  titleAttrMapping: false,
  colorAttr: 'staff_id',
  colorAttrMapping: true

}

const routeConfig = {
  meta: { name: 'routes', layerControl: true, show: true },
  colorAttr: 'staff_id',
  colorAttrMapping: true,
  opacityAttr: 'fixed',
  opacityDataFixed: 0.5,
  weightAttr: 'fixed',
  weightDataFixed: 5,
  titleAttr: 'tooltip',
  titleAttrMapping: false
}

const farmConfig = {
  meta: { name: 'farms', layerControl: true, show: false },
  colorAttr: 'staff_id',
  colorAttrMapping: true,
  fillColorAttr: 'staff_id',
  fillColorAttrMapping: true,
  opacityAttr: 'fixed',
  opacityDataFixed: 0.5,
  weightAttr: 'fixed',
  weightDataFixed: 5,
  titleAttr: 'staff_id',
  titleAttrMapping: false
}

const dairyFarmConfig = {
  meta: { name: 'Dairy Farms', layerControl: true, show: true, type: 'awesome' },
  iconAttr: 'customer_id',
  iconAttrMapping: true,
  iconBlinkAttr: 'village_id',
  iconBlinkAttrMapping: true,
  iconBlinkDataFixed: '',
  titleAttr: 'toolTip',
  titleAttrMapping: false,
  // colorAttr: 'village_id',
  // colorAttrMapping: true,
  colorAttr: 'fixed',
  colorDataFixed: 'blue',
  colorAttrMapping: true,

}

const bcoLocationConfig = {
  meta: { name: 'BCOs', layerControl: true, show: false, type: 'awesome' },
  iconAttr: 'partner_id',
  iconAttrMapping: true,
  iconBlinkAttr: 'village_id',
  iconBlinkAttrMapping: true,
  iconBlinkDataFixed: '',
  titleAttr: 'toolTip',
  titleAttrMapping: false,
  // colorAttr: 'village_id',
  // colorAttrMapping: true,
  colorAttr: 'fixed',
  colorDataFixed: 'blue',
  colorAttrMapping: true,

}

const locationIcons = ['1.png', '2.png', '3.png', '4.png'] // .map((icon) => 'img/' + icon)
const stayPtIcons = ['1.png', '2.png', '3.png', '4.png'] // .map((icon) => 'img/' + icon)
const userEventIcons = ['1.png', '2.png', '3.png', '4.png'] // .map((icon) => 'img/' + icon)
const blinkData = ['blinking', '']
const colors = ['red', 'green', 'blue', 'orange', 'purple', 'brown', 'black']

const initializeIndexedConfigAttributes = (initializationAttributes) => {
  // set iconAttr, iconBlinkAttr, colorAttr, opacityAttr, weightAttr, titleAttr
  locationMarkerConfig.iconData = setIndexedConfigValues(locationIcons, initializationAttributes.staff_ids)
  locationMarkerConfig.colorData = setIndexedConfigValues(colors, initializationAttributes.staff_ids)
  locationMarkerConfig.iconBlinkData = setIndexedConfigValues(blinkData, initializationAttributes.staff_ids)
  stayPtMarkersConfig.iconData = setIndexedConfigValues(stayPtIcons, initializationAttributes.staff_ids)
  stayPtMarkersConfig.iconBlinkData = setIndexedConfigValues(blinkData, initializationAttributes.staff_ids)
  stayPtMarkersConfig.colorData = setIndexedConfigValues(colors, initializationAttributes.staff_ids)
  userEventMarkerConfig.iconData = setIndexedConfigValues(userEventIcons, initializationAttributes.staff_ids)
  userEventMarkerConfig.iconBlinkData = setIndexedConfigValues(blinkData, initializationAttributes.staff_ids)
  userEventMarkerConfig.colorData = setIndexedConfigValues(colors, initializationAttributes.staff_ids)
  routeConfig.colorData = setIndexedConfigValues(colors, initializationAttributes.staff_ids)
  farmConfig.colorData = setIndexedConfigValues(colors, initializationAttributes.staff_ids)
  farmConfig.fillColorData = setIndexedConfigValues(colors, initializationAttributes.staff_ids)
  dairyFarmConfig.colorData = setIndexedConfigValues(colors, [0, 1, 2, 3, 4, 5])
  dairyFarmConfig.fillColorData = setIndexedConfigValues(colors, [0, 1, 2, 3, 4, 5, 6])
  bcoLocationConfig.colorData = setIndexedConfigValues(colors, [0, 1, 2, 3, 4, 5])
  bcoLocationConfig.fillColorData = setIndexedConfigValues(colors, [0, 1, 2, 3, 4, 5, 6])
}

const setIndexedConfigValues = (configValues, initializationAttributeData) => {
  // console.log('configValues', configValues, 'initializationAttributeData', initializationAttributeData)
  const indexedConfigValues = {}
  initializationAttributeData.map((dataValue, index) => {
    const configValueIndex = index % configValues.length
    //  console.log('dataValue', dataValue, 'index', index, 'valueIndex', valueIndex)
    indexedConfigValues[dataValue] = configValues[configValueIndex]
    return indexedConfigValues
  })
  //  console.log('indexedAttrs', indexedAttrs, 'values', values, 'data', data, 'data.length', data.length, 'data.length % value')
  return indexedConfigValues
}

const truth = (value) => {
  const valueStr = String(value).toLowerCase()
  return valueStr === 'true' || (valueStr !== 'false' && valueStr !== 'null' &&
  valueStr !== 'undefined' && valueStr !== '' && valueStr !== '[]' && valueStr !== '{}' && valueStr !== '0')
}

const getFarmDairyFarmLocations = async (userToken, queryParams) => {
  try {
    const headers = {
      useCase: 'Get Farmer Dairy Farm Locations',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const response = await getDairyFarmLocationReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getBCOLocations = async (userToken) => {
  try {
    const headers = {
      useCase: 'Get BCO Geo Locations',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    // const response = await getDistrictListV2(headers, stateId)
    const queryParams = {
      searchConfiguration: {
        partner_active: {
          "enableFilter": true,
          "type": "default_array",
          "dataType": "int",
          "altColumn": "partner_active_id"
        },
      },
      selectedColumns: [
        "partner_id",
        "partner_active",
        "partner_name",
        "taluk_name",
        "village_name",
        "bco_location",
      ],
      filters: {
        partner_active: [
          1000100001
        ],
      },
      forceLoadAllColumns: true,
    }

    queryParams.partnerTypeIds = [1000850002]
    const response = await getPartnerReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const getMapData = async (accessToken, dateRange, selectedFilters, stateRef) => {
  let [from, to] = dateRange
  from = from.format('YYYY-MM-DD HH:mm:ss')
  to = to.format('YYYY-MM-DD HH:mm:ss')
  const [locFrom, stayPtFrom, routeFrom] = [from, from, from]
  const [locTo, stayPtTo, routeTo] = [to, to, to]
  if (!selectedFilters || !selectedFilters.user_device_ids || selectedFilters.user_device_ids.length <= 0) return { locations: [], stayPoints: [], routes: [], userEvents: [], farms: [] }
  const user_device_ids = selectedFilters.user_device_ids.map((user_device_id) => user_device_id.value.toString())
  console.log('user_device_ids', user_device_ids)
  const urlParams = { locFrom, locTo, stayPtFrom, stayPtTo, routeFrom, routeTo, user_device_ids }
  const qryParams = new URLSearchParams(urlParams).toString()
  console.log('qryParams getMapData', qryParams, 'dateRAnge', dateRange)
  console.log('url', `/bgLocation/mapData?${qryParams}`)
  try {
    const response = await instance({
      url: `/bgLocation/mapData?${qryParams}`,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        token: accessToken
      }
    })
    console.log('response', response.status)
    // console.log('response', response.data)
    if (response?.status >= 200 && response?.status < 300 && response?.data) {
      // console.log('response', response.data)
      return response?.data?.data
    }
  } catch (error) {
    console.log(error)
    // if (error.response.status >= 400) {
    //   localStorage.removeItem('accessToken')
    //   localStorage.removeItem('resetToken')
    //   ToastersService.failureToast('Session Expired!')
    //   // TODO: redirect to login page
    //   stateRef.current.navigate('/login')
    //   window.location.reload()
    // }
  }
}

// we are going to get location of last 10 minutes or other specified minutes since now and then keep updating every few minute
// return data is only locations, update the data in the map
const getLiveMapData = async (accessToken, since, selectedFilters, stateRef) => {
  since = since || 3000 * 60 * 1000 // 10 minutes
  if (!selectedFilters || !selectedFilters.user_device_ids || selectedFilters.user_device_ids.length <= 0) return { locations: [] }
  const user_device_ids = selectedFilters.user_device_ids.map((user_device_id) => user_device_id.value)
  const urlParams = { user_device_ids, since }
  const qryParams = new URLSearchParams(urlParams).toString()
  console.log('qryParams', qryParams)
  console.log('url', `/bgLocation/liveMapData?${qryParams}`)
  try {
    const response = await instance({
      url: `/bgLocation/liveMapData?${qryParams}`,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        token: accessToken
      }
    })
    console.log('response', response?.status)
    console.log('response', response?.data)
    if (response?.status >= 200 && response?.status < 300 && response?.data) {
      console.log('response', response?.data)
      return { locations: response?.data?.data }
    }
    return { locations: [] }
  } catch (error) {
    console.log(error)
    if (error?.response?.status >= 400) {
      // localStorage.removeItem('accessToken')
      // localStorage.removeItem('resetToken')
      // ToastersService.failureToast('Session Expired!')
      // stateRef.current.navigate('/login')
      // window.location.reload()
    }
  }
}

const getAttributes = async (userToken, stateRef) => {
  try {
    const response = await instance({
      url: '/bgLocation/attributes',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        token: userToken.accessToken
      }
    })
    console.log('response', response?.status)
    console.log('response', response?.data)
    if (response?.status >= 200 && response?.status < 300 && response?.data) {
      console.log('response', response?.data)
      return response?.data?.data
    }
    return {}
  } catch (error) {
    console.log(error)
    if (error?.response?.status >= 400) {
      localStorage.removeItem('accessToken')
      localStorage.removeItem('resetToken')
      ToastersService.failureToast('Session Expired!')
      // TODO: redirect to login page
      stateRef.current.navigate('/login')
      window.location.reload()
    }
  }
}

const MapWrapper = (props) => {
  const allowMapDateRange = configurationJSON().ALLOW_MAP_DATE_RANGE || false
  console.log('allowMapDateRange', allowMapDateRange, 'ALLOW_MAP_DATE_RANGE', configurationJSON().ALLOW_MAP_DATE_RANGE)
  const staff_ids = props.staff_ids || []
  let startDate = props.startDate || moment().subtract(24, 'hours').startOf('day')
  const endDate = props.endDate || moment().subtract(24, 'hours').endOf('day')
  const userLanguage = localStorage.getItem('userLanguage') || 'en'
  const navigate = useNavigate()
  const userToken = useSelector((state) => state.user.userToken)
  const [liveTime, setLiveTime] = useState(20 * 60 * 1000)

  const [searchParams] = useSearchParams()

  let pageParamStaffId
  let pageParamReportDate

  if (searchParams.get('qP')) {
    const uncompressedParamsJsonString = decodeURIComponent(searchParams.get('qP'))
    const params = JSON.parse(uncompressedParamsJsonString)
    pageParamStaffId = params['sId']
    pageParamReportDate = params['rD']
    if (typeof pageParamReportDate === 'date') {
      pageParamReportDate = moment(pageParamReportDate).startOf('day')
    } else if (typeof pageParamReportDate === 'string' || pageParamReportDate instanceof String) {
      pageParamReportDate = moment(pageParamReportDate).startOf('day')
    }
  }
  // pageParamReportDate = moment('2023-09-27').startOf('day')
  // pageParamStaffId = 'fc8a0060-0041-11ee-90a4-83c7880772b4'
  startDate = pageParamReportDate !== undefined ? pageParamReportDate : startDate

  const containerDivRef = createRef()
  const leftDivRef = createRef()

  const [postInitialUseEffect, setPostInitialUseEffect] = useState(-1)
  const [postInitial2UseEffect, setPostInitial2UseEffect] = useState(-1)


  const [loading, setLoading] = useState(true)
  const [filtersLoading, setFiltersLoading] = useState(true)
  const [locationMarkers, setLocationMarkers] = useState({ config: locationMarkerConfig, markers: [] })
  const [dairyFarmMarkers, setDairyFarmMarkers] = useState({ config: dairyFarmConfig, markers: [] })
  const [bcoLocationMarkers, setBCOLocationMarkers] = useState({ config: bcoLocationConfig, markers: [] })
  const [stayPointMarkers, setStayPointMarkers] = useState({ config: stayPtMarkersConfig, markers: [] })
  const [userEventMarkers, setUserEventMarkers] = useState({ config: userEventMarkerConfig, markers: [] })
  const [routePolygons, setRoutePolygons] = useState({ config: routeConfig, polylines: [] })
  const [farmPolygons, setFarmPolygons] = useState({ config: farmConfig, polygons: [] })
  const [fullFilters, setFullFilters] = useState({ staff_type_ids: [], staff_ids: [], user_device_ids: [] }) // user_device_ids: []
  const [fullData, setFullData] = useState({})
  const stateRef = React.useRef()
  const [userDeviceIdsToAttributeMap, setUserDeviceIdToAttributesMap] = useState({})
  const [staffIdMap, setStaffIdMap] = useState({})
  const [staffTypeIdMap, setStaffTypeIdMap] = useState({})
  const [updatecount, setUpdateCount] = useState(0)
  // const nowTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
  const [selectedFilters, setSelectedFilters] = useState({ user_device_ids: [], staff_ids: [], staff_type_ids: [] }) // unusable untill user_device_ids are selected
  const [dateRange, setDateRange] = useState([startDate, endDate])
  const [enableSubmit, setEnableSubmit] = useState(true)
  const [KMReportOpen, setKMReportOpen] = useState(false)
  stateRef.current = {
    locationMarkers,
    dateRange,
    navigate,
    stayPointMarkers,
    dairyFarmMarkers,
    bcoLocationMarkers,
    userEventMarkers,
    selectedFilters,
    userDeviceIdsToAttributeMap,
    routePolygons,
    farmPolygons,
    loading,
    fullFilters,
    updatecount,
    fullData,
    staffIdMap,
    staffTypeIdMap
  }

  // first get attributes, then update config, then get map data and then set map related data
  /* useEffect(() => {
    // one time get attributes data
    if(userToken){

      getAttributes(userToken, stateRef)
      .then((attributesData) => {
        const userDeviceIdMappings = {}
        const staffIdMappings = {}
        const staffTypeIdMappings = {}
        const user_device_ids = []
        const staff_ids = []
        const staff_type_ids = []
        for (const [key, value] of Object.entries(attributesData)) {
          // console.log(key, value)
          const user_device_id = parseInt(key)
          const staff_id = value.staff_id
          const staff_type_id = value.staff_type_id
          const staff_name = value.staff_name_l10n.en || value.staff_name_l10n.ul || ''
          const staff_type_name = value.staff_type_name_l10n.en || value.staff_type_name_l10n.ul || ''
          userDeviceIdMappings[key] = { staff_type_id, user_device_id, staff_id, staff_name, staff_type_name }
          // console.log(userDeviceIdMappings[key])
          user_device_ids.push(user_device_id)
          staff_ids.push(userDeviceIdMappings[key].staff_id)
          staff_type_ids.push(userDeviceIdMappings[key].staff_type_id)
          if (staffIdMappings[staff_id]) {
            staffIdMappings[staff_id].user_device_ids.add(user_device_id)
            staffIdMappings[staff_id].staff_type_ids.add(staff_type_id)
            staffIdMappings[staff_id].staff_name = staff_name
            staffIdMappings[staff_id].staff_type_name = staff_type_name
          } else {
            staffIdMappings[staff_id] = { user_device_ids: new Set([user_device_id]), staff_type_ids: new Set([staff_type_id]), staff_name, staff_type_name }
          }
          if (staffTypeIdMappings[staff_type_id]) {
            staffTypeIdMappings[staff_type_id].user_device_ids.add(user_device_id)
            staffTypeIdMappings[staff_type_id].staff_ids.add(staff_id)
            staffTypeIdMappings[staff_type_id].staff_name = staff_name
            staffTypeIdMappings[staff_type_id].staff_type_name = staff_type_name
          } else {
            staffTypeIdMappings[staff_type_id] = { user_device_ids: new Set([user_device_id]), staff_ids: new Set([staff_id]), staff_name, staff_type_name }
          }
        }
        Object.keys(staffIdMappings).forEach((key) => {
          staffIdMappings[key].user_device_ids = Array.from(staffIdMappings[key].user_device_ids)
          staffIdMappings[key].staff_type_ids = Array.from(staffIdMappings[key].staff_type_ids)
        })
        Object.keys(staffTypeIdMappings).forEach((key) => {
          staffTypeIdMappings[key].user_device_ids = Array.from(staffTypeIdMappings[key].user_device_ids)
          staffTypeIdMappings[key].staff_ids = Array.from(staffTypeIdMappings[key].staff_ids)
        })
        // initialization attributed to be used once on start up, there will be duplicates
        const initializationAttributes = { user_device_ids, staff_ids, staff_type_ids }
        initializeIndexedConfigAttributes(initializationAttributes)
        setStaffIdMap(staffIdMappings)
        setStaffTypeIdMap(staffTypeIdMappings)
        // now setup attributes to be used on map we can remove du
        // staff_ids = _.uniq(staff_ids)
        // staff_type_ids = _.uniq(staff_type_ids)
        const newFullFilters = { staff_type_ids: _.uniq(staff_type_ids), staff_ids: _.uniq(staff_ids), user_device_ids }
        newFullFilters.staff_ids = newFullFilters?.staff_ids?.map((staff_id) => {
          try {
            return { value: staff_id, label: staffIdMappings[staff_id].staff_name }
          } catch (err) {
            console.log('err', err, 'staff_id', staff_id, 'staffIdMappings', staffIdMappings)
            return { value: staff_id, label: staff_id }
          }
        })
        newFullFilters.staff_type_ids = newFullFilters?.staff_type_ids?.map((staff_type_id) => { return { value: staff_type_id, label: staffTypeIdMappings[staff_type_id].staff_type_name } })
        newFullFilters.user_device_ids = newFullFilters?.user_device_ids?.map((user_device_id) => { return { value: user_device_id, label: user_device_id } })
        
        setfullFilters(newFullFilters)
        
        setUserDeviceIdToAttributesMap(userDeviceIdMappings)
        //  const newSelectedFilters = { user_device_ids, staff_ids, staff_type_ids }
        // in the beginging newSelectedFilters are same as fullFilters
        // setSelectedFilters(newFullFilters)
        // const newDateRange = [moment().subtract(240,'hours').startOf('day'), moment().endOf('day')]
        // const newDateRange = [moment().subtract(1, 'days').startOf('day'), moment().subtract(1, 'days').endOf('day')] // now showing default dateRange as a yesterdays and todays date
        // setDateRange(newDateRange)
        setFiltersLoading(false)
        // load the data first time
        // getMapData(userToken.accessToken, newFullFilters, newDateRange)
        //   .then((data) => {
          //     console.log('data', data)
          //     updateData(data, userDeviceIdMappings, newFullFilters)
          //   })
          //   .catch(err => { console.log('err First time', err); throw err })
        })
      .catch(err => { console.log('err First time', err); throw err })
  }, []) */
  useEffect(() => {
    if (postInitial2UseEffect === 0) {
      fetchData()
      setPostInitial2UseEffect(1)
      setKMReportOpen(true)
      kmReportRef.current.setKMReportVisible(true)
      const leftDivHeight = leftDivRef.current.offsetHeight;
      containerDivRef.current.style.height = `${leftDivHeight}px`;
    }
  },[postInitial2UseEffect])
  useEffect(() => {
    if (postInitialUseEffect === 0) {
      updateSelectedFilters('staff_ids', [pageParamStaffId])
      const newDateRange = [moment(pageParamReportDate).startOf('day'), moment(pageParamReportDate).clone().endOf('day')]
      setDateRange(newDateRange)
      setPostInitialUseEffect(1)
      setPostInitial2UseEffect(0)
    }
  },[postInitialUseEffect])

  useEffect(() => {
    const asyncUseEffect = async () => {
      try {
        const farmerDairyFarmLocationsResponse = await getFarmDairyFarmLocations(userToken, {})
        const bcoLocationsResponse = await getBCOLocations(userToken)
        const bcoLocations = []
        bcoLocationsResponse.report.map(object => {
          if (object['bco_location'] !== undefined && object['bco_location'] !== null) {
            const newBCOLocation = {...object, lat: object['bco_location']['latitude'], lng: object['bco_location']['longitude']}
            delete newBCOLocation['bco_location']
            bcoLocations.push(newBCOLocation)
          }
        })
        updateData({ bcoLocations: bcoLocations, dairyFarms: farmerDairyFarmLocationsResponse.report }, {}, {}, stateRef)
        const attributesData = await getAttributes(userToken, stateRef)
        const userDeviceIdMappings = {}
        const staffIdMappings = {}
        const staffTypeIdMappings = {}
        const user_device_ids = []
        const staff_ids = []
        const staff_type_ids = []
        for (const [key, value] of Object.entries(attributesData)) {
          // console.log(key, value)
          const user_device_id = parseInt(key)
          const staff_id = value.staff_id
          const staff_type_id = value.staff_type_id
          const staff_name = value.staff_name_l10n.en || value.staff_name_l10n.ul || ''
          const staff_type_name = value.staff_type_name_l10n.en || value.staff_type_name_l10n.ul || ''
          userDeviceIdMappings[key] = { staff_type_id, user_device_id, staff_id, staff_name, staff_type_name }
          // console.log(userDeviceIdMappings[key])
          user_device_ids.push(user_device_id)
          staff_ids.push(userDeviceIdMappings[key].staff_id)
          staff_type_ids.push(userDeviceIdMappings[key].staff_type_id)
          if (staffIdMappings[staff_id]) {
            staffIdMappings[staff_id].user_device_ids.add(user_device_id)
            staffIdMappings[staff_id].staff_type_ids.add(staff_type_id)
            staffIdMappings[staff_id].staff_name = staff_name
            staffIdMappings[staff_id].staff_type_name = staff_type_name
          } else {
            staffIdMappings[staff_id] = { user_device_ids: new Set([user_device_id]), staff_type_ids: new Set([staff_type_id]), staff_name, staff_type_name }
          }
          if (staffTypeIdMappings[staff_type_id]) {
            staffTypeIdMappings[staff_type_id].user_device_ids.add(user_device_id)
            staffTypeIdMappings[staff_type_id].staff_ids.add(staff_id)
            staffTypeIdMappings[staff_type_id].staff_name = staff_name
            staffTypeIdMappings[staff_type_id].staff_type_name = staff_type_name
          } else {
            staffTypeIdMappings[staff_type_id] = { user_device_ids: new Set([user_device_id]), staff_ids: new Set([staff_id]), staff_name, staff_type_name }
          }
        }
        Object.keys(staffIdMappings).forEach((key) => {
          staffIdMappings[key].user_device_ids = Array.from(staffIdMappings[key].user_device_ids)
          staffIdMappings[key].staff_type_ids = Array.from(staffIdMappings[key].staff_type_ids)
        })
        Object.keys(staffTypeIdMappings).forEach((key) => {
          staffTypeIdMappings[key].user_device_ids = Array.from(staffTypeIdMappings[key].user_device_ids)
          staffTypeIdMappings[key].staff_ids = Array.from(staffTypeIdMappings[key].staff_ids)
        })
        // initialization attributed to be used once on start up, there will be duplicates
        const initializationAttributes = { user_device_ids, staff_ids, staff_type_ids }
        initializeIndexedConfigAttributes(initializationAttributes)
        setStaffIdMap(staffIdMappings)
        setStaffTypeIdMap(staffTypeIdMappings)
        // now setup attributes to be used on map we can remove du
        // staff_ids = _.uniq(staff_ids)
        // staff_type_ids = _.uniq(staff_type_ids)
        const newFullFilters = { staff_type_ids: _.uniq(staff_type_ids), staff_ids: _.uniq(staff_ids), user_device_ids }
        newFullFilters.staff_ids = newFullFilters?.staff_ids?.map((staff_id) => {
          try {
            return { value: staff_id, label: staffIdMappings[staff_id].staff_name }
          } catch (err) {
            console.log('err', err, 'staff_id', staff_id, 'staffIdMappings', staffIdMappings)
            return { value: staff_id, label: staff_id }
          }
        })
        newFullFilters.staff_type_ids = newFullFilters?.staff_type_ids?.map((staff_type_id) => { return { value: staff_type_id, label: staffTypeIdMappings[staff_type_id].staff_type_name } })
        newFullFilters.user_device_ids = newFullFilters?.user_device_ids?.map((user_device_id) => { return { value: user_device_id, label: user_device_id } })

        setFullFilters(newFullFilters)

        setUserDeviceIdToAttributesMap(userDeviceIdMappings)
        //  const newSelectedFilters = { user_device_ids, staff_ids, staff_type_ids }
        // in the beginging newSelectedFilters are same as fullFilters
        // setSelectedFilters(newFullFilters)
        // const newDateRange = [moment().subtract(240,'hours').startOf('day'), moment().endOf('day')]
        // const newDateRange = [moment().subtract(1, 'days').startOf('day'), moment().subtract(1, 'days').endOf('day')] // now showing default dateRange as a yesterdays and todays date
        // setDateRange(newDateRange)
        setFiltersLoading(false)
        // load the data first time
        // getMapData(userToken.accessToken, newFullFilters, newDateRange)
        //   .then((data) => {
        //     console.log('data', data)
        //     updateData(data, userDeviceIdMappings, newFullFilters)
        //   })
        //   .catch(err => { console.log('err First time', err); throw err })
        // updateSelectedFilters('staff_ids', [pageParamStaffId])
        if (pageParamStaffId !== undefined) {
          setPostInitialUseEffect(0)
        }
      } catch (error) {
        console.log('uE aUE 10, error')
        console.log('uE aUE 10a, error = ', error)
        throw error
      }
    }
    asyncUseEffect()
  }, [])

  // this function updates all the map related data based on the selected filters and data
  const updateData = (mapData, userDeviceIdsToAttributeMap, newSelectedFilters, stateRef) => {
    console.log('got data ', mapData, stateRef.current.loading, newSelectedFilters)
    console.log('newSelectedFilters', JSON.stringify(newSelectedFilters))
    // add staff_id and staff_type_id to data
    // const locationsWithAttributes = mapData?.locations?.map((location) => {
    //   let attributes = userDeviceIdsToAttributeMap[location.user_device_id]
    //   if (attributes === undefined || attributes === null) {
    //     console.log('userDeviceIdsToAttributeMap', userDeviceIdsToAttributeMap)
    //     console.log('attributes missing for location', attributes, location.user_device_id)
    //     attributes = { staff_id: 'missing', staff_type_id: 'missing', staff_name: 'missing', staff_type_name: 'missing' }
    //   }

    //   const tooltip = attributes.staff_name + ': ' + moment(location.time_at).format('DD-MM h:mm a')
    //   return { ...location, ...attributes, tooltip }
    // })
    const stayPointsWithAttributes = mapData?.stayPoints?.map((stayPoint) => {
      let attributes = userDeviceIdsToAttributeMap[stayPoint.user_device_id]
      if (attributes === undefined || attributes === null) {
        console.log('stayPoint', stayPoint, 'userDeviceIdsToAttributeMap', userDeviceIdsToAttributeMap)
        console.log('attributes missing for stayPoint ', attributes, stayPoint.user_device_id)
        attributes = { staff_id: 'missing', staff_type_id: 'missing', staff_name: 'missing', staff_type_name: 'missing' }
      }

      const arrivedAt = new Date(stayPoint.arrived_at)
      const leftAt = new Date(stayPoint.left_at)
      const diffMins = Math.round((leftAt - arrivedAt) / 60000) // minutes
      const tooltip = attributes.staff_name + ': ' + moment(arrivedAt).format('DD-MM h:mm a') + ' to ' + moment(leftAt).format('h:mm') + ', total ' + diffMins + ' minutes'
      return { ...stayPoint, ...attributes, tooltip }
    })
    const userEventsWithAttributes = mapData?.userEvents?.map((userEvent) => {
      const timeAt = new Date(userEvent.time_at)
      const onOff = userEvent.location_tracking == 0 ? 'off' : 'on'
      let attributes = userDeviceIdsToAttributeMap[userEvent.user_device_id]
      if (attributes === undefined || attributes === null) {
        console.log('userDeviceIdsToAttributeMap', userDeviceIdsToAttributeMap)
        console.log('attributes missing for userEvent', attributes, userEvent.user_device_id)
        attributes = { staff_id: 'missing', staff_type_id: 'missing', staff_name: 'missing', staff_type_name: 'missing' }
      }
      const tooltip = `${attributes.staff_name}:  swittched ${onOff}  at ${moment(timeAt).format('DD-MM h:mm a')}`

      return { ...userEvent, ...attributes, tooltip }
    })
    const routesWithAttributes = mapData?.routes?.map((route) => {
      const attributes = userDeviceIdsToAttributeMap[route.user_device_id]
      const tooltip = `${attributes.staff_name}: ${moment(route.from_time).format('DD-MM h:mm a')}  to   ${moment(route.to_time).format('h:mm a')} ${route.distance} meters`
      console.log('route', Object.keys(route), 'attributes', attributes, 'tooltip', tooltip, 'points', route.points)
      return { ...route, ...attributes, tooltip }
    })
    const farmsWithAttributes = mapData?.farms?.map((farm) => {
      const attributes = userDeviceIdsToAttributeMap[farm.user_device_id]
      return { ...farm,  ...attributes }
    })
    /* const dairyFarmsWithAttributes = mapData?.dairyFarms?.map((dairyFarm) => {
      const toolTip = dairyFarm['customer_name'] + '- ' + dairyFarm['mobile_number'] + ', village: ' + dairyFarm['village_name']
      return {...dairyFarm, toolTip}
    }) */
    const dairyFarmsWithAttributes = mapData !== undefined && mapData.dairyFarms !== undefined ? mapData?.dairyFarms?.map((dairyFarm) => {
      const toolTip = dairyFarm['customer_name'] + '- ' + dairyFarm['mobile_number'] + ', village: ' + dairyFarm['village_name']
      return {...dairyFarm, toolTip}
    }) : stateRef.current.fullData.dairyFarms
    const bcoLocationsWithAttributes = mapData !== undefined && mapData.bcoLocations !== undefined ? mapData?.bcoLocations?.map((bcoLocation) => {
      const toolTip = bcoLocation['partner_name'] + ', taluk: ' + bcoLocation['taluk_name'] + ', village: ' + bcoLocation['village_name']
      return {...bcoLocation, toolTip}
    }) : stateRef.current.fullData.bcoLocations
    const fullDataToUse = { ...stateRef.current.fullData, stayPoints: stayPointsWithAttributes || [], routes: routesWithAttributes || [], userEvents: userEventsWithAttributes || [], farms: farmsWithAttributes || [], dairyFarms: dairyFarmsWithAttributes || [], bcoLocations: bcoLocationsWithAttributes || [] }
    console.log('data', fullDataToUse)
    setFullData(fullDataToUse)
    // now filter the data based on selected filters
    const filteredData = filterAndUpdateData(fullDataToUse, newSelectedFilters)
    return { filteredData, fullDataToUse }
  }
  const updateLiveMapData = (mapData, userDeviceIdsToAttributeMap, newSelectedFilters, stateRef) => {
    console.log('got data ', mapData, stateRef.current.loading, newSelectedFilters)
    console.log('newSelectedFilters', JSON.stringify(newSelectedFilters))
    // add staff_id and staff_type_id to data
    console.log('mapData', 'mapData.locations', mapData?.locations)
    if (!mapData || !mapData?.locations) {
      console.log('no location data for liveLocationUpdate')
      return
    }
    const locationsWithAttributes = mapData?.locations?.map((location) => {
      let attributes = userDeviceIdsToAttributeMap[location.user_device_id]
      if (attributes === undefined || attributes === null) {
        console.log('userDeviceIdsToAttributeMap', userDeviceIdsToAttributeMap)
        console.log('attributes missing for location', attributes, location.user_device_id)
        attributes = { staff_id: 'missing', staff_type_id: 'missing', staff_name: 'missing', staff_type_name: 'missing' }
      }
      const tooltip = attributes.staff_name + ': ' + moment(location.time_at).format('DD-MM h:mm a')
      return { ...location, ...attributes, tooltip }
    })
    const fullDataToUse = { ...stateRef.current.fullData, locations: locationsWithAttributes || [] }
    console.log('data', fullDataToUse)
    filterAndUpdateData(fullDataToUse, newSelectedFilters)
    setFullData(fullDataToUse)
  }

  useEffect(() => {
    const interval = setInterval(() => {
      const newDateRange = stateRef.current.dateRange
      console.log('dateRange to got', newDateRange[1])
      // if the toDate is today's date then update the toDate to current time
      if (newDateRange[1].isSame(moment(), 'day')) {
        newDateRange[1] = moment().endOf('day')
      }
      console.log('dateRange to updated', newDateRange[1])
      getLiveMapData(userToken.accessToken, liveTime, stateRef.current.selectedFilters, stateRef)
        .then(data => updateLiveMapData(data, stateRef.current.userDeviceIdsToAttributeMap, stateRef.current.selectedFilters, stateRef))
        .catch(err => { console.log('err', err); throw err })
    }, 20000)
    return () => clearInterval(interval)
  }, [])

  const filterAndUpdateData = (fullDataToUse, newSelectedFilters) => {
    console.log('filterData', newSelectedFilters)

    let { user_device_ids, staff_ids, staff_type_ids } = newSelectedFilters
    user_device_ids = user_device_ids?.map((user_device_id) => user_device_id.value)
    const filteredData = { }
    filteredData.locations = fullDataToUse?.locations?.filter((location) => user_device_ids.includes(location.user_device_id))
    filteredData.stayPoints = fullDataToUse?.stayPoints?.filter((stayPoint) => user_device_ids.includes(stayPoint.user_device_id))
    filteredData.userEvents = fullDataToUse?.userEvents?.filter((userEvent) => user_device_ids.includes(userEvent.user_device_id))
    filteredData.routes = fullDataToUse?.routes?.filter((route) => user_device_ids.includes(route.user_device_id))
    filteredData.farms = fullDataToUse?.farms?.filter((farm) => user_device_ids.includes(farm.user_device_id))
    filteredData.dairyFarms = fullDataToUse?.dairyFarms
    filteredData.bcoLocations = fullDataToUse?.bcoLocations
    setLocationMarkers({ config: locationMarkerConfig, markers: filteredData.locations || [] })
    setStayPointMarkers({ config: stayPtMarkersConfig, markers: filteredData.stayPoints || [] })
    setUserEventMarkers({ config: userEventMarkerConfig, markers: filteredData.userEvents || [] })
    setDairyFarmMarkers({config: dairyFarmConfig, markers: filteredData.dairyFarms})
    setBCOLocationMarkers({config: bcoLocationConfig, markers: filteredData.bcoLocations})
    setRoutePolygons({ config: routeConfig, polylines: filteredData.routes || [] })
    setFarmPolygons({ config: farmConfig, polygons: filteredData.farms || [] })
    setLoading(false)
    console.log(' data = ', filteredData, 'stayPointMarkers = ', stateRef.current.stayPointMarkers, 'loading', stateRef.current.loading)
    console.log('locationMarkerConfig.iconData', locationMarkerConfig.iconData)
    console.log('locationMarkerConfig.colorData', locationMarkerConfig.colorData)
    console.log('stayPtMarkersConfig.colorData', stayPtMarkersConfig.colorData)
    console.log('userEventMarkerConfig.colorData', userEventMarkerConfig.colorData)
    console.log('routeConfig.colorData', routeConfig.colorData)
    // console.log('farmConfig.colorData', farmConfig.colorData)
    const currentUpdateCount = stateRef.current.updatecount + 1
    setUpdateCount(currentUpdateCount)
    return filteredData
  }

  const updateSelectedFilters = async (property, values) => {
    console.log('new values', property, values)
    const currentSelectedFilters = stateRef.current.selectedFilters
    // reconstruct the selected filters from property and values get ids, and get corresponding values
    let [user_device_ids, staff_ids, staff_type_ids] = [[], [], []]
    let full_staff_ids = []
    if (property === 'staff_ids') {
      user_device_ids = _.uniq(_.flatten(values.map((staff_id) => staffIdMap[staff_id].user_device_ids)))
      staff_type_ids = _.uniq(_.flatten(values.map((staff_id) => staffIdMap[staff_id].staff_type_ids)))
      staff_ids = values
    } else if (property === 'staff_type_ids') {
      values = [values]
      // user_device_ids = _.uniq(_.flatten(values.map((staff_type_id) => staffTypeIdMap[staff_type_id].user_device_ids)))
      full_staff_ids = _.uniq(_.flatten(values.map((staff_type_id) => staffTypeIdMap[staff_type_id].staff_ids)))

      full_staff_ids = full_staff_ids?.map((staff_id) => {
        try {
          return { value: staff_id, label: staffIdMap[staff_id].staff_name }
        } catch (err) {
          console.log('err', err, 'staff_id', staff_id, 'staffIdMappings', staffIdMap)
          return { value: staff_id, label: staff_id }
        }
      })

      setFullFilters({ ...fullFilters, staff_ids: full_staff_ids })
      // staff_ids = currentSelectedFilters.staff_ids.filter((staff_id) => full_staff_ids.includes(staff_id.value))
      staff_ids = []
      user_device_ids = []
      staff_type_ids = [values]
    } else if (property === 'user_device_ids') {
      staff_ids = _.uniq(_.flatten(values.map((user_device_id) => userDeviceIdsToAttributeMap[user_device_id].staff_id)))
      staff_type_ids = _.uniq(_.flatten(values.map((user_device_id) => userDeviceIdsToAttributeMap[user_device_id].staff_type_id)))
      user_device_ids = values
    }
    // now create label value pairs for each filter
    staff_ids = staff_ids.map((staff_id) => ({ label: staffIdMap[staff_id].staff_name, value: staff_id }))
    staff_type_ids = staff_type_ids.map((staff_type_id) => ({ label: staffTypeIdMap[staff_type_id].staff_type_name, value: staff_type_id }))
    user_device_ids = user_device_ids.map((user_device_id) => ({ label: user_device_id, value: user_device_id }))
    const newFilters = { user_device_ids, staff_ids, staff_type_ids }
    if (kmReportRef && kmReportRef.current && kmReportRef.current !== null && staff_ids.length !== 0) {
      kmReportRef.current.setKMReportVisible(false)
    }
    console.log('new filters for type =', property, 'user_device_ids  =', user_device_ids, 'staff_ids =  ', staff_ids, 'staff_type_ids = ', staff_type_ids)
    // setFiltersLoading(false)
    // at this stage call getMapData and update the data
    // setLoading(true)
    // const data = await getMapData(userToken.accessToken, stateRef.current.dateRange, newFilters, stateRef)
    // const { filteredData, fullData } = updateData(data, userDeviceIdsToAttributeMap, newFilters, stateRef)
    setSelectedFilters(newFilters)
    // const liveData = await getLiveMapData(userToken.accessToken, 20 * 60 * 1000, newFilters, stateRef)
    // updateLiveMapData(liveData, userDeviceIdsToAttributeMap, newFilters, stateRef)

    // const newFilterData = filterAndUpdateData(fullData, newFilters) // updateData sets filterAndUpdateData
    // setLoading(false)
    console.log('new filters', newFilters)
  }

  const updateDateRange = async (values) => {
    if (!stateRef.current.selectedFilters || !stateRef.current.selectedFilters.user_device_ids || stateRef.current.selectedFilters.user_device_ids.length <= 0) return
    console.log('new range values', values[0].toString(), values[1].toString())
    const newDateRange = [moment(values[0]).clone().startOf('day'), moment(values[1]).clone().endOf('day')]
    console.log('new range values', newDateRange)
    setDateRange(newDateRange)
    console.log('new range values', newDateRange)
    // const liveData = await getLiveMapData(userToken.accessToken, liveTime, stateRef.current.selectedFilters, stateRef)
    // updateLiveMapData(liveData, userDeviceIdsToAttributeMap, stateRef.current.selectedFilters, stateRef)
    // const data = await getMapData(userToken.accessToken, newDateRange, stateRef.current.selectedFilters, stateRef)
    // const { filteredData, fullData } = updateData(data, userDeviceIdsToAttributeMap, stateRef.current.selectedFilters, stateRef)

    // const newFilterData = filterAndUpdateData(fullData, stateRef.current.selectedFilters) // updateData sets filterAndUpdateData
  }

  const setDate = async (date, dateString) => {
    console.log('new date', date, dateString)
    if (!date) {
      if (kmReportRef && kmReportRef.current && kmReportRef.current !== null) {
        kmReportRef.current.setKMReportVisible(false)
      }
      return
    }
    // if filters are not yet selected then no data to show return
    if (!stateRef.current.selectedFilters || !stateRef.current.selectedFilters.user_device_ids || stateRef.current.selectedFilters.user_device_ids.length <= 0) return

    const newDateRange = [moment(dateString).startOf('day'), moment(dateString).clone().endOf('day')]
    console.log('new range values', newDateRange, 'current dateRange', stateRef.current.dateRange)
    // since dates have changed get new data and update
    // const liveData = await getLiveMapData(userToken.accessToken, liveTime, stateRef.current.selectedFilters, stateRef)
    // updateLiveMapData(liveData, userDeviceIdsToAttributeMap, stateRef.current.selectedFilters, stateRef)
    // const data = await getMapData(userToken.accessToken, newDateRange, stateRef.current.selectedFilters, stateRef)
    // const { filteredData, fullData } = updateData(data, userDeviceIdsToAttributeMap, stateRef.current.selectedFilters, stateRef)
    setDateRange(newDateRange)

    // const newFilterData = filterAndUpdateData(fullData, stateRef.current.selectedFilters) // updateData sets filterAndUpdateData
  }

  const changeFilterData = (property, values) => {
  }

  const fetchData = async () => {
    console.log('fetchData', stateRef.current.selectedFilters, stateRef.current.dateRange)
    if (!stateRef.current.selectedFilters || !stateRef.current.selectedFilters.user_device_ids ||
      stateRef.current.selectedFilters.user_device_ids.length <= 0 || !stateRef.current.dateRange ||
     !stateRef.current.dateRange[0] || !stateRef.current.dateRange[1] || !stateRef.current.dateRange[0].isValid() ||
     !stateRef.current.dateRange[1].isValid()) {
      console.log('no data to fetch')
      updateData({ locations: [], stayPoints: [], routes: [], userEvents: [], farms: [] }, userDeviceIdsToAttributeMap, stateRef.current.selectedFilters, stateRef)
      updateLiveMapData({ locations: [] }, userDeviceIdsToAttributeMap, stateRef.current.selectedFilters, stateRef)
      // clear existing map data
      console.log('clearing map data')
      return
    }
    setEnableSubmit(false)
    try {
      const data = await getMapData(userToken.accessToken, stateRef.current.dateRange, stateRef.current.selectedFilters, stateRef)
      const { filteredData, fullData } = updateData(data, userDeviceIdsToAttributeMap, stateRef.current.selectedFilters, stateRef)
      const liveData = await getLiveMapData(userToken.accessToken, liveTime, stateRef.current.selectedFilters, stateRef)
      updateLiveMapData(liveData, userDeviceIdsToAttributeMap, stateRef.current.selectedFilters, stateRef)
      setEnableSubmit(true)
    } catch (err) {
      console.log('err', err)
      setEnableSubmit(true)
    } finally {
      setEnableSubmit(true)
    }
    // const newFilterData = filterAndUpdateData(fullData, stateRef.current.selectedFilters) // updateData sets filterAndUpdateData
  }

  console.log('filterLoading, loading, fillFilters', filtersLoading, loading, fullFilters) //, 'stayPointMarkers', stayPointMarkers, ' userEventMarkers', userEventMarkers, ' routePolygons', routePolygons)
  console.log('why empty??')
  const kmReportRef = createRef()
  
  const renderButtonText = () => {
    if (kmReportRef && kmReportRef.current && kmReportRef.current !== null) {
      if (kmReportRef.current.KMReportVisible) {
        return 'Hide KM Report'
      } else {
        return 'Show KM Report'
      }
    } else {
      return 'Show KM Report'
    }
  }
  
  return (
    <>{userToken &&
      <>
          <div style={{ display: 'flex', justifyContent: 'center', gap: '15px', margin: '10px' }}>
          {
            (fullFilters && !filtersLoading)
              ? <>
              <MultiSelectDropdown
                key={'staff_type_ids'}
                placeholder={_.startCase('staff_type_ids')} fullFilters={fullFilters.staff_type_ids}
                property={'staff_type_ids'}
                selectedFilters={selectedFilters.staff_type_ids}
                updateSelectedFilters={updateSelectedFilters}
                updatecount={updatecount}
                mode={'single'}
                ></MultiSelectDropdown>
              <MultiSelectDropdown
                key={'staff_ids'}
                placeholder={_.startCase('staff_ids')} fullFilters={fullFilters.staff_ids}
                property={'staff_ids'}
                selectedFilters={selectedFilters.staff_ids}
                updateSelectedFilters={updateSelectedFilters}
                updatecount={updatecount}
                mode={'multiple'}
                ></MultiSelectDropdown>
              </>
              : <></>
          }
          {
            (!filtersLoading) && (!allowMapDateRange) &&
            <DatePicker onChange={setDate}
            defaultValue={dayjs(startDate.toDate())} />
          }
          {
            (!filtersLoading) && (allowMapDateRange) &&
           <RangePicker
            onChange={updateDateRange}
            />
          }
            {enableSubmit
            ? <Button type="primary" onClick={fetchData}>Submit</Button>
            : <Button type="dashed" loading>Submit</Button>}
            {enableSubmit
            ? <Button type="primary" disabled={
              selectedFilters.staff_ids.length !== 1
            } onClick={() => {
              if (kmReportRef && kmReportRef.current && kmReportRef.current !== null) {
                if (kmReportRef.current.KMReportVisible) {
                  setKMReportOpen(false)
                } else {
                  setKMReportOpen(true)
                }
                kmReportRef.current.setKMReportVisible(!kmReportRef.current.KMReportVisible)
              }
            }}>{KMReportOpen ? 'Hide KM Report' : 'Show KM Report'}</Button>
            : <Button type="dashed" loading>Show KM Report</Button>}

          </div>
          { (!loading) &&
            <div ref={containerDivRef} style={{ display: "flex" }}>
              <div ref={leftDivRef} style={{ flex: 1, height: "100%" }}>
                <DrawMap
                  markers={[bcoLocationMarkers, dairyFarmMarkers, locationMarkers, stayPointMarkers, userEventMarkers]}
                  polylines={[routePolygons]}
                  // polygons={[farmPolygons]}
                  showLocation={false}
                />
              </div>
              <div style={{ overflowY: "auto" }}>
                <div style={{ height: "100%" }}>
                  <KMReport
                    staffIdDropdownArray={selectedFilters.staff_ids}
                    userDeviceDropdownArray={selectedFilters.user_device_ids}
                    reportDateRange={dateRange}
                    ref={kmReportRef}
                  />
                </div>
              </div>
              
            </div> 
          }
    </>
  }</>
  )
}

// export { MapWrapper }
export default withAuthorization(MapWrapper, [])

// initialization = {initialization}
// setLocalStorageMapCords={setLocalStorageMapCords}
// // fullFilters = {fullFilters}
// selectedFilters = {selectedFilters}
// showFilters = {['staff_ids', 'staff_type_ids']}
// dateRange = {dateRange}
// updateDateRange = {updateDateRange}
// updatecount = {updatecount}
// updateSelectedFilters = {updateSelectedFilters}
// markers={[locationMarkers, stayPointMarkers, userEventMarkers]}

// const updateSelectedFilters = (property, values ) => {
//   console.log('new values', property, values)
//   const newFiters=_.cloneDeep(selectedFilters)
//   // update dependent filters
//   if (property === 'staff_ids') {
//     const user_device_ids = _.uniq(_.flatten(values.map((staff_id) => staffIdMap[staff_id].user_device_ids)))
//     const staff_type_ids = _.uniq(_.flatten(values.map((name) => staffIdMap[name].staff_type_ids)))
//     newFiters['user_device_ids'] = user_device_ids
//     newFiters['staff_type_ids'] = staff_type_ids
//   }  else if (property === 'staff_type_ids') {
//     const user_device_ids = _.uniq(_.flatten(values.map((name) => staffTypeIdMap[name].user_device_ids)))
//     const staff_ids = _.uniq(_.flatten(values.map((name) => staffTypeIdMap[name].staff_ids)))
//     newFiters['user_device_ids'] = user_device_ids
//     newFiters['staff_ids'] = staff_ids
//   } else if (property === 'user_device_ids') {
//     const staff_ids = _.uniq(_.flatten(values.map((name) => userDeviceIdsToAttributeMap[name].staff_id)))
//     const staff_type_ids = _.uniq(_.flatten(values.map((name) => userDeviceIdsToAttributeMap[name].staff_type_id)))
//     newFiters['staff_ids'] = staff_ids
//     newFiters['staff_type_ids'] = staff_type_ids
//   }
//   newFiters[property] = values
//   setSelectedFilters(newFiters)
//   const newFilterData = filterAndUpdateData(fullData, newFiters)
//   console.log('new filters', newFiters)
// }

// const startTime = moment().subtract(140, 'hours').format('YYYY-MM-DD HH:mm:ss')
// const [locFrom, stayPtFrom, routeFrom] = [startTime, startTime, startTime]
// let nowTime = new Date() // '2008-12-13 03:00:00' // when real data we get this will be current time, in simulation will keep increasing every 1 minute
// console.log('nowTime', nowTime)
// nowTime = moment(nowTime).format('YYYY-MM-DD HH:mm:ss')
// console.log('nowTime', nowTime)
// let [locTo, stayPtTo, routeTo] = [nowTime, nowTime, nowTime]
// console.log('locTo', locTo, 'stayPtTo', stayPtTo, 'routeTo', routeTo)
// console.log('locFrom', locFrom, 'stayPtFrom', stayPtFrom, 'routeFrom', routeFrom)

// const getAttributes = async (stateRef) => {
//   console.log('navigate', stateRef.current.navigate)
//   const url = `${backEndService}/api/bgLocation/attributes`
//   const config = {headers:{token: localStorage.getItem("accessToken")}}
//   const response = await axios.get(url,config)
//   const data = response.data
//   console.log('data', data.data)
//   return data.data
// }

// let backEndService = 'http://dev2.krushal.in:33001'
// const getMapData = async (dateRange) => {
//   let [from, to] = dateRange
//   from = from.format('YYYY-MM-DD HH:mm:ss')
//   to = to.format('YYYY-MM-DD HH:mm:ss')
//   // if(user_device_ids && user_device_ids.length > 0) user_device_ids = user_device_ids.map((id) => id.toString())
//   const [locFrom, stayPtFrom, routeFrom] = [from, from, from]
//   const [locTo, stayPtTo, routeTo] = [to, to, to]
//   const urlParams = { locFrom, locTo, stayPtFrom, stayPtTo, routeFrom, routeTo }
//   const qryParams = new URLSearchParams(urlParams)
//   const url = `${backEndService}/api/bgLocation/mapData?${qryParams.toString()}`
//   console.log('url', url)
//   const config = {headers:{token: localStorage.getItem("accessToken")}}
//   const response = await axios.get(url,config)
//   const data = response.data
//   //console.log('data', data.data)
//   return data.data
// }

// 10000 seconds have passed  query as though 30 mintues have passed
// nowTime = moment((new Date(new Date(nowTime).getTime() + 1000 * 60 * 30))).format('YYYY-MM-DD HH:mm:ss')
// nowTime = moment(new Date()).subtract(5, 'hours').subtract(30, 'minutes').format('YYYY-MM-DD HH:mm:ss')
// locTo = nowTime; stayPtTo = nowTime; routeTo = nowTime
// console.log('new time', nowTime)

// const initalizeData = () => {
//   return { markers, polylines, showLocation: true }
// }

// this is for regular updates, if date range changes, its been taken care of in updateDateRange
// useEffect(() => {
//   const interval = setInterval(() => {
//     const newDateRange = stateRef.current.dateRange
//     console.log('dateRange to got', newDateRange[1])
//     // if the toDate is today's date then update the toDate to current time
//     if (newDateRange[1].isSame(moment(), 'day')) {
//       newDateRange[1] = moment().endOf('day')
//     }
//     console.log('dateRange to updated', newDateRange[1])

//     setDateRange(newDateRange)
//     const newSelectedFilters = stateRef.current.selectedFilters
//     const newUserDeviceIdsToAttributeMap = stateRef.current.userDeviceIdsToAttributeMap
//     getMapData(userToken.accessToken, newDateRange)
//       .then(data => updateData(data, newUserDeviceIdsToAttributeMap, newSelectedFilters))
//       .catch(err => { console.log('err', err); throw err })
//   }, 3000000)
//   return () => clearInterval(interval)
// }, [])
// regular update only for live locations
