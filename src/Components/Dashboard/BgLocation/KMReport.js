import React, { useEffect, useState, createRef, useRef } from "react"
import { useSelector } from 'react-redux'

const {EXEC_TEAM, CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, CENTRAL_VET, VET_OPS, SALES_OPS, IT_ADMIN, withAuthorization} = require('../../user/authorize')

const { getStaffKMReport } = require('../../../router')
const { OCTable } = require('../../Common/AntTableHelper')

const getStaffKMDataForDate = async (userToken, staffIds, userDeviceIds, startDate, endDate) => {
  try {
    const headers = {
      useCase: 'Get Farmer Dairy Farm Locations',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const queryParams = {}
    queryParams.staff_id_array = staffIds
    queryParams.user_device_id_array = userDeviceIds
    queryParams.start_date = startDate
    queryParams.end_date = endDate
    const response = await getStaffKMReport(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const kmReportTableConfiguration = {
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  getAllDataAtOnce: true,
  disablePagination: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'travel_route_id',
  columns: [
    {
      title: 'Start Time',
      key: 'start_at',
    },
    {
      title: 'End Time',
      key: 'end_at',
    },
    {
      title: 'Distance (in km)',
      key: 'distance_in_km',
    },
    {
      title: 'Path',
      key: 'path',
    }
  ],
  columnConfiguration: {
    start_at: {
      headingText: 'Start Time',
      formatType: 'date',
      format: 'h:mm a',
    },
    end_at: {
      headingText: 'End Time',
      formatType: 'date',
      format: 'h:mm a',
    },
    distance_in_km: {
      formatType: 'double',
      format: {decimalPlaces: 2},
    },
    start_lat_lng: {
      formatType: 'location'
    },
    end_lat_lng: {
      formatType: 'location'
    },
    path: {
      formatType: 'link_google_map_directions',
      start_lat: 'start_lat',
      start_lng: 'start_lng',
      end_lat: 'end_lat',
      end_lng: 'end_lng',
    }
  }
}


const KMReport = React.forwardRef(({staffIdDropdownArray, userDeviceDropdownArray, reportDateRange}, ref) => {
  const userToken = useSelector((state) => state.user.userToken)
  const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    kmReportTableConfiguration.enableTableExport = true
    kmReportTableConfiguration.enableTableExportFromCloud = true
  }
  const [KMReportVisible, setKMReportVisible] = useState(false)

  const [reportData, setReportData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  const tableRef = useRef()

  /* useEffect(() => {
    const asyncUseEffect = async () => {
      try {
        if (KMReportVisible === true && staffIdDropdownArray.length === 1) {
          const staffIds = staffIdDropdownArray.map(object => {
            return object['value']
          })
          const userDeviceIds = userDeviceDropdownArray.map(object => {
            return object['value']
          })
          const startDate = reportDateRange[0].toDate()
          const endDate = reportDateRange[1].toDate()
          const staffKMReportResponse = await getStaffKMDataForDate(userToken, staffIds, userDeviceIds, startDate, endDate)
        }
        // updateData({ dairyFarms: farmerDairyFarmLocationsResponse.report }, {}, {}, stateRef)

      } catch (error) {
        console.log('uE aUE 10, error')
        console.log('uE aUE 10a, error = ', error)
        throw error
      }
    }
    asyncUseEffect()
  }, [staffIdDropdownArray, KMReportVisible]) */

  React.useImperativeHandle(ref, () => ({
    KMReportVisible, setKMReportVisible
  }))

  const loadCommentsCB = async (reportQueryParams, additionalParams) => {
    if (KMReportVisible === true && staffIdDropdownArray.length === 1) {
      const staffIds = staffIdDropdownArray.map(object => {
        return object['value']
      })
      const userDeviceIds = userDeviceDropdownArray.map(object => {
        return object['value']
      })
      const startDate = reportDateRange[0].toDate()
      const endDate = reportDateRange[1].toDate()
      const staffKMReportResponse = await getStaffKMDataForDate(userToken, staffIds, userDeviceIds, startDate, endDate)
      return staffKMReportResponse
    }
  }

  return KMReportVisible && (
    <div                     style={{
/*       position: "absolute",
 */      top: 0,
      bottom: 0,
      right: 0,
      width: 600,
      overflowY: "auto",
    }}
>
      <OCTable 
        ref={tableRef}
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={kmReportTableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        // postInitialLoadCallback={postInitialLoadCB}
        loadReportCallback={loadCommentsCB}
        // loadInitialFilterDataCallback={loadInitialFilterDataCB}
        // parentPageParams={pageParams}
        // showTableHeader={true}
        // omniSearchRowEnabled={true}
        // omniSearchRowBeginning={backToQueueListLinkUI}
        // omniSearchRowMiddle={viewAnimalDocumentsFarmerSnoozeAndFarmerUpdateStatusButtons}
      />

    </div>
  )
})

export {
  KMReport
}