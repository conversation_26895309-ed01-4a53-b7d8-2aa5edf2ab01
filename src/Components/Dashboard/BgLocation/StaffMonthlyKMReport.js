import { Space, Button } from "antd"

import { useCallback, useRef , useState} from "react"
import { OCTable } from "../../Common/AntTableHelper"
import { useDispatch, useSelector } from "react-redux"
import { getAllStaffMonthlyKmReport } from "../../../router"
import { clearUserToken } from "../../user/action"
import { useNavigate } from "react-router"
import dayjs from "dayjs"
import ToastersService from "../../../Services/toasters.service";

const lodashObject = require("lodash")

const { upsertApprovedStaffKM } = require('../../../router')

const { createURLQueryStringFromPageParams, addRefToEditableTableItemsIfNotAvailable, extractTableListWithUpdatedValues, validateEditableTable, deleteRefKeysFromObjectInObjectArray, extractEditableTableDelta } = require('../../Common/AntTableHelper')

const tableConfiguration = {
  tableNavigationUrl: '/staff-monthly-km-report',
  enableSelection: true,
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  showTableHeader: false,
  omniSearchEnabled: false,
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  enableTableExport: true,
  enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: "staff_month_id",
  columns: [
    {
      title: "Role",
      key: "staff_type",
    },
    {
      title: "Employee",
      key: "staff_name",
    },
    {
      title: "Vehicle Type",
      key: "vehicle_type",
    },
    {
      title: "Per KM Allowance",
      key: "vehicle_per_km_allowance",
    },
    {
      title: "Date",
      key: "travel_month",
    },
    {
      title: "KM",
      key: "applicable_monthly_km",
    },
    {
      title: "Approved KM",
      key: "approved_monthly_km",
    },
    {
      title: "Approved KM Saved Status",
      key: "approved_monthly_km_saved_status",
    },
    {
      title: "Allowance TA",
      key: "approved_ta",
    },
    {
      title: "Valid Travel Days",
      key: "applicable_travel_days_in_month",
    },
    {
      title: "Daily DA Amount",
      key: "daily_da_amount",
    },
    {
      title: "Approved DA",
      key: "approved_da",
    },
  ],
  columnConfiguration: {
    staff_type: {
      headingText: 'Roles',
      enableFilter: true,
      // type: 'default_array',
      type: 'multi_select_array',
      dataType: 'int',
      altColumn: 'staff_type_id',
      translateFilteredValuesToAlt: 'value_id',
      filters: [
        {value_id: 1000230001, value: 'BDM', text: 'BDM'},
        {value_id: 1000230003, value: 'Vet', text: 'Vet'},
        {value_id: 1000230004, value: 'Paravet', text: 'Paravet'},
      ],
      updateFilterDataDuringReportCall: true
    },
    vehicle_type: {
      headingText: 'Vehicle Type',
      enableFilter: true,
      // type: 'default_array',
      type: 'multi_select_array',
      dataType: 'int',
      altColumn: 'vehicle_type_id',
      translateFilteredValuesToAlt: 'value_id',
      filters: [
        {value_id: 1001240001, value: '2W', text: '2W'},
        {value_id: 1001240002, value: '4W', text: '4W'},
      ],
      updateFilterDataDuringReportCall: true
    },
    staff_name: {
      headingText: 'Employee Name',
      enableFilter: true,
      type: 'single_select_array',
      dataType: 'uuid',
      altColumn: 'staff_id',
      translateFilteredValuesToAlt: 'value_uuid',
      filterValuesKey: 'staff_filter_values',
      updateFilterDataDuringReportCall: true
    },
    travel_month: {
      headingText: "Date",
      enableFilter: true,
      type: "dayjs_range",
      formatType: "date",
      format: "MMM-YYYY",
      enableSort: true,
      sortColumnType: "date",
      defaultSort: "descend",
      defaultFilteredValue: [[dayjs().subtract(30, 'days'), dayjs().add(1, 'days')]]
    },
    applicable_monthly_km: {
      headingText: "Employee Distance in KM",
      enableFilter: true,
      type: "number_range",
      enableSort: true,
      sortColumnType: "double",
      formatType: 'double',
      format: {decimalPlaces: 2},
    },
    staff_id: {
      headingText: 'Staff Id',
      dataType: 'uuid',
      enableFilter: true,
      type: 'uuid',
    },
    vehicle_per_km_allowance: {
      heading: 'Vehicle Per KM Allowance',
      formatType: 'rupee',
    },
    approved_ta: {
      heading: 'Approved TA',
      formatType: 'rupee',
    },
    daily_da_amount: {
      heading: 'Daily DA Amount',
      formatType: 'rupee',
    },
    approved_da: {
      heading: 'Approved TA',
      formatType: 'rupee',
    },
    approved_monthly_km: {
      width: '250px',
      editable: true,
      // editableReadOnly: true,
      editableStyle: {width: '250px'},
      editableType: 'positive-decimal_2',
      editableLabel: { en: "Final Monthly Approved KM", mr: "अंतिम मासिक मंजूर KM" },
      editablePlaceHolderText: {ul: "Approved KM"},
      editableValidations: [{ type: "Mandatory" },],
      editableErrorMessage: "KM mandatory and should be a positive number",
      formatType: 'double',
      format: {decimalPlaces: 2},
    },
    approved_monthly_km_saved_status: {
      headingText: 'Monthly KM Saved?',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'Approved', value: 'Approved'},
        { text: 'Not Approved', value: 'Not Approved' },
      ],
      updateFilterDataDuringReportCall: true,
    },
  },
}

const saveApprovedStaffMonthlyKM = async (userToken, staffApprovedMonthlyKMDeltaObject) => {
  try {
    console.log('OCFD gFD 1')
    const headers = {
      useCase: 'Set Staff Approved Monthly KM',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const dataToBeSaved = {
      period_of_approved_km: 'month'
    }
    if (staffApprovedMonthlyKMDeltaObject !== undefined) {
      // {anyChange, toBeInsertedList, toBeUpdatedListKeyMap, toBeDeletedKeyList}      
      if ((staffApprovedMonthlyKMDeltaObject.anyChange !== undefined && staffApprovedMonthlyKMDeltaObject.anyChange === true)
        || staffApprovedMonthlyKMDeltaObject.anyChange === undefined) {
        if (staffApprovedMonthlyKMDeltaObject.toBeInsertedList !== undefined) {
          dataToBeSaved.staff_approved_km_to_be_inserted_list = staffApprovedMonthlyKMDeltaObject.toBeInsertedList
        }
        if (staffApprovedMonthlyKMDeltaObject.toBeUpdatedListKeyMap !== undefined) {
          dataToBeSaved.staff_approved_km_to_be_updated_key_map = staffApprovedMonthlyKMDeltaObject.toBeUpdatedListKeyMap
        }
        if (staffApprovedMonthlyKMDeltaObject.toBeDeletedKeyList !== undefined) {
          dataToBeSaved.staff_approved_km_to_be_deleted_list = staffApprovedMonthlyKMDeltaObject.toBeDeletedKeyList
        }
      }
    }
    const response = await upsertApprovedStaffKM(headers, dataToBeSaved)
    return response.data
  } catch (error) {
    console.log('OCFD gFD 10, error')
    console.log('OCFD gFD 10a, error = ', error)
    throw error
  }
}

const StaffMonthlyKMReport = () => {
    const tableRef = useRef()
    const userToken = useSelector((state) => state.user.userToken)
    const [reportData, setReportData] = useState([])
    const [reportDataInDB, setReportDataInDB] = useState([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState([])
    const dispatch = useDispatch();
    const navigate = useNavigate()

    const loadReport = useCallback(
      async (userToken, queryParams) => {
        try {
          const headers = {
            token: userToken.accessToken, //authToken
          };
          const response = await getAllStaffMonthlyKmReport(headers, queryParams);
          if (response?.status === 201) {
            return response?.data;
          }
        } catch (error) {
          if (error?.response?.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
          }
        }
      },
      [dispatch, navigate]
    );

    const loadReportCB = async (reportQueryParams, additionalParams) => {
      const reportResponse = await loadReport(userToken, reportQueryParams, additionalParams)
      const clonedReportForReference = lodashObject.cloneDeep(reportResponse.report)
      setReportDataInDB(clonedReportForReference)
      // let numberOfFarms = Array.isArray(farmListFromDB) ? farmListFromDB.length : 0
      for (const reportRow of reportResponse.report) {
        if (reportRow.approved_monthly_km === undefined || reportRow.approved_monthly_km === null) {
          reportRow.approved_monthly_km = reportRow.applicable_monthly_km
        }
      }
      addRefToEditableTableItemsIfNotAvailable(reportResponse.report, tableConfiguration)
      // setFarmList(clonedFarmList)
      return reportResponse
    }

    const handleViewStaffClick = (event) => {
      const selectedRow = reportData.filter((object) => {
        return (object['staff_month_id'] === selectedRowKeys[0])
      })
  
      const staffId = selectedRow[0]['staff_id']
      const travelMonth = selectedRow[0]['travel_month']
      
      const filterParams = { 
        staff_id: [staffId],
        travel_date: [[dayjs(travelMonth).startOf('month'), dayjs(travelMonth).endOf('month')]],
        travel_applicable_day: ['Yes']
        // health_score: [ [4, 5] ] 
      }; // Example query parameters
      const urlQueryString = createURLQueryStringFromPageParams(undefined, filterParams, undefined)
    
      
      const navigationLink = `/staff-daily-km-report`
      if (event.ctrlKey || event.metaKey) {
        window.open(navigationLink + `?${urlQueryString}`, '_blank')
      } else {
        navigate({
          pathname: navigationLink,
          search: `?${urlQueryString}`
        })
      }
    }

    const handleViewSaveApprovedEmployeeMonthKMsClick = async (event) => {
      // validate
      // if not valid, throw error
      // if not, save
      // extract table
      // call save API
      console.log('SMKMR hVSAEMKC 1')
      const validationFailed = validateEditableTable(reportData, tableConfiguration)
      if (validationFailed) {
        ToastersService.failureToast('Editable Values are not valid')
      } else {
        const updatedList = extractTableListWithUpdatedValues(reportData, tableConfiguration)
        console.log('SMKMR hVSAEMKC 2, updatedList = ', updatedList)
        deleteRefKeysFromObjectInObjectArray(updatedList, tableConfiguration)
        console.log('SMKMR hVSAEMKC 3, updatedList = ', updatedList)
        const employeeMonthlyApprovedKMDeltaResult = extractEditableTableDelta(reportDataInDB, updatedList, 'staff_month_id', 'user_distance_month_row_id', 'user_distance_month_row_id')
        console.log('SMKMR hVSAEMKC 4, employeeMonthlyApprovedKMDeltaResult = ', employeeMonthlyApprovedKMDeltaResult)
        if (employeeMonthlyApprovedKMDeltaResult.anyChange) {
          // toBeInsertedList, toBeUpdatedListKeyMap, toBeDeletedKeyList
          const returnValue = await saveApprovedStaffMonthlyKM(userToken, employeeMonthlyApprovedKMDeltaResult)
          if (returnValue.return_code === 0) {
            ToastersService.successToast('Approved KMs successfully saved')
            // reload
            tableRef.current.reloadDataAndMoveToPageOne()
          } else {
            ToastersService.failureToast('Error in saving data')
          }
        } else {
          ToastersService.warningToast('There was nothing changed. Hence nothing was saved')
        }
      }
    }
//     const searchParam = "<stringified json with sid (staff id) and rD (report date)>"
// const navigationLink = "/bgLocations "
//     if (event.ctrlKey || event.metaKey) {
//       window.open(navigationLink + '?qP=' + searchParam,_blank')
//     } else {
//       navigate({
//         pathname: navigationLink,
//         search: ?qP=${searchParam }
//       })


  return (
    <>
      <div style={{ textAlign: 'center', fontSize: '18px', fontWeight: 'bold', marginTop: '12px', marginBottom: '4px' }}>
        Employee Monthly KM Report
      </div>
      <Space style={{marginTop:'8px', marginBottom:'8px'}}>
        <Button size="small" style={{ marginLeft: 8 }} disabled={selectedRowKeys.length !== 1} onClick={handleViewStaffClick}>View Details</Button>
        <Button size="small" style={{ marginLeft: 8 }} onClick={handleViewSaveApprovedEmployeeMonthKMsClick}>Save Approved KMs For Employees</Button>
      </Space>
      <OCTable
        ref={tableRef}
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={tableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        loadReportCallback={loadReportCB}
        // loadInitialFilterDataCallback={loadInitialFilterDataCB}
      />
    </>
  );
};
export default StaffMonthlyKMReport
