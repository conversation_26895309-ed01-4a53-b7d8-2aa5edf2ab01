// import { lazy, useState, useCallback, useEffect, Suspense } from "react";
// this is the original App.js for maps, not be used inside other projects
import './App.css'
import React from 'react'

import { MapWrapper } from './mapWrapper'
const showMap = 1
function App (props) {
  // eslint-disable-next-line react/prop-types
  // const query = new URLSearchParams(props.location.search)
  // const backEndService = query.get('back_end_service')
  // console.log(backEndService)// 123
  return (
    showMap
    // <MapWrapper center={[52.2297, 21.01178]} />
      ? <MapWrapper userDevicesIds={null} />
      : <>Not showing Map</>
  )
}

export default App

// "react-leaflet-markercluster": "^4.0.0-rc1",
// ,
// "react": "^18.1.0",
// "react-dom": "^18.1.0",
// "react-leaflet": "^4.2.1",
// "react-leaflet-draw": "^0.20.4",
// "react-router-dom": "^5.2.0",
// "react-tabs": "^6.0.0",

//  "@testing-library/jest-dom": "^5.11.4",
// "@testing-library/react": "^11.1.0",
// "@testing-library/user-event": "^12.1.10",
