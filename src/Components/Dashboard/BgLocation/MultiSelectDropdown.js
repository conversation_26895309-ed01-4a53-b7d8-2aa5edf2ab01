/* eslint-disable react/prop-types */
import React from 'react'
import { Select } from 'antd'
import './MapComponents.css'

const { Option } = Select

const MultiSelectDropdown = (props) => {
  console.log('multi select dropdown')
  console.log(props)
  //     const options = [];
  // for (let i = 10; i < 36; i++) {
  //   options.push({
  //     label: i.toString(36) + i,
  //     value: i.toString(36) + i,
  //   });
  // }
  // const handleChange = (value) => {
  //   console.log(`selected ${value}`);
  // };
  const { placeholder, fullFilters, property, selectedFilters, updateSelectedFilters, updatecount, mode } = props
  // console.log('multi select dropdown', fullFilters, property, selectedFilters)
  //  const options = fullFilters[property].map((item) => ({
  //     label: item,
  //     value: item
  // }));
  // const mode = 'tags'

  return (
    <>
    {
        property && fullFilters && fullFilters.length &&
          <Select
            mode={mode}
            maxTagCount={1}
            maxTagTextLength={10}
            showSearch
            className='multiSelect'
           optionFilterProp="children"
            placeholder={placeholder}
      //      defaultValue={selectedFilters[property]}
            value={selectedFilters}
            updatecount={updatecount}
            onChange={(value) => updateSelectedFilters(property, value)}
        >
            {fullFilters.map((option) => (
            <Option key={option.label} value={option.value}>
                {option.label}
            </Option>
            ))}
         </Select>
           }
    </>
  )
}

export default MultiSelectDropdown

/* <Select
      mode={mode}
  //  className='dateRangePicker'
      placeholder={placeholder}
    //  defaultValue={['a10', 'c12']}
    defaultValue={selectedFilters[property]}
      onChange={(value)=>updateSelectedFilters(property,value)}
      options={options}
    /> */
