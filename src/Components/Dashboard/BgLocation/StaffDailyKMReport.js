import { <PERSON>, Button } from "antd"

import { useCallback, useRef, useEffect, useState} from "react"
import { OCTable } from "../../Common/AntTableHelper"
import { useDispatch, useSelector } from "react-redux"
import { getAllStaffDailyKmReport } from "../../../router"
import { clearUserToken } from "../../user/action"
import { useNavigate } from "react-router"
import { useSearchParams } from 'react-router-dom'
import dayjs from "dayjs"
import ToastersService from "../../../Services/toasters.service"

const lodashObject = require("lodash")

const { upsertApprovedStaffKM, upsertApprovedStaffTravelDay } = require('../../../router')

const { createPageParamsFromURLString, createURLQueryStringFromPageParams, addRefToEditableTableItemsIfNotAvailable, extractTableListWithUpdatedValues, validateEditableTable, deleteRefKeysFromObjectInObjectArray, extractEditableTableDelta } = require('../../Common/AntTableHelper')

const tableConfiguration = {
  tableNavigationUrl: '/staff-daily-km-report',
  enableSelection: true,
  defaultPageSize: 40,
  enableHorizontalScrolling: true,
  showTableHeader: true,
  omniSearchEnabled: false,
  enableColumnConfiguration: true,
  // getAllDataAtOnce: true,
  // disablePagination: true,
  // enableTableExport: true,
  // enableTableExportFromCloud: true,
  // actionsRowEnabled: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: "staff_date_id",
  columns: [
    {
      title: "Role",
      key: "staff_type",
    },
    {
      title: "Employee",
      key: "staff_name",
    },
    {
      title: "Vehicle Type",
      key: "vehicle_type",
    },
    {
      title: "Per KM Allowance",
      key: "vehicle_per_km_allowance",
    },
    {
      title: "Date",
      key: "travel_date",
    },
    {
      title: "Day Of Week",
      key: "day_of_week",
      dataIndex: "travel_date",
    },
    {
      title: 'Travel Applicable?',
      key: 'travel_applicable_day'
    },
    {
      title: 'Travel Applicable Saved Status',
      key: 'travel_applicable_day_saved_status'
    },
    {
      title: "KM",
      key: "user_distance_in_km",
    },
    /* {
      title: "Approved KM",
      key: "user_distance_date",
    }, */
    {
      title: "Allowance For Date",
      key: "allowance_for_date",
    },
  ],
  columnConfiguration: {
    staff_type: {
      headingText: 'Roles',
      enableFilter: true,
      // type: 'default_array',
      type: 'multi_select_array',
      dataType: 'int',
      altColumn: 'staff_type_id',
      translateFilteredValuesToAlt: 'value_id',
      filters: [
        {value_id: 1000230001, value: 'BDM', text: 'BDM'},
        {value_id: 1000230003, value: 'Vet', text: 'Vet'},
        {value_id: 1000230004, value: 'Paravet', text: 'Paravet'},
      ],
      updateFilterDataDuringReportCall: true
    },
    vehicle_type: {
      headingText: 'Vehicle Type',
      enableFilter: true,
      // type: 'default_array',
      type: 'multi_select_array',
      dataType: 'int',
      altColumn: 'vehicle_type_id',
      translateFilteredValuesToAlt: 'value_id',
      filters: [
        {value_id: 1001240001, value: '2W', text: '2W'},
        {value_id: 1001240002, value: '4W', text: '4W'},
      ],
      updateFilterDataDuringReportCall: true
    },
    staff_name: {
      headingText: 'Employee Name',
      enableFilter: true,
      type: 'single_select_array',
      dataType: 'uuid',
      altColumn: 'staff_id',
      translateFilteredValuesToAlt: 'value_uuid',
      filterValuesKey: 'staff_filter_values',
      updateFilterDataDuringReportCall: true
    },
    travel_date: {
      headingText: "Date",
      enableFilter: true,
      type: "dayjs_range",
      formatType: "date",
      format: "DD-MMM-YYYY",
      enableSort: true,
      sortColumnType: "date",
      defaultSort: "descend",
      // defaultFilteredValue: [[dayjs().subtract(30, 'days'), dayjs().add(1, 'days')]]
      defaultFilteredValue: [[dayjs().subtract(30, 'days'), undefined]]
    },
    day_of_week: {
      headingText: "Day of Week",
      // enableFilter: true,
      // type: "dayjs_range",
      formatType: "date",
      format: "dddd",
      enableSort: true,
      sortColumnType: "date",
      enableFilter: true,
      // type: 'default_array',
      type: 'default_array',
      dataType: 'int',
      altColumn: 'day_of_week_as_number',
      translateFilteredValuesToAlt: 'value_id',
      filters: [
        {value_id: 7, value: 'Sunday', text: 'Sunday'},
        {value_id: 1, value: 'Monday', text: 'Monday'},
        {value_id: 2, value: 'Tuesday', text: 'Tuesday'},
        {value_id: 3, value: 'Wednesday', text: 'Wednesday'},
        {value_id: 4, value: 'Thursday', text: 'Thursday'},
        {value_id: 5, value: 'Friday', text: 'Friday'},
        {value_id: 6, value: 'Saturday', text: 'Saturday'},
      ],
      updateFilterDataDuringReportCall: true
    },
    travel_applicable_day: {
      headingText: 'Travel Day Applicable',
      enableFilter: true,
      type: 'default_array',
      dataType: 'int',
      filters: [
        { text: 'Yes', value: 'Yes', value_int: 1000105001 },
        { text: 'No', value: 'No', value_int: 1000105002 },
      ],
      translateFilteredValuesToAlt: 'value_int',
      altColumn: 'travel_applicable_day_reference_id',
      editable: true,
      // editableReadOnly: true,
      editableType: 'single-select-l10n_2',
      editableDropdownFieldNames:{ label: 'label', value: 'value' },
      editableLabel: { ul: "Travel Day Applicable"},
      editablePlaceHolderText: {ul: "Select One"},
      // editableValidations: [{ type: "Mandatory" }],
    },
    travel_applicable_day_saved_status: {
      headingText: 'Travel Day Approval Status Saved?',
      enableFilter: true,
      type: 'default_array',
      dataType: 'string',
      filters: [
        { text: 'Approved', value: 'Approved'},
        { text: 'Not Approved', value: 'Not Approved' },
      ],
      updateFilterDataDuringReportCall: true,
    },
    user_distance_in_km: {
      headingText: "KM",
      enableFilter: true,
      type: "number_range",
      enableSort: true,
      sortColumnType: "double",
      formatType: 'double',
      format: {decimalPlaces: 2},
    },
    staff_id: {
      headingText: 'Staff Id',
      dataType: 'uuid',
      enableFilter: true,
      type: 'uuid',
    },
    vehicle_per_km_allowance: {
      heading: 'Vehicle Per KM Allowance',
      formatType: 'rupee',
    },
    allowance_for_date: {
      heading: 'Allowance Per Date',
      formatType: 'rupee',
    },
    /* user_distance_date: {
      editable: true,
      // editableReadOnly: true,
      editableType: 'positive-decimal_2',
      editableLabel: { en: "Final Approved KM for Date", mr: "अंतिम मासिक मंजूर KM" },
      editablePlaceHolderText: {ul: "Approved KM"},
      editableValidations: [{ type: "Mandatory" },],
      editableErrorMessage: "KM mandatory and should be a positive number",
      formatType: 'double',
      format: {decimalPlaces: 2},
    } */
  },
}

const saveStaffKMApprovedDate = async (userToken, staffKMApprovedDateDeltaObject) => {
  try {
    console.log('OCFD gFD 1')
    const headers = {
      useCase: 'Set Staff Approved Monthly KM',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const dataToBeSaved = {
      period_of_approved_km: 'date'
    }
    if (staffKMApprovedDateDeltaObject !== undefined) {
      // {anyChange, toBeInsertedList, toBeUpdatedListKeyMap, toBeDeletedKeyList}      
      if ((staffKMApprovedDateDeltaObject.anyChange !== undefined && staffKMApprovedDateDeltaObject.anyChange === true)
        || staffKMApprovedDateDeltaObject.anyChange === undefined) {
        if (staffKMApprovedDateDeltaObject.toBeInsertedList !== undefined) {
          dataToBeSaved.staff_km_approved_date_to_be_inserted_list = staffKMApprovedDateDeltaObject.toBeInsertedList
        }
        if (staffKMApprovedDateDeltaObject.toBeUpdatedListKeyMap !== undefined) {
          dataToBeSaved.staff_km_approved_date_to_be_updated_key_map = staffKMApprovedDateDeltaObject.toBeUpdatedListKeyMap
        }
        if (staffKMApprovedDateDeltaObject.toBeDeletedKeyList !== undefined) {
          dataToBeSaved.staff_km_approved_date_to_be_deleted_list = staffKMApprovedDateDeltaObject.toBeDeletedKeyList
        }
      }
    }
    const response = await upsertApprovedStaffTravelDay(headers, dataToBeSaved)
    return response.data
  } catch (error) {
    console.log('OCFD gFD 10, error')
    console.log('OCFD gFD 10a, error = ', error)
    throw error
  }
}

const StaffDailyKMReport = () => {
  const tableRef = useRef()
  const [searchParams] = useSearchParams()
  const pageParams = createPageParamsFromURLString(tableConfiguration, searchParams)

  const userToken = useSelector((state) => state.user.userToken)
  const [preLoadFilterValuesSetStatus, setPreLoadFilterValuesSetStatus] = useState(false);
  const [reportData, setReportData] = useState([])
  const [reportDataInDB, setReportDataInDB] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const dispatch = useDispatch();
  const navigate = useNavigate()

  const loadReport = useCallback(
    async (userToken, queryParams) => {
      try {
        const headers = {
          token: userToken.accessToken, //authToken
        };
        const response = await getAllStaffDailyKmReport(headers, queryParams);
        if (response?.status === 201) {
          return response?.data;
        }
      } catch (error) {
        if (error?.response?.status === 401) {
          dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
        }
      }
    },
    [dispatch, navigate]
  );

  const loadReportCB = async (reportQueryParams, additionalParams) => {
    const reportResponse = await loadReport(userToken, reportQueryParams, additionalParams)
    if (reportResponse.return_code === 0) {
      // let numberOfFarms = Array.isArray(farmListFromDB) ? farmListFromDB.length : 0
      for (const reportRow of reportResponse.report) {
        /* if (reportRow.user_distance_date === undefined || reportRow.user_distance_date === null) {
          reportRow.user_distance_date = reportRow.user_distance_in_km
        } */
        reportRow.travel_applicable_day_list = [{"value": 1000105001, "label": "Yes"}, {"value": 1000105002, "label": "No"}]
        reportRow.travel_applicable_day = [reportRow.travel_applicable_day_reference_id]
      }
      const clonedReportForReference = lodashObject.cloneDeep(reportResponse.report)
      setReportDataInDB(clonedReportForReference)
      addRefToEditableTableItemsIfNotAvailable(reportResponse.report, tableConfiguration)
      // setFarmList(clonedFarmList)
    }
    return reportResponse
}

  const handleViewStaffClick = (event) => {
    const selectedRow = reportData.filter((object) => {
      return (object['staff_date_id'] === selectedRowKeys[0])
    })

    const staffId = selectedRow[0]['staff_id']
    const reportDate = selectedRow[0]['travel_date']
    const queryParameters = {
      sId: staffId,
      rD: reportDate
    }
    // navigate({
    //   pathname: `../oc/farmer-details`,
    //   search: `?qP=${encodedParamsAsString}`
    // })
    const queryParametersAsString = JSON.stringify(queryParameters)
    const encodedParamsAsString = encodeURIComponent(queryParametersAsString)
    const navigationLink = `/bgLocations`
    if (event.ctrlKey || event.metaKey) {
      window.open(navigationLink + `?qP=${encodedParamsAsString}`, '_blank')
    } else {
      navigate({
        pathname: navigationLink,
        search: `?qP=${encodedParamsAsString}`
      })
    }
  }
//     const searchParam = "<stringified json with sid (staff id) and rD (report date)>"
// const navigationLink = "/bgLocations "
//     if (event.ctrlKey || event.metaKey) {
//       window.open(navigationLink + '?qP=' + searchParam,_blank')
//     } else {
//       navigate({
//         pathname: navigationLink,
//         search: ?qP=${searchParam }
//       })

  const handleViewSaveApprovedEmployeeDateKMsClick = async (event) => {
    // validate
    // if not valid, throw error
    // if not, save
    // extract table
    // call save API
    console.log('SMKMR hVSAEDKC 1')
    const validationFailed = validateEditableTable(reportData, tableConfiguration)
    if (validationFailed) {
      ToastersService.failureToast('Editable Values are not valid')
    } else {
      const updatedList = extractTableListWithUpdatedValues(reportData, tableConfiguration)
      console.log('SMKMR hVSAEDKC 2, updatedList = ', updatedList)
      deleteRefKeysFromObjectInObjectArray(updatedList, tableConfiguration)
      console.log('SMKMR hVSAEDKC 3, updatedList = ', updatedList)
      const employeeKMApprovedDateDeltaResult = extractEditableTableDelta(reportDataInDB, updatedList, 'staff_date_id', 'travel_applicable_day_row_id', 'travel_applicable_day_row_id')
      console.log('SMKMR hVSAEDKC 4, employeeDateBasedApprovedKMDeltaResult = ', employeeKMApprovedDateDeltaResult)
      if (employeeKMApprovedDateDeltaResult.anyChange) {
        // toBeInsertedList, toBeUpdatedListKeyMap, toBeDeletedKeyList
        const returnValue = await saveStaffKMApprovedDate(userToken, employeeKMApprovedDateDeltaResult)
        if (returnValue.return_code === 0) {
          ToastersService.successToast('Approved KMs successfully saved')
          // reload
          tableRef.current.reloadDataAndMoveToPageOne()
        } else {
          ToastersService.failureToast('Error in saving data')
        }
      } else {
        ToastersService.warningToast('There was nothing changed. Hence nothing was saved')
      }
    }
  }

  useEffect(()=>{
    /* const referenceMap = global.references.referenceCategoryToReferencesMap
    if (referenceMap[10002300]) {
      const staffTypeFilter = referenceMap[10002300].map(object => {
        const returnValue = {
          value_id: object['reference_id'],
          value: object['reference_name_l10n'],
          text: object['reference_name_l10n'],
        }
        return returnValue
      })
      tableConfiguration.columnConfiguration.staff_type.filters = staffTypeFilter
    } */
    setPreLoadFilterValuesSetStatus(true)
  },[])

  return (preLoadFilterValuesSetStatus !== true) ? null : (
    <>
      <div style={{ textAlign: 'center', fontSize: '18px', fontWeight: 'bold', marginTop: '12px', marginBottom: '4px' }}>
        Employee Daily KM Report
      </div>
      <Space style={{marginTop:'8px', marginBottom:'8px'}}>
        <Button size="small" style={{ marginLeft: 8 }} disabled={selectedRowKeys.length !== 1} onClick={handleViewStaffClick}>View Details</Button>
        <Button size="small" style={{ marginLeft: 8 }} onClick={handleViewSaveApprovedEmployeeDateKMsClick}>Save Approved KMs For Employees</Button>
      </Space>
      <OCTable
        ref={tableRef}
        reportData={reportData}
        setReportData={setReportData}
        tableConfiguration={tableConfiguration}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        loadReportCallback={loadReportCB}
        parentPageParams={pageParams}
        // loadInitialFilterDataCallback={loadInitialFilterDataCB}
      />
    </>
  );
};
export default StaffDailyKMReport
