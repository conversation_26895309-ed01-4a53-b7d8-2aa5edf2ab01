/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, MapContainer, TileLayer, LayersControl, LayerGroup, Polygon, CircleMarker, useMap } from 'react-leaflet'
import tileLayer from './util/tileLayer'
import React, { useState, useEffect } from 'react'
import { useGeolocation } from 'react-use'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import './MapComponents.css'
import { MarkerIcon } from './types'
import { DatePicker } from 'antd'
import MultiSelectDropdown from './MultiSelectDropdown'
import _ from 'lodash'
import moment from 'moment'
import dayjs from 'dayjs'
const { RangePicker } = DatePicker

const defaultInit = { center: [12.897579, 77.4869199], zoom: 10 }
const getLocalStorageMapCords = () => {
  const zoom = localStorage.getItem('zoom') || defaultInit.zoom
  const center = [localStorage.getItem('lat') || defaultInit.center[0], localStorage.getItem('lng') || defaultInit.center[1]]
  console.log('getLocalStorageMapCords', center, zoom)
  return { center, zoom }
}
const setLocalStorageMapCords = ({ center, zoom }) => {
  localStorage.setItem('zoom', zoom)
  localStorage.setItem('lat', center[0])
  localStorage.setItem('lng', center[1])
  console.log('setLocalStorageMapCords', center, zoom)
}

// L.AwesomeMarkers.Icon.prototype.options.prefix = 'ion';

// Following code is used for evaluating the settings for the markers, polylines and polygons
// It is used to evaluate the settings for the icon, iconBlink, title, color, fillColor and title
// The settings are evaluated based on the attribute selected in the config
// The attribute can be a fixed value, a value from the data or a value from the mapping
// The mapping is used to map the value from the data to a value from the config
// The config is used to get the value from the data
// The data is used to get the value from the data
const evalSettings = (config, attrKey, attrMapping, settingDataKey, attrData) => {
  try {
    // console.log('config', config, 'attrKey', attrKey, 'attrMapping', attrMapping, 'settingDataKey', settingDataKey, 'attrData', attrData)
    const evalValue = config[attrKey] === 'fixed'
      ? config[`${settingDataKey}Fixed`]
      : (config[attrMapping]
          ? config[settingDataKey][attrData[config[attrKey]]]
          : attrData[config[attrKey]])
    return evalValue
  } catch (e) {
    console.log('error in evalSettings', e)
    return null
  }
}

// MyMarkers component is used to render the markers
// It is used to render the markers based on the settings in the config
// it takes the markers and the key as props
// the markers are the data for the markers
// markers data is an array of objects

const MylayerOverlay = ({ config, children }) => {
  return config.meta.layerControl
    ? <LayersControl.Overlay key={config.meta.name} checked={config.meta.show} name={config.meta.name} >
    {children}
    </LayersControl.Overlay>
    : config.meta.show ? children : <></>
}

const MyMarkers = ({ markers, myKey }) => {
  myKey = myKey || 0
  // console.log('key', myKey)
  return <MylayerOverlay config={markers.config}>
   <LayerGroup>
       {
          markers.markers.map((markerData, index) => {
            // build icon and title from configAttribues
            // draw the marker based on meta config
            const calculatedIconUrl = evalSettings(markers.config, 'iconAttr', 'iconAttrMapping', 'iconData', markerData) || ''
            const calculatedIconBlink = evalSettings(markers.config, 'iconBlinkAttr', 'iconBlinkAttrMapping', 'iconBlinkData', markerData) || ''
            const calculatedTitle = evalSettings(markers.config, 'titleAttr', 'titleAttrMapping', 'titleData', markerData) || ''
            const calculatedColor = evalSettings(markers.config, 'colorAttr', 'colorAttrMapping', 'colorData', markerData) || 'white'
            // console.log('calculatedColor', calculatedColor, calculatedIconUrl, calculatedTitle)
            // console.log('drawing circle marker for ', markers.config.meta)

            console.log('title', calculatedTitle, 'markerData', markerData, 'index', index)
            if (markers.config.meta.type === 'icon') {
              const icon = L.icon({ iconUrl: calculatedIconUrl, className: calculatedIconBlink })
              return (
              <Marker key={myKey + index} position={markerData} icon={icon}>
                <Popup>{calculatedTitle}</Popup>
            </Marker>)
            } else if (markers.config.meta.type === 'circle') {
              console.log('drawing circle marker for ', markerData.config)
              return (
            <CircleMarker key={myKey + index} center={markerData} pathOptions={{ color: calculatedColor }} radius={20} >
              <Popup>{calculatedTitle}</Popup>
            </CircleMarker>)
            } else if (markers.config.meta.type === 'awesome') {
              const icon = L.divIcon({
                className: 'custom-icon-marker',
                iconSize: L.point(30, 30),
                html: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" class="marker"><path fill-opacity="0.25" d="M16 32s1.427-9.585 3.761-12.025c4.595-4.805 8.685-.99 8.685-.99s4.044 3.964-.526 8.743C25.514 30.245 16 32 16 32z"/><path stroke="#fff" fill="${calculatedColor}" d="M15.938 32S6 17.938 6 11.938C6 .125 15.938 0 15.938 0S26 .125 26 11.875C26 18.062 15.938 32 15.938 32zM16 6a4 4 0 100 8 4 4 0 000-8z"/></svg>`,
                iconAnchor: [12, 24],
                popupAnchor: [9, -26]
              })
              return (<Marker key={myKey + index} position={markerData} icon={icon}>
              <Popup>{calculatedTitle}</Popup>
            </Marker>)
            } else if (markers.config.meta.type === 'located') {
              const markerHtmlStyles = `background-color: ${calculatedColor};`
              const icon = L.divIcon({
                className: 'locatedAnimation',
                iconSize: L.point(17, 17),
                html: `<div style="${markerHtmlStyles}" class="locatedAnimation"></div>`
              })
              // console.log('icon', icon)
              return (
              <Marker key={myKey + index} position={markerData} icon={icon} >
                <Popup>{calculatedTitle}</Popup>
              </Marker>
              )
            } else {
              return <></>
            }
          })
      }
      </LayerGroup>
    </MylayerOverlay>
}

const MyPolylines = ({ polylines, myKey }) => {
  myKey = myKey || 0
  // console.log('key', myKey)
  return <LayersControl.Overlay key={polylines.config.meta.name} checked name={polylines.config.meta.name} >
      <LayerGroup>
      {
        polylines?.polylines?.map((polylinesData, index) => {
        // console.log('polylinesData', polylinesData)
          const calculatedColor = evalSettings(polylines.config, 'colorAttr', 'colorAttrMapping', 'colorData', polylinesData)
          const calculatedTitle = evalSettings(polylines.config, 'titleAttr', 'titleAttrMapping', 'titleData', polylinesData)
          console.log('calculated color', calculatedColor, 'title', calculatedTitle, 'polylinesData', Object.keys(polylinesData), 'index', index, 'polylinesData.points', polylinesData.points)
          return (<Polyline
          key={index}
          positions={polylinesData.points || []}
          color={calculatedColor || 'white'}
          opacity={0.7}
          weight={4}
        >
        <Popup>{calculatedTitle || ''}</Popup>
      </Polyline>)
        })
      }
      </LayerGroup>
      </LayersControl.Overlay>
}

const MyPolyGons = ({ polygons, myKey }) => {
  myKey = myKey || 0
  // console.log('key', myKey, polygons)
}

const ShowLocation = (props) => {
  const [position, setPosition] = useState(null)
  const geolocation = useGeolocation({
    watch: true,
    positionOptions: {
      enableHighAccuracy: true
    },
    updateInterval: 10000,
    timeout: 10000,
    maximumAge: 2000
  })
  useEffect(() => {
    if (geolocation && geolocation.latitude && geolocation.longitude) {
      console.log('got new geolocation: ', geolocation)
      // setPosition({lat: geolocation.latitude, lng: geolocation.longitude});
      setPosition({ lat: 52.2297, lng: 21.01178 })
    }
  }, [geolocation])

  return (
    (position && position.lat && position.lng)
      ? <Marker
    position={position} key={120}
    icon={L.icon({
      iconUrl: 'https://unpkg.com/leaflet@1.0.3/dist/images/marker-icon-2x.png',
      className: 'blinking'
    })}
    />
      : <></>
  )
}
const onCalenderChange = (dates) => {
  console.log(dates)
}
const DrawMap = (props) => {
  const [initialization, setInitialization] = useState(getLocalStorageMapCords)// [52.2297, 21.01178], zoom: 16 }

  const MapEvents = () => {
    const map = useMap()

    useEffect(() => {
      const handleMoveEnd = () => {
        let center = map.getCenter()
        console.log('center 1', center)
        center = center.wrap()
        console.log('center 2', center)
        center = [center.lat, center.lng]
        console.log('center 3', center)
        const zoom = map.getZoom()
        setLocalStorageMapCords({ center, zoom })
        console.log('center', center, 'zoom', zoom)
      }

      map.on('moveend', handleMoveEnd)

      return () => {
        map.off('moveend', handleMoveEnd)
      }
    }, [map])

    return null
  }
  // console.log('props', props, 'selectedFilters', props.dateRange)
  let startDateDayJS
  let endDateDayJS
  if (props.dateRange && Array.isArray(props.dateRange)) {
    if (props.dateRange.length === 2 && moment(props.dateRange[0]) instanceof moment && moment(props.dateRange[1]) instanceof moment) {
      startDateDayJS = dayjs(props.dateRange[0].toDate())
      endDateDayJS = dayjs(props.dateRange[1].toDate())
    }
  }

  return (
    <>

    <MapContainer center={initialization.center} zoom={initialization.zoom} scrollWheelZoom={false}>
      <TileLayer {...tileLayer} />
      <MapEvents />
      <LayersControl position='topright'>
      {
        (props && props.markers && props.markers.length)
          ? props.markers.map((markers, index) => (
          <MyMarkers markers={markers} myKey = {index} key={index} />
          ))
          : <></>
      }
      {
        (props && props.polylines && props.polylines.length)
          ? props.polylines.map((polylines, index) => (
          <MyPolylines polylines={polylines} myKey={index} key={index}/>
          ))
          : <></>
      }
      {
        // Commenting for now till we have farms
        (props && props.polygons && props.polygons.length)
          ? props.polygons.map((polygons, index) => (
          <MyPolyGons polygons={polygons} myKey={index} key={index}/>
          ))
          : <></>
      }
      {props.showLocation ? <ShowLocation /> : <></>}
      </LayersControl>
    </MapContainer>
    </>
  )
}

export { MyPolylines, MyMarkers, DrawMap, MarkerIcon }

{ /* <div className='dateRangePicker'> */ }
{ /* <RangePicker
            onChange={props.updateDateRange}
          /> */ }
{ /* <RangePicker
            value={startDateDayJS && endDateDayJS ? [startDateDayJS, endDateDayJS] : []}
            onChange={props.updateDateRange}/> */ }
{ /* {
            (props && props.selectedFilters && props.showFilters && props.showFilters.length)
              ? (Object.keys(props.selectedFilters).map((property) => (
                  props.showFilters.includes(property) && property !== 'dateRange'
                    ? <MultiSelectDropdown
                key={property}
                placeholder={_.startCase(property)} fullFilters={props.fullFilters[property]} property={property}
                selectedFilters={props.selectedFilters[property]}
                updateSelectedFilters={props.updateSelectedFilters}
                updatecount={props.updatecount}
                mode={'multiple'}
              ></MultiSelectDropdown>
                    : <></>))
                )
              : <></>
          } */ }
{ /* </div> */ }

// defaultPickerValue={props.dateRange}
// defaultPickerValue={props.dateRange}
// defaultValue={props.dateRange}
//  value={props.dateRange}
// defaultValue= {[moment("2020-03-09 13:00"), moment("2020-03-27 13:17")]}

// <MultiSelectDropdown  placeholder={_.startCase('user_device_ids')}
//   fullFilters={props.fullFilters} property={'user_device_ids'} selectedFilters={props.selectedFilters}
//   updateSelectedFilters ={props.updateSelectedFilters}></MultiSelectDropdown>

// const styles = {located:{width:"17px",height:"17px",border:"1px solid #fff",borderRadius:"50%",background:"#2a93ee",animation:"borderPulse 2s infinite"}}

//  return <LayersControl.Overlay key={markers.config.meta.name} checked={false} name={markers.config.meta.name} >
//     <LayerGroup>
//     {
//       markers.markers.map((markerData, index) => {
//         // build icon and title from configAttrigutes
//         const calculatedIconUrl = evalSettings(markers.config, 'iconAttr', 'iconAttrMapping', 'iconData', markerData)
//         const calulatedIconBlink = evalSettings(markers.config, 'iconBlinkAttr', 'iconBlinkAttrMapping', 'iconBlinkData', markerData)
//         const calculatedTitle = evalSettings(markers.config, 'titleAttr', 'titleAttrMapping', 'titleData', markerData)
//         console.log('calculatediConUrl', calculatedIconUrl, 'calulatedIconBlinc', calulatedIconBlink, 'calculatedTitle', calculatedTitle)
//         const calculatedColor = evalSettings(markers.config, 'colorAttr', 'colorAttrMapping', 'colorData', markerData)
//      //   const icon = L.icon({ iconUrl: calculatedIconUrl, className: calulatedIconBlink })
//       //  console.log('icon', icon, 'title', calculatedTitle, 'markerData', markerData, 'index', index)
//         // return (
//         // <Marker key={myKey + index} position={markerData} icon={icon}>
//         //   <Popup>{calculatedTitle}</Popup>
//         // </Marker>)
//         return <CircleMarker key={myKey + index} center={markerData} pathOptions={{ color: calculatedColor }} radius={15} className={calulatedIconBlink} />
//       })
//     }
//     </LayerGroup>
//   </LayersControl.Overlay>

//   const icon = L.icon({ iconUrl: calculatedIconUrl, className: calulatedIconBlink })
//  console.log('icon', icon, 'title', calculatedTitle, 'markerData', markerData, 'index', index)
// return (
// <Marker key={myKey + index} position={markerData} icon={icon}>
//   <Popup>{calculatedTitle}</Popup>
// </Marker>)

// { /*  Yuse this to sandwitch any area above and switch on off <LayersControl> */ }
// { /* <LayersControl.Overlay name="Marker with popup"> */ }
// { /* </LayersControl.Overlay> */ }
// { /* </LayersControl> */ }
