
@keyframes fade {
  from {
    opacity: 0.5;
  }
}

.blinking {
  animation: fade 0.7s infinite alternate;
}
/* required for blinking marker */
.locatedAnimation {
  width: 17px;
  height: 17px;
  border: 1px solid #fff;
  border-radius: 50%;
  background: #2a93ee;
  animation: borderPulse 2s infinite;
}

.awesome-marker-icon {
  background: #fff;
  padding: 5px;
  width: 34px;
  height: 34px;
  border: 1px solid #ccc;
  display: flex;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  box-shadow: 0 1px 5px rgb(0 0 0 / 65%);
  border-radius: 4px;
}
.dateRangePicker {
  z-index: 10000;
  /* position: absolute;  */
 top: 4%; 
 left: 50%;
 transform: translate(-50%, -50%);

}
.multiSelect {
 min-width: 200px;
 max-width: 200px;
 /* min-height: 'min-content'; */
 width: 150px;
}

@keyframes borderPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 1);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(255, 0, 0, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
  }
}