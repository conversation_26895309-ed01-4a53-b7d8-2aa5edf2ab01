/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { DrawMap, MarkerIcon } from './MapComponents'
import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useSelector, useDispatch } from 'react-redux'
import TextField from '@mui/material/TextField'
import { Button, Col, Modal, Row, Table } from 'react-bootstrap'
import Autocomplete from '@mui/material/Autocomplete'
import MultiSelectDropdown from './MultiSelectDropdown'

import axios from 'axios'
// import { Atom } from 'react-loading-indicators'
import moment from 'moment'
import dayjs from 'dayjs'

import { extractBasedOnLanguage } from '@krushal-it/common-core'
import { set } from 'lodash'
import { BASE_URL, instance } from '../../../Services/api.service'
import ToastersService from '../../../Services/toasters.service'
import MapWrapper from './MapWrapper'
const _ = require('lodash')
const userLanguage = localStorage.getItem('userLanguage') || 'en'

const LocationFilterWrapper = (props) => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const userToken = useSelector((state) => state.user.userToken)
  const [filterConfig, setFilterConfig] = useState({
    staffConfig: []
  })

  const [staffList, setStaffList] = useState()

  const getStaffs = async (params) => {
    const reqBody = params || {}
    try {
      const response = await instance({
        url: '/staffs/all',
        method: 'POST',
        data: reqBody,
        headers: {
          'Content-Type': 'application/json',
          token: userToken.accessToken
        }
      })
      console.log(response)
      if (response.status === 201) {
        console.log('Staff List', response.data.data)
        setStaffList(response.data.data)
        const staffData = response.data.data.map((item) => {
          return {
            id: item.staff_id,
            label: extractBasedOnLanguage(item.staff_name_l10n, 'en')
          }
        })
        setFilterConfig({ staffConfig: response.data.data })
      }

      // setFarmersListLoader(false);
    } catch (error) {
      console.log(error)
      if (error.response.status === 401) {
        ToastersService.notify(
          'error',
          'Session Expired',
          'Please login again!'
        )
        // dispatch(logout());
      }
    }
  }

  // first get attributes, then update config, then get map data and then set map related data
  useEffect(() => {
    getStaffs()
  }, [])

  const onChanges = (type, value) => {
    console.log('onChanges', type, value)
    if (type === 'staff') {
      console.log('staff value', value)
      // setStaff(value)
    }
  }

  console.log('location Filters') //, 'stayPointMarkers', stayPointMarkers, ' userEventMarkers', userEventMarkers, ' routePolies', routePolies)
  return (
    <>
    <Filters filterConfig={filterConfig} itemChangeCallback={onChanges}></Filters>
     <MapWrapper />
     </>
  )
}

const Filters = ({ filterConfig, itemChangeCallback }) => {
  return (
    <>
            <div className="mb-3 p-3" style={{ border: '1px solid #ececec' }}>
          <Row>
            <Col>
              <Autocomplete
                style={{ minWidth: '200px' }}
                disableCloseOnSelect
                filterSelectedOptions
                options={filterConfig.staffConfig}
                onChange={(event, value) => itemChangeCallback('staff', value)}
                getOptionLabel={(option) => {
                  return extractBasedOnLanguage(option.staff_name_l10n, userLanguage)
                }}
                renderInput={(params) => {
                  return (
                    <TextField
                      {...params}
                      label="Select Staff"
                      placeholder="Search..."
                    />
                  )
                }}
              />
            </Col>
            </Row>
            </div>
    </>

  )
}

export { LocationFilterWrapper }
