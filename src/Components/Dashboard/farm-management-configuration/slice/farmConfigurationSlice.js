import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { BASE_URL } from "../../../../Services/api.service";
import { updateFormDataFromConfig } from "../../farm-management/utils";
import { fm_form_data } from "../../farm-management/components/form/formData";
import { updatedJsonResolver } from "@krushal-it/krushal-hook-form";
import ToastersService from "../../../../Services/toasters.service";

const handleGlobalError = (error) => {
  if (error.status === 401) {
    ToastersService.failureToast("Session Expired!");
    localStorage.removeItem("accessToken");
    localStorage.removeItem("resetToken");
    window.location.href = "/login";
  }
};

export const farmManagementConfigurationSlice = createApi({
  reducerPath: "farmManagementConfigurationSlice",
  baseQuery: async (args, api, extraOptions) => {
    const result = await fetchBaseQuery({
      baseUrl: BASE_URL,
      prepareHeaders: (headers, { getState }) => {
        const token = getState().user.userToken.accessToken;
        if (token) {
          headers.set("token", token);
        }
        return headers;
      },
    })(args, api, extraOptions);
    if (result.error) {
      handleGlobalError(result.error);
      return;
    }
    return result;
  },
  tagTypes: ["FM__CONFIGURATION_SLICE"],
  endpoints: (builder) => ({
    getQuestionsList: builder.query({
      query: () => ({
        url: "/task-config/list",
        method: "GET",
      }),
      keepUnusedDataFor: 0,
    }),

    getFmQuestions: builder.query({
      query: ({ question_id }) => ({
        url: `/task-config/${question_id}`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
      providesTags: ["FM__CONFIGURATION_SLICE"],
    }),
    saveFmQuestion: builder.mutation({
      query: ({ payload }) => ({
        url: "/task-config-update/basic",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["FM__CONFIGURATION_SLICE"],
    }),
    saveStatesQuestion: builder.mutation({
      query: ({ payload }) => ({
        url: "/task-config-update/states",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["FM__CONFIGURATION_SLICE"],
    }),
    saveConfigAdvices: builder.mutation({
      query: ({ payload }) => ({
        url: "/task-config-update/advice-state",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["FM__CONFIGURATION_SLICE"],
    }),
    uploadReferenceMedia: builder.mutation({
      query: ({ payload }) => ({
        url: "task-config-update/reference_media",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["FM__CONFIGURATION_SLICE"],
    }),
  }),
});

export const {
  useGetQuestionsListQuery,
  useGetFmQuestionsQuery,
  useSaveFmQuestionMutation,
  useSaveStatesQuestionMutation,
  useSaveConfigAdvicesMutation,
  useUploadReferenceMediaMutation,
} = farmManagementConfigurationSlice;
