import * as React from "react";
import TextField from "@mui/material/TextField";
import Autocomplete, { createFilterOptions } from "@mui/material/Autocomplete";
import { Controller, useFormContext } from "react-hook-form";

const filter = createFilterOptions<any>();

export default function TopicSelectComp({ field }: { field: any }) {
  const { control, setValue } = useFormContext();
  const options: any[] = field.options;
  const { label } = field;

  return (
    <Controller
      name={field.key}
      control={control}
      render={({ field }) => (
        <Autocomplete
          {...field}
          fullWidth
          onChange={(event, newValue) => {
            let finalValue;

            if (typeof newValue === "string") {
              finalValue = newValue;
            } else if (newValue && newValue.inputValue) {
              // If the user created a new entry, use the inputValue
              finalValue = newValue.inputValue;
            } else {
              finalValue = newValue?.value;
            }

            field.onChange(finalValue);
            setValue("topic_mr", newValue?.marathi);
            setValue("topic_ta", newValue?.tamil);
          }}
          filterOptions={(options, params) => {
            const filtered = filter(options, params);
            const { inputValue } = params;

            const isExisting = options.some(
              (option) => inputValue === option.label
            );
            if (inputValue !== "" && !isExisting) {
              filtered.push({
                inputValue,
                label: `Add "${inputValue}"`,
                value: inputValue,
              });
            }

            return filtered;
          }}
          selectOnFocus
          clearOnBlur
          handleHomeEndKeys
          options={options || []}
          getOptionLabel={(option) => {
            if (typeof option === "string") {
              return option;
            }
            if (option.inputValue) {
              return option.inputValue;
            }
            return option.label;
          }}
          renderOption={(props, option) => <li {...props}>{option.label}</li>}
          freeSolo
          renderInput={(params) => <TextField {...params} label={label} />}
        />
      )}
    />
  );
}
