import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>ton,
  Typography,
  Stack,
  CircularProgress,
  TextField,
  IconButton,
} from "@mui/material";
import { useSelector } from "react-redux";
import { useSaveConfigAdvicesMutation } from "../slice/farmConfigurationSlice";
import AddIcon from "@mui/icons-material/Add";
import {
  extractBasedOnLanguageMod,
  globalReferencesMapper,
} from "Utilities/Common/utils";

const AdviceForm = ({ questionId, formConfiguration }) => {
  const userLanguage = useSelector((state) => state.user.userLanguage);
  const [saveConfigAdvices, { isLoading: isSaving }] =
    useSaveConfigAdvicesMutation();
  const [advices, setAdvices] = useState([]);

  const languages = globalReferencesMapper(10706700)?.map((item) => ({
    label: extractBasedOnLanguageMod(item.reference_name_l10n, userLanguage),
    key: item.reference_information?.key,
  }));
  console.log("advices", advices);
  useEffect(() => {
    if (formConfiguration?.form_fields) {
      const adviceField = formConfiguration.form_fields.find(
        (field) => field.key === "advices"
      );
      if (adviceField?.options?.length) {
        const initialAdvices = adviceField.options.map((option) => ({
          id: option.id || `temp_${Date.now()}`,
          value: option.value || "",
          orderPriority: option.order_priority || "",
          ...languages.reduce(
            (acc, lang) => ({
              ...acc,
              [lang.key]: option.label_l10n[lang.key] || "",
            }),
            {}
          ),
        }));
        setAdvices(initialAdvices);
      }
    }
  }, [formConfiguration]);

  const handleAddAdvice = () => {
    const newAdvice = {
      id: `temp_${Date.now()}`,
      value: "",
      orderPriority: "",
      ...languages.reduce((acc, lang) => ({ ...acc, [lang.key]: "" }), {}),
    };
    setAdvices((prevAdvices) => [...prevAdvices, newAdvice]);
  };

  const handleAdviceChange = (index, field, value) => {
    setAdvices((prevAdvices) =>
      prevAdvices.map((advice, i) =>
        i === index ? { ...advice, [field]: value } : advice
      )
    );
  };

  const onSubmit = async () => {
    const payload = {
      question_uuid: questionId,
      advices: advices.map((advice) => ({
        id: advice.id && advice.id.startsWith("temp_") ? undefined : advice.id,
        label: advice[languages[0].key],
        label_l10n: languages.reduce(
          (acc, lang) => ({ ...acc, [lang.key]: advice[lang.key] || "" }),
          {}
        ),
        order_priority: advice.orderPriority
          ? Number(advice.orderPriority)
          : null,
        value: advice.value ? Number(advice.value) : null,
      })),
      weightage: 1,
    };

    try {
      await saveConfigAdvices({ question_id: questionId, payload });
      console.log("Advice configuration saved successfully");
    } catch (error) {
      console.error("Error saving advice configuration:", error);
    }
  };

  if (isSaving) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" p={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: "100%", overflowX: "auto" }}>
      <Box sx={{ minWidth: `${languages.length * 200 + 300}px` }}>
        <Typography variant="h6" gutterBottom>
          Farm Management Configuration Advice
        </Typography>

        <Stack spacing={2} sx={{ p: "5px" }}>
          {advices.map((advice, index) => (
            <Stack
              key={advice.id}
              direction="row"
              spacing={2}
              alignItems="flex-start"
              sx={{ flexWrap: "nowrap" }}
            >
              {languages.map((lang) => (
                <TextField
                  key={lang.key}
                  value={advice[lang.key]}
                  onChange={(e) =>
                    handleAdviceChange(index, lang.key, e.target.value)
                  }
                  placeholder={`Enter advice in ${lang.label}`}
                  label={lang.label}
                  InputLabelProps={{ shrink: true }}
                  required={lang.key === languages[0].key}
                  fullWidth
                  multiline
                />
              ))}
              <TextField
                type="number"
                value={advice.value}
                onChange={(e) =>
                  handleAdviceChange(index, "value", e.target.value)
                }
                placeholder="Id"
                label="Id"
                InputLabelProps={{ shrink: true }}
                size="small"
                sx={{ width: "80px", flexShrink: 0 }}
              />
              <TextField
                type="number"
                value={advice.orderPriority}
                onChange={(e) =>
                  handleAdviceChange(index, "orderPriority", e.target.value)
                }
                placeholder="Order"
                label="Order Priority"
                InputLabelProps={{ shrink: true }}
                size="small"
                sx={{ width: "100px", flexShrink: 0 }}
              />
              <IconButton color="error" sx={{ flexShrink: 0 }}></IconButton>
            </Stack>
          ))}
        </Stack>

        <Stack direction="row" spacing={2} justifyContent="flex-start" mt={2}>
          <Button
            startIcon={<AddIcon />}
            variant="outlined"
            onClick={handleAddAdvice}
            sx={{ padding: "6px 12px", fontSize: "0.875rem" }}
          >
            Add Advice
          </Button>

          <Button
            variant="contained"
            sx={{
              padding: "6px 12px",
              fontSize: "0.875rem",
              maxWidth: "150px",
            }}
            onClick={onSubmit}
            disabled={
              advices.length === 0 ||
              advices.some((advice) => !advice[languages[0].key])
            }
          >
            Submit
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};

export default AdviceForm;
