import { Box } from "@mui/material";
import { useUploadReferenceMediaMutation } from "../../slice/farmConfigurationSlice";
import ReferenceMediaForm from "./RerenceMediaForm";
import { useState } from "react";
import { Segmented, Spin } from "antd";
import {
  globalReferencesMapper,
  extractBasedOnLanguageMod,
} from "../../../../../Utilities/Common/utils";

type ReferenceMediaProps = {
  questionId: string;
  formConfiguration: {
    form_fields: Array<{
      key: string;
      selections: { values: { [key: string]: string } };
    }>;
  };
};

const ReferenceMedia = ({
  questionId,
  formConfiguration,
}: ReferenceMediaProps) => {
  const [uploadRefMedia, { isError, isLoading, isSuccess }] =
    useUploadReferenceMediaMutation();

  const languages = globalReferencesMapper(10706700);
  const languagesOptions = globalReferencesMapper(10706700)?.map((item: any) =>
    extractBasedOnLanguageMod(item.reference_name_l10n, "en")
  );
  const segmentedValueMap = languages.reduce((acc: any, reference: any) => {
    acc[extractBasedOnLanguageMod(reference.reference_name_l10n, "en")] =
      reference.reference_information.key;
    return acc;
  }, {});

  const [value, setValue] = useState<string | number>(languagesOptions[0]);

  const selectedLanguage = segmentedValueMap[value as string];

  const referenceMediaValue = formConfiguration?.form_fields?.find(
    (item: any) => item?.key === "reference_media"
  );

  const extractedValue =
    referenceMediaValue?.selections?.values?.[selectedLanguage] || "";

  if (isLoading)
    return (
      <Box
        sx={{ display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <Spin size="large" />
      </Box>
    );

  return (
    <Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          marginBottom: 5,
        }}
      >
        <Segmented
          options={languagesOptions}
          value={value}
          onChange={setValue}
          style={{ width: 200 }}
        />
      </Box>

      <ReferenceMediaForm
        questionId={questionId}
        uploadRefMedia={uploadRefMedia}
        resetData={extractedValue}
        selectedLang={selectedLanguage}
      />
    </Box>
  );
};

export default ReferenceMedia;
