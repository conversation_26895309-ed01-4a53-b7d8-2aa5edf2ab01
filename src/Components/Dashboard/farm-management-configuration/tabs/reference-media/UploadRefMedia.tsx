import React, { useEffect, useState } from "react";
import { PlusOutlined } from "@ant-design/icons";
import { Image, Upload, Spin, message } from "antd";
import type { GetProp, UploadFile, UploadProps } from "antd";
import { instance, BASE_URL } from "../../../../../Services/api.service";
import { useSelector } from "react-redux";

type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];

interface UploadRefMediaProps {
  onUploadComplete?: (docKey: string) => void;
  questionId: string;
  existingLink: string;
  existingDocumentId: string;
  disabled: boolean;
}

const getBase64 = (file: FileType): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

const UploadRefMedia: React.FC<UploadRefMediaProps> = ({
  onUploadComplete,
  questionId,
  existingDocumentId,
  existingLink,
  disabled,
}) => {
  console.log("resetDataof upload", existingDocumentId, existingLink);

  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const userToken = useSelector((state: any) => state.user.userToken);

  useEffect(() => {
    if (existingDocumentId) {
      setFileList([
        {
          name: "krushal",
          url: `${BASE_URL}/media/${existingDocumentId}`,
          uid: "-1",
        },
      ]);
    }
    if (existingLink) {
      setFileList([
        {
          name: "t",
          url: existingLink,
          uid: "-1",
        },
      ]);
    }
  }, []);

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }
    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };

  const uploadToDb = async (documentEntries: any) => {
    try {
      const response = await instance({
        url: "/document",
        method: "POST",
        data: { document_entries: documentEntries },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });

      if (response.status === 201) {
        message.success("File saved to db!");
        console.log("File saved to db!", response.data);
        return response.data?.data?.document_ids[0];
      } else {
        throw Error("Images not uploaded to db!");
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        message.error("token expired");
      } else if (error.response.status === 413) {
        message.error("file size is too large");
      }
    }
  };

  const uploadImageToServer = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("entity", "task"); // Setting entity as task

    try {
      setUploading(true);
      const response = await instance({
        url: "/document",
        method: "POST",
        data: formData,
        headers: {
          token: userToken.accessToken,
        },
      });

      if (response.status === 201) {
        message.success("File uploaded successfully!");
        const docKey = response?.data?.data?.doc_key;

        const documentEntries = [
          {
            document_type: "farm_management_ref",
            entity: "task",
            entity_1_id: questionId,
            url: docKey,
            document_information_json: {
              name: "file",
            },
          },
        ];
        const docId = await uploadToDb(documentEntries);

        onUploadComplete?.(docId);
        return docKey;
      }
      return null;
    } catch (error: any) {
      console.error("Upload error:", error);

      if (error.response?.status === 401) {
        // dispatch(clearUserToken(navigate, '/login', 'Session Expired!'));
      } else if (error.response?.status === 413) {
        message.error(error.response.data.error || "File size limit exceeded!");
      } else {
        message.error("Failed to upload file. Please try again.");
      }
      return null;
    } finally {
      setUploading(false);
    }
  };

  const beforeUpload = (file: File) => {
    // File type validation
    // const isImage = file.type.startsWith("image/");
    // if (!isImage) {
    //   message.error("You can only upload image files!");
    //   return Upload.LIST_IGNORE;
    // }

    // File size validation (5MB)
    const isLt5M = file.size / 1024 / 1024 < 3;
    if (!isLt5M) {
      message.error("File must be smaller than 3MB!");
      return Upload.LIST_IGNORE;
    }

    return true;
  };

  const handleChange: UploadProps["onChange"] = async ({ file, fileList }) => {
    setFileList(fileList);

    // Handle completed upload
    if (file.status === "done") {
      const docKey = await uploadImageToServer(file.originFileObj as File);
      file.url = docKey; // Store the docKey as the file's URL
    }
  };

  const uploadButton = (
    <button
      style={{
        border: 0,
        background: "none",
        width: "100%",
        height: "100%",
        cursor: "pointer",
      }}
      type="button"
    >
      {uploading ? (
        <Spin />
      ) : (
        <PlusOutlined
          onPointerEnterCapture={() => {}}
          onPointerLeaveCapture={() => {}}
        />
      )}
      <div style={{ marginTop: 8 }}>{uploading ? "Uploading" : "Upload"}</div>
    </button>
  );

  return (
    <div className="upload-container">
      <Upload
        listType="picture-card"
        fileList={fileList}
        onPreview={handlePreview}
        onChange={handleChange}
        beforeUpload={beforeUpload}
        maxCount={1}
        showUploadList={{
          showPreviewIcon: true,
          showRemoveIcon: true,
          showDownloadIcon: false,
        }}
        customRequest={({ onSuccess }) => {
          // Using custom request to prevent default upload
          onSuccess?.("ok");
        }}
        disabled={disabled}
      >
        {fileList.length >= 1 ? null : uploadButton}
      </Upload>

      {previewImage && (
        <Image
          style={{ display: "none" }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => {
              setPreviewOpen(visible);
              if (!visible) {
                setPreviewImage("");
              }
            },
          }}
          src={previewImage}
          alt="Preview"
        />
      )}
    </div>
  );
};

export default UploadRefMedia;
