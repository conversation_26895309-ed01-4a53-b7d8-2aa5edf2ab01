import React, { useEffect, useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Box,
  Card,
  CardContent,
  TextField,
  Typography,
  Switch,
  FormControlLabel,
  Stack,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import UploadRefMedia from "./UploadRefMedia";

const mediaFormSchema = z.object({
  media: z.array(
    z.object({
      name: z.string().min(1, "Name is required"),
      description: z.string(),
      media_type: z.string().min(1, "Media type is required"),
      link: z.string().url().optional().or(z.literal("")),
      document_id: z.string().optional(),
    })
  ),
});

type FormValues = z.infer<typeof mediaFormSchema>;

type ReferenceMediaFormProps = {
  questionId: string;
  uploadRefMedia: any;
  resetData: any;
  selectedLang: string;
};

const ReferenceMediaForm = ({
  questionId,
  uploadRefMedia,
  resetData,
  selectedLang,
}: ReferenceMediaFormProps) => {
  const [switchStates, setSwitchStates] = useState<boolean[]>([]);

  const form = useForm<FormValues>({
    resolver: zodResolver(mediaFormSchema),
    defaultValues: {
      media: [
        {
          name: "",
          description: "",
          media_type: "",
          link: "",
          document_id: "",
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "media",
  });

  const onSubmit = (data: FormValues) => {
    const payload = {
      question_uuid: questionId,
      language: selectedLang,
      refrence_media: {
        [selectedLang]: data.media,
      },
    };
    uploadRefMedia({ payload });
  };

  const handleUploadComplete = (docKey: string, index: number) => {
    form.setValue(`media.${index}.document_id`, docKey);
    form.setValue(`media.${index}.link`, "");
  };

  const handleChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const newSwitchStates = [...switchStates];
    newSwitchStates[index] = event.target.checked;
    setSwitchStates(newSwitchStates);

    if (event.target.checked) {
      form.setValue(`media.${index}.document_id`, "");
    } else {
      form.setValue(`media.${index}.link`, "");
    }
  };

  useEffect(() => {
    if (resetData?.length > 0) {
      const formattedResetData = resetData.map((item: any) => ({
        name: item.name || "",
        description: item.description || "",
        media_type: item.media_type || "",
        link: item.link || "",
        document_id: item.document_id || "",
      }));

      // Set switch states based on whether each item has a link
      const newSwitchStates = formattedResetData.map((item) => {
        if (item.link) return true;
        if (item.document_id) return false;
        return true; // default to link if neither exists
      });
      setSwitchStates(newSwitchStates);

      form.reset({ media: formattedResetData });

      // Manually set each media_type value
      formattedResetData.forEach((item, index) => {
        form.setValue(`media.${index}.media_type`, item.media_type);
      });
    } else {
      form.reset({
        media: [
          {
            name: "",
            description: "",
            media_type: "",
            link: "",
            document_id: "",
          },
        ],
      });
      setSwitchStates([true]);
    }
  }, [resetData, form.reset, form.setValue]);

  const handleAddMore = () => {
    append({
      name: "",
      description: "",
      media_type: "",
      link: "",
      document_id: "",
    });
    setSwitchStates([...switchStates, true]);
  };

  return (
    <Box
      sx={{
        flex: 1,
        flexWrap: "wrap",
        alignContent: "center",
        justifyContent: "center",
      }}
    >
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Stack
          sx={{
            display: "flex",
            flexWrap: "wrap",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "flex-start",
            rowGap: 2.5,
            columnGap: 2.5,
          }}
        >
          {fields.map((field, index) => {
            const currentLink = form.watch(`media.${index}.link`);
            const currentDocId = form.watch(`media.${index}.document_id`);

            // Disable fields if resetData exists for that index
            const isDisabled =
              !!resetData?.[index]?.document_id || !!resetData?.[index]?.link;

            return (
              <Card
                key={field.id}
                elevation={3}
                sx={{
                  borderRadius: 6,
                  height: 550,
                  width: "calc(100% / 3.15)",
                }}
              >
                <CardContent>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      mb: 2,
                    }}
                  >
                    <Typography variant="h6">Upload Media</Typography>
                    <Button
                      onClick={() => {
                        remove(index);
                        const newSwitchStates = [...switchStates];
                        newSwitchStates.splice(index, 1);
                        setSwitchStates(newSwitchStates);
                      }}
                      variant="text"
                      sx={{ textTransform: "none", color: "red" }}
                      // disabled={isDisabled} // Disable delete button for existing data
                    >
                      Delete
                    </Button>
                  </Box>

                  <Stack spacing={3}>
                    <TextField
                      label="Name"
                      fullWidth
                      {...form.register(`media.${index}.name`)}
                      error={!!form.formState.errors.media?.[index]?.name}
                      helperText={
                        form.formState.errors.media?.[index]?.name?.message
                      }
                      disabled={isDisabled} // Disable input for existing data
                    />

                    <TextField
                      label="Description"
                      fullWidth
                      multiline
                      rows={3}
                      {...form.register(`media.${index}.description`)}
                      error={
                        !!form.formState.errors.media?.[index]?.description
                      }
                      helperText={
                        form.formState.errors.media?.[index]?.description
                          ?.message
                      }
                      disabled={isDisabled} // Disable input for existing data
                    />

                    <FormControl fullWidth>
                      <InputLabel>Media Type</InputLabel>
                      <Select
                        label="Media Type"
                        value={form.watch(`media.${index}.media_type`)}
                        onChange={(e) =>
                          form.setValue(
                            `media.${index}.media_type`,
                            e.target.value
                          )
                        }
                        error={
                          !!form.formState.errors.media?.[index]?.media_type
                        }
                        disabled={isDisabled} // Disable input for existing data
                      >
                        <MenuItem value="image">Image</MenuItem>
                        <MenuItem value="video">Video</MenuItem>
                        <MenuItem value="pdf">PDF</MenuItem>
                      </Select>
                    </FormControl>

                    <FormControlLabel
                      control={
                        <Switch
                          checked={switchStates[index] ?? true}
                          onChange={(e) => handleChange(e, index)}
                          disabled={isDisabled} // Disable switch for existing data
                        />
                      }
                      label="Use Link"
                    />

                    {switchStates[index] ? (
                      <TextField
                        label="Media Link"
                        fullWidth
                        placeholder="https://"
                        {...form.register(`media.${index}.link`)}
                        error={!!form.formState.errors.media?.[index]?.link}
                        helperText={
                          form.formState.errors.media?.[index]?.link?.message
                        }
                        disabled={isDisabled} // Disable input for existing data
                      />
                    ) : (
                      <Box>
                        <UploadRefMedia
                          onUploadComplete={(docKey) =>
                            handleUploadComplete(docKey, index)
                          }
                          questionId={questionId}
                          existingLink={currentLink}
                          existingDocumentId={currentDocId}
                          disabled={isDisabled} // Disable component for existing data
                        />
                      </Box>
                    )}
                  </Stack>
                </CardContent>
              </Card>
            );
          })}
        </Stack>
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center",
            columnGap: 3,
            padding: 5,
          }}
        >
          <Button variant="outlined" onClick={handleAddMore}>
            Add more
          </Button>
          <Button
            variant="contained"
            type="submit"
            // disabled={!form.formState.isValid}
          >
            Submit
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default ReferenceMediaForm;
