import React, { useEffect, useMemo } from "react";
import { Box, Button, Typography, Stack } from "@mui/material";
import { useKHF } from "@krushal-it/krushal-hook-form";
import {
  webComponentMap,
  KrushalHookFormWeb,
} from "@krushal-it/krushal-hook-form/web";
import { FormProvider } from "react-hook-form";
import {
  useSaveFmQuestionMutation,
  useGetQuestionsListQuery,
} from "../slice/farmConfigurationSlice";
import { useSelector } from "react-redux";
import {
  extractBasedOnLanguageMod,
  globalReferencesMapper,
} from "Utilities/Common/utils";
import TopicSelectComp from "../helper-components/TopicSelectComponent";
import { useNavigate } from "react-router-dom";

const FREQUENCY_OPTIONS = [
  "one-time",
  "Every Month",
  "Every 2nd Month",
  "Every 3rd Month",
  "Every 6th Month",
  "Every 12th Month",
];

const BasicDetailsForm = ({ questionId, isNewQuestion, formData }) => {
  const { data: questionsData, isLoading: isLoadingQuestion } =
    useGetQuestionsListQuery();
  const questionData = questionsData?.find(
    (q) => q.question_uuid === questionId
  );
  const userLanguage = useSelector((state) => state.user.userLanguage);

  const _languages = globalReferencesMapper(10706700);
  const languageValueToKey = _languages.reduce((acc, reference) => {
    acc[reference.reference_id] = reference.reference_information.key;
    return acc;
  }, {});

  console.log("languageValueToKey", _languages, languageValueToKey);

  const navigate = useNavigate();
  // Get unique topics from questions data
  const availableTopics = useMemo(() => {
    if (!questionsData) return [];
    const uniqueTopics = new Set();
    questionsData.forEach((q) => {
      if (q.topic_name) uniqueTopics.add(q.topic_name);
    });
    return Array.from(uniqueTopics).map((topic) => ({
      label: topic,
      value: topic,
      marathi:
        questionsData.find((q) => q.topic_name === topic)?.topic_name_l10n
          ?.mr || "",
      tamil:
        questionsData.find((q) => q.topic_name === topic)?.topic_name_l10n
          ?.ta || "",
    }));
  }, [questionsData]);

  const languages = useMemo(
    () =>
      globalReferencesMapper(10706700)?.map((lang) => ({
        label: extractBasedOnLanguageMod(
          lang.reference_name_l10n,
          userLanguage
        ),
        value: lang.reference_id,
      })) || [],
    [userLanguage]
  );

  const yesNoOptions = [
    { value: 0, label: "Yes" },
    { value: 1, label: "No" },
  ];

  const formConfig = useMemo(() => {
    const questionFields = languages.map((lang) => {
      const langKey = languageValueToKey[lang.value];
      return {
        key: `question_${langKey}`,
        label: `Question Name (${lang.label})`,
        type: "text",
        validation:
          lang.value === 1070670001
            ? {
                required: {
                  value: true,
                  message: "Question name in English is required",
                },
              }
            : undefined,
        gridColumn: "span 1",
      };
    });

    const topicFields = languages.map((lang) => {
      const langKey = languageValueToKey[lang.value];
      return {
        key: `topic_${langKey}`,
        label: `Topic Name (${lang.label})`,
        type: lang.value === 1070670001 ? "advanced_select_1" : "text",
        options: lang.value === 1070670001 ? availableTopics : undefined,
        validation:
          lang.value === 1070670001
            ? {
                required: {
                  value: true,
                  message: "Topic name in English is required",
                },
              }
            : undefined,
        gridColumn: "span 1",
        onChange:
          lang.value === 1070670001
            ? (value, methods) => {
                const selectedTopic = availableTopics.find(
                  (t) => t.value === value
                );
                if (selectedTopic && selectedTopic.marathi) {
                  methods.setValue("topic_mr", selectedTopic.marathi);
                }
              }
            : undefined,
      };
    });

    return {
      form_name: "Farm Management Basic Questions",
      form_fields: [
        ...questionFields,
        ...topicFields,
        {
          key: "frequency",
          label: "Frequency",
          type: "advanced_select",
          options: FREQUENCY_OPTIONS.map((freq) => ({
            value: freq,
            label: freq,
          })),
          validation: {
            required: {
              value: true,
              message: "Frequency is required",
            },
          },
          gridColumn: "span 2",
        },
        {
          key: "farmer_input_required",
          label: "Farmer Input Required",
          type: "advanced_select",
          options: yesNoOptions,
          validation: {
            required: {
              value: true,
              message: "Please select if farmer input is required",
            },
          },
          gridColumn: "span 2",
        },
        {
          key: "weightage",
          label: "Weightage",
          type: "numeric_input",
          gridColumn: "span 2",
        },
      ],
    };
  }, [languages, availableTopics]);

  const defaultValues = useMemo(() => {
    const languageDefaults = languages.reduce((acc, lang) => {
      const langKey = languageValueToKey[lang.value];
      return {
        ...acc,
        [`question_${langKey}`]: "",
        [`topic_${langKey}`]: "",
      };
    }, {});

    return {
      ...languageDefaults,
      frequency: FREQUENCY_OPTIONS[0],
      farmer_input_required: 0,
      weightage: 1,
    };
  }, [languages]);

  const [saveFmQuestion, { isLoading: isSaving, data, isSuccess }] =
    useSaveFmQuestionMutation();

  const methods = useKHF(
    {
      shouldUnregister: true,
      mode: "all",
      defaultValues,
    },
    {
      config: formConfig,
      readOnly: false,
      componentMap: {
        ...webComponentMap,
        advanced_select_1: TopicSelectComp,
      },
    }
  );

  useEffect(() => {
    if (
      isSuccess &&
      data &&
      Object.keys(data).length > 0 &&
      data.question_uuid
    ) {
      navigate(`/configuration/question-details/${data.question_uuid}`);
    }
  }, [isSuccess]);

  useEffect(() => {
    if (isNewQuestion && methods.reset) {
      methods.reset({
        ...defaultValues,
        frequency: formData?.taskQuestions?.frequency || FREQUENCY_OPTIONS[0],
        farmer_input_required: 0,
        weightage: 1,
      });
    }
  }, [isNewQuestion, defaultValues, formData?.taskQuestions?.frequency]);

  useEffect(() => {
    if (questionData && methods.reset && !isNewQuestion) {
      const resetValues = languages.reduce((acc, lang) => {
        const langKey = languageValueToKey[lang.value];
        return {
          ...acc,
          [`question_${langKey}`]:
            questionData.question_name_l10n?.[langKey] ||
            (langKey === "en" ? questionData.question_name : ""),
          [`topic_${langKey}`]:
            questionData.topic_name_l10n?.[langKey] ||
            (langKey === "en" ? questionData.topic_name : ""),
        };
      }, {});

      const frequencyValue =
        formData?.taskQuestions?.frequency ||
        questionData.frequency ||
        FREQUENCY_OPTIONS[0];
      const farmerInputRequired =
        typeof questionData.farmer_input_required === "number"
          ? questionData.farmer_input_required
          : 0;

      methods.reset({
        ...resetValues,
        frequency: frequencyValue,
        farmer_input_required: farmerInputRequired,
        weightage: questionData.weightage || 1,
      });
    }
  }, [
    questionData,
    methods.reset,
    languages,
    isNewQuestion,
    formData?.taskQuestions?.frequency,
  ]);

  const onSubmit = async (formData) => {
    const questionNameL10n = {};
    const topicNameL10n = {};

    languages.forEach((lang) => {
      const langKey = languageValueToKey[lang.value];
      questionNameL10n[langKey] = formData[`question_${langKey}`];
      topicNameL10n[langKey] = formData[`topic_${langKey}`];
    });

    const payload = {
      question_uuid: questionId,
      question_name: formData[`question_en`],
      question_name_l10n: questionNameL10n,
      topic_name: formData[`topic_en`],
      topic_name_l10n: topicNameL10n,
      frequency: formData.frequency,
      farmer_input_required: Number(formData.farmer_input_required),
      weightage: Number(formData.weightage) || 1,
    };

    try {
      await saveFmQuestion({
        question_id: questionId,
        payload,
      }).unwrap();

      methods.reset({
        ...formData,
        frequency: formData.frequency,
        farmer_input_required: Number(formData.farmer_input_required),
      });
    } catch (error) {
      console.error("Error saving question:", error);
    }
  };

  if (isLoadingQuestion || isSaving) {
    return (
      <Box className="flex justify-center items-center h-full">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full" />
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: "1000px", padding: "5px" }}>
      <Typography variant="h5" sx={{ padding: "8px" }}>
        {formConfig.form_name}
      </Typography>
      <FormProvider {...methods}>
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "repeat(2, 1fr)",
            gap: "16px",
          }}
        >
          <KrushalHookFormWeb />
        </Box>
      </FormProvider>
      <Stack>
        <Button
          sx={{ mt: 2, maxWidth: "150px" }}
          className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90"
          onClick={methods.handleSubmit(onSubmit)}
        >
          Submit
        </Button>
      </Stack>
    </Box>
  );
};

export default BasicDetailsForm;
