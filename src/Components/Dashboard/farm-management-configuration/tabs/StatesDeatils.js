import React, { useMemo } from 'react';
import { Box, Button, Typography, Stack, FormControl, FormControlLabel, Checkbox, TextField, Grid } from "@mui/material";
import { FormProvider, useForm, useWatch } from "react-hook-form";
import { useSelector } from "react-redux";
import { useSaveStatesQuestionMutation } from '../slice/farmConfigurationSlice';
import { globalReferencesMapper, extractBasedOnLanguageMod } from 'Utilities/Common/utils';
import { DevTool } from '@hookform/devtools';

const formatFormTypeLabel = (type) => {
  if (!type) return '';
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};


const parseDefaultValue = (defaultValue, formType) => {
  if (!defaultValue) return '';
  
  if (Array.isArray(defaultValue)) {
    if (defaultValue.length === 0) return '';
    
    if (formType === 'advanced_multi_select') {
      return defaultValue.map(val => val?.toString() || '');
    }
        if (formType === 'numeric_input') {
      return defaultValue[0]; 
    }
    
    return defaultValue[0]?.toString() || '';
  }
  
  return defaultValue.toString();
};

 
const StatesDetailsForm = ({ 
  questionId = '', 
  category = [], 
  priorityGroup = [], 
  formType = [],
  questionDetails
}) => {
  const userLanguage = useSelector((state) => state.user.userLanguage);
  const [saveStatesQuestion, { isLoading: isSaving }] = useSaveStatesQuestionMutation();

  const formConfig = useMemo(() => {
    if (!questionDetails?.taskQuestions?.form_configuration?.form_fields) {
      return null;
    }
  
    const fields = questionDetails.taskQuestions.form_configuration.form_fields;
    const currentField = fields.find(field => field.key === 'current');
    const targetField = fields.find(field => field.key === 'target');
    
    const defaultValue = targetField?.selections?.defaultValue || [];
    const fieldType = currentField?.type || '';
  
    return {
      formType: fieldType,
      categoryId: currentField?.category_id || '',
      hasNotes: fields.some(field => field.key === 'notes'),
      hasImage: fields.some(field => field.key === 'image'),
      hasTargetDate: fields.some(field => field.key === 'target_date'),
      hasTarget: Boolean(targetField),
      defaultValue: fieldType === 'numeric_input' ? defaultValue : defaultValue,
      unit: currentField?.unit || '',
      showUnit: Boolean(currentField?.unit !== undefined),
      priorityGrouping: questionDetails.taskQuestions.priority_grouping?.toString() || '',
      referenceCategory: currentField?.category_id || ''
    };
  }, [questionDetails]);

  const shouldLockFormType = Boolean(formConfig?.formType);
  const shouldLockReferenceCategory = Boolean(formConfig?.categoryId);

  const formTypes = useMemo(() => {
    return (formType || []).map(type => ({
      value: type,
      label: formatFormTypeLabel(type)
    }));
  }, [formType]);

  const methods = useForm({
    defaultValues: {
      form_type: formConfig?.formType || '',
      reference_category: formConfig?.referenceCategory || '',
      priority_grouping: formConfig?.priorityGrouping || '',
      target: formConfig?.hasTarget || false,
      image: formConfig?.hasImage || false,
      notes: formConfig?.hasNotes || false,
      target_date: formConfig?.hasTargetDate || false,
      default_value: parseDefaultValue(formConfig?.defaultValue, formConfig?.formType),
      unit: formConfig?.unit || ''
    }
  });
  
  const { control, register, formState: { errors }, handleSubmit, watch, setValue } = methods;

  const formTypeValue = useWatch({ control, name: "form_type" });
  const referenceCategoryValue = useWatch({ control, name: "reference_category" });

  const showReferenceCategory = ["advanced_select", "advanced_multi_select"].includes(formTypeValue);
  
  const showUnitField = useMemo(() => {
    const currentField = questionDetails?.taskQuestions?.form_configuration?.form_fields?.find(field => field.key === 'current');
    return currentField?.unit !== undefined || formTypeValue === "unit";
  }, [formTypeValue, questionDetails]);

  const showDefaultValueField = useMemo(() => {
    const supportedFormTypes = [
      "unit",
      "advanced_select",
      "numeric_input",
      "date_time",
      "text_area",
      "text_input",
    ];
        return watch("target") && formTypeValue && supportedFormTypes.includes(formTypeValue);
  }, [formTypeValue, watch("target")]);

  const defaultValueOptions = useMemo(() => {
    if (["advanced_select", "advanced_multi_select"].includes(formTypeValue) && referenceCategoryValue) {
      return globalReferencesMapper(referenceCategoryValue)?.map((item) => ({
        label: extractBasedOnLanguageMod(item.reference_name_l10n, userLanguage),
        value: parseInt(item?.reference_id),
      })) || [];
    }
    return [];
  }, [referenceCategoryValue, userLanguage, formTypeValue]);

  React.useEffect(() => {
    if (formConfig) {
      Object.entries(formConfig).forEach(([key, value]) => {
        if (key === 'defaultValue') {
          setValue('default_value', parseDefaultValue(value, formConfig.formType));
        } else {
          setValue(key.replace(/([A-Z])/g, '_$1').toLowerCase(), value);
        }
      });
    }
  }, [formConfig, setValue]);

  const getUnitFieldLabel = () => {
    return 'Unit';
  };

  const renderDefaultValueField = () => {
    if (!showDefaultValueField) return null;

    const commonProps = {
      fullWidth: true,
      error: !!errors.default_value,
      helperText: errors.default_value?.message,
      value: watch('default_value') || ''
    };

    switch (formTypeValue) {
      case 'numeric_input':
        return (
          <TextField
            type="number"
            label="Default Value"
            {...register("default_value")}
            {...commonProps}
          />
        );
        case 'unit':
        return (
          <>
            <TextField
              label="Default Value"
              type="text"
              {...register("default_value")}
              {...commonProps}
            />
          </>
        );
      case 'date_time':
        return (
          <TextField
            type="datetime-local"
            label="Default Value"
            InputLabelProps={{ shrink: true }}
            {...register("default_value")}
            {...commonProps}
          />
        );

      case 'text_area':
        return (
          <TextField
            label="Default Value"
            multiline
            rows={3}
            {...register("default_value")}
            {...commonProps}
          />
        );

      case 'text_input':
        return (
          <TextField
            label="Default Value"
            {...register("default_value")}
            {...commonProps}
          />
        );

      case 'advanced_select':
        return (
          <TextField
            select
            label="Default Value"
            {...register("default_value", {
              setValueAs: value => parseInt(value)
            })}
            SelectProps={{ native: true }}
            {...commonProps}
          >
            <option value=""></option>
            {defaultValueOptions.map((option) => (
              <option 
                key={option.value} 
                value={option.value}
              >
                {option.label}
              </option>
            ))}
          </TextField>
        );

      case 'advanced_multi_select':
        return (
          <TextField
            select
            label="Default Value"
            {...register("default_value", {
              setValueAs: values => Array.isArray(values) ? values.map(v => parseInt(v)) : []
            })}
            SelectProps={{
              native: true,
              multiple: true,
              value: Array.isArray(watch('default_value')) ? watch('default_value') : []
            }}
            
            {...commonProps}
          >
            {defaultValueOptions.map((option) => (
              <option 
                key={option.value} 
                value={option.value}
              >
                {option.label}
              </option>
            ))}
          </TextField>
        );

      default:
        return null;
    }
  };

  const formatDefaultValue = (value, formType) => {
    if (!value) return [];
    
    switch (formType) {
      case 'numeric_input':
      return [Number(value)];      
      case 'unit':
      case 'text_area':
      case 'text_input':
      case 'date_time':
        return [value.toString()];
      case 'advanced_select':
        return [parseInt(value)];
      case 'advanced_multi_select':
        return Array.isArray(value) ? value.map(v => parseInt(v)) : [parseInt(value)];
      default:
        return [];
    }
  };

  const onSubmit = async (formData) => {
    try {
      const additionalFields = ['image', 'notes', 'target_date']
        .filter(key => formData[key]);
  
        const defaultValue = formatDefaultValue(formData.default_value, formData.form_type);
  
      const payload = {
        question_uuid: questionId,
        form_type: formData.form_type,
        reference_id: formData.reference_category || '',
        target: formData.target,
        priority_grouping: formData.priority_grouping,
        additionalFields,
        unit: formData.unit || '',
        default_value: defaultValue  
      };
  
      await saveStatesQuestion({ question_id: questionId, payload });
    } catch (error) {
      console.error("Error saving state details:", error);
    }
  };

  if (!questionId) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography color="error">Error: Question ID is required</Typography>
      </Box>
    );
  }

  if (isSaving) {
    return (
      <Box sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
      }}>
        Loading...
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: '700px', marginBottom: '6px' }}>
      <FormProvider {...methods}>
        <Box component="form">
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <TextField
                  select
                  label="Form Type"
                  {...register("form_type", {
                    required: "Form type is required"
                  })}
                  SelectProps={{ native: true }}
                  error={!!errors.form_type}
                  helperText={errors.form_type?.message}
                  disabled={shouldLockFormType}
                >
                  <option value=""></option>
                  {formTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </TextField>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth>
                <TextField
                  select
                  label="Priority Grouping"
                  {...register("priority_grouping", {
                    required: "Priority grouping is required"
                  })}
                  SelectProps={{ native: true }}
                  error={!!errors.priority_grouping}
                  helperText={errors.priority_grouping?.message}
                >
                  <option value=""></option>
                  {(priorityGroup || []).map(group => (
                    <option key={group.priority_grouping} value={group.priority_grouping}>
                      {extractBasedOnLanguageMod(group.name_l10n, userLanguage) || group.name}
                    </option>
                  ))}
                </TextField>
              </FormControl>
            </Grid>

            {showReferenceCategory && (
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <TextField
                    select
                    label="Reference Category"
                    {...register("reference_category", {
                      required: "Reference category is required"
                    })}
                    SelectProps={{ native: true }}
                    error={!!errors.reference_category}
                    helperText={errors.reference_category?.message}
                    disabled={shouldLockReferenceCategory}
                  >
                    <option value=""></option>
                    {(category || []).map(cat => (
                      <option key={cat.reference_category_id} value={cat.reference_category_id}>
                        {extractBasedOnLanguageMod(cat.reference_name_l10n, userLanguage) || cat.reference_name}
                      </option>
                    ))}
                  </TextField>
                </FormControl>
              </Grid>
            )}

            {showUnitField && (
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <TextField
                    label={getUnitFieldLabel()}
                    {...register("unit")}
                    disabled={formConfig?.showUnit}
                    value={watch("unit") || ''}
                  />
                </FormControl>
              </Grid>
            )}

            {showDefaultValueField && (
              <Grid item xs={12}>
                {renderDefaultValueField()}
              </Grid>
            )}

            <Grid item xs={12} ml={1.5}>
              <FormControlLabel
                control={
                  <Checkbox
                    {...register("target")}
                    checked={watch("target")}
                    onChange={(e) => setValue("target", e.target.checked)}
                  />
                }
                label="Target Required"
              />
            </Grid>

            <Grid item xs={12}>
              <Stack spacing={2}>
                {['image', 'notes', 'target_date'].map((key) => (
                  <FormControlLabel
                    key={key}
                    control={
                      <Checkbox
                        {...register(key)}
                        checked={watch(key)}
                        onChange={(e) => setValue(key, e.target.checked)}
                      />
                    }
                    label={key.charAt(0).toUpperCase() + key.slice(1).replace('_', ' ')}
                  />
                ))}
              </Stack>
            </Grid>

            <Grid item xs={12}>
              <Button
                variant="contained"
                onClick={handleSubmit(onSubmit)}
                disabled={isSaving}
              >
                Save
              </Button>
            </Grid>
          </Grid>
        </Box>
      </FormProvider>
      <DevTool control={methods.control} />
    </Box>
  );
};

export default StatesDetailsForm;




