
import React, { useState, useMemo } from 'react';
import { useGetQuestionsListQuery } from '../slice/farmConfigurationSlice';
import {
  Box,
  Typography,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  TextField,
  TablePagination,
  TableSortLabel,
  Dialog,
  DialogContent,
  Button,
  Chip,
} from "@mui/material";
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import SearchIcon from '@mui/icons-material/Search';
import AddIcon from '@mui/icons-material/Add';
import FirstPageIcon from '@mui/icons-material/FirstPage';
import KeyboardArrowLeft from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRight from '@mui/icons-material/KeyboardArrowRight';
import LastPageIcon from '@mui/icons-material/LastPage';
import { useSelector } from 'react-redux';

const TablePaginationActions = (props) => {
  const { count, page, rowsPerPage, onPageChange } = props;
  const lastPage = Math.max(0, Math.ceil(count / rowsPerPage) - 1);

  return (
    <Box sx={{ flexShrink: 0, ml: 2.5 }}>
      <IconButton onClick={(e) => onPageChange(e, 0)} disabled={page === 0} aria-label="first page">
        <FirstPageIcon />
      </IconButton>
      <IconButton onClick={(e) => onPageChange(e, page - 1)} disabled={page === 0} aria-label="previous page">
        <KeyboardArrowLeft />
      </IconButton>
      <IconButton onClick={(e) => onPageChange(e, page + 1)} disabled={page >= lastPage} aria-label="next page">
        <KeyboardArrowRight />
      </IconButton>
      <IconButton onClick={(e) => onPageChange(e, lastPage)} disabled={page >= lastPage} aria-label="last page">
        <LastPageIcon />
      </IconButton>
    </Box>
  );
};

const SearchDialog = ({ open, onClose, column, searchTerm, onSearch, onReset }) => {
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);

  const handleSearch = () => {
    onSearch(localSearchTerm);
    onClose();
  };

  const handleReset = () => {
    setLocalSearchTerm('');
    onReset();
    onClose();
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  if (!column) return null;

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogContent>
        <div style={{ padding: 8, width: 220 }}>
          <TextField
            autoFocus
            placeholder={`Search ${column.label}`}
            value={localSearchTerm}
            onChange={(e) => setLocalSearchTerm(e.target.value)}
            onKeyDown={handleKeyDown}
            style={{ width: '100%', marginBottom: 8, display: 'block' }}
            InputProps={{
              startAdornment: (
                <SearchIcon style={{ fontSize: 16, marginRight: 8 }} />
              ),
            }}
            size="small"
          />
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button
              onClick={handleSearch}
              variant="contained"
              color="primary"
              size="small"
              style={{ width: 90 }}
            >
              <SearchIcon style={{ fontSize: 16, marginRight: 4 }} /> Search
            </Button>
            <Button
              onClick={handleReset}
              variant="outlined"
              size="small"
              style={{ width: 90 }}
            >
              Reset
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const FarmManagementConfigurationQuestions = () => {
  const { data: questionsList, isLoading, error } = useGetQuestionsListQuery();
  const userLanguage = useSelector((state) => state.user.userLanguage);

  const navigate = useNavigate();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerms, setSearchTerms] = useState({
    question_name: '',
    topic_name: '',
    question_type: '',
    status: '',
  });
  const [orderBy, setOrderBy] = useState('question_name');
  const [order, setOrder] = useState('asc');
  const [searchDialogOpen, setSearchDialogOpen] = useState(false);
  const [currentSearchColumn, setCurrentSearchColumn] = useState(null);

  const getStatus = (active) => {
    return active === 1000100001 ? 'Active' : 'Inactive';
  };

  const columns = [
    { id: 'question_name', label: 'Question Name' },
    { id: 'topic_name', label: 'Topic Name' },
    { id: 'question_type', label: 'Question Type' },
    { id: 'created_at', label: 'Created At' },
    { id: 'updated_at', label: 'Updated At' },
    { id: 'status', label: 'Status' },
  ];

  const handleQuestionClick = (questionId) => {
    navigate(`/configuration/question-details/${questionId}`);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchIconClick = (column) => {
    if (column.id !== 'created_at' && column.id !== 'updated_at') {
      setCurrentSearchColumn(column);
      setSearchDialogOpen(true);
    }
  };

  const handleSearchDialogClose = () => {
    setSearchDialogOpen(false);
    setCurrentSearchColumn(null);
  };

  const handleSearch = (searchTerm) => {
    if (currentSearchColumn) {
      setSearchTerms(prev => ({ ...prev, [currentSearchColumn.id]: searchTerm }));
      setPage(0);
    }
  };

  const handleResetSearch = () => {
    if (currentSearchColumn) {
      setSearchTerms(prev => ({ ...prev, [currentSearchColumn.id]: '' }));
      setPage(0);
    }
  };

  const handleSort = (property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const getQuestionType = (formConfig) => {
    if (formConfig && formConfig.form_fields && formConfig.form_fields.length > 0) {
      return formConfig.form_fields[0].type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    }
    return 'N/A';
  };

  const filteredAndSortedQuestions = useMemo(() => {
    if (!questionsList) return [];

    return questionsList
      .filter((question) => {
        return Object.entries(searchTerms).every(([key, value]) => {
          if (!value) return true;
          if (key === 'question_type') {
            return getQuestionType(question.form_configuration).toLowerCase().includes(value.toLowerCase());
          }
          if (key === 'status') {
            const status = getStatus(question.active);
            return status.toLowerCase().includes(value.toLowerCase());
          }
          return String(question[key]).toLowerCase().includes(value.toLowerCase());
        });
      })
      .sort((a, b) => {
        const isAsc = order === 'asc';
        let valueA, valueB;

        if (orderBy === 'question_type') {
          valueA = getQuestionType(a.form_configuration);
          valueB = getQuestionType(b.form_configuration);
        } else if (orderBy === 'status') {
          valueA = getStatus(a.active);
          valueB = getStatus(b.active);
        } else {
          valueA = a[orderBy];
          valueB = b[orderBy];
        }

        if (valueA < valueB) return isAsc ? -1 : 1;
        if (valueA > valueB) return isAsc ? 1 : -1;
        return 0;
      });
  }, [questionsList, searchTerms, order, orderBy]);

  const paginatedQuestions = filteredAndSortedQuestions.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleAddNewQuestion = () => {
    navigate('/configuration/question-details/question?new=1');
  };

  if (isLoading) return <CircularProgress />;
  if (error) return <Typography color="error">Error loading questions: {error.message}</Typography>;

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
        <Button
          variant="contained"
          sx={{ backgroundColor: '#ec6237', '&:hover': { backgroundColor: '#d55a2f' } }}
          startIcon={<AddIcon />}
          onClick={handleAddNewQuestion}
        >
          Add Question
        </Button>
      </Box>
      <TableContainer 
        component={Paper} 
        sx={{ 
          borderRadius: 0,
          backgroundColor: '#fafafa',
          overflowX: 'auto',
        }}
      >
        <Table sx={{ minWidth: 800 }} aria-label="questions table">
          <TableHead>
            <TableRow>
              {columns.map((column, index) => (
                <TableCell
                  key={column.id}
                  sx={{
                    fontWeight: 'bold',
                    borderRight: index < columns.length - 1 ? '1px solid rgba(224, 224, 224, 1)' : 'none',
                    backgroundColor: '#f5f5f5'
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <TableSortLabel
                      active={orderBy === column.id}
                      direction={orderBy === column.id ? order : 'asc'}
                      onClick={() => handleSort(column.id)}
                      sx={{
                        '& .MuiTableSortLabel-icon': {
                          opacity: '1 !important'
                        }
                      }}
                    >
                      {column.label}
                    </TableSortLabel>
                    {column.id !== 'created_at' && column.id !== 'updated_at' && (
                      <IconButton size="small" onClick={() => handleSearchIconClick(column)}>
                        <SearchIcon fontSize="small" />
                      </IconButton>
                    )}
                  </Box>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedQuestions.map((question) => (
              <TableRow
                key={question.question_uuid}
                sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
              >
                <TableCell
                  component="th"
                  scope="row"
                  onClick={() => handleQuestionClick(question.question_uuid)}
                  sx={{
                    cursor: 'pointer',
                    color: 'primary.main',
                    '&:hover': { textDecoration: 'underline' },
                  }}
                >
                  {question.question_name}
                </TableCell>
                <TableCell>{question.topic_name}</TableCell>
                <TableCell>{getQuestionType(question.form_configuration)}</TableCell>
                <TableCell>{dayjs(question.created_at).format('LLL')}</TableCell>
                <TableCell>{dayjs(question.updated_at).format('LLL')}</TableCell>
                <TableCell>
                  <Chip
                    label={getStatus(question.active)}
                    sx={{
                      backgroundColor: question.active === 1000100001 ? '#85c98b' : '#f44336',
                      color: 'white',
                      fontWeight: 'bold',
                      border: 'none'
                    }}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[]}
        component="div"
        count={filteredAndSortedQuestions.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        ActionsComponent={TablePaginationActions}
        labelDisplayedRows={() => null}
      />
      {currentSearchColumn && (
        <SearchDialog
          open={searchDialogOpen}
          onClose={handleSearchDialogClose}
          column={currentSearchColumn}
          searchTerm={searchTerms[currentSearchColumn.id]}
          onSearch={handleSearch}
          onReset={handleResetSearch}
        />
      )}
    </Box>
  );
};

export default FarmManagementConfigurationQuestions;