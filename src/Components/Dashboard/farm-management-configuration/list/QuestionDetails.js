import React, { useState, useEffect } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { useGetFmQuestionsQuery } from "../slice/farmConfigurationSlice";
import {
  Box,
  Typography,
  CircularProgress,
  Tabs,
  Tab,
  Button,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import BasicDetailsForm from "../tabs/BasicDetails";
import StatesDetailsForm from "../tabs/StatesDeatils";
import AdviceForm from "../tabs/AdviceDetails";
import ReferenceMedia from "../tabs/reference-media/ReferenceMedia";

const QuestionDetails = () => {
  const { id: questionId } = useParams();
  const [tabValue, setTabValue] = useState(0);
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const isNewQuestion = searchParams.get("new") === "1";

  const {
    data: questionDetails,
    isLoading,
    error,
  } = useGetFmQuestionsQuery(
    { question_id: questionId },
    { skip: isNewQuestion }
  );

  const [localQuestionDetails, setLocalQuestionDetails] = useState(null);

  useEffect(() => {
    if (isNewQuestion) {
      setLocalQuestionDetails({
        taskQuestions: {
          form_configuration: {},
          question_name: "",
          topic_name: "",
        },
        language: [],
        category: [],
        priorityGroup: [],
        formType: [],
      });
    } else if (questionDetails) {
      setLocalQuestionDetails(questionDetails);
    }
  }, [isNewQuestion, questionDetails]);

  const handleTabChange = (event, newValue) => {
    if (isNewQuestion && newValue !== 0) {
      return;
    }
    setTabValue(newValue);
  };

  const handleGoBack = () => {
    navigate("/configuration");
  };

  if (!isNewQuestion && isLoading) return <CircularProgress />;
  if (!isNewQuestion && error)
    return (
      <Typography color="error">
        Error loading question details: {error.message}
      </Typography>
    );
  if (!localQuestionDetails) return null;

  const formConfiguration =
    localQuestionDetails.taskQuestions?.form_configuration;
  const languages = localQuestionDetails.language || [];

  const questionTabs = isNewQuestion
    ? [{ label: "Basic", count: 1 }]
    : [
        { label: "Basic", count: 1 },
        { label: "States", count: 2 },
        { label: "Advice", count: 3 },
        { label: "Reference Media", count: 4 },
      ];

  const renderTabContent = () => {
    switch (tabValue) {
      case 0:
        return (
          <BasicDetailsForm
            questionId={isNewQuestion ? null : questionId}
            questionDetails={localQuestionDetails.taskQuestions}
            isNewQuestion={isNewQuestion}
            formData={questionDetails}
          />
        );
      case 1:
        if (!isNewQuestion) {
          return (
            <StatesDetailsForm
              questionId={questionId}
              category={localQuestionDetails.category}
              priorityGroup={localQuestionDetails.priorityGroup}
              formType={localQuestionDetails.formType}
              isNewQuestion={isNewQuestion}
              questionDetails={questionDetails}
            />
          );
        }
        return null;
      case 2:
        if (!isNewQuestion) {
          return (
            <AdviceForm
              questionId={questionId}
              formConfiguration={formConfiguration}
              languages={languages}
              isNewQuestion={isNewQuestion}
            />
          );
        }
        return null;
      case 3:
        if (!isNewQuestion) {
          return (
            <ReferenceMedia
              questionId={questionId}
              formConfiguration={formConfiguration}
            />
          );
        }
        return null;
      default:
        return null;
    }
  };

  return (
    <Box>
      <Box display="flex" alignItems="center" mb={2}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={handleGoBack}
          sx={{ mr: 2 }}
        >
          Back
        </Button>
      </Box>

      <Tabs value={tabValue} onChange={handleTabChange}>
        {questionTabs.map((tab) => (
          <Tab
            key={tab.count}
            label={tab.label}
            disabled={isNewQuestion && tab.count !== 1}
          />
        ))}
      </Tabs>

      <Box mt={2}>{renderTabContent()}</Box>
    </Box>
  );
};

export default QuestionDetails;
