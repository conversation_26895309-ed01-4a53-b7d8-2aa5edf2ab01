import { Box, Stack, Typography } from "@mui/material";
import PageLayout from "../../../Components/Common/PageLayout";
import FarmManagementConfigurationQuestions from './list/index';


const FarmManagementConfiguration = () => {
  return (
    <PageLayout>
      <Stack direction="row" spacing={2} justifyContent={"space-between"}>
        <Box>
          <Typography
            component="h5"
            variant="h5"
            sx={{ fontWeight: 700, fontSize: 24 }}
          >
            Farm Management Configuration
          </Typography>
          <Box sx={{ backgroundColor: "#ff6e1b", height: 5, width: 320 }}></Box>
        </Box>
      </Stack>
      <FarmManagementConfigurationQuestions/>
    </PageLayout>
  );
};

export default FarmManagementConfiguration;
