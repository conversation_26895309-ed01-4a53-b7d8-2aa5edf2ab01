.dashboard__img-div {
  height: 40px;
  width: 40px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  /* border: 1px solid silver; */
}

.dashboard__navbar {
  /* border: 1px solid silver; */
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: #fff;
  position: fixed;
  z-index: 10;
  width: 100%;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}

.dashboard__vertical-nav {
  z-index: 9;
  min-width: 14.5rem;
  width: 14.5rem;
  /* box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px; */
  box-shadow: rgba(0, 0, 0, 0.15) 1.95px 1.95px 2.6px;
  height: calc(100vh - 4.5rem);
  /* border: 1px solid silver; */
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: #fff;
  padding: 2rem 1rem;
  overflow-y: scroll;
}

.dashboard__vertical-nav.collapsed {
  min-width: 4.5rem;
  width: 4.5rem;
  overflow: hidden;
  text-align: center;
}

.dashboard__vertical-nav-subitems {
  z-index: 9;
  min-width: 10rem;
  width: 10rem;
  /* box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px; */
  box-shadow: rgba(0, 0, 0, 0.15) 1.95px 1.95px 2.6px;
  height: calc(100vh - 4.5rem);
  /* border: 1px solid silver; */
  position: fixed;
  bottom: 0;
  left: 4.6rem;
  background-color: #fff;
  padding: 2rem 1rem;
}

.dashboard__menu-item {
  font-size: 16px;
  padding: 10px;
  cursor: pointer;
  color: #212121;
  font-weight: 400;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.dashboard__menu-item.selected {
  color: #fff;
  background-color: #673ab7;
  font-weight: 400;
  border-radius: 4px;
  pointer-events: none;
}

.dashboard__menu-item:hover {
  background-color: #f5fbfd;
}

.dashboard__main-content {
  margin-left: 14.5rem;
  /* border: 1px solid silver; */
  padding: 5rem 1rem 0 1rem;
  /* height: 200vh; */
}

.dashboard__toggle-btn {
  display: none;
}

.dashboard__profile-img-inactive {
  border: 1px solid silver;
  border-radius: 50%;
  padding: 2px;
  margin-left: auto;
}

.dashboard__profile-img-active {
  border: 1px solid #673ab7;
  border-radius: 50%;
  padding: 2px;
  margin-left: auto;
}

@media (max-width: 768px) {
  .dashboard__toggle-btn {
    display: inline;
  }

  .dashboard__main-content {
    margin-left: 0;
  }

  .dashboard__vertical-nav {
    margin-left: 0;
  }
}
