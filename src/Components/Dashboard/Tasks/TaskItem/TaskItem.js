import { useEffect, useCallback, useState, createContext } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import axios from "axios";
import moment from "moment";
// import PDFViewer from "pdf-viewer-reactjs";
import ImageViewer from "react-simple-image-viewer";
import "./TaskItem.css";
import {
  BASE_URL,
  instance,
  tokenFailureHandler,
} from "../../../../Services/api.service";

import Loader from "../../../Loader/Loader";
import ToastersService from "../../../../Services/toasters.service";
import PaymentTab from "./PaymentsTab/PaymentTab";
import Row from "react-bootstrap/Row";
import Col from "react-bootstrap/Col";
import Button from "react-bootstrap/Button";
import Modal from "react-bootstrap/Modal";

import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import resizeFile from "../../../../Services/file-resizer.service";
import { Table } from "react-bootstrap";
import FileNotFound from "../../../../Utilities/FileNotFound";
import { Box, TextareaAutosize } from "@mui/material";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import {
  convertUtcToIst,
  convertUtcToIstFormatted,
  globalReferencesMapper,
} from "../../../../Utilities/Common/utils";

import logo from "./krushal.png";
import TaskDetails from "./TaskDetails/TaskDetails";
import { useSelector } from "react-redux";

import { Image, Modal as AntdModal } from "antd";
import { PreviewModalTable } from "../../ServiceRequests/PreviewModalTable";
import ShareOTP from "./share-otp/ShareOTP";
import ServiceDoc from "./serviceDoc/ServiceDoc";
import { AIForm } from "./TaskDetails/AIForm/AiForm";
import PDForm from "./TaskDetails/PDForm/PDForm";
import CMTForm from "./TaskDetails/CMTForm/CMTForm";
import FarmTickForm from "./TaskDetails/FarmTick/FarmTickForm";
import GenerateServiceDoc from "./serviceDoc/GenerateServiceDoc";
const { extractBasedOnLanguage } = require("@krushal-it/common-core");

const {
  CS_TEAM,
  MARKETING_TEAM,
  SALES_TEAM,
  FINANCE_TEAM,
  VET_OPS,
  SALES_OPS,
  withAuthorization,
} = require("../../../user/authorize");

const getPreviewData = async (calendarId, navigate, setPreviewData) => {
  try {
    const response = await instance({
      url: "activity/preview",
      method: "POST",
      data: { care_calendar_id: calendarId },
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("getPreviewData", response);
    if (response?.status === 201) {
      setPreviewData(response?.data?.data);
    }
    console.log("getPreviewData", response);
  } catch (error) {
    console.log(error);
    if (error?.response?.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};
export const activityDetailsContext = createContext();
export const careCalenderDetails = createContext();
const TaskItem = () => {
  const [taskItemTabs, setTaskItemTabs] = useState([
    {
      count: 1,
      isSelected: true,
      name: "Activity",
    },
    {
      count: 2,
      isSelected: false,
      name: "Prescription",
    },
  ]);

  const navigate = useNavigate();
  const params = useParams();

  const taskNavigation = localStorage.getItem("taskNavigation");
  const location = useLocation();

  const [activityLoader, setActivityLoader] = useState(false);
  const [activityDetails, setActivityDetails] = useState({});

  const [savedActivityForms, setSavedActivityForms] = useState(null);

  const [noteItem, setNoteItem] = useState("");
  const [taskNotes, setTaskNotes] = useState();

  const [paravetsList, setParavetsList] = useState([]);
  const [selectedParavetId, setSelectedParavetId] = useState();

  const [activityConfig, setActivityConfig] = useState();
  const [AIconfig, setAIconfig] = useState();
  // const [activityOptions, setActivityOptions] = useState([]);
  const [statusOptions, setStatusOptions] = useState([]);
  const [medicines, setMedicines] = useState([]);
  let [selectedMedicines, setSelectedMedicines] = useState([]);
  const [isRenderTable, setRenderTable] = useState(false);

  const [eligibleCattleList, setEligibleCattleList] = useState();

  let [updateTaskForm, setUpdateTaskForm] = useState({
    animal_ids: [],
  });

  const userLanguage = useSelector((state) => state.user.userLanguage);

  const activities = [
    1000470001, 1000470003, 1000180015, 1000180011, 1000180005, 1000180014,
    1000180002, 1000180003, 1000180004, 1000180008, 1000180010, 1000180001,
    1000180012,
  ];

  // console.log(activityDetails,'checkdata')
  // useEffect(() => {
  //   if (formCompleted && activities.includes(activityDetails.activity_id)) {
  //     setTaskItemTabs((prevTabs) => {
  //       const newTabName = extractBasedOnLanguage(
  //         activityDetails?.activity_name_l10n,
  //         userLanguage
  //       );
  //       const tabExists = prevTabs.some((tab) => tab.name === newTabName);

  //       if (!tabExists) {
  //         return [
  //           ...prevTabs,
  //           { count: prevTabs.length + 1, isSelected: false, name: newTabName },
  //         ];
  //       }

  //       return prevTabs;
  //     });
  //   }
  // }, [activityDetails.activity_id]);

  const shouldShowPaymentsTab = () => {
    return activityDetails.calendar_activity_status !== 1000300001;
  };

  useEffect(() => {
    if (formCompleted && activities.includes(activityDetails.activity_id)) {
      setTaskItemTabs((prevTabs) => {
        const newTabName = extractBasedOnLanguage(
          activityDetails?.activity_name_l10n,
          userLanguage
        );

        const tabExists = prevTabs.some((tab) => tab.name === newTabName);

        if (!tabExists) {
          return [
            ...prevTabs,
            { count: prevTabs.length + 1, isSelected: false, name: newTabName },
          ];
        }

        return prevTabs;
      });
    }
    setTaskItemTabs((prevTabs) => {
      const paymentsTabExists = prevTabs.some((tab) => tab.name === "Payments");
      if (shouldShowPaymentsTab() && !paymentsTabExists) {
        return [
          ...prevTabs,
          {
            count: prevTabs.length + 1,
            isSelected: false,
            name: "Payments",
          },
        ];
      }

      if (!shouldShowPaymentsTab()) {
        return prevTabs.filter((tab) => tab.name !== "Payments");
      }

      return prevTabs;
    });
  }, [activityDetails.activity_id, activityDetails.calendar_activity_status]);

  console.log(activityDetails, "activity");
  const [activityFiles, setActivityFiles] = useState([]);
  const [currentImage, setCurrentImage] = useState(0);
  const [isViewerOpen, setIsViewerOpen] = useState(false);

  const openImageViewer = useCallback((index) => {
    setCurrentImage(index);
    setIsViewerOpen(true);
  }, []);

  const closeImageViewer = () => {
    setCurrentImage(0);
    setIsViewerOpen(false);
  };

  const [documentEntries, setDocumentEntries] = useState([]);
  let [newActivityFiles, setNewActivityFiles] = useState([]);
  let [file, setFile] = useState();
  let [fileDes, setFileDes] = useState("");
  const [showFileModal, setShowFileModal] = useState(false);
  const handleFileModalClose = () => setShowFileModal(false);
  const handleFileModalShow = () => setShowFileModal(true);

  const [showConfimationPopupModal, setConfimationPopupModal] = useState(false);
  const handleConfimationPopupModalClose = () =>
    setConfimationPopupModal(false);
  const handleConfimationPopupModalShow = () => setConfimationPopupModal(true);

  let [followUps, setFollowUps] = useState([]);
  const [freezeActivity, setFreezeActivity] = useState(false);

  //taskOperations for ask input values according to activity_status
  const [taskOperations, setTaskOperations] = useState({
    askForFollowUp: false,
    askForCompletion: false,
    askForReason: false,
    askForFollowUpRequired: true,
    showCheckboxes: false,
  });
  const [activityName, setActivityName] = useState(null);
  const [completionDate, setCompletionDate] = useState();
  const [reason, setReason] = useState("");
  const [nextActivityDate, setNextActivityDate] = useState();

  const [updateAITaskForm, setUpdateAITaskForm] = useState();

  const navigateBackHandler = () => {
    if (taskNavigation) {
      navigate("/tasks");
    } else if (location?.state?.staff_id) {
      navigate(`/staffs/${location?.state?.staff_id}`, { state: true });
    } else {
      if (activityDetails.customer_id) {
        navigate(`/farmer/${activityDetails.customer_id}`);
      } else {
        navigate("/farmer");
      }
    }
  };

  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const showModal = () => {
    getPreviewData(activityDetails.care_calendar_id, navigate, setPreviewData);
    setIsModalOpen(true);
  };
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const [previewData, setPreviewData] = useState();
  const [medicineConfig, setMedicineConfig] = useState([]);
  const handlePreviewModalClose = () => {
    setShowPreviewModal(false);
  };

  const getActivityConfig = useCallback(async () => {
    try {
      // const response = await axios.get(BASE_URL + "/references/care-calendar", {
      //   headers: {
      //     token: localStorage.getItem("accessToken"),
      //     "X-App-Id": "*********",
      //   },
      // });
      const response = await instance({
        url: "/references/care-calendar",
        method: "GET",
        // data: reqBody,
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      if (response?.status === 200) {
        if (response?.data?.result === false) {
          tokenFailureHandler(response?.data);
          navigate("/login");
        }
        console.log("Activity Config", response.data);
        setMedicineConfig(response.data.data);
        setActivityConfig({
          Preventive: response?.data?.data?.activity_config?.Preventive,
          Curative: response?.data?.data?.activity_config?.Curative,
          Reproductive: response?.data?.data?.activity_config?.Reproductive,
        });
        setAIconfig({
          ai_form_company_of_semen:
            response?.data?.data?.care_alendar_config?.ai_form_company_of_semen,
          ai_form_suitable_for_ai_based_on_discharge:
            response?.data?.data?.care_alendar_config
              ?.ai_form_suitable_for_ai_based_on_discharge,
          ai_form_suitable_for_ai_based_on_heat:
            response?.data?.data?.care_alendar_config
              ?.ai_form_suitable_for_ai_based_on_heat,
        });
        let statusArr = [];
        for (let item of response?.data?.data?.status_config) {
          if (
            [1000300050, 1000300051, 1000300052].includes(item.reference_id)
          ) {
            statusArr.push(item);
          }
        }
        setStatusOptions(statusArr);
        setMedicines(response?.data?.data?.medicine_config);
        // setFormConfigLoader(false);
        getActivityDetails(params?.taskId);
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  }, []);

  const setActivityConfigurationsFromGlobalRef = () => {
    let statusArray = [];
    for (let item of globalReferencesMapper(10003000)) {
      if ([1000300050, 1000300051, 1000300052].includes(item.reference_id)) {
        statusArray.push(item);
      }
    }
    setStatusOptions(statusArray);
    if (
      global?.medicineReferences &&
      Object.keys(global?.medicineReferences).length > 0
    ) {
      const medicineArray = Object.keys(global?.medicineReferences).map(
        (key) => global?.medicineReferences[key]
      );
      setMedicines(medicineArray);
    }
    setAIconfig({
      ai_form_company_of_semen:
        global?.referencesWithoutCategoryId?.referenceMap?.care_alendar_config
          ?.ai_form_company_of_semen,
      ai_form_suitable_for_ai_based_on_discharge:
        global?.referencesWithoutCategoryId?.referenceMap?.care_alendar_config
          ?.ai_form_suitable_for_ai_based_on_discharge,
      ai_form_suitable_for_ai_based_on_heat:
        global?.referencesWithoutCategoryId?.referenceMap?.care_alendar_config
          ?.ai_form_suitable_for_ai_based_on_heat,
    });
    getActivityDetails(params.taskId);
  };
  const getActivityDetails = useCallback(async (taskId) => {
    setActivityLoader(true);
    try {
      // const response = await axios.get(
      //   BASE_URL + "/activity/by-id?calendar_id=" + taskId,
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "Content-Type": "application/json",
      //       "X-App-Id": "*********",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/activity/by-id?calendar_id=" + taskId,
        method: "GET",
        // data: reqBody,
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log(response);
      if (response?.status === 200) {
        console.log("Activity details", response?.data);
        setActivityDetails(response?.data);
        setUpdateTaskForm({
          ...updateTaskForm,
          // care_calendar: response.data.data[0].care_calendar_id,
        });
        if (
          response?.data?.medicines &&
          response?.data?.medicines?.length > 0
        ) {
          configureSelectedMedicines(response?.data?.medicines);
        }
        if (
          response?.data?.task_files &&
          response?.data?.task_files?.length > 0
        ) {
          for (let item of response.data.task_files) {
            item.fileType = await getFileType(item.document_id);
          }
          console.log("File type", response?.data?.task_files);
          setActivityFiles(response?.data?.task_files);
        }
        if (response?.data?.entity_type_id == 1000460001) {
          getEligibleCattleList(
            response?.data?.activity_id,
            response?.data?.customer_id,
            response?.data?.activity_date.split("T")[0]
          );
        }
        if (
          [1000300050, 1000300051, 1000300052].includes(
            response?.data?.calendar_activity_status
          )
        ) {
          console.log("Disable");
          setFreezeActivity(true);
          // setAskForFollowUpRequired(false)
          setTaskOperations((prevState) => ({
            ...prevState,
            askForFollowUpRequired: false,
          }));
        } else {
          console.log("Enable");
        }
        if (response?.data?.calendar_activity_status === 1000300051) {
          setActivityName("Cancelled");
        }
        if (response?.data?.calendar_activity_status === 1000300052) {
          setActivityName("Abandoned");
        }
        if (response?.data?.activity_id == 1000470001) {
          setUpdateAITaskForm({
            ai_form_company_of_semen: response?.data?.ai_form_company_of_semen,
            ai_form_heat_date_and_time:
              response?.data?.ai_form_heat_date_and_time,
            ai_form_id_of_bull: response?.data?.ai_form_id_of_bull,
            ai_form_name_of_bull: response?.data?.ai_form_name_of_bull,
            ai_form_suitable_for_ai_based_on_discharge:
              response?.data?.ai_form_suitable_for_ai_based_on_discharge,
            ai_form_suitable_for_ai_based_on_heat:
              response?.data?.ai_form_suitable_for_ai_based_on_heat,
          });
        }
        setActivityLoader(false);
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast("Something went wrong! Try again.");
      setActivityLoader(false);
      if (error?.response?.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  }, []);

  const careCalendarId = activityDetails && activityDetails.care_calendar_id;
  const activityId = activityDetails && activityDetails.activity_id;
  const formCompleted = activityDetails?.form_completion_date !== undefined;

  console.log(formCompleted, "isdone");

  const componentMap = {
    1000470001: AIForm,
    1000470003: PDForm,
    1000180015: CMTForm,
  };

  const farmTickActivityIds = [
    1000180011, 1000180005, 1000180014, 1000180002, 1000180003, 1000180004,
    1000180008, 1000180010, 1000180001, 1000180012,
  ];

  const renderActivityForm = () => {
    if (
      formCompleted &&
      farmTickActivityIds.includes(activityDetails.activity_id)
    ) {
      return (
        <FarmTickForm careCalendarId={careCalendarId} activityId={activityId} />
      );
    }
    const FormComponent = componentMap[activityDetails.activity_id];

    if (FormComponent && formCompleted) {
      return (
        <FormComponent
          careCalendarId={careCalendarId}
          activityId={activityId}
        />
      );
    }
    return null;
  };

  const renderPaymentsTab = () => {
    if (shouldShowPaymentsTab()) {
      return <PaymentTab careCalendarId={careCalendarId} />;
    }
    return null;
  };

  const getEligibleCattleList = async (activityId, farmerId, activityDate) => {
    try {
      // const response = await axios.post(
      //   BASE_URL + "/farmtask-animal-list",
      //   {
      //     activity_id: activityId,
      //     farmer_id: farmerId,
      //     activity_date: activityDate,
      //   },
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "*********",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/farmtask-animal-list",
        method: "POST",
        data: {
          activity_id: activityId,
          farmer_id: farmerId,
          activity_date: activityDate,
        },
        headers: {
          "Content-Type": "application/json",
          token: localStorage.getItem("accessToken"),
        },
      });
      if (response?.status === 201) {
        console.log("Eligible cattle list", response?.data?.result);
        setEligibleCattleList(response?.data?.result);
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error);
      if (error?.response?.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  const getFileType = async (docId) => {
    try {
      // const response = await axios.get(BASE_URL + "/media/" + docId, {
      //   headers: {
      //     token: localStorage.getItem("accessToken"),
      //     "Content-Type": "application/json",
      //     "X-App-Id": "*********",
      //   },
      // });
      const response = await instance({
        url: "/media/" + docId,
        method: "GET",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      // console.log("Task doc response", response);
      if (response?.status === 200) {
        return response.headers["content-type"];
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
      // ToastersService.failureToast("Something went wrong! Can't fetch notes.");
      // setActivityLoader(false);
    }
  };

  const getTaskNotes = useCallback(async (taskId) => {
    try {
      // const response = await axios.get(
      //   BASE_URL + "/common/get-task-notes?calendar_id=" + taskId,
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "Content-Type": "application/json",
      //       "X-App-Id": "*********",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/common/get-task-notes?calendar_id=" + taskId,
        method: "GET",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      // console.log(response);
      if (response?.status === 200) {
        console.log("Tasks notes", response?.data);
        // setActivityDetails(response.data[0]);
        setTaskNotes(response?.data);
        // setActivityLoader(false);
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast("Something went wrong! Can't fetch notes.");
      // setActivityLoader(false);
      if (error?.response?.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  }, []);

  const addTaskNote = useCallback(async (noteItem) => {
    try {
      // const response = await axios.post(
      //   BASE_URL + "/common/add-task-note",
      //   {
      //     note: noteItem,
      //     calendar_id: params.taskId,
      //   },
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "*********",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/common/add-task-note",
        method: "POST",
        data: {
          note: noteItem,
          calendar_id: params.taskId,
        },
        headers: {
          "Content-Type": "application/json",
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log("Add task response", response);
      if (response?.status === 201) {
        getTaskNotes(params.taskId);
        setNoteItem("");
        // setCreatingTaskLoader(false);
        // ToastersService.successToast("Task created successfully!");
        // navigate(-1);
      } else {
        throw Error("Note not sent! Try again.");
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error);
      // setCreatingTaskLoader(false);
      if (error?.response?.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  }, []);

  const getParavetsList = useCallback(async () => {
    try {
      // const response = await axios.post(
      //   BASE_URL + "/get-staff",
      //   {
      //     f_staff_type: 1000230004,
      //   },
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "*********",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/get-staff",
        method: "POST",
        data: { f_staff_type: 1000230004 },
        headers: {
          "Content-Type": "application/json",
          token: localStorage.getItem("accessToken"),
        },
      });
      if (response?.status === 201) {
        console.log("Paravets list", response?.data);
        setParavetsList(response?.data);
        // getTaskNotes();
        // setNoteItem("");
      } else {
        throw Error("Couldn't fetch paravets list!");
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error);
      if (error?.response?.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  }, []);

  const reassignTask = useCallback(async () => {
    console.log("Assign task req body", {
      care_calendar: activityDetails.care_calendar_id,
      staff_id: selectedParavetId,
      activity_id: activityDetails.activity_id,
      entity_type_id: activityDetails.entity_type_id,
      entity_id: activityDetails.entity_uuid,
    });
    try {
      // const response = await axios.put(
      //   BASE_URL + "/activity/re-assign",
      //   {
      //     // calendar_id: activityDetails.care_calendar_id,
      //     // paravet_id: selectedParavetId,
      //     care_calendar: activityDetails.care_calendar_id,
      //     staff_id: selectedParavetId,
      //     activity_id: activityDetails.activity_id,
      //     entity_type_id: activityDetails.entity_type_id,
      //     entity_id: activityDetails.entity_uuid,
      //   },
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "*********",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/activity/re-assign",
        method: "PUT",
        data: {
          // calendar_id: activityDetails.care_calendar_id,
          // paravet_id: selectedParavetId,
          care_calendar: activityDetails.care_calendar_id,
          staff_id: selectedParavetId,
          activity_id: activityDetails.activity_id,
          entity_type_id: activityDetails.entity_type_id,
          entity_id: activityDetails.entity_uuid,
        },
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log(response);
      if (response?.status === 200) {
        if (response?.data?.result === true) {
          ToastersService.successToast("Task reassigned successfully!");
          getActivityDetails(params.taskId);
        }
      } else {
        throw Error("Cannot reassign task! Something went wrong.");
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error);
      if (error?.response?.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  }, [activityDetails, selectedParavetId]);

  const configureSelectedMedicines = (medicinesArr) => {
    console.log("configureSelectedMedicines", medicinesArr);
    const tempSelectedMedicines = [];
    for (let item of medicinesArr) {
      if (!tempSelectedMedicines.includes(Object.keys(item)[0].split("_")[1])) {
        tempSelectedMedicines.push(Object.keys(item)[0].split("_")[1]);
      }
    }
    console.log("configureSelectedMedicines1", tempSelectedMedicines);
    // for (let i=0; i<tempSelectedMedicines.length; i++) {
    //   tempSelectedMedicines[i] = {count: parseInt(tempSelectedMedicines[i])}
    // }

    for (let i = 0; i < tempSelectedMedicines.length; i++) {
      tempSelectedMedicines[i] = { count: parseInt(tempSelectedMedicines[i]) };
      for (let item of medicinesArr) {
        if (
          tempSelectedMedicines[i].count ===
          parseInt(Object.keys(item)[0].split("_")[1])
        ) {
          tempSelectedMedicines[i] = {
            ...tempSelectedMedicines[i],
            medicineId: item[Object.keys(item)[0]],
          };
          tempSelectedMedicines[i] = {
            ...tempSelectedMedicines[i],
            quantity: item[Object.keys(item)[1]],
          };
          // if (item.type_of_data === "value_reference_id") {
          //   tempSelectedMedicines[i] = {
          //     ...tempSelectedMedicines[i],
          //     medicineId: item[Object.keys(item)[0]],
          //   };
          // } else if (item.type_of_data === "value_double") {
          //   tempSelectedMedicines[i] = {
          //     ...tempSelectedMedicines[i],
          //     quantity: item[Object.keys(item)[1]],
          //   };
          // }
        }
      }
    }

    console.log("configureSelectedMedicines2", tempSelectedMedicines);
    setSelectedMedicines(tempSelectedMedicines);
  };

  const updateActivity = useCallback(async () => {
    const calendarActivityStatuses = [1000300050, 1000300051, 1000300052];
    if (
      calendarActivityStatuses.includes(updateTaskForm.calendar_activity_status)
    ) {
      // updateTaskForm = { ...updateTaskForm, completion_date:!isNaN(completionDate) ? moment(completionDate).format("YYYY-MM-DD") : null,completion_date_gmt: !isNaN(completionDate) ? completionDate.toISOString() : null  };
      updateTaskForm = {
        ...updateTaskForm,
        completion_date: completionDate
          ? moment(completionDate).format("YYYY-MM-DD") // sending in normal date format
          : null,
        completion_date_gmt: completionDate
          ? completionDate.toISOString() // sending in utc format
          : null,
      };

      if (nextActivityDate) {
        updateTaskForm = {
          ...updateTaskForm,
          follow_up_required: true,
          activity_date: moment(nextActivityDate).format("YYYY-MM-DD"), // sending in normal date format
          activity_date_gmt: new Date(nextActivityDate).toISOString(), // converting to utc format
        };
      } else {
        updateTaskForm = {
          ...updateTaskForm,
          follow_up_required: false,
        };
      }
    }

    if (selectedMedicines.length > 0) {
      selectedMedicines.forEach((item) => {
        const key1 = `medicine_${item.count}`;
        const value1 = item.medicineId;
        const key2 = `medicine_${item.count}_quantity`;
        const value2 = item.quantity;
        const newKeyValuePair1 = { [key1]: value1 };
        const newKeyValuePair2 = { [key2]: value2 };
        updateTaskForm = {
          ...updateTaskForm,
          ...newKeyValuePair1,
          ...newKeyValuePair2,
        };
        console.log("Update task form", updateTaskForm);
      });
    }

    console.log("updateAITaskForm", updateAITaskForm);
    if (activityDetails.activity_id == 1000470001) {
      updateTaskForm = { ...updateTaskForm, ...updateAITaskForm };
    }

    console.log("Updated acitivity form", updateTaskForm, selectedMedicines);

    // Images, PDFs and Videos start
    if (newActivityFiles.length > 0) {
      let files = [];
      // Resizing Images start
      for (let file of newActivityFiles) {
        if (file.file.type.split("/")[0] == "image") {
          file.file = await resizeFile(file.file);
        }
      }
      // Resizing Images end

      files.push({
        fieldName: "task_activity_photo_1",
        images: [...newActivityFiles],
      });
      console.log(files);
      // start loader saying uploading files
      await configureFiles(activityDetails.care_calendar_id, files);
      // close loader
    }
    // Images, PDFs and Videos end

    try {
      // const response = await axios.put(
      //   BASE_URL + "/activity/update-activity",
      //   { ...updateTaskForm, care_calendar: activityDetails.care_calendar_id },
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "*********",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/activity/update-activity",
        method: "PUT",
        data: {
          ...updateTaskForm,
          care_calendar: activityDetails.care_calendar_id,
        },
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log(response);
      if (response?.status === 200) {
        // setCreatingTaskLoader(false);
        ToastersService.successToast("Activity updated successfully!");
        // setAskForFollowUp(false);
        setTaskOperations((prevState) => ({
          ...prevState,
          askForFollowUp: false,
          showCheckboxes: false,
        }));
        getActivityDetails(params.taskId);
        getFollowUpTasks();
        // getActivityDetails();
        // navigate(-1);
      } else {
        throw Error("Status not changed! Try again.");
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error);
      if (error?.response?.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  }, [
    updateTaskForm,
    selectedMedicines,
    completionDate,
    newActivityFiles,
    nextActivityDate,
    activityDetails,
    updateAITaskForm,
  ]);

  const configureFiles = async (calendarId, files) => {
    console.log("Post images", calendarId, files);
    setNewActivityFiles([]);
    let count = 0;
    for (let file of files) {
      for (let item of file.images) {
        count = count + 1;
        // filesCount = filesCount + 1;
        console.log("Files count", count);
      }
    }

    for (let file of files) {
      for (let item of file.images) {
        const formData = new FormData();
        formData.append("file", item.file);
        formData.append("entity", "task");
        await uploadImageToServer(
          formData,
          file.fieldName,
          calendarId,
          item.description,
          count
        );
      }
    }
  };

  const uploadImageToServer = useCallback(
    async (formData, documentType, calendarId, imgDes, filesCount) => {
      try {
        // const response = await axios.post(BASE_URL + "/document", formData, {
        //   headers: {
        //     token: localStorage.getItem("accessToken"),
        //     "X-App-Id": "*********",
        //   },
        // });
        const response = await instance({
          url: "/document",
          method: "POST",
          data: formData,
          headers: {
            token: localStorage.getItem("accessToken"),
          },
        });
        console.log("File upload response", response);

        if (response?.status === 201) {
          ToastersService.successToast("File uploaded to server!");
          documentEntries.push({
            document_type: documentType,
            entity: "task",
            entity_1_id: calendarId,
            url: response?.data?.data?.doc_key,
            document_information: imgDes,
          });
          setDocumentEntries([...documentEntries]);
          console.log(documentEntries.length, filesCount);
          if (documentEntries.length === filesCount) {
            await postImages(documentEntries, calendarId);
          }
        } else {
          throw Error("Image not uploaded to server!");
        }
      } catch (error) {
        console.log(error);
        // ToastersService.failureToast(error);
        if (error?.response?.status === 401) {
          console.log("token expired");
          localStorage.removeItem("accessToken");
          localStorage.removeItem("resetToken");
          ToastersService.failureToast("Session Expired!");
          navigate("/login");
          window.location.reload();
        }
        if (error?.response?.status === 413) {
          ToastersService.failureToast(
            error?.response?.data?.error
              ? error?.response?.data?.error
              : "File size limit exceeded!"
          );
        }
      }
    },
    [documentEntries]
  );

  const postImages = useCallback(
    async (documentEntries, calendarId) => {
      try {
        // const response = await axios.post(
        //   BASE_URL + "/document",
        //   { document_entries: documentEntries },
        //   {
        //     headers: {
        //       token: localStorage.getItem("accessToken"),
        //       "X-App-Id": "*********",
        //     },
        //   }
        // );
        const response = await instance({
          url: "/document",
          method: "POST",
          data: { document_entries: documentEntries },
          headers: {
            "Content-Type": "application/json",
            token: localStorage.getItem("accessToken"),
          },
        });
        console.log("Images to db", response);
        if (response?.status === 201) {
          ToastersService.successToast("File saved to db!");
          // getActivityDetails();
        } else {
          throw Error("Images not uploaded to db!");
        }
      } catch (error) {
        console.log(error);
        // ToastersService.failureToast(error);
        if (error?.response?.status === 401) {
          console.log("token expired");
          localStorage.removeItem("accessToken");
          localStorage.removeItem("resetToken");
          ToastersService.failureToast("Session Expired!");
          navigate("/login");
          window.location.reload();
        }
      }
    },
    [documentEntries]
  );

  const getUserDetails = useCallback(async () => {
    try {
      // const response = await axios.get(BASE_URL + "/common/resolve-token", {
      //   headers: {
      //     token: localStorage.getItem("accessToken"),
      //     "X-App-Id": "*********",
      //   },
      // });
      const response = await instance({
        url: "/common/resolve-token",
        method: "GET",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      if (response?.status === 200) {
        // if (response.data.result === false) {
        //   tokenFailureHandler(response.data);
        //   // navigate("/login");
        // }
        // console.log("Profile Image", response.data);
        if (response?.data?.user_type_resolved?.type === "back_office_person") {
          getParavetsList();
        }
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  }, []);

  const getFollowUpTasks = useCallback(async () => {
    try {
      // const response = await axios.get(
      //   BASE_URL + "/linked-task-history/" + params.taskId,
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "*********",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/linked-task-history/" + params.taskId,
        method: "GET",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log("Follow ups", response);
      if (response?.status === 200) {
        setFollowUps(response?.data?.result?.reverse());
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  }, []);

  useEffect(() => {
    // getActivityConfig();
    setActivityConfigurationsFromGlobalRef();
    getTaskNotes(params.taskId);
    getUserDetails(); // calling this function for get paravet list for re-assign the task
    getFollowUpTasks();
    // Confirmation popup in case of AI
    const urlParams = new URLSearchParams(window.location.search);
    const hasQueryParams = urlParams.get("popup");
    if (hasQueryParams) {
      handleConfimationPopupModalShow();
    }
  }, [getTaskNotes, getUserDetails, getFollowUpTasks]);

  const activityStatusChangeHandler = (event) => {
    console.log({
      ...updateTaskForm,
      calendar_activity_status: parseInt(event.target.value),
    });
    setNextActivityDate();
    setTaskOperations((prevState) => ({
      ...prevState,
      askForFollowUp: true,
    }));
    setReason("");
    if (event.target.value == "1000300050") {
      setTaskOperations((prevState) => ({
        ...prevState,
        askForCompletion: true,
        askForReason: false,
      }));
      setActivityName(null);
      if (activityDetails.entity_type_id == 1000460001) {
        console.log("found");
        setTaskOperations((prevState) => ({
          ...prevState,
          showCheckboxes: true,
        }));
      }
    } else if (
      (event.target.value === "1000300051" &&
        (setActivityName("Cancelled") || 1)) ||
      (event.target.value === "1000300052" &&
        (setActivityName("Abandoned") || 1))
    ) {
      setTaskOperations((prevState) => ({
        ...prevState,
        askForReason: true,
        showCheckboxes: false,
        askForCompletion: false,
      }));
      setCompletionDate(null);
    }
    setUpdateTaskForm({
      ...updateTaskForm,
      calendar_activity_status: parseInt(event.target.value),
    });
  };
  // const changeMedicineHandler = (event, value) => {
  //   console.log(value);
  //   // setSelectedParavetId(value.staff_id);
  //   console.log({
  //     ...updateTaskForm,
  //     medicine_id: value.reference_id,
  //   });
  //   setUpdateTaskForm({
  //     ...updateTaskForm,
  //     medicine_id: value.reference_id,
  //   });
  // };

  // const medicineQuantityChangeHandler = (event) => {
  //   console.log({
  //     ...updateTaskForm,
  //     medicine_qt: event.target.value,
  //   });
  //   setUpdateTaskForm({
  //     ...updateTaskForm,
  //     medicine_qt: event.target.value,
  //   });
  // };

  const chooseMedicineHandler = (value, count) => {
    for (let item of selectedMedicines) {
      if (item.count === count) {
        item.medicineId = value ? [value.medicine_id] : []; // Check if value is not null before accessing reference_id
      }
    }
    setSelectedMedicines([...selectedMedicines]);
  };

  const medicineQuantityHandler = (event, count) => {
    console.log(event.target.value, count);
    for (let item of selectedMedicines) {
      if (item.count == count) {
        item.quantity = event.target.value;
      }
    }

    console.log(selectedMedicines);
    setSelectedMedicines(selectedMedicines);
  };

  // const removeMedicineHandler = (count) => {
  //   for (let item of selectedMedicines) {
  //     if (item.count == count) {
  //       selectedMedicines = selectedMedicines.filter((item) => item.count !== count);
  //     }
  //   }

  //   console.log(selectedMedicines);
  //   setSelectedMedicines(selectedMedicines);
  // };

  const addMedicineHandler = () => {
    if (selectedMedicines.length > 9) {
      ToastersService.failureToast("Maximum 10 medicines are allowed!");
    } else {
      setSelectedMedicines([
        ...selectedMedicines,
        {
          count: selectedMedicines.length + 1,
          medicineId: null,
          quantity: null,
        },
      ]);
    }
  };

  const activityDateChangeHandler = (event) => {
    console.log({
      ...updateTaskForm,
      activity_date: moment
        .utc(event.target.value, "YYYY-MM-DD")
        .toISOString(true),
    });
    setUpdateTaskForm({
      ...updateTaskForm,
      activity_date: moment
        .utc(event.target.value, "YYYY-MM-DD")
        .toISOString(true),
    });
  };

  const noteInputHandler = (event) => {
    setNoteItem(event.target.value);
    // console.log(noteItem);
  };

  const sendBtnHandler = () => {
    if (noteItem.trim() !== "") {
      console.log("Send Btn", noteItem);
      addTaskNote(noteItem);
    }
  };

  const chooseParavetHandler = (event, value) => {
    console.log(value);
    setSelectedParavetId(value.staff_id);
  };

  const updateTaskHandler = () => {
    console.log("date", updateTaskForm.activity_date);

    let AISpecificError = false;
    if (activityDetails.activity_id == 1000470001) {
      if (
        !updateAITaskForm.ai_form_suitable_for_ai_based_on_heat ||
        !updateAITaskForm.ai_form_suitable_for_ai_based_on_discharge
      ) {
        console.log("Dropdowns not selected");
        AISpecificError = true;
      } else {
        console.log("Dropdowns selected");
        if (
          updateAITaskForm.ai_form_suitable_for_ai_based_on_heat ==
            1000105001 &&
          updateAITaskForm.ai_form_suitable_for_ai_based_on_discharge ==
            1000105001
        ) {
          if (
            !updateAITaskForm.ai_form_id_of_bull ||
            (updateAITaskForm.ai_form_id_of_bull &&
              updateAITaskForm.ai_form_id_of_bull.trim() == "")
          ) {
            console.log("throw error");
            AISpecificError = true;
          } else if (
            !updateAITaskForm.ai_form_name_of_bull ||
            (updateAITaskForm.ai_form_name_of_bull &&
              updateAITaskForm.ai_form_name_of_bull.trim() == "")
          ) {
            console.log("throw error");
            AISpecificError = true;
          } else if (!updateAITaskForm.ai_form_heat_date_and_time) {
            console.log("throw error");
            AISpecificError = true;
          } else if (!updateAITaskForm.ai_form_company_of_semen) {
            console.log("throw error");
            AISpecificError = true;
          }
        }
      }
    }

    if (
      activityDetails.entity_type_id == 1000460001 &&
      updateTaskForm.calendar_activity_status == 1000300050 &&
      eligibleCattleList.length > 0 &&
      updateTaskForm.animal_ids.length <= 0
    ) {
      ToastersService.failureToast("Select cattle for completed activity!");
    } else if (
      updateTaskForm.calendar_activity_status == 1000300050 &&
      (completionDate == null || completionDate == undefined)
    ) {
      ToastersService.failureToast(
        "Completion date is mandatory for activity completion!"
      );
    } else if (AISpecificError == true) {
      ToastersService.failureToast("AI specific fields are mandatory!");
    } else if (
      (updateTaskForm.calendar_activity_status == 1000300051 ||
        updateTaskForm.calendar_activity_status == 1000300052) &&
      (reason == "" ||
        reason == undefined ||
        reason == null ||
        !reason ||
        !reason.trim())
    ) {
      ToastersService.failureToast(`Reason is mandatory for ${activityName}`);
    } else {
      let noteItem = "";
      if (updateTaskForm.activity_date) {
        noteItem =
          "New Date: " +
          moment(updateTaskForm.activity_date).format("D-MMM-YYYY") +
          " ";
        console.log("mjjhj", noteItem);
      }

      if (
        updateTaskForm.calendar_activity_status ||
        updateTaskForm.activity_date
      ) {
        for (let item of statusOptions) {
          // console.log("item",item)
          if (item.reference_id == updateTaskForm.calendar_activity_status) {
            noteItem +=
              "New Status: " +
              (item.reference_name_l10n.en
                ? item.reference_name_l10n.en
                : item.reference_name_l10n.ul
                ? item.reference_name_l10n.ul
                : "");
            console.log(
              "itemRef",
              item.reference_name_l10n.en
                ? item.reference_name_l10n.en
                : item.reference_name_l10n.ul
                ? item.reference_name_l10n.ul
                : ""
            );
          }
        }

        if (selectedMedicines.length > 0) {
          console.log(selectedMedicines);
          for (let item of selectedMedicines) {
            if (item.medicineId !== null) {
              for (let medicine of medicines) {
                if (medicine.medicine_id == item.medicineId) {
                  noteItem = noteItem.concat(
                    `, Medicine(${item.count}): ` + medicine.medicine_name
                  );
                }
              }
            }
            if (item.quantity !== null) {
              noteItem = noteItem.concat(
                `, Quantity(${item.count}): ` + item.quantity
              );
            }
          }
        }

        console.log(noteItem);
        //  addTaskNote(noteItem);
        if (
          updateTaskForm.calendar_activity_status == 1000300051 ||
          updateTaskForm.calendar_activity_status == 1000300052
        ) {
          addTaskNote(reason);
        } else {
          addTaskNote(noteItem);
        }
      }

      updateActivity();
    }
  };

  const fileUploadHandler = async (event) => {
    console.log(event.target.files[0]);
    if (
      event.target.files[0].type.split("/")[0] == "image" ||
      event.target.files[0].type == "application/pdf" ||
      event.target.files[0].type.split("/")[0] == "video"
    ) {
      setFile(event.target.files[0]);
    } else {
      ToastersService.failureToast("Invalid file type!");
    }
  };

  const removeFileHandler = () => {
    file = undefined;
    setFile(file);
  };

  const fileDescriptionChangeHandler = (event) => {
    console.log(event.target.value);
    setFileDes(event.target.value);
  };

  const saveFileHandler = () => {
    console.log(file, fileDes);
    if (!file) {
      ToastersService.failureToast("Please add the file!");
    } else {
      activityFileUploadHandler(file, fileDes);
      setFile(undefined);
      setFileDes("");
      handleFileModalClose();
    }
  };

  console.log(activityDetails?.customer_id, "farmerId");
  const activityFileUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setNewActivityFiles([...newActivityFiles, fileItem]);
  };

  const removeActivityFileHandler = (fileName) => {
    console.log(fileName);
    for (let item of newActivityFiles) {
      if (item.file.name === fileName) {
        newActivityFiles = newActivityFiles.filter(
          (i) => i.file.name !== fileName
        );
      }
    }
    setNewActivityFiles([...newActivityFiles]);
  };

  const checkboxChangeHandler = (event) => {
    console.log(event.target.value, event.target.checked);
    if (event.target.checked == true) {
      updateTaskForm.animal_ids.push(event.target.value);
      console.log(updateTaskForm);
      setUpdateTaskForm(updateTaskForm);
    } else if (event.target.checked == false) {
      updateTaskForm.animal_ids = updateTaskForm.animal_ids.filter(
        (item) => item !== event.target.value
      );
      console.log(updateTaskForm);
      setUpdateTaskForm(updateTaskForm);
    }
  };

  // const radioBtnChangeHandler = (event) => {
  //   console.log(event.target.value);
  //   setFollowUpRequired(event.target.value);
  // }

  const followUpsClickHandler = (careCalendarId) => {
    navigate("/tasks/" + careCalendarId);
    window.location.reload();
    // getActivityConfig();
    // getTaskNotes(params.taskId);
  };
  const completionDateChangeHandler = (date) => {
    if (date && !date.getHours() && !date.getMinutes()) {
      date.setHours(0);
      date.setMinutes(0);
    }
    setCompletionDate(date);
  };
  const followUpDateChangeHandler = (date) => {
    if (date && !date.getHours() && !date.getMinutes()) {
      date.setHours(0);
      date.setMinutes(0);
    }
    setNextActivityDate(date);
  };
  const tabChangeHandler = (tabCount) => {
    for (let item of taskItemTabs) {
      if (item.count === tabCount) {
        item.isSelected = true;
      } else {
        item.isSelected = false;
      }
    }
    setTaskItemTabs([...taskItemTabs]);
  };

  const isServiceDocAva =
    activityDetails?.serviceDocument &&
    (activityDetails?.serviceDocument?.document_information.uri ||
      activityDetails?.serviceDocument?.document_information.url ||
      activityDetails?.serviceDocument?.document_information.key);

  return (
    <>
      <div className="mb-4">
        <p className="farmer-details__back-btn" onClick={navigateBackHandler}>
          <i className="fa fa-angle-double-left" aria-hidden="true"></i> Go back
        </p>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div>
            <p
              className="mb-0"
              style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
            >
              Tasks / Task Item
            </p>
            {activityDetails && Object.keys(activityDetails).length > 0 && (
              <p
                className="m-0"
                style={{
                  fontSize: "18px",
                  fontWeight: "700",
                  color: "#222222",
                  textTransform: "capitalize",
                }}
              >
                {activityDetails.entity_type_id == 1000460001 ? (
                  <>
                    <i className="fa fa-user" aria-hidden="true"></i>{" "}
                    {activityDetails.customer_name_l10n
                      ? extractBasedOnLanguage(
                          activityDetails.customer_name_l10n,
                          userLanguage
                        )
                      : ""}{" "}
                    /{" "}
                    {activityDetails.customer_visual_id
                      ? activityDetails.customer_visual_id
                      : ""}
                  </>
                ) : (
                  <>
                    <i className="fa fa-tag" aria-hidden="true"></i>{" "}
                    {activityDetails.ear_tag ? activityDetails.ear_tag : ""} /{" "}
                    {activityDetails.animal_visual_id
                      ? activityDetails.animal_visual_id
                      : ""}
                  </>
                )}
              </p>
            )}
          </div>
          <div style={{ marginLeft: "auto", display: "flex" }}>
            <div style={{ marginRight: 10 }}>
              {activityDetails && activityDetails?.serviceDocument && (
                <ServiceDoc docInfo={activityDetails?.serviceDocument} />
              )}
            </div>
            <div style={{ marginRight: 10 }}>
              {activityDetails && <ShareOTP details={activityDetails} />}
            </div>
            <div style={{ marginRight: 10 }}>
              {activityDetails && (
                <ShareOTP
                  details={activityDetails}
                  name="Share OTP with alternate number"
                  type="alternate_farmer_contact"
                />
              )}
            </div>
            <div style={{ marginLeft: "auto" }}>
              <button className="assign-btn" onClick={showModal}>
                Preview
              </button>
            </div>
          </div>
        </div>
      </div>
      {/* tab function */}
      <div className="taskTab__floating-menu">
        {taskItemTabs.map((tab) => {
          return (
            <div
              key={tab.count}
              className={
                tab.isSelected === true
                  ? "task__tab-selected"
                  : "task__tab-unselected"
              }
              onClick={() => tabChangeHandler(tab.count)}
            >
              <div>{tab.name}</div>
            </div>
          );
        })}
      </div>
      {taskItemTabs[0].isSelected === true && (
        <div>
          {activityLoader === true ? (
            <div className="text-center my-5">
              <Loader />
            </div>
          ) : (
            <>
              {activityDetails && Object.keys(activityDetails).length > 0 ? (
                <div className="taskItem__task-data">
                  <div className="mb-5">
                    <div className="taskItem__header">Activity Details</div>
                    <div className="taskItem__body">
                      <Row>
                        <Col lg={4} md={6} sm={12}>
                          <small>Activity Name:</small>
                          <p style={{ textTransform: "capitalize" }}>
                            {activityDetails.activity_name_l10n
                              ? extractBasedOnLanguage(
                                  activityDetails.activity_name_l10n,
                                  userLanguage
                                )
                              : "-"}
                          </p>
                        </Col>
                        <Col lg={4} md={6} sm={12}>
                          <small>Service Request:</small>
                          <p>
                            {activityDetails.ticket_1
                              ? activityDetails.ticket_1
                              : "-"}
                          </p>
                        </Col>
                        <Col lg={4} md={6} sm={12}>
                          <small>Activity Date and Time:</small>
                          <DatePicker
                            style={{ width: "100%" }}
                            className="input-field input-width"
                            selected={
                              activityDetails.visit_schedule_time
                                ? convertUtcToIst(
                                    activityDetails.visit_schedule_time
                                  )
                                : convertUtcToIst(activityDetails.activity_date)
                                ? convertUtcToIst(activityDetails.activity_date)
                                : ""
                            }
                            showTimeSelect
                            dateFormat="Pp"
                            timeCaption="Time"
                            disabled
                          />
                        </Col>
                        {activityDetails.completion_date && (
                          <Col lg={4} md={6} sm={12}>
                            <small>Completion Date and Time:</small>
                            <DatePicker
                              style={{ width: "100%" }}
                              className="input-field input-width"
                              selected={
                                activityDetails.user_task_completion_time
                                  ? convertUtcToIst(
                                      activityDetails.user_task_completion_time
                                    )
                                  : activityDetails.completion_date
                                  ? convertUtcToIst(
                                      activityDetails.completion_date
                                    )
                                  : ""
                              }
                              showTimeSelect
                              dateFormat="Pp"
                              timeIntervals={5}
                              timeCaption="Time"
                              disabled
                              defaultValue={
                                activityDetails.user_task_completion_time
                                  ? convertUtcToIst(
                                      activityDetails.user_task_completion_time
                                    )
                                  : activityDetails.completion_date
                                  ? convertUtcToIst(
                                      activityDetails.completion_date
                                    )
                                  : ""
                              }
                            />
                          </Col>
                        )}
                      </Row>
                      <hr />

                      {![1000700001, 1000700002].includes(
                        activityDetails.activity_id
                      ) && (
                        <>
                          <p>Medicines:</p>
                          {selectedMedicines &&
                            selectedMedicines.map((medicine) => (
                              <Row className="mb-2" key={medicine.count}>
                                <Col lg={4} md={6} sm={12}>
                                  <small>Medicine {medicine.count}</small>
                                  <Autocomplete
                                    options={medicines}
                                    getOptionLabel={(option) =>
                                      extractBasedOnLanguage(
                                        option.medicine_name
                                      )
                                    }
                                    disabled={freezeActivity}
                                    sx={{
                                      width: "100%",
                                      marginTop: "5px",
                                      background: "#fff",
                                    }}
                                    defaultValue={() => {
                                      for (let option of medicines) {
                                        if (
                                          (!Array.isArray(
                                            medicine.medicineId
                                          ) &&
                                            medicine.medicineId ===
                                              option.medicine_id) ||
                                          (Array.isArray(medicine.medicineId) &&
                                            medicine.medicineId.length > 0 &&
                                            medicine.medicineId[0] ===
                                              option.medicine_id)
                                        ) {
                                          return option;
                                        }
                                      }
                                    }}
                                    renderInput={(params) => (
                                      <TextField
                                        {...params}
                                        label="Search..."
                                      />
                                    )}
                                    onChange={(event, value) => {
                                      console.log(event);
                                      console.log(value);
                                      chooseMedicineHandler(
                                        value,
                                        medicine.count
                                      );
                                    }}
                                  />
                                </Col>
                                <Col lg={4} md={6} sm={12}>
                                  <small>Quantity {medicine.count}</small>
                                  <input
                                    className="input-field"
                                    style={{ marginTop: "5px", width: "100%" }}
                                    type="number"
                                    placeholder="eg: 2"
                                    onWheel={(e) => e.target.blur()}
                                    disabled={freezeActivity}
                                    defaultValue={medicine.quantity}
                                    onChange={(event) =>
                                      medicineQuantityHandler(
                                        event,
                                        medicine.count
                                      )
                                    }
                                  />
                                </Col>
                                {/* <Col lg={4} md={6} sm={12}>
                          <div
                            style={{ border: "1px solid silver" }}
                            onClick={() => removeMedicineHandler(medicine.count)}
                          >
                            Remove
                          </div>
                        </Col> */}
                              </Row>
                            ))}

                          {freezeActivity == false && (
                            <div
                              className="add-cattle__add-disease-btn mt-3"
                              onClick={addMedicineHandler}
                            >
                              + Add medicine
                            </div>
                          )}
                          <hr />
                        </>
                      )}

                      {/* Activity Status */}
                      <Row>
                        <Col lg={4} md={6} sm={12}>
                          <small>Activity Status:</small>
                          <select
                            className="input-field"
                            style={{ width: "100%" }}
                            disabled={freezeActivity}
                            // defaultValue={activityDetails.calendar_activity_status}
                            onChange={activityStatusChangeHandler}
                          >
                            <option
                              disabled
                              selected
                              value={activityDetails?.calendar_activity_status}
                            >
                              {activityDetails
                                ?.calendar_activity_status_name_json?.en
                                ? activityDetails
                                    ?.calendar_activity_status_name_json?.en
                                : activityDetails
                                    ?.calendar_activity_status_name_json?.ul
                                ? activityDetails
                                    ?.calendar_activity_status_name_json?.ul
                                : ""}
                            </option>
                            {statusOptions &&
                              statusOptions.map((key) => {
                                return (
                                  <option
                                    key={key.reference_id}
                                    value={key.reference_id}
                                    style={{ textTransform: "capitalize" }}
                                  >
                                    {/* {key.reference_name_l10n.en
                                  ? key.reference_name_l10n.en
                                  : key.reference_name_l10n.ul ? key.reference_name_l10n.ul :""} */}
                                    {key.reference_name_l10n}
                                  </option>
                                );
                              })}
                          </select>
                        </Col>
                        {activityDetails?.followUpDateTime && (
                          <Col lg={4} md={6} sm={12}>
                            <small>
                              {activityDetails?.calendar_activity_status ===
                              1000300050
                                ? "Follow up Date and Time:"
                                : "Rescheduled Date and Time:"}
                            </small>
                            <div>
                              <DatePicker
                                style={{ width: "100%" }}
                                className="input-field input-width"
                                selected={
                                  activityDetails?.followUpDateTime
                                    ? convertUtcToIst(
                                        activityDetails?.followUpDateTime
                                      )
                                    : ""
                                }
                                showTimeSelect
                                dateFormat="Pp"
                                timeIntervals={5}
                                timeCaption="Time"
                                disabled
                              />
                            </div>
                          </Col>
                        )}
                        {taskOperations?.askForFollowUp && (
                          <>
                            {taskOperations?.askForCompletion && (
                              <Col lg={4} md={6} sm={12}>
                                <small>Completion Date and Time*:</small>
                                <div>
                                  <DatePicker
                                    style={{ width: "100%" }}
                                    className="input-field input-width"
                                    selected={completionDate}
                                    onChange={completionDateChangeHandler}
                                    showTimeSelect
                                    dateFormat="Pp"
                                    timeIntervals={5}
                                    timeCaption="Time"
                                    placeholderText="Please select a date and time"
                                  />
                                </div>
                              </Col>
                            )}
                            {taskOperations?.askForReason && (
                              <Col lg={4} md={6} sm={12}>
                                <small>
                                  Reason{" "}
                                  {`for ${activityName ? activityName : ""}*`}:
                                </small>
                                <div>
                                  <TextareaAutosize
                                    type="input"
                                    className="input-field"
                                    style={{ width: "100%" }}
                                    value={reason}
                                    onChange={(event) =>
                                      setReason(event.target.value)
                                    }
                                  />
                                </div>
                              </Col>
                            )}
                            {taskOperations?.askForFollowUpRequired && (
                              <Col lg={4} md={6} sm={12}>
                                {taskOperations?.askForReason ? (
                                  <small>
                                    Reschedule is required? If yes, select date
                                    and time:
                                  </small>
                                ) : (
                                  <small>
                                    Follow up required? If yes, select date and
                                    time:
                                  </small>
                                )}
                                {/* <p className="m-0" style={{ fontSize: "13px" }}>
                            If yes, choose activity date:
                          </p> */}
                                <DatePicker
                                  style={{ width: "100%" }}
                                  className="input-field input-width"
                                  selected={nextActivityDate}
                                  onChange={followUpDateChangeHandler}
                                  showTimeSelect
                                  dateFormat="Pp"
                                  timeIntervals={5}
                                  timeCaption="Time"
                                  placeholderText="Please select a date and time"
                                />
                              </Col>
                            )}
                          </>
                        )}
                      </Row>
                      <hr />

                      {activityDetails.animals && (
                        <>
                          {activityDetails.animals.length > 0 ? (
                            <>
                              <div>
                                <small>Marked cattle:</small>
                                <Table className="my-3" striped bordered>
                                  <thead>
                                    <tr>
                                      <th className="text-center">#</th>
                                      <th>Visual Id</th>
                                      <th>Ear Tag</th>
                                      <th>Cattle type</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {activityDetails.animals.map((item) => (
                                      <tr key={item.animal_id}>
                                        <td className="text-center">
                                          <input
                                            type="checkbox"
                                            style={{ cursor: "pointer" }}
                                            defaultChecked={true}
                                            disabled
                                          />
                                        </td>
                                        <td>{item.animal_visual_id}</td>
                                        <td>{item.eartag}</td>
                                        <td>
                                          {item.reference_name_l10n.en
                                            ? item.reference_name_l10n.en
                                            : item.reference_name_l10n.ul
                                            ? item.reference_name_l10n.ul
                                            : ""}
                                        </td>
                                      </tr>
                                    ))}
                                  </tbody>
                                </Table>
                                <hr />
                              </div>
                            </>
                          ) : (
                            <>
                              {eligibleCattleList && (
                                <>
                                  {eligibleCattleList.length === 0 ? (
                                    <div
                                      className="my-5 text-center"
                                      style={{
                                        color: "gray",
                                        fontSize: "14px",
                                      }}
                                    >
                                      <i className="fa fa-exclamation-triangle"></i>{" "}
                                      No eligible cattle found!
                                    </div>
                                  ) : (
                                    <div>
                                      <small>Eligible cattle:</small>
                                      <Table className="my-3" striped bordered>
                                        <thead>
                                          <tr>
                                            <th className="text-center">#</th>
                                            <th>Visual Id</th>
                                            <th>Ear Tag</th>
                                            <th>Cattle type</th>
                                          </tr>
                                        </thead>
                                        <tbody>
                                          {eligibleCattleList.map((item) => (
                                            <tr key={item.animal_id}>
                                              <td className="text-center">
                                                {taskOperations.showCheckboxes ==
                                                false ? (
                                                  ""
                                                ) : (
                                                  <input
                                                    type="checkbox"
                                                    style={{
                                                      cursor: "pointer",
                                                    }}
                                                    value={item.animal_id}
                                                    onChange={
                                                      checkboxChangeHandler
                                                    }
                                                  />
                                                )}
                                              </td>
                                              <td>{item.animal_visual_id}</td>
                                              <td>{item.ear_tag}</td>
                                              <td>
                                                {item.animal_type_l10n.en
                                                  ? item.animal_type_l10n.en
                                                  : item.animal_type_l10n.ul
                                                  ? item.animal_type_l10n.ul
                                                  : ""}
                                              </td>
                                            </tr>
                                          ))}
                                        </tbody>
                                      </Table>
                                      <hr />
                                    </div>
                                  )}
                                </>
                              )}
                            </>
                          )}
                        </>
                      )}

                      {/* AI Specific Task */}
                      {activityDetails.activity_id == 1000470001 && (
                        <AISpecificTaskComponent
                          activityDetails={activityDetails}
                          AIconfig={AIconfig}
                          updateAITaskForm={updateAITaskForm}
                          setUpdateAITaskForm={setUpdateAITaskForm}
                        />
                      )}

                      {/* Activity File */}
                      {![1000700001, 1000700002].includes(
                        activityDetails.activity_id
                      ) && (
                        <>
                          <p>Activity Files:</p>

                          {newActivityFiles.length > 0 && (
                            <Row className="my-3">
                              {newActivityFiles.map((item) => (
                                <Col
                                  lg={4}
                                  md={6}
                                  sm={6}
                                  xs={12}
                                  key={item.file.lastModified}
                                >
                                  <div className="add-task__img-div">
                                    <div
                                      className="add-farmer__remove-img-btn"
                                      style={{
                                        fontSize: "11px",
                                        backgroundColor: "red",
                                      }}
                                      onClick={() =>
                                        removeActivityFileHandler(
                                          item.file.name
                                        )
                                      }
                                    >
                                      <i className="fa fa-trash"></i>
                                    </div>
                                    <div
                                      style={{
                                        width: "100%",
                                        height: "150px",
                                        display: "flex",
                                        alignItems: "center",
                                      }}
                                    >
                                      <div
                                        style={{
                                          width: "150px",
                                          height: "150px",
                                          marginRight: "1rem",
                                          borderRight: "1px solid silver",
                                        }}
                                      >
                                        {/* Image */}
                                        {item.file.type.split("/")[0] ==
                                          "image" && (
                                          <img
                                            src={URL.createObjectURL(item.file)}
                                            alt=""
                                            className="new-img"
                                          />
                                        )}
                                        {/* Document */}
                                        {item.file.type ==
                                          "application/pdf" && (
                                          <div className="selected-file">
                                            <div className="text-center">
                                              <div>
                                                <i
                                                  className="fa fa-file"
                                                  aria-hidden="true"
                                                ></i>
                                              </div>
                                              <div style={{ fontSize: "12px" }}>
                                                {item.file.name}
                                              </div>
                                            </div>
                                          </div>
                                        )}
                                        {/* Video */}
                                        {item.file.type.split("/")[0] ==
                                          "video" && (
                                          <video className="new-img" controls>
                                            <source
                                              src={URL.createObjectURL(
                                                item.file
                                              )}
                                              type="video/mp4"
                                            />
                                          </video>
                                        )}
                                      </div>
                                      <div>
                                        <small>
                                          <i className="fa fa-file"></i>{" "}
                                          {item.file.type}
                                        </small>
                                        <p
                                          className="mt-2 mb-0"
                                          style={{ fontSize: "14px" }}
                                        >
                                          {item.description}
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </Col>
                              ))}
                            </Row>
                          )}

                          <button
                            className="add-image-btn"
                            onClick={() => handleFileModalShow()}
                          >
                            Add File
                          </button>

                          {activityFiles && (
                            <div>
                              <Row className="mt-3">
                                {activityFiles.map((doc) => (
                                  <Col lg={4} md={6} sm={12}>
                                    {doc.fileType ? (
                                      <>
                                        {/* Image */}
                                        {doc?.fileType?.split("/")[0] ==
                                          "image" && (
                                          <div>
                                            <p>Image</p>
                                            <div
                                              className="disease-img__div"
                                              key={doc.document_id}
                                            >
                                              <div
                                                className="farmer-item__img-div"
                                                style={{
                                                  borderRight:
                                                    "1px solid silver",
                                                  objectFit: "cover",
                                                }}
                                              >
                                                {/* <imgv
                                              src={
                                                BASE_URL +
                                                "/media/" +
                                                doc.document_id
                                              }
                                              className="new-img"
                                              onClick={() => openImageViewer(0)}
                                              alt=""
                                            /> */}
                                                <Image
                                                  style={{
                                                    objectFit: "cover",
                                                  }}
                                                  src={
                                                    BASE_URL +
                                                    "/media/" +
                                                    doc.document_id
                                                  }
                                                />
                                              </div>
                                              <div>
                                                {!doc.document_information
                                                  .description ||
                                                doc.document_information.description.trim() ===
                                                  "" ? (
                                                  <p
                                                    style={{
                                                      color: "silver",
                                                      fontSize: "12px",
                                                    }}
                                                  >
                                                    <i
                                                      className="fa fa-exclamation-triangle"
                                                      aria-hidden="true"
                                                    ></i>{" "}
                                                    No description!
                                                  </p>
                                                ) : (
                                                  <div>
                                                    <small>Description:</small>
                                                    <p>
                                                      {
                                                        doc.document_information
                                                          .description
                                                      }
                                                    </p>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                            {isViewerOpen && (
                                              <ImageViewer
                                                src={[
                                                  BASE_URL +
                                                    "/media/" +
                                                    doc.document_id,
                                                ]}
                                                currentIndex={currentImage}
                                                onClose={closeImageViewer}
                                                backgroundStyle={{
                                                  backgroundColor:
                                                    "rgba(0, 0, 0, 0.9)",
                                                  zIndex: 10,
                                                }}
                                                closeOnClickOutside={true}
                                              />
                                            )}
                                          </div>
                                        )}

                                        {/* PDF */}
                                        {doc?.fileType == "application/pdf" && (
                                          <div>
                                            <p>PDF</p>
                                            <div
                                              className="disease-img__div"
                                              key={doc.document_id}
                                            >
                                              <div
                                                className="farmer-item__img-div"
                                                style={{
                                                  borderRight:
                                                    "1px solid silver",
                                                }}
                                                onClick={() =>
                                                  window.open(
                                                    BASE_URL +
                                                      "/media/" +
                                                      doc.document_id
                                                  )
                                                }
                                              >
                                                {/* <PDFViewer
                                              style={{ height: "100%" }}
                                              document={{
                                                url: `${BASE_URL}/media/${doc.document_id}`,
                                              }}
                                            /> */}
                                              </div>
                                              <div>
                                                {!doc.document_information
                                                  .description ||
                                                doc.document_information.description.trim() ===
                                                  "" ? (
                                                  <p
                                                    style={{
                                                      color: "silver",
                                                      fontSize: "12px",
                                                    }}
                                                  >
                                                    <i
                                                      className="fa fa-exclamation-triangle"
                                                      aria-hidden="true"
                                                    ></i>{" "}
                                                    No description!
                                                  </p>
                                                ) : (
                                                  <div>
                                                    <small>Description:</small>
                                                    <p>
                                                      {
                                                        doc.document_information
                                                          .description
                                                      }
                                                    </p>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                        )}

                                        {/* Video */}
                                        {doc?.fileType?.split("/")[0] ==
                                          "video" && (
                                          <div>
                                            <p>Video</p>
                                            <div
                                              className="disease-img__div"
                                              key={doc.document_id}
                                            >
                                              <div
                                                className="farmer-item__img-div"
                                                style={{
                                                  borderRight:
                                                    "1px solid silver",
                                                }}
                                              >
                                                <video
                                                  className="new-img"
                                                  controls
                                                >
                                                  <source
                                                    src={
                                                      BASE_URL +
                                                      "/media/" +
                                                      doc.document_id
                                                    }
                                                    type="video/mp4"
                                                  />
                                                </video>
                                              </div>
                                              <div>
                                                {!doc.document_information
                                                  .description ||
                                                doc.document_information.description.trim() ===
                                                  "" ? (
                                                  <p
                                                    style={{
                                                      color: "silver",
                                                      fontSize: "12px",
                                                    }}
                                                  >
                                                    <i
                                                      className="fa fa-exclamation-triangle"
                                                      aria-hidden="true"
                                                    ></i>{" "}
                                                    No description!
                                                  </p>
                                                ) : (
                                                  <div>
                                                    <small>Description:</small>
                                                    <p>
                                                      {
                                                        doc.document_information
                                                          .description
                                                      }
                                                    </p>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                        )}
                                      </>
                                    ) : (
                                      <FileNotFound />
                                    )}
                                  </Col>
                                ))}
                              </Row>
                            </div>
                          )}
                          <hr />
                        </>
                      )}

                      {console.log(
                        "Activity date",
                        moment(activityDetails?.activity_date).isBefore(
                          moment()
                        )
                      )}
                      {console.log(
                        "Expiry date",
                        moment(activityDetails?.expiry_date).isAfter(moment())
                      )}

                      {[1000700001, 1000700002].includes(
                        activityDetails?.activity_id
                      ) && (
                        <>
                          {moment(activityDetails?.expiry_date).isBefore(
                            moment()
                          ) && (
                            <>
                              {[1000300050, 1000300051, 1000300052].includes(
                                activityDetails?.calendar_activity_status
                              ) ? (
                                <DisabledCollectionFormWithData
                                  surveyData={
                                    activityDetails?.farmer_survey_data
                                  }
                                  activityFiles={activityDetails?.task_files}
                                />
                              ) : (
                                <DisabledCollectionFormWithoutData
                                  farmerId={activityDetails?.customer_id}
                                  navigate={navigate}
                                  message={`This form has expired on ${moment(
                                    activityDetails?.expiry_date
                                  ).format(
                                    "DD MMM, YYYY"
                                  )}. You cannot edit this form now.`}
                                />
                              )}
                            </>
                          )}

                          {moment(activityDetails?.activity_date).isBefore(
                            moment()
                          ) &&
                            moment(activityDetails?.expiry_date).isAfter(
                              moment()
                            ) && (
                              <>
                                {[1000300050, 1000300051, 1000300052].includes(
                                  activityDetails?.calendar_activity_status
                                ) ? (
                                  <DisabledCollectionFormWithData
                                    surveyData={
                                      activityDetails?.farmer_survey_data
                                    }
                                    activityFiles={activityDetails?.task_files}
                                  />
                                ) : (
                                  <DataCollectionForm
                                    farmerId={activityDetails?.customer_id}
                                    navigate={navigate}
                                    calendarId={
                                      activityDetails?.care_calendar_id
                                    }
                                  />
                                )}
                              </>
                            )}

                          {moment(activityDetails?.activity_date).isAfter(
                            moment()
                          ) && (
                            <DisabledCollectionFormWithoutData
                              farmerId={activityDetails?.customer_id}
                              navigate={navigate}
                              message={`This form will be enabled on ${moment(
                                activityDetails?.activity_date
                              ).format(
                                "DD MMM, YYYY"
                              )}. You cannot edit this form now.`}
                            />
                          )}
                        </>
                      )}

                      {![1000700001, 1000700002].includes(
                        activityDetails?.activity_id
                      ) && (
                        <>
                          {/* Paravet name */}
                          {paravetsList.length > 0 && (
                            <div>
                              <small>Assign task to another Paravet:</small>
                              <div
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  marginTop: "10px",
                                }}
                              >
                                <div>
                                  <Autocomplete
                                    options={paravetsList}
                                    getOptionLabel={(option) =>
                                      option?.staff_name_l10n?.ul
                                        ? option?.staff_name_l10n?.ul
                                        : option?.staff_name_l10n?.en
                                        ? option?.staff_name_l10n?.en
                                        : ""
                                    }
                                    disabled={freezeActivity}
                                    sx={{ width: 300 }}
                                    defaultValue={() => {
                                      for (let option of paravetsList) {
                                        if (
                                          activityDetails?.staff_id ===
                                          option?.staff_id
                                        ) {
                                          return option;
                                        }
                                      }
                                    }}
                                    renderInput={(params) => (
                                      <TextField
                                        {...params}
                                        label="Paravet Name"
                                      />
                                    )}
                                    onChange={chooseParavetHandler}
                                  />
                                </div>
                                {freezeActivity == false && (
                                  <div className="ms-3">
                                    <button
                                      className="assign-btn"
                                      disabled={selectedParavetId === undefined}
                                      onClick={() => reassignTask()}
                                    >
                                      Assign
                                    </button>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          <hr />

                          <Row>
                            <Col lg={4} md={6} sm={12}>
                              <small>Farmer Name:</small>
                              <p style={{ textTransform: "capitalize" }}>
                                {activityDetails.customer_name_l10n
                                  ? extractBasedOnLanguage(
                                      activityDetails.customer_name_l10n,
                                      userLanguage
                                    )
                                  : "-"}
                              </p>
                            </Col>
                            {activityDetails.entity_type_id == 1000460002 && (
                              <Col lg={4} md={6} sm={12}>
                                <small>Cattle Name:</small>
                                <p style={{ textTransform: "capitalize" }}>
                                  {activityDetails.animal_name
                                    ? activityDetails.animal_name
                                    : "-"}
                                </p>
                              </Col>
                            )}
                            {activityDetails.entity_type_id == 1000460002 && (
                              <Col lg={4} md={6} sm={12}>
                                <small>Cattle Ear Tag:</small>
                                <p style={{ textTransform: "capitalize" }}>
                                  {activityDetails.ear_tag}
                                </p>
                              </Col>
                            )}

                            <Col lg={4} md={6} sm={12}>
                              <small>Farmer's Contact:</small>
                              <p style={{ textTransform: "capitalize" }}>
                                {activityDetails.farmer_contact}
                              </p>
                            </Col>
                            <Col lg={4} md={6} sm={12}>
                              <small>Farmer's Village:</small>
                              <p style={{ textTransform: "capitalize" }}>
                                {activityDetails?.village_name_l10n?.ul
                                  ? activityDetails?.village_name_l10n?.ul
                                  : activityDetails?.village_name_l10n?.en
                                  ? activityDetails?.village_name_l10n?.en
                                  : "-"}
                              </p>
                            </Col>
                            <Col lg={4} md={6} sm={12}>
                              <small>Created At:</small>
                              <p style={{ textTransform: "capitalize" }}>
                                {activityDetails?.created_at
                                  ? moment(activityDetails?.created_at).format(
                                      "DD MMM, YYYY h:mm A"
                                    )
                                  : "-"}
                              </p>
                            </Col>
                            <Col lg={4} md={6} sm={12}>
                              <small>Last Updated At:</small>
                              <p style={{ textTransform: "capitalize" }}>
                                {activityDetails?.updated_at
                                  ? moment(activityDetails?.updated_at).format(
                                      "DD MMM, YYYY h:mm A"
                                    )
                                  : "-"}
                              </p>
                            </Col>
                          </Row>

                          <button
                            className="add-task__create-btn text-center"
                            onClick={updateTaskHandler}
                            disabled={
                              activityDetails?.animal_active_status ===
                              1000300002
                                ? true
                                : false
                            }
                          >
                            Update
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                  <div>
                    {/* Notes */}
                    <div className="taskItem__task-notes mb-3">
                      <div className="taskItem__notes-header">Notes</div>
                      <div className="taskItem__notes-container">
                        <div className="taskItem__notes-div" id="notes-div">
                          {taskNotes && (
                            <>
                              {taskNotes.length > 0 ? (
                                <>
                                  {taskNotes.map((note) => (
                                    <div className="taskItem__note-item">
                                      <div style={{ display: "flex" }}>
                                        {note.creator_name && (
                                          <div
                                            style={{
                                              fontWeight: "600",
                                              textTransform: "capitalize",
                                            }}
                                          >
                                            {note.creator_name.ul
                                              ? note.creator_name.ul
                                              : note.creator_name.en}
                                          </div>
                                        )}

                                        <div
                                          style={{
                                            marginLeft: "auto",
                                            color: "gray",
                                          }}
                                        >
                                          {moment(note.note_time).format(
                                            "DD MMM YY, h:mma"
                                          )}
                                        </div>
                                      </div>
                                      {note.note.note ? (
                                        note.note.note
                                      ) : (
                                        <>
                                          {note.note.ul
                                            ? note.note.ul
                                            : note.note.en}
                                        </>
                                      )}
                                    </div>
                                  ))}
                                </>
                              ) : (
                                <div
                                  className="my-5 text-center"
                                  style={{ color: "#4c4c4c", fontSize: "13px" }}
                                >
                                  <i
                                    className="fa fa-exclamation-triangle"
                                    aria-hidden="true"
                                  ></i>{" "}
                                  No notes found!
                                </div>
                              )}
                            </>
                          )}
                        </div>
                        <div className="taskItem__action-div">
                          <div className="taskItem__text-input-div">
                            <textarea
                              type="text"
                              rows="1"
                              placeholder="Type your note here.."
                              value={noteItem}
                              style={{
                                width: "100%",
                                marginBottom: "0",
                                padding: "5px",
                                border: "none",
                                height: "100%",
                              }}
                              className="input-field"
                              onChange={noteInputHandler}
                            />
                          </div>
                          <button
                            className="taskItem__send-btn"
                            disabled={
                              activityDetails?.animal_active_status ===
                              1000300002
                                ? true
                                : false
                            }
                            onClick={sendBtnHandler}
                          >
                            <i
                              className="fa fa-paper-plane"
                              aria-hidden="true"
                            ></i>
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Follow ups */}
                    <div className="taskItem__follow-ups mb-3">
                      <div className="taskItem__follow-ups-header">
                        Case History
                      </div>
                      <div className="taskItem__follow-ups-div">
                        {followUps.length > 0 ? (
                          <>
                            {followUps.map((item) => (
                              <div
                                // className="taskItem__follow-ups-item"
                                className={
                                  activityDetails.care_calendar_id !==
                                  item.care_calendar_id
                                    ? "taskItem__follow-ups-item"
                                    : "taskItem__follow-ups-item-selected"
                                }
                                onClick={() =>
                                  followUpsClickHandler(item.care_calendar_id)
                                }
                              >
                                <p className="m-0 activity-name">
                                  {item.activity_name_json
                                    ? extractBasedOnLanguage(
                                        item.activity_name_json,
                                        userLanguage
                                      )
                                    : "-"}
                                </p>
                                <p className="m-0 activity-status">
                                  {item.activity_status_json
                                    ? extractBasedOnLanguage(
                                        item.activity_status_json,
                                        userLanguage
                                      )
                                    : "-"}

                                  <span
                                    style={{ float: "right", color: "gray" }}
                                  >
                                    {console.log(item)}
                                    {/* {moment(item.activity_date).format('DD MMM YYYY, hh:mm A')} */}
                                    {/* {item.visit_schedule_time ? convertUtcToIst(item.visit_schedule_time) : moment(item.activity_date).format('DD MMM YYYY, hh:mm A')} */}
                                    {item.visit_schedule_time
                                      ? convertUtcToIstFormatted(
                                          item.visit_schedule_time
                                        )
                                      : moment(item.activity_date).format(
                                          "DD MMM YYYY, hh:mm A"
                                        )
                                      ? moment(item.activity_date).format(
                                          "DD MMM YYYY, hh:mm A"
                                        )
                                      : ""}
                                  </span>
                                </p>
                              </div>
                            ))}
                          </>
                        ) : (
                          <div
                            className="my-3 text-center"
                            style={{ color: "gray", fontSize: "14px" }}
                          >
                            <i className="fa fa-exclamation-triangle"></i>
                            &nbsp; No follow ups!
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ) : activityDetails &&
                !Object.keys(activityDetails).length > 0 ? (
                <div className="text-center my-5" style={{ color: "#4c4c4c" }}>
                  <i
                    className="fa fa-exclamation-triangle"
                    aria-hidden="true"
                  ></i>{" "}
                  &nbsp; Activity Not Found!
                </div>
              ) : (
                <div className="text-center my-5">
                  <Loader />
                </div>
              )}

              {/* File Select Modal */}
              <Modal
                show={showFileModal}
                onHide={handleFileModalClose}
                centered
              >
                <Modal.Header closeButton>
                  <Modal.Title>Add File</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                  <div className="mb-3" style={{ fontSize: "13px" }}>
                    <p className="mb-0">Supported formats:</p>
                    <p className="mb-0">
                      <span style={{ fontWeight: "600" }}>Images:</span> &nbsp;
                      ".jpg", ".jpeg", ".png", ".gif", ".blob"
                    </p>
                    <p className="mb-0">
                      <span style={{ fontWeight: "600" }}>Documents:</span>{" "}
                      &nbsp; ".pdf"
                    </p>
                    <p className="mb-0">
                      <span style={{ fontWeight: "600" }}>Videos:</span> &nbsp;
                      ".mp4", ".mov", ".avi", ".wmv", ".webm", ".mkv", ".3gp",
                      ".flv",
                    </p>
                  </div>

                  <small>Choose File</small>
                  <input
                    className="input-field"
                    style={{ width: "100%" }}
                    type="file"
                    onChange={fileUploadHandler}
                  />
                  {file && (
                    <>
                      {/* Image */}
                      {file.type.split("/")[0] == "image" && (
                        <div className="add-farmer__img-div">
                          <div
                            className="add-farmer__remove-img-btn"
                            onClick={() => removeFileHandler()}
                          >
                            <i className="fa fa-times"></i>
                          </div>
                          <img
                            src={URL.createObjectURL(file)}
                            alt=""
                            className="new-img"
                          />
                        </div>
                      )}
                      {/* PDF */}
                      {file.type == "application/pdf" && (
                        <div className="add-farmer__img-div">
                          <div
                            className="add-farmer__remove-img-btn"
                            onClick={() => removeFileHandler()}
                          >
                            <i className="fa fa-times"></i>
                          </div>
                          <div className="selected-file">
                            <div className="text-center">
                              <div>
                                <i
                                  className="fa fa-file"
                                  aria-hidden="true"
                                ></i>
                              </div>
                              <div style={{ fontSize: "12px" }}>
                                {file.name}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      {/* Video */}
                      {file.type.split("/")[0] == "video" && (
                        <div className="add-farmer__img-div">
                          <div
                            className="add-farmer__remove-img-btn"
                            onClick={() => removeFileHandler()}
                          >
                            <i className="fa fa-times"></i>
                          </div>
                          <div className="selected-file">
                            <div className="text-center">
                              <div>
                                <i
                                  className="fa fa-file-video"
                                  aria-hidden="true"
                                ></i>
                              </div>
                              <div style={{ fontSize: "12px" }}>
                                {file.name}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </>
                  )}
                  <small>Description</small>
                  <textarea
                    className="input-field"
                    style={{ width: "100%" }}
                    rows="3"
                    type="text"
                    defaultValue={fileDes}
                    onChange={fileDescriptionChangeHandler}
                  ></textarea>
                </Modal.Body>
                <Modal.Footer>
                  <Button
                    variant="primary"
                    onClick={handleFileModalClose}
                    style={{
                      background: "#ec6237",
                      color: "#fff",
                      border: "none",
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    onClick={saveFileHandler}
                    style={{
                      background: "#673AB7",
                      color: "#fff",
                      border: "none",
                    }}
                  >
                    Save
                  </Button>
                </Modal.Footer>
              </Modal>

              {/* AI Confirmation Popup Modal */}
              <Modal
                show={showConfimationPopupModal}
                onHide={handleConfimationPopupModalClose}
                centered
                backdrop="static"
              >
                <Modal.Header closeButton>
                  <Modal.Title>PD Follow-up generated</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                  A PD Follow-up task is automatically generated for this AI
                  task on{" "}
                  {moment(
                    new URLSearchParams(window.location.search).get("popup")
                  )
                    .add(90, "days")
                    .format("DD MMM, YYYY")}
                  .
                </Modal.Body>
                <Modal.Footer>
                  <Button
                    variant="primary"
                    onClick={handleConfimationPopupModalClose}
                    style={{
                      background: "#673AB7",
                      color: "#fff",
                      border: "none",
                    }}
                  >
                    Okay
                  </Button>
                </Modal.Footer>
              </Modal>
            </>
          )}
        </div>
      )}
      {taskItemTabs[1].isSelected === true &&
        (activityDetails && Object.keys(activityDetails).length > 0 ? (
          <activityDetailsContext.Provider value={activityDetails}>
            <careCalenderDetails.Provider value={medicineConfig}>
              <TaskDetails
                careCalendarId={careCalendarId}
                farmerId={activityDetails?.customer_id}
              />
            </careCalenderDetails.Provider>
          </activityDetailsContext.Provider>
        ) : (
          <p style={{ textAlign: "center" }}>No data...</p>
        ))}

      {taskItemTabs.length > 2 && taskItemTabs[3]?.isSelected && (
        <div>
          {renderActivityForm()}
          {/* {renderPaymentsTab()} */}
          {activityDetails && !isServiceDocAva && (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <GenerateServiceDoc careCalendarId={careCalendarId} />
            </Box>
          )}
        </div>
      )}

      {taskItemTabs.length > 2 &&
        taskItemTabs[2]?.isSelected &&
        shouldShowPaymentsTab() && <div>{renderPaymentsTab()}</div>}

      {/* Preview Modal */}
      <AntdModal
        title={
          <div style={{ height: "30px" }}>
            <img alt="" src={logo} style={{ height: "100%" }} />
          </div>
        }
        open={isModalOpen}
        // onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
      >
        {previewData ? (
          // <Table bordered style={{ fontSize: "14px", fontWeight: "600" }}>
          //   <tbody>
          //     <tr>
          //       <td>Customer Complaint Id</td>
          //       <td>{previewData.ticket}</td>
          //     </tr>
          //     <tr>
          //       <td>Customer Complaint Date</td>
          //       <td>
          //         {moment(previewData.complaint_date_time).format("DD/MM/YYYY")}
          //       </td>
          //     </tr>
          //     <tr>
          //       <td>Time of Complaint</td>
          //       <td>
          //         {moment(previewData.complaint_date_time).format("hh:mm A")}
          //       </td>
          //     </tr>
          //     <tr>
          //       <td>Farmer Name</td>
          //       <td>
          //         {previewData.farmer_name_l10n
          //           ? previewData.farmer_name_l10n.en
          //             ? previewData.farmer_name_l10n.en
          //             : previewData.farmer_name_l10n.ul
          //           : "-"}
          //       </td>
          //     </tr>
          //     <tr>
          //       <td>Farmer Phone No</td>
          //       <td>{previewData.farmer_mobile}</td>
          //     </tr>
          //     <tr>
          //       <td>Farmer ID</td>
          //       <td>{previewData.farmer_visual_id}</td>
          //     </tr>
          //     <tr>
          //       <td>Animal Tag No</td>
          //       <td>{previewData.animal_ear_tag}</td>
          //     </tr>
          //     <tr>
          //       <td>Animal Reference No</td>
          //       <td>{previewData.animal_visual_id}</td>
          //     </tr>
          //     <tr>
          //       <td>Village</td>
          //       <td>
          //         {previewData.farmer_village
          //           ? previewData.farmer_village.en
          //             ? previewData.farmer_village.en
          //             : previewData.farmer_village.ul
          //           : "-"}
          //       </td>
          //     </tr>
          //     <tr>
          //       <td>Dist & Block</td>
          //       <td>
          //         {previewData.district_name_l10n
          //           ? previewData.district_name_l10n.en
          //             ? previewData.district_name_l10n.en
          //             : previewData.district_name_l10n.ul
          //           : "-"}{" "}
          //         -{" "}
          //         {previewData.taluk_name_l10n
          //           ? previewData.taluk_name_l10n.en
          //             ? previewData.taluk_name_l10n.en
          //             : previewData.taluk_name_l10n.ul
          //           : "-"}
          //       </td>
          //     </tr>
          //     <tr>
          //       <td>Dr./LSS</td>
          //       <td>
          //         {previewData.paravet_name_l10n
          //           ? previewData.paravet_name_l10n.en
          //             ? previewData.paravet_name_l10n.en
          //             : previewData.paravet_name_l10n.ul
          //           : "-"}
          //       </td>
          //     </tr>
          //     <tr>
          //       <td>Route Officer</td>
          //       <td>
          //         {previewData.ro_name_l10n
          //           ? previewData.ro_name_l10n.en
          //             ? previewData.ro_name_l10n.en
          //             : previewData.ro_name_l10n.ul
          //           : "-"}
          //       </td>
          //     </tr>
          //     <tr>
          //       <td>Veterinary Officer</td>
          //       <td>
          //         {previewData.vet_name_l10n
          //           ? previewData.vet_name_l10n.en
          //             ? previewData.vet_name_l10n.en
          //             : previewData.vet_name_l10n.ul
          //           : "-"}
          //       </td>
          //     </tr>
          //     <tr>
          //       <td>Activity / Complaint</td>
          //       <td>
          //         {previewData.activity_name_l10n
          //           ? previewData.activity_name_l10n.en
          //             ? previewData.activity_name_l10n.en
          //             : previewData.activity_name_l10n.ul
          //           : "-"}
          //       </td>
          //     </tr>
          //   </tbody>
          // </Table>
          <PreviewModalTable previewData={previewData} />
        ) : (
          <div className="my-2 text-center">Something went wrong!</div>
        )}
      </AntdModal>
    </>
  );
};

const DisabledCollectionFormWithoutData = (props) => {
  const [cattleList, setCattleList] = useState();

  const getCattleList = async () => {
    try {
      // const response = await axios.get(
      //   BASE_URL + "/animal?customer_id=" + props.farmerId,
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "*********",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/animal?customer_id=" + props.farmerId,
        method: "GET",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log("Cattle list by farmer id", response);
      if (response.status === 200) {
        // console.log("Eligible cattle list", response.data.result);
        // setEligibleCattleList(response.data.result);
        if (response.data.data.length > 0) {
          setCattleList(response.data.data);
        }
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        props.navigate("/login");
        window.location.reload();
      }
    }
  };

  useEffect(() => {
    getCattleList();
  }, []);

  return (
    <>
      <div
        className="mb-3 py-2 px-3"
        style={{
          backgroundColor: "#EE4B2B",
          color: "white",
          borderRadius: "3px",
        }}
      >
        {props.message}
      </div>
      <small>Cattle Section</small>
      {cattleList && (
        <Table className="my-3" striped bordered>
          <thead>
            <tr>
              <th className="text-center">#</th>
              <th>Cattle Id</th>
              <th>Field name</th>
              <th>Value</th>
            </tr>
          </thead>
          <tbody>
            {cattleList.map((item) => (
              <tr key={item.animal_id}>
                <td className="text-center"></td>
                <td style={{ fontSize: "14px" }}>
                  <p className="m-0" style={{ fontWeight: "600" }}>
                    <i className="fa fa-tag"></i> {item.ear_tag_1}
                  </p>
                  <p className="m-0">{item.animal_visual_id}</p>
                </td>
                <td>Max LPD</td>
                <td>
                  <input
                    className="input-field"
                    type="text"
                    disabled
                    placeholder="Enter value here..."
                    style={{ cursor: "not-allowed" }}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      )}
      <small>Farmer Section</small>
      <Table className="my-3" striped bordered>
        <thead>
          <tr>
            <th className="text-center">#</th>
            <th>Field name</th>
            <th className="text-center">Value</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td className="text-center"></td>
            <td>Today’s total milk yield of your cows</td>
            <td className="text-center">
              <input
                className="input-field"
                type="text"
                disabled
                placeholder="Enter value here..."
                style={{ cursor: "not-allowed" }}
              />
            </td>
          </tr>
          <tr>
            <td className="text-center"></td>
            <td>Owned Farm Land area in acre</td>
            <td className="text-center">
              <input
                className="input-field"
                type="text"
                disabled
                placeholder="Enter value here..."
                style={{ cursor: "not-allowed" }}
              />
            </td>
          </tr>
          <tr>
            <td className="text-center"></td>
            <td>Fodder Crops in Your Field</td>
            <td className="text-center">
              <input
                className="input-field"
                type="text"
                disabled
                placeholder="Enter value here..."
                style={{ cursor: "not-allowed" }}
              />
            </td>
          </tr>
          <tr>
            <td className="text-center"></td>
            <td>Fodder Crops Stage in Your Field</td>
            <td className="text-center">
              <input
                className="input-field"
                type="text"
                disabled
                placeholder="Enter value here..."
                style={{ cursor: "not-allowed" }}
              />
            </td>
          </tr>
          <tr>
            <td className="text-center"></td>
            <td>Did you collect Cow dung/ Manure </td>
            <td className="text-center">
              <input
                className="input-field"
                type="text"
                disabled
                placeholder="Enter value here..."
                style={{ cursor: "not-allowed" }}
              />
            </td>
          </tr>
          <tr>
            <td className="text-center"></td>
            <td>Do You need to buy Cow dung/ Manure</td>
            <td className="text-center">
              <input
                className="input-field"
                type="text"
                disabled
                placeholder="Enter value here..."
                style={{ cursor: "not-allowed" }}
              />
            </td>
          </tr>
          <tr>
            <td className="text-center"></td>
            <td>Did you sell cow dung / manure?</td>
            <td className="text-center">
              <input
                className="input-field"
                type="text"
                disabled
                placeholder="Enter value here..."
                style={{ cursor: "not-allowed" }}
              />
            </td>
          </tr>
          <tr>
            <td className="text-center"></td>
            <td>How much cow dung / manure sold (in tonnes)</td>
            <td className="text-center">
              <input
                className="input-field"
                type="text"
                disabled
                placeholder="Enter value here..."
                style={{ cursor: "not-allowed" }}
              />
            </td>
          </tr>
          <tr>
            <td className="text-center"></td>
            <td>How much price you sold in cow dung /manure</td>
            <td className="text-center">
              <input
                className="input-field"
                type="text"
                disabled
                placeholder="Enter value here..."
                style={{ cursor: "not-allowed" }}
              />
            </td>
          </tr>
        </tbody>
      </Table>
      <hr />
    </>
  );
};

const DisabledCollectionFormWithData = (props) => {
  const [currentImage, setCurrentImage] = useState(0);
  const [isViewerOpen, setIsViewerOpen] = useState(false);

  const openImageViewer = useCallback((index) => {
    setCurrentImage(index);
    setIsViewerOpen(true);
  }, []);

  const closeImageViewer = () => {
    setCurrentImage(0);
    setIsViewerOpen(false);
  };

  return (
    <>
      {props.surveyData.animal_data && (
        <>
          {props.surveyData.animal_data.length > 0 && (
            <>
              <small>Cattle Section</small>
              <Table className="my-3" striped bordered>
                <thead>
                  <tr>
                    <th className="text-center">#</th>
                    <th>Cattle Id</th>
                    <th>Field name</th>
                    <th>Value</th>
                  </tr>
                </thead>
                <tbody>
                  {props.surveyData.animal_data.map((item) => (
                    <tr key={item.animal_id}>
                      <td className="text-center"></td>
                      <td>
                        {item.animal_visual_id ? (
                          <>
                            <div style={{ fontSize: "14px" }}>
                              <p className="m-0" style={{ fontWeight: "600" }}>
                                <i className="fa fa-tag"></i> {item.ear_tag_1}
                              </p>
                              <p className="m-0">{item.animal_visual_id}</p>
                            </div>
                          </>
                        ) : (
                          item.animal_id
                        )}
                      </td>
                      <td>{item.animal_fields[0].statement}</td>
                      <td>{item.animal_fields[0].value}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </>
          )}
        </>
      )}
      {props.surveyData.farmer_data && (
        <>
          {props.surveyData.farmer_data.farmer_fields &&
            props.surveyData.farmer_data.farmer_fields.length > 0 && (
              <>
                <small>Farmer Section</small>
                <Table className="my-3" striped bordered>
                  <thead>
                    <tr>
                      <th className="text-center">#</th>
                      <th>Field name</th>
                      <th>Value</th>
                    </tr>
                  </thead>
                  <tbody>
                    {props.surveyData.farmer_data.farmer_fields.map((item) => (
                      <tr key={item.statement}>
                        <td className="text-center"></td>
                        <td>{item.statement}</td>
                        <td>{item.value}</td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </>
            )}
        </>
      )}

      {props.activityFiles.length > 0 && (
        <div>
          <Row className="mt-3">
            {props.activityFiles.map((doc) => (
              <Col lg={4} md={6} sm={12}>
                {doc.fileType ? (
                  <>
                    {/* Image */}
                    {doc?.fileType?.split("/")[0] == "image" && (
                      <div>
                        <p>Image</p>
                        <div className="disease-img__div" key={doc.document_id}>
                          <div
                            className="farmer-item__img-div"
                            style={{
                              borderRight: "1px solid silver",
                            }}
                          >
                            <img
                              src={BASE_URL + "/media/" + doc.document_id}
                              className="new-img"
                              onClick={() => openImageViewer(0)}
                              alt=""
                            />
                          </div>
                          <div>
                            {!doc.document_information.description ||
                            doc.document_information.description.trim() ===
                              "" ? (
                              <p
                                style={{
                                  color: "silver",
                                  fontSize: "12px",
                                }}
                              >
                                <i
                                  className="fa fa-exclamation-triangle"
                                  aria-hidden="true"
                                ></i>{" "}
                                No description!
                              </p>
                            ) : (
                              <div>
                                <small>Description:</small>
                                <p>{doc.document_information.description}</p>
                              </div>
                            )}
                          </div>
                        </div>
                        {isViewerOpen && (
                          <ImageViewer
                            src={[BASE_URL + "/media/" + doc.document_id]}
                            currentIndex={currentImage}
                            onClose={closeImageViewer}
                            backgroundStyle={{
                              backgroundColor: "rgba(0, 0, 0, 0.9)",
                              zIndex: 10,
                            }}
                            closeOnClickOutside={true}
                          />
                        )}
                      </div>
                    )}

                    {/* PDF */}
                    {doc?.fileType == "application/pdf" && (
                      <div>
                        <p>PDF</p>
                        <div className="disease-img__div" key={doc.document_id}>
                          <div
                            className="farmer-item__img-div"
                            style={{
                              borderRight: "1px solid silver",
                            }}
                            onClick={() =>
                              window.open(
                                BASE_URL + "/media/" + doc.document_id
                              )
                            }
                          >
                            {/* <PDFViewer
                              style={{ height: "100%" }}
                              document={{
                                url: `${BASE_URL}/media/${doc.document_id}`,
                              }}
                            /> */}
                          </div>
                          <div>
                            {!doc.document_information.description ||
                            doc.document_information.description.trim() ===
                              "" ? (
                              <p
                                style={{
                                  color: "silver",
                                  fontSize: "12px",
                                }}
                              >
                                <i
                                  className="fa fa-exclamation-triangle"
                                  aria-hidden="true"
                                ></i>{" "}
                                No description!
                              </p>
                            ) : (
                              <div>
                                <small>Description:</small>
                                <p>{doc.document_information.description}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Video */}
                    {doc?.fileType?.split("/")[0] == "video" && (
                      <div>
                        <p>Video</p>
                        <div className="disease-img__div" key={doc.document_id}>
                          <div
                            className="farmer-item__img-div"
                            style={{
                              borderRight: "1px solid silver",
                            }}
                          >
                            <video className="new-img" controls>
                              <source
                                src={BASE_URL + "/media/" + doc.document_id}
                                type="video/mp4"
                              />
                            </video>
                          </div>
                          <div>
                            {!doc.document_information.description ||
                            doc.document_information.description.trim() ===
                              "" ? (
                              <p
                                style={{
                                  color: "silver",
                                  fontSize: "12px",
                                }}
                              >
                                <i
                                  className="fa fa-exclamation-triangle"
                                  aria-hidden="true"
                                ></i>{" "}
                                No description!
                              </p>
                            ) : (
                              <div>
                                <small>Description:</small>
                                <p>{doc.document_information.description}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <FileNotFound />
                )}
              </Col>
            ))}
          </Row>
        </div>
      )}

      <hr />
    </>
  );
};

const DataCollectionForm = (props) => {
  const params = useParams();

  // let [categoriesAcc, setCategoriesAcc] = useState({
  //   farmerLevel: true,
  //   cattleLevel: true,
  // });
  const [cattleList, setCattleList] = useState([]);
  const [createTaskReqBody, setCreateTaskReqBody] = useState({
    cattleData: [],
  });

  const [documentEntries, setDocumentEntries] = useState([]);
  let [newActivityFiles, setNewActivityFiles] = useState([]);
  let [file, setFile] = useState();
  let [fileDes, setFileDes] = useState("");
  const [showFileModal, setShowFileModal] = useState(false);
  const handleFileModalClose = () => setShowFileModal(false);
  const handleFileModalShow = () => setShowFileModal(true);

  const getCattleList = useCallback(async () => {
    try {
      // const response = await getCattle({
      //   token: localStorage.getItem("accessToken"),
      //   refreshToken: localStorage.getItem("refreshToken"),
      //   "Content-Type": "application/json",
      // });
      // console.log(response);
      // if (response.data?.newToken) {
      //   console.log("new response token", response);
      //   localStorage.removeItem("accessToken");
      //   localStorage.setItem("accessToken", response.data.newToken);
      // }
      // const response = await axios.get(
      //   BASE_URL + "/animal?customer_id=" + props.farmerId,
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "*********",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/animal?customer_id=" + props.farmerId,
        method: "GET",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log("Cattle list by farmer id", response);
      if (response.status === 200) {
        console.log("Cattle list", response.data.data);
        setCattleList(response.data.data);

        if (response.data.data.length > 0) {
          for (let cattle of response.data.data) {
            createTaskReqBody.cattleData.push({
              cattleId: cattle.animal_id,
              visualId: cattle.animal_visual_id,
              earTag: cattle.ear_tag_1,
              maxLPD: "",
              // avLPD: "",
            });
          }
          console.log(createTaskReqBody);
        }
      }
    } catch (error) {
      console.log(error);
    }
  }, []);

  const updateDataCollectionActivity = async (reqBody) => {
    try {
      // const response = await axios.put(
      //   BASE_URL + "/activity/update-activity",
      //   { ...reqBody },
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "*********",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/activity/update-activity",
        method: "PUT",
        data: { ...reqBody },
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      console.log(response);
      if (response.status === 200) {
        ToastersService.successToast("Activity updated successfully!");
        // Images, PDFs and Videos start
        if (newActivityFiles.length > 0) {
          let files = [];
          // Resizing Images start
          for (let file of newActivityFiles) {
            if (file.file.type.split("/")[0] == "image") {
              file.file = await resizeFile(file.file);
            }
          }
          // Resizing Images end

          files.push({
            fieldName: "task_activity_photo_1",
            images: [...newActivityFiles],
          });
          console.log(files);
          await configureFiles(response.data.id, files);
        }
        // Images, PDFs and Videos end
        props.navigate(-1);
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        props.navigate("/login");
        window.location.reload();
      }
    }
  };

  // const createFarmerTask = async (reqBody) => {
  //   try {
  //     // const response = await getCreateFarmerTask(
  //     //   {
  //     //     token: localStorage.getItem("accessToken"),
  //     //     refreshToken: localStorage.getItem("refreshToken"),
  //     //   },
  //     //   { ...reqBody }
  //     // );
  //     // console.log("Create farmer task res", response);
  //     const response = await axios.post(
  //       BASE_URL + "/activity/create-task-by-farmer",
  //       { ...reqBody },
  //       {
  //         headers: {
  //           token: localStorage.getItem("accessToken"),
  //         },
  //       }
  //     );
  //     console.log("Cattle list by farmer id", response);
  //     if (response.status === 201) {
  //       ToastersService.successToast("Task created successfully!");

  //       // Images, PDFs and Videos start
  //       if (newActivityFiles.length > 0) {
  //         let files = [];
  //         // Resizing Images start
  //         for (let file of newActivityFiles) {
  //           if (file.file.type.split("/")[0] == "image") {
  //             file.file = await resizeFile(file.file);
  //           }
  //         }
  //         // Resizing Images end

  //         files.push({
  //           fieldName: "task_activity_photo_1",
  //           images: [...newActivityFiles],
  //         });
  //         console.log(files);
  //         await configureFiles(response.data.id, files);
  //       }
  //       // Images, PDFs and Videos end

  //       props.navigate(-1);
  //     }
  //   } catch (error) {
  //     console.log(error);
  //   }
  // };

  const configureFiles = async (calendarId, files) => {
    console.log("Post images", calendarId, files);
    setNewActivityFiles([]);
    let count = 0;
    for (let file of files) {
      for (let item of file.images) {
        count = count + 1;
        // filesCount = filesCount + 1;
        console.log("Files count", count);
      }
    }

    for (let file of files) {
      for (let item of file.images) {
        const formData = new FormData();
        formData.append("file", item.file);
        formData.append("entity", "task");
        await uploadImageToServer(
          formData,
          file.fieldName,
          calendarId,
          item.description,
          count
        );
      }
    }
  };

  const uploadImageToServer = useCallback(
    async (formData, documentType, calendarId, imgDes, filesCount) => {
      try {
        const response = await instance({
          url: "/document",
          method: "POST",
          data: formData,
          headers: {
            token: localStorage.getItem("accessToken"),
          },
        });
        console.log("File upload response", response);

        if (response.status === 201) {
          ToastersService.successToast("File uploaded to server!");
          documentEntries.push({
            document_type: documentType,
            entity: "task",
            entity_1_id: props.calendarId,
            url: response.data.data.doc_key,
            document_information: imgDes,
          });
          setDocumentEntries([...documentEntries]);
          console.log(documentEntries.length, filesCount);
          if (documentEntries.length === filesCount) {
            await postImages(documentEntries, calendarId);
          }
        } else {
          throw Error("Image not uploaded to server!");
        }
      } catch (error) {
        console.log(error);
        // ToastersService.failureToast(error);
        if (error.response.status === 401) {
          console.log("token expired");
          localStorage.removeItem("accessToken");
          localStorage.removeItem("resetToken");
          ToastersService.failureToast("Session Expired!");
          props.navigate("/login");
          window.location.reload();
        }
        if (error.response.status === 413) {
          ToastersService.failureToast(
            error.response.data.error
              ? error.response.data.error
              : "File size limit exceeded!"
          );
        }
      }
    },
    [documentEntries]
  );

  const postImages = useCallback(
    async (documentEntries) => {
      try {
        // const response = await uploadCattle_FarmerImageToServer(
        //   {
        //     token: localStorage.getItem("accessToken"),
        //     refreshToken: localStorage.getItem("refreshToken"),
        //   },
        //   { document_entries: documentEntries }
        // );
        // console.log("Images to db", response);
        // const response = await axios.post(
        //   BASE_URL + "/document",
        //   { document_entries: documentEntries },
        //   {
        //     headers: {
        //       token: localStorage.getItem("accessToken"),
        //       "X-App-Id": "*********",
        //     },
        //   }
        // );
        const response = await instance({
          url: "/document",
          method: "POST",
          data: { document_entries: documentEntries },
          headers: {
            "Content-Type": "application/json",
            token: localStorage.getItem("accessToken"),
          },
        });
        console.log("Images to db", response);
        if (response.status === 201) {
          ToastersService.successToast("File saved to db!");
          // getActivityDetails();
        } else {
          throw Error("Images not uploaded to db!");
        }
      } catch (error) {
        console.log(error);
      }
    },
    [documentEntries]
  );

  useEffect(() => {
    getCattleList();
  }, [getCattleList]);

  // const showAccordionHandler = (category) => {
  //   if (category == "farmer") {
  //     categoriesAcc = {
  //       ...categoriesAcc,
  //       farmerLevel: !categoriesAcc.farmerLevel,
  //     };
  //     setCategoriesAcc(categoriesAcc);
  //   } else if (category == "cattle") {
  //     categoriesAcc = {
  //       ...categoriesAcc,
  //       cattleLevel: !categoriesAcc.cattleLevel,
  //     };
  //     setCategoriesAcc(categoriesAcc);
  //   }
  // };

  const createBtnHandler = () => {
    let err = false;
    for (let cattle of createTaskReqBody.cattleData) {
      if (
        cattle.maxLPD == ""
        // || cattle.avLPD == ""
      ) {
        err = true;
        break;
      }
    }

    if (
      !createTaskReqBody.totalAmountOfMilk ||
      createTaskReqBody.totalAmountOfMilk == "" ||
      !createTaskReqBody.farmPatchSize ||
      createTaskReqBody.farmPatchSize == "" ||
      !createTaskReqBody.cropInput ||
      createTaskReqBody.cropInput == "" ||
      !createTaskReqBody.stageInput ||
      createTaskReqBody.stageInput == "" ||
      !createTaskReqBody.manureCollected ||
      createTaskReqBody.manureCollected == "" ||
      !createTaskReqBody.intentionToBuy ||
      createTaskReqBody.intentionToBuy == "" ||
      !createTaskReqBody.didYouSellManure ||
      createTaskReqBody.didYouSellManure == "" ||
      !createTaskReqBody.howMuchDidYouSell ||
      createTaskReqBody.howMuchDidYouSell == "" ||
      !createTaskReqBody.priceInput ||
      createTaskReqBody.priceInput == ""
    ) {
      ToastersService.failureToast("All fields are mandatory!");
    } else if (err == true) {
      ToastersService.failureToast("All fields are mandatory!");
    } else {
      console.log("createTaskReqBody", createTaskReqBody);
      configureReqBody(createTaskReqBody);
      // ToastersService.successToast("Form submitted successfully!");
    }
  };

  const configureReqBody = (createTaskReqBody) => {
    const reqBody = {
      care_calendar: props.calendarId,
      farmer_survey_data: {
        farmer_data: {
          farmer_id: props.farmerId,
          farmer_fields: [],
        },
        animal_data: [],
      },
      calendar_activity_status: 1000300050,
      completion_date: moment().format("YYYY-MM-DD"),
    };

    for (let cattle of createTaskReqBody.cattleData) {
      reqBody.farmer_survey_data.animal_data.push({
        animal_id: cattle.cattleId,
        animal_visual_id: cattle.visualId,
        ear_tag_1: cattle.earTag,
        animal_fields: [
          {
            classifier_id: 2000000103,
            statement: "Max LPD",
            value: cattle.maxLPD,
          },
          // {
          //   classifier_id: 2000000044,
          //   statement: "Avarage LPD",
          //   value: cattle.avLPD,
          // },
        ],
      });
    }

    for (let item of Object.keys(createTaskReqBody)) {
      console.log(item);
      if (item == "totalAmountOfMilk") {
        reqBody.farmer_survey_data.farmer_data.farmer_fields.push({
          classifier_id: "",
          statement: "Today’s total milk yield of your cows",
          value: createTaskReqBody[item],
        });
      } else if (item == "farmPatchSize") {
        reqBody.farmer_survey_data.farmer_data.farmer_fields.push({
          classifier_id: "",
          statement: "Owned Farm Land area in acre",
          value: createTaskReqBody[item],
        });
      } else if (item == "cropInput") {
        reqBody.farmer_survey_data.farmer_data.farmer_fields.push({
          classifier_id: "",
          statement: "Fodder Crops in Your Field",
          value: createTaskReqBody[item],
        });
      } else if (item == "stageInput") {
        reqBody.farmer_survey_data.farmer_data.farmer_fields.push({
          classifier_id: "",
          statement: "Fodder Crops Stage in Your Field",
          value: createTaskReqBody[item],
        });
      } else if (item == "manureCollected") {
        reqBody.farmer_survey_data.farmer_data.farmer_fields.push({
          classifier_id: "",
          statement: "Did you collect Cow dung/ Manure",
          value: createTaskReqBody[item],
        });
      } else if (item == "intentionToBuy") {
        reqBody.farmer_survey_data.farmer_data.farmer_fields.push({
          classifier_id: "",
          statement: "Do You need to buy Cow dung/ Manure",
          value: createTaskReqBody[item],
        });
      } else if (item == "didYouSellManure") {
        reqBody.farmer_survey_data.farmer_data.farmer_fields.push({
          classifier_id: "",
          statement: "Did you sell cow dung / manure?",
          value: createTaskReqBody[item],
        });
      } else if (item == "howMuchDidYouSell") {
        reqBody.farmer_survey_data.farmer_data.farmer_fields.push({
          classifier_id: "",
          statement: "How much cow dung / manure sold (in tonnes)",
          value: createTaskReqBody[item],
        });
      } else if (item == "priceInput") {
        reqBody.farmer_survey_data.farmer_data.farmer_fields.push({
          classifier_id: "",
          statement: "How much price you sold in cow dung /manure",
          value: createTaskReqBody[item],
        });
      }
    }

    console.log("req body", reqBody);
    // createFarmerTask(reqBody);
    updateDataCollectionActivity(reqBody);
  };

  const totalAmountOfMilkHandler = (event) => {
    console.log(event.target.value);
    const item = {
      ...createTaskReqBody,
      totalAmountOfMilk: event.target.value,
    };
    setCreateTaskReqBody(item);
  };

  const farmPatchSizeHandler = (event) => {
    console.log(event.target.value);
    const item = { ...createTaskReqBody, farmPatchSize: event.target.value };
    setCreateTaskReqBody(item);
  };

  const cropInputHandler = (event) => {
    console.log(event.target.value);
    const item = { ...createTaskReqBody, cropInput: event.target.value };
    setCreateTaskReqBody(item);
  };

  const stageInputHandler = (event) => {
    console.log(event.target.value);
    const item = { ...createTaskReqBody, stageInput: event.target.value };
    setCreateTaskReqBody(item);
  };

  const manureCollectedHandler = (event) => {
    console.log(event.target.value);
    const item = { ...createTaskReqBody, manureCollected: event.target.value };
    setCreateTaskReqBody(item);
  };

  const intentionToBuyHandler = (event) => {
    console.log(event.target.value);
    const item = { ...createTaskReqBody, intentionToBuy: event.target.value };
    setCreateTaskReqBody(item);
  };

  const didYouSellManureHandler = (event) => {
    console.log(event.target.value);
    const item = { ...createTaskReqBody, didYouSellManure: event.target.value };
    setCreateTaskReqBody(item);
  };

  const howMuchDidYouSellHandler = (event) => {
    console.log(event.target.value);
    const item = {
      ...createTaskReqBody,
      howMuchDidYouSell: event.target.value,
    };
    setCreateTaskReqBody(item);
  };

  const priceInputHandler = (event) => {
    console.log(event.target.value);
    const item = { ...createTaskReqBody, priceInput: event.target.value };
    setCreateTaskReqBody(item);
  };

  const maxLDPHandler = (event, animalId) => {
    console.log(animalId, event.target.value);
    for (let cattle of createTaskReqBody.cattleData) {
      if (cattle.cattleId == animalId) {
        cattle.maxLPD = event.target.value;
      }
    }
    setCreateTaskReqBody(createTaskReqBody);
  };

  // const avLDPHandler = (event, animalId) => {
  //   console.log(animalId, event.target.value);
  //   for (let cattle of createTaskReqBody.cattleData) {
  //     if (cattle.cattleId == animalId) {
  //       cattle.avLPD = event.target.value;
  //     }
  //   }
  //   setCreateTaskReqBody(createTaskReqBody);
  // };

  const fileUploadHandler = async (event) => {
    console.log(event.target.files[0]);
    if (
      event.target.files[0].type.split("/")[0] == "image" ||
      event.target.files[0].type == "application/pdf" ||
      event.target.files[0].type.split("/")[0] == "video"
    ) {
      setFile(event.target.files[0]);
    } else {
      ToastersService.failureToast("Invalid file type!");
    }
  };

  const removeFileHandler = () => {
    file = undefined;
    setFile(file);
  };

  const fileDescriptionChangeHandler = (event) => {
    console.log(event.target.value);
    setFileDes(event.target.value);
  };

  const saveFileHandler = () => {
    console.log(file, fileDes);
    activityFileUploadHandler(file, fileDes);
    setFile(undefined);
    setFileDes("");
    handleFileModalClose();
  };

  const activityFileUploadHandler = (file, description) => {
    const fileItem = {
      file: file,
      description: description,
    };
    console.log("File item", fileItem);
    setNewActivityFiles([...newActivityFiles, fileItem]);
  };

  const removeActivityFileHandler = (fileName) => {
    console.log(fileName);
    for (let item of newActivityFiles) {
      if (item.file.name === fileName) {
        newActivityFiles = newActivityFiles.filter(
          (i) => i.file.name !== fileName
        );
      }
    }
    setNewActivityFiles([...newActivityFiles]);
  };

  return (
    <>
      <small style={{ fontWeight: 600 }}>Cattle section</small>
      {cattleList.length > 0 ? (
        <Table striped bordered className="mt-3">
          <thead>
            <tr
              className="text-center"
              style={{ backgroundColor: "#4c4c4c", color: "white" }}
            >
              <th>Cattle</th>
              <th>
                Max LPD<span style={{ backgroundColor: "red" }}>*</span>
              </th>
            </tr>
          </thead>
          <tbody>
            {cattleList.map((cattle) => (
              <tr key={cattle.animal_id}>
                <td style={{ fontSize: "14px" }}>
                  <p className="m-0" style={{ fontWeight: "600" }}>
                    <i className="fa fa-tag"></i> {cattle.ear_tag_1}
                  </p>
                  <p className="m-0">{cattle.animal_visual_id}</p>
                </td>
                <td className="text-center">
                  <input
                    type="number"
                    className="input-field"
                    placeholder="Enter here..."
                    onWheel={(e) => e.target.blur()}
                    onChange={(event) => maxLDPHandler(event, cattle.animal_id)}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      ) : (
        <div
          className="text-center"
          style={{ fontSize: "14px", color: "gray" }}
        >
          <i className="fa fa-exclamation-triangle"></i>
          No cattle available
        </div>
      )}

      <small style={{ fontWeight: 600 }}>Farmer section</small>
      <Table striped bordered className="mt-3">
        <thead>
          <tr
            className="text-center"
            style={{ backgroundColor: "#4c4c4c", color: "white" }}
          >
            <th>Field</th>
            <th>Value</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style={{ fontSize: "14px" }}>
              Today’s total milk yield of your cows
              <span style={{ backgroundColor: "red" }}>*</span>
            </td>
            <td className="text-center">
              <input
                type="number"
                onWheel={(e) => e.target.blur()}
                className="input-field"
                placeholder="Enter here..."
                onChange={totalAmountOfMilkHandler}
              />
            </td>
          </tr>
          <tr>
            <td style={{ fontSize: "14px" }}>
              Owned Farm Land area in acre
              <span style={{ backgroundColor: "red" }}>*</span>
            </td>
            <td className="text-center">
              <input
                type="number"
                onWheel={(e) => e.target.blur()}
                className="input-field"
                placeholder="Enter here..."
                onChange={farmPatchSizeHandler}
              />
            </td>
          </tr>
          <tr>
            <td style={{ fontSize: "14px" }}>
              Fodder Crops in Your Field{" "}
              <span style={{ backgroundColor: "red" }}>*</span>
            </td>
            <td className="text-center">
              <input
                type="text"
                className="input-field"
                placeholder="Enter here..."
                onChange={cropInputHandler}
              />
            </td>
          </tr>
          <tr>
            <td style={{ fontSize: "14px" }}>
              Fodder Crops Stage in Your Field{" "}
              <span style={{ backgroundColor: "red" }}>*</span>
            </td>
            <td className="text-center">
              <input
                type="text"
                className="input-field"
                placeholder="Enter here..."
                onChange={stageInputHandler}
              />
            </td>
          </tr>
          <tr>
            <td style={{ fontSize: "14px" }}>
              Did you collect Cow dung/ Manure
              <span style={{ backgroundColor: "red" }}>*</span>
            </td>
            <td className="text-center">
              <input
                type="text"
                className="input-field"
                placeholder="Enter here..."
                onChange={manureCollectedHandler}
              />
            </td>
          </tr>
          <tr>
            <td style={{ fontSize: "14px" }}>
              Do You need to buy Cow dung/ Manure
              <span style={{ backgroundColor: "red" }}>*</span>
            </td>
            <td className="text-center">
              <input
                type="text"
                className="input-field"
                placeholder="Enter here..."
                onChange={intentionToBuyHandler}
              />
            </td>
          </tr>
          <tr>
            <td style={{ fontSize: "14px" }}>
              Did you sell cow dung / manure?
              <span style={{ backgroundColor: "red" }}>*</span>
            </td>
            <td className="text-center">
              <input
                type="text"
                className="input-field"
                placeholder="Enter here..."
                onChange={didYouSellManureHandler}
              />
            </td>
          </tr>
          <tr>
            <td style={{ fontSize: "14px" }}>
              How much did you sell?
              <span style={{ backgroundColor: "red" }}>*</span>
            </td>
            <td className="text-center">
              <input
                type="number"
                className="input-field"
                onWheel={(e) => e.target.blur()}
                placeholder="Enter here..."
                onChange={howMuchDidYouSellHandler}
              />
            </td>
          </tr>
          <tr>
            <td style={{ fontSize: "14px" }}>
              How much price you sold in cow dung /manure{" "}
              <span style={{ backgroundColor: "red" }}>*</span>
            </td>
            <td className="text-center">
              <input
                type="number"
                className="input-field"
                onWheel={(e) => e.target.blur()}
                placeholder="Enter here..."
                onChange={priceInputHandler}
              />
            </td>
          </tr>
        </tbody>
      </Table>

      <small className="mb-2">Scan of statement</small>
      <br />

      {newActivityFiles.length > 0 && (
        <Row className="my-3">
          {newActivityFiles.map((item) => (
            <Col lg={4} md={6} sm={6} xs={12} key={item.file.lastModified}>
              <div className="add-task__img-div">
                <div
                  className="add-farmer__remove-img-btn"
                  style={{
                    fontSize: "11px",
                    backgroundColor: "red",
                  }}
                  onClick={() => removeActivityFileHandler(item.file.name)}
                >
                  <i className="fa fa-trash"></i>
                </div>
                <div
                  style={{
                    width: "100%",
                    height: "150px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <div
                    style={{
                      width: "150px",
                      height: "150px",
                      marginRight: "1rem",
                      borderRight: "1px solid silver",
                    }}
                  >
                    {/* Image */}
                    {item.file.type.split("/")[0] == "image" && (
                      <img
                        src={URL.createObjectURL(item.file)}
                        alt=""
                        className="new-img"
                      />
                    )}
                    {/* Document */}
                    {item.file.type == "application/pdf" && (
                      <div className="selected-file">
                        <div className="text-center">
                          <div>
                            <i className="fa fa-file" aria-hidden="true"></i>
                          </div>
                          <div style={{ fontSize: "12px" }}>
                            {item.file.name}
                          </div>
                        </div>
                      </div>
                    )}
                    {/* Video */}
                    {item.file.type.split("/")[0] == "video" && (
                      <video className="new-img" controls>
                        <source
                          src={URL.createObjectURL(item.file)}
                          type="video/mp4"
                        />
                      </video>
                    )}
                  </div>
                  <div>
                    <small>
                      <i className="fa fa-file"></i> {item.file.type}
                    </small>
                    <p className="mt-2 mb-0" style={{ fontSize: "14px" }}>
                      {item.description}
                    </p>
                  </div>
                </div>
              </div>
            </Col>
          ))}
        </Row>
      )}

      <button className="add-image-btn" onClick={() => handleFileModalShow()}>
        Add File
      </button>

      <div
        className="add-task__create-btn text-center"
        onClick={createBtnHandler}
      >
        Update
      </div>

      {/* File Select Modal */}
      <Modal show={showFileModal} onHide={handleFileModalClose} centered>
        <Modal.Header closeButton>
          <Modal.Title>Add File</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-3" style={{ fontSize: "13px" }}>
            <p className="mb-0">Supported formats:</p>
            <p className="mb-0">
              <span style={{ fontWeight: "600" }}>Images:</span> &nbsp; ".jpg",
              ".jpeg", ".png", ".gif", ".blob"
            </p>
            <p className="mb-0">
              <span style={{ fontWeight: "600" }}>Documents:</span> &nbsp;
              ".pdf"
            </p>
            <p className="mb-0">
              <span style={{ fontWeight: "600" }}>Videos:</span> &nbsp; ".mp4",
              ".mov", ".avi", ".wmv", ".webm", ".mkv", ".3gp", ".flv",
            </p>
          </div>

          <small>Choose File</small>
          <input
            className="input-field"
            style={{ width: "100%" }}
            type="file"
            onChange={fileUploadHandler}
          />
          {file && (
            <>
              {/* Image */}
              {file.type.split("/")[0] == "image" && (
                <div className="add-farmer__img-div">
                  <div
                    className="add-farmer__remove-img-btn"
                    onClick={() => removeFileHandler()}
                  >
                    <i className="fa fa-times"></i>
                  </div>
                  <img
                    src={URL.createObjectURL(file)}
                    alt=""
                    className="new-img"
                  />
                </div>
              )}
              {/* PDF */}
              {file.type == "application/pdf" && (
                <div className="add-farmer__img-div">
                  <div
                    className="add-farmer__remove-img-btn"
                    onClick={() => removeFileHandler()}
                  >
                    <i className="fa fa-times"></i>
                  </div>
                  <div className="selected-file">
                    <div className="text-center">
                      <div>
                        <i className="fa fa-file" aria-hidden="true"></i>
                      </div>
                      <div style={{ fontSize: "12px" }}>{file.name}</div>
                    </div>
                  </div>
                </div>
              )}
              {/* Video */}
              {file.type.split("/")[0] == "video" && (
                <div className="add-farmer__img-div">
                  <div
                    className="add-farmer__remove-img-btn"
                    onClick={() => removeFileHandler()}
                  >
                    <i className="fa fa-times"></i>
                  </div>
                  <div className="selected-file">
                    <div className="text-center">
                      <div>
                        <i className="fa fa-file-video" aria-hidden="true"></i>
                      </div>
                      <div style={{ fontSize: "12px" }}>{file.name}</div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
          <small>Description</small>
          <textarea
            className="input-field"
            style={{ width: "100%" }}
            rows="3"
            type="text"
            defaultValue={fileDes}
            onChange={fileDescriptionChangeHandler}
          ></textarea>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="primary"
            onClick={saveFileHandler}
            style={{ background: "#673AB7", color: "#fff", border: "none" }}
          >
            Save
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

const AISpecificTaskComponent = (props) => {
  const AIbasedOnHeatHandler = (event) => {
    console.log(event.target.value);
    props.setUpdateAITaskForm({
      ...props.updateAITaskForm,
      ai_form_suitable_for_ai_based_on_heat: event.target.value,
    });
  };

  const AIbasedOnDischargeHandler = (event) => {
    console.log(event.target.value);
    props.setUpdateAITaskForm({
      ...props.updateAITaskForm,
      ai_form_suitable_for_ai_based_on_discharge: event.target.value,
    });
  };

  const AIbullIdHandler = (event) => {
    console.log(event.target.value);
    props.setUpdateAITaskForm({
      ...props.updateAITaskForm,
      ai_form_id_of_bull: event.target.value,
    });
  };

  const AIbullNameHandler = (event) => {
    console.log(event.target.value);
    props.setUpdateAITaskForm({
      ...props.updateAITaskForm,
      ai_form_name_of_bull: event.target.value,
    });
  };

  const AIheatDateTimeHandler = (event) => {
    console.log(event.target.value);
    props.setUpdateAITaskForm({
      ...props.updateAITaskForm,
      ai_form_heat_date_and_time: event.target.value,
    });
  };

  const AIcompanyOfSemenHandler = (event) => {
    console.log(event.target.value);
    props.setUpdateAITaskForm({
      ...props.updateAITaskForm,
      ai_form_company_of_semen: event.target.value,
    });
  };

  const gmtFormattedAIHeatTime = (date) => {
    let data = new Date(date);
    data.setHours(data.getHours() + 5);
    data.setMinutes(data.getMinutes() + 30);
    return data.toISOString();
  };
  return (
    <>
      AI Form:
      <Row className="mt-2">
        <Col lg={4} md={6} sm={12}>
          <small>Suitable for AI based on heat</small>
          <select
            className="input-field"
            style={{ width: "100%" }}
            defaultValue={
              props.activityDetails.ai_form_suitable_for_ai_based_on_heat
                ? props.activityDetails.ai_form_suitable_for_ai_based_on_heat
                : "DEFAULT"
            }
            onChange={AIbasedOnHeatHandler}
          >
            <option disabled value="DEFAULT">
              Select
            </option>
            {props.AIconfig &&
              props.AIconfig.ai_form_suitable_for_ai_based_on_heat.category_options.map(
                (option) => {
                  return (
                    <option
                      key={option.reference_id}
                      value={option.reference_id}
                      style={{ textTransform: "capitalize" }}
                    >
                      {option.reference_name_l10n.ul
                        ? option.reference_name_l10n.ul
                        : option.reference_name_l10n.en}
                    </option>
                  );
                }
              )}
          </select>
        </Col>
        <Col lg={4} md={6} sm={12}>
          <small>Suitable for AI based on discharge</small>
          <select
            className="input-field"
            style={{ width: "100%" }}
            defaultValue={
              props.activityDetails.ai_form_suitable_for_ai_based_on_discharge
                ? props.activityDetails
                    .ai_form_suitable_for_ai_based_on_discharge
                : "DEFAULT"
            }
            onChange={AIbasedOnDischargeHandler}
          >
            <option disabled value="DEFAULT">
              Select
            </option>
            {props.AIconfig &&
              props.AIconfig.ai_form_suitable_for_ai_based_on_discharge.category_options.map(
                (option) => {
                  return (
                    <option
                      key={option.reference_id}
                      value={option.reference_id}
                      style={{ textTransform: "capitalize" }}
                    >
                      {option.reference_name_l10n.ul
                        ? option.reference_name_l10n.ul
                        : option.reference_name_l10n.en
                        ? option.reference_name_l10n.en
                        : "-"}
                    </option>
                  );
                }
              )}
          </select>
        </Col>
        <Col lg={4} md={6} sm={12}>
          <small>Bull Id</small>
          <input
            type="text"
            placeholder="Type here..."
            defaultValue={props.activityDetails.ai_form_id_of_bull}
            disabled={
              !(
                props.updateAITaskForm
                  .ai_form_suitable_for_ai_based_on_discharge == 1000105001
              ) ||
              !(
                props.updateAITaskForm.ai_form_suitable_for_ai_based_on_heat ==
                1000105001
              )
            }
            style={{ width: "100%" }}
            className="input-field"
            onChange={AIbullIdHandler}
          />
        </Col>
        <Col lg={4} md={6} sm={12}>
          <small>Bull Name</small>
          <input
            type="text"
            placeholder="Type here..."
            style={{ width: "100%" }}
            className="input-field"
            defaultValue={props.activityDetails.ai_form_name_of_bull}
            disabled={
              !(
                props.updateAITaskForm
                  .ai_form_suitable_for_ai_based_on_discharge == 1000105001
              ) ||
              !(
                props.updateAITaskForm.ai_form_suitable_for_ai_based_on_heat ==
                1000105001
              )
            }
            onChange={AIbullNameHandler}
          />
        </Col>
        <Col lg={4} md={6} sm={12}>
          <small>Heat date and time</small>
          <input
            type="datetime-local"
            style={{ width: "100%" }}
            className="input-field"
            defaultValue={
              props.activityDetails.ai_form_heat_date_and_time &&
              gmtFormattedAIHeatTime(
                props.activityDetails.ai_form_heat_date_and_time
              ).split(".")[0]
            }
            disabled={
              !(
                props.updateAITaskForm
                  .ai_form_suitable_for_ai_based_on_discharge == 1000105001
              ) ||
              !(
                props.updateAITaskForm.ai_form_suitable_for_ai_based_on_heat ==
                1000105001
              )
            }
            onChange={AIheatDateTimeHandler}
          />
        </Col>
        <Col lg={4} md={6} sm={12}>
          <small>Company of semen</small>
          <select
            className="input-field"
            style={{ width: "100%" }}
            defaultValue={
              props.activityDetails.ai_form_company_of_semen
                ? props.activityDetails.ai_form_company_of_semen
                : "DEFAULT"
            }
            disabled={
              !(
                props.updateAITaskForm
                  .ai_form_suitable_for_ai_based_on_discharge == 1000105001
              ) ||
              !(
                props.updateAITaskForm.ai_form_suitable_for_ai_based_on_heat ==
                1000105001
              )
            }
            onChange={AIcompanyOfSemenHandler}
          >
            <option disabled value="DEFAULT">
              Select
            </option>
            {props.AIconfig &&
              props.AIconfig.ai_form_company_of_semen.category_options.map(
                (option) => {
                  return (
                    <option
                      key={option.reference_id}
                      value={option.reference_id}
                      style={{ textTransform: "capitalize" }}
                    >
                      {option.reference_name_l10n.ul
                        ? option.reference_name_l10n.ul
                        : option.reference_name_l10n.en
                        ? option.reference_name_l10n.en
                        : ""}
                    </option>
                  );
                }
              )}
          </select>
        </Col>
      </Row>
      {/* <Button
        variant="primary"
        className="mt-2"
        onClick={updateAItaskHandler}
        style={{ background: "#ec6237", color: "#fff", border: "none" }}
      >
        Update AI details
      </Button> */}
      <hr />
    </>
  );
};

// export default TaskItem;
export default withAuthorization(TaskItem, [CS_TEAM]);
