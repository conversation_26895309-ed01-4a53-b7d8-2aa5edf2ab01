import React, { useEffect, useState, useCallback } from "react";
import {useSelector } from "react-redux";
import {instance} from "../../../../../Services/api.service";

const useGetPaymentDetails = () => {
  const userToken = useSelector((state:any) => state.user.userToken);
  const[data,setData] =useState();
  const [isLoading,setIsLoading] =useState(false);
  const paymentDetails = useCallback(async () => {
      setIsLoading(true);
    try {
      const response = await instance({
        url: `v2/collected/amount`,
        method: "POST",
        headers: {
          token: userToken.accessToken,  
        },
      });
        setData(response.data);
    } catch (error) {
      console.error("Error While getting data",error)
    }
  }, [ userToken.accessToken]);
  useEffect(() => {
    paymentDetails()
  }, []);
  return { data ,isLoading};
};
export default useGetPaymentDetails;
