import React, { useState, useMemo, useEffect } from "react";
import { Form, Select, InputNumber, Table, Button } from "antd";
import { ColumnsType } from "antd/es/table";
import ToastersService from "../../../../../Services/toasters.service";
import { useSelector } from "react-redux";
import { globalReferencesMapper } from "../../../../../Utilities/Common/utils";
import { instance } from "../../../../../Services/api.service";

interface FormData {
  key: number;
  category: string;
  mechanism: string;
  amount: number;
  current_mechanism: string;
  current_amount: number;
}

interface OptionType {
  value: string;
  label: string;
}

interface PaymentTabProps {
  careCalendarId: string;
}

const PaymentTab: React.FC<PaymentTabProps> = ({ careCalendarId }) => {
  const userToken = useSelector((state: any) => state.user.userToken);
  const [form] = Form.useForm();
  const [data, setData] = useState<FormData[]>([]);

  const options =
    globalReferencesMapper(10013100)?.map((item) => ({
      value: item?.reference_id,
      label: item?.reference_name_l10n,
    })) || [];

  const defaultData: FormData[] = [
    {
      key: 1,
      category: "Service Cost",
      mechanism: "",
      amount: null,
      current_amount: null,
      current_mechanism: "",
    },
    {
      key: 2,
      category: "Medicine Cost",
      mechanism: "",
      amount: null,
      current_amount: null,
      current_mechanism: "",
    },
  ];

  const displayData = data.length > 0 ? data : defaultData;

  const columns: ColumnsType<FormData> = useMemo(
    () => [
      {
        title: "Category",
        dataIndex: "category",
        key: "category",
        render: (text) => text,
      },
      {
        title: "Mechanism",
        dataIndex: "actual_mechanism",
        key: "actual_mechanism",
        render: (text, record) => (
          <Form.Item
            name={`actual_mechanism_${record.key}`}
            initialValue={record.mechanism}
          >
            <Select disabled options={options} placeholder="Select Mechanism" />
          </Form.Item>
        ),
      },
      {
        title: "Amount",
        dataIndex: "actual_amount",
        key: "actual_amount",
        render: (text, record) => (
          <Form.Item
            name={`actual_amount_${record.key}`}
            initialValue={record.amount}
          >
            <InputNumber
              placeholder="Enter Amount"
              min={0}
              style={{ width: "70%" }}
              disabled
            />
          </Form.Item>
        ),
      },
      {
        title: "Current Mechanism",
        dataIndex: "current_mechanism",
        key: "current_mechanism",
        width: 200,
        render: (text, record) => (
          <Form.Item
            name={`current_mechanism_${record.key}`}
            initialValue={record.mechanism}
            rules={[{ required: true, message: "Please select a mechanism" }]}
          >
            <Select options={options} placeholder="Select Mechanism" />
          </Form.Item>
        ),
      },
      {
        title: "Current Amount",
        dataIndex: "current_amount",
        key: "current_amount",
        render: (text, record) => (
          <Form.Item
            name={`current_amount_${record.key}`}
            initialValue={record.amount}
            rules={[
              { required: true, message: "Please input an amount" },
              {
                type: "number",
                min: 0,
                message: "Amount must be non-negative",
              },
            ]}
          >
            <InputNumber
              placeholder="Enter Amount"
              min={0}
              style={{ width: "70%" }}
            />
          </Form.Item>
        ),
      },
    ],
    []
  );

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await instance({
          url: `v2/collected/amount/`,
          method: "POST",
          headers: {
            token: userToken.accessToken,
          },
          data: { care_calendar_id: careCalendarId },
        });

        const fetchedData = response.data;

        form.setFieldsValue({
          actual_amount_1: fetchedData?.service?.actual_amount ?? null,
          actual_mechanism_1: fetchedData?.service?.actual_mechanism ?? "",
          actual_amount_2: fetchedData?.medicine?.actual_amount ?? null,
          actual_mechanism_2: fetchedData?.medicine?.actual_mechanism ?? "",
          current_amount_1: fetchedData?.service?.current_amount ?? null,
          current_mechanism_1: fetchedData?.service?.current_mechanism ?? "",
          current_amount_2: fetchedData?.medicine?.current_amount ?? null,
          current_mechanism_2: fetchedData?.medicine?.current_mechanism ?? "",
        });
      } catch (error) {
        console.error("Error while fetching payment data:", error);
      }
    };

    if (careCalendarId) {
      fetchData();
    }
  }, [careCalendarId, form, userToken.accessToken]);

  const onFinish = async (values: any) => {
    const processedData = {
      collected_amount: {
        service: {
          // amount: values.amount_1,
          // mechanism: values.mechanism_1,
          current_amount: values.current_amount_1,
          current_mechanism: values.current_mechanism_1,
        },
        medicine: {
          // amount: values.amount_2,
          // mechanism: values.mechanism_2,
          current_amount: values.current_amount_2,
          current_mechanism: values.current_mechanism_2,
        },
      },
      care_calendar_id: careCalendarId,
    };

    try {
      const response = await instance({
        url: `v2/collected/amount`,
        method: "POST",
        headers: {
          token: userToken.accessToken,
        },
        data: processedData,
      });

      console.log("Response:", response);
      ToastersService.successToast("Payment Details Saved Successfully");
    } catch (error) {
      console.error("Error while saving data:", error);
      ToastersService.failureToast("Could not save payment details");
    }
  };

  return (
    <Form form={form} onFinish={onFinish} layout="vertical">
      <Table
        dataSource={displayData}
        columns={columns}
        pagination={false}
        bordered
        rowKey="key"
        style={{
          width: "980px",
        }}
      />
      <div
        style={{
          marginTop: "16px",
          textAlign: "left",
        }}
      >
        <Button
          type="primary"
          htmlType="submit"
          size={"large"}
          style={{
            backgroundColor: "#e0474c",
            width: "130px",
          }}
        >
          Save
        </Button>
      </div>
    </Form>
  );
};

export default PaymentTab;
