import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { instance } from "../../../../Services/api.service";
import { clearUserToken } from "Components/user/action";

const useGetFormData = ({ activityId, careCalendarId }) => {
  console.log("mounted");
  const userToken = useSelector((state) => state.user.userToken);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [savedActivityForms, setSavedActivityForms] = useState(null);

  const getSavedActivityForms = useCallback(async () => {
    try {
      const response = await instance({
        url: `/v2/tasks/activity/get-form-data/${activityId}?care_calendar=${careCalendarId}`,
        method: "GET",
        headers: {
          token: userToken.accessToken,
        },
      });

      if (response.status === 200) {
        console.log('getSavedActivityForms response:', response?.data);
        setSavedActivityForms(response.data);
      } else {
        throw new Error(`Failed to fetch saved activity forms: ${response.status}`);
      }
    } catch (error) {
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  }, [activityId, careCalendarId, userToken.accessToken, dispatch, navigate]);

  useEffect(() => {
    console.log('making api call');
    getSavedActivityForms();
  }, [careCalendarId,activityId]);

  return { savedActivityForms };
};

export default useGetFormData;

