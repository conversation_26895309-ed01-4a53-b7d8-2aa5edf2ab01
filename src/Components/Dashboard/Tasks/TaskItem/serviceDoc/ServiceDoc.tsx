import { BASE_URL } from "../../../../../Services/api.service";
import { Button } from "antd";

export interface Root {
  document_id: string;
  document_type_id: number;
  client_document_information: any;
  document_information: DocumentInformation;
}

export interface DocumentInformation {
  key?: string;
  uri?: string;
  url?: string;
  fileName: string;
  fileType: string;
  entityType: string;
  description: string;
  localFolderName: string;
  serverFolderName: string;
  individualLocalFolderName: string;
  individualServerFolderName: string;
}

interface ServiceDocProps {
  docInfo: Root;
}

const ServiceDoc = ({ docInfo }: ServiceDocProps) => {
  const onClickHandler = () => {
    const fileUrl = BASE_URL + "/media/" + docInfo.document_id;
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = "ServiceDocment.pdf";
    document.body.appendChild(link);
    link.target = "_blank";
    link.click();
  };
  const isServiceDocAva =
    docInfo &&
    (docInfo.document_information.uri ||
      docInfo.document_information.url ||
      docInfo.document_information.key);

  return (
    <Button
      style={{ background: "#ec6237", color: "#fff", height: 40 }}
      onClick={onClickHandler}
      disabled={!isServiceDocAva}
    >
      Service Document
    </Button>
  );
};

export default ServiceDoc;
