import { But<PERSON> } from "@mui/material";
import LoadingButton from "@mui/lab/LoadingButton";
import { clearUserToken } from "../../../../user/action";
import { useCallback, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { instance } from "../../../../../Services/api.service";

type GenerateServiceDocProps = {
  careCalendarId: string;
};
const GenerateServiceDoc = ({ careCalendarId }: GenerateServiceDocProps) => {
  const navigate = useNavigate();
  const dispatch: any = useDispatch();
  const userToken = useSelector((state: any) => state.user.userToken);
  const [loading, setLoading] = useState(false);

  const generateServiceDoc = useCallback(async () => {
    try {
      setLoading(true);
      const response = await instance({
        url: `v2/tasks/service-document/pdf/${careCalendarId}`,
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
        responseType: "blob", // Set response type to blob for handling binary data
      });

      if (response.status === 200 || response.status === 201) {
        const blob = new Blob([response.data], { type: "application/pdf" });
        const url = window.URL.createObjectURL(blob);
        window.open(url, "_blank"); // Open the PDF in a new tab
        setLoading(false);
        window.location.reload();
      }
    } catch (error) {
      setLoading(false);
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  }, [careCalendarId]);

  return (
    <LoadingButton
      variant="contained"
      sx={{ textTransform: "none" }}
      onClick={generateServiceDoc}
      loading={loading}
    >
      Generate PDF
    </LoadingButton>
  );
};

export default GenerateServiceDoc;
