import { Ta<PERSON>,  Spin } from "antd";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, CircularProgress } from '@mui/material';
import Prescription from "./Prescription/Prescription";
import Observation from "./Observation/Observation";
import Diagnosis from "./Diagnosis/Diagnosis";
import { useState, useContext } from "react";
import { activityDetailsContext } from "../TaskItem";
import { useUserAccessibility } from "./UserAccessibility";
import { useSelector, useDispatch } from "react-redux";
import { BASE_URL } from "../../../../../Services/api.service";
import { instance } from "../../../../../Services/api.service";
import ObservationTable from "./Observation/ObservationTable";
import { FilePdfOutlined, LoadingOutlined } from "@ant-design/icons";

const TaskDetails = ({ careCalendarId, farmerId }) => {
  const [activeTab, setActiveTab] = useState("1");
  const [pdfData, setPdfData] = useState();
  const [Loading, setLoading] = useState(false);
  const userToken = useSelector((state) => state?.user?.userToken);

  const onTabChange = (key) => {
    setActiveTab(key);
  };

  const { calendar_activity_status } = useContext(activityDetailsContext);
  const overrideAvailable = useUserAccessibility(calendar_activity_status);

  const items = [
    {
      key: "1",
      label: "Observation",
      children: (
        <ObservationTable
          onTabChange={(key) => {
            onTabChange(key);
          }}
        />
      ),
    },
    {
      key: "2",
      label: "Diagnosis",
      children: (
        <Diagnosis
          onTabChange={(key) => {
            onTabChange(key);
          }}
          isOverrideAvailable={overrideAvailable}
        />
      ),
    },
    {
      key: "3",
      label: "Medicine",
      children: (
        <Prescription
          onTabChange={(key) => {
            onTabChange(key);
          }}
          isOverrideAvailable={overrideAvailable}
        />
      ),
    },
  ];


  const handleGenerateRX = async () => {
    setLoading(true);
    try {
      const response = await instance({
        url: `${BASE_URL}/v2/tasks/rx/pdf?language=en&farmer_id=${farmerId}&calendar_id=${careCalendarId}`,
        method: "GET",
        headers: {
          token: userToken.accessToken,
        },
        responseType: "arraybuffer",
      });

      const pdfBlob = new Blob([response.data], { type: "application/pdf" });
      setPdfData(pdfBlob);

      const pdfUrl = URL.createObjectURL(pdfBlob);
      window.open(pdfUrl, "_blank");

      setLoading(false);
    } catch (error) {
      console.error("Error generating PDF:", error);
      setLoading(false);
    }
  };


  return (
    <div style={{ position: "relative" }}>
      <Tabs
        defaultActiveKey="1"
        items={items}
        activeKey={activeTab}
        onChange={onTabChange}
      />
       <Button
        variant="contained"
        color="error"
        startIcon={Loading ? null : <FilePdfOutlined  style={{fontSize:'22px'}}/>}
        onClick={handleGenerateRX}
        disabled={Loading}
        sx={{
          position: "absolute",
          right: "20px",
          top: "8px",
          fontSize: "16px",
          fontWeight: "bold",
          display: "flex",
          alignItems: "center",
        }}
      >
        {Loading ? (
          <CircularProgress size={24} color="inherit" />
        ) : (
          "Generate Rx"
        )}
      </Button>
    </div>
  );
};
export default TaskDetails;
