import { useEffect, useMemo, useState } from "react";
import { useContext } from "react";
import { activityDetailsContext } from "../../TaskItem";
import {
  Typography,
  Popconfirm,
  Form,
  InputNumber,
  Input,
  Select,
  Tag,
  Button,
  DatePicker,
} from "antd";
import {
  convertUtcToIstFormatted,
  extractBasedOnLanguageMod,
} from "../../../../../../Utilities/Common/utils";
import style from "../TaskDetails.module.css";
import {
  BASE_URL,
  instance,
  tokenFailureHandler,
} from "../../../../../../Services/api.service";
import { DownloadOutlined } from "@ant-design/icons";
import referenceData from "../../../../../../Utilities/Common/referenceData";
import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { clearUserToken } from "../../../../../user/action";
import ToastersService from "../../../../../../Services/toasters.service";
import dayjs from "dayjs";
import { DeleteBtn } from "../Buttons/DeleteBtn";
import { OverrideBtn } from "../Buttons/OverrideBtn";
import { AddBtn } from "../Buttons/AddBtn";
import { SaveBtn } from "../Buttons/SaveBtn";
import { FormAndTable } from "../FormTable/FormAndTable";
import { ReactTextArea } from "../FormTable/ReactTextArea";
import { Dropdown } from "../FormTable/Dropdown";
import { message } from "antd";
import AntAlert from "../FormTable/AntAlert";
import { globalReferencesMapper } from "../../../../../../Utilities/Common/utils";

const getMedicineDetailsByMedId = (medicineId) => {
  return global?.medicineReferences[medicineId];
};
const unitMappingByMedId = (medicineId) => {
  const getMedicineUnitRefId = getMedicineDetailsByMedId(medicineId);
  const medicineUnitId = getMedicineUnitRefId?.medicine_unit
    ? getMedicineUnitRefId?.medicine_unit[0]
    : null;
  const getUnitName = globalReferencesMapper(10009500)?.filter(
    (item) => item?.reference_id === medicineUnitId
  );

  // Check if getUnitName is not empty
  if (getUnitName && getUnitName.length > 0) {
    return getUnitName[0]?.reference_name_l10n;
  } else {
    console.error("No unit name found for the given medicine ID");
    message.warning("No unit name found for the given medicine");
    return null;
  }
};

const Prescription = ({ onTabChange, isOverrideAvailable }) => {
  const [validation, setValidation] = useState(false);
  const [newMedicine, setNewMedicine] = useState(false);
  const [routeList, setRouteList] = useState();
  const [data, setData] = useState();
  const [medicineList, setMedicineList] = useState();
  const [followUp, setFollowUp] = useState();
  const [writePrescriptionNotes, setWritePrescriptionNotes] = useState();
  const [prescriptionsPDF, setPrescriptionsPDF] = useState();
  const [vetConsultancy, setVetConsultancy] = useState();
  const [count, setCount] = useState();
  const [isChanged, setIsChanged] = useState(false);
  const [disabledAll, setDisableAll] = useState(false);
  const [visibleAlert, setVisibleAlert] = useState(false);
  const [deleteAllMedicine, setDeleteAllMedicine] = useState(false);
  const [ifAccept, setIfAccept] = useState(false);
  const [addVisibleAlert, setAddVisibleAlert] = useState(false);
  const [deletePreviousItems, setDeletePreviousItems] = useState(false);
  const [sameRoute, setSameRoute] = useState(false);
  const { calendar_activity_status, care_calendar_id } = useContext(
    activityDetailsContext
  );

  const handleOptionChange = (value) => {
    console.log(value);
    if (value) {
      const medicineId = medicineList.find((item) => item.value === value);
      console.log(medicineId);
      const unit = unitMappingByMedId(value);
      if (medicineId) {
        form.setFieldsValue({ unit: unit ? unit : "" });
        if (medicineId?.value === 1999999999) {
          setVisibleAlert(true);
          setDisableAll(true);
        }
      }
    }
  };

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const dropdownValue = useMemo(() => {
      if (dataIndex === "frequency") {
        return referenceData.frequency_list.map((item) => ({
          value: item.reference_id,
          label: item.reference_name_l10n,
        }));
      } else if (dataIndex === "route") {
        const routeNameList = routeList?.map((item) => ({
          value: item.reference_id,
          label: item.reference_name_l10n,
        }));
        return routeNameList;
      } else if (dataIndex === "medicineName") {
        return medicineList;
      }
    }, [dataIndex]);

    const placeholderValues = useMemo(() => {
      if (dataIndex === "frequency") {
        return "Select Frequency";
      } else if (dataIndex === "route") {
        return "Select Route";
      } else if (dataIndex === "medicineName") {
        return "Select a Medicine";
      }
      return "Select an option";
    }, [dataIndex]);

    let inputNodeAlt;
    switch (inputType) {
      case "number":
        inputNodeAlt = (
          <InputNumber
            placeholder="eg - 50"
            type="number"
            disabled={disabledAll}
            onKeyDown={(e) => {
              if (
                e.key === "-" ||
                e.key === "e" ||
                e.key === "E" ||
                e.key === "+"
              ) {
                e.preventDefault();
              }
            }}
          />
        );
        break;
      case "select":
        inputNodeAlt = (
          <Select
            showSearch
            filterOption={filterOption}
            options={dropdownValue}
            placeholder={placeholderValues}
            virtual={false}
            onChange={
              dataIndex === "medicineName" ? handleOptionChange : undefined
            }
            disabled={
              dataIndex === "medicineName"
                ? record?.status === "ADMINISTERED"
                : undefined || disabledAll
            }
          />
        );
        break;
      case "text":
        inputNodeAlt = (
          <Input
            placeholder="eg - ML"
            disabled={disabledAll}
            onKeyDown={(e) => {
              const charCode = e.key.charCodeAt(0);
              if (
                (charCode < 65 || charCode > 90) &&
                (charCode < 97 || charCode > 122)
              ) {
                e.preventDefault();
              }
            }}
          />
        );
        break;
      default:
        inputNodeAlt = <Input />;
    }

    let validations;
    if (
      inputType === "select" ||
      inputType === "number" ||
      inputType === "text"
    ) {
      validations = [
        {
          required: true,
          message: `Please Input ${title}!`,
        },
        {
          pattern: /^[A-Za-z0-9\s.]*$/,
          message: `Please Input ${title}!`,
        },
      ];
    } else {
      validations = [
        {
          required: true,
          message: `Please Input ${title}!`,
        },
      ];
    }
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex}
            style={{
              margin: 0,
            }}
            rules={!disabledAll ? validations : null}
          >
            {inputNodeAlt}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  const filterOption = (input, option) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  // edit medicine functionalities
  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState("");
  const userToken = useSelector((state) => state.user.userToken);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const isEditing = (record) => record.key === editingKey;

  const updateMedicine = async (payload) => {
    try {
      const response = await instance({
        url: "v2/tasks/prescription/override", // v2/activity/prescription/override
        method: "POST",
        data: { data: payload },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response?.status === 201) {
        if (response?.data?.message) {
          ToastersService.failureToast(response?.data?.message);
          setEditingKey("");
        } else {
          console.log("medicineList", medicineList);
          getPrescription(care_calendar_id, medicineList);
          ToastersService.successToast("Medicince updated successfully!");
        }
      }
    } catch (error) {
      ToastersService.failureToast("Something went wrong !");
      setData(data);
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const edit = (record) => {
    console.log("record", record);
    setValidation(true);
    form.setFieldsValue({
      medicineName: "",
      dosage: "",
      unit: "",
      frequency: "",
      route: "",
      ...record,
    });
    setEditingKey(record.key);
  };

  const cancel = (record) => {
    if (record.isNewRow) {
      const containsNameOption = medicineList.some(
        (option) => option.label === record.name
      );

      if (!containsNameOption) {
        handleDelete(record.key);
      }
    }
    setEditingKey("");
    setDisableAll(false);
    setValidation(false);
    setIfAccept(false);
  };

  const save = async (key) => {
    try {
      const row = await form.validateFields();
      const newData = [...data];
      const pyload = [];
      const index = newData.findIndex((item) => key === item.key);
      if (index > -1) {
        if (
          data?.find(
            (item) =>
              item?.medicineName ===
                medicineList.find(
                  (medItem) => medItem.value === row.medicineName
                )?.label &&
              item?.route ===
                routeList?.find(
                  (routeItem) => routeItem.reference_id === row.route
                )?.reference_name_l10n
          )
        ) {
          setSameRoute(true);
        } else if (
          data.find(
            (item) =>
              item?.medicineName === row.medicineName &&
              item?.route ===
                routeList.find((sub) => sub?.reference_id === row.route)
                  ?.reference_name_l10n
          )
        ) {
          setSameRoute(true);
        } else {
          const item = newData[index];
          let routeName = "";
          if (row.route && routeList) {
            const routeItem = routeList.find(
              (item) => item.reference_id === row.route
            );
            if (routeItem) {
              routeName = routeItem.reference_name_l10n;
            } else {
              routeName = item.route;
            }
          }
          let routeReferenceId = "";
          if (row.route && routeList) {
            const routeId = routeList.find(
              (item) => item.reference_name_l10n === row.route
            );
            if (routeId) {
              routeReferenceId = routeId.reference_id;
            }
          }
          let medicineName = "";
          if (row.medicineName && medicineList) {
            if (typeof row.medicineName === "number") {
              const medicineId = medicineList.find(
                (item) => item.value === row.medicineName
              );
              if (medicineId) {
                medicineName = medicineId.label;
              }
            } else {
              const medicineId = medicineList.find(
                (item) => item.label === row.medicineName
              );
              if (medicineId) {
                medicineName = medicineId.label;
              }
            }
          }
          newData.splice(index, 1, {
            ...item,
            ...row,
            route: disabledAll ? "" : routeName,
            medicineName: medicineName,
            frequency: disabledAll ? "" : row.frequency,
          });
          if (deleteAllMedicine) {
            const newData1 = newData
              .filter(
                (item) =>
                  item.status !== "PUBLISHED" &&
                  item.medicineName === "0- No Medicine Needed"
              )
              .map((item, index) => ({
                ...item,
                slNo: index + 1,
              }));
            console.log("newData1", newData1);
            setDisableAll(false);
            setData(newData1);
            setDeleteAllMedicine(false);
          } else if (deletePreviousItems) {
            const newData1 = newData.slice(-1).map((item, index) => ({
              ...item,
              slNo: index + 1,
            }));
            console.log("newData1", newData1);
            setData(newData1);
            setDeletePreviousItems(false);
          } else {
            console.log("newData", newData);
            setData(newData);
          }
          setEditingKey("");
          setValidation(false);
          if (!newMedicine) {
            pyload.splice(index, 1, {
              care_calendar_classification_id:
                item?.care_calendar_classification_id,
              route:
                typeof row?.route === "string"
                  ? routeList.find(
                      (item) => item.reference_name_l10n === row?.route
                    ).reference_id
                  : row?.route,
              dosage: row?.dosage,
              frequency: row?.frequency,
              unit: row?.unit,
              medicineName: row?.medicineName,
            });
            console.log("payload", pyload);
            updateMedicine(pyload); // api call
          }
        }
      } else {
        newData.push(row);
        setData(newData);
        setEditingKey("");
      }
    } catch (errInfo) {
      console.log("Validate Failed:", errInfo);
    }
  };

  const columns = [
    {
      title: "Sl No.",
      dataIndex: "slNo",
      key: "slNo",
      width: "6%",
    },
    {
      title: "Medicine Name",
      dataIndex: "medicineName",
      key: "medicineName",
      width: "15%",
      editable: true,
    },
    {
      title: "Dose",
      dataIndex: "dosage",
      key: "dosage",
      width: "10%",
      editable: true,
    },
    {
      title: "Unit",
      dataIndex: "unit",
      key: "unit",
      width: "15%",
      editable: true,
    },
    {
      title: "Frequency",
      dataIndex: "frequency",
      key: "frequency",
      width: "15%",
      editable: true,
    },
    {
      title: "Route",
      dataIndex: "route",
      key: "route",
      width: "15%",
      editable: true,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: "10%",
      render: (_, record) => {
        let color;
        if (record?.status === "ADMINISTERED") {
          color = "green";
        } else {
          color = "volcano";
        }
        return <Tag color={color}>{record?.status}</Tag>;
      },
    },
    {
      title: "Actions",
      dataIndex: "operation",
      width: 200,
      render: (_, record) => {
        const editable = isEditing(record);
        return (
          <span>
            {editable ? (
              <>
                <Typography.Link
                  onClick={() => save(record.key)}
                  style={{ marginRight: 8 }}
                  disabled={visibleAlert || ifAccept || addVisibleAlert}
                >
                  Save
                </Typography.Link>
                <Popconfirm
                  title="Sure to cancel?"
                  onConfirm={() => cancel(record)}
                  disabled={visibleAlert || addVisibleAlert}
                >
                  <a>Cancel</a>
                </Popconfirm>
              </>
            ) : (
              <>
                {data?.length >= 1 &&
                  record.status !== "PUBLISHED" &&
                  record.status !== "CANCELLED" &&
                  !record.isNewRow && (
                    <OverrideBtn
                      editingKey={editingKey}
                      isOverrideAvailable={
                        calendar_activity_status === 1000300051 ||
                        calendar_activity_status === 1000300052
                      }
                      record={record}
                      onOverrideClick={edit}
                      label="Override"
                    />
                  )}

                {data?.length >= 1 &&
                  !isOverrideAvailable &&
                  record?.status !== "ADMINISTERED" &&
                  record.status !== "CANCELLED" && (
                    <DeleteBtn handleDelete={handleDelete} record={record} />
                  )}
              </>
            )}
          </span>
        );
      },
    },
  ];
  // && calendar_activity_status !== 1000300050

  const mergedColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    let inputTypeAlt;
    if (col.dataIndex === "dosage") {
      inputTypeAlt = "number";
    } else if (
      col.dataIndex === "frequency" ||
      col.dataIndex === "route" ||
      col.dataIndex === "medicineName"
    ) {
      inputTypeAlt = "select";
    } else if (col.dataIndex === "unit") {
      inputTypeAlt = "text";
    } else {
      inputTypeAlt = "text";
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: inputTypeAlt,
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  const pdfDownloadHandler = () => {
    const fileUrl = BASE_URL + "/media/" + prescriptionsPDF;
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = "Prescription.pdf";
    document.body.appendChild(link);
    link.target = "_blank";
    link.click();
  };

  const handleAdd = () => {
    const newData = {
      key: data?.some((item) => item.key === count + 1) ? count + 2 : count + 1,
      slNo: data?.length + 1,
      medicineName: <Select />,
      dosage: "",
      unit: "",
      frequency: <Select />,
      route: <Select />,
      isNewRow: true,
    };
    console.log("newData", newData);
    if (data?.some((item) => item?.medicineName === "0- No Medicine Needed")) {
      setAddVisibleAlert(true);
    }
    edit(newData);
    setData([...data, newData]);
    setCount(count + 1);
    setNewMedicine(true);
    setIsChanged(true);
  };

  const handleDelete = (key) => {
    const newData = data?.filter((item) => item.key !== key);
    const updatedData = newData.map((item, idx) => ({
      ...item,
      slNo: idx + 1,
    }));
    setCount(count - 1);
    setData(updatedData);
    setIsChanged(true);
  };

  const handlePrescriptionChange = (value) => {
    setWritePrescriptionNotes(value);
    setIsChanged(true);
  };

  const onChange = (value, dateString) => {
    setFollowUp(dayjs(dateString, "DD MMM YYYY hh:mm A"));
    setIsChanged(true);
  };

  const onOk = (value) => {
    console.log("onOk: ", value);
  };

  const handlePublish = () => {
    const generatePayload = (
      activity_id,
      data,
      follow_up,
      prescriptionNotes,
      vet_consultancy
    ) => {
      const prescription = data.map((item) => ({
        care_calendar_classification_id: item?.care_calendar_classification_id,
        id:
          medicineList?.find((newItem) => newItem?.label === item?.medicineName)
            ?.value || "",
        dosage:
          item?.medicineName === "0- No Medicine Needed" ? 0 : item?.dosage,
        prescribed_info: {
          unit:
            item?.medicineName === "0- No Medicine Needed" ? null : item?.unit,
          route:
            item?.medicineName === "0- No Medicine Needed"
              ? null
              : routeList?.find(
                  (newItem) => newItem.reference_name_l10n === item?.route
                )?.reference_id || "",
          frequency:
            item?.medicineName === "0- No Medicine Needed"
              ? null
              : item?.frequency,
          status:
            item?.status === "ADMINISTERED"
              ? "ADMINISTERED"
              : item?.status === "CANCELLED"
              ? "CANCELLED"
              : "PUBLISHED",
        },
      }));
      const dateObject = follow_up;
      const isoString = dateObject.isValid()
        ? dateObject?.toISOString()
        : undefined;
      const formattedDate = dateObject.isValid()
        ? dateObject?.format("YYYY-MM-DD")
        : undefined;
      const newPayload = {
        activity_id: activity_id,
        medicine: prescription,
        follow_up_required:
          isoString !== undefined && formattedDate !== undefined ? true : false,
        follow_up_date_gmt: isoString,
        follow_up_date: formattedDate,
        suggestions_advisory_notes: prescriptionNotes,
        vet_cosultant_type: referenceData.vet_consultancy.find(
          (item) => item.vet_consultancy_type === vet_consultancy
        )?.reference_id,
      };

      return newPayload;
    };
    console.log(
      "generatePayload",
      generatePayload(
        care_calendar_id,
        data,
        followUp,
        writePrescriptionNotes,
        vetConsultancy
      )
    );
    publishPrescription(
      generatePayload(
        care_calendar_id,
        data,
        followUp,
        writePrescriptionNotes,
        vetConsultancy
      )
    );
    setNewMedicine(false);
  };

  const publishPrescription = async (payload) => {
    try {
      const response = await instance({
        url: `/v2/tasks/prescription/publish`,
        method: "POST",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
        data: payload,
      });
      if (response?.status === 201) {
        if (response?.data?.data === true) {
          ToastersService.successToast("Prescription updated successfully!");
          setIsChanged(false);
          getPrescription(care_calendar_id, medicineList);
        } else {
          ToastersService.failureToast(
            "Something went wrong, please try again!"
          );
        }
      }
    } catch (error) {
      console.log(error);
      ToastersService.failureToast("Something went wrong, please try again!");
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  const getPrescription = async (activity_id, medicineList) => {
    try {
      const response = await instance({
        url: `/activity/prescription/${activity_id}`,
        method: "GET",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      if (response?.status === 200) {
        if (response?.data?.result === false) {
          tokenFailureHandler(response?.data);
          navigate("/login");
        }
        console.log("prescription api response", response?.data?.data);

        const routeOptions = response?.data?.data?.options?.routes?.map(
          (item, index) => ({
            key: index,
            reference_id: item?.reference_id,
            reference_name_l10n:
              item?.reference_name_l10n?.ul || item?.reference_name_l10n?.en,
          })
        );

        setRouteList(routeOptions);

        const prescriptions = response?.data?.data?.selected?.medicine?.map(
          (item, index) => ({
            key: index,
            slNo: index + 1,
            medicineName: medicineList?.some(
              (newItem) => newItem?.value === item?.id
            )
              ? medicineList?.find((newItem) => newItem?.value === item?.id)
                  ?.label
              : "",
            care_calendar_classification_id:
              item?.care_calendar_classification_id,
            dosage: item?.dosage,
            unit: item?.prescribed_info?.unit,
            frequency: item?.prescribed_info?.frequency,
            route: response?.data?.data?.options?.routes?.some(
              (newItem) =>
                newItem?.reference_id === item?.prescribed_info?.route
            )
              ? response?.data?.data?.options?.routes?.find(
                  (newItem) =>
                    newItem?.reference_id === item?.prescribed_info?.route
                )?.reference_name_l10n?.en ||
                response?.data?.data?.options?.routes?.find(
                  (newItem) =>
                    newItem?.reference_id === item?.prescribed_info?.route
                )?.reference_name_l10n?.ul ||
                ""
              : "",
            status: item?.prescribed_info?.status,
          })
        );

        setData(prescriptions);

        setFollowUp(() => {
          const dateTime = convertUtcToIstFormatted(
            response?.data?.data?.follow_up_date
          );
          return dayjs(dateTime, "DD MMM YYYY hh:mm A");
        });

        setWritePrescriptionNotes(
          response?.data?.data?.suggestions_advisory_notes?.note?.ul
        );

        setPrescriptionsPDF(response?.data?.data?.prescriptionPDF?.document_id);

        setVetConsultancy(
          referenceData.vet_consultancy.some(
            (item) =>
              item.reference_id === response?.data?.data?.vet_cosultant_type
          )
            ? referenceData.vet_consultancy.find(
                (item) =>
                  item.reference_id === response?.data?.data?.vet_cosultant_type
              )?.vet_consultancy_type
            : "Online"
        );

        setCount(response?.data?.data?.selected?.medicine?.length);
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  const getMedicineListToMapping = () => {
    const medicineArray = Object.keys(global?.medicineReferences).map(
      (key) => global?.medicineReferences[key]
    );
    const medicineOptions = medicineArray?.map((item, index) => ({
      key: index,
      label: extractBasedOnLanguageMod(item?.medicine_name_l10n, "en"),
      value: item?.medicine_id,
    }));
    return medicineOptions;
  };

  useEffect(() => {
    const medicineOptions = getMedicineListToMapping();
    setMedicineList(medicineOptions);
    getPrescription(care_calendar_id, medicineOptions);
  }, []);

  const handleAccept = () => {
    setDeleteAllMedicine(true);
    setVisibleAlert(false);
  };

  const handleDecline = () => {
    setVisibleAlert(false);
    setIfAccept(true);
  };

  const handleAddAccept = () => {
    setDeletePreviousItems(true);
    setAddVisibleAlert(false);
  };

  const handleAddDecline = () => {
    setAddVisibleAlert(false);
    setIfAccept(true);
  };

  return (
    <>
      <div style={{ textAlign: "end" }}>
        {prescriptionsPDF && (
          <Button
            size="large"
            onClick={pdfDownloadHandler}
            style={{ margin: "0 5px" }}
            icon={<DownloadOutlined />}
            type="primary"
          >
            Download PDF
          </Button>
        )}
      </div>
      <div>
        <AddBtn
          buttonText="Add Medicine"
          handleAdd={handleAdd}
          count={null}
          validation={validation}
          isOverrideAvailable={isOverrideAvailable}
        />
        <Form form={form} component={false}>
          <FormAndTable
            data={data}
            mergedColumns={mergedColumns}
            components={EditableCell}
            pagination={false}
          />
        </Form>
      </div>
      <div className="row mt-3">
        <div
          className="col-4"
          style={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          <span className={style.textStyle}>Follow up date</span>
          <DatePicker
            style={{ width: 250, margin: "10px" }}
            disabled={isOverrideAvailable}
            showTime={{
              format: "HH:mm A",
            }}
            format="DD MMM YYYY hh:mm A"
            value={followUp?.isValid() ? followUp : undefined}
            onChange={onChange}
            onOk={onOk}
          />
        </div>
        <div
          className="col-4"
          style={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          <span className={style.textStyle}>Vet Consultancy</span>
          <div style={{ margin: "10px" }}>
            <Dropdown
              placeholder="Not defined"
              value={vetConsultancy}
              // onChange={handleChangeDiagnosis}
              // options={responseToTreatment}
              disabled={true}
            />
          </div>
        </div>
      </div>
      <span className={style.textStyle}>Prescription Notes</span>
      <ReactTextArea
        observationalNotes={writePrescriptionNotes}
        handleObservationChange={handlePrescriptionChange}
        isOverrideAvailable={null}
      />
      <div style={{ margin: "20px 0px" }}>
        <SaveBtn
          label="Publish"
          disabled={isOverrideAvailable || !isChanged}
          onClick={handlePublish}
        />
      </div>
      {visibleAlert && (
        <AntAlert
          onAccept={handleAccept}
          onDecline={handleDecline}
          type="error"
          message="Warning..!"
          description="If you select no medicine needed then all previous medicine got automatically removed and hence forward you don't be able to add any medicine."
        />
      )}
      {addVisibleAlert && (
        <AntAlert
          onAccept={handleAddAccept}
          onDecline={handleAddDecline}
          message="Information..!"
          description="If you accept this then you can add medicines and previous no medicine needed was automatically removed."
          type="info"
        />
      )}
      {sameRoute && (
        <AntAlert
          onAccept={() => setSameRoute(false)}
          onDecline={undefined}
          message="Alert!"
          type="error"
          description="This medicine is already associated with the selected route. Please choose a different medicine or route to save."
        />
      )}
    </>
  );
};

export default Prescription;
