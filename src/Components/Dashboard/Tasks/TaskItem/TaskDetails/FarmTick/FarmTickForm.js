
import React from 'react';
import useGetFormData from '../../useGetFormData';
import FarmTickConfig from './FarmTickConfig.json';
import { Card, CardContent, Typography, Divider, Box } from '@mui/material';

const FarmTickForm = ({ activityId, careCalendarId }) => {
  const { savedActivityForms } = useGetFormData({ activityId, careCalendarId });

  if (!savedActivityForms || !savedActivityForms.result) {
    return <div>Loading...</div>;
  }

  const animalData = savedActivityForms.result.animal_data;

  if (!animalData || animalData.length === 0) {
    return <div>No cattle data available</div>;
  }

  return (
    <div className="p-4">
      <Box style={{overflowY: "scroll", maxHeight: 600, scrollbarWidth: 'none', msOverflowStyle: 'none'}}>
        {animalData.map((animal, animalIndex) => (
          <Card key={animal.id || animalIndex} sx={{ mb: 2, boxShadow: 3, width: '380px' }}>
            <CardContent>
              {FarmTickConfig.FarmTickForm.map((field, index) => {
                const value = animal[field.key];
                const displayValue = field.type === 'radio' 
                  ? `Task ${field.options.find(opt => opt.value === value)?.label || 'N/A'}`
                  : value || 'N/A';

                return index === 0 ? (
                  <React.Fragment key={field.key}>
                    <Typography variant="h6" component="div" sx={{ fontWeight: 'bold', fontSize: '18px' }}>
                      {field.label}: {displayValue}
                    </Typography>
                    <Divider sx={{ my: 1, borderBottomWidth: 4, bgcolor: '#dedcdc' }} />
                  </React.Fragment>
                ) : (
                  <Typography key={field.key} variant="body2" sx={{ fontSize: '15px' }}>
                    {displayValue}
                  </Typography>
                );
              })}
            </CardContent>
          </Card>
        ))}
      </Box>
    </div>
  );
};

export default FarmTickForm;
