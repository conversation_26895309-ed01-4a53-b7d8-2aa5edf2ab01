
import React, { useState, useEffect } from 'react';
import { Card, CardContent, Typography, Grid, Divider, Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import dayjs from 'dayjs';
import aiConfig from "./aiConfig.json";
import useGetFormData from '../../useGetFormData';
import { globalReferencesMapper,extractBasedOnLanguageMod } from 'Utilities/Common/utils';

const mapConfigWithOptions = () => {
  return aiConfig.AIForm.reduce((acc, item) => {
    if (item.category_id) {
      item.option = globalReferencesMapper(item.category_id);
    }
    acc[item.key] = item;
    return acc;
  }, {});
};

const formatValue = (configItem, value) => {
  if (value === undefined || value === null || value === '') return 'N/A';

  if (configItem.key.toLowerCase().includes('date')) {
    return dayjs(value).format('LLL');
  }

  if (Array.isArray(value)) {
    if (value.length === 0) return 'NA';
    if (configItem.option) {
      return value.map(field => {
        const option = configItem.option.find(opt => opt.reference_id === field);
        return option ? extractBasedOnLanguageMod(option.reference_name_l10n) : 'null';
      }).join(', ');
    }
    return value.join(', ');
  }

  if (configItem.option) {
    const option = configItem.option.find(opt => opt.reference_id === value);
    return option ? extractBasedOnLanguageMod(option.reference_name_l10n) : 'null';
  }

  return String(value);
};

const StyledCard = styled(Card)(({ theme }) => ({
  width: '760px',
  backgroundColor: 'white',
}));

export const AIForm = ({ activityId, careCalendarId}) => {
  console.log('nk',careCalendarId);

  const [error, setError] = useState(null);
  const mappedConfig = mapConfigWithOptions();
  const { savedActivityForms } = useGetFormData({activityId, careCalendarId});

  console.log(savedActivityForms, 'savedforms');

  if (!savedActivityForms) {
    return <Typography>Loading...</Typography>;
  }

  const formItems = Object.entries(mappedConfig)
    .filter(([key]) => key in savedActivityForms && key !== 'service_document')
    .map(([key, configItem]) => ({
      key,
      label: configItem.label,
      value: formatValue(configItem, savedActivityForms[key])
    }));

  return (
    <Box display="flex" bgcolor="grey.100" p={2}>
      <StyledCard elevation={3}>
        <CardContent>
          <Grid container spacing={1}>
            <Grid item xs={6}>
              <Typography variant="subtitle2" fontWeight="bold" fontSize='16px'>
                Label
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="subtitle2" fontWeight="bold" fontSize='16px' marginLeft='55px'>
                Value
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Divider sx={{ width: '100%', height: '2px', backgroundColor:'#ebebeb' }}/>
            </Grid>
            {formItems.map(({ key, label, value }) => (
              <Grid item xs={12} key={key}>
                <Grid container spacing={16}>
                  <Grid item xs={6}>
                    <Typography variant="caption" fontWeight="bold" fontSize={14} marginBottom={15}>
                      {label}
                    </Typography>
                    <Divider sx={{ width: '300%', height: '2px', backgroundColor:'#ebebeb' }}/>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="caption" fontWeight="bold" fontSize={14} color="text.secondary">
                      {value}
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </StyledCard>
    </Box>
  );
};

