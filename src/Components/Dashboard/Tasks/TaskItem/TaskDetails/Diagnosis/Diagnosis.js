import { useContext, useState, useEffect } from "react";
import { activityDetailsContext } from "../../TaskItem";
import { Form, Select, InputNumber, Popconfirm, Typography, Input } from "antd";
import style from "../TaskDetails.module.css";
import ToastersService from "../../../../../../Services/toasters.service";
import { useNavigate } from "react-router-dom";
import {
  instance,
  tokenFailureHandler,
} from "../../../../../../Services/api.service";
import { DeleteBtn } from "../Buttons/DeleteBtn";
import { OverrideBtn } from "../Buttons/OverrideBtn";
import { AddBtn } from "../Buttons/AddBtn";
import { SaveBtn } from "../Buttons/SaveBtn";
import { FormAndTable } from "../FormTable/FormAndTable";
import { Dropdown } from "../FormTable/Dropdown";

const Diagnosis = ({ onTabChange, isOverrideAvailable }) => {
  const columns = [
    {
      title: "Sl No.",
      dataIndex: "slNo",
      key: "slNo",
      width: 100,
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: 200,
      editable: true,
    },
    {
      title: "Actions",
      dataIndex: "operation",
      width: 200,
      render: (_, record) => {
        const editable = isEditing(record);
        return (
          <span>
            {editable ? (
              <>
                <Typography.Link
                  onClick={() => save(record.key)}
                  style={{ marginRight: 8 }}
                >
                  Save
                </Typography.Link>
                <Popconfirm
                  title="Sure to cancel?"
                  onConfirm={() => cancel(record)}
                >
                  <a>Cancel</a>
                </Popconfirm>
              </>
            ) : (
              <>
                {/* {data?.length >= 1 && (
                  <OverrideBtn
                    editingKey={editingKey}
                    isOverrideAvailable={isOverrideAvailable}
                    record={record}
                    onOverrideClick={edit}
                    label="Edit"
                  />
                )} */}
                {data?.length >= 1 && !isOverrideAvailable && (
                  <DeleteBtn handleDelete={handleDelete} record={record} />
                )}
              </>
            )}
          </span>
        );
      },
    },
  ];

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const selectedOptions = data?.map((item) => item.name);
    const filteredOptions = diagnosisList?.filter(
      (option) => !selectedOptions.includes(option.label)
    );
    let validations;
    if (inputType === "number" || inputType === "select") {
      validations = [
        {
          required: true,
          message: `Please Input ${title}!`,
        },
        {
          pattern: /^[A-Za-z0-9\s.]*$/,
          message: `Please Input ${title}!`,
        },
      ];
    } else {
      validations = [
        {
          required: true,
          message: `Please Input ${title}!`,
        },
      ];
    }

    let inputNodeAlt;
    if (inputType === "number") {
      inputNodeAlt = (
        <InputNumber
          placeholder="value"
          type="number"
          onKeyDown={(e) => {
            if (e.key === '-' || e.key === 'e' || e.key === 'E' || e.key === '+') {
              e.preventDefault();
            }
          }}
        />
      );
    } else if (inputType === "select") {
      inputNodeAlt = (
        <Select
          showSearch
          placeholder="Select Category"
          options={filteredOptions}
          onSearch={onSearch}
          filterOption={filterOption}
          virtual={false}
        />
      );
    } else {
      inputNodeAlt = <Input />;
    }

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex}
            style={{
              margin: 0,
            }}
            rules={validations}
          >
            {inputNodeAlt}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  const { care_calendar_id } = useContext(activityDetailsContext);

  const [diagnosisList, setDiagnosisList] = useState([]);
  const [responseToTreatment, setResponseToTreatment] = useState([]);
  const [resPreviousTreatment, setResPreviousTreatment] = useState();
  const [isChanged, setIsChanged] = useState(false);

  const onSearch = (value) => {
    console.log("search:", value);
  };

  const filterOption = (input, option) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  const handleChangeDiagnosis = (value) => {
    setResPreviousTreatment(value);
    setIsChanged(true);
  };

  const [validation, setValidation] = useState(false);
  const navigate = useNavigate();

  const [data, setData] = useState();

  const getDiagnosisList = async (activity_id) => {
    try {
      const response = await instance({
        url: `/activity/diagnosis/${activity_id}`,
        method: "GET",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      if (response?.status === 200) {
        if (response?.data?.result === false) {
          tokenFailureHandler(response?.data);
          navigate("/login");
        }
        console.log("Diagnosis api response", response?.data?.data);

        const transformedDiagnosisList =
          response?.data?.data?.options?.diagnosis?.map((item) => ({
            value: item?.reference_id,
            label:
              item?.reference_name_l10n?.en || item?.reference_name_l10n?.ul,
          }));

        setDiagnosisList(transformedDiagnosisList);

        const transformedDiagnosisTreatment =
          response?.data?.data?.options?.response_to_treatment?.map((item) => ({
            value: item?.reference_id,
            label:
              item?.reference_name_l10n?.en || item?.reference_name_l10n?.ul,
          }));

        setResponseToTreatment(transformedDiagnosisTreatment);

        const selectedDiagnosis =
          response?.data?.data?.selected?.diagnosis?.map((item, index) => ({
            key: index,
            slNo: index + 1,
            name: response?.data?.data?.options?.diagnosis?.some(
              (newItem) => newItem?.reference_id === item?.value
            )
              ? response?.data?.data?.options?.diagnosis?.find(
                (newItem) => newItem?.reference_id === item?.value
              )?.reference_name_l10n?.en ||
              response?.data?.data?.options?.diagnosis?.find(
                (newItem) => newItem?.reference_id === item?.value
              )?.reference_name_l10n?.ul ||
              ""
              : "",
            care_calendar_classification_id:
              item?.care_calendar_classification_id,
          }));

        setData(selectedDiagnosis);

        const reqToRes =
          response?.data?.data?.selected?.response_to_treatment?.map(
            (item) => item?.value
          );

        setResPreviousTreatment(reqToRes[0]);
        setCount(response?.data?.data?.selected?.diagnosis?.length);
      }
    } catch (error) {
      ToastersService.failureToast("Something went wrong, please try again!");
      console.log(error);
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  useEffect(() => {
    getDiagnosisList(care_calendar_id);
  }, []);

  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState("");
  const isEditing = (record) => record.key === editingKey;

  const edit = (record) => {
    setValidation(true);
    form.setFieldsValue({
      name: <Select placeholder="Select Diagnosis" options={diagnosisList} />,
      ...record,
    });
    setEditingKey(record.key);
    setIsChanged(true);
  };

  const cancel = (record) => {
    if (record.isNewRow) {
      const containsNameOption = diagnosisList.some(
        (option) => option.label === record.name
      );
      if (!containsNameOption) {
        handleDelete(record.key);
      }
    }
    setEditingKey("");
    setValidation(false);
  };

  const save = async (key) => {
    try {
      const row = await form.validateFields();
      const newData = [...data];
      const index = newData.findIndex((item) => key === item.key);

      if (index > -1) {
        const item = newData[index];
        let diagnosisName = "";
        if (row.name && diagnosisList) {
          const diagnosisItem = diagnosisList.find(
            (item) => item.value === row.name
          );
          if (diagnosisItem) {
            diagnosisName = diagnosisItem.label;
          }
        }
        newData.splice(index, 1, {
          ...item,
          ...row,
          name: diagnosisName,
        });
        const updatedData = newData.map((item, idx) => ({
          ...item,
          slNo: idx + 1,
        }));
        setData(updatedData);
        setEditingKey("");
        setValidation(false);
      } else {
        console.log("Row not found for key:", key);
      }
    } catch (errInfo) {
      console.log("Validate Failed:", errInfo);
    }
  };

  const mergedColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    let inputTypeAlt;
    if (col.dataIndex === "value") {
      inputTypeAlt = "number";
    } else if (col.dataIndex === "name") {
      inputTypeAlt = "select";
    } else {
      inputTypeAlt = "text";
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: inputTypeAlt,
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  const [count, setCount] = useState();

  const handleAdd = () => {
    console.log(data.some((item) => item.key) == count + 1);
    const newData = {
      key: data.some((item) => item.key === count + 1) ? count + 2 : count + 1,
      slNo: count + 1,
      name: (
        <Select
          showSearch
          placeholder="Select Category"
          options={diagnosisList}
          onSearch={onSearch}
          filterOption={filterOption}
        />
      ),
      isNewRow: true,
    };
    edit(newData);
    console.log(data);
    setData([...data, newData]);
    setCount(count + 1);
    setIsChanged(true);
  };

  const handleDelete = (key) => {
    const newData = data.filter((item) => item.key !== key);
    const updatedData = newData.map((item, idx) => ({
      ...item,
      slNo: idx + 1,
    }));
    setCount(count - 1);
    setData(updatedData);
    setIsChanged(true);
  };

  const saveDiagnosis = async (payload) => {
    try {
      const response = await instance({
        url: `/v2/tasks/diagnosis`,
        method: "PUT",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
        data: payload,
      });
      if (response?.status === 200) {
        if (response?.data?.result === true) {
          ToastersService.successToast("Diagnosis updated successfully!");
          getDiagnosisList(care_calendar_id);
          setIsChanged(false);
        } else {
          ToastersService.failureToast(
            "Something went wrong, please try again!"
          );
        }
      }
    } catch (error) {
      console.log(error);
      ToastersService.failureToast("Something went wrong, please try again!");
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  const handleSaveAndNext = () => {
    console.log(resPreviousTreatment);
    if (!validation && resPreviousTreatment) {
      const generatePayload = (diagnosis, responseToTreatment, activityId) => {
        const payload = {
          activity_id: activityId,
          diagnosis: diagnosis.map((item) => ({
            value: diagnosisList.find((x) => x.label === item.name)
              ? diagnosisList.find((x) => x.label === item.name).value
              : "",
            care_calendar_classification_id:
              item.care_calendar_classification_id,
          })),
          response_to_treatment: [
            {
              value: responseToTreatment,
            },
          ],
        };

        return payload;
      };
      console.log(
        "payload",
        generatePayload(data, resPreviousTreatment, care_calendar_id)
      );
      saveDiagnosis(
        generatePayload(data, resPreviousTreatment, care_calendar_id)
      );
    } else {
      ToastersService.failureToast(
        "Diagnosis has not been saved, please check and fill correctly!"
      );
    }
  };

  return (
    <>
      <div>
        <div>
          <AddBtn
            buttonText="Add Diagnosis"
            handleAdd={handleAdd}
            count={null}
            validation={validation}
            isOverrideAvailable={isOverrideAvailable}
          />
          <Form form={form} component={false}>
            <FormAndTable
              data={data}
              mergedColumns={mergedColumns}
              components={EditableCell}
              pagination={false}
            />
          </Form>
        </div>
      </div>
      <div className="mt-3">
        <p className={style.textStyle}>Response for previous treatment</p>
        <Dropdown
          placeholder={"Previous Treatment"}
          value={resPreviousTreatment}
          onChange={handleChangeDiagnosis}
          options={responseToTreatment}
          disabled={isOverrideAvailable}
        />
      </div>
      <SaveBtn
        label="Save"
        disabled={isOverrideAvailable || !isChanged}
        onClick={handleSaveAndNext}
      />
    </>
  );
};
export default Diagnosis;
