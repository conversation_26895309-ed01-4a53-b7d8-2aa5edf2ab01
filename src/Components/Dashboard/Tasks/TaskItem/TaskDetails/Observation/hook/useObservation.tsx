import { clearUserToken } from "../../../../../../user/action";
import { instance } from "../../../../../../../Services/api.service";
import { useCallback, useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { AppDispatch } from "../../../../../../../redux/store";

interface LocalizedString {
    [key : string] : any
}

interface DataType {
    observation_category_id?: number;
    observation_category?: LocalizedString;
    observation_name_id?: number;
    observation_name?: LocalizedString;
    observation_val?: string[];
}

interface UseObservationProps {
    taskId : string
}

const useObservation = ({ taskId } : UseObservationProps)=>{
    const navigate = useNavigate()
    const dispatch : AppDispatch = useDispatch();

    const userToken = useSelector((state : any) => state.user.userToken);
    const [observationList, setObservationList] = useState<DataType[]>([])
    const [observationNote, setObservationNote] = useState<any>()
    const [loading, setLoading] = useState<boolean>(false)

    const getObservationList = useCallback(async () => {    
        try {
          setLoading(true)
          const response = await instance({
            url: `v3/tasks/${taskId}/observations/all`,
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              token: userToken.accessToken,
            },
          });
          if (response.status === 200) {
            setLoading(false)
            setObservationList(response?.data?.observations)
            setObservationNote(response.data?.notes?.note?.ul)
    
          }
        } catch (error : any) {
          setLoading(false)
          console.log(error);
          if ('response' in error && error.response.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
          }
        }
      },[])
      useEffect(()=>{
        getObservationList()
      },[])

    return {
        observationList,
        observationNote,
        loading
    } 
}

export default useObservation;