import { useEffect, useState } from "react";
import { useContext } from "react";
import { activityDetailsContext } from "../../TaskItem";
import { Form, Select, InputNumber, Popconfirm, Typography, Input } from "antd";
import style from "../TaskDetails.module.css";
import ToastersService from "../../../../../../Services/toasters.service";
import {
  instance,
  tokenFailureHandler,
} from "../../../../../../Services/api.service";
import { useNavigate } from "react-router-dom";
import { DeleteBtn } from "../Buttons/DeleteBtn";
import { OverrideBtn } from "../Buttons/OverrideBtn";
import { AddBtn } from "../Buttons/AddBtn";
import { SaveBtn } from "../Buttons/SaveBtn";
import { FormAndTable } from "../FormTable/FormAndTable";
import { ReactTextArea } from "../FormTable/ReactTextArea";

const Observation = ({ onTabChange, isOverrideAvailable }) => {
  const [nameOptions, setNameOptions] = useState([]);
  const [isChanged, setIsChanged] = useState(false);

  const coloumns = [
    {
      title: "Sl No.",
      dataIndex: "slNo",
      key: "slNo",
      width: 100,
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: 200,
      editable: true,
    },
    {
      title: "Value",
      dataIndex: "value",
      key: "value",
      width: 200,
      editable: true,
    },
    {
      title: "Actions",
      dataIndex: "operation",
      width: 200,
      render: (_, record) => {
        const editable = isEditing(record);
        return (
          <span>
            {editable ? (
              <>
                <Typography.Link
                  onClick={() => save(record.key)}
                  style={{ marginRight: 8 }}
                >
                  Save
                </Typography.Link>
                <Popconfirm
                  title="Sure to cancel?"
                  onConfirm={() => cancel(record)}
                >
                  <a>Cancel</a>
                </Popconfirm>
              </>
            ) : (
              <>
                {data?.length >= 1 && (
                  <OverrideBtn
                    editingKey={editingKey}
                    isOverrideAvailable={isOverrideAvailable}
                    record={record}
                    onOverrideClick={edit}
                    label="Edit"
                  />
                )}
                {data?.length >= 1 && !isOverrideAvailable && (
                  <DeleteBtn handleDelete={handleDelete} record={record} />
                )}
              </>
            )}
          </span>
        );
      },
    },
  ];

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const selectedOptions = data?.map((item) => item.name);
    const filteredOptions = nameOptions.filter(
      (option) => !selectedOptions?.includes(option.label)
    );
    let validations;
    if (inputType === "number" || inputType === "select") {
      validations = [
        {
          required: true,
          message: `Please Input ${title}!`,
        },
        {
          pattern: /^[A-Za-z0-9\s.]*$/,
          message: `Please Input ${title}!`,
        },
      ];
    } else {
      validations = [
        {
          required: true,
          message: `Please Input ${title}!`,
        },
      ];
    }

    let inputNodeAlt;
    if (inputType === "number") {
      inputNodeAlt = (
        <InputNumber
          placeholder="value"
          type="number"
          onKeyDown={(e) => {
            if (e.key === '-' || e.key === 'e' || e.key === 'E' || e.key === '+') {
              e.preventDefault();
            }
          }}
        />
      );
    } else if (inputType === "select") {
      inputNodeAlt = (
        <Select
          placeholder="Select Observation"
          options={filteredOptions}
          disabled={nameOptions.some((item) => item.label === record.name)}
        />
      );
    } else {
      inputNodeAlt = <Input />;
    }

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex}
            style={{
              margin: 0,
            }}
            rules={validations}
          >
            {inputNodeAlt}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  const { care_calendar_id } = useContext(activityDetailsContext);

  const [validation, setValidation] = useState(false);

  const [observationalNotes, setObservationalNotes] = useState();

  const [data, setData] = useState();

  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState("");
  const isEditing = (record) => record.key === editingKey;

  const edit = (record) => {
    setValidation(true);
    setIsChanged(true);
    form.setFieldsValue({
      name: <Select options={nameOptions} />,
      value: "",
      ...record,
    });
    setEditingKey(record.key);
  };

  const cancel = (record) => {
    if (record.isNewRow) {
      const containsNameOption = nameOptions.some(
        (option) => option.label === record.name
      );
      if (!containsNameOption) {
        handleDelete(record.key);
      }
    }

    setEditingKey("");
    setValidation(false);
  };

  const save = async (key) => {
    try {
      const row = await form.validateFields();
      const newData = [...data];
      const index = newData.findIndex((item) => key === item.key);

      if (index > -1) {
        const item = newData[index];
        newData.splice(index, 1, {
          ...item,
          ...row,
        });
        const updatedData = newData.map((item, idx) => ({
          ...item,
          slNo: idx + 1,
        }));
        setData(updatedData);
        setEditingKey("");
        setValidation(false);
      } else {
        console.log("Row not found for key:", key);
      }
    } catch (errInfo) {
      console.log("Validate Failed:", errInfo);
    }
  };

  const mergedColumns = coloumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    let inputTypeAlt;
    if (col.dataIndex === "value") {
      inputTypeAlt = "number";
    } else if (col.dataIndex === "name") {
      inputTypeAlt = "select";
    } else {
      inputTypeAlt = "text";
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: inputTypeAlt,
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  const [count, setCount] = useState();

  const handleAdd = () => {
    console.log(data.some((item) => item.key) == count + 1);
    const newData = {
      key: data.some((item) => item.key === count + 1) ? count + 2 : count + 1,
      slNo: count + 1,
      name: <Select options={nameOptions} placeholder="Select Observation" />,
      value: <InputNumber placeholder="value" />,
      isNewRow: true,
    };
    edit(newData);
    console.log(data);
    setData([...data, newData]);
    setCount(count + 1);
    setIsChanged(true);
  };

  const handleObservationChange = (value) => {
    setIsChanged(true);
    setObservationalNotes(value);
  };

  const handleDelete = (key) => {
    const newData = data.filter((item) => item.key !== key);
    const updatedData = newData.map((item, idx) => ({
      ...item,
      slNo: idx + 1,
    }));
    setCount(count - 1);
    setData(updatedData);
    setIsChanged(true);
  };

  const saveObservations = async (payload) => {
    try {
      const response = await instance({
        url: `/v2/tasks/observation`,
        method: "PUT",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
        data: payload,
      });
      if (response?.status === 200) {
        if (response?.data?.result === true) {
          ToastersService.successToast("Observation updated successfully!");
          getObservations(care_calendar_id);
          setIsChanged(false);
        } else {
          ToastersService.failureToast(
            "Something went wrong, please try again!"
          );
        }
      }
    } catch (error) {
      console.log(error);
      ToastersService.failureToast("Something went wrong, please try again!");
      if (error.response.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  const handleSaveAndNext = () => {
    if (!validation && observationalNotes.length > 0) {
      const generatePayload = (data, observationalNotes, activityId) => {
        const observations = data.map((item) => ({
          id: item.name === "Weight" ? "weight_1" : "temperature_1",
          value: item.value,
          care_calendar_classification_id: item.care_calendar_classification_id,
        }));

        const payload = {
          activity_id: activityId,
          observations: observations,
          observation_notes_1: observationalNotes,
        };

        return payload;
      };
      saveObservations(
        generatePayload(data, observationalNotes, care_calendar_id)
      );
    } else {
      ToastersService.failureToast(
        "Observations are not been saved, please check and fill correctly!"
      );
    }
  };

  const getObservations = async (activity_id) => {
    try {
      const response = await instance({
        url: `/activity/observation/${activity_id}`,
        method: "GET",
        headers: {
          token: localStorage.getItem("accessToken"),
        },
      });
      if (response?.status === 200) {
        if (response?.data?.result === false) {
          tokenFailureHandler(response?.data);
          navigate("/login");
        }
        console.log("observation api response", response?.data?.data?.data);
        setData(
          response?.data?.data?.data?.obervations.map((item, index) => ({
            key: index,
            slNo: index + 1,
            name: item?.id === "weight_1" ? "Weight" : "Temperature",
            value: item?.value,
            care_calendar_classification_id:
              item?.care_calendar_classification_id,
            keyRef: item?.keyRef,
          }))
        );
        setNameOptions(
          response?.data?.data?.data?.options.map((item, index) => ({
            label: item?.reference_name,
            value: item?.reference_name,
          }))
        );
        setCount(response?.data?.data?.data?.obervations?.length);
        setObservationalNotes(
          response?.data?.data?.data?.observationNotes?.note?.ul
        );
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        console.log("token expired");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("resetToken");
        ToastersService.failureToast("Session Expired!");
        navigate("/login");
        window.location.reload();
      }
    }
  };

  useEffect(() => {
    getObservations(care_calendar_id);
  }, []);

  return (
    <>
      <div>
        <div>
          <AddBtn
            buttonText="Add Observation"
            handleAdd={handleAdd}
            count={count}
            validation={validation}
            isOverrideAvailable={isOverrideAvailable}
          />
          <Form form={form} component={false}>
            <FormAndTable
              data={data}
              mergedColumns={mergedColumns}
              components={EditableCell}
              pagination={false}
            />
          </Form>
        </div>
      </div>
      <div className="mt-3">
        <span className={style.textStyle}>Observation Notes</span>
      </div>
      <ReactTextArea
        observationalNotes={observationalNotes}
        handleObservationChange={handleObservationChange}
        isOverrideAvailable={isOverrideAvailable}
      />
      <SaveBtn
        label="Save"
        disabled={isOverrideAvailable || !isChanged}
        onClick={handleSaveAndNext}
      />
    </>
  );
};
export default Observation;
