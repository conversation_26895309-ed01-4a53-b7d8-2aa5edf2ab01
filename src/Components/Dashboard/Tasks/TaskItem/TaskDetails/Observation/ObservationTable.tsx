import React, { useContext, useRef, useState } from 'react';
import { Table, Input, Button, Space, Select, Typography, Spin } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import type { InputRef, TableColumnType, TableColumnsType } from 'antd';
import type { FilterDropdownProps } from 'antd/es/table/interface';
import Highlighter from 'react-highlight-words';
import _ from 'lodash'
import useObservation from './hook/useObservation';
import { extractBasedOnLanguageMod, globalReferencesMapper } from '../../../../../../Utilities/Common/utils';
import { ReactTextArea } from '../FormTable/ReactTextArea';
import { activityDetailsContext } from '../../TaskItem';

interface DataType {
    observation_category_id?: number;
    observation_category?: any;
    observation_name_id?: number;
    observation_name?: any;
    observation_val?: any;
}

interface OptionType {
    value: number | string;
    text: string;
}
export interface ObservationValType {
    reference_id: number
    reference_name_l10n: ReferenceNameL10n
    reference_information: any
    reference_category_id: number
}

export interface ReferenceNameL10n {
    [key: string]: any
}

type DataIndex = keyof DataType;

const ObservationTable: React.FC = () => {
    const [searchText, setSearchText] = useState<string>('');
    const [searchedColumn, setSearchedColumn] = useState<string>('');
    const searchInput = useRef<InputRef>(null);
    const { care_calendar_id } = useContext(activityDetailsContext);
    const observationVal: ObservationValType[] = globalReferencesMapper(10012900)

    const mapper = (data: string[]) => {
        console.log("Mapper input:", data);
        if (observationVal.length > 0) {
            const values = observationVal
                .filter(item => data.includes(item.reference_id.toString()))
                .map(item => extractBasedOnLanguageMod(item.reference_name_l10n, 'en'));

            if (values.length > 0) {
                const result = values.join(", ");
                console.log("Mapper output:", result);
                return result;
            } else {
                const result = data.join(", ");
                console.log("Mapper output (fallback):", result);
                return result;
            }
        }
    }

    const { observationList, loading, observationNote } = useObservation({ taskId: care_calendar_id as string })

    const dataSource = () => {
        return observationList.length > 0 && observationList.map((item) => {
            const mappedObservationVal = item.observation_val ? mapper(item.observation_val) : undefined;
            console.log("Mapped Observation Value:", mappedObservationVal);
            return {
                observation_category_id: item.observation_category_id,
                observation_category: extractBasedOnLanguageMod(item.observation_category, 'en'),
                observation_name_id: item.observation_name_id,
                observation_name: extractBasedOnLanguageMod(item.observation_name, 'en'),
                observation_val: mappedObservationVal
            };
        });
    }
    // if (observationList.length === 0) {
    //     return
    // }
    const filter: OptionType[] = _.uniqBy(observationList, 'observation_category_id').map((item: any) => ({
        value: item.observation_category_id,
        text: extractBasedOnLanguageMod(item.observation_category, 'en')
    }));

    const getColumnSearchProps = (dataIndex: DataIndex): TableColumnType<DataType> => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
            <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
                {
                    dataIndex === "observation_category" ?
                        (
                            <Select
                                showSearch
                                placeholder={`Select ${dataIndex}`}
                                optionFilterProp="children"
                                value={selectedKeys[0]}
                                style={{ display: "block", marginBottom: "10px" }}
                                virtual={false}
                                onChange={(value) => setSelectedKeys(value ? [value] : [])}
                                filterOption={(input, option) =>
                                    (option?.children as unknown as string)?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                                }
                            >
                                {filter?.map((option) => (
                                    <Select.Option key={option?.value} value={option?.text}>
                                        {option?.text}
                                    </Select.Option>
                                ))}
                            </Select>
                        ) : (
                            <Input
                                ref={searchInput}
                                placeholder={`Search ${dataIndex}`}
                                value={selectedKeys[0]}
                                onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                                style={{ marginBottom: 8, display: 'block' }}
                            />
                        )
                }

                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                        icon={<SearchOutlined onPointerEnterCapture={undefined} onPointerLeaveCapture={undefined} />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button
                        onClick={() => {
                            clearFilters && handleReset(clearFilters)
                            setSearchText((selectedKeys as string[])[0]);
                            setSearchedColumn(dataIndex);
                            handleSearch(selectedKeys as string[], confirm, dataIndex)
                            close()
                        }}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Reset
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: (filtered: boolean) => (
            <SearchOutlined style={{ color: filtered ? '#1677ff' : undefined }} onPointerEnterCapture={undefined} onPointerLeaveCapture={undefined} />
        ),
        onFilter: (value, record) => {
            if (dataIndex === undefined) return false;

            const recordValue = record[dataIndex];
            return recordValue !== undefined && recordValue !== null
                ? recordValue.toString().toLowerCase().includes((value as string).toLowerCase())
                : false;
        },
        onFilterDropdownOpenChange: (visible) => {
            if (visible) {
                setTimeout(() => searchInput.current?.select(), 100);
            }
        },
        render: (text) =>
            searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleSearch = (
        selectedKeys: string[],
        confirm: FilterDropdownProps['confirm'],
        dataIndex: DataIndex,
    ) => {
        confirm();
        setSearchText(selectedKeys[0]);
        setSearchedColumn(dataIndex);
    };

    const handleReset = (clearFilters: () => void) => {
        clearFilters();
        setSearchText('');
        setSearchedColumn('')
    };

    const columns: TableColumnsType<DataType> = [
        {
            title: 'Category Name',
            dataIndex: 'observation_category',
            key: 'observation_category',
            ...getColumnSearchProps('observation_category'),
            render: (item, index) => {
                return (
                    <div>
                        {extractBasedOnLanguageMod(index.observation_category, 'en')}
                    </div>
                )
            }
        },
        {
            title: 'Observation Name',
            dataIndex: 'observation_name',
            key: 'observation_name',
            ...getColumnSearchProps('observation_name'),
            render: (item, index) => {
                return (
                    <div>
                        {extractBasedOnLanguageMod(index.observation_name, 'en')}
                    </div>
                )
            }
        },
        {
            title: 'Observation Value',
            dataIndex: 'observation_val',
            key: 'observation_val',
            ...getColumnSearchProps('observation_val')
        },
    ];
    // if (observationVal.length !== 0) {
    //     return <Spin/>
    // }
    return (
        <>
            <Table columns={columns} dataSource={dataSource() || []} loading={loading} />
            <h4 style={{ color: "gray" }}>Observation Notes</h4>
            {
                observationNote && <ReactTextArea handleObservationChange={() => { }} observationalNotes={observationNote} isOverrideAvailable={true} />
            }
        </>
    );
};

export default ObservationTable;
