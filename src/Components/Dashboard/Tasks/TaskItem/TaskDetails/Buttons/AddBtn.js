import React from "react";
import { Button } from "antd";
import { PlusOutlined } from "@ant-design/icons";

export const AddBtn = ({
  buttonText,
  handleAdd,
  count,
  validation,
  isOverrideAvailable,
}) => {
  return (
    <Button
      onClick={handleAdd}
      size="large"
      style={{
        backgroundColor: "#EC5800",
        color: "white",
        marginBottom: 16,
      }}
      disabled={count === 2 || validation || isOverrideAvailable}
      icon={<PlusOutlined />}
    >
      {buttonText}
    </Button>
  );
};
