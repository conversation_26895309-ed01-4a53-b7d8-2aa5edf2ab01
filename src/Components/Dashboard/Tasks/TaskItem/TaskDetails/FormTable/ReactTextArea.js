import React from "react";
import { Box } from "../../../../../../Utilities/Common/utils";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

export const ReactTextArea = ({
  observationalNotes,
  handleObservationChange,
  isOverrideAvailable,
}) => {
  const modules = {
    toolbar: [[{ list: "ordered" }]],
  };

  const formats = ["list"];
  return (
    <Box>
      <ReactQuill
        placeholder="Add Notes"
        value={observationalNotes}
        theme="snow"
        onChange={handleObservationChange}
        readOnly={isOverrideAvailable}
        modules={modules}
        formats={formats}
      />
    </Box>
  );
};
