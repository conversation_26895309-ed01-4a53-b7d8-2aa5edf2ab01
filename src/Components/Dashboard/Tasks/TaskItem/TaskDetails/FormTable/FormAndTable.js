import React from "react";
import { Table } from "antd";

export const FormAndTable = ({
  data,
  mergedColumns,
  components,
  pagination,
}) => {
  return (
    <Table
      components={{
        body: {
          cell: components,
        },
      }}
      loading={data === null || data === undefined}
      dataSource={data}
      rowClassName="editable-row"
      columns={mergedColumns}
      pagination={pagination}
    />
  );
};
