import React from "react";
import { <PERSON>ert, Space, Button } from "antd";

const AntAlert = ({ onAccept, onDecline, message, description, type }) => {
  return (
    <Alert
      style={{
        position: "absolute",
        top: "-3%",
        left: "50%",
        transform: "translate(-50%, -40%)",
      }}
      message={message}
      description={description}
      type={type}
      action={
        <Space direction="horizontal">
          {onAccept && (
            <Button size="small" type="primary" onClick={onAccept}>
              Accept
            </Button>
          )}
          {onDecline && (
            <Button size="small" danger ghost onClick={onDecline}>
              Decline
            </Button>
          )}
        </Space>
      }
    />
  );
};

export default AntAlert;
