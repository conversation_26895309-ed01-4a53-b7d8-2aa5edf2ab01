
import React from 'react';
import { globalReferences<PERSON>apper, extractBasedOnLanguageMod } from "Utilities/Common/utils";
import pdConfig from './pdConfig.json';
import { Card, CardContent, Typography, Grid, Divider } from '@mui/material';
import dayjs from 'dayjs';
import useGetFormData from '../../useGetFormData';

const formatValue = (key, value, options) => {
  if (value == null || value === '') return null;

  if (key.toLowerCase().includes('date')) {
    return dayjs(value).format('LLL');
  }

  if (key === 'number_of_months_pregnant_2') {
    return Number(value).toFixed(1);
  }

  if (options) {
    const option = options.find(opt => opt.reference_id === value);
    return option ? extractBasedOnLanguageMod(option.reference_name_l10n) : value;
  }

  return String(value);
};

const FormItem = ({ label, value }) => (
  <>
    <Grid item xs={6}>
      <Typography variant="body2">{label}</Typography>
    </Grid>
    <Grid item xs={6}>
      <Typography variant="body2">{value}</Typography>
    </Grid>
    <Grid item xs={12}>
      <Divider />
    </Grid>
  </>
);

const PDForm = ({ activityId, careCalendarId }) => {
  console.log('PDForm rendered with:', { activityId, careCalendarId });

  const { savedActivityForms } = useGetFormData({ activityId, careCalendarId });
  console.log('savedActivityForms:', savedActivityForms);

  if (!savedActivityForms?.result?.[0]) {
    return null;
  }

  const formData = savedActivityForms.result[0];

  const isCattlePresent = formData.is_cattle_pregnant == 1000105001||1000105002;

  if (isCattlePresent) {
    const formItems = pdConfig.PDForm
      .map(configItem => {
        const value = configItem.key === 'form_completion_date'
          ? savedActivityForms.form_completion_date
          : formData[configItem.key];

        const options = configItem.category_id ? globalReferencesMapper(configItem.category_id) : null;
        const formattedValue = formatValue(configItem.key, value, options);

        return formattedValue ? { ...configItem, formattedValue } : null;
      })
      .filter(Boolean);
    if (formItems.length === 0) {
      return null;
    }

    return (
      <Card style={{ width: '760px', backgroundColor: 'white', marginBottom: '20px' }}>
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="subtitle1" fontWeight="bold">Label</Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="subtitle1" fontWeight="bold">Value</Typography>
            </Grid>
            <Grid item xs={12}>
              <Divider />
            </Grid>
            {formItems.map(({ key, label, formattedValue }) => (
              <FormItem key={key} label={label} value={formattedValue} />
            ))}
          </Grid>
        </CardContent>
      </Card>
    );
  } else {
    return null;
  }
};

export default PDForm;