
import React from 'react';
import useGetFormData from '../../useGetFormData';
import { Card, CardContent, Typography, Grid, Divider,Box } from '@mui/material';
import CMTConfig from './CMTConfig.json';
import { useSelector } from "react-redux";
const { extractBasedOnLanguage } = require("@krushal-it/common-core");

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString();
};

const capitalizeWords = (text) => {
  const lowercaseWords = ['in', 'of', 'and'];
  return text
    .split(' ')
    .map((word, index) => 
      (index === 0 || !lowercaseWords.includes(word.toLowerCase())) ? 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() :
      word.toLowerCase()
    )
    .join(' ');
};

const AnimalCard = ({ animal, config, userLanguage }) => {
  const extraInfoKeys = ['ear_tag', 'animal_breed', 'animal_dob', 'age_in_years', 'stage', 'number_of_months_pregnant', 'months_of_calvings', 'no_of_calvings'];
  const cmtScoreItems = config.filter(item => item.key.includes('_teat_cmt_score'));
  const notesItem = config.find(item => item.key === 'note');

  return (
    <Card variant="outlined" sx={{ width: '100%', maxWidth: '860px', mb: 2,boxShadow:3}}>
      <Box sx={{padding: 2 }}>
        <Grid container spacing={2}>
          {extraInfoKeys.map((key) => (
            <Grid item xs={6} key={key}>
              <Typography variant="body2" fontWeight={'bold'}>
                <strong>{capitalizeWords(key.replace(/_/g, ' '))}  :  </strong>
                {key === 'animal_dob' ? formatDate(animal[key]) : animal[key] || 'N/A'}
              </Typography>
            </Grid>
          ))}
        </Grid>
      </Box>
      <Divider sx={{ my: 1, borderBottomWidth: 4, bgcolor: '#dedcdc' ,width:'800px',ml:2}} />
      <CardContent>
        <Grid container spacing={2}>
          <Grid container item xs={12} spacing={2}>
            <Grid item xs={6}>
              <Typography variant="subtitle2"><strong>Label</strong></Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="subtitle2"><strong>Value</strong></Typography>
            </Grid>
          </Grid>
          {cmtScoreItems.map((item) => (
            <React.Fragment key={item.key}>
              <Grid container item xs={12} spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2">{item.label}:</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">
                    {item.values[animal[item.key]] || 'N/A'}
                  </Typography>
                </Grid>
              </Grid>
            </React.Fragment>
          ))}
          {notesItem && (
            <Grid container item xs={12} spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2">{notesItem.label}:</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2">
                  {animal.note ? extractBasedOnLanguage(animal.note, userLanguage) : 'N/A'}
                </Typography>
              </Grid>
            </Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  );
};

const CMTForm = ({ activityId, careCalendarId }) => {
  const { savedActivityForms } = useGetFormData({ activityId, careCalendarId });
  const animals = savedActivityForms?.animals || [];
  const userLanguage = useSelector((state) => state.user.userLanguage);

    return (
    <div className="p-4">
      <div style={{overflowY:"scroll", maxHeight: 600,scrollbarWidth: 'none',
          msOverflowStyle: 'none' }}>
        {animals.map((animal) => (
          <div key={animal.animal_visual_id || animal.animal_id}>
            <AnimalCard animal={animal} config={CMTConfig.CMTForm} userLanguage={userLanguage} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default CMTForm;

