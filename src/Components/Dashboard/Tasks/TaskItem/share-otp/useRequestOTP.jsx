import { clearUserToken } from 'Components/user/action';
import {useCallback, useState} from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { instance } from 'Services/api.service';

const useRequestOTP = () => {
    const navigate = useNavigate()
    const dispatch = useDispatch();
    const userToken = useSelector((state) => state.user.userToken);
    const [loading, setLoading] = useState(false)

    const requestOTP = useCallback( async(careCalendarId)=>{
        try {
            setLoading(true)
            const response = await instance({
                url: `v2/cdpl/verify/otp/${careCalendarId}`,
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    token: userToken.accessToken,
                },
            });
            if (response.status === 200 || response.status === 201) {
                setLoading(false)
                return response?.data?.otp
            }
        } catch (error) {
            setLoading(false)
            if ('response' in error && error.response.status === 401) {
                dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
            }
        }
    },[])
  return {
    requestOTP,
    loading
  }
}

export default useRequestOTP