import { useCallback } from "react";
import { Button, message } from "antd";
import useRequestOTP from "./useRequestOTP";
import shareOTPTemplate from "./shareOTPTemplate";

const ShareOTP = ({ details, name = "Share OTP", type = "farmer_contact" }) => {
  const { requestOTP, loading } = useRequestOTP();

  const handleShareOtp = useCallback(async () => {
    const data = await requestOTP(details?.care_calendar_id);
    if (data && !loading && details[type] !== undefined) {
      const encodedTemplate = encodeURIComponent(
        shareOTPTemplate(details, data)
      );
      let shareContact = details[type];
      if (shareContact.length === 10 && !shareContact.startsWith("+91")) {
        shareContact = "+91".concat(shareContact);
      }
      const url = `https://wa.me/${shareContact}?text=${encodedTemplate}`;

      let link = document.createElement("a");
      link.href = url;
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      message.error("Otp is not available");
    }
  }, [details]);

  return (
    <Button
      style={{ background: "#ec6237", color: "#fff", height: 40 }}
      onClick={handleShareOtp}
      loading={loading}
    >
      {name}
    </Button>
  );
};

export default ShareOTP;
