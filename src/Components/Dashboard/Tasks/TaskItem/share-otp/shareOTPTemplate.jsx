
import React from 'react'
import { formatedDate } from 'Utilities/Common/utils';
import { extractBasedOnLanguageMod } from 'Utilities/Common/utils';

const shareOTPTemplate = (details, data) => {
    const template = `
*Activity Name*: ${extractBasedOnLanguageMod(details?.activity_name_l10n,'en')}
*Service Request Number*: ${details?.ticket_number}
*Visit Date*: ${formatedDate(details?.activity_date)}
    
If you accept the total cost of the service, please share the OTP ${data}.
`;
    return template
}

export default shareOTPTemplate