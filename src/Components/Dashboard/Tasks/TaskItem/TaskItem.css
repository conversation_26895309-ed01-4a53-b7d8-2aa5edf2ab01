.taskItem__header {
  /* border: 1px solid silver; */
  padding: 1rem;
  background-color: #4c4c4c;
  color: #fff;
}

.taskItem__body {
  padding: 1rem;
  /* border: 1px solid silver; */
}

.taskItem__task-data {
  /* border: 1px solid red; */
  display: flex;
}

.taskItem__task-notes {
  width: 400px;
  /* border: 1px solid silver; */
  margin-left: 20px;
}

.taskItem__notes-header {
  /* border: 1px solid silver; */
  padding: 1rem;
  background-color: #ec6237;
  color: #fff;
}

.taskItem__notes-container {
  /* border: 1px solid red; */
  position: relative;
  height: 350px;
}

.taskItem__notes-div {
  border: 1px solid silver;
  height: calc(350px - 65px) !important;
  /* height: 300px; */
  padding: 5px;
  overflow-y: scroll;
  background-color: #fff;
}

.taskItem__action-div {
  border: 1px solid silver;
  background-color: #fff;
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 65px;
  padding: 5px;
  display: flex;
  align-items: center;
}

.taskItem__send-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  /* border: 1px solid silver; */
  background-color: #ec6237;
  color: #fff;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  margin-left: auto;
  cursor: pointer;
  outline: none;
  border: none;
}

.taskItem__text-input-div {
  width: calc(100% - 50px);
}

.taskItem__note-item {
  /* border: 1px solid silver; */
  background-color: #ececec;
  margin-bottom: 10px;
  border-radius: 5px;
  padding: 8px 10px;
  font-size: 13px;
}

.assign-btn {
  background-color: #ec6237;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
}

.disease-img__div {
  display: flex;
  align-items: center;
  border: 1px solid silver;
  margin-bottom: 1rem;
  background-color: #fff;
  border-radius: 5px;
  font-size: 14px;
  max-width: 400px;
}

.taskItem__follow-ups {
  width: 400px;
  /* border: 1px solid silver; */
  margin-left: 20px;
}

.taskItem__follow-ups-header {
  /* border: 1px solid silver; */
  padding: 1rem;
  background-color: #00C8F4;
  color: #fff;
}

.taskItem__follow-ups-div {
  border: 1px solid silver;
  max-height: 350px;
  padding: 10px;
  overflow-y: scroll;
  background-color: #fff;
}

.taskItem__follow-ups-item {
  /* border: 1px solid #ececec; */
  padding: 10px;
  margin-bottom: 10px;
  background-color: #f9f9f9;
  cursor: pointer;
}

.taskItem__follow-ups-item .activity-name {
    font-size: 15px;
    font-weight: 600;
    color: #4c4c4c;
}

.taskItem__follow-ups-item .activity-status {
  font-size: 14px;
  /* font-weight: 600; */
  /* color: #4c4c4c; */
}

.taskItem__follow-ups-item-selected {
  /* border: 1px solid silver; */
  padding: 10px;
  margin-bottom: 10px;
  background-color: #f5fdff;
  border: 1px solid #00C8F4;
  cursor: pointer;
}

.taskItem__follow-ups-item-selected .activity-name {
  font-size: 15px;
  font-weight: 600;
  color: #4c4c4c;
}

.taskItem__follow-ups-item-selected .activity-status {
font-size: 14px;
/* font-weight: 600; */
/* color: #4c4c4c; */
}

.taskTab__floating-menu {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  background-color: #3d4146;
  color: #ffffff;
  margin-bottom: 1rem;
  border: 1xp solid silver;
}
.task__tab-unselected {
  font-weight: 600;
  font-size: 17px;
  display: flex;
  align-items: center;
  /* border: 1px solid silver; */
  margin-right: 1rem;
  cursor: pointer;
  color: #bdbdbd;
  padding: 0.5rem;
}

.task__tab-selected {
  font-weight: 600;
  font-size: 17px;
  color: #ec6237;
  display: flex;
  align-items: center;
  /* border: 1px solid silver; */
  margin-right: 1rem;
  border-bottom: 3px solid #ec6237;
  padding: 0.5rem;
}