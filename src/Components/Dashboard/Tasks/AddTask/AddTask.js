import "./AddTask.css";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import {useSelector, useDispatch} from "react-redux"
import Row from "react-bootstrap/Row";
import Col from "react-bootstrap/Col";
import Button from "react-bootstrap/Button";
import Modal from "react-bootstrap/Modal";
import { useState, useEffect, Suspense } from "react";
import axios from "axios";
import resizeFile from "../../../../Services/file-resizer.service";
import { clearUserToken } from "../../../user/action";
import {
  BASE_URL,
  instance,
  tokenFailureHandler,
} from "../../../../Services/api.service";
import Loader from "../../../Loader/Loader";
import ToastersService from "../../../../Services/toasters.service";

import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import moment from "moment";
import { getWalletBalanceRedux } from "../../../../redux/slices/ppuu/action";
import {Modal as AntdModal} from "antd";
import {ExclamationCircleOutlined} from "@ant-design/icons";
import { extractBasedOnLanguageMod } from "../../../../Utilities/Common/utils";

const {CS_TEAM, MARKETING_TEAM, SALES_TEAM, FINANCE_TEAM, VET_OPS, SALES_OPS, withAuthorization} = require('../../../user/authorize')

const AddTask = (props) => {
  console.log(props)
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const dispatch = useDispatch();
  const userToken = useSelector((state)=>state.user.userToken);
  const walletBalance = useSelector((state)=> state.ppuu.walletBalance)
  const [formConfigLoader, setFormConfigLoader] = useState(true);
  const [farmerName, setFarmerName] = useState();
  const [cattleEarTagNo, setCattleEarTagNo] = useState();
  const [activityConfig, setActivityConfig] = useState();
  const [activityOptions, setActivityOptions] = useState([]);
  const [statusOptions, setStatusOptions] = useState([]);
  const [modal,contextHolder] = AntdModal.useModal();
  const [autocompleteValue, setAutocompleteValue] = useState(null);

  // const [medicines, setMedicines] = useState([]);
  // let [selectedMedicines, setSelectedMedicines] = useState([]);
  const [initialDate,setInitialDate] = useState(null)
  let [createTaskForm, setCreateTaskForm] = useState({
    // animal_id: location.search.split("=")[1],
    calendar_activity_status: 1000300001,
  });
  const [creatingTaskLoader, setCreatingTaskLoader] = useState(false);
  const farmerId = params?.farmerId ? params?.farmerId : props?.farmerId
  const cattleId = params?.cattleId ? params?.cattleId : props?.cattleId
  const staffId = props?.staffId ? props?.staffId : ""
  // AI specific
  // const [AIselected, setAIselected] = useState(false);
  // const [AIconfig, setAIconfig] = useState();

  const getFarmersList = async () => {
    let reqBody = {
      page_number: 1,
    };
    console.log(reqBody);
    try {
      // const response = await axios.post(
      //   BASE_URL + "/customer-listing",
      //   reqBody,
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       // refreshToken: localStorage.getItem("refreshToken")
      //       "X-App-Id": "123456789",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/admin-customers",
        method: "POST",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      console.log(response);
      if (response.status === 201) {
        if (response.data.result === false) {
          tokenFailureHandler(response.data);
          // navigate("/login");
        }
        console.log("Farmers List", response.data.data);
        for (let farmer of response.data.data) {
          if (farmer.customer_id == farmerId ) {
            // setFarmerName(
            //   farmer.customer_name_l10n.en !== ""
            //     ? farmer.customer_name_l10n.en
            //     : farmer.customer_name_l10n.ul
            // );
            setFarmerName(
              farmer.customer_name_l10n.en ? farmer.customer_name_l10n.en : farmer.customer_name_l10n.ul
            )
          }
        }
        getCattleListByFarmerId();
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
      }
    }
  }

  const getCattleListByFarmerId = async () => {
    try {
      // const response = await axios.get(
      //   BASE_URL + "/animal?customer_id=" + params.farmerId,
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "123456789",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/animal?customer_id=" +  farmerId,
        method: "GET",
        // data: reqBody,
        headers: {
          token: userToken.accessToken,
        },
      });
      // console.log(response);
      if (response.status === 200) {
        if (response.data.result === false) {
          tokenFailureHandler(response.data);
          navigate("/login");
        }
        response.data.data = response.data.data.filter(
          (item) => item.animal_id !== null
        );
        for (let cattle of response.data.data) {
          // console.log(cattle);
          if (cattle.animal_id == cattleId) {
            if (cattle.ear_tag_1) {
              setCattleEarTagNo(cattle.ear_tag_1);
            }
          }
        }
        // console.log("Cattle list by farmer", response.data.data);
        // getActivityConfig();
        setActivityConfigurationsFromGlobalRef()
      }
    } catch (error) {
      console.log(error);
      if (error.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
      }
    }
  }

  const getActivityConfig = async () => {
    try {
      // const response = await axios.get(BASE_URL + "/references/care-calendar", {
      //   headers: {
      //     token: localStorage.getItem("accessToken"),
      //     "X-App-Id": "123456789",
      //   },
      // });
      const response = await instance({
        url: "/references/care-calendar",
        method: "GET",
        // data: reqBody,
        headers: {
          token: userToken.accessToken,
        },
      });
      if (response.status === 200) {
        if (response.data.result === false) {
          tokenFailureHandler(response.data);
          navigate("/login");
        }
        console.log("Activity Config", response.data);
        setActivityConfig({
          Preventive: response.data.data.activity_config.Preventive,
          Curative: response.data.data.activity_config.Curative,
          Reproductive: response.data.data.activity_config.Reproductive,
        });
        setStatusOptions(response.data.data.status_config);
        // setMedicines(response.data.data.medicine_config);

        //AI specific
        // setAIconfig({
        //   ai_form_company_of_semen:
        //     response.data.data.care_alendar_config.ai_form_company_of_semen,
        //   ai_form_heat_date_and_time:
        //     response.data.data.care_alendar_config.ai_form_heat_date_and_time,
        //   ai_form_id_of_bull:
        //     response.data.data.care_alendar_config.ai_form_id_of_bull,
        //   ai_form_name_of_bull:
        //     response.data.data.care_alendar_config.ai_form_name_of_bull,
        //   ai_form_suitable_for_ai_based_on_discharge:
        //     response.data.data.care_alendar_config
        //       .ai_form_suitable_for_ai_based_on_discharge,
        //   ai_form_suitable_for_ai_based_on_heat:
        //     response.data.data.care_alendar_config
        //       .ai_form_suitable_for_ai_based_on_heat,
        // });

        setFormConfigLoader(false);
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
      }
    }
  }
const setActivityConfigurationsFromGlobalRef = ()=>{
  setActivityConfig({
    Preventive: global.activityReferences[1000270001] , // Preventive,
    Curative: global.activityReferences[1000270002],  // Curative,
    Reproductive: global.activityReferences[1000270003] // Reproductive,
  });
  setStatusOptions(global.referencesWithoutCategoryId.referenceMap.status_config);
  setFormConfigLoader(false);
}
  const createTask = async () => {
    console.log(createTaskForm);

    // if (selectedMedicines.length > 0) {
    //   selectedMedicines.forEach((item) => {
    //     const key1 = `medicine_${item.count}`;
    //     const value1 = item.medicineId;
    //     const key2 = `medicine_${item.count}_quantity`;
    //     const value2 = item.quantity;
    //     const newKeyValuePair1 = { [key1]: value1 };
    //     const newKeyValuePair2 = { [key2]: value2 };
    //     createTaskForm = {
    //       ...createTaskForm,
    //       ...newKeyValuePair1,
    //       ...newKeyValuePair2,
    //     };
    //     console.log("Create task form", createTaskForm);
    //   });
    // }
    if (props.isPPUU && walletBalance?.balance < 250) {
      // ToastersService.failureToast("You insufficent balance !")
      modal.confirm({
        title: "You have insufficient balance !",
        icon: <ExclamationCircleOutlined />,
        content:
          "Do you want add balance ?",
        okText: "Ok",
        cancelText: "Cancel",
        onOk: () => {
          // navigate(`/dashboard/staffs/${props.staffId}`);
          // navigate({
          //   to : `/dashboard/staffs/${props.staffId}`,
          //   option : {
          //     state : 4
          //   }
          // })
          navigate(`/staffs/${props.staffId}`, { state: true });

        },
      });
    }else{
    setCreatingTaskLoader(true);
    try {
      // const response = await axios.post(
      //   BASE_URL + "/activity/create-task",
      //   { ...createTaskForm, entity_uuid: params.cattleId },
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "123456789",
      //     },
      //   }
      // );
      const response = await instance({
        url: props.isPPUU ? "/v2/tasks" : "/activity/create-task",
        method: "POST",
        data: { ...createTaskForm, entity_uuid: cattleId, staff_id : staffId },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      console.log(response);
      if (response.status === 201) {
        setCreatingTaskLoader(false);
        ToastersService.successToast("Task created successfully!");
        sendAutoGeneratedNote(response.data.id);
        navigate(
          "/tasks/" +
            response.data.id +
            `${
              createTaskForm.activity_id == 1000470001
                ? "?popup=" + createTaskForm.activity_date
                : ""
            }`
        );
      } else {
        throw Error("Task not created! Try again.");
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error);
      setCreatingTaskLoader(false);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
      }
    }
  }
  }

  const addTaskNote = async (noteItem, calendarId) => {
    try {
      // const response = await axios.post(
      //   BASE_URL + "/common/add-task-note",
      //   {
      //     note: noteItem,
      //     calendar_id: calendarId,
      //   },
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "123456789",
      //     },
      //   }
      // );
      const response = await instance({
        url: "/common/add-task-note",
        method: "POST",
        data: {
          note: noteItem,
          calendar_id: calendarId,
        },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      console.log("Add task response", response);
      if (response.status === 201) {
      } else {
        throw Error("Note not sent! Try again.");
      }
    } catch (error) {
      console.log(error);
      // ToastersService.failureToast(error);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
      }
    }
  }

  useEffect(() => {
    getFarmersList();
    if (props?.isPPUU) {
      dispatch(getWalletBalanceRedux(props?.staffId,userToken,navigate))
    }
  }, []);

  const navigateBackHandler = () => {
    navigate(-1);
  };

  const sendAutoGeneratedNote = (calendarId) => {
    let noteItem = "";
    for (let item of statusOptions) {
      if (item.reference_id == createTaskForm.calendar_activity_status) {
        noteItem =
          "New Status: " + item.reference_name_l10n.en
            ? item.reference_name_l10n.en
            : item.reference_name_l10n.ul;
      }
    }

    // if (selectedMedicines.length > 0) {
    //   console.log(selectedMedicines);
    //   for (let item of selectedMedicines) {
    //     if (item.medicineId !== null) {
    //       for (let medicine of medicines) {
    //         if (medicine.reference_id == item.medicineId) {
    //           console.log("Found!", item);
    //           noteItem = noteItem.concat(
    //             `, Medicine(${item.count}): ${medicine.reference_name_l10n.ul
    //               ? medicine.reference_name_l10n.ul
    //               : medicine.reference_name_l10n.en}`
    //           );
    //         }
    //       }
    //     }
    //     if (item.quantity !== null) {
    //       noteItem = noteItem.concat(
    //         `, Quantity(${item.count}): ` + item.quantity
    //       );
    //     }
    //   }
    // }

    console.log(noteItem);

    addTaskNote(noteItem, calendarId);
  };

  const activityTypeSelectHandler = (event) => {
    console.log(
      event.target.value,
      activityConfig[event.target.value]?.filter(
        (item) => item.activity_id !== 1000180011
      )
    );
    let activityIds = [1000180011, 1000180015,1000180013]
    const options = activityConfig[event.target.value]?.filter((item)=>{
      if (!activityIds.includes(item.activity_id)) {
        return item
      }
    })
    setActivityOptions(options)
    setAutocompleteValue(null)
    // setActivityOptions(
    //   activityConfig[event.target.value].options.filter(
    //     (item) => item.activity_id !== 1000180011
    //   )
    // );
    setCreateTaskForm({ ...createTaskForm, activity_type: event.target.value, activity_id : null });
  };

  const dateChangeHandler = (date) => {
     if (date && !date.getHours() && !date.getMinutes()) {
       date.setHours(0);
       date.setMinutes(0);
     }
     setInitialDate(date)
       const inputDate = new Date(date);
       const utcDateString = inputDate.toISOString();
       setCreateTaskForm({
         ...createTaskForm,
         activity_date: moment(date).format("YYYY-MM-DD"),
         activity_date_gmt : utcDateString
       });
   };

  const activitySelectHandler = (event, activityId) => {
    // console.log(event.target.value);

    // if (activityId == 1000470001) {
    //   setAIselected(true);
    // } else {
    //   setAIselected(false);
    // }

    console.log({
      ...createTaskForm,
      activity_id: parseInt(activityId),
    });
    setCreateTaskForm({
      ...createTaskForm,
      activity_id: parseInt(activityId),
    });
  };

  // const statusSelectHandler = (event) => {
  //   console.log({
  //     ...createTaskForm,
  //     calendar_activity_status: event.target.value,
  //   });
  //   setCreateTaskForm({
  //     ...createTaskForm,
  //     calendar_activity_status: event.target.value,
  //   });
  // };

  // const chooseMedicineHandler = (value, count) => {
  //   console.log(value, count);

  //   for (let item of selectedMedicines) {
  //     if (item.count == count) {
  //       item.medicineId = value.reference_id;
  //     }
  //   }

  //   console.log(selectedMedicines);
  //   setSelectedMedicines(selectedMedicines);
  // };

  // const medicineQuantityHandler = (event, count) => {
  //   console.log(event.target.value, count);
  //   for (let item of selectedMedicines) {
  //     if (item.count == count) {
  //       item.quantity = event.target.value;
  //     }
  //   }

  //   console.log(selectedMedicines);
  //   setSelectedMedicines(selectedMedicines);
  // };

  const createTaskHandler = () => {
    console.log(createTaskForm);

    //check for AI specific conditions
    // let AISpecificError = false;
    // if (createTaskForm.activity_id == 1000470001) {
    //   console.log("AI task checks");
    //   if (!createTaskForm.ai_form_suitable_for_ai_based_on_heat || !createTaskForm.ai_form_suitable_for_ai_based_on_discharge) {
    //     console.log("Dropdowns not selected");
    //     AISpecificError = true;
    //   } else {
    //     console.log("Dropdowns selected");
    //     if (createTaskForm.ai_form_suitable_for_ai_based_on_heat == 1000105001 && createTaskForm.ai_form_suitable_for_ai_based_on_discharge == 1000105001) {
    //       if (!createTaskForm.ai_form_id_of_bull || (createTaskForm.ai_form_id_of_bull && createTaskForm.ai_form_id_of_bull.trim() == "")) {
    //         console.log("throw error");
    //         AISpecificError = true;
    //       } else if (!createTaskForm.ai_form_name_of_bull || (createTaskForm.ai_form_name_of_bull && createTaskForm.ai_form_name_of_bull.trim() == "")) {
    //         console.log("throw error");
    //         AISpecificError = true;
    //       } else if (!createTaskForm.ai_form_heat_date_and_time) {
    //         console.log("throw error");
    //         AISpecificError = true;
    //       } else if (!createTaskForm.ai_form_company_of_semen) {
    //         console.log("throw error");
    //         AISpecificError = true;
    //       }
    //     }
    //   }
    // }

    if (!createTaskForm.activity_date) {
      ToastersService.failureToast("Select activity date!");
    } else if (!createTaskForm.activity_id) {
      ToastersService.failureToast("Select activity!");
    } else if (!createTaskForm.calendar_activity_status) {
      ToastersService.failureToast("Select activity status!");
      // } else if (AISpecificError == true) {
      //   ToastersService.failureToast("AI specific fields are mandatory!");
    } else {
      createTask();
    }
  };

  // const addMedicineHandler = () => {
  //   if (selectedMedicines.length > 9) {
  //     ToastersService.failureToast("Maximum 10 medicines are allowed!");
  //   } else {
  //     setSelectedMedicines([
  //       ...selectedMedicines,
  //       {
  //         count: selectedMedicines.length + 1,
  //         medicineId: null,
  //         quantity: null,
  //       },
  //     ]);
  //   }
  // };

  // const AIbasedOnHeatHandler = (event) => {
  //   console.log(event.target.value);
  //   setCreateTaskForm({
  //     ...createTaskForm,
  //     ai_form_suitable_for_ai_based_on_heat: event.target.value,
  //   });
  // };

  // const AIbasedOnDischargeHandler = (event) => {
  //   console.log(event.target.value);
  //   setCreateTaskForm({
  //     ...createTaskForm,
  //     ai_form_suitable_for_ai_based_on_discharge: event.target.value,
  //   });
  // };

  // const AIbullIdHandler = (event) => {
  //   console.log(event.target.value);
  //   setCreateTaskForm({
  //     ...createTaskForm,
  //     ai_form_id_of_bull: event.target.value,
  //   });
  // };

  // const AIbullNameHandler = (event) => {
  //   console.log(event.target.value);
  //   setCreateTaskForm({
  //     ...createTaskForm,
  //     ai_form_name_of_bull: event.target.value,
  //   });
  // };

  // const AIheatDateTimeHandler = (event) => {
  //   console.log(event.target.value);
  //   setCreateTaskForm({
  //     ...createTaskForm,
  //     ai_form_heat_date_and_time: event.target.value,
  //   });
  // };

  // const AIcompanyOfSemenHandler = (event) => {
  //   console.log(event.target.value);
  //   setCreateTaskForm({
  //     ...createTaskForm,
  //     ai_form_company_of_semen: event.target.value,
  //   });
  // };

  return (
    <Suspense fallback={<Loader/>}>
      {!props.isPPUU && (
      <div className="mb-4">
        <p className="farmer-details__back-btn" onClick={navigateBackHandler}>
          <i className="fa fa-angle-double-left" aria-hidden="true"></i> Go back
        </p>

        <p
          className="mb-0 mt-3"
          style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
        >
          Tasks / Add New Task
        </p>
        <p
          className="m-0"
          style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
        >
          Add Task
        </p>
      </div>
      )}

      {formConfigLoader === true ? (
        <div className="text-center my-5">
          <Loader />
        </div>
      ) : (
        <div className="add-task__form pb-5">
          {props.isPPUU ? "": <div className="add-task__form-header">Add Task</div>}
          <div>
            <Row>
              <Col lg={4} md={6} sm={12}>
                <small>Farmer Name</small>
                <input
                  type="text"
                  style={{
                    width: "100%",
                    textTransform: "capitalize",
                    cursor: "not-allowed",
                  }}
                  className="input-field"
                  value={farmerName}
                  disabled
                />
              </Col>
              <Col lg={4} md={6} sm={12}>
                <small>Cattle Ear Tag Number</small>
                <input
                  type="text"
                  style={{ width: "100%", cursor: "not-allowed" }}
                  className="input-field"
                  value={cattleEarTagNo}
                  disabled
                />
              </Col>
              <Col lg={4} md={6} sm={12}>
                <small>Select Date and Time*</small>
                {/* <input
                  type="date"
                  style={{ width: "100%" }}
                  className="input-field"
                  onChange={dateChangeHandler}
                /> */}
                <DatePicker
                style={{ width: "100%" }}
                className="input-field input-width"
                selected={initialDate}
                onChange={dateChangeHandler}
                showTimeSelect
                dateFormat="Pp"
                timeIntervals={15}
                timeCaption="Time"
                placeholderText="Please select a date and time"
                />
              </Col>
            </Row>
            {/* <hr /> */}
            <br/>
            <Row>
              <Col lg={4} md={6} sm={12}>
                <small>Select Activity Type*</small>
                <select
                  className="input-field"
                  style={{ width: "100%" }}
                  defaultValue={"DEFAULT"}
                  onChange={activityTypeSelectHandler}
                >
                  <option disabled value="DEFAULT">
                    Select
                  </option>
                  {activityConfig &&
                    Object.keys(activityConfig).map((key) => {
                      return (
                        <option
                          key={key}
                          value={key}
                          style={{ textTransform: "capitalize" }}
                        >
                          {key}
                        </option>
                      );
                    })}
                </select>
              </Col>
              <Col lg={4} md={6} sm={12}>
                <small>Select Activity*</small>
                <Autocomplete
                  options={activityOptions}
                  getOptionLabel={(option) => extractBasedOnLanguageMod(option?.activity_name_l10n,'en')
                  }
                  value={autocompleteValue}

                  disabled={activityOptions.length === 0}
                  sx={{
                    width: "100%",
                    marginTop: "5px",
                    background: "#fff",
                  }}
                  renderInput={(params) => (
                    <TextField {...params} label="Search..." />
                  )}
                  // onChange={activitySelectHandler}
                  onChange={(event, value) =>{
                    setAutocompleteValue(value)
                    activitySelectHandler(event, value.activity_id)
                  }
                  }
                />
              </Col>
              {/* <Col lg={4} md={6} sm={12}>
                <small>Status*</small>
                <select
                  className="input-field"
                  style={{ width: "100%" }}
                  defaultValue={"DEFAULT"}
                  onChange={statusSelectHandler}
                >
                  <option disabled value="DEFAULT">
                    Select
                  </option>
                  {statusOptions.map((option) => {
                    return (
                      <option
                        key={option.reference_id}
                        value={option.reference_id}
                      >
                        {option.reference_name_l10n.en
                          ? option.reference_name_l10n.en
                          : option.reference_name_l10n.ul}
                      </option>
                    );
                  })}
                </select>
              </Col> */}
            </Row>
            {/* <hr /> */}

            {/* AI specific */}
            {/* {AIselected == true && (
              <>
                AI Form:
                <Row className="mt-2">
                  <Col lg={4} md={6} sm={12}>
                    <small>Suitable for AI based on heat</small>
                    <select
                      className="input-field"
                      style={{ width: "100%" }}
                      defaultValue={"DEFAULT"}
                      onChange={AIbasedOnHeatHandler}
                    >
                      <option disabled value="DEFAULT">
                        Select
                      </option>
                      {AIconfig &&
                        AIconfig.ai_form_suitable_for_ai_based_on_heat.category_options.map(
                          (option) => {
                            return (
                              <option
                                key={option.reference_id}
                                value={option.reference_id}
                                style={{ textTransform: "capitalize" }}
                              >
                                {option.reference_name_l10n.ul
                                  ? option.reference_name_l10n.ul
                                  : option.reference_name_l10n.en}
                              </option>
                            );
                          }
                        )}
                    </select>
                  </Col>
                  <Col lg={4} md={6} sm={12}>
                    <small>Suitable for AI based on discharge</small>
                    <select
                      className="input-field"
                      style={{ width: "100%" }}
                      defaultValue={"DEFAULT"}
                      onChange={AIbasedOnDischargeHandler}
                    >
                      <option disabled value="DEFAULT">
                        Select
                      </option>
                      {AIconfig &&
                        AIconfig.ai_form_suitable_for_ai_based_on_discharge.category_options.map(
                          (option) => {
                            return (
                              <option
                                key={option.reference_id}
                                value={option.reference_id}
                                style={{ textTransform: "capitalize" }}
                              >
                                {option.reference_name_l10n.ul
                                  ? option.reference_name_l10n.ul
                                  : option.reference_name_l10n.en}
                              </option>
                            );
                          }
                        )}
                    </select>
                  </Col>
                  <Col lg={4} md={6} sm={12}>
                    <small>Bull Id</small>
                    <input
                      type="text"
                      placeholder="Type here..."
                      disabled={
                        !(createTaskForm.ai_form_suitable_for_ai_based_on_discharge ==
                          1000105001) ||
                        !(createTaskForm.ai_form_suitable_for_ai_based_on_heat ==
                            1000105001)
                      }
                      style={{ width: "100%" }}
                      className="input-field"
                      onChange={AIbullIdHandler}
                    />
                  </Col>
                  <Col lg={4} md={6} sm={12}>
                    <small>Bull Name</small>
                    <input
                      type="text"
                      placeholder="Type here..."
                      style={{ width: "100%" }}
                      className="input-field"
                      disabled={
                        !(createTaskForm.ai_form_suitable_for_ai_based_on_discharge ==
                          1000105001) ||
                        !(createTaskForm.ai_form_suitable_for_ai_based_on_heat ==
                            1000105001)
                      }
                      onChange={AIbullNameHandler}
                    />
                  </Col>
                  <Col lg={4} md={6} sm={12}>
                    <small>Heat date and time</small>
                    <input
                      type="datetime-local"
                      style={{ width: "100%" }}
                      className="input-field"
                      disabled={
                        !(createTaskForm.ai_form_suitable_for_ai_based_on_discharge ==
                          1000105001) ||
                        !(createTaskForm.ai_form_suitable_for_ai_based_on_heat ==
                            1000105001)
                      }
                      onChange={AIheatDateTimeHandler}
                    />
                  </Col>
                  <Col lg={4} md={6} sm={12}>
                    <small>Company of semen</small>
                    <select
                      className="input-field"
                      style={{ width: "100%" }}
                      defaultValue={"DEFAULT"}
                      disabled={
                        !(createTaskForm.ai_form_suitable_for_ai_based_on_discharge ==
                          1000105001) ||
                        !(createTaskForm.ai_form_suitable_for_ai_based_on_heat ==
                            1000105001)
                      }
                      onChange={AIcompanyOfSemenHandler}
                    >
                      <option disabled value="DEFAULT">
                        Select
                      </option>
                      {AIconfig &&
                        AIconfig.ai_form_company_of_semen.category_options.map(
                          (option) => {
                            return (
                              <option
                                key={option.reference_id}
                                value={option.reference_id}
                                style={{ textTransform: "capitalize" }}
                              >
                                {option.reference_name_l10n.ul
                                  ? option.reference_name_l10n.ul
                                  : option.reference_name_l10n.en}
                              </option>
                            );
                          }
                        )}
                    </select>
                  </Col>
                </Row>
                <hr />
              </>
            )} */}

            {/* <p>Medicines:</p>
            {selectedMedicines.length > 0 &&
              selectedMedicines.map((medicine) => (
                <Row key={medicine.count}>
                  <Col lg={4} md={6} sm={12}>
                    <small>Medicine {medicine.count}</small>
                    <Autocomplete
                      options={medicines}
                      getOptionLabel={(option) =>
                        option.reference_name_l10n.en
                          ? option.reference_name_l10n.en
                          : option.reference_name_l10n.ul
                      }
                      sx={{
                        width: "100%",
                        marginTop: "5px",
                        background: "#fff",
                      }}
                      renderInput={(params) => (
                        <TextField {...params} label="Search..." />
                      )}
                      onChange={(event, value) =>
                        chooseMedicineHandler(value, medicine.count)
                      }
                    />
                  </Col>
                  <Col lg={4} md={6} sm={12}>
                    <small>Quantity {medicine.count}</small>
                    <input
                      className="input-field"
                      style={{ width: "100%" }}
                      type="number"
                      placeholder="eg: 2"
                      onChange={(event) =>
                        medicineQuantityHandler(event, medicine.count)
                      }
                    />
                  </Col>
                </Row>
              ))}

            <div
              className="add-cattle__add-disease-btn mt-3"
              onClick={addMedicineHandler}
            >
              + Add medicine
            </div>

            <hr /> */}

            {creatingTaskLoader === true ? (
              <div
                className="text-center"
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Loader />{" "}
                <div
                  className="ms-2"
                  style={{ fontSize: "14px", color: "#4c4c4c" }}
                >
                  Creating task...
                </div>
              </div>
            ) : (
              <div className="add-task__create-btn" onClick={createTaskHandler}>
                Create
              </div>
            )}
          </div>
        </div>
      )}
      {contextHolder}
    </Suspense>
  );
};

// export default AddTask;
export default withAuthorization(AddTask, [CS_TEAM])