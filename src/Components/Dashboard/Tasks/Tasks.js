import * as React from "react";
import { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import Moment from "react-moment";
import Pagination from "@mui/material/Pagination";
import Stack from "@mui/material/Stack";
import TextField from "@mui/material/TextField";
import "./Tasks.css";
import { instance } from "../../../Services/api.service";
import { tokenFailureHandler } from "../../../Services/api.service";
import ToastersService from "../../../Services/toasters.service";
import { clearUserToken } from "../../user/action";
import { getColumnSearchProps } from "../TableAndUtils/ColumnSearchInputUtil";
import { Table } from "antd";
import { Button, Modal } from "react-bootstrap";
import { Autocomplete } from "@mui/material";

const {
  CS_TEAM,
  MARKETING_TEAM,
  SALES_TEAM,
  FINANCE_TEAM,
  VET_OPS,
  SALES_OPS,
  withAuthorization,
} = require("../../user/authorize");

const Tasks = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userData = useSelector((state) => state.user.userData);
  const userToken = useSelector((state) => state.user.userToken);
  const [activitiesLoader, setActivitesLoader] = useState(false);
  const [totalPages, setTotalPages] = useState();
  const [currentPage, setCurrentPage] = useState(1);
  const [activities, setActivities] = useState([]);
  let [activityStatusArr, setActivityStatusArr] = useState([]);
  const activityConfig = [
    {
      text: "Preventive",
      value: 1000270001,
    },
    { 
      text: "Curative", 
      value: 1000270002,
    },
    {
      text: "Reproductive",
      value: 1000270003,
    },
    {
      text: "Data Collection",
      value: 1000270004,
    },
  ];
  let [selectedActivities, setSelectedActivities] = useState([]);
  let [filterParams, setFilterParams] = useState({});
  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const [paravetsList, setParavetList] = useState([]);
  const [selectedParavet, setSelectedParavet] = useState();
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef(null);

  const getRoutePlan = async (pageNumber, params) => {
    let reqBody = {
      page_number: pageNumber,
      page_limit: 100,
    };
    if (params !== null || params !== undefined) {
      reqBody = { ...reqBody, ...params };
    }
    setActivitesLoader(true);
    console.log(reqBody);
    try {
      const response = await instance({
        url: "/activity/task-list",
        method: "POST",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      console.log("get route plan", response);
      if (response?.status === 201) {
        if (response.data.result === false) {
          tokenFailureHandler(response.data);
        }
        setTotalPages(response.data.total_page);

        for (let item of response.data.result) {
          item.isSelected = false;
        }

        console.log("Activities List", response.data.result);
        setActivities(response.data.result);
        setActivitesLoader(false);
      }
    } catch (error) {
      console.log(error);
      setActivitesLoader(false);
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const getActivityConfig = async () => {
    console.log("Hello");
    try {
      const response = await instance({
        url: "/references/care-calendar",
        method: "GET",
        headers: {
          token: userToken.accessToken,
        },
      });
      if (response?.status === 200) {
        if (response?.data?.result === false) {
          tokenFailureHandler(response?.data);
          navigate("/login");
        }
        // console.log("Status Config", response?.data?.data?.status_config);
        // const status = response?.data?.data?.status_config?.map((item) => ({
        //   text: item?.reference_name_l10n?.en || item?.reference_name_l10n?.ul,
        //   value: item?.reference_id,
        // }));
        // setActivityStatusArr(status);
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const setActivityConfigurationsFromGlobalRef = () => {
    const status =
      global?.referencesWithoutCategoryId?.referenceMap?.status_config?.map(
        (item) => ({
          text: item?.reference_name_l10n?.en || item?.reference_name_l10n?.ul,
          value: item?.reference_id,
        })
      );
    setActivityStatusArr(status);
  };
  console.log("activityStatusArr", activityStatusArr);
  const getParavetsList = async () => {
    try {
      const response = await instance({
        url: "/get-staff",
        method: "POST",
        data: { f_staff_type: 1000230004 },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      console.log("Paravets list", response);
      if (response?.status === 201) {
        setParavetList(response?.data);
      } else {
        throw Error("Couldn't fetch paravets list!");
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const reassignTask = async () => {
    console.log("Assign task req body", {
      care_calendar: selectedActivities,
      staff_id: selectedParavet,
    });
    try {
      const response = await instance({
        url: "/activity/re-assign",
        method: "PUT",
        data: {
          care_calendar: selectedActivities,
          staff_id: selectedParavet,
        },
        headers: {
          token: userToken.accessToken,
        },
      });
      console.log(response);
      if (response?.status === 200) {
        if (response?.data?.result === true) {
          ToastersService.successToast("Tasks reassigned successfully!");
          setShow(false);
          setSelectedActivities([]);
          setSelectedParavet(undefined);
          getRoutePlan(currentPage, filterParams);
        }
      } else {
        throw Error("Cannot reassign task! Something went wrong.");
      }
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  const changePageNumber = (event, value) => {
    console.log("New page number", value);
    setCurrentPage(value);
    getRoutePlan(value, filterParams);
  };

  useEffect(() => {
    // console.log(
    //   "Page Filters",
    //   JSON.parse(localStorage.getItem("pageFilters"))
    // );
    // if (
    //   localStorage.getItem("pageFilters") &&
    //   JSON.parse(localStorage.getItem("pageFilters")).taskList
    // ) {
    //   const filters = JSON.parse(localStorage.getItem("pageFilters")).taskList
    //     .filters;
    //   setFilterParams(filters);
    //   getRoutePlan(currentPage, filters);
    // } else {
    //   getRoutePlan(currentPage, filterParams);
    // }
    getRoutePlan(currentPage, filterParams);
    setActivityConfigurationsFromGlobalRef();
  }, [userToken, userData]);

  const activityNameInputHandler = (searchText) => {
    filterParams = { ...filterParams, f_activity_name: searchText };
    setFilterParams(filterParams);
    getRoutePlan(1, filterParams);
  };

  const farmerNameInputHandler = (searchText) => {
    filterParams = { ...filterParams, f_farmer_name: searchText };
    setFilterParams(filterParams);
    getRoutePlan(1, filterParams);
  };

  const farmerContactInputHandler = (searchText) => {
    filterParams = { ...filterParams, f_farmer_mobile: searchText };
    setFilterParams(filterParams);
    getRoutePlan(1, filterParams);
  };

  const earTagInputHandler = (searchText) => {
    filterParams = { ...filterParams, f_ear_tag: searchText };
    setFilterParams(filterParams);
    getRoutePlan(1, filterParams);
  };

  const villageChangeHandler = (searchText) => {
    filterParams = { ...filterParams, f_village: searchText };
    setFilterParams(filterParams);
    getRoutePlan(1, filterParams);
  };

  const dateRangeInputHandler = (startDate, endDate) => {
    const formattedDateStart = startDate?.format("YYYY-MM-DD");
    const formattedDateEnd = endDate?.format("YYYY-MM-DD");
    if (formattedDateStart && formattedDateEnd) {
      filterParams = {
        ...filterParams,
        f_date_from: formattedDateStart,
        f_date_to: formattedDateEnd,
      };
      setFilterParams(filterParams);
      getRoutePlan(1, filterParams);
    } else {
      filterParams = {
        ...filterParams,
        f_date_from: "",
        f_date_to: "",
      };
      getRoutePlan(1, filterParams);
    }
  };

  const dateRangeInputHandlerCompletionDate = (startDate, endDate) => {
    const formattedDateStart = startDate?.format("YYYY-MM-DD");
    const formattedDateEnd = endDate?.format("YYYY-MM-DD");
    if (formattedDateStart && formattedDateEnd) {
      filterParams = {
        ...filterParams,
        f_completion_date_from: formattedDateStart,
        f_completion_date_to: formattedDateEnd,
      };
      setFilterParams(filterParams);
      getRoutePlan(1, filterParams);
    } else {
      filterParams = {
        ...filterParams,
        f_completion_date_from: "",
        f_completion_date_to: "",
      };
      getRoutePlan(1, filterParams);
    }
  };

  const statusChangeHandler = (value) => {
    filterParams = { ...filterParams, f_activity_status: value };
    setFilterParams(filterParams);
    getRoutePlan(1, filterParams);
  };

  const categoryChangeHandler = (value) => {
    filterParams = { ...filterParams, f_activity_category: value };
    setFilterParams(filterParams);
    getRoutePlan(1, filterParams);
  };

  const ticketIdChangeHandler = (searchText) => {
    filterParams = { ...filterParams, f_complaint_number: searchText };
    setFilterParams(filterParams);
    getRoutePlan(1, filterParams);
  };

  const paravetChangeHandler = (searchText) => {
    filterParams = { ...filterParams, f_paravet_name: searchText };
    setFilterParams(filterParams);
    getRoutePlan(1, filterParams);
  };

  const onReassignActivities = () => {
    console.log("Selected activities", selectedActivities);
    getParavetsList();
    setShow(true);
  };

  const navigateToActivityDetails = (careCalendarId) => {
    let taskListFilters = {
      taskList: {
        filters: filterParams,
      },
    };
    localStorage.setItem("pageFilters", JSON.stringify(taskListFilters));
    // navigate("/tasks/" + careCalendarId);
    localStorage.setItem("taskNavigation", true);
  };

  const clearFiltersHandler = () => {
    localStorage.removeItem("pageFilters");
    window.location.reload();
  };

  const paravetSelectHandler = (event, staffId) => {
    console.log(event, staffId);
    setSelectedParavet(staffId);
  };

  const confirmReassignHandler = () => {
    console.log(selectedActivities, selectedParavet);
    setSelectedActivities([]);
    reassignTask();
  };

  const onSelectChange = (newSelectedRowKeys) => {
    console.log("selectedRowKeys changed: ", newSelectedRowKeys);
    setSelectedActivities(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedActivities,
    onChange: onSelectChange,
    getCheckboxProps: (record) => {
      const isDisabled = [
        "Visit Completed",
        "Visit Abandoned",
        "Visit Cancelled",
      ].includes(record.status);
      return {
        disabled: isDisabled,
      };
    },
  };

  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    if (dataIndex === "ticketId") {
      ticketIdChangeHandler(selectedKeys[0]);
    } else if (dataIndex === "activityName") {
      activityNameInputHandler(selectedKeys[0]);
    } else if (dataIndex === "farmerContact") {
      farmerContactInputHandler(selectedKeys[0]);
    } else if (dataIndex === "village") {
      villageChangeHandler(selectedKeys[0]);
    } else if (dataIndex === "cattleEarTag") {
      earTagInputHandler(selectedKeys[0]);
    } else if (dataIndex === "paravet") {
      paravetChangeHandler(selectedKeys[0]);
    } else if (dataIndex === "farmerName") {
      farmerNameInputHandler(selectedKeys[0]);
    } else if (dataIndex === "category") {
      const value = activityConfig.find(
        (item) => item?.text === selectedKeys[0]
      )?.value;
      categoryChangeHandler(value);
    } else if (dataIndex === "status") {
      const value = activityStatusArr.find(
        (item) => item?.text === selectedKeys[0]
      ).value;
      statusChangeHandler(value);
    } else if (dataIndex === "activityDate") {
      dateRangeInputHandler(selectedKeys[0], selectedKeys[1]);
    } else if (dataIndex === "completionDate"){
      dateRangeInputHandlerCompletionDate(selectedKeys[0],selectedKeys[1])
    }
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
    confirm();
  };

  const handleReset = (clearFilters, confirm, dataIndex) => {
    if (dataIndex === "ticketId") {
      ticketIdChangeHandler("");
    } else if (dataIndex === "activityName") {
      activityNameInputHandler("");
    } else if (dataIndex === "farmerContact") {
      farmerContactInputHandler("");
    } else if (dataIndex === "village") {
      villageChangeHandler("");
    } else if (dataIndex === "cattleEarTag") {
      earTagInputHandler("");
    } else if (dataIndex === "paravet") {
      paravetChangeHandler("");
    } else if (dataIndex === "farmerName") {
      farmerNameInputHandler("");
    } else if (dataIndex === "category") {
      categoryChangeHandler("");
    } else if (dataIndex === "status") {
      statusChangeHandler("");
    } else if (dataIndex === "activityDate") {
      dateRangeInputHandler(null, null);
    } else if(dataIndex === "completionDate"){
      dateRangeInputHandlerCompletionDate(null,null)
    }
    clearFilters();
    setSearchText("");
    confirm();
  };

  const tableData = activities?.map((task) => ({
    key: task?.care_calendar_id,
    ticketId: task?.complaint_number ? task?.complaint_number : "-",
    activityDate: task?.activity_date,
    completionDate:task?.completion_date,
    category: task?.activity_category_name,
    activityName: task?.activity_name,
    farmerName: task?.customer_name,
    farmerContact: task?.mobile_number,
    village: task?.village_name,
    cattleEarTag: task?.animal_ear_tag,
    status: task?.activity_status,
    paravet: task?.paravet_name,
  }));

  const tableColumns = [
    {
      title: "Ticket Id",
      dataIndex: "ticketId",
      key: "ticketId",
      width: "7%",
      ...getColumnSearchProps(
        "ticketId",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
    {
      title: "Activity Date",
      dataIndex: "activityDate",
      key: "activityDate",
      width: "10%",
      ...getColumnSearchProps(
        "activityDate",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
      render: (text) =>
        text !== null ? <Moment format="DD MMM, YYYY">{text}</Moment> : "-",
    },
    {
      title:"Completion Date",
      dataIndex: "completionDate",
      key: "completionDate",
      ...getColumnSearchProps(
        "completionDate",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
      render: (text) =>
        text !== null ? <Moment format="DD MMM, YYYY">{text}</Moment> : "-",
    },
    {
      title: "Category",
      dataIndex: "category",
      key: "category",
      width: "5%",
      ...getColumnSearchProps(
        "category",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn,
        activityConfig
      ),
    },
    {
      title: "Activity Name",
      dataIndex: "activityName",
      key: "activityName",
      width: "12%",
      ...getColumnSearchProps(
        "activityName",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
    {
      title: "Farmer Name",
      dataIndex: "farmerName",
      key: "farmerName",
      width: "15%",
      ...getColumnSearchProps(
        "farmerName",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
      render: (text, record) => (
        <a
          className="farmers__farmer-name"
          onClick={() => navigateToActivityDetails(record?.key)}
          href={`/tasks/${record.key}`}
        >
          {text}
        </a>
      ),
    },
    {
      title: "Farmer Contact",
      dataIndex: "farmerContact",
      key: "farmerContact",
      width: "10%",
      ...getColumnSearchProps(
        "farmerContact",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
      render: (text) => (
        <>
          <i className="fa fa-phone" aria-hidden="true" />
          <span>{text}</span>
        </>
      ),
    },
    {
      title: "Village",
      dataIndex: "village",
      key: "village",
      width: "5%",
      ...getColumnSearchProps(
        "village",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
    {
      title: "Cattle Ear Tag",
      dataIndex: "cattleEarTag",
      key: "cattleEarTag",
      width: "10%",
      ...getColumnSearchProps(
        "cattleEarTag",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: "10%",
      align: "center",
      ...getColumnSearchProps(
        "status",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn,
        activityStatusArr
      ),
      render: (text) => <span className="badge text-bg-success">{text}</span>,
    },
    {
      title: "Paravet",
      dataIndex: "paravet",
      key: "paravet",
      width: "15%",
      ...getColumnSearchProps(
        "paravet",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
  ];

  return (
    <>
      {userToken && userData && (
        <>
          <div className="my-3">
            <p
              className="m-0"
              style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
            >
              Tasks
            </p>
            <p
              className="m-0"
              style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
            >
              Task List
            </p>
          </div>
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Button
              style={{ backgroundColor: "#ec6237", color: "#fff" }}
              onClick={() => navigate("/schedule-task")}
            >
              Schedule Task
            </Button>
          </div>
          <div style={{ display: "flex" }}>
            {selectedActivities?.length > 0 && (
              <Button
                variant="primary"
                onClick={onReassignActivities}
                style={{ background: "#ec6237", color: "#fff", border: "none" }}
              >
                Re-assign ({selectedActivities?.length})
              </Button>
            )}
            {filterParams !== null ||
              (filterParams !== undefined && (
                <div
                  className="clear-filters-btn"
                  onClick={clearFiltersHandler}
                >
                  Clear Filters
                </div>
              ))}
          </div>
          <div className="tasks__tasks-table">
            <Table
              rowSelection={rowSelection}
              columns={tableColumns}
              dataSource={tableData}
              loading={activitiesLoader}
              pagination={false}
              scroll={{ x: 'max-content' }}
            />
          </div>

          <Modal show={show} centered onHide={handleClose}>
            <Modal.Header closeButton>
              <Modal.Title>
                Re-assign {selectedActivities.length} tasks?
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              {paravetsList.length > 0 && (
                <div>
                  <small>
                    Choose a paravet
                    <span style={{ color: "red" }}>*</span>
                  </small>
                  <Autocomplete
                    options={paravetsList}
                    getOptionLabel={(option) =>
                      option.staff_name_l10n.en
                        ? option.staff_name_l10n.en
                        : option.staff_name_l10n.ul
                    }
                    sx={{
                      width: "100%",
                      marginTop: "5px",
                      background: "#fff",
                    }}
                    renderInput={(params) => (
                      <TextField {...params} label="Search..." />
                    )}
                    onChange={(event, value) => {
                      // Check if value is not null before accessing its properties
                      if (value && value.staff_id) {
                        paravetSelectHandler(event, value.staff_id);
                      } else {
                        // Handle the case when value is null or does not have staff_id
                        // For example, you might want to clear the selected value or show an error message.
                        paravetSelectHandler(event, null);
                      }
                    }}
                  />
                </div>
              )}
            </Modal.Body>
            <Modal.Footer>
              <Button
                variant="primary"
                disabled={!selectedParavet}
                style={{ background: "#673AB7", color: "#fff", border: "none" }}
                onClick={confirmReassignHandler}
              >
                Confirm
              </Button>
            </Modal.Footer>
          </Modal>

          <div
            className="mt-2 mb-5"
            style={{ display: "flex", justifyContent: "center" }}
          >
            <Stack spacing={2}>
              <Pagination
                count={totalPages}
                color="secondary"
                onChange={changePageNumber}
              />
            </Stack>
          </div>
        </>
      )}
    </>
  );
};

// export default Tasks;
export default withAuthorization(Tasks, [CS_TEAM, VET_OPS]);
