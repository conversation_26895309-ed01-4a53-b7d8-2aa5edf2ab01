import { instance } from "../../../../../Services/api.service";
import ToastersService from "../../../../../Services/toasters.service";
import { clearUserToken } from "../../../../user/action";

export const scheduleTaskApi = async (payload,userToken,dispatch,navigate) => {
  try {
    const response = await instance({
      url: "/schedule/tasks/animal",
      method: "POST",
      data : payload,
      headers: {
        token: userToken?.accessToken,
      },
    });
    if (response?.status === 200 || 201) {
        // ToastersService.successToast("Task scheduled successfully")
      return response?.data
    }else if (response?.status === 500) {
      ToastersService.failureToast("Something went wrong!")
    }
  } catch (error) {
    if (error?.response?.status === 401) {
      dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
    }
  }
};
