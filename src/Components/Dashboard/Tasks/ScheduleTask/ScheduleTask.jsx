import { Fragment, useState, useRef, useEffect } from "react";
import { Box, Typo<PERSON>, Stack, Button } from "@mui/material";
import { LoadingButton } from "@mui/lab";
import ScheduleTaskActivity from "./component/scheduleTaskActivity/ScheduleTaskActivity";
import TaskApplicableTo from "./component/taskApplicableTo/TaskApplicableTo";
import AssignTask from "./component/assignTask/AssignTask";
import PreviewScheduleTask from "./component/preview/PreviewScheduleTask";
import { useDispatch, useSelector } from "react-redux";
import { useScheduleTaskMutation } from "../../activityManagement/reducer/activityManagementSlice";
import { setResetScheduleState } from "./scheduleTaskSlice/scheduleTaskSlice";
import {Modal} from "antd"
import { Link } from "react-router-dom";

const ScheduleTask = () => {
  const [page, setPage] = useState(1);
  const dispatch = useDispatch()
  const [modal, modalContext] = Modal.useModal()
  const [,{isLoading}] = useScheduleTaskMutation({fixedCacheKey : "shared-update-post"})
  const componentMap = {
    1: ScheduleTaskActivity,
    2: TaskApplicableTo,
    3: AssignTask,
    4: PreviewScheduleTask,
  };
  const Comp = componentMap[page];
  const ref = useRef();
  const handleSaveAndNext = () => {
    switch (page) {
      case 1:
        ref.current?.callPage1();
        break;
      case 2:
        ref.current?.callPage2();
        break;
      case 3:
        ref.current?.callPage3();
        break;
      case 4:
        ref?.current?.callPage4();
        break;
      default:
        break;
    }
  };
  const scheduleInformation = useSelector(
    (state) => state?.scheduleTask
  );
 console.log("scheduleInformation",scheduleInformation)
  useEffect(() => {
    if (scheduleInformation?.activity?.activity_id) {
      modal.confirm({
        title: "Confirm",
        content : "We have found your previous data!",
        onOk: () => {},
        okText: "Continue with previous data",
        cancelText: "Reset data",
        onCancel: () => {
          dispatch(setResetScheduleState())
          ref?.current?.resetActivity()
        }
      })
    }
  }, [])  
  return (
    <Fragment>
      <Box sx={{height: 60, display:"flex", alignItems:"center", padding: 1}}>
        <Box>
        <Link className="farmer-details__back-btn" to="/tasks">
              <i className="fa fa-angle-double-left" aria-hidden="true"></i> Go
              back
            </Link>
          {/* <Button sx={{textTransform:"none",padding:1, marginTop: "10px"}} onClick={()=>window.history.back()}>Go to tasks</Button> */}
          <Typography variant="h5" component="h5">
             Schedule Activity
          </Typography>
          <Box sx={{ backgroundColor: "#ff6e1b", height: 5, width: 100 }}></Box>
        </Box>
      </Box>
      <Box
        sx={{
          height: 500,
          width: "80%",
          maxHeight: 500,
          minHeight: 500,
          padding: 2,
          margin: "50px 0 0 130px", 
          boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 8px",
          borderRadius: "16px",
          backgroundColor :"#fff"
        }}
      >
        <Box sx={{height: 400, maxHeight: 400, minHeight: 400}}>
          <Comp ref={ref} setPage={setPage}  />
        </Box>
        <Stack
          spacing={{ xs: 1, sm: 2 }}
          justifyContent="flex-end"
          flexWrap="nowrap"
          direction="row"
        >
          <Button
            variant="outlined"
            style={{
              textTransform: "none",
              margin: "10px",
            }}
            disabled={page === 1}
            onClick={() => setPage(page - 1)}
          >
            Back
          </Button>
          <LoadingButton
            loading={isLoading}
            variant="contained"
            style={{
              textTransform: "none",
              color: "white",
              backgroundColor: "#ff6e1b",
              margin: "10px",
            }}
            loadingPosition="start"
            onClick={handleSaveAndNext}
          >
            {page === 4 ? "Schedule" : "Save & Next"}
          </LoadingButton>
        </Stack>
      </Box>
      {modalContext}
    </Fragment>
  );
};

export default ScheduleTask;
