import { useNavigate } from "react-router-dom";
import { useCallback, useEffect } from "react";
import { useDispatch } from "react-redux";
import {Modal} from "antd"
import { setResetScheduleState } from "./scheduleTaskSlice/scheduleTaskSlice";
import { useScheduleTaskMutation } from "../../activityManagement/reducer/activityManagementSlice";
const useScheduleTaskHook = ()=>{
    const navigate = useNavigate()
    const dispatch = useDispatch();
    const [scheduleTask, {isLoading, data, isError, isSuccess,reset}] = useScheduleTaskMutation({fixedCacheKey : "shared-update-post"})
    const success = (count) => {
      Modal.success({
        content: `${count} tasks successfully scheduled`,
        onOk: ()=>{
          navigate('/tasks')
          dispatch(setResetScheduleState())
          reset()
        }
      });
    };
    const failure = (message) =>{
      Modal.error({
        content : message ? "There was no cattles to create a task" : "Task not scheduled",
        onOk: ()=>{
          navigate('/tasks')
          dispatch(setResetScheduleState())
          reset()
        }
      })
    }
    const scheduleTaskApiCall = useCallback(async (payload)=>{
       scheduleTask(payload)
    },[])

    useEffect(() => {
      if (isSuccess && data && Object.keys(data).length > 0) {
        success(data?.count);
      }else if (isSuccess && data && Object.keys(data).length === 0) {
        failure(true)
      }
      if (isError) {
        failure(false);
      }
    }, [data, isError])

    const extractPayloadForScheduleTask = (payload) => {
      // Create the modified data object
      const { preview, schedulePreview, ...modifiedData } = payload;
  
      // Modify the activity object to only include activity_id
      modifiedData.activity = {
          activity_id: modifiedData.activity.activity_id.activity_id
      };
      return modifiedData;
  };
  

    return {scheduleTaskApiCall, extractPayloadForScheduleTask , isLoading}
}

export default useScheduleTaskHook;

