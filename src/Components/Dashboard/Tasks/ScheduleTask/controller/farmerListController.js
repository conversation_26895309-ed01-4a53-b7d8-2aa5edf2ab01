import { useSelector, useDispatch } from "react-redux";
import { useEffect, useState } from "react";
import { clearUserToken } from "Components/user/action";
import { useNavigate } from "react-router-dom";
import { getFarmersReport } from "../../../../../OpenCommerce/ocrouter";

import { useGetCustomersListMutation } from "../../../activityManagement/reducer/activityManagementSlice";



const useGetFarmerListData = () =>{
    const userToken = useSelector((state) => state?.user.userToken)
    const dispatch = useDispatch()
    const navigate = useNavigate()
    const [customerList, setCustomerList] = useState([]);
    const [getCustomersList,{data , isLoading : customerIsLoading}] = useGetCustomersListMutation()
    const makePreviewCustomerData = (selectedActivities) => {
      const list = data?.report?.length > 0 && data?.report?.filter((listItem) =>
        selectedActivities?.some((item) => item === listItem?.customer_id)
      )
      // setModalTableList(list);
      const modalTableData = list && list?.map((customer) => ({
        key: customer?.customer_id,
        customerName: customer?.customer_name || "Not defined",
        contact: customer?.mobile_number || "Not defined",
        state: customer?.state || "Not defined",
        district: customer?.district || "Not defined",
        taluk: customer?.taluk || "Not defined",
        village: customer?.village || "Not defined",
      }));
      return modalTableData
    };
    
    const customerTableData = data?.report?.length > 0 && data?.report?.map((customer) => ({
        key: customer?.customer_id,
        customerName: customer?.customer_name || "Not defined",
        contact: customer?.mobile_number || "Not defined",
        state: customer?.state || "Not defined",
        district: customer?.district || "Not defined",
        taluk: customer?.taluk || "Not defined",
        village: customer?.village || "Not defined",
      }));
    const getCustomer = async () => {
        try {
          const headers = {
            useCase: "Get Customer With Location Recorded Report",
            token: userToken.accessToken, //authToken
          };
          const response = await getFarmersReport(headers);
          if (response.status === 201) {
            setCustomerList(response?.data?.report);
          }
        } catch (error) {
          if (error?.response?.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
          }
        }
      };
      // console.log(data,"customer data from rtk")
      useEffect(()=>{
        // getCustomer()
        getCustomersList()
      },[])
      return {
        customerTableData,
        makePreviewCustomerData,
        customerIsLoading
      }
}

export default useGetFarmerListData;