import { instance } from "Services/api.service";
import { useSelector, useDispatch } from "react-redux";
import { useState } from "react";
import { clearUserToken } from "Components/user/action";
import { useNavigate } from "react-router-dom";



const useGetGeoData = () =>{
    const userToken = useSelector((state) => state?.user.userToken)
    const dispatch = useDispatch()
    const navigate = useNavigate()

    const [districtList, setDistrictsList] = useState([]);
    const [talukList, setTalukasList] = useState([]);
    const [villageList, setVillagesList] = useState([]);
    const [stateList, setStateList] = useState([]);

    const getState = async () => {
        try {
          const response = await instance({
            url: "/location/state",
            method: "GET",
            headers: {
              token: userToken.accessToken,
            },
          });
          console.log("state", response?.data?.data);
          if (response.status === 200) {
            const mouldToShow = response?.data?.data?.map((item) => ({
              reference_name_l10n:
                item?.state_name_l10n?.en || item?.state_name_l10n?.ul,
              reference_id: item?.state_id,
            }));
            setStateList(mouldToShow);
          }
        } catch (error) {
          console.log(error);
          if (error?.response?.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
          }
        }
      };
    
      const getDistrictsBasedOnState = async (stateId) => {
        try {
          const response = await instance({
            url: "/location/district?state_id=" + stateId,
            method: "GET",
            headers: {
              token: userToken.accessToken,
            },
          });
          console.log("getDistrictsBasedOnState", response);
          if (response.status === 200) {
            const mouldToShow = response?.data?.data?.map((item) => ({
              reference_name_l10n:
                item?.district_name_l10n?.en || item?.district_name_l10n?.ul,
              reference_id: item?.district_id,
            }));
            setDistrictsList(mouldToShow);
          }
        } catch (error) {
          console.log(error);
          if (error?.response?.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
          }
        }
      };
    
      const getTaluksBasedOnDistrict = async (districtIds) => {
        try {
          const response = await instance({
            url: "/location/taluka?district_id=" + districtIds,
            method: "GET",
            headers: {
              token: userToken.accessToken,
            },
          });
    
          console.log("getTaluksBasedOnDistrict", response);
    
          if (response.status === 200) {
            const data = response?.data?.data;
    
            if (Array.isArray(data)) {
              const mouldToShow = data.map((item) => ({
                reference_name_l10n:
                  item?.taluk_name_l10n?.en || item?.taluk_name_l10n?.ul,
                reference_id: item?.taluk_id,
              }));
              setTalukasList(mouldToShow);
            }
          }
        } catch (error) {
          console.log(error);
          if (error?.response?.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
          }
        }
      };
    
      const getVillagesBasedOnTaluk = async ( talukId) => {
        try {
          const response = await instance({
            url: "/location/village?taluka_id=" + talukId,
            method: "GET",
            headers: {
              token: userToken.accessToken,
            },
          });
          console.log("getVillagesBasedOnTaluk", response);
          if (response.status === 200) {
            const mouldToShow = response?.data?.data?.map((item) => ({
              reference_name_l10n:
                item?.village_name_l10n?.en || item?.village_name_l10n?.ul,
              reference_id: item?.village_id,
            }));
            setVillagesList(mouldToShow);
          }
        } catch (error) {
          console.log(error);
          if (error?.response?.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
          }
        }
      };

      return {
        getState,
        stateList,  
        getDistrictsBasedOnState,
        districtList,
        getTaluksBasedOnDistrict,
        talukList,
        getVillagesBasedOnTaluk,
        villageList
      }
}

export default useGetGeoData;