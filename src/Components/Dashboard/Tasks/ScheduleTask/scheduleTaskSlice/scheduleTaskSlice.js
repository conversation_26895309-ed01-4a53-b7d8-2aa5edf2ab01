import { createSlice } from "@reduxjs/toolkit";
import dayjs from "dayjs";
import { FrequencyType, EndingConditionType } from "../../../activityManagement/schedule/recurrence/types";
const today = dayjs(new Date());
const tommorrow = today.add(1, 'day');

const defaultRecurrence = {
  startDate: today,
  endDate: tommorrow,
  frequency: FrequencyType.Daily,
  numberOfRepetitions: 1,
  weekDaysRepetition: [],
  endingCondition: EndingConditionType.EndDate,
  endingOccurrencesNumber: 0,
  isAllDay: true,
  startTime: today,
  endTime: tommorrow,
};
const initialState = {
  activity: {
    activity_id: null,
  },
  schedule: {},
  appliesTo: {
    geography: {},
    customer: {
      customer_id : []
    },
  },
  staff_type_list : [],
  preview : {},
  schedulePreview : defaultRecurrence
};


export const scheduleTaskSlice = createSlice({
  name: "schedule_data",
  initialState,
  reducers: {
    setScheduleData: (state, action) => {
      state.activity.activity_id = action.payload;
    },
    setTaskSchedule: (state, action) => {
        state.schedule = action.payload
    },
    setToApplicable: (state, action) => {
      state.appliesTo = action.payload;
    },
    setAssignTo : (state,action) =>{
      state.staff_type_list = action.payload
    },
    setPreview : (state,action) =>{
      state.preview = action.payload
    },
    setSchedulePreview : (state,action) =>{
      state.schedulePreview = action.payload
    },
    setResetScheduleState : (state) => {
      Object.assign(state, initialState);
    },
  },
});

export const { setScheduleData, setTaskSchedule, setToApplicable, setAssignTo, setPreview , setResetScheduleState, setSchedulePreview} =
  scheduleTaskSlice.actions;
export default scheduleTaskSlice.reducer;
