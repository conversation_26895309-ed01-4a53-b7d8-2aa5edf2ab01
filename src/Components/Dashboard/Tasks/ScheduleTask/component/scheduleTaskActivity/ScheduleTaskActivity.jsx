import { useRef, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, useEffect } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Grid,
  Box,
  Autocomplete,
  TextField,
} from "@mui/material";
import { useGetActivityQuery } from "../../../../activityManagement/reducer/activityManagementSlice";
import WrapperActivityRecurrenceComponent from "../../../../activityManagement/schedule/recurrence/WrapperActivityRecurrenceComponent";
import ActivityCalendar from "../../../../activityManagement/activity calender/ActivityCalendar";
import { setScheduleData } from "../../scheduleTaskSlice/scheduleTaskSlice";
import { useDispatch, useSelector } from "react-redux";
import ToastersService from "../../../../../../Services/toasters.service";
// import { useSelector } from "react-redux";

const ScheduleTaskActivity = forwardRef(({ setPage }, refs) => {
  const { register, watch, reset, control } = useForm({
    defaultValues: {
      activity_id: null,
    },
  });
  const ref = useRef();
  const dispatch = useDispatch();
  const { data } = useGetActivityQuery();
  const activity_id = watch("activity_id");
  const scheduleInformation = useSelector((state) => state?.scheduleTask);
  const frequencyDetails = scheduleInformation?.schedulePreview;
  let isWeekDays =
    frequencyDetails.frequency === "weekly" &&
    !frequencyDetails?.weekDaysRepetition?.length > 0;
  const handleSave = () => {
    ref?.current?.log();
    dispatch(setScheduleData(activity_id));
  };

  useImperativeHandle(refs, () => ({
    callPage1() {
      if (!activity_id) {
        ToastersService.failureToast("Select activity");
      } else if (isWeekDays) {
        ToastersService.failureToast("Select days");
      } else {
        setPage(2);
        handleSave();
      }
    },
    resetActivity(){
      reset({activity_id:null})
    }
  }));
  useEffect(() => {
    if (scheduleInformation?.activity?.activity_id) {
      reset({ activity_id: scheduleInformation?.activity?.activity_id });
    }
  }, [scheduleInformation?.activity?.activity_id, reset]);

  return (
    <Box sx={{ margin: 5 }}>
      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <Controller
              name="activity_id"
              control={control}
              defaultValue={null}
              render={({ field: { onChange, onBlur, value } }) => (
                <Autocomplete
                  value={value}
                  onBlur={onBlur}
                  multiple={false}
                  options={data?.report || []}
                  onChange={(event, newValue) => {
                    onChange(newValue); // Change here
                  }}
                  renderInput={(params) => (
                    <TextField {...params} label="Select activity" />
                  )}
                  renderOption={(params, option) => (
                    <li
                      {...params}
                      key={option?.activity_id}
                      value={option?.activity_id}
                    >
                      {option?.activity_name}
                    </li>
                  )}
                  isOptionEqualToValue={(option, value) =>
                    option?.activity_id === value?.activity_id
                  }
                  getOptionLabel={(option) => {
                    return option?.activity_name;
                  }}
                />
              )}
            />
          </FormControl>
          <WrapperActivityRecurrenceComponent
            ref={ref}
            activity_id={activity_id}
          />
        </Grid>
        <Grid
          item
          xs={12}
          md={6}
          // sx={{ justifyContent: "flex-end", display: "flex" }}
        >
          {scheduleInformation && (
            <ActivityCalendar
              activity_information={scheduleInformation?.schedule}
              handleHeader={true}
            />
          )}
        </Grid>
      </Grid>
    </Box>
  );
});

export default ScheduleTaskActivity;
