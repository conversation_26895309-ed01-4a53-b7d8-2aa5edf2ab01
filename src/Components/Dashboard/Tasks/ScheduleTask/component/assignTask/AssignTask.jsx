import {
  Box,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Grid,
  Typography,
  Stack
} from "@mui/material";
import { globalReferencesMapper } from "Utilities/Common/utils";
import { useImperativeHandle, forwardRef, useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { extractBasedOnLanguageMod } from "Utilities/Common/utils";
import { useDispatch, useSelector } from "react-redux";
import { setAssignTo } from "../../scheduleTaskSlice/scheduleTaskSlice";
// import { DevTool } from "@hookform/devtools";


const AssignTask = forwardRef(({ setPage }, refs) => {
  const staffList = globalReferencesMapper(10002300);
  const methods = useForm();
  const { control, watch } = methods;
  const value = watch("staff_type_id");
  const dispatch = useDispatch();
  const scheduleInfo = useSelector((state) => state?.scheduleTask);

  useImperativeHandle(refs, () => ({
    callPage3() {
      dispatch(setAssignTo(value));
      setPage(4);
    },
  }));
  useEffect(() => {
    if (scheduleInfo?.staff_type_list?.length > 0) {
      methods.reset({
        staff_type_id: scheduleInfo?.staff_type_list,
      });
    }
  }, [methods.reset]);
  return (
    <Box sx={{height:"100%"}}>
      <Stack direction="column" justifyContent="center">
      <Typography component="p" sx={{textAlign:'left', padding: 1, fontSize: "20px" }}>
       Please select staff type you want the rules to be applied to *
      </Typography>
      <Box
        sx={{  padding: 2}}
      >
        <Controller
          name="staff_type_id"
          control={control}
          defaultValue={[]} // Ensure defaultValue is set as an array
          rules={{
            required: true,
          }}
          render={({ field }) => {
            const handleChange = (event) => {
              const newValue = [...field.value]; // Create a copy of the current value array
              const referenceId = parseInt(event.target.value, 10); // Parse the value to an integer
              if (event.target.checked) {
                newValue.push(referenceId); // Add the parsed integer value to the array
              } else {
                const index = newValue.indexOf(referenceId);
                if (index !== -1) {
                  newValue.splice(index, 1); // Remove the parsed integer value from the array
                }
              }
              field.onChange(newValue); // Update the form value
            };
            return (
              <FormGroup>
                <Grid container direction="row" spacing={2}>
                  {staffList?.map((item, index) => (
                    <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={field.value.includes(item?.reference_id)}
                            onChange={handleChange}
                            value={item?.reference_id}
                          />
                        }
                        label={extractBasedOnLanguageMod(
                          item?.reference_name_l10n,
                          "en"
                        )}
                      />
                    </Grid>
                  ))}
                </Grid>
              </FormGroup>
            );
          }}
        />
      </Box>
      </Stack>
    </Box>
  );
});

export default AssignTask;
