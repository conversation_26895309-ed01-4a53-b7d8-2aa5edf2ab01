import { Button } from "antd";

const ShowSelectedBtn = ({ selectedActivities, handleFunction, intity }) => {
  return (
    <div className="d-flex justify-content-end">
      {selectedActivities?.length > 0 && (
        <Button
          variant="primary"
          className="d-flex justify-content-end"
          onClick={handleFunction}
          style={{
            background: "#ec6237",
            color: "#fff",
            border: "none",
          }}
        >
          Selected {intity} ({selectedActivities?.length})
        </Button>
      )}
    </div>
  );
};

export default ShowSelectedBtn;
