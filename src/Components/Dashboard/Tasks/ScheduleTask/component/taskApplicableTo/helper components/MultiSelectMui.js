import { Controller, useFormContext } from "react-hook-form";
import { Autocomplete, TextField } from "@mui/material";
import Checkbox from "@mui/material/Checkbox";

const MultiSelectMui = ({ dropDownOptions, dropDownName }) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  return (
    <>
      <Controller
        name={dropDownName}
        control={control}
        disabled={false}
        rules={{
          required: true,
        }}
        defaultValue={[]}
        render={({ field }) => {
          const allOptions =
            dropDownOptions?.length > 0
              ? [
                  { reference_name_l10n: "Select All", reference_id: "all"},
                  ...dropDownOptions
                    ?.slice()
                    ?.sort((a, b) =>
                      a.reference_name_l10n?.localeCompare(
                        b.reference_name_l10n
                      )
                    ),
                ]
              : [];
          return (
            <Autocomplete
              multiple
              options={allOptions}
              disableCloseOnSelect
              getOptionLabel={(option) => option?.reference_name_l10n}
              disabled={dropDownOptions?.length === 0}
              renderOption={(props, option, { selected }) => (
                <li {...props} key={option?.reference_id}>
                  <Checkbox style={{ marginRight: 8 }} checked={selected} />
                  {option?.reference_name_l10n}
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={`Select ${dropDownName}`}
                  disabled={false}
                />
              )}
              onChange={(e,newValue) => {
                if (newValue?.some((item) => item?.reference_id === "all")) {
                  field.onChange(dropDownOptions);
                } else {
                  field.onChange(newValue);
                }
              }}
              isOptionEqualToValue={(option,value)=>
               option["reference_id"] === value["reference_id"]
              }
              value={field?.value}
            />
          );
        }}
      />
      {errors[dropDownName] && (
        <p style={{ color: "red" }}>{dropDownName} is required.</p>
      )}
    </>
  );
};

export default MultiSelectMui;
