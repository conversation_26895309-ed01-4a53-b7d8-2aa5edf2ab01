import { Controller, useFormContext } from "react-hook-form";
import { Autocomplete, TextField } from "@mui/material";

const SingleSelectMui = ({ dropDownOptions, dropDownName }) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  let labelName;
  switch (dropDownName) {
    case "state_for_district":
    case "state_for_taluk":
    case "state_for_village":
      labelName = "State";
      break;
    case "district_for_taluk":
    case "district_for_village":
      labelName = "District";
      break;
    case "taluk_for_village":
      labelName = "Taluk";
      break;
    default:
      labelName = dropDownName;
  }
  return (
    <>
      <Controller
        name={dropDownName}
        control={control}
        disabled={false}
        rules={{
          required: true,
        }}
        render={({ field }) => {
          return (
            <Autocomplete
              {...field}
              options={
                dropDownOptions?.length > 0
                  ? dropDownOptions
                      ?.slice()
                      ?.sort((a, b) =>
                        a.reference_name_l10n?.localeCompare(
                          b.reference_name_l10n
                        )
                      )
                  : []
              }
              getOptionLabel={(option) => {
                return (
                  option?.reference_name_l10n ||
                  dropDownOptions.find((item) => item?.reference_id === option)
                    ?.reference_name_l10n
                );
              }}
              disabled={dropDownOptions?.length === 0}
              renderOption={(props, option) => (
                <li {...props} key={option?.reference_id}>
                  {option?.reference_name_l10n}
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={`Select ${labelName}`}
                  disabled={false}
                />
              )}
              onChange={(event, newValue) => {
                field.onChange(newValue)
              }}
              value={field?.value}
            />
          );
        }}
      />
      {errors[dropDownName] && (
        <p style={{ color: "red" }}>{labelName} is required.</p>
      )}
    </>
  );
};

export default SingleSelectMui;
