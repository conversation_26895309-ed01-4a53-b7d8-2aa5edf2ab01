import { Box } from "@mui/material";
import { Table } from "antd";

const OperateTableData = ({
  selectedActivities,
  setSelectedActivities,
  tableDataSource,
  tableColumns,
  intity,
  loading
}) => {
  const onSelectChange = (newSelectedRowKeys) => {
    console.log("selectedRowKeys changed: ", newSelectedRowKeys);
    setSelectedActivities(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedActivities,
    onChange: onSelectChange,
    getCheckboxProps: (record) => ({
      disabled: undefined,
    }),
  };

  return (
    <Box>
      {/* <p
        className="m-0"
        style={{
          fontSize: "18px",
          fontWeight: "700",
          color: "#222222",
        }}
      >
        Select {intity} from the Table
      </p> */}
      <Table
        rowSelection={rowSelection}
        columns={tableColumns}
        dataSource={tableDataSource}
        loading={loading}
        style={{ marginTop: "30px" }}
        scroll={{ y : 200}}
      />
    </Box>
  );
};

export default OperateTableData;
