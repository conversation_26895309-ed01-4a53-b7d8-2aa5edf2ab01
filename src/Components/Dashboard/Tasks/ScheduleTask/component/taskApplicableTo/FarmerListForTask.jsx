import getColumnSearchProps from "../../../../../../Utilities/Common/TableUtils";
import { useState, useRef, Fragment, useEffect } from "react";
import ShowSelectedBtn from "./helper components/ShowSelectedBtn";
import OperateTableData from "./helper components/OperateTableData";
import useGetFarmerListData from "../../controller/farmerListController";
import {Modal, Table} from "antd"
import { Box } from "@mui/material";
import { useSelector } from "react-redux";

const FarmerListForTask = ({setSelectCustomers}) => {
  const scheduleInformation = useSelector(
    (state) => state?.scheduleTask
  );
  const {appliesTo} = scheduleInformation
  const {customer} = appliesTo
  const preSelectedCustomer = customer?.customer_id?.length > 0 ? customer?.customer_id : []
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  let [selectedActivities, setSelectedActivities] = useState(preSelectedCustomer);
  const [isModalOpen, setIsModalOpen] = useState(false)
  const searchInput = useRef(null);
  const { customerTableData, makePreviewCustomerData, customerIsLoading } = useGetFarmerListData();

  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
    confirm();
  };

  const handleReset = (clearFilters, confirm, dataIndex) => {
    clearFilters();
    setSearchText("");
    confirm();
  };
  useEffect(()=>{
    setSelectCustomers(selectedActivities)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[selectedActivities])

  const customerColumns = [
    {
      title: "Customer Name",
      dataIndex: "customerName",
      key: "customerName",
      // width: "20%",
      ...getColumnSearchProps(
        "customerName",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
    {
      title: "Contact",
      dataIndex: "contact",
      key: "contact",
      ...getColumnSearchProps(
        "contact",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
    {
      title: "Taluk",
      dataIndex: "taluk",
      key: "taluk",
      // width: "15%",
      ...getColumnSearchProps(
        "taluk",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
    {
      title: "Village",
      dataIndex: "village",
      key: "village",
      // width: "15%",
      ...getColumnSearchProps(
        "village",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
  ];
  const handleModalOpen = () => {
    setIsModalOpen(true);
  };
  const dataSourceForModal = makePreviewCustomerData(selectedActivities)
  return (
    <Box>
      <ShowSelectedBtn
        selectedActivities={selectedActivities}
        handleFunction={handleModalOpen}
        intity={"Customer"}
      />
      {/* {selectCustomers && (
        <p style={{ color: "red" }}>Select farmer from the table.</p>
      )} */}
      <OperateTableData
        intity={"Customer"}
        selectedActivities={selectedActivities}
        setSelectedActivities={setSelectedActivities}
        tableColumns={customerColumns}
        tableDataSource={customerTableData}
        loading={customerIsLoading}
      />
      <Modal
        title="Selected Customer List"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        width={1000}
        footer={false}
      >
        <hr />
        <Table columns={customerColumns} dataSource={dataSourceForModal} loading={customerIsLoading}/>
      </Modal>
    </Box>
  );
};

export default FarmerListForTask;
