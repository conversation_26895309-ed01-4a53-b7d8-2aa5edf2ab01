import { forwardRef, useEffect, useState, useImperativeHandle } from "react";
import { useForm, FormProvider } from "react-hook-form";
// import { DevTool } from "@hookform/devtools";
// import ShowSelectedBtn from "../../Forms/FormTypes/ScheduleActivity/ShowSelectedBtn";
import MultiSelectMui from "./helper components/MultiSelectMui";
import SingleSelectMui from "./helper components/SingleSelectMui";
import useGetGeoData from "../../controller/geoController";
import FarmerListForTask from "./FarmerListForTask";
import "./scheduleTask.css";
import { Box, Typography, Stack } from "@mui/material";
import { Segmented } from "antd";
import {
  setToApplicable,
  setPreview,
} from "../../scheduleTaskSlice/scheduleTaskSlice";
import { useDispatch, useSelector } from "react-redux";
import ToastersService from "../../../../../../Services/toasters.service";

const TaskApplicableTo = forwardRef(({ setPage }, ref) => {
  const scheduleInformation = useSelector((state) => state?.scheduleTask);
  const { appliesTo } = scheduleInformation;
  const { geography, customer } = appliesTo;
  const { preview } = scheduleInformation;

  const {
    states: statesPreview,
    districts: districtsPreview,
    state_for_district: districtIdPreviewForState,
    taluks: taluksPreview,
    state_for_taluk : stateIdForTalukPreview,
    district_for_taluk : districtIdForTalukPreview,
    villages : villagesPreview,
    state_for_village : stateIdForVillPreview,
    district_for_village :  districtIdForVillPreview,
    taluk_for_village : talukIdForVillPreview,
    basedOn : basedOnPreview
  } = preview;

  let preSetValue = geography && Object.keys(geography)?.length > 0 ? "Geography" : customer?.customer_id?.length > 0 ?  "Customer" : "";
  const [value, setValue] = useState(preSetValue);
  const [basedOn, setBasedOn] = useState("");
  const [selectCustomers, setSelectCustomers] = useState([]);
  const dispatch = useDispatch();
  const methods = useForm();
  const { watch } = methods;
  // const selectedValueForBasedOn = watch("basedOn");
  const states = watch("State");
  const districts = watch("District");
  const taluks = watch("Taluk");
  const villages = watch("Village");
  const state_for_district = watch("state_for_district");
  const state_for_village = watch("state_for_village");
  const state_for_taluk = watch("state_for_taluk");
  const district_for_taluk = watch("district_for_taluk");
  const district_for_village = watch("district_for_village");
  const taluk_for_village = watch("taluk_for_village");
  // console.log("selectedValue", value);

  const {
    stateList,
    districtList,
    talukList,
    villageList,
    getState,
    getDistrictsBasedOnState,
    getVillagesBasedOnTaluk,
    getTaluksBasedOnDistrict,
  } = useGetGeoData();

  const basedOnApiCall = () => {
    switch (basedOn) {
      case "Based on district":
        getDistrictsBasedOnState(state_for_district?.reference_id);
        break;
      case "Based on taluk":
        // getTaluksBasedOnDistrict(state_for_taluk?.reference_id);
        if (state_for_taluk?.reference_id) {
          getDistrictsBasedOnState(state_for_taluk?.reference_id);
        }
        if (district_for_taluk?.reference_id) {
          getTaluksBasedOnDistrict(district_for_taluk?.reference_id);
        }
        break;
      case "Based on village":
        if (state_for_village?.reference_id) {
          getDistrictsBasedOnState(state_for_village?.reference_id);
        }
        if (district_for_village?.reference_id) {
          getTaluksBasedOnDistrict(district_for_village?.reference_id);
        }
        if (taluk_for_village?.reference_id) {
          getVillagesBasedOnTaluk(taluk_for_village?.reference_id);
        }
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    switch (true) {
        case statesPreview !== undefined && basedOnPreview === "Based on state":
            setBasedOn("Based on state");
            methods.reset({ "State": statesPreview });
            break;
        case districtsPreview !== undefined && basedOnPreview === "Based on district":
            setBasedOn("Based on district");
            methods.reset({
                District: districtsPreview,
                state_for_district: districtIdPreviewForState,
            });
            break;
        case taluksPreview !== undefined && basedOnPreview === "Based on taluk":
            setBasedOn("Based on taluk");
            methods.reset({
                Taluk: taluksPreview,
                state_for_taluk: stateIdForTalukPreview,
                district_for_taluk: districtIdForTalukPreview
            });
            break;
        case villagesPreview !== undefined && basedOnPreview === "Based on village":
            setBasedOn("Based on village");
            methods.reset({
                Village: villagesPreview,
                state_for_village: stateIdForVillPreview,
                district_for_village: districtIdForVillPreview,
                taluk_for_village: talukIdForVillPreview
            });
            break;
        default:
            break;
    }
}, [methods.reset]);

  useEffect(() => {
    getState();
    basedOnApiCall();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    basedOn,
    state_for_district,
    state_for_taluk,
    state_for_village,
    district_for_taluk,
    district_for_village,
    taluk_for_village,
    // methods.reset
  ]);

  // based on tab selected I'm defining payload in redux
  const defineThePayload = () => {
    const dispatchAction = {
      "Based on state": () =>
        dispatch(
          setToApplicable({
            geography: {
              state_ids: states?.map((item) => item?.reference_id),
            },
          })
        ),
      "Based on district": () =>
        dispatch(
          setToApplicable({
            geography: {
              district_ids: districts?.map((item) => item?.reference_id),
            },
          })
        ),
      "Based on taluk": () =>
        dispatch(
          setToApplicable({
            geography: {
              taluka_ids: taluks?.map((item) => item?.reference_id),
            },
          })
        ),
      "Based on village": () =>
        dispatch(
          setToApplicable({
            geography: {
              village_ids: villages?.map((item) => item?.reference_id),
            },
          })
        ),
    };

    if (value === "Customer") {
      dispatch(
        setToApplicable({
          customer: {
            customer_id: selectCustomers,
          },
        })
      );
    } else {
      const action = dispatchAction[basedOn];
      if (action) {
        action();
      }
    }
  };

  // validation for move to next page
  const validation = () => {
    let isValid = true;

    if (value === "Geography") {
      switch (basedOn) {
        case "Based on state":
          if (!states || states.length === 0) {
            ToastersService.failureToast(
              "Please select state to schedule the task"
            );
            isValid = false;
          }
          break;
        case "Based on district":
          if (!districts || districts.length === 0) {
            ToastersService.failureToast(
              "Please select district to schedule the task"
            );
            isValid = false;
          }
          break;
        case "Based on taluk":
          if (!taluks || taluks.length === 0) {
            ToastersService.failureToast(
              "Please select taluk to schedule the task"
            );
            isValid = false;
          }
          break;
        case "Based on village":
          if (!villages || villages.length === 0) {
            ToastersService.failureToast(
              "Please select village to schedule the task"
            );
            isValid = false;
          }
          break;
        default:
          break;
      }
    } else if (value === "Customer") {
      if (!selectCustomers || selectCustomers.length === 0) {
        ToastersService.failureToast(
          "Please select customers to schedule the task"
        );
        isValid = false;
      }
    } else {
      ToastersService.failureToast(
        "Please select geography or customers to schedule the task"
      );
      isValid = false;
    }

    return isValid;
  };

  useImperativeHandle(ref, () => ({
    callPage2() {
      if (validation()) {
        setPage(3);
        defineThePayload();
        dispatch(
          setPreview({
            states,
            districts,
            taluks,
            villages,
            state_for_district,
            state_for_village,
            state_for_taluk,
            district_for_taluk,
            district_for_village,
            taluk_for_village,
            basedOn : basedOn
          })
        );
      }
    },
  }));
 
  return (
    <FormProvider {...methods}>
      {/* <Typography>
      Define the geography or customer group for scheduling the activity
      </Typography> */}
      <Stack justifyContent="center" alignItems="center" marginTop={2}>
        <Segmented
          size="large"
          options={["Geography", "Customer"]}
          value={value}
          onChange={setValue}
        />
      </Stack>

      {value === "Geography" && (
        <div>
          <Stack justifyContent="center" alignItems="center">
            <Segmented
              size="large"
              style={{ marginTop: "10px" }}
              options={[
                "Based on state",
                "Based on district",
                "Based on taluk",
                "Based on village",
              ]}
              value={basedOn}
              onChange={setBasedOn}
            />
          </Stack>
          <Box sx={{ marginTop: 5 }}>
            {basedOn === "Based on state" && (
              <div className="col-4">
                <MultiSelectMui
                  dropDownName="State"
                  dropDownOptions={stateList}
                  // value={statesPreview || []}
                />
              </div>
            )}
            {basedOn === "Based on district" && (
              <div className="row">
                <div className="col-4">
                  <SingleSelectMui
                    dropDownName="state_for_district"
                    dropDownOptions={stateList}
                  />
                </div>
                <div className="col-4">
                  <MultiSelectMui
                    dropDownName="District"
                    dropDownOptions={districtList}
                  />
                </div>
              </div>
            )}
            {basedOn === "Based on taluk" && (
              <div className="row">
                <div className="col-4">
                  <SingleSelectMui
                    dropDownName="state_for_taluk"
                    dropDownOptions={stateList}
                  />
                </div>
                <div className="col-4">
                  <SingleSelectMui
                    dropDownName="district_for_taluk"
                    dropDownOptions={districtList}
                  />
                </div>
                <div className="col-4">
                  <MultiSelectMui
                    dropDownName="Taluk"
                    dropDownOptions={talukList}
                  />
                </div>
              </div>
            )}
            {basedOn === "Based on village" && (
              <div className="row">
                <div className="col-4">
                  <SingleSelectMui
                    dropDownName="state_for_village"
                    dropDownOptions={stateList}
                  />
                </div>
                <div className="col-4">
                  <SingleSelectMui
                    dropDownName="district_for_village"
                    dropDownOptions={districtList}
                  />
                </div>
                <div className="col-4">
                  <SingleSelectMui
                    dropDownName="taluk_for_village"
                    dropDownOptions={talukList}
                  />
                </div>
                <div className="col-4 mt-3">
                  <MultiSelectMui
                    dropDownName="Village"
                    dropDownOptions={villageList}
                  />
                </div>
              </div>
            )}
          </Box>
        </div>
      )}
      {value === "Customer" && (
        <FarmerListForTask setSelectCustomers={setSelectCustomers} />
      )}

      {/* <DevTool control={methods.control} /> */}
    </FormProvider>
  );
});

export default TaskApplicableTo;
