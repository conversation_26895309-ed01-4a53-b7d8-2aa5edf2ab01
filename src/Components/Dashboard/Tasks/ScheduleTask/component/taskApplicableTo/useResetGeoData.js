import { useEffect } from 'react';

const useResetBasedOnPreview = (methods, setBasedOn, previews) => {
    useEffect(() => {
        const resetData = () => {
            if (previews.state !== undefined) {
                setBasedOn("Based on state");
                methods.reset({ "State": previews.state });
            }
            if (previews.district !== undefined) {
                setBasedOn("Based on district");
                methods.reset({
                    District: previews.district,
                    state_for_district: previews.districtIdForState,
                });
            }
            if (previews.taluk !== undefined) {
                setBasedOn("Based on taluk");
                methods.reset({
                    Taluk: previews.taluk,
                    state_for_taluk: previews.stateIdForTaluk,
                    district_for_taluk: previews.districtIdForTaluk
                });
            }
            if (previews.village !== undefined) {
                setBasedOn("Based on village");
                methods.reset({
                    Village: previews.village,
                    state_for_village: previews.stateIdForVillage,
                    district_for_village: previews.districtIdForVillage,
                    taluk_for_village: previews.talukIdForVillage
                });
            }
        };

        resetData();
    }, [methods.reset, setBasedOn, previews]);
};

export default useResetBasedOnPreview;
