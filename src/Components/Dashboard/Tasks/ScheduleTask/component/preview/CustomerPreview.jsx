
import {Table} from "antd"
import { useState, useRef } from "react";
import getColumnSearchProps from "../../../../../../Utilities/Common/TableUtils";

const CustomerPreview = ({data}) =>{
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef(null);
  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
    confirm();
  };

  const handleReset = (clearFilters, confirm, dataIndex) => {
    clearFilters();
    setSearchText("");
    confirm();
  };
  const customerColumns = [
    {
      title: "Customer Name",
      dataIndex: "customerName",
      key: "customerName",
      // width: "20%",
      ...getColumnSearchProps(
        "customerName",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
    {
      title: "Contact",
      dataIndex: "contact",
      key: "contact",
      ...getColumnSearchProps(
        "contact",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
    {
      title: "Taluk",
      dataIndex: "taluk",
      key: "taluk",
      // width: "15%",
      ...getColumnSearchProps(
        "taluk",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
    {
      title: "Village",
      dataIndex: "village",
      key: "village",
      // width: "15%",
      ...getColumnSearchProps(
        "village",
        handleSearch,
        handleReset,
        searchInput,
        searchedColumn
      ),
    },
  ];

    return (
        <Table columns={customerColumns} dataSource={data} scroll={{ y : 200}}/>
    )
}

export default CustomerPreview;