import usePreviewData from "./previewController";
import CustomerPreview from "./CustomerPreview";
import GeographyPreview from "./GeographyPreview";
import { Box, Grid, Stack, Typography } from "@mui/material";
import { useSelector } from "react-redux";
import {But<PERSON>, Modal} from "antd"
import { forwardRef, useState, useImperativeHandle } from "react";
import ActivityCalendar from "../../../../activityManagement/activity calender/ActivityCalendar";
import useScheduleTaskHook from "../../helper";
import Loader from "../../../../../Loader/Loader";
const PreviewScheduleTask = forwardRef(({ setPage }, ref) => {
  const { tableData, activityDetails, scheduleDetails, applicableTo, selectStaffTypes } =
    usePreviewData();
  const {scheduleTaskApiCall, extractPayloadForScheduleTask, isLoading} = useScheduleTaskHook();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const data = useSelector((state) => state?.scheduleTask);

  useImperativeHandle(ref,()=>({
    callPage4(){
      const payload = extractPayloadForScheduleTask(data)
      if (payload) {
        scheduleTaskApiCall(payload)
      }
    }
  }))

  const ScheduleLoader = () =>{
    return (
      <Stack  justifyContent="center" alignItems="center" style={{ height: 350 }}>
        <Box>
          <Loader/>
          <Typography>Task scheduling...</Typography>
        </Box>
      </Stack>
    )
  }

  const scheduleInformation = useSelector((state) => state?.scheduleTask?.schedule)
  return isLoading ? (<ScheduleLoader/>) : (
    <>
      <Box sx={{padding:2,boxShadow: "rgba(0, 0, 0, 0.16) 0px 1px 4px",borderRadius:"6px"}}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography variant="body1">
              <strong>Activity Name:</strong> {activityDetails?.activity_name}
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="body1">
              <strong>Start Date:</strong> {scheduleDetails.startDate}
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="body1">
              <strong>End Date:</strong> {scheduleDetails.endDate}
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="body1">
              <strong>Applicable To:</strong> {applicableTo}
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="body1">
              <strong>Assigned To:</strong> {selectStaffTypes?.join(', ')}
            </Typography>
          </Grid>
        </Grid>
      </Box>
      <Stack spacing={2} justifyContent="space-between" flexDirection="row" alignItems="center">
      <Typography sx={{marginTop:1,color:"gray"}}>Selected {applicableTo} details</Typography>
      <Button type="primary" onClick={showModal}>Show schedule</Button>
      </Stack>
      <Box>
        {applicableTo === "Customer" && <CustomerPreview data={tableData} />}
        {applicableTo === "Geography" && <GeographyPreview />}
      </Box>
      <Modal title="Activity Calendar Preview" open={isModalOpen} footer={null} width={500} onCancel={handleCancel}>
         <ActivityCalendar activity_information={scheduleInformation} handleHeader={true}/>
      </Modal>
    </>
  );
})

export default PreviewScheduleTask;
