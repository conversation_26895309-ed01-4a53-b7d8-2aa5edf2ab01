import useGetFarmerListData from "../../controller/farmerListController";
import { useSelector } from "react-redux";
import { useGetActivityQuery } from "../../../../activityManagement/reducer/activityManagementSlice";
import { extractBasedOnLanguageMod } from "Utilities/Common/utils";
import { globalReferencesMapper } from "Utilities/Common/utils";

const usePreviewData = () => {
  const previewPayloadData = useSelector((state) => state.scheduleTask);
  // console.log("previewPayloadData", previewPayloadData);
  const { makePreviewCustomerData } = useGetFarmerListData();
  const staffList = globalReferencesMapper(10002300);
  const { data } = useGetActivityQuery();

  const tableData = makePreviewCustomerData(
    previewPayloadData?.appliesTo?.customer?.customer_id
  );

  const activityDetails = data?.report?.find(
    (item) => item?.activity_id === previewPayloadData.activity?.activity_id?.activity_id
  );

  const scheduleDetails = {
    startDate: previewPayloadData?.schedule?.startDate,
    endDate: previewPayloadData?.schedule?.endingCondition?.endDate,
  };

  const applicableTo = previewPayloadData?.appliesTo?.customer
    ? "Customer"
    : "Geography";
  const geographyData = previewPayloadData?.appliesTo?.geography;
  const geographyPreview = previewPayloadData?.preview;
  const selectStaffTypes = staffList
    ?.filter((item) =>
      previewPayloadData?.staff_type_list?.includes(item?.reference_id)
    )
    ?.map((name) => extractBasedOnLanguageMod(name?.reference_name_l10n, "en"));

  return {
    tableData,
    activityDetails,
    scheduleDetails,
    applicableTo,
    geographyData,
    geographyPreview,
    selectStaffTypes,
  };
};

export default usePreviewData;
