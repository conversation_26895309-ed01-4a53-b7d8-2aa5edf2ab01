import { Table } from "antd";
import usePreviewData from "./previewController";
import { Fragment, useEffect, useState } from "react";
const stateColumn = [
  {
    title: "State",
    dataIndex: "state",
    key: "state",
  },
];

const districtColumn = stateColumn.concat([
  {
    title: "District",
    dataIndex: "district",
    key: "district",
  },
]);

const talukColumn = districtColumn.concat([
  {
    title: "Taluk",
    dataIndex: "taluk",
    key: "taluk",
  },
]);

const villageColumn = talukColumn.concat([
  {
    title: "Village",
    dataIndex: "village",
    key: "village",
  },
]);
const GeographyPreview = () => {
  const [column, setColumn] = useState([]);
  const [data, setData] = useState([]);
  const { geographyPreview } = usePreviewData();

  const stateData = geographyPreview?.basedOn === "Based on state" &&  geographyPreview?.states?.map((stateName) => ({
    key: stateName?.reference_id || "Not Defined",
    state: stateName?.reference_name_l10n || "Not Defined",
  }));

  const districtData = geographyPreview?.basedOn === "Based on district" && geographyPreview?.districts?.map((districtName) => ({
    key: districtName?.reference_id || "Not Defined",
    state:
      geographyPreview?.state_for_district?.reference_name_l10n ||
      "Not Defined",
    district: districtName?.reference_name_l10n || "Not Defined",
  }));

  const talukData = geographyPreview?.basedOn === "Based on taluk" && geographyPreview?.taluks?.map((talukName) => ({
    key: talukName?.reference_id || "Not Defined",
    state:
      geographyPreview?.state_for_taluk?.reference_name_l10n || "Not Defined",
    district:
      geographyPreview?.district_for_taluk?.reference_name_l10n ||
      "Not Defined",
    taluk: talukName?.reference_name_l10n || "Not Defined",
  }));

  const villageData = geographyPreview?.basedOn === "Based on village" &&  geographyPreview?.villages?.map((villageName) => ({
    key: villageName?.reference_id || "Not Defined",
    state:
      geographyPreview?.state_for_village?.reference_name_l10n || "Not Defined",
    district:
      geographyPreview?.district_for_village?.reference_name_l10n ||
      "Not Defined",
    taluk:
      geographyPreview?.taluk_for_village?.reference_name_l10n || "Not Defined",
    village: villageName?.reference_name_l10n || "Not Defined",
  }));

  useEffect(() => {
    const dataMap = {
      state: { column: stateColumn, data: stateData },
      district: { column: districtColumn, data: districtData },
      taluk: { column: talukColumn, data: talukData },
      village: { column: villageColumn, data: villageData },
    };
  
    for (const dataType in dataMap) {
      if (dataMap[dataType].data?.length > 0) {
        setColumn(dataMap[dataType].column);
        setData(dataMap[dataType].data);
        break;
      }
    }
  }, []);
  return (
    <Fragment>
      {data?.length > 0 && <Table columns={column} dataSource={data} scroll={{y : 180}} />}
    </Fragment>
  );
};

export default GeographyPreview;
