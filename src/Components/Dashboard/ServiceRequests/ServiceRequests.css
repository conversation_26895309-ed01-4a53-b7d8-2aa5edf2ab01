.sr__tab-header {
    color: silver;
    padding: 0 1rem;
    margin-right: 10rem;
}

.sr__tab-header.selected {
    /* border: 1px solid silver; */
    color: white;
}

.sr__tab-header-count {
    border: 1px solid silver;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 5px auto;
}

.sr__tab-header-count.selected {
    border: 1px solid #ec6237;
    background-color: white;
    color: #ec6237;
    font-weight: 600;
}

.sr__cattle-list-item:hover {
    background-color: #ececec;
    cursor: pointer;
}

.sr__cattle-list-item.selected {
    background-color: #dfdfdf;
}

.sr__cattle-list-item.disabled {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.5;
}
.sr__tab-header-line{
    position: absolute;
    top: 19px;
    left: 87px;
    width: 245px;
    height: 2px;
    background-color: #ec6237;
}