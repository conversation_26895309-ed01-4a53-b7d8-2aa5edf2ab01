import { Table } from 'react-bootstrap';
import moment from 'moment'
import React from 'react'
import "./ServiceRequests.css";
import { Typography } from '@mui/material';

export const PreviewModalTable = ({previewData}) => {
    return (
        <div>
            {
                previewData && previewData?.length > 0 ? (
                    <Table bordered style={{ fontSize: "14px", fontWeight: "600" }}>
                <tbody>
                    <tr>
                        <td>Customer Complaint Id</td>
                        <td>{previewData[0].ticket || '-'}</td>
                    </tr>
                    <tr>
                        <td>Customer Complaint Date</td>
                        <td>
                            {moment(
                                previewData[0].complaint_date_time
                            ).format("DD/MM/YYYY") || '-'}
                        </td>
                    </tr>
                    <tr>
                        <td>Time of Complaint</td>
                        <td>
                            {moment(
                                previewData[0].complaint_date_time
                            ).format("hh:mm A") || '-'}
                        </td>
                    </tr>
                    <tr>
                        <td>Farmer Name</td>
                        <td>
                            {previewData[0].farmer_name_l10n
                                ? previewData[0].farmer_name_l10n.en
                                    ? previewData[0].farmer_name_l10n.en
                                    : previewData[0].farmer_name_l10n.ul
                                : "-"}
                        </td>
                    </tr>
                    <tr>
                        <td>Farmer Phone No</td>
                        <td>{previewData[0].farmer_mobile || '-'}</td>
                    </tr>
                    <tr>
                        <td>Farmer ID</td>
                        <td>{previewData[0].farmer_visual_id || '-'}</td>
                    </tr>
                    <tr>
                        <td>Animal Tag No</td>
                        <td>{previewData[0].animal_ear_tag || '-'}</td>
                    </tr>
                    <tr>
                        <td>Animal Reference No</td>
                        <td>{previewData[0].animal_visual_id || '-'}</td>
                    </tr>
                    <tr>
                        <td>Animal Subscription Plan</td>
                        <td>{previewData[0].animal_subscription_plan || '-'}</td>
                    </tr>
                    <tr>
                        <td>Village</td>
                        <td>
                            {previewData[0].farmer_village
                                ? previewData[0].farmer_village.en
                                    ? previewData[0].farmer_village.en
                                    : previewData[0].farmer_village.ul
                                : "-"}
                        </td>
                    </tr>
                    <tr>
                        <td>Dist & Block</td>
                        <td>
                            {previewData[0].district_name_l10n
                                ? previewData[0].district_name_l10n.en
                                    ? previewData[0].district_name_l10n.en
                                    : previewData[0].district_name_l10n.ul
                                : "-"}{" "}
                            -{" "}
                            {previewData[0].taluk_name_l10n
                                ? previewData[0].taluk_name_l10n.en
                                    ? previewData[0].taluk_name_l10n.en
                                    : previewData[0].taluk_name_l10n.ul
                                : "-"}
                        </td>
                    </tr>
                    <tr>
                        <td>Dr./LSS</td>
                        <td>
                            {previewData[0].paravet_name_l10n
                                ? previewData[0].paravet_name_l10n.en
                                    ? previewData[0].paravet_name_l10n.en
                                    : previewData[0].paravet_name_l10n.ul
                                : "-"}
                        </td>
                    </tr>
                    <tr>
                        <td>Route Officer</td>
                        <td>
                            {previewData[0].ro_name_l10n
                                ? previewData[0].ro_name_l10n.en
                                    ? previewData[0].ro_name_l10n.en
                                    : previewData[0].ro_name_l10n.ul
                                : "-"}
                        </td>
                    </tr>
                    <tr>
                        <td>Veterinary Officer</td>
                        <td>
                            {previewData[0].vet_name_l10n
                                ? previewData[0].vet_name_l10n.en
                                    ? previewData[0].vet_name_l10n.en
                                    : previewData[0].vet_name_l10n.ul
                                : "-"}
                        </td>
                    </tr>
                    <tr>
                        <td>Activity / Complaint</td>
                        <td>
                            {previewData[0].activity_name_l10n
                                ? previewData[0].activity_name_l10n.en
                                    ? previewData[0].activity_name_l10n.en
                                    : previewData[0].activity_name_l10n.ul
                                : "-"}
                        </td>
                    </tr>
                </tbody>
            </Table>
                ) : (
                    <Typography>No preview data found...</Typography>
                )
            }
        </div>
    )
}