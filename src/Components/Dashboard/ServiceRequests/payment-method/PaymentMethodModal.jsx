import React, { useState } from "react";
import { Button, Modal, Radio, Typography, message } from "antd";
import { CreditCardFilled, InfoCircleFilled } from "@ant-design/icons";
import paymentDescription from "./paymentDescription.json"

const PaymentMethodModal = ({ options, setCreateTaskForm, createTaskForm }) => {
  const [modalVisible, setModalVisible] = useState(true);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);

  const handlePaymentMethodChange = (e) => {
    setSelectedPaymentMethod(e.target.value);
  };

  const handleModalOk = () => {
    console.log("Selected payment method:", selectedPaymentMethod);
    if (!selectedPaymentMethod) {
      message.error("Please select the payment method");
    } else {
      setCreateTaskForm({
        ...createTaskForm,
        account_payment_type : selectedPaymentMethod
      });
      setModalVisible(false);
    }
  };
  
  const mapPaymentName = {
    ********** : {
      name : "Pay by DAS (Deduction At Source)",
      template : paymentDescription.payDas
    },
    ********** : {
      name : "Pay by self",
      template : paymentDescription.paySelf
    },
    ********** : {
      name : "Pay in full",
      template : paymentDescription.payInFull
    }
  }
  return (
    <Modal
      open={modalVisible}
      onOk={handleModalOk}
      okText="Submit"
      maskClosable={false}
      width={700}
      footer={[
        <Button key="submit" type="primary" onClick={handleModalOk}>
          Submit
        </Button>,
      ]}
      closeIcon={false}
    >
     <Typography.Title level={5}> <CreditCardFilled style={{color:"gray"}} lang="tn"/> Select Payment Method</Typography.Title>
      <Radio.Group
        onChange={handlePaymentMethodChange}
        value={selectedPaymentMethod}
        style={{marginTop:"5px"}}
      >
        {
          options?.map((item)=>(
            <Radio value={item} key={item}>
              {mapPaymentName[item].name}
            </Radio>
          ))
        }
      </Radio.Group>
      {selectedPaymentMethod !== null && (
        <>
           <hr />
          <Typography.Title style={{ marginTop: 5 }} level={5}>
          <InfoCircleFilled style={{marginRight: "3px", color:"orange"}} /> Description
          </Typography.Title>
          <Typography>
           <ul>
           {mapPaymentName[selectedPaymentMethod].template?.map((item)=>(
              <li>{item}</li>
            ))}
           </ul>
          </Typography>
        </>
      )}
    </Modal>
  );
};

export default PaymentMethodModal;
