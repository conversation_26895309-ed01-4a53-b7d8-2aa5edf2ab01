import React from 'react';
import { Table } from 'antd';
import { extractBasedOnLanguageMod } from 'Utilities/Common/utils';

const AnimalLevelSummaryTable = ({ data, loading }) => {
  // Transform the data into Ant Design table format
  const dataSource = data && data.map(item => ({
    animal_id: item?.Details?.animal_id,
    animal_eartag: item?.Details?.animal_eartag,
    subscription_plan_l10n: extractBasedOnLanguageMod(item.Details.subscription_plan_l10n,'en'),
    Curative_total_cost: item?.Curative?.total_cost,
    Curative_count: item?.Curative?.count,
    Preventive_total_cost: item?.Preventive?.total_cost,
    Preventive_count: item?.Preventive?.count,
    Reproductive_total_cost: item?.Reproductive?.total_cost,
    Reproductive_count: item?.Reproductive?.count,
  }));
  // Define columns for the table
  const columns = [
    {
      title: 'Animal Eartag',
      dataIndex: 'animal_eartag',
      key: 'animal_eartag',
    },
    {
      title: 'Subscription Plan',
      dataIndex: 'subscription_plan_l10n',
      key: 'subscription_plan_l10n',
    },
    {
      title: 'Curative Total Cost',
      dataIndex: 'Curative_total_cost',
      key: 'Curative_total_cost',
    },
    {
      title: 'Curative Count',
      dataIndex: 'Curative_count',
      key: 'Curative_count',
    },
    {
      title: 'Preventive Total Cost',
      dataIndex: 'Preventive_total_cost',
      key: 'Preventive_total_cost',
    },
    {
      title: 'Preventive Count',
      dataIndex: 'Preventive_count',
      key: 'Preventive_count',
    },
    {
      title: 'Reproductive Total Cost',
      dataIndex: 'Reproductive_total_cost',
      key: 'Reproductive_total_cost',
    },
    {
      title: 'Reproductive Count',
      dataIndex: 'Reproductive_count',
      key: 'Reproductive_count',
    },
  ];

  return <Table dataSource={dataSource} columns={columns} pagination={false} loading={loading}/>;
};

export default AnimalLevelSummaryTable;
