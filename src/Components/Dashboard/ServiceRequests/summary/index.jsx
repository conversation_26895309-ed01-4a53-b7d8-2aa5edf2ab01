import React, { useState } from "react";
import useSummaryApi from "./service";
import FarmerLevelSummary from "./FarmerSummary";
import AnimalLevelSummary from "./AnimalLevelSummary";
import { Box, Stack, Typography } from "@mui/material";
import { Segmented } from "antd";
import { extractBasedOnLanguageMod } from "Utilities/Common/utils";

const Summary = ({ farmerId }) => {
  const { summaryData, loading } = useSummaryApi(farmerId);
  const [value, setValue] = useState("Farmer");
 
  return (
    <Box sx={{width : "100%"}}>
      <Box display="flex" flexDirection="column" alignItems="center">
        <Typography variant="p" style={{ fontWeight: 600, color: '#2e2e2e', fontSize: 16  }}>
          Farmer: <span style={{ fontWeight: 400 }}>{extractBasedOnLanguageMod(summaryData?.farmerLevelSummary?.Details.customer_name_l10n,"en")}</span>
        </Typography>
      </Box>
      <Stack justifyContent="center" alignItems="center">
        <Stack justifyContent="center" alignItems="center">
          <Segmented
            size="large"
            style={{ marginTop: "10px" }}
            options={["Farmer", "Animal"]}
            value={value}
            onChange={setValue}
          />
        </Stack>
        {value === "Farmer" && (
          <FarmerLevelSummary data={summaryData?.farmerLevelSummary} loading={loading}/>
        )}
        {value === "Animal" && (
          <AnimalLevelSummary data={summaryData?.animalLevelSummary} loading={loading} />
        )}
      </Stack>
    </Box>
  );
};

export default Summary;
