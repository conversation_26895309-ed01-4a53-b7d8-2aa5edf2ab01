import React from 'react';
import { Table } from 'antd';
import { Box, Typography } from '@mui/material';

const FarmerLevelSummary = ({ data, loading }) => {
  // Define the columns for the table
  const columns = [
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: 'Total Cost',
      dataIndex: 'total_cost',
      key: 'total_cost',
    },
    {
      title: 'Count',
      dataIndex: 'count',
      key: 'count',
    },
  ];

  // Transform the data into the format needed for the table
  const dataSource = data && Object.keys(data).filter(key => key !== 'Details').map((key, index) => ({
    key: index,
    type: key,
    total_cost: data[key].total_cost,
    count: data[key].count,
  }));

  return (
    <Box sx={{width: "100%"}} >
      <Table dataSource={dataSource} columns={columns} pagination={false} loading={loading} />
    </Box>
  );
};

export default FarmerLevelSummary;
