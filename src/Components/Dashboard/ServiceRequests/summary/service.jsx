import { clearUserToken } from "Components/user/action";
import { instance } from "Services/api.service";
import {useEffect, useState} from "react"
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";


const useSummaryApi = (farmerId)=>{
    const navigate = useNavigate()
    const dispatch = useDispatch();
    const userToken = useSelector((state) => state.user.userToken);
    const [summaryData, setSummaryData] = useState([])
    const [loading, setLoading] = useState(false)
    
    const getSummary = async (farmerId) => {
        setLoading(true)
        try {
          const response = await instance({
            url: `v2/finance/farmers/summary/${farmerId}`,
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              token: userToken.accessToken,
            },
          });
          if (response.status === 200 || response.status === 201) {
            setSummaryData(response.data)
            setLoading(false)
          }
        } catch (error) {
          console.log(error);
          setLoading(false)
          if ('response' in error && error.response.status === 401) {
            dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
          }
        }
      }
    useEffect(()=>{
        getSummary(farmerId)
    },[farmerId])

    return {summaryData, loading}
}

export default useSummaryApi;