import { useEffect, useState, useRef } from "react";
import "./ServiceRequests.css";
import Box from "@mui/material/Box";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import { Button, Col, Modal, Row } from "react-bootstrap";
import ToastersService from "../../../Services/toasters.service";
import { instance } from "../../../Services/api.service";
import { useNavigate } from "react-router-dom";
import moment from "moment";
import Tooltip, { tooltipClasses } from "@mui/material/Tooltip";
import { styled } from "@mui/material/styles";
import { Table, Modal as ATNDModal } from "antd";
import Pagination from "@mui/material/Pagination";
import Stack from "@mui/material/Stack";
import Loader from "../../Loader/Loader";
import logo from "./krushal.png";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

import Moment from "react-moment";
import { PreviewModalTable } from "./PreviewModalTable";
import Summary from "./summary";
import PaymentMethodModal from "./payment-method/PaymentMethodModal";
import { extractBasedOnLanguageMod } from "Utilities/Common/utils";

const {
  CS_TEAM,
  MARKETING_TEAM,
  SALES_TEAM,
  FINANCE_TEAM,
  VET_OPS,
  SALES_OPS,
  withAuthorization,
} = require("../../user/authorize");

const findFarmerByMobileNo = async (farmerContact, navigate) => {
  let reqBody = { f_farmer_mobile: farmerContact };
  console.log("Req body", reqBody);
  try {
    const response = await instance({
      url: "/find/farmer",
      method: "POST",
      data: reqBody,
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("findFarmerByMobileNo", response);
    if (response.status == 201) {
      return response.data.data;
    } else if (response.status == 204) {
      return [];
    }
  } catch (error) {
    console.log("findFarmerByMobileNo", error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const findFarmerByCattleEarTag = async (eartag, navigate) => {
  let reqBody = { f_ear_tag: eartag };
  console.log("Req body", reqBody);
  try {
    const response = await instance({
      url: "/find/animal",
      method: "POST",
      data: reqBody,
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("findFarmerByCattleEarTag", response);
    if (response.status == 201) {
      return response.data.data;
    } else if (response.status == 204) {
      return [];
    }
  } catch (error) {
    console.log("findFarmerByCattleEarTag", error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const getCattleLastFiveActivities = async (
  cattleId,
  page,
  navigate,
  setTotalPages
) => {
  console.log(page, "current aheb");
  let reqBody = {
    animal_id: cattleId,
    page_number: page,
    page_limit: 10,
  };
  try {
    const response = await instance({
      url: "/find/activity",
      method: "POST",
      data: reqBody,
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("getCattleLastFiveActivities", response);
    if (response.status == 201) {
      setTotalPages(response.data.total_page);
      return response.data.data;
    } else if (response.status == 204) {
      return [];
    }
  } catch (error) {
    console.log("getCattleLastFiveActivities", error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const getActivityConfig = async (navigate, setActivityConfig) => {
  try {
    const response = await instance({
      url: "/references/care-calendar",
      method: "GET",
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    if (response.status === 200) {
      console.log("Activity Config", response.data);
      setActivityConfig({
        Preventive: response.data.data.activity_config.Preventive,
        Curative: response.data.data.activity_config.Curative,
        Reproductive: response.data.data.activity_config.Reproductive,
      });
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};
const getActivityConfigFromGlobalRef = (setActivityConfig) => {
  setActivityConfig({
    Preventive: global.activityReferences[1000270001], // Preventive,
    Curative: global.activityReferences[1000270002], // Curative,
    Reproductive: global.activityReferences[1000270003], // Reproductive,
  });
};

const createTask = async (
  createTaskForm,
  cattleId,
  navigate,
  setCreateTaskLoader,
  handlePreviewModalShow,
  setPreviewData
) => {
  console.log(createTaskForm);
  setCreateTaskLoader(true);
  try {
    const response = await instance({
      url: "/activity/create-task",
      method: "POST",
      data: { ...createTaskForm, entity_uuid: cattleId },
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log(response);
    if (response.status === 201) {
      ToastersService.successToast("Task created successfully!");
      sendAutoGeneratedNote(response.data.id, navigate);
      setPreviewData(response.data.data);
      setCreateTaskLoader(false);
      handlePreviewModalShow();
      // navigate(
      //   "/dashboard/tasks/" +
      //     response.data.id +
      //     `${
      //       createTaskForm.activity_id == 1000470001
      //         ? "?popup=" + createTaskForm.activity_date
      //         : ""
      //     }`
      // );
    } else {
      throw Error("Task not created! Try again.");
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const sendAutoGeneratedNote = (calendarId, navigate) => {
  let noteItem = "New Case Visit Pending";
  addTaskNote(noteItem, calendarId, navigate);
};

const addTaskNote = async (noteItem, calendarId, navigate) => {
  try {
    const response = await instance({
      url: "/common/add-task-note",
      method: "POST",
      data: {
        note: noteItem,
        calendar_id: calendarId,
      },
      headers: {
        "Content-Type": "application/json",
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log("Add task response", response);
    if (response.status === 201) {
    } else {
      throw Error("Note not sent! Try again.");
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const createFollowUp = async (
  followUpReqBody,
  handleCreateFollowUpModalClose,
  cattleId,
  setPastActivities,
  navigate,
  currentPage,
  setTotalPages,
  setPreviewData,
  handlePreviewModalShow
) => {
  try {
    const response = await instance({
      url: "/activity/update-activity",
      method: "PUT",
      data: followUpReqBody,
      headers: {
        token: localStorage.getItem("accessToken"),
      },
    });
    console.log(response);
    if (response.status === 200) {
      ToastersService.successToast("Follow-up created successfully!");
      let tempPastActivities = await getCattleLastFiveActivities(
        cattleId,
        currentPage,
        navigate,
        setTotalPages
      );
      console.log("tempPastActivities", tempPastActivities);
      setPastActivities(tempPastActivities);
      handleCreateFollowUpModalClose();
      setPreviewData(response.data.data);
      handlePreviewModalShow();
    }
  } catch (error) {
    console.log(error);
    if (error.response.status === 401) {
      console.log("token expired");
      localStorage.removeItem("accessToken");
      localStorage.removeItem("resetToken");
      ToastersService.failureToast("Session Expired!");
      navigate("/login");
      window.location.reload();
    }
  }
};

const ServiceRequests = () => {
  const navigate = useNavigate();

  const [srTabs, setSrTabs] = useState([
    {
      count: 1,
      isSelected: true,
      name: "Select Cattle",
    },
    {
      count: 2,
      isSelected: false,
      name: "Create SR",
    },
  ]);

  const searchOptions = [
    {
      id: 1,
      name: "Mobile number",
    },
    {
      id: 2,
      name: "Ear tag",
    },
  ];

  const [search, setSearch] = useState({
    selectedSearchOption: 1,
    searchInput: "",
  });

  const [farmersList, setFarmersList] = useState();
  const [selectedFarmer, setSelectedFarmer] = useState();
  const [selectedCattle, setSelectedCattle] = useState();
  const [pastActivities, setPastActivities] = useState();

  const [initialDate, setInitialDate] = useState(null);
  const [aiInitialDate, setAiInitialDate] = useState(null);
  const [initialDateFollowUp, setInitialDateFollowUp] = useState(null);

  let [createTaskForm, setCreateTaskForm] = useState({
    calendar_activity_status: 1000300001,
    get_preview: true,
  });
  const [activityConfig, setActivityConfig] = useState();
  const [activityOptions, setActivityOptions] = useState([]);

  const [totalPages, setTotalPages] = useState();
  const [currentPage, setCurrentPage] = useState(1);

  const [createTaskLoader, setCreateTaskLoader] = useState(false);
  const [previewData, setPreviewData] = useState();

  const [showPreviewModal, setShowPreviewModal] = useState(false);

  const [isSummaryModalOpen, setIsSummaryModalOpen] = useState(false);
  const [farmerId, setFarmerId] = useState("");
  const showSummaryModal = (farmerId) => {
    setIsSummaryModalOpen(true);
    setFarmerId(farmerId);
    console.log(farmerId);
  };

  const handleCancelSummaryModal = () => {
    setIsSummaryModalOpen(false);
    setFarmerId("");
  };
  const handlePreviewModalClose = () => {
    setShowPreviewModal(false);
  };
  const handlePreviewModalShow = () => {
    setShowPreviewModal(true);
  };

  const [followUpReqBody, setFollowUpReqBody] = useState({
    care_calendar: "",
    // calendar_activity_status: 1000300050,
    follow_up_required: true,
    // completion_date: "",
    activity_date: "",
    get_preview: true,
  });

  const [showCreateFollowUpModal, setShowCreateFollowUpModal] = useState(false);
  const handleCreateFollowUpModalClose = () => {
    console.log("Modal close event!");
    setFollowUpReqBody({
      ...followUpReqBody,
      care_calendar: "",
      activity_date: "",
      // completion_date: "",
    });
    setShowCreateFollowUpModal(false);
  };
  const handleCreateFollowUpModalShow = (calendarId) => {
    console.log("Modal open event!", calendarId);
    setFollowUpReqBody({ ...followUpReqBody, care_calendar: calendarId });
    setShowCreateFollowUpModal(true);
  };

  const [screenSize, setScreenSize] = useState(window.screen.width);
  useEffect(() => {
    // getActivityConfig(navigate, setActivityConfig);
    getActivityConfigFromGlobalRef(setActivityConfig);
    const handleResize = () => {
      setScreenSize(window.screen.width);
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const handleSearchOptionSelect = (event) => {
    console.log(event.target.value);
    setSearch({
      ...search,
      selectedSearchOption: event.target.value,
      searchInput: "",
    });
  };

  const nextBtnHandler = async () => {
    for (let item of srTabs) {
      if (item.count === 2) {
        item.isSelected = true;
        let tempPastActivities = await getCattleLastFiveActivities(
          selectedCattle,
          currentPage,
          navigate,
          setTotalPages
        );
        console.log("tempPastActivities", tempPastActivities);
        setPastActivities(tempPastActivities);
      } else {
        item.isSelected = false;
      }
    }
    setSrTabs([...srTabs]);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const backBtnHandler = () => {
    for (let item of srTabs) {
      if (item.count === 1) {
        item.isSelected = true;
      } else {
        item.isSelected = false;
      }
    }
    setSrTabs([...srTabs]);
  };

  const searchInputHandler = (event) => {
    console.log(event.target.value);
    setSearch({ ...search, searchInput: event.target.value });
  };

  const findBtnHandler = async () => {
    console.log("search", search);
    setSelectedFarmer(undefined);
    setSelectedCattle(undefined);
    if (search.selectedSearchOption == 1) {
      if (search.searchInput.trim().length !== 10) {
        ToastersService.failureToast("Enter a valid mobile number!");
      } else {
        console.log("Call API");
        let tempFarmersList = await findFarmerByMobileNo(
          search.searchInput,
          navigate
        );
        console.log("tempFarmersList", tempFarmersList);
        configureFarmersList(tempFarmersList);
      }
    } else if (search.selectedSearchOption == 2) {
      if (search.searchInput.trim().length == 0) {
        ToastersService.failureToast("Enter a valid ear tag!");
      } else {
        console.log("Call API");
        let tempFarmersList = await findFarmerByCattleEarTag(
          search.searchInput,
          navigate
        );
        console.log("tempFarmersList", tempFarmersList);
        configureFarmersList(tempFarmersList);
      }
    }
  };

  const configureFarmersList = (farmerList) => {
    if (farmerList.length > 0) {
      for (let farmer of farmerList) {
        farmer.isSelected = false;
        if (farmer.cattles.length > 0) {
          for (let cattle of farmer.cattles) {
            cattle.isSelected = false;
          }
        }
      }
      farmerList[0].isSelected = true;
      setFarmersList([...farmerList]);
      setSelectedFarmer(farmerList[0]);
      // farmerItemClickHandler(farmerList[0].farmer_id);
    } else {
      setFarmersList(farmerList);
    }

    console.log("configureFarmersList", farmerList);
  };

  const farmerItemClickHandler = (farmerId) => {
    console.log(farmerId);
    for (let farmer of farmersList) {
      if (farmer.farmer_id == farmerId) {
        farmer.isSelected = true;
        setSelectedFarmer(farmer);
      } else {
        farmer.isSelected = false;
      }
    }
    setFarmersList([...farmersList]);
  };

  const cattleItemClickHandler = (cattleId) => {
    console.log(cattleId);
    for (let cattle of selectedFarmer.cattles) {
      if (cattle.animal_id == cattleId) {
        cattle.isSelected = true;
        setSelectedCattle(cattleId);
      } else {
        cattle.isSelected = false;
      }
    }
    setSelectedFarmer({ ...selectedFarmer });
  };

  const activityTypeSelectHandler = (event) => {
    console.log(
      event.target.value,
      activityConfig[event.target.value]?.filter(
        (item) => item.activity_id !== 1000180011
      )
    );
    setActivityOptions(
      activityConfig[event.target.value]?.filter(
        (item) => item.activity_id !== 1000180011
      )
    );
    setCreateTaskForm({ ...createTaskForm, activity_type: event.target.value });
  };

  const dateChangeHandler = (date) => {
    // console.log(event.target.value);
    // console.log({ ...createTaskForm, activity_date: event.target.value });
    // setCreateTaskForm({ ...createTaskForm, activity_date: event.target.value });
    if (date && !date.getHours() && !date.getMinutes()) {
      date.setHours(0);
      date.setMinutes(0);
    }
    setInitialDate(date);
    const inputDate = new Date(date);
    const utcDateString = inputDate.toISOString();
    setCreateTaskForm({
      ...createTaskForm,
      activity_date: moment(date).format("YYYY-MM-DD"),
      activity_date_gmt: utcDateString,
    });
  };
  const aidateChangeHandler = (date) => {
    if (date && !date.getHours() && !date.getMinutes()) {
      date.setHours(0);
      date.setMinutes(0);
    }
    setAiInitialDate(date);
    const inputDate = new Date(date);
    const utcDateString = inputDate.toISOString();
    setCreateTaskForm({
      ...createTaskForm,
      ai_form_heat_date_and_time: inputDate,
    });
  };

  const activitySelectHandler = (event, activityId) => {
    console.log({
      ...createTaskForm,
      activity_id: parseInt(activityId),
    });
    setCreateTaskForm({
      ...createTaskForm,
      activity_id: parseInt(activityId),
    });
  };

  const createTaskHandler = () => {
    console.log(createTaskForm);
    if (!createTaskForm.activity_date) {
      ToastersService.failureToast("Select activity date!");
    } else if (!createTaskForm.activity_id) {
      ToastersService.failureToast("Select activity!");
      // } else if (!createTaskForm.calendar_activity_status) {
      //   ToastersService.failureToast("Select activity status!");
    } else if (
      createTaskForm.activity_id === 1000470001 &&
      !createTaskForm.ai_form_heat_date_and_time
    ) {
      ToastersService.failureToast("Select ai heat date and time!");
    } else {
      // console.log("createTaskForm", createTaskForm);
      createTask(
        createTaskForm,
        selectedCattle,
        navigate,
        setCreateTaskLoader,
        handlePreviewModalShow,
        setPreviewData
      );
    }
  };

  const completionDateHandler = (event) => {
    console.log(event.target.value);
    setFollowUpReqBody({
      ...followUpReqBody,
      // completion_date: event.target.value,
    });
  };

  const followUpDateHandler = (date) => {
    if (date && !date.getHours() && !date.getMinutes()) {
      date.setHours(0);
      date.setMinutes(0);
    }
    setInitialDateFollowUp(date);
    const inputDate = new Date(date);
    const utcDateString = inputDate.toISOString();
    setFollowUpReqBody({
      ...followUpReqBody,
      activity_date: moment(date).format("YYYY-MM-DD"),
      activity_date_gmt: utcDateString,
    });
  };

  const createBtnHandler = () => {
    if (
      // followUpReqBody.completion_date == "" ||
      followUpReqBody.activity_date == ""
    ) {
      ToastersService.failureToast("All fields are mandatory!");
    } else {
      console.log("followUpReqBody", followUpReqBody);
      createFollowUp(
        followUpReqBody,
        handleCreateFollowUpModalClose,
        selectedCattle,
        setPastActivities,
        navigate,
        currentPage,
        setTotalPages,
        setPreviewData,
        handlePreviewModalShow
      );
    }
  };

  const testRef = useRef(null);
  const scrollToElement = () => testRef.current.scrollIntoView();

  const changePageNumber = async (event, value) => {
    console.log("New page number", value);
    setCurrentPage(value);
    let tempPastActivities = await getCattleLastFiveActivities(
      selectedCattle,
      value,
      navigate,
      setTotalPages
    );
    setPastActivities(tempPastActivities);
    scrollToElement();
  };
  const showTextDot = (text) => {
    const maxLength = screenSize <= 1440 ? 10 : 20;
    return text.length > maxLength ? text.slice(0, maxLength) + "..." : text;
  };
  const LightTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: theme.palette.common.white,
      color: "rgba(0, 0, 0, 0.87)",
      boxShadow: theme.shadows[1],
      fontSize: 11,
    },
  }));

  const tableDataFarmer = farmersList?.map((farmer) => ({
    key: farmer?.farmer_id,
    visualId: farmer?.farmer_visual_id,
    name: farmer?.farmer_name_l10n?.en || farmer?.farmer_name_l10n?.ul,
    contact: farmer?.farmer_mobile_number,
    village: farmer?.village_name_l10n?.en || farmer?.village_name_l10n?.ul,
    taluka: farmer?.taluk_name_l10n?.en || farmer?.taluk_name_l10n?.ul,
    district: farmer?.district_name_l10n?.en || farmer?.district_name_l10n?.ul,
    alt_contact: farmer?.farmer_alt_mobile_number,
  }));

  const rowSelectionFarmer = {
    selectedRowKeys:
      tableDataFarmer?.length > 0 ? [tableDataFarmer[0].key] : null,
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        "selectedRows: ",
        selectedRows
      );
    },
  };

  const tableColumnsFarmer = [
    {
      title: "Visual Id",
      dataIndex: "visualId",
      key: "visualId",
      width: 250,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Contact",
      dataIndex: "contact",
      key: "contact",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Alt Contact",
      dataIndex: "alt_contact",
      key: "alt_contact",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Village",
      dataIndex: "village",
      key: "village",
      width: 150,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Taluka",
      dataIndex: "taluka",
      key: "taluka",
      width: 150,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "District",
      dataIndex: "district",
      key: "district",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Summary",
      dataIndex: "summary",
      key: "summary",
      width: 200,
      render: (_, record) => (
        <Button
          style={{ background: "#ff6e1b" }}
          onClick={() => showSummaryModal(record?.key)}
        >
          Summary
        </Button>
      ),
    },
  ];

  const tableDataCattle = selectedFarmer?.cattles?.map((cattle, index) => ({
    key: cattle?.animal_id,
    visualId: cattle?.animal_visual_id,
    earTag: cattle?.animal_ear_tag,
    type: cattle?.animal_type_l10n?.en || cattle?.animal_type_l10n?.ul,
    status: cattle?.animal_active_status,
    subscription_plan_1: cattle?.subscription_plan_1,
    healthcare_plan_subscription_date_1:
      cattle?.healthcare_plan_subscription_date_1,
    inactiveDate: cattle?.inactive_date_1,
    reason: cattle?.inactive_reason_1,
    reasonNote: cattle?.inactive_reason_note_1,
    cattle_milking_status: cattle?.cattle_milking_status,
    last_calving_date: cattle?.last_calving_date,
    number_of_months_pregnant: cattle?.number_of_months_pregnant,
    summary: index,
  }));

  const rowSelectionCattle = {
    onChange: (selectedRowKeys) => {
      cattleItemClickHandler(selectedRowKeys[0]);
    },
    getCheckboxProps: (record) => ({
      disabled: record?.status !== **********,
    }),
  };

  const tableColumnsCattle = [
    {
      title: "Visual Id",
      dataIndex: "visualId",
      key: "visualId",
      fixed: "left",
      width: 250,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Ear Tag",
      dataIndex: "earTag",
      key: "earTag",
      width: 200,
      fixed: "left",
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 150,
      render: (text, record) =>
        record.status === null ? (
          "-"
        ) : record.status === ********** ? (
          <span
            className="staff__badge"
            style={{
              backgroundColor: "#D1FAE5",
              color: "#059691",
            }}
          >
            Active
          </span>
        ) : (
          <span
            className="staff__badge"
            style={{
              backgroundColor: "#FFE4E6",
              color: "#E11D48",
            }}
          >
            Inactive
          </span>
        ),
    },
    {
      title: "Subscription Plan",
      dataIndex: "subscription_plan_1",
      key: "subscription_plan_1",
      width: 200,
      render: (text) =>
        text !== null ? extractBasedOnLanguageMod(text, "en") : "-",
    },
    {
      title: "Subscription Date",
      dataIndex: "healthcare_plan_subscription_date_1",
      key: "healthcare_plan_subscription_date_1",
      width: 200,
      render: (text) =>
        text !== null ? <Moment format="DD MMM, YYYY">{text}</Moment> : "-",
    },
    {
      title: "Milking Status",
      dataIndex: "cattle_milking_status",
      key: "cattle_milking_status",
      width: 200,
      render: (text, record) =>
        record.cattle_milking_status === null ? (
          "-"
        ) : record.cattle_milking_status === ********** ? (
          <span
            className="staff__badge"
            style={{
              backgroundColor: "#D1FAE5",
              color: "#059691",
            }}
          >
            Yes
          </span>
        ) : (
          <span
            className="staff__badge"
            style={{
              backgroundColor: "#FFE4E6",
              color: "#E11D48",
            }}
          >
            No
          </span>
        ),
    },
    {
      title: "Last Calving",
      dataIndex: "last_calving_date",
      key: "last_calving_date",
      width: 250,
      render: (text) =>
        text !== null ? <Moment format="DD MMM, YYYY">{text}</Moment> : "-",
    },
    {
      title: "# Months (Pregnant)",
      dataIndex: "number_of_months_pregnant",
      key: "number_of_months_pregnant",
      width: 250,
      render: (text) =>
        text !== null && text !== "" ? parseInt(text, 10) : "-",
    },
    {
      title: "Inactive Date",
      dataIndex: "inactiveDate",
      key: "inaciveDate",
      width: 200,
      render: (text) =>
        text !== null ? <Moment format="DD MMM, YYYY">{text}</Moment> : "-",
    },
    {
      title: "Reason",
      dataIndex: "reason",
      key: "reason",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Reason Note",
      dataIndex: "reasonNote",
      key: "reasonNote",
      width: 200,
      render: (text) => (
        <LightTooltip
          title={text ? text : ""}
          placement="top"
          followCursor={true}
          style={{ pointerEvents: "auto" }}
        >
          {text ? showTextDot(text) : "-"}
        </LightTooltip>
      ),
    },
  ];

  const tableDataPastActivity = pastActivities?.map((activity) => ({
    key: activity?.care_calendar_id,
    ticketId: activity?.ticket_id,
    activityDate: activity?.activity_date,
    category:
      activity?.activity_category?.en || activity?.activity_category?.ul,
    activityName:
      activity?.activity_name_l10n?.en || activity?.activity_name_l10n?.ul,
    status:
      activity?.calendar_status_l10n?.en || activity?.calendar_status_l10n?.ul,
    paravet: activity?.staff_name?.en || activity?.staff_name?.ul,
    actions: activity?.care_calendar_id,
    calendarStatus: activity?.calendar_status,
  }));

  const tableColumnsPastActivity = [
    {
      title: "Ticket Id",
      dataIndex: "ticketId",
      key: "ticketId",
      width: 250,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Activity Date",
      dataIndex: "activityDate",
      key: "activityDate",
      width: 200,
      render: (text) =>
        text !== null ? <Moment format="DD MMM, YYYY">{text}</Moment> : "-",
    },
    {
      title: "Category",
      dataIndex: "category",
      key: "category",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Activity Name",
      dataIndex: "activityName",
      key: "activityName",
      width: 150,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 150,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Paravet",
      dataIndex: "paravet",
      key: "paravet",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      width: 200,
      render: (_, record) =>
        record?.calendarStatus === 1000300050 && (
          <>
            <button
              className="btn btn-sm btn-info"
              onClick={() => handleCreateFollowUpModalShow(record?.actions)}
            >
              Re-open
            </button>
          </>
        ),
    },
  ];
  console.log(createTaskForm);
  return (
    <>
      <div className="my-3" style={{ display: "flex", alignItems: "center" }}>
        <div>
          <p
            className="m-0"
            style={{ fontSize: "14px", fontWeight: "400", color: "gray" }}
          >
            Service Requests
          </p>
          <p
            className="m-0"
            style={{ fontSize: "18px", fontWeight: "700", color: "#222222" }}
          >
            Raise Service Request
          </p>
        </div>
      </div>
      <hr />
      <div className="mb-5">
        <div
          className="add-farmer__floating-menu mt-3 p-2"
          style={{ position: "relative" }}
        >
          {srTabs.map((tab) => (
            <div
              key={tab.count}
              className={`sr__tab-header ${
                tab.isSelected === true && "selected"
              }`}
            >
              <div
                className={`sr__tab-header-count ${
                  tab.isSelected === true && "selected"
                }`}
              >
                {tab.count}
              </div>
              <div>{tab.name}</div>
            </div>
          ))}
          <div className="sr__tab-header-line"></div>
        </div>

        {/* Page 1 */}
        {srTabs[0].isSelected === true && (
          <div>
            <div style={{ display: "flex" }}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  margin: "auto",
                }}
              >
                <Box style={{ minWidth: "160px" }}>
                  <FormControl fullWidth style={{ backgroundColor: "#ececec" }}>
                    <Select
                      labelId="demo-simple-select-label"
                      id="demo-simple-select"
                      value={search.selectedSearchOption}
                      onChange={handleSearchOptionSelect}
                    >
                      {searchOptions.map((item) => (
                        <MenuItem key={item.id} value={item.id}>
                          {item.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
                <TextField
                  style={{ minWidth: "300px" }}
                  value={search.searchInput}
                  placeholder={
                    search.selectedSearchOption == 1
                      ? "Enter Farmer Contact"
                      : "Enter Cattle Eartag"
                  }
                  disabled={!search.selectedSearchOption}
                  onChange={searchInputHandler}
                />
                <div className="ms-4">
                  <Button
                    variant="primary"
                    style={{
                      background: "#673AB7",
                      color: "#fff",
                      border: "none",
                      height: "54px",
                    }}
                    disabled={!search.selectedSearchOption}
                    onClick={findBtnHandler}
                  >
                    <i className="fa fa-search" aria-hidden="true"></i> Find
                  </Button>
                </div>
              </div>
            </div>
            <hr />

            {farmersList && (
              <>
                {farmersList.length > 0 ? (
                  <>
                    <div>
                      <p
                        style={{
                          fontSize: "18px",
                          fontWeight: "700",
                          color: "#222222",
                        }}
                      >
                        Select farmer:
                      </p>
                      <Table
                        rowSelection={{
                          type: "radio",
                          ...rowSelectionFarmer,
                        }}
                        t
                        columns={tableColumnsFarmer}
                        dataSource={tableDataFarmer}
                        pagination={false}
                      />
                    </div>
                    <hr />
                  </>
                ) : (
                  <div
                    className="text-center my-5"
                    style={{ color: "#4c4c4c" }}
                  >
                    <i
                      className="fa fa-exclamation-triangle"
                      aria-hidden="true"
                    ></i>{" "}
                    No farmers found!
                  </div>
                )}
              </>
            )}

            {selectedFarmer && (
              <>
                {selectedFarmer.cattles.length > 0 ? (
                  <div>
                    <p
                      style={{
                        fontSize: "18px",
                        fontWeight: "700",
                        color: "#222222",
                      }}
                    >
                      Select cattle:
                    </p>
                    <Table
                      scroll={{ x: 1300 }}
                      rowSelection={{
                        type: "radio",
                        ...rowSelectionCattle,
                      }}
                      dataSource={tableDataCattle}
                      columns={tableColumnsCattle}
                      pagination={false}
                    />
                  </div>
                ) : (
                  <div
                    className="text-center my-5"
                    style={{ color: "#4c4c4c" }}
                  >
                    <i
                      className="fa fa-exclamation-triangle"
                      aria-hidden="true"
                    ></i>{" "}
                    No cattle found!
                  </div>
                )}
              </>
            )}

            {selectedCattle && (
              <div style={{ display: "flex" }}>
                <Button
                  variant="primary"
                  style={{
                    background: "#ec6237",
                    color: "#fff",
                    border: "none",
                    marginLeft: "auto",
                  }}
                  onClick={nextBtnHandler}
                >
                  Next <i className="fa fa-arrow-right" aria-hidden="true"></i>
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Page 2 */}
        {srTabs[1].isSelected === true && (
          <div ref={testRef}>
            {farmersList && farmersList[0]?.account_payment_type && (
              <PaymentMethodModal
                options={farmersList[0]?.account_payment_type}
                setCreateTaskForm={setCreateTaskForm}
                createTaskForm={createTaskForm}
              />
            )}
            <div>
              <Row>
                <Col lg={4} md={6} sm={12}>
                  <small>Activity Date and Time*:</small>
                  <DatePicker
                    style={{ width: "100%" }}
                    className="input-field input-width"
                    selected={initialDate}
                    onChange={dateChangeHandler}
                    showTimeSelect
                    dateFormat="Pp"
                    timeIntervals={5}
                    timeCaption="Time"
                    placeholderText="Please select a date and time"
                  />
                </Col>
                <Col lg={4} md={6} sm={12}>
                  <small>Select Activity Type*</small>
                  <select
                    className="input-field"
                    style={{ width: "100%" }}
                    defaultValue={"DEFAULT"}
                    onChange={activityTypeSelectHandler}
                  >
                    <option disabled value="DEFAULT">
                      Select
                    </option>
                    {activityConfig &&
                      Object.keys(activityConfig).map((key) => {
                        return (
                          <option
                            key={key}
                            value={key}
                            style={{ textTransform: "capitalize" }}
                          >
                            {key}
                          </option>
                        );
                      })}
                  </select>
                </Col>
                <Col lg={4} md={6} sm={12}>
                  <small>Select Activity*</small>
                  <Autocomplete
                    options={activityOptions}
                    getOptionLabel={(option) =>
                      option.activity_name_l10n.en
                        ? option.activity_name_l10n.en
                        : option.activity_name_l10n.ul
                    }
                    disabled={activityOptions.length === 0}
                    sx={{
                      width: "100%",
                      marginTop: "5px",
                      background: "#fff",
                    }}
                    renderInput={(params) => (
                      <TextField {...params} label="Search..." />
                    )}
                    onChange={(event, value) =>
                      activitySelectHandler(event, value.activity_id)
                    }
                  />
                </Col>
                {createTaskForm?.activity_id === 1000470001 && (
                  <Col lg={4} md={6} sm={12}>
                    <small>AI Heat Date and Time*:</small>
                    <DatePicker
                      style={{ width: "100%" }}
                      className="input-field input-width"
                      selected={aiInitialDate}
                      onChange={aidateChangeHandler}
                      showTimeSelect
                      dateFormat="Pp"
                      timeIntervals={5}
                      timeCaption="Time"
                      placeholderText="Please select a date and time"
                    />
                  </Col>
                )}
              </Row>
              <div
                className="mt-3"
                style={{ display: "flex", alignItems: "center" }}
              >
                <div>
                  <Button
                    variant="primary"
                    style={{
                      background: "#4c4c4c",
                      color: "#fff",
                      border: "none",
                    }}
                    onClick={backBtnHandler}
                  >
                    <i className="fa fa-arrow-left" aria-hidden="true"></i> Back
                  </Button>
                </div>

                {createTaskLoader == true && (
                  <div style={{ marginLeft: "auto" }}>
                    <Loader />
                  </div>
                )}

                {createTaskLoader == false && (
                  <div
                    className="farmers__add-farmer-btn"
                    onClick={createTaskHandler}
                  >
                    Create
                  </div>
                )}
              </div>
            </div>
            <hr />
            <div>
              <p
                style={{
                  fontSize: "18px",
                  fontWeight: "700",
                  color: "#222222",
                }}
              >
                Past activities:
              </p>

              {pastActivities.length > 0 ? (
                <Table
                  columns={tableColumnsPastActivity}
                  dataSource={tableDataPastActivity}
                  pagination={false}
                />
              ) : (
                <div className="text-center my-5" style={{ color: "#4c4c4c" }}>
                  <i
                    className="fa fa-exclamation-triangle"
                    aria-hidden="true"
                  ></i>{" "}
                  No past activities found!
                </div>
              )}
              <div
                className="mt-2 mb-5"
                style={{ display: "flex", justifyContent: "center" }}
              >
                <Stack spacing={2}>
                  <Pagination
                    count={totalPages}
                    color="secondary"
                    onChange={changePageNumber}
                  />
                </Stack>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Create Follow-up Modal */}
      <Modal
        show={showCreateFollowUpModal}
        onHide={handleCreateFollowUpModalClose}
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>Create Follow Up</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row>
            <Col lg={6} md={6} sm={12}>
              <small>Select Follow-up Date and Time*:</small>
              <div>
                <DatePicker
                  style={{ width: "100%" }}
                  className="input-field input-width"
                  selected={initialDateFollowUp}
                  onChange={followUpDateHandler}
                  showTimeSelect
                  dateFormat="Pp"
                  timeIntervals={5}
                  timeCaption="Time"
                  placeholderText="Please select a date and time"
                />
              </div>
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="primary"
            onClick={createBtnHandler}
            style={{ background: "#673AB7", color: "#fff", border: "none" }}
          >
            Create
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Preview Modal */}
      <Modal
        show={showPreviewModal}
        onHide={() => navigate("/tasks/" + previewData.care_calendar_id)}
        centered
        backdrop="static"
        style={{ background: "black" }}
      >
        <Modal.Header closeButton>
          <Modal.Title
            style={{
              width: "100%",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <div style={{ height: "30px" }}>
              <img alt="" src={logo} style={{ height: "100%" }} />
            </div>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {previewData && (
            <PreviewModalTable previewData={previewData?.previewData} />
          )}
        </Modal.Body>
      </Modal>
      <ATNDModal
        title=""
        open={isSummaryModalOpen}
        onCancel={handleCancelSummaryModal}
        footer={null}
        width={1000}
      >
        <Summary farmerId={farmerId} />
      </ATNDModal>
    </>
  );
};

// export default ServiceRequests;
export default withAuthorization(ServiceRequests, [CS_TEAM]);
