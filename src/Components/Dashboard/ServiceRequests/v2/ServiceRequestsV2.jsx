import { Box, Stack, Typography } from "@mui/material";

import useSRSteps from "./hooks/useSRSteps";
import SelectCattle from "./components/step1/SelectCattle";
import SelectService from "./components/step2/SelectService";
import SRPaymentMethod from "./components/step3/SRPaymentMethod";

import { useReducer, useRef } from "react";
import { Button } from "antd";
import SRPastActivities from "./components/step2/past-activity/SRPastActivities";

const { CS_TEAM, withAuthorization } = require("../../../user/authorize");

const initialState = {
  payload: {},
  followup: {},
  others: {},
};

const reducer = (state = initialState, action) => {
  switch (action.type) {
    case "CATTLE":
      return {
        ...state,
        payload: {
          ...state.payload,
          entity_uuid: action.payload,
        },
      };
    case "SERVICE":
      return {
        ...state,
        payload: {
          ...state.payload,
          ...action.payload,
        },
      };
    case "FOLLOWUP":
      return {
        ...state,
        followup: {
          ...state.followup,
          ...action.payload,
        },
      };
    case "OTHERS":
      return {
        ...state,
        others: {
          ...state.others,
          ...action.payload,
        },
      };
    default:
      return state;
  }
};

const ServiceRequestsV2 = () => {
  const { SRSteps, current, setCurrent } = useSRSteps();
  const [srPayload, dispatch] = useReducer(reducer, initialState);

  const ref = useRef();
  const componentMap = {
    0: SelectCattle,
    1: SelectService,
    2: SRPaymentMethod,
  };
  const Comp = componentMap[current];
  const handleSaveAndNext = () => {
    switch (current) {
      case 0:
        ref.current?.callPage0();
        break;
      case 1:
        ref.current?.callPage1();
        break;
      case 2:
        ref.current?.callPage2();
        break;
      default:
        break;
    }
  };
  return (
    <Box p={1} m={1}>
      <Typography
        sx={{ fontWeight: "700" }}
        gutterBottom
        variant="h6"
        component="h4"
      >
        Raise Service Request
      </Typography>
      <Box mt={2}>
        <SRSteps />
      </Box>
      <Box mt={2}>
        <Comp
          ref={ref}
          dispatch={dispatch}
          srPayload={srPayload}
          setCurrent={setCurrent}
        />
      </Box>

      <Stack mt={2} direction="row" justifyContent="flex-end" spacing={2}>
        {" "}
        <Button
          disabled={current === 0}
          onClick={() => setCurrent(current - 1)}
          type="primary"
        >
          Previous
        </Button>
        <Button
          type="primary"
          style={{ background: "#ec6237", color: "#fff" }}
          onClick={handleSaveAndNext}
        >
          {current === 2 ? "Create" : "Next"}
        </Button>
      </Stack>
      {current === 1 && (
        <SRPastActivities
          entity_uuid={srPayload.payload.entity_uuid}
          dispatch={dispatch}
          setCurrent={setCurrent}
          srPayload={srPayload}
        />
      )}
    </Box>
  );
};

export default withAuthorization(ServiceRequestsV2, [CS_TEAM]);
