import React, { useState } from "react";
import { Button, message, Steps, theme } from "antd";
const steps = [
  {
    title: "Select Cattle",
    content: "First-content",
  },
  {
    title: "Select Service",
    content: "Second-content",
  },
  {
    title: "Select Payment Method & Create",
    content: "Last-content",
  },
];

const useSRSteps = () => {
  const [current, setCurrent] = useState(0);

  const next = () => {
    setCurrent(current + 1);
  };
  const prev = () => {
    setCurrent(current - 1);
  };

  const items = steps.map((item) => ({
    key: item.title,
    title: item.title,
  }));

  const SRSteps = () => {
    return <Steps current={current} items={items} />;
  };

  const SRStepsController = ({ completedSteps }) => {
    const toShow = () => {
      if (
        (current === 0 && completedSteps.step1) ||
        (current === 1 && completedSteps.step2) ||
        (current === 2 && completedSteps.step3)
      ) {
        return true;
      }
      return false;
    };

    return (
      <div
        style={{
          marginTop: 24,
        }}
      >
        {current < steps.length - 1 && toShow() && (
          <Button type="primary" onClick={() => next()}>
            Next
          </Button>
        )}
        {current === steps.length - 1 && (
          <Button
            type="primary"
            onClick={() => message.success("Processing complete!")}
          >
            Done
          </Button>
        )}
        {current > 0 && (
          <Button
            style={{
              margin: "0 8px",
            }}
            onClick={() => prev()}
          >
            Previous
          </Button>
        )}
      </div>
    );
  };
  return {
    current,
    SRSteps,
    SRStepsController,
    setCurrent,
  };
};
export default useSRSteps;
