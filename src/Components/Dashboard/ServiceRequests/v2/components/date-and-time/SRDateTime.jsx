import React, { useState } from "react";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
// import {} from "@mui/x-date-pickers/MobileDateTimePicker"
import {
  FormControlLabel,
  FormGroup,
  FormLabel,
  TextField,
} from "@mui/material";

const SRDateTime = ({ selectedDate, setSelectedDate, label }) => {
  //   const [selectedDate, setSelectedDate] = useState(null);

  const handleDateChange = (newValue) => {
    setSelectedDate(newValue);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <FormGroup>
        {/* <FormLabel>{}</FormLabel> */}
        <DateTimePicker
          label={label || "fallback label"}
          value={selectedDate}
          onChange={handleDateChange}
          renderInput={(params) => <TextField {...params} />}
        />
      </FormGroup>
    </LocalizationProvider>
  );
};

export default SRDateTime;
