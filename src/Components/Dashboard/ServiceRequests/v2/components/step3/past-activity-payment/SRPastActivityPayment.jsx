import { Box } from "@mui/material";
import { forwardRef, useImperativeHandle, useState } from "react";
import { Tag } from "antd";
import { FormProvider, useForm } from "react-hook-form";
import SRPastActivityPaymentMethodForm from "./SRPastActivityPaymentForm";
import dayjs from "dayjs";
import SRLoader from "../../loader/SRLoader";
import useSRPastActivityApi from "./hook/useSRPastActivityAPI";

const SRPastActivityPaymentMethod = forwardRef(
  ({ srPayload, setPreviewData, setCurrent }, ref) => {
    const [createTaskLoader, setCreateTaskLoader] = useState(false);
    const { createPastActivity } = useSRPastActivityApi();
    const methods = useForm();
    const { handleSubmit } = methods;

    const createTaskApi = async (payloadPayment) => {
      const followUpPayload = {
        care_calendar_id: srPayload?.followup?.care_calendar,
        follow_up_required: srPayload?.followup?.follow_up_required,
        get_preview: srPayload?.followup?.get_preview,
        activity_date: dayjs(srPayload?.followup?.activity_date).format(
          "YYYY-MM-DD"
        ),
        activity_date_gmt: srPayload?.followup?.activity_date,
        ai_form_heat_date_and_time:
          srPayload?.followup?.ai_form_heat_date_and_time || undefined,
        entity_uuid: srPayload?.payload?.entity_uuid,
      };
      setCreateTaskLoader(true);
      try {
        const res = await createPastActivity({
          ...followUpPayload,
          ...payloadPayment,
        });
        if (res?.data) {
          setPreviewData(res?.data?.data);
          setCurrent(2);
          setCreateTaskLoader(false);
        }
      } catch (error) {
        setCreateTaskLoader(false);
        console.log(error);
      }
    };

    const onSubmit = (data) => {
      const payloadPayment = {
        payment_details: {
          service_cost: {
            payer_type: data?.payer_service?.value,
            payment_type: data?.service_payment_method?.value,
          },
          medicine_cost: {
            payer_type: data?.payer_medicine?.value,
            payment_type: data?.medicine_payment_method?.value,
          },
        },
      };
      createTaskApi(payloadPayment);
    };

    useImperativeHandle(ref, () => ({
      reOpen() {
        handleSubmit(onSubmit)();
      },
    }));

    if (createTaskLoader) return <SRLoader />;

    return (
      <Box
        mt={6}
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          flexDirection: "column",
        }}
      >
        <Box mb={4}>
          <Tag color="purple" style={{ fontSize: "18px", lineHeight: "50px" }}>
            Credit Limit :{" "}
            {parseFloat(srPayload?.others?.customer_credit_limit) || "N/A"}
          </Tag>
          <Tag
            color="purple"
            style={{ fontSize: "18px", lineHeight: "50px", marginLeft: "30px" }}
          >
            Credit Usage :{" "}
            {parseFloat(srPayload?.others?.customer_credit_usage) || "N/A"}
          </Tag>
        </Box>
        <FormProvider {...methods}>
          <SRPastActivityPaymentMethodForm />
        </FormProvider>
      </Box>
    );
  }
);

export default SRPastActivityPaymentMethod;
