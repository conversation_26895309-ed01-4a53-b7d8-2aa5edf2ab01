import React, { useCallback, useEffect, useState } from "react";
import { instance } from "../../../../../../../../Services/api.service";
import ToastersService from "../../../../../../../../Services/toasters.service";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { clearUserToken } from "../../../../../../../user/action";

const useSRPastActivityApi = () => {
  const userToken = useSelector((state) => state.user.userToken);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const sendAutoGeneratedNote = (calendarId) => {
    let noteItem = "New Case Visit Pending";
    addTaskNote(noteItem, calendarId);
  };

  const addTaskNote = async (noteItem, calendarId) => {
    try {
      const response = await instance({
        url: "/common/add-task-note",
        method: "POST",
        data: {
          note: noteItem,
          calendar_id: calendarId,
        },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });

      if (response.status === 201) {
      } else {
        throw Error("Note not sent! Try again.");
      }
    } catch (error) {
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };
  const createPastActivity =
    async (
      payload
    ) => {
      try {
        const response = await instance({
          url: "/activity/create-task",
          method: "POST",
          data: payload,
          headers: {
            "Content-Type": "application/json",
            token: userToken.accessToken,
          },
        });

        if (response.status === 201) {
          ToastersService.successToast("Task created successfully!");
          sendAutoGeneratedNote(response?.data?.id, navigate);
        //   setPreviewData(response?.data?.data);
        //   setCreateTaskLoader(false);
        //   handlePreviewModalShow();
        //   handleClose()
        return response
        } else {
          throw Error("Task not created! Try again.");
        }
      } catch (error) {
        // setCreateTaskLoader(false);
        if ("response" in error && error.response.status === 401) {
          dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
        }
      }
    }
  return { createPastActivity };
};
export default useSRPastActivityApi;
