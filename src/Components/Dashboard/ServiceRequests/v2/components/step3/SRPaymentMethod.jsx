import {
  Box,
  Grid,
  Autocomplete,
  TextField,
  FormLabel,
  Typography,
} from "@mui/material";
import { forwardRef, useImperativeHandle, useState, useEffect } from "react";
import { globalReferencesMapper } from "../../../../../../Utilities/Common/utils";
import { Button, Col, Modal, Row } from "react-bootstrap";
import { PreviewModalTable } from "../../../PreviewModalTable";
import { useNavigate } from "react-router-dom";
import logo from "../../../krushal.png";
import useSRStep3Api from "./hook/useSRStep3-api";
import { message, Tag } from "antd";
import { FormProvider, useForm } from "react-hook-form";
import SRPaymentMethodForm from "./SRPaymentMethodForm";
import dayjs from "dayjs";

const SRPaymentMethod = forwardRef(
  ({ dispatch, srPayload, setCurrent }, ref) => {
    const navigate = useNavigate();
    const [createTaskLoader, setCreateTaskLoader] = useState(false);
    const [previewData, setPreviewData] = useState();
    const [showPreviewModal, setShowPreviewModal] = useState(false);
    const { createTask, paymentDetails, isPaymentDetailsLoading } =
      useSRStep3Api(srPayload?.payload);
    const methods = useForm();
    const { handleSubmit, reset } = methods;

    const handlePreviewModalShow = () => {
      setShowPreviewModal(true);
    };
    console.log(srPayload, "srPaylaod sr")
    const createTaskApi = async (payloadPayment) => {
      const modifiyedPayload = {
        entity_uuid: srPayload?.payload.entity_uuid || undefined,
        activity_date: dayjs(srPayload?.payload?.activity_date).format(
          "YYYY-MM-DD"
        ),
        activity_id: srPayload?.payload?.activity_id?.activity_id,
        ai_form_heat_date_and_time:
          srPayload?.payload?.ai_form_heat_date_and_time || undefined,
        entity_type_id: srPayload?.payload?.entity_mapping?.value || undefined,
        activity_date_gmt: srPayload?.payload?.activity_date,
        get_preview: srPayload?.payload?.get_preview,
      };

      const res = await createTask(
        {
          ...modifiyedPayload,
          ...payloadPayment,
        },
        setCreateTaskLoader,
        handlePreviewModalShow,
        setPreviewData
      );
    };
    const onSubmit = (data) => {
      const payloadPayment = {
        payment_details: {
          service_cost: {
            payer_type: data?.payer_service?.value,
            payment_type: data?.service_payment_method?.value,
          },
          medicine_cost: {
            payer_type: data?.payer_medicine?.value,
            payment_type: data?.medicine_payment_method?.value,
          },
        },
      };
      createTaskApi(payloadPayment);
      dispatch({
        type: "SERVICE",
        payload: payloadPayment,
      });
    };
    useImperativeHandle(ref, () => ({
      callPage2() {
        handleSubmit(onSubmit)();
      },
    }));
    return (
      <Box
        mt={6}
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          flexDirection: "column",
        }}
      >
        <Box mb={4}>
          <Tag color="purple" style={{ fontSize: "18px", lineHeight: "50px" }}>
            Credit Limit :{" "}
            {parseFloat(srPayload?.others?.customer_credit_limit) || "N/A"}
          </Tag>
          <Tag
            color="purple"
            style={{ fontSize: "18px", lineHeight: "50px", marginLeft: "30px" }}
          >
            Credit Usage :{" "}
            {parseFloat(srPayload?.others?.customer_credit_usage) || "N/A"}
          </Tag>
        </Box>
        <FormProvider {...methods}>
          <SRPaymentMethodForm
            paymentDetails={paymentDetails}
            isPaymentDetailsLoading={isPaymentDetailsLoading}
          />
        </FormProvider>
        <Box>
          <Modal
            show={showPreviewModal}
            onHide={() => navigate("/tasks/" + previewData.care_calendar_id)}
            centered
            backdrop="static"
            style={{ background: "black" }}
          >
            <Modal.Header closeButton>
              <Modal.Title
                style={{
                  width: "100%",
                  display: "flex",
                  justifyContent: "center",
                }}
              >
                <div style={{ height: "30px" }}>
                  <img alt="" src={logo} style={{ height: "100%" }} />
                </div>
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              {previewData && (
                <PreviewModalTable previewData={previewData?.previewData} />
              )}
            </Modal.Body>
          </Modal>
        </Box>
      </Box>
    );
  }
);

export default SRPaymentMethod;
