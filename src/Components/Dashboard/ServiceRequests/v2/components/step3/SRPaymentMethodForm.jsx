import { useEffect, useMemo } from "react";
import { Autocomplete, Box, Grid, TextField, Typography } from "@mui/material";
import { useForm<PERSON>ontext, Controller } from "react-hook-form";
import {
  globalReferencesMapper,
  extractBasedOnLanguageMod,
} from "../../../../../../Utilities/Common/utils";
import { Spin } from "antd";

const SRPaymentMethodForm = ({ paymentDetails, isPaymentDetailsLoading }) => {
  const {
    watch,
    control,
    setValue,
    reset,
    formState: { errors },
  } = useFormContext();

  const payer = globalReferencesMapper(10013400).map((item) => ({
    label: extractBasedOnLanguageMod(item.reference_name_l10n, "en"),
    value: item.reference_id,
    paymentType: item.reference_information?.payment_type,
  }));

  const paymentMethodList = globalReferencesMapper(10013100);

  const formattedPaymentMethodList =
    globalReferencesMapper(10013100)?.map((item) => ({
      label: extractBasedOnLanguageMod(item.reference_name_l10n, "en"),
      value: item?.reference_id,
    })) || [];

  const availablePaymentMethodService = paymentMethodList.filter((obj) =>
    watch("payer_service")?.paymentType?.includes(obj.reference_id?.toString())
  );

  const paymentMethodService =
    availablePaymentMethodService?.map((item) => ({
      label: extractBasedOnLanguageMod(item.reference_name_l10n, "en"),
      value: item?.reference_id,
    })) || [];

  const availablePaymentMethodMedicine = paymentMethodList.filter((obj) =>
    watch("payer_medicine")?.paymentType?.includes(obj.reference_id?.toString())
  );

  const paymentMethodMedicine =
    availablePaymentMethodMedicine?.map((item) => ({
      label: extractBasedOnLanguageMod(item.reference_name_l10n, "en"),
      value: item?.reference_id,
    })) || [];

  useEffect(() => {
    if (paymentDetails) {
      const servicePayer =
        payer.find(
          (p) => p.value === paymentDetails?.service_cost?.payer_type
        ) || null;

      const servicePaymentMethod =
        formattedPaymentMethodList.find(
          (p) => p.value === paymentDetails?.service_cost?.payment_type
        ) || null;

      const medicinePayer =
        payer.find(
          (p) => p.value === paymentDetails?.medicine_cost?.payer_type
        ) || null;

      const medicinePaymentMethod =
        formattedPaymentMethodList.find(
          (p) => p.value === paymentDetails?.medicine_cost?.payment_type
        ) || null;

      reset({
        payer_service: servicePayer,
        service_payment_method: servicePaymentMethod,
        payer_medicine: medicinePayer,
        medicine_payment_method: medicinePaymentMethod,
      });
    }
  }, [paymentDetails, reset, watch, isPaymentDetailsLoading]);

  if (isPaymentDetailsLoading) return <Spin />;

  return (
    <Box sx={{ width: "50%" }}>
      <Grid container spacing={2}>
        <Grid item md={2.5}>
          <Typography sx={{ fontWeight: "bold", marginTop: "1rem" }}>
            Service Cost
          </Typography>
        </Grid>
        <Grid item md={4}>
          <Controller
            name="payer_service"
            control={control}
            rules={{ required: { value: true, message: "This is required" } }}
            render={({ field: { onChange, value, ...field } }) => (
              <Autocomplete
                options={payer}
                value={value || null}
                isOptionEqualToValue={(option, value) =>
                  option.value === value?.value
                }
                getOptionLabel={(option) => option.label || ""}
                onChange={(event, newValue) => {
                  onChange(newValue);
                  setValue("service_payment_method", null);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Payer*"
                    error={Boolean(errors.payer_service)}
                    helperText={errors?.payer_service?.message}
                  />
                )}
                {...field}
              />
            )}
          />
        </Grid>
        <Grid item md={4}>
          <Controller
            name="service_payment_method"
            control={control}
            rules={{ required: { value: true, message: "This is required" } }}
            render={({ field: { onChange, value, ...field } }) => (
              <Autocomplete
                options={paymentMethodService}
                value={value || null}
                isOptionEqualToValue={(option, value) =>
                  option.value === value?.value
                }
                onChange={(event, newValue) => {
                  onChange(newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Payment Method*"
                    error={Boolean(errors.service_payment_method)}
                    helperText={errors?.service_payment_method?.message}
                  />
                )}
                {...field}
              />
            )}
          />
        </Grid>
      </Grid>
      <Grid container spacing={2} mt={4}>
        <Grid item md={2.5}>
          <Typography sx={{ fontWeight: "bold", marginTop: "1rem" }}>
            Medicine Cost
          </Typography>
        </Grid>
        <Grid item md={4}>
          <Controller
            name="payer_medicine"
            control={control}
            rules={{ required: { value: true, message: "This is required" } }}
            render={({ field: { onChange, value, ...field } }) => (
              <Autocomplete
                options={payer}
                value={value || null}
                isOptionEqualToValue={(option, value) =>
                  option.value === value?.value
                }
                onChange={(event, newValue) => {
                  onChange(newValue);
                  setValue("medicine_payment_method", null);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Payer*"
                    error={Boolean(errors.payer_medicine)}
                    helperText={errors?.payer_medicine?.message}
                  />
                )}
                {...field}
              />
            )}
          />
        </Grid>
        <Grid item md={4}>
          <Controller
            name="medicine_payment_method"
            control={control}
            rules={{ required: { value: true, message: "This is required" } }}
            render={({ field: { onChange, value, ...field } }) => (
              <Autocomplete
                options={paymentMethodMedicine}
                value={value || null}
                isOptionEqualToValue={(option, value) =>
                  option.value === value?.value
                }
                onChange={(event, newValue) => {
                  onChange(newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Payment Method*"
                    error={Boolean(errors.medicine_payment_method)}
                    helperText={errors?.medicine_payment_method?.message}
                  />
                )}
                {...field}
              />
            )}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default SRPaymentMethodForm;
