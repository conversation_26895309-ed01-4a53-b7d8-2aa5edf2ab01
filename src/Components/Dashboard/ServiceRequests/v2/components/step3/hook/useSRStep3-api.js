import React, { useCallback, useEffect, useState } from "react";
import { instance } from "../../../../../../../Services/api.service";
import ToastersService from "../../../../../../../Services/toasters.service";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { clearUserToken } from "../../../../../../user/action";

const useSRStep3Api = (data) => {
  const userToken = useSelector((state) => state.user.userToken);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [isPaymentDetailsLoading, setIsPaymentDetailsLoading] = useState(false);
  const [paymentDetails, setPaymentDetails] = useState();

  const sendAutoGeneratedNote = (calendarId) => {
    let noteItem = "New Case Visit Pending";
    addTaskNote(noteItem, calendarId);
  };

  const addTaskNote = async (noteItem, calendarId) => {
    try {
      const response = await instance({
        url: "/common/add-task-note",
        method: "POST",
        data: {
          note: noteItem,
          calendar_id: calendarId,
        },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });

      if (response.status === 201) {
      } else {
        throw Error("Note not sent! Try again.");
      }
    } catch (error) {
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };
  const createTask = async (
    payload,
    setCreateTaskLoader,
    handlePreviewModalShow,
    setPreviewData,
    setCheck
  ) => {
    setCreateTaskLoader(true);
    try {
      const response = await instance({
        url: "/activity/create-task",
        method: "POST",
        data: payload,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });

      if (response.status === 201) {
        ToastersService.successToast("Task created successfully!");
        sendAutoGeneratedNote(response.data.id, navigate);
        setPreviewData(response.data.data);
        setCreateTaskLoader(false);
        handlePreviewModalShow();
      } else {
        throw Error("Task not created! Try again.");
      }
    } catch (error) {
      setCreateTaskLoader(false);
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };
  const getPaymentDetails = async (payload) => {
    try {
      setIsPaymentDetailsLoading(true);
      const response = await instance({
        url: "/task/payment-details",
        method: "POST",
        data: payload,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });

      if (response.status === 201 || response.status === 200) {
        setIsPaymentDetailsLoading(false);
        setPaymentDetails(response.data);
      } else {
        throw new Error("No data");
      }
    } catch (error) {
      setIsPaymentDetailsLoading(false);
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  };

  useEffect(() => {
    getPaymentDetails({
      entity_uuid: data?.entity_uuid,
      entity_type_id: data?.entity_mapping?.value,
      activity_category_id: data?.activity_id?.activity_category,
      activity_id: data?.activity_id?.activity_id,
    });
  }, []);
  return { createTask, paymentDetails, isPaymentDetailsLoading };
};
export default useSRStep3Api;
