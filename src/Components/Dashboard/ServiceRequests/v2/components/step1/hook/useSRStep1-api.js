import React, { useEffect, useState } from "react";
import { instance } from "../../../../../../../Services/api.service";
import ToastersService from "../../../../../../../Services/toasters.service";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { clearUserToken } from "../../../../../../user/action";

const useSRStep1Api = ({ setIsLoading }) => {
  const userToken = useSelector((state) => state.user.userToken);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const findFarmerByMobileNo = React.useCallback(async (farmerContact) => {
    let reqBody = { f_farmer_mobile: farmerContact };
    try {
      setIsLoading(true);
      const response = await instance({
        url: "/find/farmer",
        method: "POST",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status == 201 || response.status === 200) {
        setIsLoading(false);
        return response.data.data[0];
      } else if (response.status == 204) {
        setIsLoading(false);
        ToastersService.failureToast("Invalid mobile number");
        return [];
      }
    } catch (error) {
      setIsLoading(false);
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  }, []);
  const findFarmerByCattleEarTag = React.useCallback(async (eartag) => {
    let reqBody = { f_ear_tag: eartag };
    try {
      setIsLoading(true);
      const response = await instance({
        url: "/find/animal",
        method: "POST",
        data: reqBody,
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });
      if (response.status == 201) {
        setIsLoading(false);
        return response.data.data[0];
      } else if (response.status == 204) {
        setIsLoading(false);
        ToastersService.failureToast("Invalid ear tag number");
        return [];
      }
    } catch (error) {
      setIsLoading(false);
      if ("response" in error && error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
      }
    }
  }, []);

  return {
    findFarmerByCattleEarTag,
    findFarmerByMobileNo,
  };
};
export default useSRStep1Api;
