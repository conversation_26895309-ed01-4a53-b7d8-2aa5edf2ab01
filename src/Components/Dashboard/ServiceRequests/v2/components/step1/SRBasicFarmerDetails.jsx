import { Box, Grid, Typography } from "@mui/material";
import { extractBasedOnLanguageMod } from "../../../../../../Utilities/Common/utils";
import { Table, Modal, Button } from "antd";
import Summary from "../../../summary/index";
import { useState } from "react";

const applicableOn = { value: 1000460001, label: "Farm" };

const SRBasicFarmerDetails = ({ farmerDetails, dispatch, srPayload }) => {
  const [isSummaryModalOpen, setIsSummaryModalOpen] = useState(false);
  const showSummaryModal = () => {
    setIsSummaryModalOpen(true);
  };

  const handleCancelSummaryModal = () => {
    setIsSummaryModalOpen(false);
  };
  // rowSelection object indicates the need for row selection
  // rowSelection object indicates the need for row selection
  const rowSelection = {
    selectedRowKeys: [srPayload?.payload?.entity_uuid],
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        "selectedRows: ",
        selectedRows
      );
      if (selectedRows?.length > 0) {
        dispatch({
          type: "SERVICE",
          payload: {
            entity_mapping: applicableOn,
            entity_uuid: selectedRowKeys[0],
          },
        });
      } else {
        dispatch({
          type: "SERVICE",
          payload: { entity_mapping: undefined, entity_uuid: undefined },
        });
      }
    },
  };
  const tableColumnsFarmer = [
    {
      title: "Visual Id",
      dataIndex: "visualId",
      key: "visualId",
      width: 250,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Contact",
      dataIndex: "contact",
      key: "contact",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Alt Contact",
      dataIndex: "alt_contact",
      key: "alt_contact",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Village",
      dataIndex: "village",
      key: "village",
      width: 150,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Taluka",
      dataIndex: "taluka",
      key: "taluka",
      width: 150,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "District",
      dataIndex: "district",
      key: "district",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Summary",
      dataIndex: "summary",
      key: "summary",
      width: 200,
      render: (_, record) => (
        <Button
          style={{ background: "#ff6e1b", color: "#fff" }}
          onClick={showSummaryModal}
        >
          View
        </Button>
      ),
    },
  ];

  const tableDataFarmer = [
    {
      key: farmerDetails.farmer_id || "",
      visualId: farmerDetails.farmer_visual_id || "",
      name:
        farmerDetails.farmer_name_l10n?.en ||
        farmerDetails.farmer_name_l10n?.ul ||
        "",
      contact: farmerDetails.farmer_mobile_number || "",
      village:
        farmerDetails.village_name_l10n?.en ||
        farmerDetails.village_name_l10n?.ul ||
        "",
      taluka:
        farmerDetails.taluk_name_l10n?.en ||
        farmerDetails.taluk_name_l10n?.ul ||
        "",
      district:
        farmerDetails.district_name_l10n?.en ||
        farmerDetails.district_name_l10n?.ul ||
        "",
      alt_contact: farmerDetails.farmer_alt_mobile_number || "",
    },
  ];

  return (
    <>
      <Box>
        <Typography fontWeight={700} variant="h6" gutterBottom>
          Farmer Details
        </Typography>
        <Table
          rowSelection={{
            type: "radio",
            ...rowSelection,
          }}
          columns={tableColumnsFarmer}
          dataSource={tableDataFarmer}
          pagination={false}
        />
      </Box>
      <Modal
        title=""
        open={isSummaryModalOpen}
        onCancel={handleCancelSummaryModal}
        footer={null}
        width={1000}
      >
        <Summary farmerId={farmerDetails?.farmer_id} />
      </Modal>
    </>
  );
};

export default SRBasicFarmerDetails;
