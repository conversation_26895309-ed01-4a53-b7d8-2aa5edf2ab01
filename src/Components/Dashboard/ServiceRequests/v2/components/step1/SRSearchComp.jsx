import {
  Box,
  Button,
  FormControl,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { useState } from "react";
import useSRStep1Api from "./hook/useSRStep1-api";
import ToastersService from "../../../../../../Services/toasters.service";
const searchOptions = [
  {
    id: 1,
    name: "Mobile number",
  },
  {
    id: 2,
    name: "Ear tag",
  },
];

const SRSearchComp = ({ setFarmerDetails, setIsLoading }) => {
  const [search, setSearch] = useState({
    selectedSearchOption: 1,
    searchInput: "",
  });
  const { findFarmerByCattleEarTag, findFarmerByMobileNo } = useSRStep1Api({
    setIsLoading,
  });
  const handleSearchOptionSelect = (event) => {
    setSearch({
      ...search,
      selectedSearchOption: event.target.value,
      searchInput: "",
    });
  };

  const searchInputHandler = (event) => {
    setSearch({ ...search, searchInput: event.target.value });
  };

  const findBtnHandler = async () => {
    if (search.selectedSearchOption == 1) {
      if (search.searchInput.trim().length !== 10) {
        ToastersService.failureToast("Enter a valid mobile number!");
      } else {
        let tempFarmersList = await findFarmerByMobileNo(search.searchInput);
        setFarmerDetails(tempFarmersList);
      }
    } else if (search.selectedSearchOption == 2) {
      if (search.searchInput.trim().length == 0) {
        ToastersService.failureToast("Enter a valid ear tag!");
      } else {
        let tempFarmersList = await findFarmerByCattleEarTag(
          search.searchInput
        );
        setFarmerDetails(tempFarmersList);
      }
    }
  };

  return (
    <div style={{ display: "flex" }}>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          margin: "auto",
        }}
      >
        <Box style={{ minWidth: "160px" }}>
          <FormControl fullWidth style={{ backgroundColor: "#ececec" }}>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              value={search.selectedSearchOption}
              onChange={handleSearchOptionSelect}
            >
              {searchOptions.map((item) => (
                <MenuItem key={item.id} value={item.id}>
                  {item.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
        <TextField
          style={{ minWidth: "300px" }}
          value={search.searchInput}
          placeholder={
            search.selectedSearchOption == 1
              ? "Enter Farmer Contact"
              : "Enter Cattle Eartag"
          }
          disabled={!search.selectedSearchOption}
          onChange={searchInputHandler}
        />
        <div className="ms-4">
          <Button
            variant="primary"
            style={{
              background: "#673AB7",
              color: "#fff",
              border: "none",
              height: "54px",
            }}
            disabled={!search.selectedSearchOption}
            onClick={findBtnHandler}
          >
            <i className="fa fa-search" aria-hidden="true"></i> Find
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SRSearchComp;
