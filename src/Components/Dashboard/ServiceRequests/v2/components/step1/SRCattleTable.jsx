import Tooltip, { tooltipClasses } from "@mui/material/Tooltip";
import { styled } from "@mui/material/styles";
import { Table } from "antd";
import Moment from "react-moment";
import { extractBasedOnLanguageMod } from "../../../../../../Utilities/Common/utils";
import { useEffect, useState } from "react";
import { Box, Typography } from "@mui/material";

const applicableOn = { value: 1000460002, label: "Cattle" };

const LightTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.common.white,
    color: "rgba(0, 0, 0, 0.87)",
    boxShadow: theme.shadows[1],
    fontSize: 11,
  },
}));

const showTextDot = (text, screenSize) => {
  const maxLength = screenSize <= 1440 ? 10 : 20;
  return text.length > maxLength ? text.slice(0, maxLength) + "..." : text;
};
const SRCattleTable = ({
  farmerDetails,
  setCattleId,
  cattleId,
  dispatch,
  srPayload,
}) => {
  const [screenSize, setScreenSize] = useState(window.screen.width);
  
  console.log('farmerDetails',farmerDetails);

  const cattleItemClickHandler = (cattleId) => {
    for (let cattle of farmerDetails.cattles) {
      if (cattle.animal_id == cattleId) {
        cattle.isSelected = true;
        setCattleId(cattleId);
      } else {
        cattle.isSelected = false;
      }
    }
  };

  const rowSelectionCattle = {
    selectedRowKeys: [srPayload?.payload?.entity_uuid],
    onChange: (selectedRowKeys) => {
      console.log("selected Row", selectedRowKeys);
      cattleItemClickHandler(selectedRowKeys[0]);
      if (selectedRowKeys?.length > 0) {
        dispatch({
          type: "SERVICE",
          payload: {
            entity_mapping: applicableOn,
            entity_uuid: selectedRowKeys[0],
          },
        });
      } else {
        dispatch({
          type: "SERVICE",
          payload: { entity_mapping: undefined, entity_uuid: undefined },
        });
      }
    },
    getCheckboxProps: (record) => ({
      disabled: record?.status !== 1000100001,
    }),
  };
  const tableColumnsCattle = [
    {
      title: "Visual Id",
      dataIndex: "visualId",
      key: "visualId",
      fixed: "left",
      width: 250,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Ear Tag",
      dataIndex: "earTag",
      key: "earTag",
      width: 200,
      fixed: "left",
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 150,
      render: (text, record) =>
        record.status === null ? (
          "-"
        ) : record.status === 1000100001 ? (
          <span
            className="staff__badge"
            style={{
              backgroundColor: "#D1FAE5",
              color: "#059691",
            }}
          >
            Active
          </span>
        ) : (
          <span
            className="staff__badge"
            style={{
              backgroundColor: "#FFE4E6",
              color: "#E11D48",
            }}
          >
            Inactive
          </span>
        ),
    },
    {
      title: "Subscription Plan",
      dataIndex: "subscription_plan_1",
      key: "subscription_plan_1",
      width: 200,
      render: (text) =>
        text !== null ? extractBasedOnLanguageMod(text, "en") : "-",
    },
    {
      title: "Subscription Date",
      dataIndex: "healthcare_plan_subscription_date_1",
      key: "healthcare_plan_subscription_date_1",
      width: 200,
      render: (text) =>
        text !== null ? <Moment format="DD MMM, YYYY">{text}</Moment> : "-",
    },
    {
      title: "Milking Status",
      dataIndex: "cattle_milking_status",
      key: "cattle_milking_status",
      width: 200,
      render: (text, record) =>
        record.cattle_milking_status === null ? (
          "-"
        ) : record.cattle_milking_status === ********** ? (
          <span
            className="staff__badge"
            style={{
              backgroundColor: "#D1FAE5",
              color: "#059691",
            }}
          >
            Yes
          </span>
        ) : (
          <span
            className="staff__badge"
            style={{
              backgroundColor: "#FFE4E6",
              color: "#E11D48",
            }}
          >
            No
          </span>
        ),
    },
    {
      title: "Last Calving",
      dataIndex: "last_calving_date",
      key: "last_calving_date",
      width: 250,
      render: (text) =>
        text !== null ? <Moment format="DD MMM, YYYY">{text}</Moment> : "-",
    },
    {
      title: "# Months (Pregnant)",
      dataIndex: "number_of_months_pregnant",
      key: "number_of_months_pregnant",
      width: 250,
      render: (text) =>
        text !== null && text !== "" ? parseInt(text, 10) : "-",
    },
    {
      title: "Inactive Date",
      dataIndex: "inactiveDate",
      key: "inaciveDate",
      width: 200,
      render: (text) =>
        text !== null ? <Moment format="DD MMM, YYYY">{text}</Moment> : "-",
    },
    {
      title: "Reason",
      dataIndex: "reason",
      key: "reason",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Reason Note",
      dataIndex: "reasonNote",
      key: "reasonNote",
      width: 200,
      render: (text) => (
        <LightTooltip
          title={text ? text : ""}
          placement="top"
          followCursor={true}
          style={{ pointerEvents: "auto" }}
        >
          {text ? showTextDot(text, screenSize) : "-"}
        </LightTooltip>
      ),
    },
  ];
  const tableDataCattle = farmerDetails?.cattles?.map((cattle, index) => ({
    key: cattle?.animal_id,
    visualId: cattle?.animal_visual_id,
    earTag: cattle?.animal_ear_tag,
    type: cattle?.animal_type_l10n?.en || cattle?.animal_type_l10n?.ul,
    status: cattle?.animal_active_status,
    subscription_plan_1: cattle?.subscription_plan_1,
    healthcare_plan_subscription_date_1:
      cattle?.healthcare_plan_subscription_date_1,
    inactiveDate: cattle?.inactive_date_1,
    reason: cattle?.inactive_reason_1,
    reasonNote: cattle?.inactive_reason_note_1,
    cattle_milking_status: cattle?.cattle_milking_status,
    last_calving_date: cattle?.last_calving_date,
    number_of_months_pregnant: cattle?.number_of_months_pregnant,
    summary: index,
  }));

  useEffect(() => {
    const handleResize = () => {
      setScreenSize(window.screen.width);
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <Box>
      <Typography fontWeight={700} variant="h6" gutterBottom>
        Cattles
      </Typography>
      <Table
        scroll={{ x: 1300 }}
        rowSelection={{
          type: "radio",
          ...rowSelectionCattle,
        }}
        dataSource={tableDataCattle}
        columns={tableColumnsCattle}
        pagination={false}
      />
    </Box>
  );
};

export default SRCattleTable;
