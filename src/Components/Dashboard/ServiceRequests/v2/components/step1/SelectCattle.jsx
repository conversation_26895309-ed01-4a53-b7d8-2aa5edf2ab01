import { Box, Typography } from "@mui/material";
import SRSearchComp from "./SRSearchComp";
import { useEffect, useState } from "react";
import SRCattleTable from "./SRCattleTable";
import SRBasicFarmerDetails from "./SRBasicFarmerDetails";
import { forwardRef, useImperativeHandle } from "react";
import { Button, message, Spin } from "antd";
import SRLoader from "../loader/SRLoader";

const SelectCattle = forwardRef(({ dispatch, srPayload, setCurrent }, ref) => {
  const [farmerDetails, setFarmerDetails] = useState(srPayload?.others || []);
  const [cattleId, setCattleId] = useState(
    srPayload?.payload?.entity_uuid || ""
  );
  const [isLoading, setIsLoading] = useState(false);

  useImperativeHandle(ref, () => ({
    callPage0() {
      if (srPayload?.payload?.entity_uuid) {
        // dispatch({ type: "CATTLE", payload: cattleId });
        dispatch({ type: "OTHERS", payload: farmerDetails });
        setCurrent(1);
      } else {
        message.info(
          "Please enter mobile number or ear tag and select the cattle"
        );
      }
    },
  }));

  return (
    <Box>
      <SRSearchComp
        setFarmerDetails={setFarmerDetails}
        setIsLoading={setIsLoading}
      />
      {isLoading && <SRLoader />}
      {!isLoading && Object.keys(farmerDetails).length > 0 && (
        <>
          <Box>
            <SRBasicFarmerDetails
              farmerDetails={farmerDetails}
              dispatch={dispatch}
              srPayload={srPayload}
            />
          </Box>
          <Box>
            <SRCattleTable
              farmerDetails={farmerDetails}
              setCattleId={setCattleId}
              cattleId={cattleId}
              srPayload={srPayload}
              dispatch={dispatch}
            />
          </Box>
        </>
      )}
    </Box>
  );
});

export default SelectCattle;
