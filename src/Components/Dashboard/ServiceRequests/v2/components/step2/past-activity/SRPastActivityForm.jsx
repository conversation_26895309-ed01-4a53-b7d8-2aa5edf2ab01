import { Box, FormGroup, TextField, Typography } from "@mui/material";
import { Controller, useFormContext } from "react-hook-form";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";

const SRPastActivityForm = ({ showAIDate }) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  return (
    <Box>
      <FormGroup sx={{ width: 400 }}>
        <Controller
          name="activity_date"
          control={control}
          defaultValue={null}
          rules={{
            required: { value: true, message: "This is required" },
          }}
          render={({ field: { onChange, value, ...field } }) => (
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DateTimePicker
                label={"Select Activity Date*"}
                value={value}
                onChange={(newValue) => {
                  onChange(newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    error={Boolean(errors.ai_form_heat_date_and_time)}
                    helperText={errors?.ai_form_heat_date_and_time?.message}
                  />
                )}
                {...field}
              />
            </LocalizationProvider>
          )}
        />
      </FormGroup>
      {showAIDate && (
        <FormGroup>
          <hr />
          <Typography fontWeight={700} textAlign="center">
            AI Heat Date and Time
          </Typography>
          <br />
          <Controller
            name="ai_form_heat_date_and_time"
            control={control}
            defaultValue={null}
            rules={{
              required: { value: true, message: "This is required" },
            }}
            render={({ field: { onChange, value, ...field } }) => (
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DateTimePicker
                  label={"Select AI Heat date and time*"}
                  value={value}
                  onChange={(newValue) => {
                    onChange(newValue);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      error={Boolean(errors.ai_form_heat_date_and_time)}
                      helperText={errors?.ai_form_heat_date_and_time?.message}
                    />
                  )}
                  {...field}
                />
              </LocalizationProvider>
            )}
          />
        </FormGroup>
      )}
    </Box>
  );
};

export default SRPastActivityForm;
