import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON>,
  <PERSON>con<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Too<PERSON><PERSON>,
  Typo<PERSON>,
} from "@mui/material";
import { useEffect, useState, forwardRef, useRef } from "react";
import useSRStep2Api from "../hook/useSRStep2-api";
import { <PERSON><PERSON>, Table, Modal, Steps } from "antd";
import Pagination from "@mui/material/Pagination";
import Moment from "react-moment";
import SRLoader from "../../loader/SRLoader";
import { useForm, FormProvider } from "react-hook-form";
import SRPastActivityForm from "./SRPastActivityForm";
import dayjs from "dayjs";
import SRPastActivityPaymentMethod from "../../step3/past-activity-payment/SRPastActivityPayment";
import CloseIcon from "@mui/icons-material/Close";
import Slide from "@mui/material/Slide";
import { LoadingOutlined } from "@ant-design/icons";
import { PreviewModalTable } from "../../../../PreviewModalTable";
import { useNavigate } from "react-router-dom";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const steps = [
  {
    title: "Fill the required fields",
  },
  {
    title: "Select Payment Method & Create",
  },
  {
    title: "Created Task Information",
  },
];

const SRPastActivities = ({ entity_uuid, dispatch, srPayload }) => {
  const [pastActivities, setPastActivities] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState();
  const [isLoading, setIsLoading] = useState(false);
  const { getCattleLastFiveActivities } = useSRStep2Api({ setIsLoading });
  const [highlightedRowKey, setHighlightedRowKey] = useState(
    srPayload?.payload?.care_calendar
  ); // State to track highlighted row
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showAIDate, setShowAIDate] = useState(false);
  const [showDetails, setShowDetails] = useState();
  const [current, setCurrent] = useState(0);
  const [previewData, setPreviewData] = useState(null);
  const navigate = useNavigate();

  const ref = useRef();

  const items = steps.map((item) => ({
    key: item.title,
    title: item.title,
  }));

  const methods = useForm();

  const { reset } = methods;

  const { handleSubmit } = methods;

  const handleClose = () => {
    setCurrent(0);
    setShowDetails();
    setIsModalOpen(false);
    setHighlightedRowKey(null);
    setPreviewData(null);
    dispatch({
      type: "FOLLOWUP",
      payload: {
        care_calendar_id: undefined,
        follow_up_required: undefined,
        get_preview: undefined,
        activity_date: undefined,
        activity_date_gmt: undefined,
        ai_form_heat_date_and_time: undefined,
      },
    });
  };

  const handleCreateFollowUp = (record) => {
    setShowDetails(record);
    setHighlightedRowKey(record?.key);
    setIsModalOpen(true);
    if (record?.activity_id === 1000470001) {
      setShowAIDate(true);
    } else {
      setShowAIDate(false);
    }

    dispatch({
      type: "FOLLOWUP",
      payload: {
        care_calendar: record?.key,
        follow_up_required: true,
        get_preview: true,
      },
    });
  };

  const tableColumnsPastActivity = [
    {
      title: "Ticket Id",
      dataIndex: "ticketId",
      key: "ticketId",
      width: 250,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Activity Date",
      dataIndex: "activityDate",
      key: "activityDate",
      width: 200,
      render: (text) =>
        text !== null ? <Moment format="DD MMM, YYYY">{text}</Moment> : "-",
    },
    {
      title: "Category",
      dataIndex: "category",
      key: "category",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Activity Name",
      dataIndex: "activityName",
      key: "activityName",
      width: 150,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 150,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Paravet",
      dataIndex: "paravet",
      key: "paravet",
      width: 200,
      render: (text) => <span>{text || "-"}</span>,
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      width: 200,
      render: (_, record) =>
        record?.calendarStatus === 1000300050 && (
          <>
            <Button
              type="primary"
              style={{ background: "#ec6237", color: "#fff" }}
              onClick={() => handleCreateFollowUp(record)}
            >
              {record.key !== highlightedRowKey ? "Re-open" : "Selected"}
            </Button>
          </>
        ),
    },
  ];

  const changePageNumber = async (event, value) => {
    setCurrentPage(value);
    let tempPastActivities = await getCattleLastFiveActivities(
      entity_uuid,
      value,
      setTotalPages
    );
    setPastActivities(tempPastActivities);
    // scrollToElement();
  };

  useEffect(() => {
    if (entity_uuid) {
      const getActivity = async () => {
        const pastActivity = await getCattleLastFiveActivities(
          entity_uuid,
          currentPage,
          setTotalPages
        );
        setPastActivities(pastActivity);
      };
      getActivity();
    }
  }, [entity_uuid]);

  useEffect(() => {
    reset({
      care_calendar: srPayload?.followup?.care_calendar,
      activity_date: srPayload?.followup?.activity_date,
      ai_form_heat_date_and_time:
        srPayload?.followup?.ai_form_heat_date_and_time || undefined,
    });
  }, [reset, srPayload]);

  const tableDataPastActivity = pastActivities?.map((activity) => ({
    key: activity?.care_calendar_id,
    ticketId: activity?.ticket_id,
    activityDate: activity?.activity_date,
    category:
      activity?.activity_category?.en || activity?.activity_category?.ul,
    activityName:
      activity?.activity_name_l10n?.en || activity?.activity_name_l10n?.ul,
    status:
      activity?.calendar_status_l10n?.en || activity?.calendar_status_l10n?.ul,
    paravet: activity?.staff_name?.en || activity?.staff_name?.ul,
    actions: activity?.care_calendar_id,
    calendarStatus: activity?.calendar_status,
    activity_id: activity?.activity_id || null,
    activity_type: activity?.activity_category?.en || "",
    created_at: activity?.created_at || "",
    entity_mapping: activity?.entity_type_id || null,
  }));

  const onSubmit = (data) => {
    console.log("next", data);
    dispatch({
      type: "FOLLOWUP",
      payload: {
        activity_date: data?.activity_date,
        ai_form_heat_date_and_time:
          data?.ai_form_heat_date_and_time || undefined,
      },
    });
    setCurrent(1);
  };
  const handleNext = () => {
    if (current === 0) {
      handleSubmit(onSubmit)();
    } else if (current === 1) {
      ref.current?.reOpen();
    } else {
      handleClose();
      navigate("/tasks/" + previewData?.care_calendar_id);
    }
  };

  if (isLoading) return <SRLoader />;
  return (
    <Box>
      <hr />
      <Typography align="center" gutterBottom variant="h6" fontWeight={700}>
        Past Activities
      </Typography>
      <style>
        {`
    .highlight-row {
      background-color: #828181 !important;
      color: white !important;
    }
    .highlight-row:hover {
      color: black !important;
    }
  `}
      </style>
      {pastActivities.length > 0 ? (
        // Update the Table component
        <Table
          columns={tableColumnsPastActivity}
          dataSource={tableDataPastActivity}
          pagination={false}
          rowClassName={(record) =>
            record.key === highlightedRowKey ? "highlight-row" : ""
          }
        />
      ) : (
        <div className="text-center my-5" style={{ color: "#4c4c4c" }}>
          <i className="fa fa-exclamation-triangle" aria-hidden="true"></i> No
          past activities found!
        </div>
      )}
      <div
        className="mt-2 mb-5"
        style={{ display: "flex", justifyContent: "center" }}
      >
        <Stack spacing={2}>
          <Pagination
            page={currentPage}
            count={totalPages}
            // color="primary"
            onChange={changePageNumber}
          />
        </Stack>
      </div>
      <Dialog
        fullScreen
        open={isModalOpen}
        onClose={handleClose}
        TransitionComponent={Transition}
      >
        <AppBar sx={{ position: "relative", backgroundColor: "#673ab7" }}>
          <Toolbar>
            <IconButton
              edge="start"
              color="inherit"
              onClick={handleClose}
              aria-label="close"
            >
              <CloseIcon />
            </IconButton>
            <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
              Creating Follow up
            </Typography>
          </Toolbar>
        </AppBar>
        <Box>
          <Box p={10}>
            <Steps current={current} items={items} />
          </Box>
          {current === 0 && (
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                width: "100%",
              }}
            >
              <Box mb={4}>
                <Card elevation={3} sx={{ maxWidth: 500 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Ticket Details
                    </Typography>
                    <Typography variant="body1">
                      <strong>Ticket Id:</strong> {showDetails?.ticketId}
                    </Typography>
                    <Typography variant="body1">
                      <strong>Activity Date:</strong>{" "}
                      {dayjs(showDetails?.activityDate).format("LLLL")}
                    </Typography>
                    <Typography variant="body1">
                      <strong>Activity Name:</strong>{" "}
                      {showDetails?.activityName}
                    </Typography>
                  </CardContent>
                </Card>
              </Box>
              <FormProvider {...methods}>
                <SRPastActivityForm showAIDate={showAIDate} />
              </FormProvider>
            </Box>
          )}
          {current === 1 && (
            <SRPastActivityPaymentMethod
              srPayload={srPayload}
              setPreviewData={setPreviewData}
              setCurrent={setCurrent}
              ref={ref}
            />
          )}

          <Box
            sx={{
              // width: "50%",
              marginTop: "-50px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {current === 2 && previewData && (
              <PreviewModalTable previewData={previewData?.previewData} />
            )}
          </Box>

          <Stack justifyContent="flex-end" flexDirection="row">
            <Box sx={{ display: "flex", padding: 5 }}>
              {current !== 0 && current !== 2 && (
                <Button onClick={() => setCurrent(current - 1)}>
                  Previous
                </Button>
              )}
              <Button
                style={{ marginLeft: 10, backgroundColor: "#ec6237" }}
                onClick={handleNext}
                type="primary"
              >
                {current === 0 ? "Next" : current === 2 ? "Done" : "Re-Open"}
              </Button>
            </Box>
          </Stack>
        </Box>
      </Dialog>
    </Box>
  );
};

export default SRPastActivities;
