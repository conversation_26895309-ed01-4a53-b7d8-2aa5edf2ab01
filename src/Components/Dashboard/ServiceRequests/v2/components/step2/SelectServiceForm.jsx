import { useEffect, useState } from "react";
import {
  Autocomplete,
  Box,
  FormGroup,
  Grid,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { Button, message, Tag } from "antd";
import { useFormContext, Controller } from "react-hook-form";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import dayjs from "dayjs";
import ModeEditIcon from "@mui/icons-material/ModeEdit";
import _ from "lodash";

const SelectServiceForm = ({ srPayload }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activityOpt, setActivityOpt] = useState([]);
  const [change, setChange] = useState(1);
  const isFreezed = srPayload?.payload?.follow_up_required ? true : false;

  const activityConfig = {
    Preventive: global.activityReferences[**********],
    Curative: global.activityReferences[**********],
    Reproductive: global.activityReferences[**********],
    "Data Collection": global.activityReferences[**********],
  };

  const applicableOnOptions = [
    { value: **********, label: "Farm" },
    { value: **********, label: "Cattle" },
  ];

  const {
    control,
    watch,
    formState: { errors },
    setValue,
    getValues,
  } = useFormContext();

  const handleClose = () => {
    // setOpen(false);
    setValue("ai_form_heat_date_and_time", undefined);
    setValue("activity_id", undefined);
    setIsModalOpen(false);
  };

  const entityMappingId = srPayload?.payload?.entity_mapping;
  const activityType = watch("activity_type");

  // activity Options filter by activityType (activity Type)

  const activityId = watch("activity_id");

  // Handle form submission
  const handleFormSubmit = () => {
    // Close the modal
    if (watch("ai_form_heat_date_and_time")) {
      setIsModalOpen(false);
    } else {
      message.error("Please select AI Heat date and time");
    }
  };

  // Open modal if AI is selected
  useEffect(() => {
    if (
      activityId?.activity_id === ********** &&
      !watch("ai_form_heat_date_and_time")
    ) {
      setIsModalOpen(true);
    }
  }, [activityId]);                    

  useEffect(() => {
    const activityOptionsfilterbyactivityType =
      activityConfig[activityType] || [];

    const filterByEntity = activityOptionsfilterbyactivityType
      ? _.filter(activityOptionsfilterbyactivityType, (act) =>
          _.includes(
            act.activity_information.entity_mapping,
            entityMappingId?.value
          )
        )
      : [];
    setActivityOpt(filterByEntity);

    const currentActivityId = getValues("activity_id");
    if (currentActivityId && !filterByEntity.some(opt => opt.activity_id === currentActivityId.activity_id)) {
      setValue("activity_id", undefined);
    }
  }, [change, activityType]);                 

  return (
    <>
      <Grid container spacing={2}>
        <Grid item md={4}>
          <Controller
            name="entity_mapping"
            control={control}
            rules={{ required: { value: true, message: "This is required" } }}
            disabled
            render={({ field: { onChange, value, ...field } }) => (
              <Autocomplete
                options={applicableOnOptions}
                value={value || null}
                getOptionLabel={(option) => option.label}
                isOptionEqualToValue={(option, value) =>
                  option.value === value.value
                }
                onChange={(event, newValue) => {
                  onChange(newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Type"
                    error={Boolean(errors.activity_information?.entity_mapping)}
                    helperText={
                      errors?.activity_information?.entity_mapping?.message
                    }
                  />
                )}
                {...field}
              />
            )}
          />
        </Grid>

        <Grid item md={4}>
          <Controller
            name="activity_type"
            control={control}
            // disabled={isFreezed}
            rules={{ required: { value: true, message: "This is required" } }}
            render={({ field: { onChange, value, ...field } }) => (
              <Autocomplete
                options={Object.keys(activityConfig)}
                value={value || null}
                onChange={(event, newValue) => {
                  setChange(Math.random());
                  onChange(newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Category*"
                    error={Boolean(errors.activity_type)}
                    helperText={errors?.activity_type?.message}
                  />
                )}
                // disabled={!activityInformation}
                {...field}
              />
            )}
          />
        </Grid>

        <Grid item md={4}>
          <Controller
            name="activity_id"
            control={control}
            rules={{ required: { value: true, message: "This is required" } }}
            render={({ field: { onChange, value, ...field } }) => (
              <Autocomplete
                options={activityOpt}
                getOptionLabel={(option) =>
                  option.activity_name_l10n?.en || option.activity_name_l10n?.ul
                }
                isOptionEqualToValue={(option, value) =>
                  option.activity_id === value?.activity_id
                }
                onChange={(event, newValue) => {
                  onChange(newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Activity*"
                    error={Boolean(errors.activity_id)}
                    helperText={errors?.activity_id?.message}
                  />
                )}
                value={value || null}
                // disabled={!activityType}
                {...field}
              />
            )}
          />
        </Grid>

        <Grid item md={4}>
          <FormGroup>
            <Controller
              name="activity_date"
              control={control}
              rules={{ required: { value: true, message: "This is required" } }}
              render={({ field: { onChange, value, ...field } }) => (
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DateTimePicker
                    label={"Select date and time"}
                    value={value}
                    referenceDate={dayjs()}
                    onChange={(newValue) => {
                      onChange(newValue);
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        error={Boolean(errors.activity_date)}
                        helperText={errors?.activity_date?.message}
                      />
                    )}
                    // disabled={!activityId}
                    {...field}
                  />
                </LocalizationProvider>
              )}
            />
          </FormGroup>
        </Grid>
      </Grid>
      {watch("ai_form_heat_date_and_time") &&
        activityId?.activity_id === ********** && (
          <Grid container spacing={2} sx={{ marginTop: "5px" }}>
            <Grid item md={4}>
              <FormGroup>
                <Controller
                  name="ai_form_heat_date_and_time"
                  control={control}
                  defaultValue={new Date() || null}
                  rules={{
                    required: { value: true, message: "This is required" },
                  }}
                  render={({ field: { onChange, value, ...field } }) => (
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DateTimePicker
                        label={"Select AI Heat date and time"}
                        value={value || new Date()}
                        onChange={(newValue) => {
                          onChange(newValue);
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            error={Boolean(errors.ai_form_heat_date_and_time)}
                            helperText={
                              errors?.ai_form_heat_date_and_time?.message
                            }
                          />
                        )}
                        // disabled={!activityId}
                        {...field}
                      />
                    </LocalizationProvider>
                  )}
                />
              </FormGroup>
            </Grid>
          </Grid>
        )}

      {/* AI Heat Modal */}
      <Dialog open={isModalOpen} onClose={handleClose}>
        <DialogTitle>AI Heat Date and Time</DialogTitle>
        <DialogContent>
          <Box mt={2} sx={{ width: 400 }}>
            <FormGroup>
              <Controller
                name="ai_form_heat_date_and_time"
                control={control}
                defaultValue={null}
                rules={{
                  required: { value: true, message: "This is required" },
                }}
                render={({ field: { onChange, value, ...field } }) => (
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DateTimePicker
                      label={"Select AI Heat date and time*"}
                      value={value}
                      onChange={(newValue) => {
                        onChange(newValue);
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          error={Boolean(errors.ai_form_heat_date_and_time)}
                          helperText={
                            errors?.ai_form_heat_date_and_time?.message
                          }
                        />
                      )}
                      {...field}
                    />
                  </LocalizationProvider>
                )}
              />
            </FormGroup>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleFormSubmit} type="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SelectServiceForm;
