import { Box } from "@mui/material";
import { forwardRef, useEffect, useState, useImperativeHandle } from "react";
import SelectServiceForm from "./SelectServiceForm";
import { useForm, FormProvider } from "react-hook-form";
import { Tag } from "antd";
import dayjs from "dayjs";

const SelectService = forwardRef(({ dispatch, srPayload, setCurrent }, ref) => {
  const methods = useForm();
  const { handleSubmit, reset } = methods;

  useEffect(() => {
    if (srPayload.payload) {
      reset({
        activity_date: srPayload.payload?.activity_date || null,
        activity_type: srPayload.payload?.activity_type || null,
        activity_id: srPayload.payload?.activity_id || null,
        ai_form_heat_date_and_time:
          srPayload.payload?.ai_form_heat_date_and_time,
        entity_mapping: srPayload.payload?.entity_mapping || null,
      });
    }
  }, [reset, srPayload.payload]);

  const onSubmit = (data) => {
    dispatch({
      type: "SERVICE",
      payload: {
        ...data,
        activity_date: data?.activity_date,
        get_preview: true,
        entity_mapping: srPayload.payload?.entity_mapping,
      },
    });
    setCurrent(2);
  };

  useImperativeHandle(ref, () => ({
    callPage1() {
      handleSubmit(onSubmit)();
    },
  }));

  return (
    <Box sx={{ mt: 4 }}>
      <FormProvider {...methods}>
        <SelectServiceForm srPayload={srPayload} />
      </FormProvider>
      {/* <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          mt: 3,
        }}
      >
        {srPayload?.payload?.follow_up_required && (
          <Tag color="red">Note : You're creating a followup now</Tag>
        )}
      </Box> */}
    </Box>
  );
});

export default SelectService;
