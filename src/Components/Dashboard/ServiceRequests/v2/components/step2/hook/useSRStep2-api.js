import React, { useCallback, useEffect, useState } from "react";
import { instance } from "../../../../../../../Services/api.service";
// import ToastersService from "../../../../../../../Services/toasters.service";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { clearUserToken } from "../../../../../../user/action";

const useSRStep2Api = ({ setIsLoading }) => {
  const userToken = useSelector((state) => state.user.userToken);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const getCattleLastFiveActivities = useCallback(
    async (entity_uuid, page, setTotalPages) => {
      let reqBody = {
        entity_uuid: entity_uuid,
        page_number: page,
        page_limit: 10,
      };
      try {
        setIsLoading(true);
        const response = await instance({
          url: "/find/activity",
          method: "POST",
          data: reqBody,
          headers: {
            "Content-Type": "application/json",
            token: userToken.accessToken,
          },
        });
        if (response.status == 201) {
          setIsLoading(false);
          setTotalPages(response.data.total_page);
          return response.data.data;
        } else if (response.status == 204) {
          setIsLoading(false);
          return [];
        }
      } catch (error) {
        setIsLoading(false);
        if ("response" in error && error.response.status === 401) {
          dispatch(clearUserToken(navigate, "/login", "Session Expired!"));
        }
      }
    },
    []
  );
  return { getCattleLastFiveActivities };
};
export default useSRStep2Api;
