.fullscreen-modal {
  /* Customize the modal styles here */
}
  
/* CSS for the semi-transparent overlay */
.centered-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%; /*  Ensure the content fills the modal height */
}

.image-container1 {
  position: relative;
  width: 200px;
}

.image-container {
  position: relative;
}

.overlay-text {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3); /* Background color with opacity */
  color: white;
  opacity: 0;
  padding: 10px; /* Adjust as needed */
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-container:hover {
  cursor: pointer;
}
.image-container:hover .overlay-text {
  opacity: 1; /* Show the overlay text on hover */
  /* cursor: pointer; */
}