import React, { useEffect, useState, useRef, createRef } from "react"
import { useSelector } from "react-redux"
import { useNavigate, useSearchParams } from 'react-router-dom';
import moment from 'moment';
import { Image, Popover, Checkbox, Input, InputNumber, Space, Button, Select, DatePicker, Table, Modal } from "antd";
import { GlobalOutlined, SearchOutlined, FilterFilled, EnvironmentOutlined } from '@ant-design/icons';
import ExcelJS from 'exceljs';
import dayjs from 'dayjs'
import { PropaneSharp } from "@mui/icons-material";
const lodashObject = require("lodash");

const { RangePicker } = DatePicker
const { Option } = Select;

const { getComments } = require('../../router')

const { MediaCarousel: MediaCarousel2, standardMediaCallback } = require('./MediaCarousel')
const { MediaCarousel } = require('./MediaComponents')
const { KrushalOCDropdown, KrushalOCInput, validateValue } = require('../Common/KrushalOCForm')

const {
  extractBasedOnLanguage, configurationJSON
} = require("@krushal-it/common-core")

const BASE_URL = configurationJSON().SERVICE_END_POINTS.BACK_END

const OCTable = React.forwardRef((props, ref) => {
  const navigate = useNavigate()

  const [tableKey, setTableKey] = useState(0)

  const [currentPage, setCurrentPage] = useState(props.tableConfiguration.getAllDataAtOnce === true ? -1 : 1)
  const [reportRowCount, setReportRowCount] = useState(-1)

  const [pageSize, setPageSize] = useState(props.tableConfiguration.disablePagination === true ? - 1 : props.tableConfiguration.defaultPageSize)
  const [columnFilters, setColumnFilters] = useState({})
  const [globalSearch, setGlobalSearch] = useState('')
  const [sorting, setSorting] = useState({})
  const [tableLoading, setTableLoading] = useState(false)
  const [filterValuesByKey, setFilterValuesByKey] = useState({})
  const [omniSearchPopoverVisibility, setOmniSearchPopoverVisibility] = useState(false)

  const [rearrangedColumnList, setRearrangedColumnList] = useState(props.tableConfiguration.columns)
  const [checkedColumnList, setCheckedColumnList] = useState(props.tableConfiguration.columns.map((object) => { return object.key }))
  // const [columnConfigurationModalDialogVisibility, setColumnConfigurationModalDialogVisibility] = useState(false)

  // const [tableKey, setTableKey] = useState(0)

  const onFilterFunction = (column) => {
    const columnConfiguration = props.tableConfiguration.columnConfiguration[column]
    if (columnConfiguration && columnConfiguration.type === 'string') {
      const stringFunction = (value, record) => {
        if (record[column] !== undefined && record[column] !== null
          && (typeof record[column] === 'string' || record[column] instanceof String)) {
          return (record[column].toLowerCase().includes(value.toLowerCase()))
        } else {
          return false
        }
      }
      return stringFunction
    } else {
      if (!props.tableConfiguration.getAllDataAtOnce) {
        const nonStringFunction = (value, record) => {
          return true
        }
        return nonStringFunction
      } else if (columnConfiguration && columnConfiguration.type === 'number') {
        const intFunction = (value, record) => {
          if (value === null || record[column] == null || value === undefined || record[column] === undefined) {
            return false
          } else {
            return Number(record[column]) === Number(value)
          }
        }
        return intFunction
      } else if (columnConfiguration && columnConfiguration.type === 'dayjs_range') {
        const dateRangeFunction = (value, record) => {
          if (value === null || record[column] == null || value === undefined || record[column] === undefined || !Array.isArray(value)) {
            return false
          } else {
            const startRangeDayJS = value[0]
            const endRangeDayJS = value[1]
            const startDate = startRangeDayJS.toDate()
            const endDate = endRangeDayJS.toDate()
            const recordAsDate = new Date(record[column])
            if (recordAsDate >= startDate && recordAsDate <= endDate) {
              return true
            } else {
              return false
            }
          }
        }
        return dateRangeFunction
      } else if (columnConfiguration && columnConfiguration.type === 'dayjs_date_till') {
        const dateFunction = (value, record) => {
          if (value === null || record[column] == null || value === undefined || record[column] === undefined || !Array.isArray(value)) {
            return false
          } else {
            return false
          }
        }
        return dateFunction
      } else if (columnConfiguration && columnConfiguration.type === 'number_range') {
        const numberRangeFunction = (value, record) => {
          if (value === null || record[column] == null || value === undefined || record[column] === undefined || !Array.isArray(value)) {
            return false
          } else {
            const minValue = value[0]
            const maxValue = value[1]
            const recordValue = record[column]
            if ((minValue === undefined || minValue == null) && (maxValue === undefined || maxValue === null)) {
              return false
            } else if (minValue !== undefined && minValue !== null && (maxValue === undefined || maxValue === null)) {
              if (recordValue >= minValue) {
                return true
              } else {
                return false
              }
            } else if (maxValue !== undefined && maxValue !== null && (minValue === undefined || minValue === null)) {
              if (recordValue <= maxValue) {
                return true
              } else {
                return false
              }
            } else if (minValue !== undefined && minValue !== null && maxValue !== undefined && maxValue !== null) {
              if (recordValue >= minValue && recordValue <= maxValue) {
                return true
              } else {
                return false
              }
            } else {
              return false
            }
          }
        }
        return numberRangeFunction
      } else if (columnConfiguration && ['default_array', 'single_select_array', 'multi_select_array'].includes(columnConfiguration.type)) {
        let columnToIndex = column
        if (columnConfiguration.altColumn) {
          columnToIndex = columnConfiguration.altColumn
        }
        const defaultArrayFunction = (value, record) => {
          if (value === null || record[column] == null || value === undefined || record[column] === undefined) {
            return false
          } else {
            return String(record[columnToIndex]) === String(value)
          }
        }
        return defaultArrayFunction
      }
    }
  }

  const openGoogleMaps = (location) => {
    const { lat, lng } = location
    window.open(`https://www.google.com/maps/search/?api=1&query=${lat},${lng}`, '_blank');
  }

  const setUpColumnConfiguration = (columnKey, indexIntoColumnConfiguration) => {
    const pageLevelFilterParams = (props.parentPageParams && props.parentPageParams.filterParams) ? props.parentPageParams.filterParams : {}
    const pageLevelSortParams = (props.parentPageParams && props.parentPageParams.sortParams) ? props.parentPageParams.sortParams : {}
    const returnValue = {
    }
    // console.log('ATH sUCC 1, columnKey = ', columnKey, ', indexIntoColumnConfiguration = '.indexIntoColumnConfiguration)
    const columnInformation = props.tableConfiguration.columns[indexIntoColumnConfiguration]
    // console.log('ATH sUCC 2,  columnInformation = ', columnInformation)
    if (columnInformation.dataIndex === undefined) {
      returnValue.dataIndex = columnKey
    } else {
      returnValue.dataIndex = columnInformation.dataIndex
    }
    const column = columnInformation.dataIndex ? columnInformation.dataIndex : columnInformation.key

    const columnConfiguration = props.tableConfiguration.columnConfiguration[columnInformation.key]
    if (columnConfiguration && columnConfiguration.align !== undefined) {
      returnValue.align = columnConfiguration.align
    }
    if (columnConfiguration && columnConfiguration.width !== undefined) {
      returnValue.width = columnConfiguration.width
    }
    const pageLevelFilterColumnConfiguration = pageLevelFilterParams[column]
    if (columnConfiguration && columnConfiguration.enableSort === true) {
      if (props.tableConfiguration.getAllDataAtOnce === true) {
        if (columnConfiguration.sortColumnType === 'date') {
          returnValue.sorter = (a, b) => {
            if (a[column] !== undefined && b[column] !== undefined && a[column] !== null && b[column] !== null) {
              const dateA = new Date(a[column])
              const dateB = new Date(b[column])
              return dateA - dateB
            } else {
              return true
            }
          }
        } else if (['int', 'double'].includes(columnConfiguration.sortColumnType)) {
          returnValue.sorter = (a, b) => {
            if (a[column] !== undefined && b[column] !== undefined && a[column] !== null && b[column] !== null) {
              return (a[column] - b[column])
            } else {
              return false
            }
          }
        } else if (['string'].includes(columnConfiguration.sortColumnType)) {
          returnValue.sorter = (a, b) => {
            if (a[column] !== undefined && b[column] !== undefined && a[column] !== null && b[column] !== null) {
              return a[column].localeCompare(b[column])
            } else {
              return true
            }

          }
        }
      } else {
        returnValue.sorter = true
      }
      returnValue.sortDirections = ['ascend', 'descend']
    }
    if (pageLevelSortParams.columnKey && pageLevelSortParams.columnKey === column) {
      returnValue.defaultSortOrder = pageLevelSortParams.order
    }
    if (Object.keys(pageLevelSortParams).length === 0 && columnConfiguration && columnConfiguration.defaultSort) {
      returnValue.defaultSortOrder = columnConfiguration.defaultSort
    }
    if (columnConfiguration && columnConfiguration.enableFilter && columnConfiguration.type !== 'default_array') {
      returnValue.filterIcon = (filtered) => filterIconFunction(column, filtered)
      returnValue.filterDropdown = (object) => onFilterDropdownFunction(object, column)
      returnValue.onFilter = onFilterFunction(column)
    }
    if (columnConfiguration && columnConfiguration.enableFilter /* && columnConfiguration.allowSettingFilterFromPageParams */ && pageLevelFilterColumnConfiguration) {
      returnValue.defaultFilteredValue = pageLevelFilterColumnConfiguration
    }
    if (columnConfiguration && columnConfiguration.enableFilter && columnConfiguration.defaultFilteredValue) {
      returnValue.defaultFilteredValue = columnConfiguration.defaultFilteredValue
    }
    if (columnConfiguration && columnConfiguration.type === 'default_array' && filterValuesByKey) {
      if (filterValuesByKey[column + '_filter_values']) {
        returnValue.filters = filterValuesByKey[column + '_filter_values']
      } else if (columnConfiguration.filters) {
        returnValue.filters = columnConfiguration.filters
      }
      returnValue.onFilter = onFilterFunction(column)
    }
    if (columnConfiguration && columnConfiguration.formatType) {
      if (columnConfiguration.formatType === 'date') {
        if (returnValue.align === undefined) {
          returnValue.align = 'center'
        }
        returnValue.render = (date) => {
          let returnValue = date
          if (date instanceof Date || (date !== undefined && date !== null)) {
            returnValue = moment(date).format(columnConfiguration.format)
          }
          return returnValue
        }
      }
      if (columnConfiguration.formatType === 'double' && columnConfiguration.format.decimalPlaces) {
        if (returnValue.align === undefined) {
          returnValue.align = 'right'
        }
        returnValue.render = (floatValueAsString) => {
          if (floatValueAsString === undefined || floatValueAsString === null) {
            return floatValueAsString
          }
          if (floatValueAsString.constructor.name === 'String') {
            const floatValue = parseFloat(floatValueAsString)
            return floatValue.toFixed(columnConfiguration.format.decimalPlaces)
          } else if (typeof floatValueAsString === 'number') {
            return floatValueAsString.toFixed(columnConfiguration.format.decimalPlaces)
          } else {
            return floatValueAsString
          }
        }
      }
      if (columnConfiguration.formatType === 'rupee') {
        if (returnValue.align === undefined) {
          returnValue.align = 'right'
        }
        returnValue.render = (floatValueAsString) => {
          if (floatValueAsString === undefined || floatValueAsString === null) {
            return floatValueAsString
          }
          if (floatValueAsString.constructor.name === 'String') {
            const floatValue = parseFloat(floatValueAsString)
            return '₹' + floatValue.toFixed(2)
          } else if (typeof floatValueAsString === 'number') {
            const floatValue = floatValueAsString
            return '₹' + floatValue.toFixed(2)
          } else {
            return floatValueAsString
          }
        }
      }
      if (columnConfiguration.formatType === 'months_since') {
        returnValue.render = (date) => {
          let returnValue = date
          if (date instanceof Date || (date !== undefined && date !== null)) {
            // returnValue = moment(date).format(columnConfiguration.format)
            returnValue = moment().diff(moment(date), 'months')
          }
          return returnValue
        }
      }
      if (columnConfiguration.formatType === 'hyperLink') {
        returnValue.render = (urlLink) => {
          let returnValue = urlLink
          if (urlLink !== undefined && urlLink !== null && urlLink !== '') {
            // returnValue = moment(date).format(columnConfiguration.format)
            return (
              <a href={urlLink} target="_blank" rel="noopener noreferrer">
                {columnConfiguration.headingText}
              </a>
            )
          }
          return returnValue
        }
      }
      if (columnConfiguration.formatType === 's3Link') {
        returnValue.render = (documentInformation) => {
          if (documentInformation && documentInformation.url !== undefined && documentInformation.url !== null && documentInformation.url !== '') {
            const urlLink = BASE_URL + "/media-key?url=" + documentInformation.url
            return (
              <a href={urlLink} target="_blank" rel="noopener noreferrer">
              {columnConfiguration.linkText}
              </a>
            )
          }
          return null
        }
      }
      if (columnConfiguration.formatType === 'l10n') {
        returnValue.render = (value) => {
          let returnValue = value
          if (value !== undefined && value !== null && typeof (value) === 'object') {
            returnValue = extractBasedOnLanguage(value)
          }
          return returnValue
        }
      }
      if (columnConfiguration.formatType === 'location') {
        returnValue.render = (value) => {
          return (
            <EnvironmentOutlined style={{ fontSize: '18px', color: '#52c41a', cursor: 'pointer' }} onClick={() => openGoogleMaps(value)} />
          )
        }
      }
      if (columnConfiguration.formatType === 'link_google_map_directions') {
        if (columnConfiguration['start_lat'] && columnConfiguration['start_lng'] && columnConfiguration['end_lat'] && columnConfiguration['end_lng']) {
          returnValue.render = (value, record) => {
            const startLat = record[columnConfiguration['start_lat']]
            const startLng = record[columnConfiguration['start_lng']]
            const endLat = record[columnConfiguration['end_lat']]
            const endLng = record[columnConfiguration['end_lng']]
            return (
              <a href={`https://www.google.com/maps/dir/${startLat},${startLng}/${endLat},${endLng}`} target="_blank" rel="noopener noreferrer">
                <GlobalOutlined style={{ fontSize: '18px', color: '#1890ff' }} />
              </a>
            )
          }

        }

      }
      if (columnConfiguration.formatType === 'custom') {
        returnValue.render = (custom, record) => {
          console.log('ATH sUCC fT c r 1')
          console.log('ATH sUCC fT c r 2, custom = ', custom)
          console.log('ATH sUCC fT c r 3, record = ', record)
          const returnValue = columnConfiguration.formatFunction(custom, record)
          console.log('ATH sUCC fT c r 4')
          return returnValue
        }
      }
    }
    if (columnConfiguration && columnConfiguration.editable === true) {
      if (['single-select-l10n_2', 'single-select_2', 'multi-select-l10n_2', 'multi-select_2',
        'text-l10n_2', 'text_2', 'positive-decimal_2', 'text-numeric_2', 'positive-number_2',
        'text-number_2', 'text-decimal_2',
        'single-select-l10n', 'single-select', 'multi-select-l10n', 'multi-select',
        'text-l10n', 'text', 'positive-decimal', 'text-numeric', 'positive-number',
        'text-number', 'text-decimal',
      ].includes(columnConfiguration.editableType)) {
        returnValue.render = (field, record) => {
          const recordKey = record[props.tableConfiguration.rowKey]
          const columnValue = record[columnKey]
          if (['single-select-l10n_2', 'single-select_2', 'multi-select-l10n_2', 'multi-select_2',
            'single-select-l10n', 'single-select', 'multi-select-l10n', 'multi-select'
          ].includes(columnConfiguration.editableType)) {
            return (
              <>
                <KrushalOCDropdown
                  style={columnConfiguration.editableStyle}
                  ref={record[columnKey + '_ref']}
                  type={columnConfiguration.editableType}
                  label={columnConfiguration.editableLabel}
                  placeHolderText={columnConfiguration.editablePlaceHolderText}
                  validations={columnConfiguration.editableValidations}
                  errorMessage={columnConfiguration.editableErrorMessage}
                  fieldNames={columnConfiguration.editableDropdownFieldNames}
                  labelKeyInList={columnConfiguration.editableDropdownFieldNames.label}
                  valueKeyInList={columnConfiguration.editableDropdownFieldNames.value}
                  options={record[columnKey + '_list'] ? record[columnKey + '_list'] : columnConfiguration.editableList}
                  value={columnValue}
                  onChangeCallback={(selectedValues) => {
                    if (columnConfiguration.editableOnChangeCallback) {
                      columnConfiguration.editableOnChangeCallback(columnKey, recordKey, selectedValues)
                    }
                  }}
                  onBlurCallback={() => {
                    if (columnConfiguration.editableOnBlurCallback) {
                      columnConfiguration.editableOnBlurCallback(columnKey, recordKey)
                    }
                  }}
                />
              </>
            )
          } else if (['text-l10n', 'text', 'text-numeric', 'text-decimal', 'positive-decimal',
            'text-number', 'positive-number',
            'text-l10n_2', 'text_2', 'text-numeric_2', 'text-decimal_2', 'positive-decimal_2',
            'text-number_2', 'positive-number_2',].includes(columnConfiguration.editableType)) {
            const formatInformation = {}
            if (columnConfiguration.formatType !== undefined) {
              formatInformation.formatType = columnConfiguration.formatType
            }
            if (columnConfiguration.format !== undefined) {
              formatInformation.format = columnConfiguration.format
            }
            return (
              <>
                <KrushalOCInput
                  ref={record[columnKey + '_ref']}
                  type={columnConfiguration.editableType}
                  formatInformation={Object.keys(formatInformation).length > 0 ? formatInformation : undefined}
                  value={columnValue}
                  // label={columnConfiguration.editableLabel}
                  placeHolderText={columnConfiguration.editablePlaceHolderText}
                  validations={columnConfiguration.editableValidations}
                  errorMessage={columnConfiguration.editableErrorMessage}
                  onChangeCallback={(newValue) => {
                    if (columnConfiguration.editableOnChangeCallback) {
                      columnConfiguration.editableOnChangeCallback(columnKey, recordKey, newValue)
                    }
                  }}
                  onBlurCallback={() => {
                    if (columnConfiguration.editableOnBlurCallback) {
                      columnConfiguration.editableOnBlurCallback(columnKey, recordKey)
                    }
                  }}
                />
              </>
            )
          }
        }
      }
    }
    return returnValue
    // const {

    //   filterDropdown: (object) => onFilterDropdownFunction(object, column, props.tableConfiguration, props.tableConfiguration.resetPaginationPageNumberFunction),
    //   onFilter: (value, record) => onFilterFunction(value, record, column, props.tableConfiguration)
    // }
  }

  const createColumnsFromTableConfiguration = () => {
    const returnValueColumns = []
    if (rearrangedColumnList && Array.isArray(rearrangedColumnList)) {
      rearrangedColumnList.forEach((columnConfiguration, index) => {
        const columnKey = columnConfiguration.key
        if (!checkedColumnList.includes(columnKey)) {
          return
        }
        const newColumnConfiguration = { ...columnConfiguration, header: columnConfiguration['title'], ...setUpColumnConfiguration(columnKey, index) }
        returnValueColumns.push(newColumnConfiguration)
      })
    }
    return returnValueColumns
  }

  const columns = createColumnsFromTableConfiguration()

  const updateFilterStateObject = (response, modifiedFilterValuesByKeyParam) => {
    const modifiedFilterValuesByKey = modifiedFilterValuesByKeyParam ? modifiedFilterValuesByKeyParam : { ...filterValuesByKey }
    if (response === undefined) {
      return modifiedFilterValuesByKey
    }
    const filterKeys = Object.keys(response)
    let modifiedFlag = false
    filterKeys.forEach((individualFilterKey, index) => {
      if (individualFilterKey.endsWith('_filter_values')) {
        modifiedFilterValuesByKey[individualFilterKey] = response[individualFilterKey]
        modifiedFlag = true
      }
    })
    if (modifiedFlag) {
      setFilterValuesByKey(modifiedFilterValuesByKey)
    }
    return modifiedFilterValuesByKey
  }

  const transformFilterData = (reportQueryParams) => {
    console.log('ATH tFD 1, reportQueryParams = ', reportQueryParams)
    const returnValueReportQueryParams = lodashObject.cloneDeep(reportQueryParams)
    console.log('ATH tFD 1a, returnValueReportQueryParams = ', returnValueReportQueryParams)
    if (returnValueReportQueryParams && returnValueReportQueryParams.filters) {
      const existingFilters = Object.keys(returnValueReportQueryParams.filters)
      const columnKeys = columns.map((column, index) => {
        return column.key
      })
      existingFilters.forEach((filterColumn, index) => {
        if (columnKeys.includes(filterColumn)) {
          if (props.tableConfiguration && props.tableConfiguration.columnConfiguration && props.tableConfiguration.columnConfiguration[filterColumn]) {
            const filterForColumn = returnValueReportQueryParams.filters[filterColumn]
            if (filterForColumn === undefined || filterForColumn === null) {
              return
            }
            const columnConfiguration = props.tableConfiguration.columnConfiguration[filterColumn]
            if (columnConfiguration.translateFilteredValuesToAlt) {
              let columnIndexIntoFilterArray = filterColumn + '_filter_values'
              if (columnConfiguration.filterValuesKey) {
                columnIndexIntoFilterArray = columnConfiguration.filterValuesKey
              }
              const filterValues = filterValuesByKey[columnIndexIntoFilterArray]
              const columnFilters = filterValues !== undefined ? filterValues : columnConfiguration.filters !== undefined ? columnConfiguration.filters : []
              const newFilterForColumn = []
              filterForColumn.forEach((filterValue, innerIndex) => {
                const filterValueRow = columnFilters.filter((object) => {
                  return (object['value'] === filterValue)
                })
                if (filterValueRow.length > 0) {
                  const substituteValue = filterValueRow[0][columnConfiguration.translateFilteredValuesToAlt]
                  console.log('filter value of ', filterValue, ' translated to ', substituteValue)
                  newFilterForColumn.push(substituteValue)
                }
              })
              returnValueReportQueryParams.filters[filterColumn] = newFilterForColumn
            }
          }
        }
      })
    }
    return returnValueReportQueryParams
  }

  const transformData = (data) => {
    if (data === undefined) {
      return
    }
    if (props.tableConfiguration.dataTransformationRules) {
      const keysToReplace = Object.keys(props.tableConfiguration.dataTransformationRules)
      if (Array.isArray(data)) {
        data.forEach((elementInArray, index) => {
          keysToReplace.forEach((keyToReplace, index2) => {
            if (elementInArray[keyToReplace]) {
              const keyToReplaceWith = props.tableConfiguration.dataTransformationRules[keyToReplace]
              elementInArray[keyToReplaceWith] = elementInArray[keyToReplace]
              delete elementInArray[keyToReplace]
            }
          })
        })
      } else {
        keysToReplace.forEach((keyToReplace, index2) => {
          const elementInArray = data
          if (elementInArray[keyToReplace]) {
            const keyToReplaceWith = props.tableConfiguration.dataTransformationRules[keyToReplace]
            elementInArray[keyToReplaceWith] = elementInArray[keyToReplace]
            delete elementInArray[keyToReplace]
          }
        })
      }
    }
  }

  const fetchData = async (paginationParams, filterParams, sortParams, globalSearchParam, modifiedFilterValuesByKey, returnDataAndNotAssign, selectedColumns) => {
    const reportQueryParams = {
      searchConfiguration: props.tableConfiguration.columnConfiguration,
      selectedColumns: selectedColumns
    }

    if (props.parentPageParams) {
      reportQueryParams.pageParams = props.parentPageParams
    }

    if (props.tableConfiguration.getAllDataAtOnce === true) {
      reportQueryParams.start = -1
    } else if (paginationParams && Object.keys(paginationParams).length > 0) {
      reportQueryParams.start = (paginationParams.current - 1) * paginationParams.pageSize
    } else {
      reportQueryParams.start = (currentPage - 1) * pageSize
    }

    if (paginationParams && Object.keys(paginationParams).length > 0) {
      reportQueryParams.size = paginationParams.pageSize
    } else {
      reportQueryParams.size = pageSize
    }

    if (filterParams && Object.keys(filterParams).length > 0) {
      reportQueryParams.filters = filterParams
    } else {
      reportQueryParams.filters = columnFilters
    }

    if (globalSearchParam === null) {
      reportQueryParams.globalSearch = ''
    } else if (globalSearchParam && globalSearchParam !== '') {
      reportQueryParams.globalSearch = globalSearchParam
    } else {
      reportQueryParams.globalSearch = globalSearch
    }

    if (sortParams && Object.keys(sortParams).length > 0) {
      reportQueryParams.sorting = sortParams
    } else {
      reportQueryParams.sorting = sorting
    }

    const updatedReportQueryParams = transformFilterData(reportQueryParams)

    try {
      // const reportResponse = await tableConfiguration.loadReport(tableConfiguration.userToken, updatedReportQueryParams, tableConfiguration.additionalParams)
      let forceLoadAllColumns = true
      if (props.tableConfiguration.enableSplittingOfServerData === true) {
        forceLoadAllColumns = false
      }
      if (returnDataAndNotAssign === true) {
        forceLoadAllColumns = true
      }
      if (props.loadReportCallback) {
        const reportResponse = await props.loadReportCallback({ ...updatedReportQueryParams, forceLoadAllColumns: forceLoadAllColumns }, props.tableConfiguration.additionalParams)
        transformData(reportResponse?.report)
        if (returnDataAndNotAssign === true) {
          return reportResponse.report
        } else {
          let reportResponseForRemainingColumns = {}
          if (reportResponse.primaryKeysForRemainingColumns) {
            reportResponseForRemainingColumns = await props.loadReportCallback({ ...updatedReportQueryParams, primaryKeysForRemainingColumns: reportResponse.primaryKeysForRemainingColumns }, props.tableConfiguration.additionalParams)
            console.log('ATH OCT fD, reportResponseForRemainingColumns = ', reportResponseForRemainingColumns)
          }
          const basicResponse = reportResponse.report
          if (reportResponseForRemainingColumns && reportResponseForRemainingColumns.rowsForPrimaryKeys) {
            for (let counter = 0; counter < basicResponse.length; counter++) {
              const basicResponseRow = basicResponse[counter]
              const primaryKey = basicResponseRow[props.tableConfiguration.rowKey]
              if (reportResponseForRemainingColumns.rowsForPrimaryKeys[primaryKey]) {
                const newResponseRow = { ...basicResponseRow, ...reportResponseForRemainingColumns.rowsForPrimaryKeys[primaryKey] }
                basicResponse[counter] = newResponseRow
              }
            }
          }
          props.setReportData(basicResponse);
          setReportRowCount(reportResponse.count);
          updateFilterStateObject(reportResponse, modifiedFilterValuesByKey)
        }
      }
    } catch (error) {
      console.error(error);
      throw error
    }
  }

  const reloadDataAndMoveToPageOne = async () => {
    setTableLoading(true)
    await fetchData({ current: 1, pageSize: props.tableConfiguration.disablePagination === true ? - 1 : props.tableConfiguration.defaultPageSize }, undefined, undefined, undefined, undefined, undefined, checkedColumnList)
    setTableLoading(false)
  }

  const initialLoad = async () => {
    setTableLoading(true)
    let modifiedFilterValuesByKey
    if (props.loadInitialFilterDataCallback) {
      // modifiedFilterValuesByKey = await initializeFilterData(tableConfiguration)
      const response = await props.loadInitialFilterDataCallback(props.tableConfiguration.additionalParams)
      if (response.return_code === 0) {
        transformData(response.tableFilters)
        modifiedFilterValuesByKey = updateFilterStateObject(response.tableFilters)
      }
    }

    const defaultPaginationConfiguration = constructPaginationCriteriaBasedOnDefaultValues()
    const defaultSortConfiguration = constructSortCriteriaBasedOnDefaultValues()
    const defaultFilterConfiguration = constructFilterCriteriaBasedOnDefaultValues()
    const pageLevelPaginationParams = (props.parentPageParams && props.parentPageParams.paginationParams) ? props.parentPageParams.paginationParams : {}
    const pageLevelFilterParams = (props.parentPageParams && props.parentPageParams.filterParams) ? props.parentPageParams.filterParams : {}
    const pageLevelSortParams = (props.parentPageParams && props.parentPageParams.sortParams) ? props.parentPageParams.sortParams : {}
    const pageLevelColumnConfiguration = (props.parentPageParams && props.parentPageParams.columnConfiguration) ? props.parentPageParams.columnConfiguration : undefined
    if (pageLevelColumnConfiguration) {
      setRearrangedColumnList(pageLevelColumnConfiguration.rearrangedColumnList)
      setCheckedColumnList(pageLevelColumnConfiguration.checkedColumnList)
    }
    const finalPaginationParams = { ...defaultPaginationConfiguration, ...pageLevelPaginationParams }
    const finalPageLevelFiterParams = { ...defaultFilterConfiguration, ...pageLevelFilterParams }
    const finalInitialSortParams = { ...defaultSortConfiguration, ...pageLevelSortParams }
    setCurrentPage(finalPaginationParams.current)
    setPageSize(finalPaginationParams.pageSize)
    setSorting(finalInitialSortParams)
    setColumnFilters(finalPageLevelFiterParams)
    await fetchData(finalPaginationParams, finalPageLevelFiterParams, finalInitialSortParams, null, modifiedFilterValuesByKey, undefined, checkedColumnList)
    setTableLoading(false)
    // await fetchData({current:1, pageSize: tableConfiguration.disablePagination === true ? - 1 : tableConfiguration.defaultPageSize}, {}, {}, null, {})
  }

  const getSelectedColumnsInTable = () => {
    return checkedColumnList
  }

  React.useImperativeHandle(ref, () => ({
    reloadDataAndMoveToPageOne, initialLoad, reset, getSelectedColumnsInTable
  }));

  const enablePaginationIfNeeded = () => {
    if (props.tableConfiguration.disablePagination) {
      return false
    } else {
      return {
        total: props.tableConfiguration.getAllDataAtOnce === true ? undefined : reportRowCount, // Set the number of rows per page,
        current: props.tableConfiguration.getAllDataAtOnce === true ? undefined : currentPage,
        pageSize: pageSize,
        showSizeChanger: true
        // Handle page number change
      }
    }
  }

  const enableSelectionIfNeeded = () => {
    const rowSelection = {
      selectedRowKeys: props.selectedRowKeys,
      onChange: (selectedKeys) => {
        props.setSelectedRowKeys(selectedKeys)
      },
      // You can customize the selection behavior, such as disallowing some rows to be selected
      getCheckboxProps: (record) => ({
        // Example: Disable selection for rows with a specific condition
        disabled: record.someCondition === true,
      }),
    }
    if (props.tableConfiguration.enableSelection === true) {
      return rowSelection
    } else {
      return undefined
    }
  }

  const handleTableChange = async (paginationParams, filterParams, sortParams) => {
    setTableLoading(true)
    if (Object.keys(paginationParams).length > 0) {
      setCurrentPage(paginationParams.current)
      setPageSize(paginationParams.pageSize)
    }
    setSorting(sortParams)
    setColumnFilters(filterParams)
    await fetchData(paginationParams, filterParams, sortParams, undefined, undefined, undefined, checkedColumnList)
    if (props.tableConfiguration && props.tableConfiguration.tableNavigationUrl) {
      const pageLevelFilterParams = (props.parentPageParams && props.parentPageParams.filterParams) ? props.parentPageParams.filterParams : {}
      const filterParamsForURLString = { ...pageLevelFilterParams, ...filterParams }
      const urlQueryString = createURLQueryStringFromPageParams(paginationParams, filterParamsForURLString, sortParams, { globalSearch: globalSearch }, { rearrangedColumnList: rearrangedColumnList, checkedColumnList: checkedColumnList })
      navigate({
        pathname: `${props.tableConfiguration.tableNavigationUrl}`,
        search: `?${urlQueryString}`,
        replace: true
      })
    }
    setTableLoading(false)
  }

  const enableOnChangeIfNeeded = () => {
    if (props.tableConfiguration.getAllDataAtOnce) {
      return undefined
    } else {
      return (arg1, arg2, arg3) => handleTableChange(arg1, arg2, arg3)
    }
  }

  const constructPaginationCriteriaBasedOnDefaultValues = () => {
    const returnValue = { current: 1, pageSize: props.tableConfiguration.disablePagination === true ? - 1 : props.tableConfiguration.defaultPageSize }
    return returnValue
  }

  const constructSortCriteriaBasedOnDefaultValues = () => {
    const returnValue = { columnKey: 'ignoreColumn', order: 'ascend' }
    const columnConfiguration = props.tableConfiguration.columnConfiguration
    const columns = Object.keys(columnConfiguration)
    columns.every((column, index) => {
      if (columnConfiguration[column].defaultSort) {
        returnValue.columnKey = column
        returnValue.order = columnConfiguration[column].defaultSort
        return false
      }
      return true
    })
    return returnValue
  }

  const constructFilterCriteriaBasedOnDefaultValues = () => {
    const returnValue = { ignoreColumn: ['ABC'] }
    const columnConfiguration = props.tableConfiguration.columnConfiguration
    const columns = Object.keys(columnConfiguration)
    columns.forEach((column, index) => {
      if (columnConfiguration[column].defaultFilteredValue) {
        returnValue[column] = columnConfiguration[column].defaultFilteredValue
      }
    })
    return returnValue
  }

  const filterIconFunction = (field, filtered) => {
    const columnConfiguration = props.tableConfiguration.columnConfiguration[field]
    if (columnConfiguration && columnConfiguration.enableFilter === true) {
      if (['string', 'number'].includes(columnConfiguration.type)) {
        return (
          <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
        )
      } else if (['number_range', 'dayjs_range', 'dayjs_date_till'].includes(columnConfiguration.type)) {
        return (
          <Button icon={<FilterFilled />} style={{ marginLeft: 8, color: filtered ? '#1890ff' : undefined }} />
        )
      } else if (['single_select_array', 'multi_select_array'].includes(columnConfiguration.type)) {
        return (
          <Button icon={<FilterFilled />} style={{ marginLeft: 8 }} />
        )
      }
    }
  }

  const handleSetFilter = (selectedKeys, confirm, dataIndex/* , resetPaginationPageNumberFunction */) => {
    console.log('hS 1, selectedKeys = ', selectedKeys)
    console.log('hS 2, confirm = ', confirm)
    console.log('hS 3, dataIndex = ', dataIndex)
    confirm()
    // resetPaginationPageNumberFunction(1)
    setCurrentPage(1)
  }

  const handleResetFilter = (confirm, clearFilters, dataIndex/* , resetPaginationPageNumberFunction */) => {
    console.log('hR 1, clearFilters = ', clearFilters)
    console.log('hR 2, dataIndex = ', dataIndex)
    clearFilters()
    confirm()
    // resetPaginationPageNumberFunction(1)
    setCurrentPage(1)
  }

  const onFilterDropdownFunction = (object, field) => {
    // const resetPaginationPageNumberFunction = tableConfiguration.resetPaginationPageNumberFunction
    const columnConfiguration = props.tableConfiguration.columnConfiguration[field]
    const searchConfiguration = props.tableConfiguration.columnConfiguration
    if (searchConfiguration && field && searchConfiguration[field] && searchConfiguration[field].type) {
      const { setSelectedKeys, selectedKeys, confirm, clearFilters } = object
      if (searchConfiguration[field].type === 'string') {
        return (
          <div style={{ padding: 8 }}>
            <Input
              placeholder="Search Name"
              value={selectedKeys[0]}
              onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
              onPressEnter={() => handleSetFilter(selectedKeys, confirm, field/* , setCurrentPage */)}
              style={{ width: 188, marginBottom: 8, display: 'block' }}
            />
            <Space>
              <Button
                type="primary"
                onClick={() => handleSetFilter(selectedKeys, confirm, field/* , setCurrentPage */)}
                icon={<SearchOutlined />}
                size="small"
                style={{ width: 90 }}
              >
                Search
              </Button>
              <Button onClick={() => handleResetFilter(confirm, clearFilters, field/* , setCurrentPage */)} size="small" style={{ width: 90 }}>
                Reset
              </Button>
            </Space>
          </div>
        )
      } else if (searchConfiguration[field].type === 'number_range') {
        return (
          <div style={{ padding: 8 }}>
            <Space>
              <InputNumber
                placeholder="Enter min number"
                value={selectedKeys.length > 0 ? selectedKeys[0][0] : undefined}
                onChange={(e, arg2) => {
                  let minValue, maxValue
                  if (selectedKeys.length > 0) {
                    minValue = selectedKeys[0][0]
                    maxValue = selectedKeys[0][1]
                  }
                  setSelectedKeys(e !== undefined && e !== null ? [[e, maxValue]] : [[undefined, maxValue]])
                }}
                // onPressEnter={() => handleSetFilter(selectedKeys, confirm, field/* , setCurrentPage */)}
                style={{ width: 188, marginBottom: 8, display: 'block' }}
              />
              <InputNumber
                placeholder="Enter max number"
                value={selectedKeys.length > 0 ? selectedKeys[0][1] : undefined}
                onChange={(e, arg2) => {
                  let minValue, maxValue
                  if (selectedKeys.length > 0) {
                    minValue = selectedKeys[0][0]
                    maxValue = selectedKeys[0][1]
                  }
                  setSelectedKeys(e !== undefined && e !== null ? [[minValue, e]] : [[minValue, undefined]])
                }}
                // onPressEnter={() => handleSetFilter(selectedKeys, confirm, field/* , setCurrentPage */)}
                style={{ width: 188, marginBottom: 8, display: 'block' }}
              />
            </Space>
            <Space>
              <Button
                type="primary"
                onClick={() => handleSetFilter(selectedKeys, confirm, field/* , setCurrentPage */)}
                size="small"
                style={{ width: 90 }}
              >
                Search
              </Button>
              <Button onClick={() => handleResetFilter(confirm, clearFilters, field/* , setCurrentPage */)} size="small" style={{ width: 90 }}>
                Reset
              </Button>
            </Space>
          </div>
        )
      } else if (searchConfiguration[field].type === 'dayjs_range') {
        return (
          <div style={{ padding: 8 }}>
            <RangePicker
              // value={field === "latest_health_score_verification_status_updated_at" ? [undefined, dayjs().endOf('day') ] : selectedKeys.length > 0 ? selectedKeys[0] : []}
              value={selectedKeys.length > 0 ? selectedKeys[0] : []}
              // value={valueToBeAssigned}
              onChange={(dateRange) => {
                setSelectedKeys([dateRange])
              }}
            />
            <Space>
              <Button
                type="primary"
                onClick={() => handleSetFilter(selectedKeys, confirm, field/* , setCurrentPage */)}
                size="small"
                style={{ width: 90 }}
              >
                Search
              </Button>
              <Button onClick={() => handleResetFilter(confirm, clearFilters, field/* , setCurrentPage */)} size="small" style={{ width: 90 }}>
                Reset
              </Button>
            </Space>
          </div>
        )
      } else if (searchConfiguration[field].type === 'dayjs_date_till') {
        console.log('ATH oFDDF dd 1, selectedKeys = ', selectedKeys)
        return (
          <div style={{ padding: 8 }}>
            <DatePicker
              value={selectedKeys.length > 0 ? selectedKeys[0] : undefined}
              onChange={(date) => {
                console.log('ATH oFDDF dd 2, date = ', date)
                setSelectedKeys([date])
              }}
              placeholder="Select Date"
            />
            <Space>
              <Button
                type="primary"
                onClick={() => handleSetFilter(selectedKeys, confirm, field/* , setCurrentPage */)}
                size="small"
                style={{ width: 90 }}
              >
                Search
              </Button>
              <Button onClick={() => handleResetFilter(confirm, clearFilters, field/* , setCurrentPage */)} size="small" style={{ width: 90 }}>
                Reset
              </Button>
            </Space>
          </div>
        )
      } else if (searchConfiguration[field].type === 'number') {
        return (
          <div style={{ padding: 8 }}>
            <Space>
              <InputNumber
                placeholder="Enter a number"
                value={selectedKeys.length > 0 ? selectedKeys[0] : undefined}
                onChange={(e) => {
                  setSelectedKeys(e ? [e] : [])
                }}
                onPressEnter={() => handleSetFilter(selectedKeys, confirm, field/* , setCurrentPage */)}
                style={{ width: 188, marginBottom: 8, display: 'block' }}
              />
            </Space>
            <Space>
              <Button
                type="primary"
                onClick={() => handleSetFilter(selectedKeys, confirm, field/* , setCurrentPage */)}
                size="small"
                style={{ width: 90 }}
              >
                Search
              </Button>
              <Button onClick={() => handleResetFilter(confirm, clearFilters, field/* , setCurrentPage */)} size="small" style={{ width: 90 }}>
                Reset
              </Button>
            </Space>
          </div>
        )
      } else if (['single_select_array', 'multi_select_array'].includes(searchConfiguration[field].type)) {
        let filterValuesKey = field + '_filter_values'
        if (columnConfiguration && columnConfiguration.filterValuesKey) {
          filterValuesKey = columnConfiguration.filterValuesKey
        }
        const filterValues = filterValuesByKey[filterValuesKey] !== undefined ? filterValuesByKey[filterValuesKey] : columnConfiguration.filters !== undefined ? columnConfiguration.filters : []
        if (filterValues.length > 0) {
          return (
            <div style={{ padding: 8 }}>
            <Select
            mode={searchConfiguration[field].type === 'multi_select_array' ? "multiple" : "single"}
            allowClear
            showSearch
            style={{ width: '100%' }}
            placeholder="Select one"
            value={selectedKeys}
            onChange={(value) => {
              let selectedKeysToSet = [];
              if (value) {
                if (searchConfiguration[field].type === 'multi_select_array') {
                  selectedKeysToSet = value;
                } else {
                  selectedKeysToSet = [value];
                }
              }
              setSelectedKeys(selectedKeysToSet);
            }}
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            virtual={false}
          >
            {filterValues.map((singleSelectFilterValue) => (
              <Option
                key={singleSelectFilterValue.value}
                value={singleSelectFilterValue.value}
              >
                {singleSelectFilterValue.text}
              </Option>
            ))}
          </Select>
              <Space>
                <Button
                  type="primary"
                  onClick={() => handleSetFilter(selectedKeys, confirm, field/* , setCurrentPage */)}
                  size="small"
                  style={{ width: 90 }}
                >
                  Search
                </Button>
                <Button onClick={() => handleResetFilter(confirm, clearFilters, field/* , setCurrentPage */)} size="small" style={{ width: 90 }}>
                  Reset
                </Button>
              </Space>
            </div>
          )
        }
        else {
          return null
        }
      }
    } else {
      return (<></>)
    }
  }

  useEffect(() => {
    if (props.preInitialLoadCallback) {
      props.preInitialLoadCallback()
    }
    initialLoad()
    if (props.postInitialLoadCallback) {
      props.postInitialLoadCallback()
    }
  }, [
  ])

  const omniSearch = async () => {
    const searchValue = document.getElementById('searchInput').value
    setTableLoading(true)
    setGlobalSearch(searchValue)
    await fetchData({}, {}, {}, searchValue, undefined, undefined, checkedColumnList)
    setTableLoading(false)
  }

  const reset = async () => {
    setTableLoading(true)
    setTableKey(tableKey + 1)
    setColumnFilters({})
    setSorting({})
    if (document.getElementById('searchInput') !== undefined && document.getElementById('searchInput') !== null) {
      document.getElementById('searchInput').value = ""
    }
    setGlobalSearch('')
    const defaultSortConfiguration = constructSortCriteriaBasedOnDefaultValues()
    await fetchData({ current: 1, pageSize: props.tableConfiguration.disablePagination === true ? - 1 : props.tableConfiguration.defaultPageSize }, { ignoreColumn: ['ABC'] }, defaultSortConfiguration, null, undefined, undefined, checkedColumnList)
    setTableLoading(false)
  }

  const exportPage = async (allOrNot) => {
    setTableLoading(true)
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('Sheet 1')
    const exportedColumns = createColumnsFromTableConfiguration()

    let finalReportData
    if (allOrNot) {
      finalReportData = await fetchData({ current: 1, pageSize: - 1 }, columnFilters, sorting, undefined, undefined, true, checkedColumnList)
    } else {
      finalReportData = props.reportData
    }
    const dataToBeExported = []
    for (const reportDataRow of finalReportData) {
      const exportableDataRow = {}
      for (const individualColumn of exportedColumns) {
        exportableDataRow[individualColumn.dataIndex] = reportDataRow[individualColumn.dataIndex]
      }
      dataToBeExported.push(exportableDataRow)
    }

    // Add column headers to the worksheet
    // worksheet.columns = props.tableConfiguration.columns
    worksheet.columns = exportedColumns

    // Add data to the worksheet
    // worksheet.addRows(props.reportData)
    worksheet.addRows(dataToBeExported)

    // Save the workbook to a file
    workbook.xlsx.writeBuffer().then((buffer) => {
      // saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'exported-data.xlsx');
      const link = document.createElement('a')
      const blob = new Blob([buffer], { type: 'application/octet-stream' })
      link.href = window.URL.createObjectURL(blob)
      link.download = 'exported-data.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    })
    setTableLoading(false)
  }

  const omniSearchFieldPopupContent = () => {
    return (
      <div style={{ padding: 8 }}>
        <input
          id='searchInput'
          type='text'
          placeholder="Search relevant columns"
          style={{ width: 300, marginBottom: 16 }}
        />
        <div>
          <Button
            disabled={props.tableLoading}
            size="small"
            onClick={() => omniSearch()}
          >
            Search
          </Button>
          <Button
            disabled={props.tableLoading}
            size="small"
            onClick={() => omniSearch()}
          >
            Clear
          </Button>
        </div>
      </div>
    )
  }

  return (
    <>
      {props.tableConfiguration.showTableHeader ? (
        <OCTableHeader
          tableConfiguration={props.tableConfiguration}
          parentPageParams={props.parentPageParams}
          columnFilters={columnFilters}
          filterValuesByKey={filterValuesByKey}
          sorting={sorting}
        />
      ) : null}
      <div>
        {props.tableConfiguration.omniSearchEnabled ? (
          <>
            <Popover content={omniSearchFieldPopupContent} trigger="click" title="Search">
              <Image
                style={{ width: '24px', marginRight: '4px', cursor: 'pointer' }}
                src={process.env.PUBLIC_URL + "../../img/icons8-search-50.png"}
                preview={false}
              />
            </Popover>
            <img
              alt=""
              src={process.env.PUBLIC_URL + "../../img/clear-filter-icon-20.jpg"}
              style={{ width: '24px', marginRight: '4px', cursor: 'pointer' }}
              onClick={reset}
              disabled={props.tableLoading}
            />
          </>
        ) : null}
        {props.tableConfiguration.enableColumnConfiguration ? (
          <ColumnConfiguration
            tableConfiguration={props.tableConfiguration}
            rearrangedColumnList={rearrangedColumnList}
            setRearrangedColumnList={setRearrangedColumnList}
            checkedColumnList={checkedColumnList}
            setCheckedColumnList={setCheckedColumnList}
            paginationParams={{ current: currentPage, pageSize: pageSize }}
            filterParams={columnFilters}
            sortParams={sorting}
            globalSearch={globalSearch}
          // columnConfigurationModalDialogVisibility={props.columnConfigurationModalDialogVisibility}
          // setColumnConfigurationModalDialogVisibility={props.setColumnConfigurationModalDialogVisibility}
          />
        ) : null
        }
        {props.tableConfiguration.enableTableExport === true ? (
          <img
            alt=""
            src={process.env.PUBLIC_URL + "../../img/icons8-export-excel-50.png"}
            style={{ width: '24px', marginRight: '4px', cursor: 'pointer' }}
            onClick={() => exportPage(false)}
            disabled={props.tableLoading}
          />
        ) : null}
        {props.tableConfiguration.enableTableExportFromCloud === true ? (
          <img
            alt=""
            src={process.env.PUBLIC_URL + "../../img/excel-and-cloud.png"}
            style={{ width: '24px', marginRight: '4px', cursor: 'pointer' }}
            onClick={() => exportPage(true)}
            disabled={props.tableLoading}
          />
        ) : null}
        {/* <img
            alt=""
            src={process.env.PUBLIC_URL + "../../img/icons8-columns-24.png"}
            style={{width:'22px', marginRight:'4px', cursor:'pointer'}}
          />
        <img
            alt=""
            src={process.env.PUBLIC_URL + "../../img/icons8-columns-50.png"}
            style={{width:'16px', marginRight:'4px', cursor:'pointer'}}
          /> */}
      </div>
      <Table
        key={tableKey}
        loading={tableLoading}
        columns={columns}
        dataSource={props.reportData}
        scroll={props.tableConfiguration.enableHorizontalScrolling ? { x: true } : undefined}
        rowKey={props.tableConfiguration.rowKey}

        rowSelection={enableSelectionIfNeeded()}
        pagination={enablePaginationIfNeeded()}
        onChange={enableOnChangeIfNeeded()}
      />
    </>
  )
})

const OCTableHeader = (props) => {
  // console.log('ATH OCTH 1, props = ', props)
  const arePageLevelFiltersAvailable = (props) => {
    let returnValue = false
    if (props && props.parentPageParams && props.parentPageParams.filterParams) {
      returnValue = true
    }
    return returnValue
  }

  const isFilterEnabled = (columnConfiguration, isPageLevel) => {
    if (isPageLevel) {
      return (columnConfiguration && columnConfiguration.allowFilterInPageParams && columnConfiguration.allowSettingFilterFromPageParams !== true)
    } else {
      return (columnConfiguration && columnConfiguration.enableFilter)
    }
  }

  const getColumnHeadingText = (props, column) => {
    if (props.tableConfiguration.columnConfiguration[column] && props.tableConfiguration.columnConfiguration[column].headingText) {
      return props.tableConfiguration.columnConfiguration[column].headingText
    } else {
      return column
    }
  }

  const isTableSortingAvailable = (props) => {
    let returnValue = false
    if (props && props.sorting && props.sorting.columnKey) {
      if (props.sorting.columnKey !== 'ignoreColumn') {
        returnValue = true
      }
    }
    return returnValue
  }

  const areTableFiltersAvailable = (props) => {
    let returnValue = false
    if (props && props.columnFilters && Object.keys(props.columnFilters).length > 0) {
      const potentialFilterColumns = Object.keys(props.columnFilters)
      potentialFilterColumns.every((column, index) => {
        const columnFilterValue = props.columnFilters[column]
        if (columnFilterValue !== null && columnFilterValue !== undefined) {
          returnValue = true
          return false
        }
        return true
      })
    }
    return returnValue
  }

  const getColumnTypeAttribute = (columnConfiguration, isPageLevel) => {
    if (isPageLevel && columnConfiguration.allowFilterInPageParams && columnConfiguration.allowSettingFilterFromPageParams !== true) {
      return { isPageLevel: true, columnType: columnConfiguration.paramSearchType, columnDataType: columnConfiguration.paramSearchDataType }
    } else {
      return { isPageLevel: false, columnType: columnConfiguration.type, columnDataType: columnConfiguration.dataType }
    }
  }

  const displayColumnLevelFilterValue = (props, column, isPageLevel) => {
    // console.log('ATH dCLFV 1, column = ', column, ', isPageLevel = ', isPageLevel)
    let returnValue = null
    const columnConfiguration = props.tableConfiguration.columnConfiguration[column]

    // console.log('ATH dCLFV 2, columnConfiguration = ', columnConfiguration)
    if (isFilterEnabled(columnConfiguration, isPageLevel)) {
      const columnFilterConfiguration = getColumnTypeAttribute(columnConfiguration, isPageLevel)
      // console.log('ATH dCLFV 3, columnFilterConfiguration = ', columnFilterConfiguration)
      returnValue = isPageLevel ? props.parentPageParams.filterParams[column] : props.columnFilters[column]
      if (columnFilterConfiguration.columnType === 'number_range') {
        if (Array.isArray(returnValue) && returnValue.length > 0) {
          const filterOrRangeValues = returnValue[0]
          if (Array.isArray(filterOrRangeValues) && filterOrRangeValues.length >= 2) {
            if (filterOrRangeValues[0] === undefined && filterOrRangeValues[1] !== undefined) {
              returnValue = 'Less than or equal to ' + filterOrRangeValues[1]
            } else if (filterOrRangeValues[0] !== undefined && filterOrRangeValues[1] === undefined) {
              returnValue = 'Greater than or equal to ' + filterOrRangeValues[0]
            } else if (filterOrRangeValues[0] !== undefined && filterOrRangeValues[1] !== undefined) {
              returnValue = 'Between ' + filterOrRangeValues[0] + ' and ' + filterOrRangeValues[1]
            }
          }
        }
      } else if (columnFilterConfiguration.columnType === 'dayjs_range') {
        if (Array.isArray(returnValue) && returnValue.length > 0) {
          const filterOrRangeValues = returnValue[0]
          if (Array.isArray(filterOrRangeValues) && filterOrRangeValues.length >= 2) {
            let startValue
            let endValue
            if (filterOrRangeValues[0] !== undefined && filterOrRangeValues[0] !== null && filterOrRangeValues[0] !== '') {
              if (dayjs.isDayjs(filterOrRangeValues[0])) {
                startValue = filterOrRangeValues[0]
              } else {
                startValue = dayjs(filterOrRangeValues[0])
              }
            }
            if (filterOrRangeValues[1] !== undefined && filterOrRangeValues[1] !== null && filterOrRangeValues[1] !== '') {
              if (dayjs.isDayjs(filterOrRangeValues[1])) {
                endValue = filterOrRangeValues[1]
              } else {
                endValue = dayjs(filterOrRangeValues[1])
              }
            }
            if (startValue === undefined && endValue !== undefined) {
              returnValue = 'Less than or equal to ' + endValue.toDate()
            } else if (startValue !== undefined && endValue === undefined) {
              returnValue = 'Greater than or equal to ' + startValue.toDate()
            } else if (startValue !== undefined && endValue !== undefined) {
              returnValue = 'Between ' + startValue.toDate() + ' and ' + endValue.toDate()
            }
          }
        }
      } else if (['single_select_array', 'multi_select_array', 'default_array'].includes(columnFilterConfiguration.columnType)) {
        if (Array.isArray(returnValue) && returnValue.length > 0) {
          let columnIndexIntoFilterArray = column + '_filter_values'
          if (columnConfiguration.filterValuesKey) {
            columnIndexIntoFilterArray = columnConfiguration.filterValuesKey
          }
          const filterValues = props.filterValuesByKey[columnIndexIntoFilterArray]
          if (filterValues && Array.isArray(filterValues)) {
            let filterTextToDisplay = ''
            filterValues.map(filterValue => {
              if (returnValue.includes(filterValue.value)) {
                if (filterTextToDisplay !== '') {
                  filterTextToDisplay = filterTextToDisplay + ', '
                }
                filterTextToDisplay = filterTextToDisplay + filterValue.text
              }
            })
            if (returnValue.length > 1 && filterTextToDisplay !== '') {
              filterTextToDisplay = 'In ' + filterTextToDisplay
            }
            returnValue = filterTextToDisplay
          } else {
            if (columnConfiguration.filters && Array.isArray(columnConfiguration.filters) && columnConfiguration.filters.length > 0) {
              let columnInFilterArray = 'value'
              if (columnConfiguration.translateFilteredValuesToAlt) {
                columnInFilterArray = columnConfiguration.translateFilteredValuesToAlt
              }
              const selectedColumns = columnConfiguration.filters.filter((object, index) => {
                if (returnValue.includes(object[columnInFilterArray]) || returnValue.includes(object['value'])) {
                  return true
                } else {
                  return false
                }
              })
              let filterTextToDisplay = ''
              selectedColumns.map(filterValue => {
                if (filterTextToDisplay !== '') {
                  filterTextToDisplay = filterTextToDisplay + ', '
                }
                filterTextToDisplay = filterTextToDisplay + filterValue.text
              })
              if (selectedColumns.length > 1 && filterTextToDisplay !== '') {
                filterTextToDisplay = 'In ' + filterTextToDisplay
              }
              returnValue = filterTextToDisplay
            }
          }
        }
      } else if (columnFilterConfiguration.columnType === 'array') {
        if (returnValue.length > 1) {
          returnValue = 'In ' + returnValue.join(', ')
        } else {
          returnValue = returnValue[0]
        }
      } else if (columnFilterConfiguration.columnType === 'dayjs_date_till') {
        returnValue = 'Till ' + moment(returnValue[0].toDate()).format('DD MMM YYYY')
      }
    }
    return returnValue
  }

  const getColumnLevelFilter = (props, isPageLevel) => {
    const columnFilters = isPageLevel ? props.parentPageParams.filterParams : props.columnFilters
    let counter = 0
    const returnValue = Object.keys(columnFilters).map(column => {
      counter++
      return (columnFilters[column] !== null && column !== 'ignoreColumn') ? (
        <div key={'101' + counter}>
          {getColumnHeadingText(props, column)}: {displayColumnLevelFilterValue(props, column, isPageLevel)}
        </div>
      ) : null
    })
    return returnValue
  }

  return (
    <div>
      {/* {arePageLevelFiltersAvailable(props) ? (
        <div>
          Page Level Filter: 
          {getColumnLevelFilter(props, true)}
          <div></div>
        </div>
      ) : null} */}
      {(props && props.parentPageParams && props.parentPageParams.globalSearch) ? (
        <div key='101'>
          Page Search:
        </div>
      ) : null}
      {areTableFiltersAvailable(props) ? (
        <div key='102'>
          Filters:
          {getColumnLevelFilter(props)}
        </div>
      ) : null}
      {isTableSortingAvailable(props) ? (
        <div>
          Sort:
          <div key='103'>
            {getColumnHeadingText(props, props.sorting.columnKey)}: {props.sorting.order}
          </div>
        </div>
      ) : null}
    </div>
  )
}

const delayInMillis = (millisec) => {
  return new Promise(resolve => {
    setTimeout(() => { resolve('') }, millisec);
  })
}

const validateTableConfiguration = (tableConfiguration) => {
  let returnValue = 0
  const columnsConfiguration = tableConfiguration.columnConfiguration
  const columns = Object.keys(columnsConfiguration)
  let numberOfRowsWithDefaultSortOrder = 0
  columns.forEach((column, index) => {
    const columnConfiguration = columnsConfiguration[column]
    if (columnConfiguration.defaultSort) {
      numberOfRowsWithDefaultSortOrder++
    }
  })
  if (numberOfRowsWithDefaultSortOrder > 2) {
    returnValue--
  }

  let validSortColumnsAndDataTypesWhenAllDataIsGotAtOnce = true
  if (tableConfiguration.getAllDataAtOnce) {
    columns.forEach((column, index) => {
      const columnConfiguration = columnsConfiguration[column]
      if (columnConfiguration.enableSort) {
        if (!['date'].includes(columnConfiguration.sortColumnType)) {
          validSortColumnsAndDataTypesWhenAllDataIsGotAtOnce = false
        }
      }
    })
  }
  if (!validSortColumnsAndDataTypesWhenAllDataIsGotAtOnce) {
    returnValue--
  }

  if (tableConfiguration.enableSelection) {
    if (!tableConfiguration.rowKey) {
      returnValue--
    }
  }
  if (returnValue < 0) {
    throw new Error('Invalid form format')
  }
}

const extractSpecificParamsFromSearchParamsFromUrlAndAssignToObject = (pageParams, searchParamsFromURL, paramNameToExtract) => {
  if (searchParamsFromURL.get(paramNameToExtract)) {
    const uncompressedParamsJsonString = decodeURIComponent(searchParamsFromURL.get(paramNameToExtract))// urlEncodedJson)
    const params = JSON.parse(uncompressedParamsJsonString)
    pageParams[paramNameToExtract] = params
  }
}

const createPageParamsFromURLString = (tableConfiguration, searchParamsFromURL) => {
  const pageParams = {}
  extractSpecificParamsFromSearchParamsFromUrlAndAssignToObject(pageParams, searchParamsFromURL, 'paginationParams')
  /* if (searchParamsFromURL.get('filterParams')) {
    const uncompressedFilterParamsJsonString = decodeURIComponent(searchParamsFromURL.get('filterParams'))// urlEncodedJson)
    const filterParams = JSON.parse(uncompressedFilterParamsJsonString)
    pageParams.filterParams = filterParams
    const pageParamKeys = Object.keys(pageParams.filterParams)
    pageParamKeys.forEach((key, index) => {
      if (tableConfiguration && tableConfiguration.columnConfiguration && tableConfiguration.columnConfiguration[key] && tableConfiguration.columnConfiguration[key].allowFilterInPageParams) {

      } else {
        delete pageParams.filterParams[key]
      }
    })
  } */
  extractSpecificParamsFromSearchParamsFromUrlAndAssignToObject(pageParams, searchParamsFromURL, 'filterParams')
  /* if (pageParams.filterParams && Object.keys(pageParams.filterParams).length > 0) {
    const filterParamKeys = Object.keys(pageParams.filterParams)
    filterParamKeys.forEach((key, index) => {
      if (tableConfiguration && tableConfiguration.columnConfiguration
        && tableConfiguration.columnConfiguration[key]
        && tableConfiguration.columnConfiguration[key].allowFilterInPageParams) {
      } else {
        delete pageParams.filterParams[key]
      }
    })
  } */
  /* if (searchParamsFromURL.get('sortParams')) {
    const uncompressedSortParamsJsonString = decodeURIComponent(searchParamsFromURL.get('sortParams'))// urlEncodedJson)
    const sortParams = JSON.parse(uncompressedSortParamsJsonString)
    pageParams.sortParams = sortParams
  } */
  extractSpecificParamsFromSearchParamsFromUrlAndAssignToObject(pageParams, searchParamsFromURL, 'sortParams')
  extractSpecificParamsFromSearchParamsFromUrlAndAssignToObject(pageParams, searchParamsFromURL, 'globalParams')
  extractSpecificParamsFromSearchParamsFromUrlAndAssignToObject(pageParams, searchParamsFromURL, 'columnConfiguration')
  return pageParams
}

const stringifyAndEncode = (runningQueryString, paramName, params) => {
  if (params && Object.keys(params).length > 0) {
    // console.log('ATH cUQSFPP 2')
    const paramsAsString = JSON.stringify(params)
    const encodedParamsAsString = encodeURIComponent(paramsAsString)
    if (runningQueryString !== '') {
      runningQueryString = runningQueryString + '&'
    }
    runningQueryString = runningQueryString + `${paramName}=${encodedParamsAsString}`
    // console.log('ATH cUQSFPP 3, returnQueryString = ', returnQueryString)
  }
  return runningQueryString
}

const createURLQueryStringFromPageParams = (paginationParams, filterParams, sortParams, globalSearchParams, columnConfiguration) => {
  // console.log('ATH cUQSFPP 1')
  let returnQueryString = ''
  // const filterParams = { customer_id: selectedRowKeys }; // Example query parameters
  /* if (paginationParams && Object.keys(paginationParams).length > 0) {
    // console.log('ATH cUQSFPP 2')
    const paramsAsString = JSON.stringify(paginationParams)
    const encodedParamsAsString = encodeURIComponent(paramsAsString)
    if (returnQueryString !== '') {
      returnQueryString = returnQueryString + '&'
    }
    returnQueryString = returnQueryString + `paginationParams=${encodedParamsAsString}`
    // console.log('ATH cUQSFPP 3, returnQueryString = ', returnQueryString)
  } */
  returnQueryString = stringifyAndEncode(returnQueryString, 'paginationParams', paginationParams)
  /* if (filterParams && Object.keys(filterParams).length > 0) {
    // console.log('ATH cUQSFPP 2')
    const paramsAsString = JSON.stringify(filterParams)
    const encodedParamsAsString = encodeURIComponent(paramsAsString)
    if (returnQueryString !== '') {
      returnQueryString = returnQueryString + '&'
    }
    returnQueryString = returnQueryString + `filterParams=${encodedParamsAsString}`
    // console.log('ATH cUQSFPP 3, returnQueryString = ', returnQueryString)
  } */
  const nonNullFilterParams = {}
  for (const key of Object.keys(filterParams)) {
    if (key !== undefined && key !== null && filterParams[key] !== undefined && filterParams[key] !== null) {
      nonNullFilterParams[key] = filterParams[key]
    }
  }

  returnQueryString = stringifyAndEncode(returnQueryString, 'filterParams', nonNullFilterParams)
  /* if (sortParams && Object.keys(sortParams).length > 0) {
    // console.log('ATH cUQSFPP 4')
    const paramsAsString = JSON.stringify(sortParams)
    const encodedParamsAsString = encodeURIComponent(paramsAsString)
    if (returnQueryString !== '') {
      returnQueryString = returnQueryString + '&'
    }
    returnQueryString = returnQueryString + `sortParams=${encodedParamsAsString}`
    // console.log('ATH cUQSFPP 5, returnQueryString = ', returnQueryString)
  } */
  returnQueryString = stringifyAndEncode(returnQueryString, 'sortParams', sortParams)
  returnQueryString = stringifyAndEncode(returnQueryString, 'globalParams', globalSearchParams)
  returnQueryString = stringifyAndEncode(returnQueryString, 'columnConfiguration', columnConfiguration)
  // console.log('ATH cUQSFPP 6, returnQueryString = ', returnQueryString)
  return returnQueryString
}

const MediaCarouselModalButton = ({ buttonTitle, disabled, onClickHandler, modalDialogTitle, configuration }) => {
  const [mediaCarouselModalDialogVisibility, setMediaCarouselModalDialogVisibility] = useState(false)

  const __onClickHandler = () => {
    const openModalDialog = onClickHandler()
    if (openModalDialog) {
      setMediaCarouselModalDialogVisibility(true)
    }
  }

  const onMediaCarouselModalOK = () => {
    setMediaCarouselModalDialogVisibility(false)
  }

  const onMediaCarouselModalCancel = () => {
    setMediaCarouselModalDialogVisibility(false)
  }

  return (
    <>
      <Button
        size="small"
        style={{ marginLeft: 8 }}
        disabled={disabled}
        onClick={__onClickHandler}
      >
        {buttonTitle}
      </Button>
      <Modal
        title={modalDialogTitle}
        open={mediaCarouselModalDialogVisibility}
        onOk={onMediaCarouselModalOK}
        onCancel={onMediaCarouselModalCancel}
        footer={null}
        width={'100%'}
        centered
      >
        <div style={{ display: 'flex', justifyContent: 'center', flexDirection: 'column' }}>
          {/* <MediaCarousel2 
                mediaCallback={standardMediaCallback}
                mediaConfiguration={configuration}
              /> */}
          <MediaCarousel
            key='ath-mcm'
            mediaConfiguration={configuration}
          />

        </div>
      </Modal>
    </>
  )
}

const commentsTableConfiguration = {
  defaultPageSize: 10,
  enableHorizontalScrolling: true,
  getAllDataAtOnce: true,
  disablePagination: true,
  dataTransformationRules: {
    // staff_filter_values: 'bdm_filter_values',
    // staff_name: 'bdm'
  },
  rowKey: 'note_id',
  columns: [
    {
      title: 'Date',
      key: 'note_time',
    },
    {
      title: 'Comment Type',
      key: 'note_type',
    },
    {
      title: 'Comment',
      key: 'note',
    }
  ],
  columnConfiguration: {
    note_time: {
      headingText: 'Note Time',
      formatType: 'date',
      format: 'h:mm a, DD-MMM-YYYY',
    },
  }
}

const loadCommentsReport = async (userToken, queryParams, additionalParams) => {
  try {
    const headers = {
      useCase: 'Get Comments',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    console.log('ATH lCR 1, queryParams = ', queryParams)
    console.log('ATH lCR 2, additionalParams = ', additionalParams)
    if (additionalParams && additionalParams.configuration && additionalParams.configuration.note_query_configuration) {
      const response = await getComments(headers, { query_params: additionalParams.configuration.note_query_configuration })
      return response.data
    } else {
      return { return_code: 0, result: [] }
    }
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const loadCommentFilters = async (userToken) => {
  try {
    return {}
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const CommentViewerModalButton = ({ buttonTitle, disabled, onClickHandler, configuration }) => {
  const userToken = useSelector((state) => state.user.userToken);

  const [reportData, setReportData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  commentsTableConfiguration.additionalParams = { configuration: configuration }

  const [commentModalDialogVisibility, setCommentModalDialogVisibility] = useState(false)

  const tableRef = useRef()

  useEffect(() => {
    if (commentModalDialogVisibility) {
      if (tableRef.current) {
        tableRef.current.initialLoad()
      }
      // initialLoad(commentsTableConfiguration)
    }
  }, [commentModalDialogVisibility]);
  // const columns = createColumnsFromTableConfiguration(commentsTableConfiguration)

  console.log('ATH CVMB 1, configuration = ', configuration)

  const __onClickHandler = () => {
    onClickHandler()
    setCommentModalDialogVisibility(true)
  }

  const loadCommentsCB = (reportQueryParams, additionalParams) => {
    return loadCommentsReport(userToken, reportQueryParams, additionalParams)
  }

  return (
    <>
      <Button
        size="small"
        style={{ marginLeft: 8 }}
        disabled={disabled}
        onClick={__onClickHandler}
      >
        {buttonTitle}
      </Button>
      <Modal
        title={configuration && configuration.title ? configuration.title : undefined}
        open={commentModalDialogVisibility === true ? commentModalDialogVisibility : false}
        onOK={() => {
          console.log('ATH CVM 2')
          setCommentModalDialogVisibility(false)
        }}
        onCancel={() => {
          setCommentModalDialogVisibility(false)
        }}
      >
        {/* <OCTable
          tableKey={tableKey}
          columns={columns}
          reportData={reportData}
          tableConfiguration={commentsTableConfiguration}
          selectedRowKeys={selectedRowKeys}
          setSelectedRowKeys={setSelectedRowKeys}
        /> */}
        <OCTable
          ref={tableRef}
          reportData={reportData}
          setReportData={setReportData}
          tableConfiguration={commentsTableConfiguration}
          selectedRowKeys={selectedRowKeys}
          setSelectedRowKeys={setSelectedRowKeys}
          // postInitialLoadCallback={postInitialLoadCB}
          loadReportCallback={loadCommentsCB}
        // loadInitialFilterDataCallback={loadInitialFilterDataCB}
        // parentPageParams={pageParams}
        // showTableHeader={true}
        // omniSearchRowEnabled={true}
        // omniSearchRowBeginning={backToQueueListLinkUI}
        // omniSearchRowMiddle={viewAnimalDocumentsFarmerSnoozeAndFarmerUpdateStatusButtons}
        />
      </Modal>
    </>
  )
}

const ColumnConfiguration = (props) => {
  const navigate = useNavigate()

  console.log('ATH DC 1')
  const [columnConfigurationModalDialogVisibility, setColumnConfigurationModalDialogVisibility] = useState(false)
  const [itemIndexGettingDragged, setItemIndexGettingDragged] = useState(-1)
  const [newRearrangedColumnList, setNewRearrangedColumnList] = useState(props.rearrangedColumnList)
  const [newCheckedColumnList, setNewCheckedColumnList] = useState(props.checkedColumnList)

  const handleConfigureColumnClick = () => {
    console.log('ATH hCCC 1')
    setColumnConfigurationModalDialogVisibility(true)
  }

  const handleDragStart = (e, index) => {
    setItemIndexGettingDragged(index)
  }

  const handleDrop = (e, index) => {
    console.log('ATH DC hD 1, index = ', index, ', itemIndexGettingDragged = ', itemIndexGettingDragged)
    if (index === itemIndexGettingDragged) {
      return
    }

    let firstIndex
    let secondIndex
    let moveDown = true

    if (index > itemIndexGettingDragged) {
      firstIndex = itemIndexGettingDragged
      secondIndex = index
      // 0 to itemIndexGettingDragged - 1 - do nothing      
      // itemIndexGettingDragged + 1 to index, shift up
      // then oldIndex
      // index + 1 to end - do nothing
    } else if (itemIndexGettingDragged > index) {
      moveDown = false
      firstIndex = index
      secondIndex = itemIndexGettingDragged
      // 0 to index - 1 - do nothing
      // itemIndexGettingDragged
      // index to itemIndexGettingDragged shift down
      // itemIndexGettingDragged + 1 to end - do nothing
    }
    // const part1 = props.rearrangedColumnList.slice(0, firstIndex)
    // const part5 = props.rearrangedColumnList.slice(secondIndex + 1, props.rearrangedColumnList.length)
    // const part3 = props.rearrangedColumnList.slice(firstIndex + 1, secondIndex)
    // const part2 = props.rearrangedColumnList.slice(firstIndex, firstIndex + 1)
    // const part4 = props.rearrangedColumnList.slice(secondIndex, secondIndex + 1)


    const part1 = newRearrangedColumnList?.slice(0, firstIndex)
    const part5 = newRearrangedColumnList?.slice(secondIndex + 1, newRearrangedColumnList?.length)
    const part3 = newRearrangedColumnList?.slice(firstIndex + 1, secondIndex)
    const part2 = newRearrangedColumnList?.slice(firstIndex, firstIndex + 1)
    const part4 = newRearrangedColumnList?.slice(secondIndex, secondIndex + 1)




    let TempnewRearrangedColumnList = []
    if (moveDown) {
      TempnewRearrangedColumnList = [...part1, ...part3, ...part4, ...part2, ...part5]
    } else {
      TempnewRearrangedColumnList = [...part1, ...part4, ...part2, ...part3, ...part5]
    }
    setNewRearrangedColumnList(TempnewRearrangedColumnList)
  }

  const handleCancel = () => {
    setColumnConfigurationModalDialogVisibility(false)
  }

  return (
    <>
      <img
        alt=""
        src={process.env.PUBLIC_URL + "../../img/icons8-columns-24.png"}
        style={{ width: '30px', marginRight: '4px', cursor: 'pointer' }}
        onClick={() => handleConfigureColumnClick()}
      />
      <img
        alt=""
        src={process.env.PUBLIC_URL + "../../img/icons8-columns-50.png"}
        style={{ width: '24px', marginRight: '4px', cursor: 'pointer' }}
        onClick={() => {
          const checkedColumnLists = props.tableConfiguration.columns.map((object) => { return object.key })
          props.setRearrangedColumnList(props.tableConfiguration.columns)
          props.setCheckedColumnList(checkedColumnLists)
          if (props.tableConfiguration && props.tableConfiguration.tableNavigationUrl) {
            const urlQueryString = createURLQueryStringFromPageParams(props.paginationParams, props.filterParams, props.sortParams, { globalSearch: props.globalSearch }, { rearrangedColumnList: props.tableConfiguration.columns, checkedColumnList: checkedColumnLists })
            navigate({
              pathname: `${props.tableConfiguration.tableNavigationUrl}`,
              search: `?${urlQueryString}`,
              replace: true
            })
          }
        }}
      />
      <Modal style={{
        maxWidth: '40vw',
        maxHeight: '100vh',
      }}
        title="Basic Modal" open={columnConfigurationModalDialogVisibility}
        onOk={() => {
          props.setRearrangedColumnList(newRearrangedColumnList)
          props.setCheckedColumnList(newCheckedColumnList)
          setColumnConfigurationModalDialogVisibility(false)
          if (props.tableConfiguration && props.tableConfiguration.tableNavigationUrl) {
            const urlQueryString = createURLQueryStringFromPageParams(props.paginationParams, props.filterParams, props.sortParams, { globalSearch: props.globalSearch }, { rearrangedColumnList: newRearrangedColumnList, checkedColumnList: newCheckedColumnList })
            navigate({
              pathname: `${props.tableConfiguration.tableNavigationUrl}`,
              search: `?${urlQueryString}`,
              replace: true
            })
          }

        }}
        onCancel={handleCancel}
      >
        <div style={{ overflowY: 'auto', maxHeight: '80vh' }}>
          {newRearrangedColumnList.map((column, index) => {
            return (
              <div
                style={{
                  margin: '4px',
                  padding: '4px',
                  height: 50,
                  cursor: 'move',
                  boxShadow: 'rgba(0, 0, 0, 0.35) 0px 5px 15px'
                }}
                onDragStart={(e) => handleDragStart(e, index)}
                onDragOver={(e) => e.preventDefault()}
                draggable={true}
                // onDragEnter={(e) => handleDragEnter(e, index)}
                onDrop={(e) => handleDrop(e, index)}
              >
                <Checkbox
                  checked={newCheckedColumnList.includes(column.key)}
                  key={'column-' + index}
                  disabled={props.tableConfiguration.rowKey === column.key}
                  onChange={(event) => {
                    const result = newCheckedColumnList?.some((checkItem) => checkItem === column.key && column.key);

                    if (result === true) {
                      const updatedColumnKeys = [...newCheckedColumnList].filter((key) => key !== column.key);
                      setNewCheckedColumnList(updatedColumnKeys ? updatedColumnKeys : [])
                    } else {
                      const updatedColumnKeys = [...newCheckedColumnList]
                      updatedColumnKeys.push(column.key);
                      setNewCheckedColumnList(updatedColumnKeys)
                      //   // props.setCheckedColumnList(updatedColumnKeys)
                      //   setNewCheckedColumnList(updatedColumnKeys)
                    }
                    // if (event.target.checked === true) {
                    //   // props.setCheckedColumnList([...props.checkedColumnList, column.key])
                    //   setNewCheckedColumnList([...newCheckedColumnList, column.key])
                    // } else {
                    //   const updatedColumnKeys = newCheckedColumnList.filter((key) => key !== column.key)
                    //   // props.setCheckedColumnList(updatedColumnKeys)
                    //   setNewCheckedColumnList(updatedColumnKeys)
                    // }
                  }}
                >
                  {column.title}
                </Checkbox>
              </div>
            )
          })}
        </div>
      </Modal>
    </>
  )
}

const addRefToEditableTableItemsIfNotAvailable = (list, tableConfiguration) => {
  const columnsToAddRefToArray = []
  if (tableConfiguration && tableConfiguration.columnConfiguration) {
    const columnsInColumnConfiguration = Object.keys(tableConfiguration.columnConfiguration)
    columnsInColumnConfiguration.map((columnInColumnConfiguration, index) => {
      const columnConfiguration = tableConfiguration.columnConfiguration[columnInColumnConfiguration]
      if (columnConfiguration.editable === true) {
        columnsToAddRefToArray.push(columnInColumnConfiguration)
      }
    })
  }
  list.map((listItem, index) => {
    columnsToAddRefToArray.map(columnToAddRefTo => {
      if (listItem[columnToAddRefTo + '_ref'] === undefined) {
        listItem[columnToAddRefTo + '_ref'] = createRef()
      }
    })
  })
}

const addCounterToTableRows = (list, indexKey) => {
  list.map((listItem, index) => {
    // const listItem = list[counter]
    listItem[indexKey] = index
  })
}

const deleteKeysFromObjectInObjectArray = (objectArray, keysToBeDeleted) => {
  objectArray.map(object => {
    for (const keyToBeDeleted of keysToBeDeleted) {
      delete object[keyToBeDeleted]
    }
  })
}

const deleteRefKeysFromObjectInObjectArray = (objectArray, tableConfiguration) => {
  const keysToBeDeleted = []
  if (tableConfiguration && tableConfiguration.columnConfiguration) {
    const columnsInColumnConfiguration = Object.keys(tableConfiguration.columnConfiguration)
    columnsInColumnConfiguration.map((columnInColumnConfiguration, index) => {
      const columnConfiguration = tableConfiguration.columnConfiguration[columnInColumnConfiguration]
      if (columnConfiguration.editable === true) {
        keysToBeDeleted.push(columnInColumnConfiguration + '_ref')
      }
    })
  }
  objectArray.map(object => {
    for (const keyToBeDeleted of keysToBeDeleted) {
      delete object[keyToBeDeleted]
    }
  })
}

const extractEditableTableDelta = (originalList, updatedList, rowKey, insertInsteadOfUpdateOnAbsenseOfIdentifierKey, deleteRowIdentifierKey) => {
  let anyChange = false
  const originalListMap = {}
  originalList.map(object => {
    if (object[rowKey] === undefined || object[rowKey] === null) {
      throw new Error('need key in each row')
    }
    originalListMap[object[rowKey]] = object
  })

  const originalListOfKeys = Object.keys(originalListMap)

  const toBeUpdatedListKeyMap = {}

  const toBeInsertedList = []

  for (const updatedListItem of updatedList) {
    if (updatedListItem[rowKey] === undefined || updatedListItem[rowKey] === null) {
      // add to insert array
      toBeInsertedList.push(updatedListItem)
    } else {
      const objectKey = updatedListItem[rowKey]
      const originalListItem = originalListMap[objectKey]
      if (originalListItem === undefined) {
        // not in original list, needs to be added 
        // throw new Error('key exists in updated list, but not in original')
        toBeInsertedList.push(updatedListItem)
      } else if (lodashObject.isEqual(updatedListItem, originalListItem)) {
        // no change, just remove from original list of keys
        const indexIntoOriginalListOfKeys = originalListOfKeys.indexOf(String(objectKey))
        originalListOfKeys.splice(indexIntoOriginalListOfKeys, 1)
      } else {
        // add to updatedListKeyMap
        if (insertInsteadOfUpdateOnAbsenseOfIdentifierKey !== undefined) {
          if (updatedListItem[insertInsteadOfUpdateOnAbsenseOfIdentifierKey] === undefined || updatedListItem[insertInsteadOfUpdateOnAbsenseOfIdentifierKey] === null) {
            toBeInsertedList.push(updatedListItem)
          } else {
            toBeUpdatedListKeyMap[objectKey] = updatedListItem
          }
        }
        else {
          toBeUpdatedListKeyMap[objectKey] = updatedListItem
        }
        // remove from original listofkeys
        const indexIntoOriginalListOfKeys = originalListOfKeys.indexOf(String(objectKey))
        originalListOfKeys.splice(indexIntoOriginalListOfKeys, 1)
      }
    }
  }
  // the remaining go into to be deleted list
  let toBeDeletedKeyList = []
  if (originalListOfKeys.length > 0) {
    if (deleteRowIdentifierKey) {
      for (const originalListOfKey of originalListOfKeys) {
        const originalItem = originalListMap[originalListOfKey]
        toBeDeletedKeyList.push(originalItem[deleteRowIdentifierKey])
      }
    } else {
      toBeDeletedKeyList = [...originalListOfKeys]
    }
  }

  /* toBeInsertedList.map(object => {
    for (const additionalKeyToBeDeletedAtEndOfProcess of additionalKeysToBeDeletedAtEndOfProcess) {
      delete object[additionalKeyToBeDeletedAtEndOfProcess]
    }
  })

  Object.keys(toBeUpdatedListKeyMap).map(key => {
    const object = toBeUpdatedListKeyMap[key]
    for (const additionalKeyToBeDeletedAtEndOfProcess of additionalKeysToBeDeletedAtEndOfProcess) {
      delete object[additionalKeyToBeDeletedAtEndOfProcess]
    }
  }) */

  if (toBeInsertedList.length > 0 || toBeDeletedKeyList.length > 0 || Object.keys(toBeUpdatedListKeyMap).length > 0) {
    anyChange = true
  }
  return { anyChange, toBeInsertedList, toBeUpdatedListKeyMap, toBeDeletedKeyList }
}

const extractTableListWithUpdatedValues = (editableTableList, tableConfiguration) => {
  const editableKeyArray = []
  if (tableConfiguration && tableConfiguration.columnConfiguration) {
    const columnsInColumnConfiguration = Object.keys(tableConfiguration.columnConfiguration)
    columnsInColumnConfiguration.map((columnInColumnConfiguration, index) => {
      const columnConfiguration = tableConfiguration.columnConfiguration[columnInColumnConfiguration]
      if (columnConfiguration.editable === true) {
        editableKeyArray.push(columnInColumnConfiguration)
      }
    })
  }
  const newList = []
  for (const row of editableTableList) {
    const newRow = lodashObject.cloneDeep(row)
    /* const keyIntoRowDataAsMap = newItem[primaryKeyIntoValueMap]
    const rowValues = rowDataAsMap[keyIntoRowDataAsMap]
    if (rowValues && Object.keys(rowValues).length > 0) {
      const rowValueKeys = Object.keys(rowValues)
      for (const rowValueKey of rowValueKeys) {
        newItem[rowValueKey] = rowValues[rowValueKey]
      }
    } */
    /* for (const itemToDelete of itemsToDelete) {
      delete newItem[itemToDelete]
    } */
    for (const editableKey of editableKeyArray) {
      const ref = editableKey + '_ref'
      if (newRow[ref] && newRow[ref].current !== undefined && newRow[ref].current !== null) {
        let newValue
        try {
          newValue = newRow[ref].current.getValue()
        } catch (error) {
        }
        if (newValue !== undefined) {
          newRow[editableKey] = newValue
        }
      }
    }
    newList.push(newRow)
  }
  return newList
}

const validateEditableTable = (editableTableList, tableConfiguration) => {
  const editableKeyArray = []
  const editableKeyToColumnConfigurationMap = {}
  if (tableConfiguration && tableConfiguration.columnConfiguration) {
    const columnsInColumnConfiguration = Object.keys(tableConfiguration.columnConfiguration)
    columnsInColumnConfiguration.map((columnInColumnConfiguration, index) => {
      const columnConfiguration = tableConfiguration.columnConfiguration[columnInColumnConfiguration]
      if (columnConfiguration.editable === true) {
        editableKeyArray.push(columnInColumnConfiguration)
        editableKeyToColumnConfigurationMap[columnInColumnConfiguration] = columnConfiguration
      }
    })
  }

  let validationFailed = false
  for (const editableRow of editableTableList) {
    for (const editableKey of editableKeyArray) {
      const ref = editableKey + '_ref'
      if (editableRow[ref] && editableRow[ref].current !== undefined && editableRow[ref].current !== null) {
        console.log('Check if row is valid')
        const control = editableRow[ref].current
        const valueToValidate = control.getValue()
        const columnConfiguration = editableKeyToColumnConfigurationMap[editableKey]
        const isValidValue = validateValue(valueToValidate, columnConfiguration.editableType, columnConfiguration.editableValidations)
        if (!isValidValue) {
          validationFailed = true
          control.setErrorStatus("error")
        }
      }
    }
  }
  return validationFailed
}
export {
  delayInMillis, validateTableConfiguration, createPageParamsFromURLString, OCTable, createURLQueryStringFromPageParams, MediaCarouselModalButton, CommentViewerModalButton,
  addRefToEditableTableItemsIfNotAvailable, addCounterToTableRows, deleteKeysFromObjectInObjectArray,
  deleteRefKeysFromObjectInObjectArray,
  extractEditableTableDelta,
  extractTableListWithUpdatedValues, validateEditableTable
}
