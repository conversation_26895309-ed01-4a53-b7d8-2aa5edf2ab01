import React, { useState, useEffect } from 'react'
import { useSelector } from "react-redux"
import { Card, Image, Upload, Modal, Select, Button, Checkbox } from 'antd'
import { FileAddOutlined, UploadOutlined, ExportOutlined, LeftCircleOutlined, EyeOutlined, RightCircleOutlined } from '@ant-design/icons'

// import PDFViewer from "pdf-viewer-reactjs";
// import { pdfjs } from 'react-pdf';
import { Document, Page } from 'react-pdf'
import 'react-pdf/dist/Page/TextLayer.css'

import {
  BASE_URL,
} from '../../Services/api.service';
import { formatedDate } from '../../Utilities/Common/utils';
import './MediaCarousel.css'

const fallbackImageUrlForImage = "https://www.independentmediators.co.uk/wp-content/uploads/2016/02/placeholder-image.jpg";
const fallbackImageUrlForVideo = 'https://th.bing.com/th/id/OIP.zxy3gaRsf4j2zrgH92TdUAAAAA?pid=ImgDet&rs=1'
const fallbackImageForPDF = 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTjd9G2ZJpRM0ctiEx4cIGCDsvTZUPFBbLOZg'

const { getDocuments, uploadFileToS3, saveDocuments } = require('../../router')


// pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`
// pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@2.16.105/build/pdf.worker.min.js`
// pdfjs.GlobalWorkerOptions.workerSrc = '//cdn.jsdelivr.net/npm/pdfjs-dist@2.16.105/build/pdf.worker.js'
// console.log('APP.js pdfjs.version = ', pdfjs.version)

const standardMediaCallback = (media) => {
  const mediaMetadata = {}
  if (media.document_information && media.document_information.fileType && media.document_information.url) {
    if (['video/mp4'].includes(media.document_information.fileType)) {
      mediaMetadata.type = 'video'
    } else if (['application/pdf'].includes(media.document_information.fileType)) {
      mediaMetadata.type = 'pdf'
      if (media.document_information.numberOfPages) {
        mediaMetadata.numberOfPages = media.document_information.numberOfPages
      }
    } else {
      mediaMetadata.type = 'image'
    }
    // mediaMetadata.url = BASE_URL + "/media/" + media.document_id
    mediaMetadata.url = BASE_URL + "/media-key?url=" + media.document_information.url
    mediaMetadata.documentType = media.document_type
    mediaMetadata.documentUploadTime = media.document_upload_time
    mediaMetadata.fileName = (media.document_information.fileName === undefined) ? 'download' : media.document_information.fileName
  }
  return mediaMetadata
}

const MediaCarousel = ({ mediaCallback, mediaConfiguration }) => {
  const [previewVisible, setPreviewVisible] = useState(false)
  const [currentIndexForPreview, setCurrentIndexForPreview] = useState(null)
  const [currentIndexForHoveredMedia, setCurrentIndexForHoveredMedia] = useState(null)
  const [selectedDocumentedTypesForFilter, setSelectedDocumentedTypesForFilter] = useState([])
  const [documentTypeForUpload, setDocumentTypeForUpload] = useState(null)
  const [latestOnly, setLatestOnly] = useState(mediaConfiguration.latestOnly === true ? true : false)
  const [mediaArray, setMediaArray] = useState([])
  const [uploadFileList, setUploadFileList] = useState([])
  const [fileUploading, setFileUploading] = useState(false);
  console.log('MC c 1, mediaArray.length = ', mediaArray.length)
  console.log('MC c 0, mediaConfiguration = ', mediaConfiguration)

  const userToken = useSelector((state)=>state.user.userToken)

  const uploadConfiguration = {
    onRemove: (file) => {
      const index = uploadFileList.indexOf(file);
      const newFileList = uploadFileList.slice();
      newFileList.splice(index, 1);
      setUploadFileList(newFileList);
    },
    beforeUpload: (file) => {
      setUploadFileList([...uploadFileList, file]);
      return false;
    },
    fileList: uploadFileList,
    listType: "picture",
  };

  const handleFileUpload = async () => {
    try {
      console.log('MC hFU 1')
      setFileUploading(true)
      const basicDocumentInformation = {
        ...mediaConfiguration.uploadEntityInformation,
        document_type_id: documentTypeForUpload
      }
      const documents = []
      for (let counter = 0 ; counter < uploadFileList.length ; counter++) {
        console.log('MC hFU 2')
        const file = uploadFileList[counter]
        console.log('MC hFU 3')
        const headers = {
          useCase: 'Upload File',
          token: userToken.accessToken, //authToken
          refreshtoken: localStorage.getItem("refreshToken"),
          // 'content-type': "multipart/form-data; boundary=----WebKitFormBoundary16fFTZb5x5AJ1G9x"
          'content-type': "multipart/form-data"
        }
        const formData = new FormData()
        formData.append('file', file)
        formData.append("entity", mediaConfiguration.uploadFolder);
        const response = await uploadFileToS3(headers, formData)
        console.log('MC hFU 4, response = ', response)
        if (response.data.result === true) {
          const locationInS3 = response.data.data.doc_key
          const documentInformation = {
            ...mediaConfiguration.uploadEntityDocumentInformation,
            fileName: file.name,
            fileType: file.type,
            url: locationInS3
          }
          basicDocumentInformation.document_information = documentInformation
          console.log('MC hFU 5, basicDocumentInformation = ', basicDocumentInformation)
          documents.push(basicDocumentInformation)
        } else {
          throw new Error('Not able to upload file to s3')
        }
      }
      console.log('MC hFU 6, documents = ', documents)
      const headers2 = {
        useCase: 'Save Documents',
        token: userToken.accessToken, //authToken
        refreshtoken: localStorage.getItem("refreshToken"),
      }
      const saveDocumentsResult = await saveDocuments(headers2, {document_entity_name: mediaConfiguration.uploadEntity, documents: documents})
      console.log('MC hFU 7, saveDocumentsResult = ', saveDocumentsResult)
      await reloadMedia()
    } catch (error) {
      console.log('MC hFU 10, error')
      console.log('MC hFU 10a, error = ', error)
    } finally {
      setUploadFileList([])
      setFileUploading(false)
    }
  }

  const downloadHandler = (index) => {
    console.log('MC dH 1, index = ', index)
    const media = mediaArray[index]
    const mediaMetadata = mediaCallback(media)
    
    const link = document.createElement("a")
    link.href = mediaMetadata.url
    // link.download = mediaMetadata.fileName // "Prescription.pdf"
    link.setAttribute("download", mediaMetadata.fileName)
    document.body.appendChild(link)
    link.target = "_blank"
    link.click()
    link?.parentNode?.removeChild(link)
  }

  const reloadMedia = async () => {
    console.log('MC rL 1')
    await loadMediaAsync(userToken, {...mediaConfiguration, document_type_id_array: selectedDocumentedTypesForFilter, latestOnly: latestOnly})
  }
  
  const loadMediaAsync = async (userToken, mediaConfiguration) => {
    try {
      console.log('MC lMA 0, mediaConfiguration = ', mediaConfiguration)
      if (mediaConfiguration.completeInformation) {
        console.log('MC lMA 1, mediaConfiguration = ', mediaConfiguration)
        const headers = {
          useCase: 'Get Document Information',
          token: userToken.accessToken, //authToken
          refreshtoken: localStorage.getItem("refreshToken")
        }
        const response = await getDocuments(headers, mediaConfiguration)
        console.log('MC lMA 2, response = ', response)
        
        if (response.data.return_code === 0) {
          setMediaArray(response.data.result)
        } else {
          setMediaArray([])
        }
      } else {
        setMediaArray([])
      }
    } catch (error) {
      console.log('SA lR 10, error')
      console.log('SA lR 10, error = ', error)
      throw error
    }
  }

  useEffect(() => {
    loadMediaAsync(userToken, mediaConfiguration)
  }, [mediaConfiguration]);

  const openMediaPreview = (index) => {
    setPreviewVisible(true)
    setCurrentIndexForPreview(index)
  }

  const closeMediaPreview = () => {
    setPreviewVisible(false)
    setCurrentIndexForPreview(null)
  }

  const onDocumentLoadSuccess = (pageInformation, index) => {
    console.log('MC oDLS 1, pageInformation = ', pageInformation)
    const numPages = pageInformation.numPages
    console.log('MC oDLS 2, numPages = ', numPages)
    console.log('MC oDLS 3, arg2 = ', index)
    const mediaElement = mediaArray[index]
    mediaElement.document_information.numberOfPages = numPages
  }

  const renderPDFInPreviewMode = (isPreviewMode, mediaMetadata) => {
    if (mediaMetadata.numberOfPages && mediaMetadata.numberOfPages > 0) {
      console.log('MC rPIPM, rendering multiplage pdf')
      return (
        <div style={{overflowY: 'scroll', maxHeight: '80vh' }}>
          {Array.apply(null, Array(mediaMetadata.numberOfPages)).map((x, i) => i + 1).map(page => {
            return (
              <Page
                pageNumber={page}
                renderAnnotationLayer={false}
              />
            )
          })}
        </div>
      )
    } else {
      return (
        <Page
          width={isPreviewMode ? undefined : 200}
          pageIndex={isPreviewMode ? 0 : undefined}
          pageNumber={isPreviewMode ? undefined : 1}
          renderAnnotationLayer={false}
        />
      )
    }
  }

  const getMediaPlayer = (isPreviewMode, index) => {
    const media = mediaArray[index]
    const mediaMetadata = mediaCallback(media)
    if (mediaMetadata.type === 'image') {
      return (
        <div
          class={isPreviewMode ? undefined: 'image-container'}
          onClick={isPreviewMode ? undefined : () => openMediaPreview(index)}
        >
          <Image
            // class={isPreviewMode ? 'image-style-in-preview-mode' : 'overlay-image'}
            style={isPreviewMode
              ? 
              {
                maxWidth: '80vw',
                minWidth: '80vw',
                maxHeight: '80vh',
                minHeight: '80vh',
                overflowY: 'auto',
                objectFit: 'contain'}
              :
              {
                maxWidth: '200px',
                height: 'auto',
                position: 'relative'
              }
            }
            preview={false}
            height={isPreviewMode ? "auto" : undefined}
            src={mediaMetadata.url}
            fallback={fallbackImageUrlForImage}
          />
          {isPreviewMode ? null : (
            <div class="overlay-text">Preview<EyeOutlined style={{paddingLeft: '5px'}}/></div>
          )}
        </div>
      )
    } else if (mediaMetadata.type === 'pdf') {
      /* return (
        <PDFViewer
          style={{ height: "100%", maxWidth: '200px' }}
          hideNavbar={true}
          // page={1}
          document={{
            url: 'https://arxiv.org/pdf/quant-ph/0410100.pdf',
          }}
        />
      ) */
      return (
        <div
          class={isPreviewMode ? undefined: 'image-container'}
          onClick={isPreviewMode ? undefined : () => openMediaPreview(index)}
        >
          <Document
            file={mediaMetadata.url}
            // file='https://arxiv.org/pdf/quant-ph/0410100.pdf'
            // file='https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
            onLoadSuccess={(pageInformation) => onDocumentLoadSuccess(pageInformation, index)}
            onLoadError={(arg1, arg2) => {
              console.log('MC p r oLE 1, arg1 = ', arg1)
              console.log('MC p r oLE 2, arg2 = ', arg2)
            }}
            onRenderError={(arg1, arg2) => {
              console.log('MC p r oRE 1, arg1 = ', arg1)
              console.log('MC p r oRE 2, arg2 = ', arg2)
            }}
          >
            {/* {mediaMetadata.numberOfPages ? (<div></div>) : (<div></div>)} */}
            {console.log('mediaMetadata.numberOfPages = ', mediaMetadata.numberOfPages)}
            {isPreviewMode ? renderPDFInPreviewMode(isPreviewMode, mediaMetadata) : (
              <Page
                  width={isPreviewMode ? undefined : 200}
                  pageIndex={isPreviewMode ? 0 : undefined}
                  pageNumber={isPreviewMode ? undefined : 1}
                  renderAnnotationLayer={false}
                />
            )}
          </Document>
          {isPreviewMode ? null : (
            <div class="overlay-text">Preview<EyeOutlined style={{paddingLeft: '5px'}}/></div>
          )}
        </div>
      )
    } else if (mediaMetadata.type === 'video') {
      if (!isPreviewMode) {
        return (
          <div
            class={'image-container'}
            onClick={() => openMediaPreview(index)}
          >
          <Image
            preview={false}
            width={200}
            src={mediaMetadata.url}
            fallback={fallbackImageUrlForVideo}
            // onClick={isPreviewMode ? undefined : () => openMediaPreview(index)}
          />
          <div class="overlay-text">Preview<EyeOutlined style={{paddingLeft: '5px'}}/></div>
          </div>
        )
      } else {
        return (
          <video
            style={{ maxWidth: '80vw', minWidth: '80vw', maxHeight: '80vh', minHeight: '80vh', overflowY: 'auto', objectFit: 'contain' }}
            // width="100%"
            controls
            src={mediaMetadata.url}
            />
        )
      }
    }
  }

  return (
    <div>
      {mediaConfiguration && mediaConfiguration.documentTypesForFilter && Array.isArray(mediaConfiguration.documentTypesForFilter) && mediaConfiguration.documentTypesForFilter.length > 0 ? (
        <div>
          <Select
            mode="multiple"
            allowClear
            style={{
              width: '300px',
              marginLeft: '10px'
            }}
            placeholder="Please select"
            onChange={(selectValues, selectedObjects) => {
              setSelectedDocumentedTypesForFilter(selectValues)
            }}
            options={mediaConfiguration.documentTypesForFilter}
          />
          <Button style={{
              marginLeft: '10px'
            }} onClick={() => reloadMedia()}>Refresh</Button>
          {
            mediaConfiguration.showLatestOnlyFlag === true ? (
              <Checkbox style={{
                marginLeft: '10px'
              }} checked={latestOnly} onChange={() => {
                setLatestOnly(!latestOnly)
              }} disabled={mediaConfiguration.enableLatestOnlyFlag === true ? undefined : true}>Latest Only</Checkbox>
            ) : null
          }
          
        </div>
      ): null}
      <div style={{overflowX: 'scroll', display: 'flex', flexWrap: 'nowrap'}}>
      {mediaArray.map((media, index) => {
        const mediaMetadata = mediaCallback(media)
        // console.log('MC 1, mediaMetadata = ', mediaMetadata)
        return (
          <Card key={'media-card-'+index} style={{ display: 'inline-block', margin: '8px' }}>
            {getMediaPlayer(false, index)}
            <div>{mediaMetadata.documentType}</div>
            <div>{formatedDate(mediaMetadata.documentUploadTime)}</div>
            <div><ExportOutlined onClick={() => downloadHandler(index)}/></div>
          </Card>
        )
      })}
      {previewVisible && (
        <div className="centered-content">
          {/* <div className="overlay" /> Semi-transparent overlay */}
          <Modal
            title='Preview'
            open={previewVisible}
            onCancel={closeMediaPreview}
            footer={null}
            // class="centered-modal" // Add a custom class for styling
            // style={{bottom: -40}}
            width={'100%'}
            centered
          >
            <LeftCircleOutlined 
              style={{
                marginLeft: '16px',
                position: 'absolute',
                left: 0,
                top: '50%',
                transform: 'translateY(-50%)',
                fontSize: '64px',
                color: currentIndexForPreview === 0 ? 'gray' : 'black'
              }}
              onClick={() => {
                if (currentIndexForPreview !== 0) {
                  setCurrentIndexForPreview(currentIndexForPreview - 1)
                }
              }}
              // disabled={currentIndexForPreview === 0}
            />
            <RightCircleOutlined 
              style={{
                marginRight: '16px',
                position: 'absolute',
                right: 0,
                top: '50%',
                transform: 'translateY(-50%)',
                fontSize: '64px',
                color: (currentIndexForPreview === (mediaArray.length - 1)) ? 'gray' : 'black'
              }}
              onClick={() => {
                if (currentIndexForPreview !== (mediaArray.length - 1)) {
                  setCurrentIndexForPreview(currentIndexForPreview + 1)
                }
              }}
              disabled={(currentIndexForPreview === (mediaArray.length - 1))}
            />
            
            <div style={{display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
              {getMediaPlayer(true, currentIndexForPreview)}
              {/* <div>{mediaCallback(mediaArray[currentIndexForPreview]).documentType}</div> */}
              {/* <div>{formatedDate(mediaCallback(mediaArray[currentIndexForPreview]).documentUploadTime)}</div> */}
            </div>
          </Modal>
        </div>
      )}
      </div>
      {mediaConfiguration && mediaConfiguration.enableUploadOfFiles && mediaConfiguration.documentTypesForUpload && Array.isArray(mediaConfiguration.documentTypesForUpload) && mediaConfiguration.documentTypesForUpload.length > 0 ? (
        <div>
          <Button 
            icon={<UploadOutlined />}
            style={{
              marginLeft: '10px'
            }}
            type="primary"
            onClick={handleFileUpload}
            disabled={uploadFileList.length === 0 || documentTypeForUpload === null}
            loading={fileUploading}
            >
            Upload
          </Button>
          <Select
              mode="single"
              allowClear
              style={{
                width: '300px',
                marginLeft: '10px'
              }}
              placeholder="Please select"
              onChange={(selectValues, selectedObjects) => {
                console.log('MC dUS 1, selectedValues = ', selectValues)
                setDocumentTypeForUpload(selectValues)
              }}
              options={mediaConfiguration.documentTypesForUpload}
            />
          <Upload {...uploadConfiguration}>
            <Button icon={<FileAddOutlined />}>Select File</Button>
          </Upload>
          
        </div>
      ) : null}
    </div>
  );
};

export { MediaCarousel, standardMediaCallback }