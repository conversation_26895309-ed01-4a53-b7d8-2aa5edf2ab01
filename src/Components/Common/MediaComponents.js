import { useEffect, useState } from "react";
import { useSelector } from "react-redux"

import { Card, Image, Modal, Button, Upload, Select, Checkbox } from 'antd'
import { RotateLeftOutlined, RotateRightOutlined, FileAddOutlined, UploadOutlined, ExportOutlined, EyeOutlined, LeftCircleOutlined, RightCircleOutlined, DownloadOutlined } from '@ant-design/icons'

import { Document, Page } from 'react-pdf'
import 'react-pdf/dist/Page/TextLayer.css'

import './MediaCarousel.css'

const {ToastersService} = require('../../Services/toasters.service')

const {
  configurationJSON,
} = require("@krushal-it/common-core");

const fallbackImageUrlForImage = "https://www.independentmediators.co.uk/wp-content/uploads/2016/02/placeholder-image.jpg";
const fallbackImageUrlForVideo = 'https://th.bing.com/th/id/OIP.zxy3gaRsf4j2zrgH92TdUAAAAA?pid=ImgDet&rs=1'
const fallbackImageForPDF = 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTjd9G2ZJpRM0ctiEx4cIGCDsvTZUPFBbLOZg'

const { getDocumentDetails, getDocuments, uploadFileToS3, saveDocuments } = require('../../router')
const { formatedDate } = require('../../Utilities/Common/utils')

const BASE_URL = configurationJSON().SERVICE_END_POINTS.BACK_END

const onDocumentLoadSuccess = (pageInformation, mediaInformation) => {
  const numPages = pageInformation.numPages
  mediaInformation.numberOfPages = numPages
}

const getMediaTypeFromFileType = (fileType) => {
  let mediaType = 'image'
  if (['video/mp4'].includes(fileType)) {
    mediaType = 'video'
  } else if (['application/pdf'].includes(fileType)) {
    mediaType = 'pdf'
  }
  return mediaType
}

const MediaPlayer = ({styleContainer, enablePreview, previewRelatedInformation, mediaInformationArray, mediaInformationArrayIndex, enablePreviewScroll}) => {
  const [previewModalVisibile, setPreviewModalVisibile] = useState(false)
  const [currentIndexForPreview, setCurrentIndexForPreview] = useState(mediaInformationArrayIndex)
  const [rotateInPreview, setRotateInPreview] = useState(false)
  const [rotateDegree, setRotateDegree] = useState(0)
  const openPreviewModal = () => {
    setPreviewModalVisibile(true)
  }
  const mediaType = getMediaTypeFromFileType(mediaInformationArray[mediaInformationArrayIndex].fileType)
  /* let mediaType = 'image'
  if (['video/mp4'].includes(mediaInformationArray[mediaInformationArrayIndex].fileType)) {
    mediaType = 'video'
  } else if (['application/pdf'].includes(mediaInformationArray[mediaInformationArrayIndex].fileType)) {
    mediaType = 'pdf'
  } */

  const onRotateLeft = () => {
    if (rotateDegree === -270) {
      setRotateDegree(0)
    } else {
      setRotateDegree(rotateDegree -90)
    }
  }
  const onRotateRight = () => {
    if (rotateDegree === 270) {
      setRotateDegree(0)
    } else {
      setRotateDegree(rotateDegree + 90)
    }
  }
  let elementHTML = null

  if (mediaType === 'image') {
    elementHTML = (
      <div
        class='image-container'
        style={styleContainer.mediaTagWrapperDivStyle}
        onClick={enablePreview ? openPreviewModal : undefined}
      >
        <Image
          // class={isPreviewMode ? 'image-style-in-preview-mode' : 'overlay-image'}
          style={styleContainer.mediaTagStyle}
          preview={false}
          src={mediaInformationArray[mediaInformationArrayIndex].url}
          fallback={fallbackImageUrlForImage}
        />
        {enablePreview ? (
          <div class="overlay-text">Preview<EyeOutlined style={{paddingLeft: '5px'}}/></div>
        ) : null}
      </div>
    )
  } else if (mediaType === 'video') {
    elementHTML = (
      <div
        class='image-container'
        style={styleContainer.mediaTagWrapperDivStyle}
        onClick={enablePreview ? openPreviewModal : undefined}
          /* onClick={() => openMediaPreview(index)} */
      >
      <Image
        style={styleContainer.mediaTagStyle}
        preview={false}
        fallback={fallbackImageUrlForVideo}
        // onClick={isPreviewMode ? undefined : () => openMediaPreview(index)}
      />
      {enablePreview ? (
        <div class="overlay-text">Preview<EyeOutlined style={{paddingLeft: '5px'}}/></div>
      ) : null}
      </div>
    )
  } else if (mediaType === 'pdf') {
    elementHTML = (
      <div
        class='image-container'
        style={styleContainer.mediaTagWrapperDivStyle}
        onClick={enablePreview ? openPreviewModal : undefined}
      >
        <Document
          file={mediaInformationArray[mediaInformationArrayIndex].url}
          // file='https://arxiv.org/pdf/quant-ph/0410100.pdf'
          // file='https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
          onLoadSuccess={(pageInformation) => onDocumentLoadSuccess(pageInformation, mediaInformationArray[mediaInformationArrayIndex])}
          onLoadError={(arg1, arg2) => {
            console.log('MC p r oLE 1, arg1 = ', arg1)
            console.log('MC p r oLE 2, arg2 = ', arg2)
          }}
          onRenderError={(arg1, arg2) => {
            console.log('MC p r oRE 1, arg1 = ', arg1)
            console.log('MC p r oRE 2, arg2 = ', arg2)
          }}
        >
          {/* {mediaMetadata.numberOfPages ? (<div></div>) : (<div></div>)} */}
          {console.log('mediaInformationArray[mediaInformationArrayIndex].numberOfPages = ', mediaInformationArray[mediaInformationArrayIndex].numberOfPages)}
          <Page
              width={200}
              pageNumber={1}
              renderAnnotationLayer={false}
            />
        </Document>
        {enablePreview ? (
          <div class="overlay-text">Preview<EyeOutlined style={{paddingLeft: '5px'}}/></div>
        ) : null}
      </div>
    )
  }
  if (enablePreview) {
    return (
      <>
        {elementHTML}
        <Modal
          title='Preview'
          open={previewModalVisibile}
          onCancel={() => {setPreviewModalVisibile(false)}}
          footer={null}
          // class="centered-modal" // Add a custom class for styling
          // style={{bottom: -40}}
          width={'100%'}
          centered
        >
          {enablePreviewScroll ? (
            <>
              <LeftCircleOutlined
                style={{
                  marginLeft: '16px',
                  position: 'absolute',
                  left: 0,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  fontSize: '64px',
                  color: currentIndexForPreview === 0 ? 'gray' : 'black'
                }}
                onClick={() => {
                  if (currentIndexForPreview !== 0) {
                    setCurrentIndexForPreview(currentIndexForPreview - 1)
                  }
                }}
                disabled={currentIndexForPreview === 0}
              />
              <RightCircleOutlined 
                style={{
                  marginRight: '16px',
                  position: 'absolute',
                  right: 0,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  fontSize: '64px',
                  color: (currentIndexForPreview === (mediaInformationArray.length - 1)) ? 'gray' : 'black'
                }}
                onClick={() => {
                  if (currentIndexForPreview !== (mediaInformationArray.length - 1)) {
                    setCurrentIndexForPreview(currentIndexForPreview + 1)
                  }
                }}
                disabled={(currentIndexForPreview === (mediaInformationArray.length - 1))}
              />
            </>
          ) : null}
          <MediaPreview
            mediaInformation={mediaInformationArray[mediaInformationArrayIndex]}
            previewRelatedInformation={previewRelatedInformation}
            mediaInformationArray={mediaInformationArray}
            mediaInformationArrayIndex={currentIndexForPreview}
            rotateInPreview={rotateInPreview}
            setRotateInPreview={setRotateInPreview}
            rotateDegree={rotateDegree}
            setRotateDegree={setRotateDegree}
          />
          {rotateInPreview ? (
            <div style={{position:'absolute', bottom:'5px', display:'flex', justifyContent:'center', width:'100%'}}>
              <div style={{marginTop:'16px'}}>
                <RotateLeftOutlined style={{ fontSize: '24px', marginRight:'8px' }} onClick={onRotateLeft}/>
                <RotateRightOutlined style={{ fontSize: '24px', marginRight:'8px' }} onClick={onRotateRight}/>
              </div>
            </div>
          ) : null}
        </Modal>
      </>
    )
  } else {
    return elementHTML
  }
}

const launchInBrowser = (mediaInformation) => {
  if (mediaInformation.url) {
    const link = document.createElement("a")
    link.href = mediaInformation.url
    // link.download = mediaMetadata.fileName // "Prescription.pdf"
    if (mediaInformation.fileName) {
      link.setAttribute("download", mediaInformation.fileName)
    } else {
      const lastDotIndex = mediaInformation.url.lastIndexOf(".")
      if (lastDotIndex !== -1) {
        const substringToRight = mediaInformation.url.slice(lastDotIndex + 1);
        link.setAttribute("download", 'UnnamedFile.' + substringToRight)
      } else {
        link.setAttribute("download", 'UnnamedFile.txt')
      }
    }
    document.body.appendChild(link)
    link.target = "_blank"
    link.click()
    link?.parentNode?.removeChild(link)
  } else {
    ToastersService.failureToast('Not a valid url to download')
  }
}

const downloadHandler = async (mediaInformation) => {
  if (mediaInformation.url) {
    const response = await fetch(mediaInformation.url)
    const blob = await response.blob()

    // Create a link element and trigger the download
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    if (mediaInformation.fileName) {
      // link.setAttribute("download", mediaInformation.fileName)
      link.download = mediaInformation.fileName // You can specify the filename here
    } else {
      const lastDotIndex = mediaInformation.url.lastIndexOf(".")
      if (lastDotIndex !== -1) {
        const substringToRight = mediaInformation.url.slice(lastDotIndex + 1)
        // link.setAttribute("download", 'UnnamedFile.' + substringToRight)
        link.download = 'UnnamedFile.' + substringToRight
      } else {
        // link.setAttribute("download", 'UnnamedFile.txt')
        link.download = 'UnnamedFile.tmp'
      }
    }
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}
const loadMediaInformation = async(userToken, documentId, mediaInformation, setMediaInformation) => {
  const headers = {
    useCase: 'Download File',
    token: userToken.accessToken, //authToken
    refreshtoken: localStorage.getItem("refreshToken"),
  }
  const response = await getDocumentDetails(headers, documentId)
  if (response.data && response.data.return_code === 0) {
    const result = response.data.result
    const newMediaInformation = {
      ...mediaInformation
    }
    if (result.document_type_id) {
      newMediaInformation.documentTypeId = result.document_type_id
    }
    if (result.document_type) {
      newMediaInformation.documentType = result.document_type
    }
    if (result.document_name) {
      newMediaInformation.fileName = result.document_name
    }
    if (result.document_upload_time) {
      newMediaInformation.documentUploadTime = result.document_upload_time
    }
    if (result.document_information && result.document_information.url) {
      newMediaInformation.s3Key = result.document_information.url
      newMediaInformation.url = BASE_URL + "/media-key?url=" + result.document_information.url
    }
    if (result.document_information && result.document_information.fileType) {
      newMediaInformation.fileType = result.document_information.fileType
    }
    if (result.document_information && result.document_information.fileName) {
      newMediaInformation.fileName = result.document_information.fileName
    }
    setMediaInformation(newMediaInformation)
  }
}

const onRemoveUploadedFiles = (file, uploadFileList, setUploadFileList) => {
  const index = uploadFileList.indexOf(file)
  const newFileList = uploadFileList.slice()
  newFileList.splice(index, 1)
  setUploadFileList(newFileList)
}

const beforeUploadOfFiles = (file, uploadFileList, setUploadFileList) => {
  setUploadFileList([...uploadFileList, file]);
  return false;
}

const handleFileUpload = async (userToken, documentTypeForUpload, uploadFileList, setUploadFileList, setFileUploading, uploadInformation, reloadDocumentsInParent) => {
  try {
    console.log('MC hFU 1')
    setFileUploading(true)
    const basicDocumentInformation = uploadInformation && uploadInformation.uploadEntityInformation ? {
      ...uploadInformation.uploadEntityInformation,
      document_type_id: documentTypeForUpload !== undefined ? documentTypeForUpload : undefined
    } : {}
    const documents = []
    for (let counter = 0 ; counter < uploadFileList.length ; counter++) {
      console.log('MC hFU 2')
      const file = uploadFileList[counter]
      console.log('MC hFU 3')
      const headers = {
        useCase: 'Upload File',
        token: userToken.accessToken, //authToken
        refreshtoken: localStorage.getItem("refreshToken"),
        'content-type': "multipart/form-data"
      }
      const formData = new FormData()
      formData.append('file', file)
      formData.append("entity", uploadInformation.uploadFolder);
      const response = await uploadFileToS3(headers, formData)
      console.log('MC hFU 4, response = ', response)
      if (response.data.result === true) {
        const locationInS3 = response.data.data.doc_key
        const documentInformation = {
          ...uploadInformation.uploadEntityDocumentInformation,
          fileName: file.name,
          fileType: file.type,
          url: locationInS3
        }
        basicDocumentInformation.document_information = documentInformation
        console.log('MC hFU 5, basicDocumentInformation = ', basicDocumentInformation)
        documents.push(basicDocumentInformation)
      } else {
        throw new Error('Not able to upload file to s3')
      }
    }
    console.log('MC hFU 6, documents = ', documents)
    const headers2 = {
      useCase: 'Save Documents',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken"),
    }
    const saveDocumentsResult = await saveDocuments(headers2, {document_entity_name: uploadInformation.uploadEntity, documents: documents})
    console.log('MC hFU 7, saveDocumentsResult = ', saveDocumentsResult)
    // await reloadMedia()
    if (reloadDocumentsInParent) {
      await reloadDocumentsInParent()
    }
  } catch (error) {
    console.log('MC hFU 10, error')
    console.log('MC hFU 10a, error = ', error)
  } finally {
    setUploadFileList([])
    setFileUploading(false)
  }
}

const UploadDocumentInKrushalOCFormControl = (props) => {
  const userToken = useSelector((state)=>state.user.userToken)

  const [uploadFileList, setUploadFileList] = useState([])
  const [fileUploading, setFileUploading] = useState(false)

  const uploadConfiguration = {
    onRemove: (file) => onRemoveUploadedFiles(file, uploadFileList, setUploadFileList),
    beforeUpload: (file) => beforeUploadOfFiles(file, uploadFileList, setUploadFileList),
    fileList: uploadFileList,
    listType: "picture",
    multiple: props.uploadInformation.uploadMultiple,
    maxCount: (props.uploadInformation.uploadMaxCount)? props.uploadInformation.uploadMaxCount : undefined
  }

  return (
  <div>
    <Button 
      icon={<UploadOutlined />}
      style={{
        marginLeft: '10px'
      }}
      type="primary"
      onClick={() => handleFileUpload(userToken, undefined, uploadFileList, setUploadFileList, setFileUploading, props.uploadInformation)}
      disabled={uploadFileList.length === 0 || uploadFileList.length > 1}
      loading={fileUploading}
      >
      Upload
    </Button>
    <Upload
      {...uploadConfiguration}
    >
      <Button icon={<FileAddOutlined />}>Select File</Button>
    </Upload>
    
  </div>
  )
}

const UploadDocumentInCarousel = (props) => {
  const userToken = useSelector((state)=>state.user.userToken)

  const [uploadFileList, setUploadFileList] = useState([])
  const [fileUploading, setFileUploading] = useState(false)
  const [documentTypeForUpload, setDocumentTypeForUpload] = useState(null)

  const uploadConfiguration = {
    onRemove: (file) => onRemoveUploadedFiles(file, uploadFileList, setUploadFileList),
    beforeUpload: (file) => beforeUploadOfFiles(file, uploadFileList, setUploadFileList),
    fileList: uploadFileList,
    listType: "picture",
    multiple: props.uploadInformation.uploadMultiple,
    maxCount: (props.uploadInformation.uploadMaxCount)? props.uploadInformation.uploadMaxCount : undefined
  }

  return (
  <div>
    <Button 
      icon={<UploadOutlined />}
      style={{
        marginLeft: '10px'
      }}
      type="primary"
      onClick={() => handleFileUpload(userToken, documentTypeForUpload, uploadFileList, setUploadFileList, setFileUploading, props.uploadInformation, props.reloadDocuments)}
      disabled={uploadFileList.length === 0 || documentTypeForUpload === null}
      loading={fileUploading}
      >
      Upload
    </Button>
    <Select
        mode="single"
        allowClear
        style={{
          width: '300px',
          marginLeft: '10px'
        }}
        placeholder="Please select"
        onChange={(selectValues, selectedObjects) => {
          console.log('MC dUS 1, selectedValues = ', selectValues)
          setDocumentTypeForUpload(selectValues)
        }}
        options={props.documentTypesForUpload}
      />
    <Upload {...uploadConfiguration}>
      <Button icon={<FileAddOutlined />}>Select File</Button>
    </Upload>
    
  </div>
  )
}

const MediaCard = (props) => {
  const userToken = useSelector((state)=>state.user.userToken)
  const mediaInformationInitialState = {
    s3Key: props.mediaInformation.s3Key,
    documentId: props.mediaInformation.documentId,
    fileType: props.mediaInformation.fileType,
    documentTypeId: props.mediaInformation.documentTypeId,
    documentType: props.mediaInformation.documentType,
    documentUploadTime: props.mediaInformation.documentUploadTime,
    fileName: props.mediaInformation.documentName ? props.mediaInformation.documentName : props.mediaInformation.fileName,
    url: props.mediaInformation.s3Key ? BASE_URL + "/media-key?url=" + props.mediaInformation.s3Key : undefined
  }
  const [mediaInformation, setMediaInformation] = useState(mediaInformationInitialState)

  useEffect(() => {
    if (props.mediaInformation.fileType === undefined) {
      if (props.mediaInformation.documentId) {
        loadMediaInformation(userToken, props.mediaInformation.documentId, mediaInformation, setMediaInformation)
      }
    }
  }, [mediaInformation.documentId, mediaInformation.s3Key])

  return (
    <div>
      <Card key={props.key} bodyStyle={props.styleContainer.mediaCardBodyStyle} style={props.styleContainer.mediaCardStyle}>
        <MediaPlayer
          // mediaInformation={mediaInformation}
          enablePreviewScroll={props.mediaInformationArray === undefined ? false : true}
          styleContainer={props.styleContainer}
          enablePreview={props.enablePreview}
          previewRelatedInformation={props.previewRelatedInformation}
          mediaInformationArray={props.mediaInformationArray === undefined ? [mediaInformation] : props.mediaInformationArray}
          mediaInformationArrayIndex={props.mediaInformationArrayIndex === undefined ? 0 : props.mediaInformationArrayIndex}
        />
        {/* {renderMediaPlayer(mediaInformation, props.styleContainer, props.enablePreview, props.previewRelatedInformation)} */}
        {mediaInformation.documentType ? (
          <div>{mediaInformation.documentType}</div>
        ) : null}
        {mediaInformation.documentUploadTime || props.enableViewInBrowser || props.enableDownload ? (
          <div style={{display: 'flex', flexDirection: 'row', justifyContent: 'space-between'}}>
            {mediaInformation.documentUploadTime ? (
              <div style={{marginRight:'auto'}}>{formatedDate(mediaInformation.documentUploadTime)}</div>
            ) : null}
            {props.enableViewInBrowser === true ? (
              <div style={{marginLeft: 'auto', marginRight:'5px'}}><ExportOutlined onClick={() => launchInBrowser(mediaInformation)}/></div>
            ) : null}
            {props.enableDownload === true ? (
              <div><DownloadOutlined onClick={() => downloadHandler(mediaInformation)}/></div>
            ) : null}
          </div>
        ) : null}
        {props.enableUploadOfFiles === true ? (
          <UploadDocumentInKrushalOCFormControl
            uploadInformation={props.uploadInformation}
          />
        ) : null}
      </Card>
    </div>
  )
}

const MediaPreview = ({mediaInformationArray, mediaInformationArrayIndex, rotateInPreview=rotateInPreview, setRotateInPreview, rotateDegree, setRotateDegree}) => {
  const mediaType = getMediaTypeFromFileType(mediaInformationArray[mediaInformationArrayIndex].fileType)
  useEffect(() => {
    setRotateDegree(0)
  }, [mediaInformationArrayIndex])
  /* let mediaType = 'image'
  if (['video/mp4'].includes(mediaInformationArray[mediaInformationArrayIndex].fileType)) {
    mediaType = 'video'
  } else if (['application/pdf'].includes(mediaInformationArray[mediaInformationArrayIndex].fileType)) {
    mediaType = 'pdf'
  } */
  if (mediaType === 'image' || mediaType === 'video') {
    setRotateInPreview(true)
  } else {
    setRotateInPreview(false)
  }
  let elementHTML = null
  if (mediaInformationArray[mediaInformationArrayIndex].url) {
    if (mediaType === 'image') {
      let imageStyle = { maxWidth: '80vw', minWidth: '80vw', maxHeight: '80vh', minHeight: '80vh', overflowY: 'auto', objectFit: 'contain'}
      if (rotateDegree !== 0) {
        imageStyle = {transform:"rotate(" + rotateDegree + "deg)", ...imageStyle}
      }
      elementHTML = (
        <Image
            // class={isPreviewMode ? 'image-style-in-preview-mode' : 'overlay-image'}
            style={imageStyle}
            preview={false}
            height={'auto'}
            src={mediaInformationArray[mediaInformationArrayIndex].url}
            fallback={fallbackImageUrlForImage}
          />
      )
    } else if (mediaType === 'video') {
      let videoStyle = { maxWidth: '80vw', minWidth: '80vw', maxHeight: '80vh', minHeight: '80vh', overflowY: 'auto', objectFit: 'contain' }
      if (rotateDegree !== 0) {
        videoStyle = {transform:"rotate(" + rotateDegree + "deg)", transformOrigin: 'bottomLeft', ...videoStyle}
      }
      elementHTML = (
        <video
          style={videoStyle}
          // width="100%"
          controls
          src={mediaInformationArray[mediaInformationArrayIndex].url}
          />
      )
    } else if (mediaType === 'pdf') {
      elementHTML = (
        <Document
          file={mediaInformationArray[mediaInformationArrayIndex].url}
          // file='https://arxiv.org/pdf/quant-ph/0410100.pdf'
          // file='https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
          onLoadSuccess={(pageInformation) => onDocumentLoadSuccess(pageInformation, mediaInformationArray[mediaInformationArrayIndex])}
          onLoadError={(arg1, arg2) => {
            console.log('MC p r oLE 1, arg1 = ', arg1)
            console.log('MC p r oLE 2, arg2 = ', arg2)
          }}
          onRenderError={(arg1, arg2) => {
            console.log('MC p r oRE 1, arg1 = ', arg1)
            console.log('MC p r oRE 2, arg2 = ', arg2)
          }}
        >
          {(mediaInformationArray[mediaInformationArrayIndex].numberOfPages && mediaInformationArray[mediaInformationArrayIndex].numberOfPages > 0) ? (
            <div style={{overflowY: 'scroll', maxHeight: '80vh' }}>
              {Array.apply(null, Array(mediaInformationArray[mediaInformationArrayIndex].numberOfPages)).map((x, i) => i + 1).map(page => {
                return (
                  <Page
                    pageNumber={page}
                    renderAnnotationLayer={false}
                  />
                )
              })}
            </div>
          ) : (
            <Page
              pageIndex={0}
              renderAnnotationLayer={false}
            />
          )}
        </Document>
      )
    }
  }
  if (elementHTML !== null) {
    elementHTML = (
      <div style={{display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
        {elementHTML}
      </div>
    )
  }
  return elementHTML
}

const convertDocumentInServerResponseToMediaInformation = (documentInServerResponse) => {
  const mediaInformationInitialState = {
    s3Key: documentInServerResponse.document_information.url,
    documentId: documentInServerResponse.document_id,
    fileType: documentInServerResponse.document_information.fileType,
    documentTypeId: documentInServerResponse.document_type_id,
    documentType: documentInServerResponse.document_type,
    documentUploadTime: documentInServerResponse.document_upload_time,
    fileName: documentInServerResponse.document_name ? documentInServerResponse.document_name : documentInServerResponse.document_information.fileName,
    url: documentInServerResponse.document_information.url ? BASE_URL + "/media-key?url=" + documentInServerResponse.document_information.url : undefined
  }
  return mediaInformationInitialState
}

const convertServerResponseToMediaInformationArray = (serverResponse) => {
  const mediaInformationArray = []
  if (Array.isArray(serverResponse)) {
    serverResponse.forEach((documentInServerResponse, key) => {
      const mediaInformation = convertDocumentInServerResponseToMediaInformation(documentInServerResponse)
      mediaInformationArray.push(mediaInformation)
    })
  } else {
    [serverResponse].forEach((documentInServerResponse, key) => {
      const mediaInformation = convertDocumentInServerResponseToMediaInformation(documentInServerResponse)
      mediaInformationArray.push(mediaInformation)
    })
  }
  return mediaInformationArray  
}

const loadMediaAsync = async (userToken, mediaConfiguration, setMediaInformationArray, setInitialMediaInformationArray) => {
  try {
    console.log('MC lMA 0, mediaConfiguration = ', mediaConfiguration)
    if (mediaConfiguration.completeInformation) {
      console.log('MC lMA 1, mediaConfiguration = ', mediaConfiguration)
      const headers = {
        useCase: 'Get Document Information',
        token: userToken.accessToken, //authToken
        refreshtoken: localStorage.getItem("refreshToken")
      }
      const response = await getDocuments(headers, mediaConfiguration)
      console.log('MC lMA 2, response = ', response)
      
      if (response.data.return_code === 0) {
        const newMediaInformationArray = convertServerResponseToMediaInformationArray(response.data.result)
        if (setInitialMediaInformationArray) {
          setInitialMediaInformationArray(newMediaInformationArray)
        }
        setMediaInformationArray(newMediaInformationArray)
      } else {
        setMediaInformationArray([])
      }
    } else {
      setMediaInformationArray([])
    }
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const MediaDocumentFilterConfiguration = ({mediaConfiguration, setMediaInformationArray}) => {
  const userToken = useSelector((state)=>state.user.userToken)

  const [selectedDocumentedTypesForFilter, setSelectedDocumentedTypesForFilter] = useState([])
  const [latestOnly, setLatestOnly] = useState(mediaConfiguration.latestOnly === true ? true : false)
  return (
    <>
      {mediaConfiguration && mediaConfiguration.documentTypesForFilter && Array.isArray(mediaConfiguration.documentTypesForFilter) && mediaConfiguration.documentTypesForFilter.length > 0 ? (
        <div>
          <Select
            mode="multiple"
            allowClear
            style={{
              width: '300px',
              marginLeft: '10px'
            }}
            placeholder="Please select"
            onChange={(selectValues, selectedObjects) => {
              setSelectedDocumentedTypesForFilter(selectValues)
            }}
            options={mediaConfiguration.documentTypesForFilter}
          />
          <Button style={{
              marginLeft: '10px'
            }} onClick={() => {
              const newMediaConfiguration = {...mediaConfiguration, document_type_id_array: selectedDocumentedTypesForFilter, latestOnly: latestOnly}
              loadMediaAsync(userToken, newMediaConfiguration, setMediaInformationArray)
            }} >Refresh</Button>
          {
            mediaConfiguration.showLatestOnlyFlag === true ? (
              <Checkbox style={{
                marginLeft: '10px'
              }} checked={latestOnly} onChange={() => {
                setLatestOnly(!latestOnly)
              }} disabled={mediaConfiguration.enableLatestOnlyFlag === true ? undefined : true}>Latest Only</Checkbox>
            ) : null
          }          
        </div>
      ): null}
    </>
  )
}

const MediaCarousel = (props) => {
  const userToken = useSelector((state)=>state.user.userToken)
  const [mediaInformationArray, setMediaInformationArray] = useState(props.mediaInformationArray ? props.mediaInformationArray : [])
  const [initialMediaInformationArray, setInitialMediaInformationArray] = useState([])

  useEffect(() => {
    loadMediaAsync(userToken, props.mediaConfiguration, setMediaInformationArray, setInitialMediaInformationArray)
  }, [props.mediaConfiguration])

  const reloadDocuments = () => {
    loadMediaAsync(userToken, props.mediaConfiguration, setMediaInformationArray)
  }

  return (
    <>
      {(initialMediaInformationArray && Array.isArray(initialMediaInformationArray) && initialMediaInformationArray.length > 0) ? (
        <>
          <MediaDocumentFilterConfiguration
            mediaConfiguration={props.mediaConfiguration}
            setMediaInformationArray={setMediaInformationArray}
          />    
          <div style={{overflowX: 'scroll', display: 'flex', flexWrap: 'nowrap'}}>
            {mediaInformationArray.map((mediaInformation, index) => {
              return (
                <MediaCard
                  key={props.key + "-media-" + index}
                  mediaInformation={mediaInformation}
                  enablePreview={props.mediaConfiguration.enablePreview}
                  enableDownload={props.mediaConfiguration.enableDownload}
                  enableViewInBrowser={props.mediaConfiguration.enableViewInBrowser}
                  styleContainer={props.mediaConfiguration.styleContainer}
                  mediaInformationArray={mediaInformationArray}
                  mediaInformationArrayIndex={index}
                />
              )
            })}
          </div>
        </>
      ) : null}
      {props.mediaConfiguration && props.mediaConfiguration.enableUploadOfFiles && props.mediaConfiguration.documentTypesForUpload && Array.isArray(props.mediaConfiguration.documentTypesForUpload) && props.mediaConfiguration.documentTypesForUpload.length > 0 ? (
        <UploadDocumentInCarousel
          uploadInformation={{
            uploadMultiple: props.mediaConfiguration.uploadMultiple,
            uploadMaxCount: props.mediaConfiguration.uploadMaxCount,
            uploadFolder: props.mediaConfiguration.uploadFolder,
            uploadEntity: props.mediaConfiguration.uploadEntity,
            uploadEntityInformation: props.mediaConfiguration.uploadEntityInformation,
            uploadEntityDocumentInformation: props.mediaConfiguration.uploadEntityDocumentInformation
          }}
          documentTypesForUpload={props.mediaConfiguration.documentTypesForUpload}
          reloadDocuments={reloadDocuments}
        />
      ) : null}
    </>
  )
}

export {
  MediaCard,
  MediaCarousel
}