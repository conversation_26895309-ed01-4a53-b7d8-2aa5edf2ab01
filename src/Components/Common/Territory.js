import React, { useEffect, useState, createRef, useRef } from "react"
import { useSelector } from 'react-redux'

import {
  KrushalOCForm,
  assignDataToFormState,
  validateFormElements,
  validateFormElements2,
  assignValuesFromControlReferencesToFormState,
  extractChangedValues,
  assignReferencesAsListToForm,
} from './KrushalOCForm';

const lodashObject = require("lodash")

const { getGeographyData } = require('../../router')

const getTerritoryData = async (userToken, stateIdArray, districtIdArray, talukIdArray, villageIdArray) => {
  try {
    const headers = {
      useCase: 'Get Territory Data',
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken")
    }
    const queryParams = {
      stateIdArray: stateIdArray,
      districtIdArray: districtIdArray,
      talukIdArray: talukIdArray,
      villageIdArray: villageIdArray
    }
    const response = await getGeographyData(headers, queryParams)
    return response.data
  } catch (error) {
    console.log('SA lR 10, error')
    console.log('SA lR 10, error = ', error)
    throw error
  }
}

const _territoryFormDefinition = {
  formControls: {
    _state: {
      ocFormRef: createRef(),
      // type: "single-select-l10n_2",
      // label: "BCO State",
      labelKeyInList: "state_name_l10n",
      valueKeyInList: "state_id",
      // placeHolderText: "Select Farmer State",
      // errorMessage: "BCO State is mandatory",
      // validations: [{ type: "Mandatory" }, { type: "regex", regexArray: ["PositiveDecimal"] }],
      onSetValue2: async (
        stateIdList,
        additionalParams,
      ) => {
        const additionalParamsFinal = additionalParams && Array.isArray(additionalParams) && additionalParams.length > 0 ? additionalParams[0] : {}
        const {userToken, _territoryFormState, _setTerritoryFormState,
        } = additionalParamsFinal
        const updatedFormState = assignDataToFormState(
          _territoryFormDefinition,
          {village_id: [], village_id_list: [], taluk_id: [], taluk_id_list: [], district_id: [], district_id_list: [], state_id: [], state_id_list: []},
          _territoryFormState,
          _setTerritoryFormState
        )
        additionalParamsFinal['_territoryFormState'] = updatedFormState
        const response = await getTerritoryData(userToken, stateIdList, [], [], [])
        const updatedState = updateTerritoryFormStateBasedOnInputs(response, additionalParamsFinal)
      }, 
    },
    _district: {
      ocFormRef: createRef(),
      // type: "single-select-l10n_2",
      // label: "BCO District",
      labelKeyInList: "district_name_l10n",
      valueKeyInList: "district_id",
      /* placeHolderText: "Select BCO District",
      errorMessage: "BCO District is mandatory", */
      // validations: [{ type: "Mandatory" }, { type: "regex", regexArray: ["PositiveDecimal"] }],
      onSetValue2: async (
        districtIdList,
        additionalParams,
      ) => {
        const additionalParamsFinal = additionalParams && Array.isArray(additionalParams) && additionalParams.length > 0 ? additionalParams[0] : {}
        const {userToken, _territoryFormState, _setTerritoryFormState,
        } = additionalParamsFinal
        const updatedFormState = assignDataToFormState(
          _territoryFormDefinition,
          {village_id: [], village_id_list: [], taluk_id: [], taluk_id_list: [], district_id: [], district_id_list: [], state_id: [], state_id_list: []},
          _territoryFormState,
          _setTerritoryFormState
        )
        additionalParamsFinal['_territoryFormState'] = updatedFormState
        const response = await getTerritoryData(userToken, [], districtIdList, [], [])
        const updatedState = updateTerritoryFormStateBasedOnInputs(response, additionalParamsFinal)
      }, 
    },
    _taluk: {
      ocFormRef: createRef(),
      // type: "single-select-l10n_2",
      /* label: "BCO Taluk",
      placeHolderText: "Select BCO Taluk",
      errorMessage: "Farmer BCO is mandatory", */
      labelKeyInList: "taluk_name_l10n",
      valueKeyInList: "taluk_id",
      onSetValue2: async (
        talukIdList,
        additionalParams,
      ) => {
        const additionalParamsFinal = additionalParams && Array.isArray(additionalParams) && additionalParams.length > 0 ? additionalParams[0] : {}
        const {userToken, _territoryFormState, _setTerritoryFormState,
        } = additionalParamsFinal
        const updatedFormState = assignDataToFormState(
          _territoryFormDefinition,
          {village_id: [], village_id_list: [], taluk_id: [], taluk_id_list: [], district_id: [], district_id_list: [], state_id: [], state_id_list: []},
          _territoryFormState,
          _setTerritoryFormState
        )
        additionalParamsFinal['_territoryFormState'] = updatedFormState
        const response = await getTerritoryData(userToken, [], [], talukIdList, [])
        const updatedState = updateTerritoryFormStateBasedOnInputs(response, additionalParamsFinal)
      },
      // validations: [{ type: "Mandatory" }, { type: "regex", regexArray: ["PositiveDecimal"] }],
    },
    _village: {
      ocFormRef: createRef(),
      // type: "single-select-l10n_2",
      /* label: "Village",
      placeHolderText: "BCO Village",
      errorMessage: "Farmer BCO is mandatory", */
      labelKeyInList: "village_name_l10n",
      valueKeyInList: "village_id",
      // validations: [{ type: "Mandatory" }],
    },
  },
  visibleFormControls: [
    "_state",
    "_district",
    "_taluk",
    "_village",
  ],
  elementToDataMapping: {
    _state: 'state_id',
    _district: 'district_id',
    _taluk: 'taluk_id',
    _village: 'village_id',
  },
  dataToElementMapping: {
    state_id: '_state',
    district_id: '_district',
    taluk_id: '_taluk',
    village_id: '_village',
  },
}

const updateFormConfigurationBasedOnConfiguration = (configuration) => {
  const configurationArgument = typeof configuration === 'object' ? configuration : {}
  const {
    showStates, showDistricts, showTaluks, showVillages,
    stateLabel, districtLabel, talukLabel, villageLabel,
    statePlaceholderText, districtPlaceholderText, talukPlaceholderText, villagePlaceholderText,
    stateErrorMessage, districtErrorMessage, talukErrorMessage, villageErrorMessage,
    enableMultipleStates, enableMultipleDistricts, enableMultipleTaluks, enableMultipleVillages,
    readOnlyStates, readOnlyDistricts, readOnlyTaluks, readOnlyVillages,
  } = configurationArgument
  const geographyTypes = ['state', 'district', 'taluk', 'village']
  const geographyFormControlKeys = ['_state', '_district', '_taluk', '_village']
  const capitalizedPluralGeographyTypes = ['States', 'Districts', 'Taluks', 'Villages']
  const keysIntoConfiguration = ['label', 'placeHolderText', 'errorMessage']
  const configurationPrefixes = ['Label', 'PlaceHolderText', 'ErrorMessage']

  geographyTypes.map((geography, index) => {
    const formControlKey = geographyFormControlKeys[index]
    for (let counter = 0; counter < configurationPrefixes.length ; counter++) {
      const keyIntoConfiguration = geography + configurationPrefixes[counter]
      if (configurationArgument[keyIntoConfiguration] !== undefined && configurationArgument[keyIntoConfiguration] !== null && configuration[keyIntoConfiguration] !== '') {
        _territoryFormDefinition.formControls[formControlKey][keysIntoConfiguration[counter]] = configurationArgument[keyIntoConfiguration]
      }
    }
    const dropdownControlKey = 'enableMultiple' + capitalizedPluralGeographyTypes[index]
    _territoryFormDefinition.formControls[formControlKey].type = (configurationArgument[dropdownControlKey] === true) ? 'multi-select-l10n_2' : 'single-select-l10n_2'
  })
}

const updateTerritoryFormStateBasedOnInputs = (incomingData, stateFunctionsInObject) => {
  const {_territoryFormState, _setTerritoryFormState } = stateFunctionsInObject
  const updatedTerritoryFormState = lodashObject.cloneDeep(_territoryFormState)
  const valueToAssignToState = {
  }

  if (incomingData.selectedStateIdArray) {
    valueToAssignToState['state_id'] = incomingData.selectedStateIdArray
  }
  if (incomingData.stateArray && Array.isArray(incomingData.stateArray)) {
    valueToAssignToState['state_id_list'] = incomingData.stateArray
  }
  if (incomingData.selectedDistrictIdArray) {
    valueToAssignToState['district_id'] = incomingData.selectedDistrictIdArray
  }
  if (incomingData.districtArray && Array.isArray(incomingData.districtArray)) {
    valueToAssignToState['district_id_list'] = incomingData.districtArray
  }
  if (incomingData.selectedTalukIdArray) {
    valueToAssignToState['taluk_id'] = incomingData.selectedTalukIdArray
  }
  if (incomingData.talukArray && Array.isArray(incomingData.talukArray)) {
    valueToAssignToState['taluk_id_list'] = incomingData.talukArray
  }
  if (incomingData.selectedVillageIdArray) {
    valueToAssignToState['village_id'] = incomingData.selectedVillageIdArray
  }
  if (incomingData.villageArray && Array.isArray(incomingData.villageArray)) {
    valueToAssignToState['village_id_list'] = incomingData.villageArray
  }
  const updatedFormState = assignDataToFormState(
    _territoryFormDefinition,
    valueToAssignToState,
    _territoryFormState,
    _setTerritoryFormState
  )
  /* let villageIdsInVillageArray = []
  let talukIdsInVillageArray = []
  let districtIdsInVillageArray = []
  let stateIdsInVillageArray = []
  if (incomingData.villageArray && Array.isArray(incomingData.villageArray)) {
    for (const village of incomingData.villageArray) {
      if (village['village_id']) {
        villageIdsInVillageArray.push(village['village_id'])
      }
      if (village['taluk_id']) {
        talukIdsInVillageArray.push(village['taluk_id'])
      }
      if (village['district_id']) {
        districtIdsInVillageArray.push(village['district_id'])
      }
      if (village['state_id']) {
        stateIdsInVillageArray.push(village['state_id'])
      }
    }
  }
  _setVillageIdsInVillageArray(villageIdsInVillageArray)
  _setTalukIdsInVillageArray(talukIdsInVillageArray)
  _setDistrictIdsInVillageArray(talukIdsInVillageArray)
  _setStateIdsInVillageArray(stateIdsInVillageArray)

  let talukIdsInTalukArray = []
  let districtIdsInTalukArray = []
  let stateIdsInTalukArray = []
  if (incomingData.talukArray && Array.isArray(incomingData.talukArray)) {
    for (const taluk of incomingData.talukArray) {
      if (taluk['taluk_id']) {
        talukIdsInTalukArray.push(taluk['taluk_id'])
      }
      if (taluk['district_id']) {
        districtIdsInTalukArray.push(taluk['district_id'])
      }
      if (taluk['state_id']) {
        stateIdsInTalukArray.push(taluk['state_id'])
      }
    }
  }
  _setTalukIdsInTalukArray(talukIdsInTalukArray)
  _setDistrictIdsInTalukArray(districtIdsInTalukArray)
  _setStateIdsInTalukArray(stateIdsInTalukArray)


  let districtIdsInDistrictArray = []
  let stateIdsInDistrictArray = []
  if (incomingData.districtArray && Array.isArray(incomingData.districtArray)) {
    for (const district of incomingData.districtArray) {
      if (district['district_id']) {
        districtIdsInDistrictArray.push(district['district_id'])
      }
      if (district['state_id']) {
        stateIdsInDistrictArray.push(district['state_id'])
      }
    }
  }
  _setDistrictIdsInDistrictArray(districtIdsInDistrictArray)
  _setStateIdsInDistrictArray(stateIdsInDistrictArray)


  let stateIdsInStateArray = []
  if (incomingData.stateArray && Array.isArray(incomingData.stateArray)) {
    for (const state of incomingData.stateArray) {
      if (state['state_id']) {
        stateIdsInStateArray.push(state['state_id'])
      }
    }
  }
  _setStateIdsInStateArray(stateIdsInDistrictArray) */
}

const Territory = React.forwardRef((
  /* {
    configuration,
    selectedStateIdArray, selectedDistrictIdArray, selectedTalukIdArray, selectedVillageIdArray,
    stateArray, districtArray, talukArray, villageArray,
  } */props, ref) => {
  const {
    configuration,
    selectedStateIdArray, selectedDistrictIdArray, selectedTalukIdArray, selectedVillageIdArray,
    stateArray, districtArray, talukArray, villageArray,
  } = props
  const incomingConfiguration = typeof configuration === 'object' ? configuration : {}
  const {
    showStates, showDistricts, showTaluks, showVillages,
    stateLabel, districtLabel, talukLabel, villageLabel,
    statePlaceholderText, districtPlaceholderText, talukPlaceholderText, villagePlaceholderText,
    stateErrorMessage, districtErrorMessage, talukErrorMessage, villageErrorMessage,
    enableMultipleStates, enableMultipleDistricts, enableMultipleTaluks, enableMultipleVillages,
    readOnlyStates, readOnlyDistricts, readOnlyTaluks, readOnlyVillages,
  } = incomingConfiguration
  updateFormConfigurationBasedOnConfiguration(incomingConfiguration)

  const userToken = useSelector((state) => state.user.userToken)
  /* const appGroups = useSelector(state => state.user.appGroups)
  if (appGroups.includes(EXEC_TEAM)) {
    kmReportTableConfiguration.enableTableExport = true
    kmReportTableConfiguration.enableTableExportFromCloud = true
  } */

  
  const [_loading, _setLoading] = useState(props.loading === false ? false : true)
  const [_checkConfiguration, _setCheckConfiguration] = useState(-1)
  const [_territoryFormState, _setTerritoryFormState] = useState({})
  
  // const [_villageIdsInVillageArray, _setVillageIdsInVillageArray] = useState([])
  // const [_talukIdsInVillageArray, _setTalukIdsInVillageArray] = useState([])
  // const [_districtIdsInVillageArray, _setDistrictIdsInVillageArray] = useState([])
  // const [_stateIdsInVillageArray, _setStateIdsInVillageArray] = useState([])
  // const [_talukIdsInTalukArray, _setTalukIdsInTalukArray] = useState([])
  // const [_districtIdsInTalukArray, _setDistrictIdsInTalukArray] = useState([])
  // const [_stateIdsInTalukArray, _setStateIdsInTalukArray] = useState([])
  // const [_districtIdsInDistrictArray, _setDistrictIdsInDistrictArray] = useState([])
  // const [_stateIdsInDistrictArray, _setStateIdsInDistrictArray] = useState([])
  // const [_stateIdsInStateArray, _setStateIdsInStateArray] = useState([])

  const additionalParamsAsObject = {userToken, _territoryFormState, _setTerritoryFormState}
  const additionalParams = [additionalParamsAsObject]

  /* const validateInitialConfigurationAndData = () => {
    let validConfiguration = true
    const validatingSelectedVillageIdArray = selectedVillageIdArray && Array.isArray(selectedVillageIdArray) ? selectedVillageIdArray : []
    const validatingSelectedTalukIdArray = selectedTalukIdArray && Array.isArray(selectedTalukIdArray) ? selectedTalukIdArray : []
    const validatingSelectedDistrictIdArray = selectedDistrictIdArray && Array.isArray(selectedDistrictIdArray) ? selectedDistrictIdArray : []
    const validatingSelectedStateIdArray = selectedStateIdArray && Array.isArray(selectedStateIdArray) ? selectedStateIdArray : []
    if (validatingSelectedVillageIdArray.length > 0 && (validatingSelectedTalukIdArray.length > 0 || validatingSelectedDistrictIdArray.length > 0 || validatingSelectedStateIdArray.length > 0)) {
      validConfiguration = false
    }
    if (validatingSelectedTalukIdArray.length > 0 && (validatingSelectedDistrictIdArray.length > 0 || validatingSelectedStateIdArray.length > 0)) {
      validConfiguration = false
    }
    if (validatingSelectedDistrictIdArray.length > 0 && (validatingSelectedStateIdArray.length > 0)) {
      validConfiguration = false
    }
    if (validatingSelectedVillageIdArray.length > 0) {
      // ensure selected village ids are part of village array. also, identify the necessary taluks
      for (const selectedVillageId of validatingSelectedVillageIdArray) {
        if (_villageIdsInVillageArray.length > 0 && !_villageIdsInVillageArray.includes(selectedVillageId)) {
          validConfiguration = false
        }
      }
    }
    if (validatingSelectedTalukIdArray.length > 0) {
      // ensure selected village ids are part of village array. also, identify the necessary taluks
      for (const selectedTalukId of validatingSelectedTalukIdArray) {
        if (_talukIdsInVillageArray.length > 0 && !_talukIdsInVillageArray.includes(selectedTalukId)) {
          validConfiguration = false
        }
        if (_talukIdsInTalukArray.length > 0 && !_talukIdsInTalukArray.includes(selectedTalukId)) {
          validConfiguration = false
        }
      }
    }
    if (validatingSelectedDistrictIdArray.length > 0) {
      // ensure selected village ids are part of village array. also, identify the necessary taluks
      for (const selectedDistrictId of validatingSelectedDistrictIdArray) {
        if (_districtIdsInVillageArray.length > 0 && !_districtIdsInVillageArray.includes(selectedDistrictId)) {
          validConfiguration = false
        }
        if (_districtIdsInTalukArray.length > 0 && !_districtIdsInTalukArray.includes(selectedDistrictId)) {
          validConfiguration = false
        }
        if (_districtIdsInDistrictArray.length > 0 && !_districtIdsInDistrictArray.includes(selectedDistrictId)) {
          validConfiguration = false
        }
      }
    }
    if (validatingSelectedStateIdArray.length > 0) {
      // ensure selected village ids are part of village array. also, identify the necessary taluks
      for (const selectedStateId of validatingSelectedStateIdArray) {
        if (_stateIdsInVillageArray.length > 0 && !_stateIdsInVillageArray.includes(selectedStateId)) {
          validConfiguration = false
        }
        if (_stateIdsInTalukArray.length > 0 && !_stateIdsInTalukArray.includes(selectedStateId)) {
          validConfiguration = false
        }
        if (_stateIdsInDistrictArray.length > 0 && !_stateIdsInDistrictArray.includes(selectedStateId)) {
          validConfiguration = false
        }
        if (_stateIdsInStateArray.length > 0 && !_stateIdsInStateArray.includes(selectedStateId)) {
          validConfiguration = false
        }
      }
    }
    if (!validConfiguration) {
      throw new Error('Invalid configuration')
    }
  } */

  const loadTerritoryRelatedData = async () => {
    const selectedStateIdArray = props.selectedStateIdArray && Array.isArray(props.selectedStateIdArray) ? props.selectedStateIdArray : []
    const selectedDistrictIdArray = props.selectedDistrictIdArray && Array.isArray(props.selectedDistrictIdArray) ? props.selectedDistrictIdArray : []
    const selectedTalukIdArray = props.selectedTalukIdArray && Array.isArray(props.selectedTalukIdArray) ? props.selectedTalukIdArray : []
    const selectedVillageIdArray = props.selectedVillageIdArray && Array.isArray(props.selectedVillageIdArray) ? props.selectedVillageIdArray : []
    const territoryDataFromServer = await getTerritoryData(userToken, selectedStateIdArray, selectedDistrictIdArray, selectedTalukIdArray, selectedVillageIdArray)
    updateTerritoryFormStateBasedOnInputs(territoryDataFromServer, {_territoryFormState, _setTerritoryFormState})
  }

  useEffect(() => {
    const asyncUseEffect = async () => {
      if (_checkConfiguration !== -1) {
        // validateInitialConfigurationAndData()
        // how to show data
        // if more than 1 state, show district name with state short code prefix, taluk name with state and district short code prefix, village name with state, district and taluk short code prefix
        // if 1 state, but more than 1 district, show taluk name with district short code prefix, village name with district and taluk short code prefix
        // if 1 state, 1 district, but more than 1 taluk, show village name with taluk short code prefix
    
        // what data to show
        // if showStates is false, then show district with state code prefix
        // if showStates is false and showDistricts is false, then show taluk with state and district code prefix
        // if showStates is true and showDistricts is false, then show taluk with district code prefix
        // if showStates is false and showDistricts is false and showTaluks is false, then show village with state, district and taluk code prefix
        // if showStates is true and showDistricts is false and showTaluks is false, then show village with state, district and taluk code prefix
        // ... this needs to be thought through
    
        // what data to load
        // if village_ids, get all states, and stateids of village ids, get districts of stateids, and district ids of village ids, get taluks of district ids, and taluk id of village, get all villages of taluk ids
        // else if taluk ids, get all states, and state ids of taluk ids, get districts of state ids, and district ids of taluk ids, and get taluks of district ids, get villages of taluk ids
        // else if district ids, get all states, and state ids of district ids, get districts of state ids, get taluks and of district ids
        // else if states ids, get all states, and district ids of state ids
        await loadTerritoryRelatedData()
      }  
    }
    asyncUseEffect()
  }, [_checkConfiguration])

  useEffect(() => {
    const asyncUseEffect = async () => {
      if (props.loading === false) {
        updateTerritoryFormStateBasedOnInputs(props, { _territoryFormState, _setTerritoryFormState})
        if (_checkConfiguration === 0) {
          _setCheckConfiguration(1)
        } else {
          _setCheckConfiguration(0)
        }
      }
    }
    asyncUseEffect()
  }, [
    props.loading,
    props.selectedStateIdArray,
    props.selectedDistrictIdArray,
    props.selectedTalukIdArray,
    props.selectedVillageIdArray
  ])

  const getSelectedGeography = () => {
    const returnValue = {return_code: 0}
    const selectedStateIdArray = _territoryFormState['_state'] && _territoryFormState['_state'].value ? _territoryFormState['_state'].value : undefined
    const selectedDistrictIdArray = _territoryFormState['_district'] && _territoryFormState['_district'].value ? _territoryFormState['_district'].value : undefined
    const selectedTalukIdArray = _territoryFormState['_taluk'] && _territoryFormState['_taluk'].value ? _territoryFormState['_taluk'].value : undefined
    const selectedVillageIdArray = _territoryFormState['_village'] && _territoryFormState['_village'].value ? _territoryFormState['_village'].value : undefined
    if (selectedStateIdArray === undefined && selectedDistrictIdArray === undefined && selectedTalukIdArray === undefined && selectedVillageIdArray === undefined) {
      returnValue.return_code = -101
    }
    returnValue.selectedStateIdArray = selectedStateIdArray
    returnValue.selectedDistrictIdArray = selectedDistrictIdArray
    returnValue.selectedTalukIdArray = selectedTalukIdArray
    returnValue.selectedVillageIdArray = selectedVillageIdArray
    return returnValue
  }
  
  React.useImperativeHandle(ref, () => ({
    getSelectedGeography
  }))

  return (
    <>      
      <KrushalOCForm
        formState={_territoryFormState}
        setFormState={_setTerritoryFormState}
        formDefinition={_territoryFormDefinition}
        additionalParams={additionalParams}
      />    
    </>
  )
})

export {
  Territory
}