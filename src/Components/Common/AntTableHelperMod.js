import moment from 'moment';
import { Input, InputNumber, Space, Button, Select, DatePicker, Table,Menu, Checkbox, Dropdown, Modal } from "antd";
import { SearchOutlined, FilterFilled, MenuOutlined } from '@ant-design/icons';
import { tableCellClasses, tableContainerClasses} from '@mui/material';

const { RangePicker } = DatePicker
const { Option } = Select;

var timeOutId=undefined;

const DisplayColumns = (props) => {
  // props.tableConfiguration.setFilteredColumns(props.tableConfiguration.columns)
  const dropdownHandler = (value) => {
    props.tableConfiguration.setFilteredColumnsAnt((prevState) => {
      if (prevState.includes(value)) {
        return prevState.filter((col) => col !== value);
      } else {
        return [...prevState, value];
      }
    });
  };

  const showModal = () => {
    props.tableConfiguration.setIsModalOpen(true);
  };

  const handleCancel = () => {
    props.tableConfiguration.setIsModalOpen(false);
  };

  const handleDragStart = (e, index) => {
    e.dataTransfer.setData('itemIndex', index.toString());  // Convert index to string

  };

  // const handleDrop = (e, index) => {
  //   const movingItemIndex = e.dataTransfer.getData('itemIndex');
  //   const updatedItems = [...props.tableConfiguration.columns];
  //   const movingItem = updatedItems[movingItemIndex];
  //   updatedItems.splice(movingItemIndex, 1);
  //   updatedItems.splice(index, 0, movingItem);
  //   props.tableConfiguration.setFilteredColumns(updatedItems);
  // };
  console.log(props.tableConfiguration.columns)
  const handleDrop = (e, index) => {
    // Convert the itemIndex from a string back to a number
    const movingItemIndex = Number(e.dataTransfer.getData('itemIndex'));
  
    // Create a copy of the columns array
    const mapDataa =  props.tableConfiguration.columns
    const updatedItems = [...mapDataa];
  
    // Get the item that is being moved
    const movingItem = updatedItems[movingItemIndex];
  
    // Remove the moving item from its original position
    updatedItems.splice(movingItemIndex, 1);
  
    // Insert the moving item at the new position
    updatedItems.splice(index, 0, movingItem);
  
    // Update the state with the new columns array
    props.tableConfiguration.setFilteredColumns(updatedItems);
  };
  
  const handleDragEnter = (e, index) => {
    const movingItemIndex = Number(e.dataTransfer.getData('itemIndex'));  // Convert string back to number
    // ... rest of your code ...
    if (movingItemIndex !== index) {
      handleDrop(e, index);
    }
  };
  const mapData = props.tableConfiguration.filteredColumns.length > 0 ? props.tableConfiguration.filteredColumns : props.tableConfiguration.columns;
  const menu = () => {
    return (
      <ul style={{listStyle:"none"}}>
          {mapData.map((item, index) => (
        <li 
          key={index}
          draggable
          onDragStart={(e) => handleDragStart(e, index)}
          onDragOver={(e) => e.preventDefault()}
          // onDragEnter={(e) => handleDragEnter(e, index)}
          onDrop={(e) => handleDrop(e, index)}
          style={{cursor:"move",boxShadow:' rgba(0, 0, 0, 0.35) 0px 5px 15px',padding:"10px",marginBottom:"5px"}}
        >  
            <Checkbox
              defaultChecked={!mapData?.includes(item.title)}
              onChange={(e) => {
                dropdownHandler(item.title);
              }}
            >
              {item.title}
            </Checkbox>
            </li>
          ))}
      </ul>
    );
  }
  return (
    <div>
      <Button onClick={showModal}>Open</Button>
      <Modal title="Basic Modal" open={props.tableConfiguration.isModalOpen} onCancel={handleCancel}>
        {menu()}
      </Modal>
    </div>
  );
};

const OCTable = (props) => {
  return (
    <div>
      <DisplayColumns tableConfiguration={props.tableConfiguration}/>
    <Table
      key={props.tableKey}
      columns={props.columns}
      dataSource={props.reportData}
      rowKey={props.tableConfiguration.rowKey}
      rowSelection={enableSelectionIfNeeded(props.tableConfiguration, props.selectedRowKeys, props.setSelectedRowKeys)}
      pagination={enablePaginationIfNeeded(props.tableConfiguration)}
      onChange={enableOnChangeIfNeeded(props.tableConfiguration)}
      scroll={props.tableConfiguration.enableHorizontalScrolling ? { x: true } : undefined}
      />
      </div>
  )  
}

const getColumnHeadingText = (tableConfiguration, column) => {
  if (tableConfiguration.columnConfiguration[column] && tableConfiguration.columnConfiguration[column].headingText) {
    return tableConfiguration.columnConfiguration[column].headingText
  } else {
    return column
  }
}

const isTableSortingAvailable = (tableConfiguration) => {
  let returnValue = false
  if (tableConfiguration && tableConfiguration.sorting && tableConfiguration.sorting.columnKey) {
    if (tableConfiguration.sorting.columnKey !== 'ignoreColumn') {
      returnValue = true
    }
  }
  return returnValue
}

const areTableFiltersAvailable = (tableConfiguration) => {
  let returnValue = false
  if (tableConfiguration && tableConfiguration.columnFilters && Object.keys(tableConfiguration.columnFilters).length > 0) {
    const potentialFilterColumns = Object.keys(tableConfiguration.columnFilters)
    potentialFilterColumns.every((column, index) => {
      const columnFilterValue = tableConfiguration.columnFilters[column]
      if (columnFilterValue !== null && columnFilterValue !== undefined) {
        returnValue = true
        return false
      }
      return true
    })
  }
  return returnValue
}

const isFilterEnabled = (columnConfiguration, isPageLevel) => {
  if (isPageLevel) {
    return (columnConfiguration && columnConfiguration.allowFilterInPageParams && columnConfiguration.allowSettingFilterFromPageParams !== true)
  } else {
    return (columnConfiguration && columnConfiguration.enableFilter)
  }
}

const getColumnTypeAttribute = (columnConfiguration, isPageLevel) => {
  if (isPageLevel && columnConfiguration.allowFilterInPageParams && columnConfiguration.allowSettingFilterFromPageParams !== true) {
    return {isPageLevel: true, columnType: columnConfiguration.paramSearchType, columnDataType: columnConfiguration.paramSearchDataType}
  } else {
    return {isPageLevel: false, columnType: columnConfiguration.type, columnDataType: columnConfiguration.dataType}
  }
}

const displayColumnLevelFilterValue = (tableConfiguration, column, isPageLevel) => {
  // console.log('ATH dCLFV 1, column = ', column, ', isPageLevel = ', isPageLevel)
  let returnValue = null
  const columnConfiguration = tableConfiguration.columnConfiguration[column]
  
  // console.log('ATH dCLFV 2, columnConfiguration = ', columnConfiguration)
  if (isFilterEnabled(columnConfiguration, isPageLevel)) {
    const columnFilterConfiguration = getColumnTypeAttribute(columnConfiguration, isPageLevel)
    // console.log('ATH dCLFV 3, columnFilterConfiguration = ', columnFilterConfiguration)
    returnValue = isPageLevel ? tableConfiguration.pageParams.filterParams[column] : tableConfiguration.columnFilters[column]
    if (columnFilterConfiguration.columnType === 'number_range') {
      if (Array.isArray(returnValue) && returnValue.length > 0) {
        const filterOrRangeValues = returnValue[0]
        if (Array.isArray(filterOrRangeValues) && filterOrRangeValues.length >= 2) {
          if (filterOrRangeValues[0] === undefined && filterOrRangeValues[1] !== undefined) {
            returnValue = 'Less than or equal to ' + filterOrRangeValues[1]
          } else if (filterOrRangeValues[0] !== undefined && filterOrRangeValues[1] === undefined) {
            returnValue = 'Greater than or equal to ' + filterOrRangeValues[0]
          } else if (filterOrRangeValues[0] !== undefined && filterOrRangeValues[1] !== undefined) {
            returnValue = 'Between ' + filterOrRangeValues[0] + ' and ' + filterOrRangeValues[1]
          }
        }
      }
    } else if (['single_select_array', 'default_array'].includes(columnFilterConfiguration.columnType)) {
      if (Array.isArray(returnValue) && returnValue.length > 0) {
        let columnIndexIntoFilterArray  = column + '_filter_values'
        if (columnConfiguration.filterValuesKey) {
          columnIndexIntoFilterArray = columnConfiguration.filterValuesKey
        }
        const filterValues = tableConfiguration.filterValuesByKey[columnIndexIntoFilterArray]
        if (filterValues && Array.isArray(filterValues)) {
          let filterTextToDisplay = ''
          filterValues.map(filterValue => {
            if (returnValue.includes(filterValue.value)) {
              if (filterTextToDisplay !== '') {
                filterTextToDisplay = filterTextToDisplay + ', '
              }
              filterTextToDisplay = filterTextToDisplay + filterValue.text
            }
          })
          if (returnValue.length > 1 && filterTextToDisplay !== '') {
            filterTextToDisplay = 'In ' + filterTextToDisplay    
          }
          returnValue = filterTextToDisplay    
        }
      }
    } else if (columnFilterConfiguration.columnType === 'array') {
      if (returnValue.length > 1) {
        returnValue = 'In ' + returnValue.join(', ')
      } else {
        returnValue = returnValue[0]
      }
    } else if (columnFilterConfiguration.columnType === 'dayjs_date_till') {
      returnValue = 'Till ' + moment(returnValue[0].toDate()).format('DD MMM YYYY')
    }
  }
  return returnValue
}

const getColumnLevelFilter = (tableConfiguration, isPageLevel) => {
  const columnFilters = isPageLevel ? tableConfiguration.pageParams.filterParams : tableConfiguration.columnFilters
  const returnValue = Object.keys(columnFilters).map(column => {
    return (columnFilters[column] !== null && column !== 'ignoreColumn') ? (
      <div>
        {getColumnHeadingText(tableConfiguration, column)}: {displayColumnLevelFilterValue(tableConfiguration, column, isPageLevel)}
      </div>
    ) : null
  })
  return returnValue
}

const arePageLevelFiltersAvailable = (tableConfiguration) => {
  let returnValue = false
  if (tableConfiguration && tableConfiguration.pageParams && tableConfiguration.pageParams.filterParams) {
    returnValue = true
  }
  return returnValue
}

const OCTableHeader = (props) => {
  // console.log('ATH OCTH 1, props = ', props)
  return (
    <div>
      {arePageLevelFiltersAvailable(props.tableConfiguration) ? (
        <div>
          Page Level Filter: 
          {getColumnLevelFilter(props.tableConfiguration, true)}
          <div></div>
        </div>
      ) : null}
      {(props && props.tableConfiguration && props.tableConfiguration.pageParams && props.tableConfiguration.pageParams.globalSearch) ? (
        <div>
        Page Search:
        </div>
      ) : null}
      {areTableFiltersAvailable(props.tableConfiguration) ? (
        <div>
        Filters:
          {getColumnLevelFilter(props.tableConfiguration)}
        </div>
      ) : null}
      {isTableSortingAvailable(props.tableConfiguration) ? (
        <div>
        Sort:
          <div>
            {getColumnHeadingText(props.tableConfiguration,props.tableConfiguration.sorting.columnKey)}: {props.tableConfiguration.sorting.order}
          </div>
        </div>
        ) : null}
    </div>
  )  
}

const initialLoad = async (tableConfiguration) => {
  const modifiedFilterValuesByKey = await initializeFilterData(tableConfiguration)
  const defaultSortConfiguration = constructSortCriteriaBasedOnDefaultValues(tableConfiguration)
  const defaultFilterConfiguration = constructFilterCriteriaBasedOnDefaultValues(tableConfiguration)
  const pageLevelFilterParams = (tableConfiguration.pageParams && tableConfiguration.pageParams.filterParams) ? tableConfiguration.pageParams.filterParams : {}
  const pageLevelSortParams = (tableConfiguration.pageParams && tableConfiguration.pageParams.sortParams) ? tableConfiguration.pageParams.sortParams : {}
  const finalPageLevelFiterParams = {...pageLevelFilterParams, ...defaultFilterConfiguration}
  const finalInitialSortParams = {...defaultSortConfiguration, ...pageLevelSortParams}
  tableConfiguration.setSorting(finalInitialSortParams)
  tableConfiguration.setColumnFilters(finalPageLevelFiterParams)
  await fetchData(tableConfiguration, {current:1, pageSize: tableConfiguration.disablePagination === true ? - 1 : tableConfiguration.defaultPageSize}, finalPageLevelFiterParams, finalInitialSortParams, null, modifiedFilterValuesByKey)
}

const reloadDataAndMoveToPageOne = async (tableConfiguration) => {
  await fetchData(tableConfiguration, {current:1, pageSize: tableConfiguration.disablePagination === true ? - 1 : tableConfiguration.defaultPageSize})
}

const reset = (tableConfiguration) => {
  tableConfiguration.setTableKey(tableConfiguration.tableKey + 1)
  tableConfiguration.setColumnFilters({})
  tableConfiguration.setSorting({})
  // document.getElementById('searchInput').value = ""
  let searchInput = document.getElementById('searchInput'); if (searchInput) { searchInput.value = "" }
  tableConfiguration.setGlobalSearch('')
  const defaultSortConfiguration = constructSortCriteriaBasedOnDefaultValues(tableConfiguration)
  fetchData(tableConfiguration, {current:1, pageSize: tableConfiguration.disablePagination === true ? - 1 : tableConfiguration.defaultPageSize}, {ignoreColumn: ['ABC']}, defaultSortConfiguration, null)
}

// const handleTableChange = (page, pageSize) => {
const handleTableChange = (paginationParams, filterParams, sortParams, tableConfiguration) => {
  if (Object.keys(paginationParams).length > 0) {
    tableConfiguration.setCurrentPage(paginationParams.current)
    tableConfiguration.setPageSize(paginationParams.pageSize)
  }
  tableConfiguration.setSorting(sortParams)
  tableConfiguration.setColumnFilters(filterParams)
  fetchData(tableConfiguration, paginationParams, filterParams, sortParams)
}

const fetchData = async (tableConfiguration, paginationParams, filterParams, sortParams, globalSearchParam, modifiedFilterValuesByKey) => {
  const reportQueryParams = { 
    searchConfiguration: tableConfiguration.columnConfiguration,
  }

  if (tableConfiguration.pageParams) {
    reportQueryParams.pageParams = tableConfiguration.pageParams
  }

  if (tableConfiguration.getAllDataAtOnce === true) {
    reportQueryParams.start = -1
  } else if (paginationParams && Object.keys(paginationParams).length > 0) {
    reportQueryParams.start = (paginationParams.current-1)*paginationParams.pageSize
  } else {
    reportQueryParams.start = (tableConfiguration.currentPage-1)*tableConfiguration.pageSize
  }

  if (paginationParams && Object.keys(paginationParams).length > 0) {
    reportQueryParams.size = paginationParams.pageSize
  } else {
    reportQueryParams.size = tableConfiguration.pageSize
  }

  if (filterParams && Object.keys(filterParams).length > 0) {
    reportQueryParams.filters = filterParams
  } else {
    reportQueryParams.filters = tableConfiguration.columnFilters
  }

  if (globalSearchParam === null) {
    reportQueryParams.globalSearch = ''
  } else if (globalSearchParam && globalSearchParam !== '') {
    reportQueryParams.globalSearch = globalSearchParam
  } else {
    reportQueryParams.globalSearch = tableConfiguration.globalSearch
  }

  if (sortParams && Object.keys(sortParams).length > 0) {
    reportQueryParams.sorting = sortParams
  } else {
    reportQueryParams.sorting = tableConfiguration.sorting
  }

  try {
    const reportResponse = await tableConfiguration.loadReport(tableConfiguration.userToken, reportQueryParams)
    transformData(tableConfiguration, reportResponse.report)
    tableConfiguration.setReportData(reportResponse.report);
    tableConfiguration.setReportRowCount(reportResponse.count);
    updateFilterStateObject(tableConfiguration, tableConfiguration.filterValuesByKey, tableConfiguration.setFilterValuesByKey, reportResponse, modifiedFilterValuesByKey)
  } catch (error) {
    console.error(error);
    throw error
  }
}

// const initializeFilterData = async (tableConfiguration) => {
//   try {
//     // const headers = createHeaders(-1, 'Get Complaint Report', authToken)
//     const response = await tableConfiguration.loadFilters(tableConfiguration.userToken)
//     console.log(response)
//     if (response.return_code === 0) {
//       transformData(tableConfiguration, response.tableFilters)
//       const modifiedFilterValuesByKey = updateFilterStateObject(tableConfiguration, tableConfiguration.filterValuesByKey, tableConfiguration.setFilterValuesByKey, response.tableFilters)
//       return modifiedFilterValuesByKey
//     } else {
//       return undefined
//     }
//   } catch (error) {
//     console.error(error);
//     throw error
//   }
// }
const initializeFilterData = async (tableConfiguration) => {
  try {
    // const headers = createHeaders(-1, 'Get Complaint Report', authToken)
    const response = await tableConfiguration.loadFilters(tableConfiguration.userToken)
    if (response.return_code === 0) {
      transformData(tableConfiguration, response)
      const modifiedFilterValuesByKey = updateFilterStateObject(tableConfiguration, tableConfiguration.filterValuesByKey, tableConfiguration.setFilterValuesByKey, response)
      return modifiedFilterValuesByKey
    } else {
      return undefined
    }
  } catch (error) {
    console.error(error);
    throw error
  }
}

const omniSearch = async(tableConfiguration) => {
  const searchValue = document.getElementById('searchInput').value
  tableConfiguration.setGlobalSearch(searchValue)
  fetchData(tableConfiguration, {}, {}, {}, searchValue)
}

const transformData = (tableConfiguration, data) => {
  if (data === undefined) {
    return
  }
  if (tableConfiguration.dataTransformationRules) {
    const keysToReplace = Object.keys(tableConfiguration.dataTransformationRules)
    if (Array.isArray(data)) {
      data.forEach((elementInArray, index) => {
        keysToReplace.forEach((keyToReplace, index2) => {
          if (elementInArray[keyToReplace]) {
            const keyToReplaceWith = tableConfiguration.dataTransformationRules[keyToReplace]
            elementInArray[keyToReplaceWith] = elementInArray[keyToReplace]
            delete elementInArray[keyToReplace]
          }
        })
      })
    } else {
      keysToReplace.forEach((keyToReplace, index2) => {
        const elementInArray = data
        if (elementInArray[keyToReplace]) {
          const keyToReplaceWith = tableConfiguration.dataTransformationRules[keyToReplace]
          elementInArray[keyToReplaceWith] = elementInArray[keyToReplace]
          delete elementInArray[keyToReplace]
        }
      })
    }
  }
}

const enableSelectionIfNeeded = (tableConfiguration, selectedRowKeys, setSelectedRowKeys) => {
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys);
    },
    // You can customize the selection behavior, such as disallowing some rows to be selected
    getCheckboxProps: (record) => ({
      // Example: Disable selection for rows with a specific condition
      disabled: record.someCondition === true,
    }),
  }
  if (tableConfiguration.enableSelection) {
    return rowSelection
  } else {
    return undefined
  }
}

const enablePaginationIfNeeded = (tableConfiguration) => {
  if (tableConfiguration.disablePagination) {
    return false
  } else {
    return {
      total: tableConfiguration.getAllDataAtOnce === true ? undefined : tableConfiguration.reportRowCount, // Set the number of rows per page,
      current: tableConfiguration.getAllDataAtOnce === true ? undefined : tableConfiguration.currentPage,
      pageSize: tableConfiguration.pageSize,
      showSizeChanger: true
      // Handle page number change
    }
  }
}

const enableOnChangeIfNeeded = (tableConfiguration) => {
  if (tableConfiguration.getAllDataAtOnce) {
    return undefined
  } else {
    return (arg1, arg2, arg3) => handleTableChange(arg1, arg2, arg3, tableConfiguration)
  }
}

const updateFilterStateObject = (tableConfiguration, filterValuesByKey, setFilterValuesByKey, response, modifiedFilterValuesByKeyParam) => {
  const modifiedFilterValuesByKey = modifiedFilterValuesByKeyParam ? modifiedFilterValuesByKeyParam : {...filterValuesByKey}
  if (response === undefined) {
    return modifiedFilterValuesByKey
  }
  const filterKeys = Object.keys(response)
  let modifiedFlag = false
  filterKeys.forEach((individualFilterKey, index) => {
    if (individualFilterKey.endsWith('_filter_values')) {
      modifiedFilterValuesByKey[individualFilterKey] = response[individualFilterKey]
      modifiedFlag = true
    }
  })
  if (modifiedFlag) {
    setFilterValuesByKey(modifiedFilterValuesByKey)
  }
  return modifiedFilterValuesByKey
}

const getPageLevelFilterColumnAndOrder = () => {  
  return 
}

const setUpColumnConfiguration = (tableConfiguration, columnDataIndex, indexIntoColumnConfiguration) => {
  const pageLevelFilterParams = (tableConfiguration.pageParams && tableConfiguration.pageParams.filterParams) ? tableConfiguration.pageParams.filterParams : {}
  const pageLevelSortParams = (tableConfiguration.pageParams && tableConfiguration.pageParams.sortParams) ? tableConfiguration.pageParams.sortParams : {}
  const returnValue = {
  }
  const columnInformation = tableConfiguration.columns[indexIntoColumnConfiguration]
  if (columnInformation.key === undefined) {
    returnValue.key = columnDataIndex
  }
  const column = columnInformation.key ? columnInformation.key : columnInformation.dataIndex

  const columnConfiguration = tableConfiguration.columnConfiguration[column]
  const pageLevelFilterColumnConfiguration = pageLevelFilterParams[column]
  if (columnConfiguration && columnConfiguration.enableSort === true) {
    if (tableConfiguration.getAllDataAtOnce === true) {
      if (columnConfiguration.sortColumnType === 'date') {
        returnValue.sorter = (a, b) => {
          if (a[column] !== undefined && b[column] !== undefined && a[column] !== null && b[column] !== null) {
            const dateA = new Date(a[column])
            const dateB = new Date(b[column])
            return dateA - dateB
          } else {
            return true
          }
        }
      } else if (['int'].includes(columnConfiguration.sortColumnType)) {
        returnValue.sorter = (a, b) => {
          if (a[column] !== undefined && b[column] !== undefined && a[column] !== null && b[column] !== null) {
            return (a[column] - b[column])
          } else {
            return false
          }
        }
      } else if (['string'].includes(columnConfiguration.sortColumnType)) {
        returnValue.sorter = (a, b) => {
          if (a[column] !== undefined && b[column] !== undefined && a[column] !== null && b[column] !== null) {
            return a[column].localeCompare(b[column])
          } else {
            return true
          }
          
        }
      }
    } else {
      returnValue.sorter = true
    }
    returnValue.sortDirections = ['ascend', 'descend']
  }
  if (pageLevelSortParams.columnKey && pageLevelSortParams.columnKey === column) {
    returnValue.defaultSortOrder = pageLevelSortParams.order
  }
  if (Object.keys(pageLevelSortParams).length === 0 && columnConfiguration && columnConfiguration.defaultSort) {
    returnValue.defaultSortOrder = columnConfiguration.defaultSort
  }
  if (columnConfiguration && columnConfiguration.enableFilter && columnConfiguration.type !== 'default_array') {
    returnValue.filterIcon = (filtered) => filterIconFunction(column, tableConfiguration, filtered)
    returnValue.filterDropdown = (object) => onFilterDropdownFunction(object, column, tableConfiguration)
    returnValue.onFilter = onFilterFunction(column, tableConfiguration)
  }
  if (columnConfiguration && columnConfiguration.enableFilter && columnConfiguration.allowSettingFilterFromPageParams && pageLevelFilterColumnConfiguration) {
    returnValue.defaultFilteredValue = pageLevelFilterColumnConfiguration
  }
  if (columnConfiguration && columnConfiguration.enableFilter && columnConfiguration.defaultFilteredValue) {
    returnValue.defaultFilteredValue = columnConfiguration.defaultFilteredValue
  }
  if (columnConfiguration && columnConfiguration.type === 'default_array' && tableConfiguration.filterValuesByKey) {
    if (tableConfiguration.filterValuesByKey[column + '_filter_values']) {
      returnValue.filters = tableConfiguration.filterValuesByKey[column + '_filter_values']
    } else if (columnConfiguration.filters) {
      returnValue.filters = columnConfiguration.filters
    }
    returnValue.onFilter = onFilterFunction(column, tableConfiguration)
    console.log(returnValue)
  }
  if (columnConfiguration && columnConfiguration.formatType) {

    if (columnConfiguration.formatType === 'date') {
      returnValue.render = (date) => {
        let returnValue = date
        if (date instanceof Date || (date !== undefined && date !== null)) {
          returnValue = moment(date).format(columnConfiguration.format)
        }
        return returnValue
      }
    }
    if (columnConfiguration.formatType === 'double' && columnConfiguration.format.decimalPlaces) {
      returnValue.render = (floatValueAsString) => {
        if (floatValueAsString === undefined || floatValueAsString === null) {
          return floatValueAsString
        }
        if (floatValueAsString.constructor.name === 'String') {
          const floatValue = parseFloat(floatValueAsString)
          return floatValue.toFixed(1)
        } else {
          return floatValueAsString
        }
      }
    }
    if (columnConfiguration.formatType === 'months_since') {
      returnValue.render = (date) => {
        let returnValue = date
        if (date instanceof Date || (date !== undefined && date !== null)) {
          // returnValue = moment(date).format(columnConfiguration.format)
          returnValue = moment().diff(moment(date), 'months')
        }
        return returnValue
      }
    }
    if (columnConfiguration.formatType === 'hyperLink') {
      returnValue.render = (urlLink) => {
        let returnValue = urlLink
        if (urlLink !== undefined && urlLink !== null && urlLink !== '') {
          // returnValue = moment(date).format(columnConfiguration.format)
          return (
            <a href={urlLink} target="_blank" rel="noopener noreferrer">
            {columnConfiguration.headingText}
            </a>
          )
        }
        return returnValue
      }
    }
  }
  if (columnConfiguration && columnConfiguration.hiddenColumn === true) {
    console.log('ATH sUCC 1, hiding column ', column)
    returnValue.hidden = true
  }
  return returnValue
  // const {
    
  //   filterDropdown: (object) => onFilterDropdownFunction(object, column, tableConfiguration, tableConfiguration.resetPaginationPageNumberFunction),
  //   onFilter: (value, record) => onFilterFunction(value, record, column, tableConfiguration)
  // }
}

const delayInMillis = (millisec) => { 
  return new Promise(resolve => { 
      setTimeout(() => { resolve('') }, millisec); 
  }) 
}

const constructFilterCriteriaBasedOnDefaultValues = (tableConfiguration) => {
  const returnValue = {ignoreColumn: ['ABC']}
  const columnConfiguration = tableConfiguration.columnConfiguration
  const columns = Object.keys(columnConfiguration)
  columns.forEach((column, index) => {
    if (columnConfiguration[column].defaultFilteredValue) {
      returnValue[column] = columnConfiguration[column].defaultFilteredValue
    }
  })
  return returnValue
}

const constructSortCriteriaBasedOnDefaultValues = (tableConfiguration) => {
  const returnValue = {columnKey: 'ignoreColumn', order: 'ascend'}
  const columnConfiguration = tableConfiguration.columnConfiguration
  const columns = Object.keys(columnConfiguration)
  columns.every((column, index) => {
    if (columnConfiguration[column].defaultSort) {
      returnValue.columnKey = column
      returnValue.order = columnConfiguration[column].defaultSort
      return false
    }
    return true
  })
  return returnValue
}

const validateTableConfiguration = (tableConfiguration) => {
  let returnValue = 0
  const columnsConfiguration = tableConfiguration.columnConfiguration
  const columns = Object.keys(columnsConfiguration)
  let numberOfRowsWithDefaultSortOrder = 0
  columns.forEach((column, index) => {
    const columnConfiguration = columnsConfiguration[column]
    if (columnConfiguration.defaultSort) {
      numberOfRowsWithDefaultSortOrder++
    }
  })
  if (numberOfRowsWithDefaultSortOrder > 2) {
    returnValue--
  }

  let validSortColumnsAndDataTypesWhenAllDataIsGotAtOnce = true
  if (tableConfiguration.getAllDataAtOnce) {
    columns.forEach((column, index) => {
      const columnConfiguration = columnsConfiguration[column]
      if (columnConfiguration.enableSort) {
        if (!['date'].includes(columnConfiguration.sortColumnType)) {
          validSortColumnsAndDataTypesWhenAllDataIsGotAtOnce = false
        }
      }
    })
  }
  if (!validSortColumnsAndDataTypesWhenAllDataIsGotAtOnce) {
    returnValue--
  }

  if (tableConfiguration.enableSelection) {
    if (!tableConfiguration.rowKey) {
      returnValue--
    }
  }
  if (returnValue < 0) {
    throw new Error('Invalid form format')
  }
}

const filterIconFunction = (field, tableConfiguration, filtered) => {
  const columnConfiguration = tableConfiguration.columnConfiguration[field]
  if (columnConfiguration && columnConfiguration.enableFilter === true) {
    if (['string', 'number'].includes(columnConfiguration.type)) {
      return (
        <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
      )
    } else if (['number_range', 'dayjs_range', 'dayjs_date_till'].includes(columnConfiguration.type)) {
      return (
        <Button icon={<FilterFilled />} style={{ marginLeft: 8, color: filtered ? '#1890ff' : undefined }} />
      )
    } else if (['single_select_array'].includes(columnConfiguration.type) ) {
      return (
        <Button icon={<FilterFilled />} style={{ marginLeft: 8 }} />
      )
    }
  }
}

const onFilterFunction = (column, tableConfiguration) => {
  const columnConfiguration = tableConfiguration.columnConfiguration[column]
  if (columnConfiguration && columnConfiguration.type === 'string') {
    const stringFunction = (value, record) => {
      return (record[column]?.toLowerCase()?.includes(value?.toLowerCase()))
    }
    return stringFunction
  } else {
    if (!tableConfiguration.getAllDataAtOnce) {
      const nonStringFunction = (value, record) => {
        return true
      }
      return nonStringFunction
    } else if (columnConfiguration && columnConfiguration.type === 'number') {
      const intFunction = (value, record) => {
        if (value === null || record[column] == null || value === undefined || record[column] === undefined) {
          return false
        } else {
          return Number(record[column]) === Number(value)
        }
      }
      return intFunction
    } else if (columnConfiguration && columnConfiguration.type === 'dayjs_range') {
      const dateRangeFunction = (value, record) => {
        if (value === null || record[column] == null || value === undefined || record[column] === undefined || !Array.isArray(value)) {
          return false
        } else {
          const startRangeDayJS = value[0]
          const endRangeDayJS = value[1]
          const startDate = startRangeDayJS.toDate()
          const endDate = endRangeDayJS.toDate()
          const recordAsDate = new Date(record[column])
          if (recordAsDate >= startDate && recordAsDate <= endDate) {
            return true
          } else {
            return false
          }
        }
      }
      return dateRangeFunction
    } else if (columnConfiguration && columnConfiguration.type === 'dayjs_date_till') {
      const dateFunction = (value, record) => {
        if (value === null || record[column] == null || value === undefined || record[column] === undefined || !Array.isArray(value)) {
          return false
        } else {
          return false
        }
      }
      return dateFunction
    } else if (columnConfiguration && columnConfiguration.type === 'number_range') {
      const numberRangeFunction = (value, record) => {
        if (value === null || record[column] == null || value === undefined || record[column] === undefined || !Array.isArray(value)) {
          return false
        } else {
          const minValue = value[0]
          const maxValue = value[1]
          const recordValue = record[column]
          if ((minValue === undefined || minValue == null) && (maxValue === undefined || maxValue === null)) {
            return false
          } else if (minValue !== undefined && minValue !== null && (maxValue === undefined || maxValue === null)) {
            if (recordValue >= minValue) {
              return true
            } else {
              return false
            }
          } else if (maxValue !== undefined && maxValue !== null && (minValue === undefined || minValue === null)) {
            if (recordValue <= maxValue) {
              return true
            } else {
              return false
            }
          } else if (minValue !== undefined && minValue !== null && maxValue !== undefined && maxValue !== null) {
            if (recordValue >= minValue && recordValue <= maxValue) {
              return true
            } else {
              return false
            }
          } else {
            return false
          }
        }
      }
      return numberRangeFunction
    } else if (columnConfiguration && ['default_array', 'single_select_array'].includes(columnConfiguration.type)) {
      let columnToIndex = column
      if (columnConfiguration.altColumn) {
        columnToIndex = columnConfiguration.altColumn
      }
      const defaultArrayFunction = (value, record) => {
        if (value === null || record[column] == null || value === undefined || record[column] === undefined) {
          return false
        } else {
          return String(record[columnToIndex]) === String(value)
        }
      }
      return defaultArrayFunction
    }
  }
}

const handleSetFilter = (selectedKeys, confirm, dataIndex, resetPaginationPageNumberFunction) => {
  console.log('hS 1, selectedKeys = ', selectedKeys)
  console.log('hS 2, confirm = ', confirm)
  console.log('hS 3, dataIndex = ', dataIndex)
  confirm()
  resetPaginationPageNumberFunction(1)
}

const handleResetFilter = (confirm, clearFilters, dataIndex, resetPaginationPageNumberFunction) => {
  console.log('hR 1, clearFilters = ', clearFilters)
  console.log('hR 2, dataIndex = ', dataIndex)
  clearFilters()
  confirm()
  resetPaginationPageNumberFunction(1)
  // setData(dataSource);
}

const onFilterDropdownFunction = (object, field, tableConfiguration) => {
  const resetPaginationPageNumberFunction = tableConfiguration.resetPaginationPageNumberFunction
  const columnConfiguration = tableConfiguration.columnConfiguration[field]
  const searchConfiguration = tableConfiguration.columnConfiguration
  if (searchConfiguration && field && searchConfiguration[field] && searchConfiguration[field].type) {
    const { setSelectedKeys, selectedKeys, confirm, clearFilters } = object
    if (searchConfiguration[field].type === 'string') {

      return (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Search Name"
            value={selectedKeys[0]}
            onChange={(e) => {
              if (timeOutId !== undefined) {
                clearInterval(timeOutId)
              }
              setSelectedKeys(e.target.value ? [e.target.value] : [])
              debounce(selectedKeys, confirm, field, resetPaginationPageNumberFunction)
          }}
            onPressEnter={() => handleSetFilter(selectedKeys, confirm, field, resetPaginationPageNumberFunction)}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Space>
            {/* <Button
              type="primary"
              onClick={() => handleSetFilter(selectedKeys, confirm, field, resetPaginationPageNumberFunction)}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              Search
            </Button> */}
            {/* <Button onClick={() => handleResetFilter(confirm, clearFilters, field, resetPaginationPageNumberFunction)} size="small" style={{ width: 90 }}>
              Reset
            </Button> */}
          </Space>
        </div>
      )
    } else if (searchConfiguration[field].type === 'number_range') {
      return (
        <div style={{ padding: 8 }}>
          <Space>
            <InputNumber
              placeholder="Enter min number"
              value={selectedKeys.length > 0 ? selectedKeys[0][0] : undefined}
              // onChange={(e, arg2) => {
              //   let minValue, maxValue
              //   if (selectedKeys.length > 0) {
              //     minValue = selectedKeys[0][0]
              //     maxValue = selectedKeys[0][1]
              //   }
              //   setSelectedKeys(e ? [[e, maxValue]] : [[undefined, maxValue]])
              // }}
              onChange={(e, arg2) => {
                console.log('selectedKeys = ', selectedKeys);
                if (selectedKeys && selectedKeys[0]) {
                  let minValue = selectedKeys[0][0];
                  let maxValue = selectedKeys[0][1];
                  if (e) {
                    // Update minValue only if e is defined
                    minValue = e;
                  }
                  setSelectedKeys([[minValue, maxValue]]);
                } else {
                  // Handle the case when selectedKeys or selectedKeys[0] is not defined
                  setSelectedKeys([[e, undefined]]);
                }
              }}
              // onPressEnter={() => handleSetFilter(selectedKeys, confirm, field, setCurrentPage)}
              style={{ width: 188, marginBottom: 8, display: 'block' }}
            />
            <InputNumber
              placeholder="Enter max number"
              value={selectedKeys.length > 0 ? selectedKeys[0][1] : undefined}
              // onChange={(e, arg2) => {
              //   let minValue, maxValue
              //   if (selectedKeys.length > 0) {
              //     minValue = selectedKeys[0][0]
              //     maxValue = selectedKeys[0][1]
              //   }
              //   setSelectedKeys(e ? [[minValue, e]] : [[minValue, undefined]])
              // }}
              onChange={(e, arg2) => {
                let minValue, maxValue
                if (selectedKeys.length > 0) {
                  minValue = selectedKeys[0][0]
                  maxValue = selectedKeys[0][1]
                }
                // handle the case when user entered 0 
                setSelectedKeys(e !== null && e !== undefined ? [[minValue, e]] : [[minValue, undefined]])
              }}
              // onPressEnter={() => handleSetFilter(selectedKeys, confirm, field, setCurrentPage)}
              style={{ width: 188, marginBottom: 8, display: 'block' }}
            />
          </Space>
          <Space>
            <Button
              type="primary"
              onClick={() => handleSetFilter(selectedKeys, confirm, field, resetPaginationPageNumberFunction)}
              size="small"
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button onClick={() => handleResetFilter(confirm, clearFilters, field, resetPaginationPageNumberFunction)} size="small" style={{ width: 90 }}>
              Reset
            </Button>
          </Space>
        </div>
      )
    } else if (searchConfiguration[field].type === 'dayjs_range') {
      return (
        <div style={{ padding: 8 }}>
          <RangePicker
            value={selectedKeys.length > 0 ? selectedKeys[0] : []}
            onChange={(dateRange) => {
              setSelectedKeys([dateRange])
            }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => handleSetFilter(selectedKeys, confirm, field, resetPaginationPageNumberFunction)}
              size="small"
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button onClick={() => handleResetFilter(confirm, clearFilters, field, resetPaginationPageNumberFunction)} size="small" style={{ width: 90 }}>
              Reset
            </Button>
          </Space>
        </div>
      )
    } else if (searchConfiguration[field].type === 'dayjs_date_till') {
      console.log('ATH oFDDF dd 1, selectedKeys = ', selectedKeys)
      return (
        <div style={{ padding: 8 }}>
          <DatePicker
            value={selectedKeys.length > 0 ? selectedKeys[0] : undefined}
            onChange={(date) => {
              console.log('ATH oFDDF dd 2, date = ', date)
              setSelectedKeys([date])
            }}
            placeholder="Select Date"
          />
          <Space>
            <Button
              type="primary"
              onClick={() => handleSetFilter(selectedKeys, confirm, field, resetPaginationPageNumberFunction)}
              size="small"
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button onClick={() => handleResetFilter(confirm, clearFilters, field, resetPaginationPageNumberFunction)} size="small" style={{ width: 90 }}>
              Reset
            </Button>
          </Space>
        </div>
      )
    } else if (searchConfiguration[field].type === 'number') {
      return (
        <div style={{ padding: 8 }}>
          <Space>
            <InputNumber
              placeholder="Enter a number"
              value={selectedKeys.length > 0 ? selectedKeys[0] : undefined}
              onChange={(e) => {
                setSelectedKeys(e ? [e] : [])
              }}
              onPressEnter={() => handleSetFilter(selectedKeys, confirm, field, resetPaginationPageNumberFunction)}
              style={{ width: 188, marginBottom: 8, display: 'block' }}
            />
          </Space>
          <Space>
            <Button
              type="primary"
              onClick={() => handleSetFilter(selectedKeys, confirm, field, resetPaginationPageNumberFunction)}
              size="small"
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button onClick={() => handleResetFilter(confirm, clearFilters, field, resetPaginationPageNumberFunction)} size="small" style={{ width: 90 }}>
              Reset
            </Button>
          </Space>
        </div>
      )
    } else if (searchConfiguration[field].type === 'single_select_array') {
      let filterValuesKey = field + '_filter_values'
      if (columnConfiguration && columnConfiguration.filterValuesKey) {
        filterValuesKey = columnConfiguration.filterValuesKey
      }
      if (tableConfiguration.filterValuesByKey[filterValuesKey]) {
        const singleSelectFilterArray = tableConfiguration.filterValuesByKey[filterValuesKey]
        return (
          <div style={{ padding: 8, width:150 }}>
            <Select
              style={{ width: '100%' }}
              mode="single"
              placeholder="Select one"
              value={selectedKeys[0]}
              onChange={(value) => {
                setSelectedKeys(value ? [value] : [])
                handleSetFilter(selectedKeys, confirm, field, resetPaginationPageNumberFunction)
              }}
            >
              {singleSelectFilterArray.map((singleSelectFilterValue) => (
                <Option key={singleSelectFilterValue.value} value={singleSelectFilterValue.value}>
                  {singleSelectFilterValue.text}
                </Option>
              ))}
            </Select>
            <Space>
              {/* <Button
                type="primary"
                onClick={() => handleSetFilter(selectedKeys, confirm, field, resetPaginationPageNumberFunction)}
                size="small"
                style={{ width: 90 }}
              >
                Search
              </Button> */}
              {/* <Button onClick={() => handleResetFilter(confirm, clearFilters, field, resetPaginationPageNumberFunction)} size="small" style={{ width: 90 }}>
                Reset
              </Button> */}
            </Space>
          </div>
        )
      }
      else {
        return null
      }
    }
  } else {
    return (<></>)
  }
}

const createPageParamsFromURLString = (tableConfiguration, searchParams) => {
  const pageParams = {}
  if (searchParams.get('filterParams')) {
    const uncompressedFilterParamsJsonString = decodeURIComponent(searchParams.get('filterParams'))// urlEncodedJson)
    const filterParams = JSON.parse(uncompressedFilterParamsJsonString)
    pageParams.filterParams = filterParams
    const pageParamKeys = Object.keys(pageParams.filterParams)
    pageParamKeys.forEach((key, index) => {
      if (tableConfiguration && tableConfiguration.columnConfiguration && tableConfiguration.columnConfiguration[key] && tableConfiguration.columnConfiguration[key].allowFilterInPageParams) {

      } else {
        delete pageParams.filterParams[key]
      }
    })
  }
  if (searchParams.get('sortParams')) {
    const uncompressedSortParamsJsonString = decodeURIComponent(searchParams.get('sortParams'))// urlEncodedJson)
    const sortParams = JSON.parse(uncompressedSortParamsJsonString)
    pageParams.sortParams = sortParams
  }
  return pageParams
}

const createURLQueryStringFromPageParams = (filterParams, sortParams) => {
  // console.log('ATH cUQSFPP 1')
  let returnQueryString = '' 
  // const filterParams = { customer_id: selectedRowKeys }; // Example query parameters
  if (filterParams && Object.keys(filterParams).length > 0) {
    // console.log('ATH cUQSFPP 2')
    const paramsAsString = JSON.stringify(filterParams)
    const encodedParamsAsString = encodeURIComponent(paramsAsString)
    if (returnQueryString !== '') {
      returnQueryString = returnQueryString + '&'
    }
    returnQueryString = returnQueryString + `filterParams=${encodedParamsAsString}`
    // console.log('ATH cUQSFPP 3, returnQueryString = ', returnQueryString)
  }
  if (sortParams && Object.keys(sortParams).length > 0) {
    // console.log('ATH cUQSFPP 4')
    const paramsAsString = JSON.stringify(sortParams)
    const encodedParamsAsString = encodeURIComponent(paramsAsString)
    if (returnQueryString !== '') {
      returnQueryString = returnQueryString + '&'
    }
    returnQueryString = returnQueryString + `sortParams=${encodedParamsAsString}`
    // console.log('ATH cUQSFPP 5, returnQueryString = ', returnQueryString)
  }
  // console.log('ATH cUQSFPP 6, returnQueryString = ', returnQueryString)
  return returnQueryString
}

const createColumnsFromTableConfiguration = (tableConfiguration) => {

  console.log(tableConfiguration)
  console.log(tableConfiguration.filteredColumns)
  const returnValueColumns = []
  const returnDndValueColumns = []
  if (tableConfiguration.columns && Array.isArray(tableConfiguration.columns)) {
    tableConfiguration.columns.forEach((columnConfiguration, index) => {
      const columnDataIndex = columnConfiguration.dataIndex
      const newColumnConfiguration = {...columnConfiguration, ...setUpColumnConfiguration(tableConfiguration, columnDataIndex, index)}
      returnValueColumns.push(newColumnConfiguration)
    })
  }
  if (tableConfiguration.filteredColumns.length > 0) {
    tableConfiguration.filteredColumns.forEach((columnConfiguration, index) => {
      const columnDataIndex = columnConfiguration.dataIndex
      const newColumnConfiguration = {...columnConfiguration, ...setUpColumnConfiguration(tableConfiguration, columnDataIndex, index)}
      returnDndValueColumns.push(newColumnConfiguration)
    })
  }
  console.log(returnDndValueColumns)
  // return returnValueColumns
  let dynamicColoumns = returnValueColumns.filter(col => !tableConfiguration.filteredColumnsAnt.includes(col.title));
  let dnd = returnDndValueColumns?.length > 0 ? returnDndValueColumns.filter(col => !tableConfiguration.filteredColumnsAnt.includes(col.title)) : returnValueColumns.filter(col => !tableConfiguration.filteredColumnsAnt.includes(col.title));
  console.log(dnd)
  return dnd
}

const onColumnConfigurationModalOK = (tableConfiguration) => {
  if (tableConfiguration && tableConfiguration.setColumnConfigurationModalDialogVisibility) {
    tableConfiguration.setColumnConfigurationModalDialogVisibility(false)
  }
}

const onColumnConfigurationModalCancel = (tableConfiguration) => {
  if (tableConfiguration && tableConfiguration.setColumnConfigurationModalDialogVisibility) {
    tableConfiguration.setColumnConfigurationModalDialogVisibility(false)
  }  
}

const handleConfigureColumnClick = (tableConfiguration) => {
  console.log('ATH hCCC 1')
  if (tableConfiguration && tableConfiguration.setColumnConfigurationModalDialogVisibility) {
    tableConfiguration.setColumnConfigurationModalDialogVisibility(true)
  }
}

function debounce( selectedKeys, confirm, field, resetPaginationPageNumberFunction){
 
  timeOutId = setTimeout(()=>{
    handleSetFilter(selectedKeys, confirm, field, resetPaginationPageNumberFunction)
  },1000)
  return timeOutId;
}
export { delayInMillis, validateTableConfiguration, setUpColumnConfiguration, initialLoad, omniSearch, reset, createPageParamsFromURLString, OCTable, OCTableHeader, createURLQueryStringFromPageParams, createColumnsFromTableConfiguration, onColumnConfigurationModalOK, onColumnConfigurationModalCancel, handleConfigureColumnClick, reloadDataAndMoveToPageOne }