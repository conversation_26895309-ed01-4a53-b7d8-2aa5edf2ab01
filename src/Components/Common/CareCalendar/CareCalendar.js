import { useMemo, useState, useCallback, useEffect } from "react";
import {useSelector, useDispatch} from "react-redux"
import axios from "axios";
import { BASE_URL, instance } from "../../../Services/api.service";
import { useNavigate } from "react-router-dom";
import { useParams } from "react-router-dom";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { Calendar, dateFnsLocalizer } from "react-big-calendar";
import format from "date-fns/format";
import parse from "date-fns/parse";
import startOfWeek from "date-fns/startOfWeek";
import getDay from "date-fns/getDay";
import "../../Dashboard/Cattle/CattleItem/CattleItem.css";
import ToastersService from "../../../Services/toasters.service";
import {clearUserToken} from "../../user/action"
import { extractBasedOnLanguageMod } from "../../../Utilities/Common/utils";

const locales = {
  "en-US": require("date-fns/locale/en-US"),
};
const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});

const CareCalendar = (props) => {
  const params = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userToken = useSelector((state)=>state.user.userToken);

  const [activities, setActivities] = useState([]);
  const [curData, setCurData] = useState([]);
  const [prevData, setPrevData] = useState([]);
  const [reproData, setReproData] = useState([]);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [collectionData, setCollectionData] = useState([]);
  const components = useMemo(() => ({
    event: Check,
  }));

  const eventColors = {
    1000270001: "#C5E1A5",
    1000270002: "#00C8F4",
    1000270003: "#A6C4FF",
    1000270004: "#CF9FFF",
  };

  const createCalendarEvent = (item, activityCategory) => ({
    calendarId: item.care_calendar_id,
    title: item.activity_name,
    titleJSON: item.activity_name_l10n,
    status: item.calendar_activity_status,
    entity_type_id: item.entity_type_id,
    start: new Date(
      new Date(item.activity_date).getFullYear(),
      new Date(item.activity_date).getMonth(),
      new Date(item.activity_date).getDate()
    ),
    end: new Date(
      new Date(item.activity_date).getFullYear(),
      new Date(item.activity_date).getMonth(),
      new Date(item.activity_date).getDate()
    ),
    eventColor: eventColors[item.activity_category],
  });

  const getCattleCareCalendar = async () => {
    try {
      // const response = await axios.get(
      //   BASE_URL + "/activity/care-calendar?animal_id=" + params.cattleId,
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //     },
      //   }
      // );
      // const response = await axios.post(
      //   BASE_URL +
      //     "/tasks/animal",
      //   { animal_id: params.cattleId },
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "123456789"
      //     },
      //   }
      // );
      const response = await instance({
        url: "/tasks/animal",
        method: "POST",
        data: { animal_id: params.cattleId },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });

      if (response.status === 201) {
        const curativeActivities = response.data
          .filter((item) => item.activity_category === 1000270002)
          .map((item) => createCalendarEvent(item));

        const preventiveActivities = response.data
          .filter((item) => item.activity_category === 1000270001)
          .map((item) => createCalendarEvent(item));

        const reproductiveActivities = response.data
          .filter((item) => item.activity_category === 1000270003)
          .map((item) => createCalendarEvent(item));

        const allActivities = response.data.map((item) =>
          createCalendarEvent(item)
        );

        setCurData(curativeActivities);
        setPrevData(preventiveActivities);
        setReproData(reproductiveActivities);
        setActivities(allActivities);
        console.log("cattle");
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate, "/login", "Session Expired!"))
      }
    }
  };

  const getFarmerCareCalendar = async () => {
    console.log("Called");
    try {
      // const response = await axios.post(
      //   BASE_URL +
      //     // "/activity/care-calendar?farmer_id=" + params.farmerId,
      //     "/tasks/farmer",
      //   { farmer_id: params.farmerId },
      //   {
      //     headers: {
      //       token: localStorage.getItem("accessToken"),
      //       "X-App-Id": "123456789"
      //     },
      //   }
      // );
      const response = await instance({
        url: "/tasks/farmer",
        method: "POST",
        data: { farmer_id: params.farmerId },
        headers: {
          "Content-Type": "application/json",
          token: userToken.accessToken,
        },
      });

      if (response.status === 201) {
        const curativeActivities = response.data
          .filter((item) => item.activity_category === 1000270002)
          .map((item) => createCalendarEvent(item));

        const preventiveActivities = response.data
          .filter((item) => item.activity_category === 1000270001)
          .map((item) => createCalendarEvent(item));

        const reproductiveActivities = response.data
          .filter((item) => item.activity_category === 1000270003)
          .map((item) => createCalendarEvent(item));

        const collectionActivities = response.data
          .filter((item) => item.activity_category === 1000270004)
          .map((item) => createCalendarEvent(item));

        const allActivities = response.data.map((item) =>
          createCalendarEvent(item)
        );

        setCurData(curativeActivities);
        setPrevData(preventiveActivities);
        setReproData(reproductiveActivities);
        setCollectionData(collectionActivities);
        setActivities(allActivities);
        console.log("farmer");
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        dispatch(clearUserToken(navigate,"/login", "Session Expired!"))
      }
    }
  }

  useEffect(() => {
    // console.log("care-calendar", window.location.pathname.split("/")[4]);
    // window.location.pathname.split("/")[4] == "cattle"
    //   ? getCattleCareCalendar()
    //   : getFarmerCareCalendar();
    console.log("props.type", props.type);
    if (props.type == 'farmer') {
      getFarmerCareCalendar();
    } else if (props.type == 'cattle') {
      getCattleCareCalendar();
    }
  }, []);

  const toggleFilter = (filterId) => {
    setSelectedFilters((filters) =>
      filters.includes(filterId)
        ? filters.filter((id) => id !== filterId)
        : [...filters, filterId]
    );
  };

  const filteredData = useMemo(() => {
    let result = [];

    if (selectedFilters == "") {
      result = result.concat(activities);
    }

    if (selectedFilters.includes("curative")) {
      result = result.concat(curData);
    }

    if (selectedFilters.includes("preventive")) {
      result = result.concat(prevData);
    }

    if (selectedFilters.includes("reproductive")) {
      result = result.concat(reproData);
    }

    if (selectedFilters.includes("dataCollection")) {
      result = result.concat(collectionData);
    }

    return result;
  }, [selectedFilters, curData, prevData, reproData, collectionData]);

  return (
    <>
      <div>
        {activities.length > 0 ? (
          <>
            <div className="filtersSection mt-4">
              <div className="d-flex justify-content-around">
                <div onClick={() => toggleFilter("curative")}>
                  <div
                    className="filterIcon"
                    style={{
                      backgroundColor: selectedFilters.includes("curative")
                        ? "#ec6237"
                        : "#2D3240",
                    }}
                  >
                    <i className="fa-solid fa-syringe"></i>
                  </div>
                  <p>Curative</p>
                </div>
                <div onClick={() => toggleFilter("preventive")}>
                  <div
                    className="filterIcon"
                    style={{
                      backgroundColor: selectedFilters.includes("preventive")
                        ? "#ec6237"
                        : "#2D3240",
                    }}
                  >
                    <i className="fa-solid fa-clipboard-check"></i>
                  </div>
                  <p>Preventive</p>
                </div>
                <div onClick={() => toggleFilter("reproductive")}>
                  <div
                    className="filterIcon"
                    style={{
                      backgroundColor: selectedFilters.includes("reproductive")
                        ? "#ec6237"
                        : "#2D3240",
                    }}
                  >
                    <i className="fa-solid fa-droplet"></i>
                  </div>
                  <p>Reproductive</p>
                </div>
                {window.location.pathname.split("/")[4] == "cattle" ? (
                  ""
                ) : (
                  <>
                    <div onClick={() => toggleFilter("dataCollection")}>
                      <div
                        className="filterIcon"
                        style={{
                          backgroundColor: selectedFilters.includes(
                            "dataCollection"
                          )
                            ? "#ec6237"
                            : "#2D3240",
                        }}
                      >
                        <i className="fa-solid fa-pen-to-square"></i>
                      </div>
                      <p>Collection</p>
                    </div>
                  </>
                )}
              </div>
            </div>
            <Calendar
              views={["month", "day"]}
              localizer={localizer}
              events={filteredData}
              startAccessor="start"
              endAccessor="end"
              style={{ height: 500 }}
              components={components}
              messages={{
                next: ">",
                previous: "<",
                today: "Today",
              }}
              eventPropGetter={(event) => ({
                style: {
                  backgroundColor: event.eventColor,
                },
              })}
            />
          </>
        ) : (
          <>
            <div className="text-center mt-5" style={{ color: "#4c4c4c" }}>
              <i className="fa fa-exclamation-triangle" aria-hidden="true"></i>{" "}
              Care Calendar not generated yet!
            </div>
          </>
        )}
      </div>
    </>
  );
};

const Check = (activities) => {
  const navigate = useNavigate();
  const calendarEventClickHandler = (calendarId) => {
    console.log("Clicked!", calendarId);
    navigate("/tasks/" + calendarId);
  };
  return (
    <>
      <div
        style={{ display: "flex", alignItems: "center" }}
        onClick={() => calendarEventClickHandler(activities.event.calendarId)}
      >
        <div className="calendar__status-box">
          {activities.event.status == 1000300001 && (
            <i className="fa fa-star" style={{ color: "yellow" }}></i>
          )}
          {activities.event.status == 1000300002 && 1}
          {activities.event.status == 1000300003 && 2}
          {activities.event.status == 1000300004 && 3}
          {activities.event.status == 1000300005 && 4}
          {activities.event.status == 1000300006 && 5}
          {activities.event.status == 1000300007 && 6}
          {activities.event.status == 1000300008 && 7}
          {activities.event.status == 1000300009 && 8}
          {activities.event.status == 1000300010 && 9}
          {activities.event.status == 1000300011 && 10}
          {activities.event.status == 1000300012 && 11}
          {activities.event.status == 1000300013 && 12}
          {activities.event.status == 1000300014 && 13}
          {activities.event.status == 1000300015 && 14}
          {activities.event.status == 1000300016 && 15}
          {activities.event.status == 1000300050 && (
            <i className="fa fa-check" style={{ color: "green" }}></i>
          )}
          {activities.event.status == 1000300051 && (
            <i className="fa fa-times" style={{ color: "red" }}></i>
          )}
          {activities.event.status == 1000300052 && (
            <i className="fa fa-exclamation" style={{ color: "purple" }}></i>
          )}
        </div>
        <div>
          {" "}
          {extractBasedOnLanguageMod(activities.event.titleJSON,"en")}
        </div>
      </div>
    </>
  );
};

export default CareCalendar;