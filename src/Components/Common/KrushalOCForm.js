import React, { useState, useEffect } from "react"
import { Select } from 'antd'
import TextField from "@mui/material/TextField";
import FormHelperText from "@mui/material/FormHelperText";
import { DesktopDatePicker } from "@mui/x-date-pickers/DesktopDatePicker";
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import Autocomplete from "@mui/material/Autocomplete";
import { <PERSON><PERSON>, Popup, MapContainer, TileLayer } from 'react-leaflet'
import { Input } from 'antd';
import { EnvironmentOutlined } from '@ant-design/icons';
import dayjs from 'dayjs'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
// import  './MapComponents.css'
import tileLayer from '../../Components/Dashboard/BgLocation/util/tileLayer'

const moment = require("moment");
const lodashArray = require("lodash");

const { TextArea } = Input;

const {
  extractBasedOnLanguage,
  assignL10NObjectToState,
  assignValueToL10NObject,
  extractLabelOrPlaceHolderTextAsPerLanguage,
  assignL10NObjectOrObjectArrayToItSelf,
} = require("@krushal-it/common-core")

const { MediaCard } = require('../../Components/Common/MediaComponents')
const {
  commonRegexes,
  generateErrorMessage,
} = require("../../Utilities/Common/validation")

// const ITEM_HEIGHT = 48;
// const ITEM_PADDING_TOP = 8;
// const MenuProps = {
//   PaperProps: {
//     style: {
//       maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
//       width: 250,
//     },
//   },
// };

/*const { basicStyles } = require('../common/style')
const styles = StyleSheet.create({
  ...basicStyles,
  phoneNumber: {
    fontSize: 21,
    marginTop: 20,
  },
  datePicker: {
    justifyContent: 'center',
    alignItems: 'flex-start',
    width: 320,
    height: 260,
    display: 'flex',
  },
})*/

const setValueIntoStateForElement = (
  elementName,
  formDefinition,
  formState,
  setFormState,
  newValue,
  additionalParams
) => {
  const newFormState = { ...formState };
  const newFormElementState = { ...formState[elementName] };
  const elementDefinition = formDefinition.formControls[elementName]
  if (!["date", "date-time"].includes(elementDefinition.type)) {
    newFormElementState.displayValue = newValue;
  } else {
    let formatToUse = elementDefinition.type === 'date-time' ? "YYYY-MM-DD hh:mm" : "YYYY-MM-DD"
    if (elementDefinition.format) {
      formatToUse = elementDefinition.format
    }
    newFormElementState.displayValue = moment(newValue).format(formatToUse)
    newFormElementState.value = newValue; 
  }
  if (elementDefinition.type === "text-l10n") {
    if (newFormElementState.value === undefined) {
      newFormElementState.value = {}
    }
    assignValueToL10NObject(
      newFormElementState.value,
      newValue,
      formDefinition.userLanguage
    );
  }
  newFormState[elementName] = newFormElementState;
  setFormState(lodashArray.cloneDeep(newFormState));
  return newFormState;
};

const validateFormElements = (formDefinition, formState, setFormState) => {
  let currentFormState = { ...formState };
  let validationFailed = false;
  for (const elementName of formDefinition.visibleFormControls) {
    const [returnedFormState, elementValidationFailed] =
      validateValueForElement(
        elementName,
        formDefinition,
        currentFormState,
        setFormState,
        undefined,
        []
      );
    currentFormState = returnedFormState;
    if (elementValidationFailed) {
      validationFailed = true;
    }
  }
  if (validationFailed) {
    if (currentFormState.formSnackbar) {
      currentFormState.formSnackbar.visible = 1;
    }
    setFormState(currentFormState);
  }
  return [currentFormState, validationFailed];
}

const validateFormElements2 = (formDefinition, formState, setFormState) => {
  let currentFormState = { ...formState };
  let validationFailed = false;
  for (const elementName of formDefinition.visibleFormControls) {
    const elementDefinition = formDefinition.formControls[elementName]
    if (elementDefinition.visible === false) {
      continue
    }
    if (elementDefinition
        && elementDefinition.ocFormRef
        && elementDefinition.ocFormRef.current
        && elementDefinition.ocFormRef.current !== null
    ) {
      const control = elementDefinition.ocFormRef.current
      const valueToValidate = control.getValue()
      const isValidValue = validateValue(valueToValidate, elementDefinition.type, elementDefinition.validations)
      if (!isValidValue) {
        validationFailed = true
        elementDefinition.ocFormRef.current.setErrorStatus("error")
      }
      // && elementDefinition.ocFormRef.current.getErrorStatus() !== undefined
    } else {
      if (!['map-marker', 'media'].includes(elementDefinition.type)) {
        validationFailed = true
      }
    }
  }
  if (validationFailed) {
    if (currentFormState.formSnackbar) {
      currentFormState.formSnackbar.visible = 1;
    }
    setFormState(currentFormState);
  }
  return [currentFormState, validationFailed]
}

const validateValueForElement = (
  elementName,
  formDefinition,
  formState,
  setFormState,
  event,
  additionalParams
) => {
  const elementDefinition = formDefinition.formControls[elementName];
  const elementValidations =
    elementDefinition.validations &&
    Array.isArray(elementDefinition.validations)
      ? elementDefinition.validations
      : [];
  let elementState = formState[elementName];
  if (elementState === undefined) {
    elementState = {}
  }
  let modifiedState = false;
  const newFormState = { ...formState };
  const newFormElementState = { ...formState[elementName] };
  let someCheckDone = false;
  let someMatchFailed = false;
  // console.log('KOCF vVFE 1, elementName = ', elementName)
  if (
    ["text-decimal"].includes(elementDefinition.type) &&
    elementValidations.length === 0
  ) {
    elementValidations.push({ type: "regex", regexArray: ["AnyDecimal"] });
  }
  if (
    ["positive-decimal"].includes(elementDefinition.type) &&
    elementValidations.length === 0
  ) {
    elementValidations.push({ type: "regex", regexArray: ["AnyDecimal"] });
  }
  if (
    ["text-number"].includes(elementDefinition.type) &&
    elementValidations.length === 0
  ) {
    elementValidations.push({ type: "regex", regexArray: ["AnyNumber"] });
  }
  if (
    ["positive-number"].includes(elementDefinition.type) &&
    elementValidations.length === 0
  ) {
    elementValidations.push({ type: "regex", regexArray: ["AnyNumber"] });
  }
  if (elementValidations && elementValidations.length > 0) {
    someCheckDone = true;

    for (const validationRegexReference of elementValidations) {
      // console.log('KOCF vVFE 3, validationRegexReference = ', validationRegexReference)
      if (validationRegexReference.type === "Mandatory") {
        if (
          [
            "text",
            "positive-decimal",
            "positive-number",
            "text-numeric",
            "text-area",
          ].includes(elementDefinition.type)
        ) {
          if (
            elementState.displayValue &&
            elementState.displayValue.length > 0
          ) {
            modifiedState = true;
            newFormElementState.showError = 1;
            newFormState[elementName] = newFormElementState;
          } else {
            someMatchFailed = true;
            modifiedState = true;
            newFormElementState.showError = -1;
            newFormState[elementName] = newFormElementState;
          }
        } else if (
          [
            "single-select",
            "single-select-l10n",
            "multi-select",
            "multi-select-l10n",
          ].includes(elementDefinition.type)
        ) {
          if (
            elementState.value === undefined ||
            (Array.isArray(elementState.value) &&
              elementState.value.length === 0)
          ) {
            someMatchFailed = true;
            modifiedState = true;
            newFormElementState.showError = -1;
            newFormState[elementName] = newFormElementState;
          } else if (
            newFormElementState.showError === -1 &&
            elementState.value !== undefined
          ) {
            modifiedState = true;
            newFormElementState.showError = 1;
            newFormState[elementName] = newFormElementState;
          }
        } else if (["text-l10n"].includes(elementDefinition.type)) {
          let elementKeysExist = true;
          if (elementState.value === undefined) {
            elementKeysExist = false;
          } else if (typeof elementState.value !== "object") {
            elementKeysExist = false;
          } else if (Object.keys(elementState.value).length === 0) {
            elementKeysExist = false;
          } else {
            const l10nObjectKeys = Object.keys(elementState.value);
            const numberOfKeys = l10nObjectKeys.length;
            if (
              l10nObjectKeys.includes("ul") &&
              elementState.value["ul"].length === 0
            ) {
              elementKeysExist = false;
            }
          }
          if (!elementKeysExist) {
            someMatchFailed = true;
            modifiedState = true;
            newFormElementState.showError = -1;
            newFormState[elementName] = newFormElementState;
          } else if (elementKeysExist && newFormElementState.showError === -1) {
            modifiedState = true;
            newFormElementState.showError = 1;
            newFormState[elementName] = newFormElementState;
          }
        } else if (["date"].includes(elementDefinition.type)) {
          if (elementState.value === null || elementState.value === undefined) {
            someMatchFailed = true;
            modifiedState = true;
            newFormElementState.showError = -1;
            newFormState[elementName] = newFormElementState;
          } else if (
            newFormElementState.showError === -1 &&
            elementState.value !== undefined
          ) {
            modifiedState = true;
            newFormElementState.showError = 1;
            newFormState[elementName] = newFormElementState;
          }
        }
      } else {
        if (!someMatchFailed && validationRegexReference.type === "regex") {
          const regexArray = validationRegexReference.regexArray;
          // console.log("KOCF vVFE 2, regexArray = ", regexArray);
          for (const regexStringReference of regexArray) {
            const regexString = commonRegexes[regexStringReference];
            const regex = new RegExp(regexString);
            let regexMatched = true;
            if (
              elementState.displayValue &&
              elementState.displayValue.length > 0
            ) {
              regexMatched = regex.test(
                elementState.displayValue ? elementState.displayValue : ""
              );
            }

            // console.log("KOCF vVFE 4, regexMatched = ", regexMatched);
            if (!regexMatched) {
              someMatchFailed = true;
              modifiedState = true;
              newFormElementState.showError = -1;
              // console.log("KOCF vVFE 5");
              const elementLevelErrorMessage = generateErrorMessage(
                validationRegexReference,
                elementState.displayValue
              );
              if (elementLevelErrorMessage === "") {
                delete newFormElementState.errorMessage;
              } else {
                newFormElementState.errorMessage = elementLevelErrorMessage;
              }
              // console.log('KOCF vVFE 10')
              newFormState[elementName] = newFormElementState;
            } else {
              if (newFormElementState.showError === -1) {
                modifiedState = true;
                newFormElementState.showError = 1;
                newFormState[elementName] = newFormElementState;
              }
            }
          }
        }
      }
      // console.log('KOCF vVFE 6, return value = ', regexMatched)
    }
    if (!someMatchFailed) {
      let newValue;
      if (["positive-decimal"].includes(elementDefinition.type)) {
        newValue = parseFloat(newFormElementState.displayValue);
        if (isNaN(newValue)) {
          newValue = null;
        }
      } else if (["positive-number"].includes(elementDefinition.type)) {
        newValue = parseInt(newFormElementState.displayValue);
        if (isNaN(newValue)) {
          newValue = null;
        }
      } else if (
        [
          "date",
          "date-time",
          "single-select",
          "single-select-l10n",
          "multi-select",
          "multi-select-l10n",
        ].includes(elementDefinition.type)
      ) {
        // don't do anything for date as we don't use displayvalue but directly use value
        newValue = newFormElementState.value;
      } else if (["text-l10n"].includes(elementDefinition.type)) {
        // don't do anything for date as we don't use displayvalue but directly use value
        // assignL10NObjectToState(data[dataToElementFieldName], newFormElementState, formDefinition.userLanguage)
        newValue = newFormElementState.value;
      } else {
        // for others assign display value to newValue
        newValue = newFormElementState.displayValue;
      }
      if (newValue !== newFormElementState.value) {
        modifiedState = true;
        newFormElementState.value = newValue;
        newFormState[elementName] = newFormElementState;
      }
    }
  }

  // below is to enforce display value to be value when there are no validations, but only if there are changes
  if (
    !someCheckDone &&
    newFormElementState.value !== newFormElementState.displayValue &&
    [ "positive-decimal", "positive-number", "text-numeric","text", "text-area"].includes(
      elementDefinition.type
    )
  ) {
    someCheckDone = true;
    modifiedState = true;
    newFormElementState.value = newFormElementState.displayValue;
    newFormState[elementName] = newFormElementState;
  }

  if (
    !someCheckDone &&
    newFormElementState.value !== newFormElementState.dbValue &&
    ["date"].includes(elementDefinition.type)
  ) {
    someCheckDone = true;
    modifiedState = true;
    newFormState[elementName] = newFormElementState;
  }

  // if state is modified, do decide whether to re-render form or not
  if (modifiedState) {
    setFormState(newFormState);
    return [newFormState, someMatchFailed];
  } else {
    return [formState, someMatchFailed];
  }
};

const extractChangedValues = (formDefinition, formState, enforcedValues) => {
  const consolidatedValues = {};
  for (const elementName of formDefinition.visibleFormControls) {
    const formElement = formState[elementName] !== undefined ? formState[elementName] : {};
    const elementDefinition = formDefinition.formControls[elementName];
    if (
      [
        "single-select",
        "multi-select",
        "single-select-l10n",
        "multi-select-l10n",
        "single-select_2",
        "multi-select_2",
        "single-select-l10n_2",
        "multi-select-l10n_2",
      ].includes(elementDefinition.type)
    ) {
      if (
        formElement.value === undefined &&
        formElement.dbValue !== formElement.value
      ) {
        formElement.value = null;
      }
    } else {
    }
    // console.log('KOCF eCV 1, elementName = ', elementName, ', formElement.dbValue = ', formElement.dbValue, ', formElement.value = ', formElement.value)
    let hasFormElementChanged = false
    // if (formElement.dbValue !== formElement.value) {
    if ((typeof formElement.dbValue === 'object' && typeof formElement.value === 'object' && !lodashArray.isEqual(formElement.dbValue, formElement.value)) || ((typeof formElement.dbValue !== 'object' || typeof formElement.value !== 'object') && formElement.dbValue !== formElement.value)) {
      if (formElement.dbValue === undefined && formElement.value === null) {
      } else {
        hasFormElementChanged = true
      }
    }
    // console.log('KOCF eCV 2, elementName = ', elementName, ', hasFormElementChanged = ', hasFormElementChanged)
    if (
      (enforcedValues && enforcedValues.includes(elementName)) ||
      hasFormElementChanged
    ) {
      // console.log('KOCF eCV 3')
      if (
        formDefinition.elementToDataMapping &&
        formDefinition.elementToDataMapping[elementName]
      ) {
        // console.log('KOCF eCV 4')
        const elementMappingDataFieldName =
          formDefinition.elementToDataMapping[elementName];
        // console.log('KOCF eCV 5, elementMappingDataFieldName = ')
        consolidatedValues[elementMappingDataFieldName] = formElement.value;
      } else {
        // console.log('KOCF eCV 6')
        consolidatedValues[elementName] = formElement.value;
      }

      // for date-time, convert from IST to GMT
      // if (elementDefinition.type === 'date-time') {
      //   consolidatedValues[elementName] = moment(consolidatedValues[elementName])
      // }

      // only for react, not for react-native
      // for single select and single select l10n, move value in array out of array and assign directly to value
      if (["single-select", "single-select-l10n"].includes(formDefinition.formControls[elementName])) {
        if (Array.isArray(consolidatedValues[elementName]) && consolidatedValues[elementName].length > 0) {
          consolidatedValues[elementName] = consolidatedValues[elementName][0]
        }
      }
      // console.log('KOCF eCV 7, consolidatedValues = ', consolidatedValues)
    }
  }
  // console.log('KOCF eCV 8, consolidatedValues = ', consolidatedValues)
  return consolidatedValues;
}

const assignValuesFromControlReferencesToFormState = (formDefinition, formState, setFormState) => {
  const updatedFormState = lodashArray.cloneDeep(formState)
  let dataAssigned = false
  for (const elementName of formDefinition.visibleFormControls) {
    const elementDefinition = formDefinition.formControls[elementName]
    if (elementDefinition && elementDefinition.ocFormRef && elementDefinition.ocFormRef.current && elementDefinition.ocFormRef.current !== null) {
      let updatedFormElement = updatedFormState[elementName]
      if (updatedFormElement === undefined || updatedFormElement === null) {
        updatedFormElement = {}
        updatedFormState[elementName] = updatedFormElement
      }
      dataAssigned = true
      const updatedValue = elementDefinition.ocFormRef.current.getValue()
      updatedFormElement.value = updatedValue
      if (elementDefinition.ocFormRef.current.getDisplayValue !== undefined && elementDefinition.ocFormRef.current.getDisplayValue !== null) {
        updatedFormElement.displayValue = elementDefinition.ocFormRef.current.getDisplayValue()
      }
    }
  }
  if (dataAssigned) {
    setFormState(updatedFormState)
    return updatedFormState
  } else {
    return formState
  }
}

const assignDataToFormState = (
  formDefinition,
  data,
  formState,
  setFormState
) => {
  // console.log("KOCF aDTF 1, formDefinition = ", formDefinition);
  // console.log("KOCF aDTF 2, data = ", data);
  const newFormState = { ...formState }
  let valueChanged = false;
  for (const elementName of formDefinition.visibleFormControls) {
    // console.log("KOCF aDTF 2b, elementName = ", elementName);
    let dataToElementFieldName = elementName;
    const elementDefinition = formDefinition.formControls[elementName]
    if (
      formDefinition.elementToDataMapping &&
      formDefinition.elementToDataMapping[elementName]
    ) {
      dataToElementFieldName = formDefinition.elementToDataMapping[elementName];
    }
    const dataToElementFieldNameForList = dataToElementFieldName + "_list"
    
    const dataToElementFieldNameForMarkerPosition = dataToElementFieldName + "_position";
    const dataToElementFieldNameForMarkerZoom = dataToElementFieldName + "_zoom";
    const dataToElementFieldNameForMarkerTooltip = dataToElementFieldName + "_tooltip";
    
    const dataToElementFieldNameForMediaDocumentId = dataToElementFieldName + "_document_id"
    const dataToElementFieldNameForMediaS3Key = dataToElementFieldName + "_s3_key"
    const dataToElementFieldNameForMediaDocumentTypeId = dataToElementFieldName + "_document_type_id"
    const dataToElementFieldNameForMediaDocumentType = dataToElementFieldName + "_document_type"
    const dataToElementFieldNameForMediaFileType = dataToElementFieldName + "_file_type"
    const dataToElementFieldNameForMediaDocumentUploadTime = dataToElementFieldName + "_document_upload_time"

    const selectionListFromServer = data[dataToElementFieldNameForList]
    
    const selectionMarkerPositionFromServer = data[dataToElementFieldNameForMarkerPosition]
    const selectionMarkerZoomFromServer = data[dataToElementFieldNameForMarkerZoom]
    const selectionMarkerTooltipFromServer = data[dataToElementFieldNameForMarkerTooltip]

    const mediaDocumentIdFromServer = data[dataToElementFieldNameForMediaDocumentId]
    const mediaS3KeyFromServer = data[dataToElementFieldNameForMediaS3Key]
    const mediaDocumentTypeIdFromServer = data[dataToElementFieldNameForMediaDocumentTypeId]
    const mediaDocumentTypeFromServer = data[dataToElementFieldNameForMediaDocumentType]
    const mediaFileTypeFromServer = data[dataToElementFieldNameForMediaFileType]
    const mediaDocumentUploadTimeFromServer = data[dataToElementFieldNameForMediaDocumentUploadTime]

    const selectionListInConfiguration = elementDefinition['list'];

    const selectionMarkerPositionInConfiguration =
      elementDefinition.position
    const selectionMarkerZoomInConfiguration =
      elementDefinition.zoom
    const selectionMarkerTooltipInConfiguration =
      elementDefinition.markerTooltip


    const mediaDocumentIdInConfiguration =
      elementDefinition.documentId
    const mediaS3KeyInConfiguration =
      elementDefinition.s3Key
    const mediaDocumentTypeIdInConfiguration =
      elementDefinition.documentTypeId
    const mediaDocumentTypeInConfiguration =
      elementDefinition.documentType
    const mediaFileTypeInConfiguration =
      elementDefinition.fileType

    // console.log(selectionListFromServer)
    
    // const selectionListInState =
    //   formState[elementName].list;
    const finalListData = selectionListInConfiguration
      ? selectionListInConfiguration
      : selectionListFromServer//  ? selectionListFromServer : selectionListInState);
    // console.log('KOCF aDTF 3, selectionListFromServer = ', selectionListFromServer,  ', selectionListInConfiguration = ', selectionListInConfiguration, ', finalListData = ', finalListData)

    const finalMarkerPositionData = selectionMarkerPositionFromServer
      ? selectionMarkerPositionFromServer
      : selectionMarkerPositionInConfiguration
    const finalMarkerZoomData = selectionMarkerZoomFromServer
      ? selectionMarkerZoomFromServer
      : selectionMarkerZoomInConfiguration ? selectionMarkerZoomInConfiguration : 17
    const finalMarkerTooltipData = selectionMarkerTooltipFromServer
      ? selectionMarkerTooltipFromServer
      : selectionMarkerTooltipInConfiguration

    const finalMediaDocumentId = mediaDocumentIdFromServer
      ? mediaDocumentIdFromServer : mediaDocumentIdInConfiguration
    const finalMediaS3Key = mediaS3KeyFromServer
      ? mediaS3KeyFromServer : mediaS3KeyInConfiguration
    const finalMediaDocumentTypeId = mediaDocumentTypeIdFromServer
      ? mediaDocumentTypeIdFromServer : mediaDocumentTypeIdInConfiguration
    const finalMediaDocumentType = mediaDocumentTypeFromServer
      ? mediaDocumentTypeFromServer : mediaDocumentTypeInConfiguration
    const finalMediaFileType = mediaFileTypeFromServer
      ? mediaFileTypeFromServer : mediaFileTypeInConfiguration


    if (
      [
        "single-select_2",
        "single-select-l10n_2",
        "multi-select_2",
        "multi-select-l10n_2",
        "single-select",
        "single-select-l10n",
        "multi-select",
        "multi-select-l10n",
      ].includes(elementDefinition.type)
    ) {
      const doesListDataExist = selectionListInConfiguration
        ? selectionListInConfiguration
        : selectionListFromServer // ) ? selectionListFromServer : selectionListInState;
      // console.log('KOCF aDTF 4, finalListData = ', finalListData, ', doesListDataExist = ', doesListDataExist);
      if (finalListData === undefined) {
        // console.log("KOCF aDTF 4a")
        continue;
      }
    } else if (['map-marker'].includes(elementDefinition.type)) {
      if (!finalMarkerPositionData) {
        continue
      }
    } else if (['media'].includes(elementDefinition.type)) {
      if (!finalMediaDocumentId && !finalMediaS3Key) {
        continue
      }
    } else {
      if (data[dataToElementFieldName] === undefined) {
        continue;
      }
    }
    // console.log("KOCF aDTF 5");
    valueChanged = true;
    const newFormElementState = { ...newFormState[elementName] };
    if (["single-select", "single-select-l10n", "single-select_2", "single-select-l10n_2"].includes(elementDefinition.type)) {
      // console.log("KOCF aDTF 6");
      // newFormElementState.value = 1201 // 1 // data[dataToElementFieldName]
      // newFormElementState.dbValue = 1201 // 1 // data[dataToElementFieldName]
      // newFormElementState.displayValue = 1201 // 1 // data[dataToElementFieldName]
      newFormElementState.value = data[dataToElementFieldName] // [data[dataToElementFieldName]];
      newFormElementState.dbValue = data[dataToElementFieldName] // [data[dataToElementFieldName]];
      newFormElementState.displayValue = data[dataToElementFieldName] // [data[dataToElementFieldName]];
      // newFormElementState.list = [{"district_name": "Ahmednagar", "ref_district_id": 1201}, {"district_name": "Solapur", "ref_district_id": 1202}]// data[dataToElementFieldNameForList]

      // console.log("KOCF aDTF 7");
      if (["single-select", "single-select_2"].includes(elementDefinition.type)) {
        // console.log("KOCF aDTF 8");
        // newFormElementState.list = data[dataToElementFieldNameForList];
        newFormElementState.list = finalListData
      } else {
        // console.log("KOCF aDTF 9");
        if (elementDefinition.list) {
          // console.log("KOCF aDTF 10");
          newFormElementState.list = elementDefinition.list
        } else {
          // console.log("KOCF aDTF 11");
          const l10nObjectKey = elementDefinition["labelKeyInList"];
          // const listRecordsWithLanguage = data[dataToElementFieldNameForList];
          const listRecordsWithLanguage = finalListData;
          for (const listRecordWithLanguage of listRecordsWithLanguage) {
            const l10nObject = listRecordWithLanguage[l10nObjectKey];
            const languageSpecificLabel = extractBasedOnLanguage(
              l10nObject,
              formDefinition.userLanguage
            );
            listRecordWithLanguage[l10nObjectKey] = languageSpecificLabel;
          }
          newFormElementState.list = listRecordsWithLanguage;
        }
      }
    } else if (["multi-select","multi-select-l10n", "multi-select_2","multi-select-l10n_2"].includes(elementDefinition.type)) {
      // console.log("KOCF aDTF 6");
      // newFormElementState.value = 1201 // 1 // data[dataToElementFieldName]
      // newFormElementState.dbValue = 1201 // 1 // data[dataToElementFieldName]
      // newFormElementState.displayValue = 1201 // 1 // data[dataToElementFieldName]
      newFormElementState.value = data[dataToElementFieldName];
      newFormElementState.dbValue = data[dataToElementFieldName];
      newFormElementState.displayValue = data[dataToElementFieldName];
      // newFormElementState.list = [{"district_name": "Ahmednagar", "ref_district_id": 1201}, {"district_name": "Solapur", "ref_district_id": 1202}]// data[dataToElementFieldNameForList]

      // console.log("KOCF aDTF 7");
      if (["multi-select"].includes(elementDefinition.type)) {
        // console.log("KOCF aDTF 8");
        // newFormElementState.list = data[dataToElementFieldNameForList];
        newFormElementState.list = finalListData
      } else {
        // console.log("KOCF aDTF 9");
        if (elementDefinition.list) {
          // console.log("KOCF aDTF 10");
        } else {
          // console.log("KOCF aDTF 11");
          const l10nObjectKey = elementDefinition["labelKeyInList"];
          // const listRecordsWithLanguage = data[dataToElementFieldNameForList];
          const listRecordsWithLanguage = finalListData
          for (const listRecordWithLanguage of listRecordsWithLanguage) {
            const l10nObject = listRecordWithLanguage[l10nObjectKey];
            const languageSpecificLabel = extractBasedOnLanguage(
              l10nObject,
              formDefinition.userLanguage
            );
            listRecordWithLanguage[l10nObjectKey] = languageSpecificLabel;
          }
          newFormElementState.list = listRecordsWithLanguage;
        }
      }
    } else if (["text-l10n", "text-l10n_2"].includes(elementDefinition.type)) {
      assignL10NObjectToState(
        data[dataToElementFieldName],
        newFormElementState,
        formDefinition.userLanguage
      );
    } else if (['map-marker'].includes(elementDefinition.type)) {
      let displayValue = ''
      if (finalMarkerPositionData && finalMarkerPositionData.latitude && finalMarkerPositionData.longitude) {
        displayValue = displayValue + '@' + finalMarkerPositionData.latitude + ',' + finalMarkerPositionData.longitude
        if (finalMarkerZoomData) {
          displayValue = displayValue + ',' + finalMarkerZoomData + 'z'
        }
      }
      newFormElementState.value = {position: [finalMarkerPositionData.latitude, finalMarkerPositionData.longitude], zoom: finalMarkerZoomData, markerTooltip: finalMarkerTooltipData}
      newFormElementState.dbValue = {position: [finalMarkerPositionData.latitude, finalMarkerPositionData.longitude], zoom: finalMarkerZoomData}
      newFormElementState.displayValue = displayValue
    } else if (['media'].includes(elementDefinition.type)) {
      const displayValue = finalMediaDocumentId ? finalMediaDocumentId : finalMediaS3Key
      newFormElementState.value = {documentId: finalMediaDocumentId, s3Key: finalMediaS3Key, documentTypeId: finalMediaDocumentTypeId, documentType: finalMediaDocumentType, fileType: finalMediaFileType, documentUploadTime: mediaDocumentUploadTimeFromServer}
      newFormElementState.dbValue = {documentId: finalMediaDocumentId, s3Key: finalMediaS3Key, documentTypeId: finalMediaDocumentTypeId, documentType: finalMediaDocumentType, fileType: finalMediaFileType, documentUploadTime: mediaDocumentUploadTimeFromServer}
      newFormElementState.displayValue = displayValue
    } else {
      newFormElementState.value = data[dataToElementFieldName];
      newFormElementState.displayValue = String(newFormElementState.value);
      newFormElementState.dbValue = data[dataToElementFieldName];
    }
    newFormState[elementName] = newFormElementState;
  }
  if (valueChanged) {
    setFormState(newFormState);
    return newFormState;
  } else {
    return formState;
  }
}

// const setDatePickerVisibility = (
//   elementName,
//   formDefinition,
//   formState,
//   setFormState,
//   visibility
// ) => {
//   const elementDefinition = formDefinition.formControls[elementName];
//   const elementState = formState[elementName];

//   const newFormState = { ...formState };
//   const newFormElementState = { ...formState[elementName] };

//   newFormElementState.dataPickerVisible = visibility;
//   newFormState[elementName] = newFormElementState;

//   setFormState(newFormState);
//   return newFormState;
// };

// const setDateForElement = async (
//   elementName,
//   formDefinition,
//   formState,
//   setFormState,
//   date
// ) => {
//   const elementDefinition = formDefinition.formControls[elementName];
//   const elementState = formState[elementName];

//   const newFormState = { ...formState };
//   const newFormElementState = { ...formState[elementName] };

//   newFormElementState.value = date;
//   // newFormElementState.displayValue = String(date)
//   newFormState[elementName] = newFormElementState;

//   setFormState(newFormState);
//   return newFormState;
// };

const dismissSnackbar = (formDefinition, formState, setFormState) => {
  if (formState.formSnackbar) {
    const newFormState = { ...formState };
    const newFormElementState = { ...formState["formSnackbar"] };
    newFormElementState.visible = -1;
    newFormState["formSnackbar"] = newFormElementState;
    setFormState(newFormState);
  }
};

// const setDropdownOpenStatus = (
//   elementName,
//   formDefinition,
//   formState,
//   setFormState,
//   visibility
// ) => {
//   const elementDefinition = formDefinition.formControls[elementName];
//   const elementState = formState[elementName];

//   let newFormState = { ...formState };
//   const newFormElementState = { ...formState[elementName] };

//   newFormElementState.dropdownOpen = visibility;
//   newFormState[elementName] = newFormElementState;

//   if (!visibility) {
//     // const vVFEReturnValue = validateValueForElement(elementName, formDefinition, newFormState, setFormState)
//     // const returnedFormState = vVFEReturnValue.returnedFormState
//     // const elementValidationFailed = vVFEReturnValue.validationFailed
//     const [returnedFormState, elementValidationFailed] =
//       validateValueForElement(
//         elementName,
//         formDefinition,
//         newFormState,
//         setFormState
//       );
//     newFormState = returnedFormState;
//   }

//   setFormState(newFormState);
//   return newFormState;
// };

const setDropdownValue = (
  elementName,
  formDefinition,
  formState,
  setFormState,
  value
) => {
  const elementDefinition = formDefinition.formControls[elementName];
  const elementState = formState[elementName];

  let valueChanged = false;

  let newFormState = { ...formState };
  const newFormElementState = { ...formState[elementName] };

  if (formState[elementName].value !== value) {
    valueChanged = true;
    newFormElementState.value = value;
    newFormElementState.displayValue = value;
    newFormElementState.dropdownOpen = false;
    newFormState[elementName] = newFormElementState;

    /*
    if (elementDefinition['onSetValue']) {
      const setValueCallbackFunction = elementDefinition['onSetValue']
      newFormState = await setValueCallbackFunction(userDeviceId, formDefinition.userLanguage, newFormState, setFormState, value)
    }
    */
  } else {
    newFormElementState.dropdownOpen = false;
    newFormState[elementName] = newFormElementState;
  }
  return [valueChanged, newFormState];
  /*
  let returnedFormState
  let elementValidationFailed
  if (formDefinition.autoSave && formDefinition.autoSave === 1) {
    const autoSaveCallbackFunction = formDefinition.autoSaveCallback
    // await autoSaveCallbackFunction(newFormState)
  } else {
    const vVFEReturnValue = validateValueForElement(elementName, formDefinition, newFormState, setFormState)
    returnedFormState = vVFEReturnValue.returnedFormState
    elementValidationFailed = vVFEReturnValue.validationFailed
    setFormState(returnedFormState)
    return returnedFormState
  }
  */
};

const getSelectValues = (
  elementName,
  formDefintion,
  formState,
  listData
) => {
  let returnValue = [];
  // console.log(
  //   "KOCF gMSV 0, elementName = ",
  //   elementName,
  //   ", formDefintion = ",
  //   formDefintion
  // );
  const elementDefinition = formDefintion.formControls[elementName];
  const elementState = formState[elementName];
  // console.log("KOCF gMSV 1, elementDefinition = ", elementDefinition);
  // console.log("KOCF gMSV 1, elementState = ", elementState);
  // console.log("KOCF gMSV 1, elementState.value = ", elementState.value);
  if (elementState && elementState.value && Array.isArray(elementState.value)) {
    // console.log("KOCF gMSV 2");
    if (listData && Array.isArray(listData)) {
      returnValue = listData.filter((option) => {
        return elementState.value.includes(
          option[elementDefinition.valueKeyInList]
        );
      });
    }
  }
  // console.log("KOCF gMSV 5 = ", returnValue);
  // return [{"reference_id" : 1000360002, "reference_name": "Harvester"}]
  return returnValue;
  /*return [{"reference_id" : 1000360002, "reference_name": "Harvester"}]*/
};

const handleDropdownSetValue = async (
  elementName,
  formDefinition,
  formState,
  setFormState,
  newValues,
  additionalParams,
) => {
  // extract ids
  // set form state
  const elementDefinition = formDefinition.formControls[elementName];

  if (newValues[0] == null) {
    newValues = [];
  }
  // console.log("KOCF hMSDDSV 1, newValues = ", newValues);
  const selectedIds = newValues.map((individualOption) => {
    return individualOption[elementDefinition.valueKeyInList];
  });
  // console.log("KOCF hMSDDSV 2, selectedIds = ", selectedIds);

  // if auto save is on, just call save, that in turn will do the validation
  // if auto save is off, validate where these are correct

  let newFormState = { ...formState };
  const newElementState = {...newFormState[elementName]}
  newElementState.value = selectedIds;
  newFormState[elementName] = newElementState;
  // console.log("KOCF hMSDDSV 3, selectedIds = ", selectedIds);

  const valueChanged = true;

  if (valueChanged) {
    if (elementDefinition["onSetValue"]) {
      const setValueCallbackFunction = elementDefinition["onSetValue"];
      // console.log("KOCF hMSDDSV 4, selectedIds = ", selectedIds);
      newFormState = await setValueCallbackFunction(
        formDefinition.userLanguage,
        newFormState,
        setFormState,
        selectedIds,
        additionalParams,
      );
      // console.log("KOCF hDDSV 5, newFormState = ", newFormState);
    }

    /*if (formDefinition.autoSave && formDefinition.autoSave === 1) {
      const autoSaveCallbackFunction = formDefinition.autoSaveCallback;
      await autoSaveCallbackFunction(newFormState);
    } else {*/
    // const vVFEReturnValue = validateValueForElement(elementName, formDefinition, newFormState, setFormState)
    // returnedFormState = vVFEReturnValue.returnedFormState
    // elementValidationFailed = vVFEReturnValue.validationFailed
    /*const [returnedFormState, elementValidationFailed] =
      validateValueForElement(
        elementName,
        formDefinition,
        newFormState,
        setFormState
      );
    setFormState(returnedFormState);
    return returnedFormState;*/
    /*}*/
  }
  setFormState(newFormState);
  return newFormState;
}

// const handleDropdownSetValue1 = async (
//   elementName,
//   formDefinition,
//   formState,
//   setFormState,
//   event
// ) => {
//   // console.log("Single autocomplete values", elementName, event.target.value);

//   const elementDefinition = formDefinition.formControls[elementName];
//   const newValue = event.target.value;
//   let valueChanged = false;
//   let newFormState;
//   [valueChanged, newFormState] = setDropdownValue(
//     elementName,
//     formDefinition,
//     formState,
//     setFormState,
//     newValue
//   );
//   if (valueChanged) {
//     // console.log("KOCF hDDSV 1");
//     if (elementDefinition["onSetValue"]) {
//       // console.log("KOCF hDDSV 2");
//       const setValueCallbackFunction = elementDefinition["onSetValue"];
//       // console.log("KOCF hDDSV 3");
//       newFormState = await setValueCallbackFunction(
//         formDefinition.userLanguage,
//         newFormState,
//         setFormState,
//         newValue
//       );
//       // console.log("KOCF hDDSV 4, newFormState = ", newFormState);
//     }

//     /*if (formDefinition.autoSave && formDefinition.autoSave === 1) {
//       const autoSaveCallbackFunction = formDefinition.autoSaveCallback;
//       await autoSaveCallbackFunction(newFormState);
//     } else {*/
//     // const vVFEReturnValue = validateValueForElement(elementName, formDefinition, newFormState, setFormState)
//     // returnedFormState = vVFEReturnValue.returnedFormState
//     // elementValidationFailed = vVFEReturnValue.validationFailed
//     const [returnedFormState, elementValidationFailed] =
//       validateValueForElement(
//         elementName,
//         formDefinition,
//         newFormState,
//         setFormState
//       );
//     setFormState(returnedFormState);
//     return returnedFormState;
//     /*}*/
//   }
// };

// const clearDropdown = (
//   elementName,
//   formDefinition,
//   formState,
//   setFormState
// ) => {
//   const elementDefinition = formDefinition.formControls[elementName];
//   const elementState = formState[elementName];

//   let newFormState = { ...formState };
//   const newFormElementState = { ...formState[elementName] };

//   newFormElementState.value = undefined;
//   newFormState[elementName] = newFormElementState;

//   setFormState(newFormState);
//   return newFormState;
// };

// const submitBtnHandler = () => {};

const KrushalOCForm = (props) => {
  // const [personName, setPersonName] = useState([]);
  const formState = props.formState;
  const setFormState = props.setFormState;
  const formDefinition = props.formDefinition;
  const manualCss = props.manualCss
  const numberOfControls = formDefinition.visibleFormControls.length;
  const defaultClasses = (formDefinition.defaultClasses !== undefined) ? formDefinition.defaultClasses : `col-lg-4 col-md-6 col-sm-12 mb-3 ${manualCss}`
  const additionalParams = props.additionalParams ? [...props.additionalParams] : []

  // console.log("KOCF c 1, formDefinition = ", formDefinition);
  // console.log("KOCF c 2, formState = ", formState);

  // const handleChange = (event) => {
  //   const {
  //     target: { value },
  //   } = event;
  //   setPersonName(
  //     // On autofill we get a stringified value.
  //     typeof value === "string" ? value.split(",") : value
  //   );
  // };
  return (
    <div className="container">
      <div className="row my-3">
        {formDefinition.visibleFormControls.map((elementName, index) => {
          // forceNewLineAfterControl
          let elementHTML
          // console.log("KOCF c r 1, elementName = ", elementName, ", index = ", index)
          const elementDefinition = formDefinition.formControls[elementName]
          if (elementDefinition.visible === false) {
            return
          }
          const elementState = (formState[elementName] === undefined) ? {} : formState[elementName]
          console.log("KOCF c r 1, elementDefinition = ", elementDefinition, ", elementState = ", elementState)
          const readOnly = (elementState.readOnly === 1) ? true : (elementState.readOnly === -1) ? false : (elementDefinition.readOnly === 1) ? true : false
          const isMandatory =
            elementDefinition.validations &&
            Array.isArray(elementDefinition.validations) &&
            lodashArray.findIndex(
              elementDefinition.validations,
              (validation) => {
                return validation.type === "Mandatory";
              }
            ) >= 0;

          // console.log('KOC c 1, elementName = ', elementName, ', elementState = ', elementState, ', isMandatory = ', isMandatory)
          const elementLabel = extractLabelOrPlaceHolderTextAsPerLanguage(
            elementDefinition.label !== undefined ? elementDefinition.label : {},
            isMandatory,
            formDefinition.userLanguage
          );
          let elementPlaceHolderText =
            extractLabelOrPlaceHolderTextAsPerLanguage(
              elementDefinition.placeHolderText !== undefined ? elementDefinition.placeHolderText : {},
              isMandatory,
              formDefinition.userLanguage
            );
          if (readOnly && formState.displayValue === "") {
            elementPlaceHolderText = "";
          }

          // const isMandatory = (elementDefinition.validations && Array.isArray(elementDefinition.validations) && elementDefinition.validations.includes('Mandatory'))
          switch (elementDefinition.type) {
            case "text-decimal":
            case "positive-decimal":
            case "positive-number":
            case "text-numeric":
            case "text-l10n":
            case "text": {
              elementHTML = (
                <div
                  className={defaultClasses}
                  key={"element" + index}
                >
                  <TextField
                    disabled={readOnly}
                    // InputLabelProps={readOnly ? { shrink: true } : undefined}
                    // InputLabelProps={{ shrink: true }}
                    InputLabelProps={(elementState && elementState.displayValue && elementState.displayValue !== '' || readOnly) ? { shrink: true } : undefined}
                    style={{ width: "100%" }}
                    type={
                      elementDefinition.type === "positive-number" && "number"
                    }
                    // {elementDefinition.type === 'postive-number'} ? 'type="number"' : ''}
                    label={elementLabel}
                    keyboardType={
                      elementDefinition.type === "text-numeric"
                        ? "number-pad"
                        : ""
                    }
                    variant="outlined"
                    error={
                      elementState.showError && elementState.showError === -1
                    }
                    value={elementState.displayValue}
                    onChange={(event) => {
                      // console.log("KOCF c tf oC 1, newValue = ", event);
                      const newValue = event.target.value;
                      // console.log("KOCF c tf oC 5, param4 = ", newValue);
                      const returnedFormState = setValueIntoStateForElement(
                        elementName,
                        formDefinition,
                        formState,
                        setFormState,
                        newValue,
                        additionalParams
                      );
                      // console.log("KOCF c tf oC 2, returnedFormState = ", returnedFormState);
                      if (
                        formState[elementName] && 
                        formState[elementName].showError &&
                        formState[elementName].showError === -1
                      ) {
                        validateValueForElement(
                          elementName,
                          formDefinition,
                          returnedFormState,
                          setFormState,
                          additionalParams,
                        );
                      }
                    }}
                    onBlur={async (event) => {
                      // console.log(event);
                      if (
                        formDefinition.autoSave &&
                        formDefinition.autoSave === 1
                      ) {
                        const autoSaveCallbackFunction =
                          formDefinition.autoSaveCallback;
                        await autoSaveCallbackFunction(formState);
                      } else {
                        // console.log("KOCF c tf oB 1, elementName = ", elementName);
                        // console.log("KOCF c tf oB 1a, formState = ", formState);
                        validateValueForElement(
                          elementName,
                          formDefinition,
                          formState,
                          setFormState,
                          event,
                          additionalParams,
                        );
                      }
                    }}
                  ></TextField>
                  {elementState.showError === -1 ? (
                    <FormHelperText type="error" style={{ color: "#d32f2f" }}>
                      {elementState.errorMessage &&
                      elementState.errorMessage.length > 0
                        ? elementState.errorMessage
                        : elementDefinition.errorMessage &&
                          elementDefinition.errorMessage.length > 0
                        ? elementDefinition.errorMessage
                        : ""}
                    </FormHelperText>
                  ) : (
                    <></>
                  )}
                </div>
              )
              break
            }
            case "text-number_2":
            case "text-decimal_2":
            case "positive-decimal_2":
            case "positive-number_2":
            case "text-numeric_2":
            case "text-l10n_2":
            case "text-area_2":
            case "text_2": {
              elementHTML = (
                <div
                  className={defaultClasses}
                  key={"element" + index}
                >
                  <KrushalOCInput
                    ref={elementDefinition.ocFormRef}
                    definition={elementDefinition}
                    type={elementDefinition.type}
                    value={elementState.value}
                    label={elementDefinition.label}
                    placeHolderText={elementDefinition.placeHolderText}
                    validations={elementDefinition.validations}
                    errorMessage={elementDefinition.errorMessage}
                    // onChangeCallback
                    // onBlurCallback
                    readOnly={readOnly}
                  />
                </div>
              )
              break
            }
            case "text-area": {
              elementHTML = (
                <div
                  className={defaultClasses}
                  key={"element" + index}
                >
                  <TextArea
                    rows={4}
                    disabled={readOnly}
                    // InputLabelProps={(elementState && elementState.displayValue && elementState.displayValue !== '' || readOnly) ? { shrink: true } : undefined}
                    style={{ width: "100%" }}
                    label={elementLabel}
                    keyboardType={
                      elementDefinition.type === "text-numeric"
                        ? "number-pad"
                        : ""
                    }
                    variant="outlined"
                    error={
                      elementState.showError && elementState.showError === -1
                    }
                    value={elementState.displayValue}
                    onChange={(event) => {
                      // console.log("KOCF c tf oC 1, newValue = ", event);
                      const newValue = event.target.value;
                      // console.log("KOCF c tf oC 5, param4 = ", newValue);
                      const returnedFormState = setValueIntoStateForElement(
                        elementName,
                        formDefinition,
                        formState,
                        setFormState,
                        newValue,
                        additionalParams
                      );
                      // console.log("KOCF c tf oC 2, returnedFormState = ", returnedFormState);
                      if (
                        formState[elementName] && 
                        formState[elementName].showError &&
                        formState[elementName].showError === -1
                      ) {
                        validateValueForElement(
                          elementName,
                          formDefinition,
                          returnedFormState,
                          setFormState,
                          undefined,
                          additionalParams,
                        );
                      }
                    }}
                    onBlur={async (event) => {
                      // console.log(event);
                      if (
                        formDefinition.autoSave &&
                        formDefinition.autoSave === 1
                      ) {
                        const autoSaveCallbackFunction =
                          formDefinition.autoSaveCallback;
                        await autoSaveCallbackFunction(formState);
                      } else {
                        // console.log("KOCF c tf oB 1, elementName = ", elementName);
                        // console.log("KOCF c tf oB 1a, formState = ", formState);
                        validateValueForElement(
                          elementName,
                          formDefinition,
                          formState,
                          setFormState,
                          event,
                          additionalParams,
                        );
                      }
                    }}
                  ></TextArea>
                  {elementState.showError === -1 ? (
                    <FormHelperText type="error" style={{ color: "#d32f2f" }}>
                      {elementState.errorMessage &&
                      elementState.errorMessage.length > 0
                        ? elementState.errorMessage
                        : elementDefinition.errorMessage &&
                          elementDefinition.errorMessage.length > 0
                        ? elementDefinition.errorMessage
                        : ""}
                    </FormHelperText>
                  ) : (
                    <></>
                  )}
                </div>
              )
              break
            }
            case "date-time":
            case "date": {
              let minimumDate
              let maximumDate
              if (
                elementDefinition.validations &&
                Array.isArray(elementDefinition.validations)
              ) {
                const minimumDateValidationIndex = lodashArray.findIndex(
                  elementDefinition.validations,
                  (validation) => {
                    return validation.type === "MinimumDate";
                  }
                );
                if (minimumDateValidationIndex >= 0) {
                  minimumDate =
                    elementDefinition.validations[minimumDateValidationIndex]
                      .value;
                }
                const maximumDateValidationIndex = lodashArray.findIndex(
                  elementDefinition.validations,
                  (validation) => {
                    return validation.type === "MaximumDate";
                  }
                );
                if (maximumDateValidationIndex >= 0) {
                  maximumDate =
                    elementDefinition.validations[maximumDateValidationIndex]
                      .value;
                }
              }

              let dateDefaultValue = null;
              let currentDate = new Date();
              if (minimumDate && minimumDate > currentDate) {
                dateDefaultValue = minimumDate;
              }
              if (maximumDate && maximumDate < currentDate) {
                dateDefaultValue = maximumDate;
              }

              // console.log("KOCF c r d 1, elementState.value = ", moment(elementState.value).format('YYYY-MM-DD'));

              const dateObject =
                elementState.value &&
                elementState.value !== "" &&
                elementState.value !== null
                  ? new Date(elementState.value)
                  : dateDefaultValue;
              const dateDisplay =
                elementState.value &&
                elementState.value !== "" &&
                elementState.value !== null
                  ? moment(elementState.value).format("MM/DD/YYYY")
                  : undefined;
              
              let formatToUse = elementDefinition.type === 'date-time' ? "DD/MM/YYYY hh:mm a" : "DD/MM/YYYY"
              if (elementDefinition.format) {
                formatToUse = elementDefinition.format
              }

              const datePickerComponentsProps = {
                actionBar: {
                  actions: ["clear"],
                },
              }
              
              const datePickerOnAccept= (event) => {
                console.log("KOCF c r d oA 1, event = ", event);
                if (
                  event &&
                  event.$d &&
                  event.$d instanceof Date &&
                  !isNaN(event.$d)
                ) {
                  const returnedFormState = setValueIntoStateForElement(
                    elementName,
                    formDefinition,
                    formState,
                    setFormState,
                    event.$d,
                    additionalParams
                  );
                  // console.log("KOCF c tf oC 2, returnedFormState = ", returnedFormState);
                  if (
                    formState[elementName] && 
                    formState[elementName].showError &&
                    formState[elementName].showError === -1
                  ) {
                    validateValueForElement(
                      elementName,
                      formDefinition,
                      returnedFormState,
                      setFormState,
                      undefined,
                      additionalParams,
                    );
                  }
                } else if (event === null) {
                  const returnedFormState = setValueIntoStateForElement(
                    elementName,
                    formDefinition,
                    formState,
                    setFormState,
                    null,
                    additionalParams
                  );
                  // console.log("KOCF c tf oC 2, returnedFormState = ", returnedFormState);
                  validateValueForElement(
                    elementName,
                    formDefinition,
                    returnedFormState,
                    setFormState,
                    undefined,
                    additionalParams,
                  );
                }
              }
              
              const datePickerOnChange=(event) => {
                console.log("KOCF r d oC 1, event = ", event);
                // // const newValue = moment(event).format("YYYY-MM-DD");
                // if (
                //   event &&
                //   event.$d &&
                //   event.$d instanceof Date &&
                //   !isNaN(event.$d)
                // ) {
                //   const returnedFormState = setValueIntoStateForElement(
                //     elementName,
                //     formDefinition,
                //     formState,
                //     setFormState,
                //     event.$d
                //   );
                //   // console.log("KOCF c tf oC 2, returnedFormState = ", returnedFormState);
                //   if (
                //     formState[elementName].showError &&
                //     formState[elementName].showError === -1
                //   ) {
                //     validateValueForElement(
                //       elementName,
                //       formDefinition,
                //       returnedFormState,
                //       setFormState
                //     );
                //   }
                // } else {
                //   const returnedFormState = setValueIntoStateForElement(
                //     elementName,
                //     formDefinition,
                //     formState,
                //     setFormState,
                //     null
                //   );
                //   // console.log("KOCF c tf oC 2, returnedFormState = ", returnedFormState);
                //   /*if (
                //     formState[elementName].showError &&
                //     formState[elementName].showError === -1
                //   ) {*/
                //   validateValueForElement(
                //     elementName,
                //     formDefinition,
                //     returnedFormState,
                //     setFormState
                //   );
                //   // }
                // }
              }
              
              const datePickerOnViewChange = (param1, param2, param3) => {
                // console.log("KOCF r d oVC 1, param1 = ", param1);
                // console.log("KOCF r d oVC 1, param2 = ", param2);
                // console.log("KOCF r d oVC 1, param3 = ", param3);
              }
              
              const datePickerOnBlur = async (event, param2, param3, param4) => {
                // console.log("KOCF r d oB 1, event = ", event);
                // console.log("KOCF r d oB 1, param2 = ", param2);
                // console.log("KOCF r d oB 1, param3 = ", param3);
                // console.log("KOCF r d oB 1, param4 = ", param4);
                if (
                  formDefinition.autoSave &&
                  formDefinition.autoSave === 1
                ) {
                  const autoSaveCallbackFunction =
                    formDefinition.autoSaveCallback;
                  await autoSaveCallbackFunction(formState);
                } else {
                  // console.log("KOCF c tf oB 1, elementName = ", elementName);
                  // console.log("KOCF c tf oB 1a, formState = ", formState);
                  validateValueForElement(
                    elementName,
                    formDefinition,
                    formState,
                    setFormState,
                    event,
                    additionalParams,
                  );
                }
              }
              
              const datePickerRenderInput= (params) => (
                <TextField
                  fullWidth
                  {...params}
                  onBlur={(event) => {
                    // console.log("event = ", event);
                    validateValueForElement(
                      elementName,
                      formDefinition,
                      formState,
                      setFormState,
                      event,
                      additionalParams,
                    );
                  }}
                />
              )


              elementHTML = (
                <div
                  className={defaultClasses}
                  key={"element" + index}
                >
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    {elementDefinition.type === 'date-time' ? (
                      <DateTimePicker
                        views={['day','hours', 'minutes']}
                        minDate={minimumDate !== undefined ? minimumDate : undefined}
                        maxDate={maximumDate !== undefined ? maximumDate : undefined}
                        disabled={readOnly}
                        componentsProps={datePickerComponentsProps}
                        onAccept={datePickerOnAccept}
                        label={elementLabel}
                        inputFormat={formatToUse}
                        value={dateObject}
                        onChange={datePickerOnChange}
                        onViewChange={datePickerOnViewChange}
                        onBlur={datePickerOnBlur}
                        renderInput={datePickerRenderInput}
                      />
                    ) : (
                      <DesktopDatePicker
                        minDate={minimumDate !== undefined ? minimumDate : undefined}
                        maxDate={maximumDate !== undefined ? maximumDate : undefined}
                        disabled={readOnly}
                        componentsProps={datePickerComponentsProps}
                        onAccept={datePickerOnAccept}
                        label={elementLabel}
                        inputFormat={formatToUse}
                        value={dateObject}
                        onChange={datePickerOnChange}
                        onViewChange={datePickerOnViewChange}
                        onBlur={datePickerOnBlur}
                        renderInput={datePickerRenderInput}
                      />
                    )}                    
                  </LocalizationProvider>

                  {elementState.showError === -1 && (
                    <FormHelperText type="error" style={{ color: "#d32f2f" }}>
                      {elementState.errorMessage &&
                      elementState.errorMessage.length > 0
                        ? elementState.errorMessage
                        : elementDefinition.errorMessage &&
                          elementDefinition.errorMessage.length > 0
                        ? elementDefinition.errorMessage
                        : ""}
                    </FormHelperText>
                  )}
                </div>
              )
              break
            }
            case "date-time_2":
            case "date_2": {
              console.log('KOCF r dt2, elementDefinition.type = ', elementDefinition.type, ', elementState.value = ', elementState.value)
              elementHTML = (
                <div className={defaultClasses}  >
                  <KrushalOCDateTime
                    ref={elementDefinition.ocFormRef}
                    definition={elementDefinition}
                    type={elementDefinition.type}
                    value={elementState.value}
                    label={elementDefinition.label}
                    placeHolderText={elementDefinition.placeHolderText}
                    validations={elementDefinition.validations}
                    errorMessage={elementDefinition.errorMessage}
                    // onChangeCallback={elementDefinition['onSetValue2'] !== undefined ? elementDefinition['onSetValue2'] : undefined}
                    // onChangeCallback
                    // onBlurCallback
                    readOnly={readOnly}
                    additionalParams={additionalParams}

                    style={{ width: "100%" }}
                    id={"select-" + index}
                  />
                </div>
              )
              break
            }
            case "single-select-l10n_2":
            case "single-select_2":
            case "multi-select-l10n_2":
            case "multi-select_2": {
              // const listData = [{"reference_id": 1, "reference_name": "Two Wheeler"}, {"reference_id": 2, "reference_name": "Tractor"}, {"reference_id":3,"reference_name": "Harvester"}]// elementState.list
              const listData = elementState.list && Array.isArray(elementState.list) ? elementState.list : elementDefinition.list;
              // console.log("KOCF c r ms 0, listData = ", listData);
              // console.log(
              //   "KOCF c r ms 0a, elementStateValue = ",
              //   elementState.value
              // );
              elementHTML = (
                <div className={defaultClasses}  >
                  <KrushalOCDropdown
                    ref={elementDefinition.ocFormRef}
                    definition={elementDefinition}
                    labelKeyInList={elementDefinition.labelKeyInList}
                    valueKeyInList={elementDefinition.valueKeyInList}
                    type={elementDefinition.type}
                    options={listData === undefined ? [] : listData}
                    value={elementState.value}
                    label={elementDefinition.label}
                    placeHolderText={elementDefinition.placeHolderText}
                    validations={elementDefinition.validations}
                    errorMessage={elementDefinition.errorMessage}
                    onChangeCallback={elementDefinition['onSetValue2'] !== undefined ? elementDefinition['onSetValue2'] : undefined}
                    // onChangeCallback
                    // onBlurCallback
                    readOnly={readOnly}
                    additionalParams={additionalParams}

                    style={{ width: "100%" }}
                    id={"select-" + index}
                  />
                </div>
              )
              break
            }
            case "multi-select-l10n":
            case "multi-select": {
              // const listData = [{"reference_id": 1, "reference_name": "Two Wheeler"}, {"reference_id": 2, "reference_name": "Tractor"}, {"reference_id":3,"reference_name": "Harvester"}]// elementState.list
              const listData = elementState.list && Array.isArray(elementState.list) ? elementState.list : elementDefinition.list;
              // console.log("KOCF c r ms 0, listData = ", listData);
              // console.log(
              //   "KOCF c r ms 0a, elementStateValue = ",
              //   elementState.value
              // );
              elementHTML = (
                <div className={defaultClasses}  >
                  <Autocomplete
                    style={{ width: "100%" }}
                    multiple
                    disabled={readOnly}
                    disableCloseOnSelect
                    filterSelectedOptions
                    id={"select-" + index}
                    options={listData === undefined ? [] : listData}
                    value={getSelectValues(
                      elementName,
                      formDefinition,
                      formState,
                      listData
                    )}
                    onChange={(event, newValues, param3, param4, param5) => {
                      // console.log('KOCF c r ms 3, event = ', event)
                      // console.log('KOCF c r ms 4, newValues = ', newValues)
                      // console.log('KOCF c r ms 5, param3 = ', param3)
                      // console.log('KOCF c r ms 6, param4 = ', param4)
                      // console.log('KOCF c r ms 7, param5 = ', param5)
                      // setValue(newValue);
                      handleDropdownSetValue(
                        elementName,
                        formDefinition,
                        formState,
                        setFormState,
                        newValues,
                        additionalParams,
                      );
                    }}
                    getOptionLabel={(option) => {
                      // console.log('KOCF c r ms 1, option = ', option)
                      return option[elementDefinition.labelKeyInList];
                    }}
                    // defaultValue={[top100Films[13]]}
                    renderInput={(params) => {
                      // console.log("KOCF c r ms 2, ri = ", params);
                      return (
                        <TextField
                          {...params}
                          label={elementDefinition.label}
                          placeholder={elementDefinition.placeHolderText}
                        />
                      );
                    }}
                  />
                  {elementState.showError === -1 ? (
                    <FormHelperText type="error" style={{ color: "#d32f2f" }}>
                      {elementState.errorMessage &&
                      elementState.errorMessage.length > 0
                        ? elementState.errorMessage
                        : elementDefinition.errorMessage &&
                          elementDefinition.errorMessage.length > 0
                        ? elementDefinition.errorMessage
                        : ""}
                    </FormHelperText>
                  ) : (
                    <></>
                  )}
                </div>
              )
              break
            }
            case "single-select-l10n":
            case "single-select": {
              // const keyToUseInSingleSelectValue = formDefinition.elementToDataMapping[elementName]
              // const keyToUseInSingleSelectList = keyToUseInSingleSelectValue + "_list"
              const listData = elementState.list && Array.isArray(elementState.list) ? elementState.list : elementDefinition.list;
              const selectedValues = getSelectValues(
                     elementName,
                     formDefinition,
                     formState,
                     listData
                    )
              // console.log("KOCF c 4, listData = ", listData);
              // console.log("KOCF c 5, elementState = ", elementState);
              // console.log("KOCF c 6, selectedValues = ", selectedValues);
              elementHTML = (
                <div
                  className={defaultClasses}
                  key={"element" + index}
                >
                  <Autocomplete
                    style={{ width: "100%" }}
                    disableCloseOnSelect
                    filterSelectedOptions
                    disabled={readOnly}
                    id={"select-" + index}
                    value={selectedValues}
                    options={listData === undefined ? [] : listData}
                    getOptionLabel={(option) => {
                      if (Array.isArray(option) && option.length > 0) {
                        return option[0][elementDefinition.labelKeyInList];
                      } else if (Array.isArray(option) && option.length === 0) {
                        return ''
                      } else if (typeof(option) === 'object') {
                        return option[elementDefinition.labelKeyInList];
                      } else {
                        return ''
                      }
                    }}
                    onChange={(event, newValues) => {
                      // console.log("KOCF c r ms 3, event = ", event);
                      // console.log("KOCF c r ms 4, newValues = ", newValues);
                      handleDropdownSetValue(
                        elementName,
                        formDefinition,
                        formState,
                        setFormState,
                        [newValues],
                        additionalParams,
                      );
                    }}
                    onBlur={async (event) => {
                      // console.log(event);
                      if (
                        formDefinition.autoSave &&
                        formDefinition.autoSave === 1
                      ) {
                        const autoSaveCallbackFunction =
                          formDefinition.autoSaveCallback;
                        await autoSaveCallbackFunction(formState);
                      } else {
                        // console.log("KOCF c tf oB 1, elementName = ", elementName);
                        // console.log("KOCF c tf oB 1a, formState = ", formState);
                        validateValueForElement(
                          elementName,
                          formDefinition,
                          formState,
                          setFormState,
                          event,
                          additionalParams,
                        );
                      }
                    }}
                    renderInput={(params) => {
                      return  (
                        <TextField
                          {...params}
                          label={elementDefinition.label}
                          placeholder={elementDefinition.placeHolderText}
                        />
                      )
                    }}
                  />
                  {elementState.showError === -1 ? (
                    <FormHelperText type="error" style={{ color: "#d32f2f" }}>
                      {elementState.errorMessage &&
                      elementState.errorMessage.length > 0
                        ? elementState.errorMessage
                        : elementDefinition.errorMessage &&
                          elementDefinition.errorMessage.length > 0
                        ? elementDefinition.errorMessage
                        : ""}
                    </FormHelperText>
                  ) : (
                    <></>
                  )}
                </div>
              )
              break
            }
            case "hyper-link" : {
              console.log('KOCF c r 1, elementState.value = ', elementState.value)
              elementHTML = (
                <div
                  className={defaultClasses}
                >
                  <a target="_blank" rel="noopener noreferrer" href={elementState.value}>{elementDefinition.label}</a>
                </div>
              )
              break
            }
            case "map-marker" : {
              console.log('KOCF c r 1, elementState.value = ', elementState.value)
              if (elementState.value && elementState.value.position) {
                const latitude = elementState.value.position[0]
                const longitude = elementState.value.position[1]
                const zoomLevel = elementState.value.zoom
                const googleMapsLink = `https://www.google.com/maps?q=${latitude},${longitude}&z=${zoomLevel}`
                elementHTML = (
                  <div
                    className={defaultClasses}
                  >
                    <MapContainer
                      id={"map-" + index}
                      center={elementState.value.position}
                      zoom={elementState.value.zoom}
                      scrollWheelZoom={false}
                      style={elementDefinition.componentStyle}
                      >
                      <TileLayer {...tileLayer} />
                      <Marker position={elementState.value.position} icon={L.divIcon({ html: `<div><EnvironmentOutlined /></div>` })}>
                        {elementState.value.markerTooltip ? (
                          <Popup>
                            {' '}
                            <a href={googleMapsLink} target="_blank" rel="noopener noreferrer">
                              {elementState.value.markerTooltip}
                            </a>{' '}
                          </Popup>
                        ) : null}
                      </Marker>
                    </MapContainer>
                  </div>
                )
              } else {
                elementHTML = (
                  <></>
                )
              }
              
              break
            }
            case "media" : {
              if (elementState.value && elementState.value.documentId) {
                elementHTML = (
                  <div
                    className={defaultClasses}
                    >
                    <MediaCard
                      id={"media-" + index}
                      key={"media-" + index}
                      enableUploadOfFiles={elementDefinition.enableUploadOfFiles}
                      uploadInformation={{
                        uploadMultiple: elementDefinition.uploadMultiple,
                        uploadMaxCount: elementDefinition.uploadMaxCount,
                        uploadFolder: elementDefinition.uploadFolder,
                        uploadEntity: elementDefinition.uploadEntity,
                        uploadEntityInformation: elementDefinition.uploadEntityInformation,
                        uploadEntityDocumentInformation: elementDefinition.uploadEntityDocumentInformation
                      }}
                      styleContainer={{
                        mediaCardStyle: elementDefinition.mediaCardStyle,
                        mediaCardBodyStyle: elementDefinition.mediaCardBodyStyle,
                        mediaTagWrapperDivStyle: elementDefinition.mediaTagWrapperDivStyle,
                        mediaTagWrapperOnHoverDivStyle: elementDefinition.mediaTagWrapperOnHoverDivStyle,
                        mediaTagPreviewOverlayStyle: elementDefinition.mediaTagPreviewOverlayStyle,
                        mediaTagPreviewOverlayOnHoverStyle: elementDefinition.mediaTagPreviewOverlayOnHoverStyle,
                        mediaTagStyle: elementDefinition.mediaTagStyle,
                      }}
                      mediaInformation={{documentId: elementState.value.documentId}}
                      enablePreview={elementDefinition.enablePreview}
                      enableDownload={elementDefinition.enableDownload}
                      enableViewInBrowser={elementDefinition.enableViewInBrowser}
                      // previewRelatedInformation
                    />
                  </div>
                )  
              } else {
                elementHTML = null  
              }
              // elementHTML = MediaCard(index, elementDefinition, elementState, isMandatory, readOnly, elementLabel, elementPlaceHolderText)
              break
            }
          }
          if (formDefinition.forceNewLineAfterControl) {
            return <div>{elementHTML}</div>
          } else {
            return elementHTML
          }
        })}

        {/* <Snackbar
          visible={formState.formSnackbar.visible === 1}
          onDismiss={() =>
            dismissSnackbar(formDefinition, formState, setFormState)
          }
        >
          Some data is not valid
        </Snackbar> */}
      </div>
    </div>
  );
};

const assignReferencesAsListToForm = (formDefinition) => {
  const referenceMap = global.references.referenceCategoryToReferencesMap
  const formControlKeys = Object.keys(formDefinition.formControls)
  for (const elementName of formControlKeys) {
    const formControl = formDefinition.formControls[elementName]
    if (formControl && formControl.referenceCategoryId && referenceMap && referenceMap[formControl.referenceCategoryId]) {
      const referencesOfCategory = referenceMap[formControl.referenceCategoryId]
      const clonedReferencesOfCategory = assignL10NObjectOrObjectArrayToItSelf(referencesOfCategory, 'reference_name_l10n')
      formControl.list = clonedReferencesOfCategory
    }
  }
}

const validateValue = (value, type, validations) => {
  const validationsToValidate = lodashArray.cloneDeep(validations)
  let mandtoryOrTypeValidationFailed = false
  let newValue = value
  let mandatoryValidationRequired = false
  const otherValidations = []
  let regexValidations = []
  if (validationsToValidate && Array.isArray(validationsToValidate)) {
    for (const validation of validationsToValidate) {
      if (validation.type === "Mandatory") {
        mandatoryValidationRequired = true
      } else if (validation.type === "regex") {
        if (validation.regexArray && Array.isArray(validation.regexArray)) {
          regexValidations = validation.regexArray
        }
      } else {
        otherValidations.push(validation)
      }
    }
  }
  switch (type) {
    case "text-decimal_2":
    case "positive-decimal_2":
    case "text-decimal":
    case "positive-decimal":
      {
        if (mandatoryValidationRequired && (value === undefined || value === null || value === "")) {
          mandtoryOrTypeValidationFailed = true
        } else {
          if (!mandatoryValidationRequired && (value === undefined || value === null || value === "")) {

          } else {
            newValue = parseFloat(value)
            if (isNaN(newValue)) {
              mandtoryOrTypeValidationFailed = true
            }
          }
        }
        if (["text-decimal", "text-decimal_2"].includes(type)) {
          regexValidations.push("AnyDecimal")
        } else if (["positive-decimal", "positive-decimal_2"].includes(type)) {
          regexValidations.push("PositiveDecimal")
        }
      }
      break
    case "text_2":
    case "text-area_2":
    case "text-l10n_2":
    case "text":
    case "text-area":
    case "text-l10n":
    case "date_2":
    case "date-time_2":
    case "date":
    case "date-time":
      {
        if (mandatoryValidationRequired && (value === undefined || value === null || value === "")) {
          mandtoryOrTypeValidationFailed = true
        }
      }
      break
    case "text-numeric_2":
    case "text-number_2":
    case "positive-number_2":
    case "text-numeric":
    case "text-number":
    case "positive-number":
      {
        if (mandatoryValidationRequired && (value === undefined || value === null || value === "")) {
          mandtoryOrTypeValidationFailed = true
        } else {
          if (!mandatoryValidationRequired && (value === undefined || value === null || value === "")) {

          } else {
            newValue = parseInt(value)
            if (isNaN(newValue)) {
              mandtoryOrTypeValidationFailed = true
            }
          }
        }
        if (["text-number", "text-number_2"].includes(type)) {
          regexValidations.push("AnyNumber")
        } else if (["positive-number", "positive-number_2"].includes(type)) {
          regexValidations.push("PositiveNumber")
        }
      }
      break
    case "single-select-l10n_2":
    case "single-select_2":
    case "multi-select-l10n_2":
    case "multi-select_2":
    case "single-select-l10n":
    case "single-select":
    case "multi-select-l10n":
    case "multi-select":
      {
        if (value === undefined && mandatoryValidationRequired) {
          mandtoryOrTypeValidationFailed = true
        } else if (value && !Array.isArray(value)) {
          mandtoryOrTypeValidationFailed = true
        } else if (value && Array.isArray(value) && mandatoryValidationRequired && value.length === 0) {
          mandtoryOrTypeValidationFailed = true
        }
      }
      break
    default:
      throw new Error("Not a valid type")
  }
  let additionalValidationFailed = false
  if (!mandtoryOrTypeValidationFailed) {
    for (const regexStringReference of regexValidations) {
      const regexString = commonRegexes[regexStringReference]
      console.log("regexString = ", regexString)
      const regex = new RegExp(regexString)
      if (value !== undefined && value !== null && value !== "") {
        const regexMatched = regex.test(value)
        console.log("regexMatched = ", regexMatched)
        if (!regexMatched) {
          additionalValidationFailed = true
          break
        }
      }
    }

    switch (type) {
      case "date":
      case "date-time":
      case "date_2":
      case "date-time_2": {
        
        const hasMinimumDate = otherValidations.filter(object => object['type'] === 'MinimumDate' && object['value'] instanceof Date).length > 0 ? true : false
        const hasMaximumDate = otherValidations.filter(object => object['type'] === 'MaximumDate' && object['value'] instanceof Date).length > 0 ? true : false
        const minimumDate = hasMinimumDate ? otherValidations.filter(object => object['type'] === 'MinimumDate' && object['value'] instanceof Date)[0]['value'] : undefined
        const maximumDate = hasMaximumDate ? otherValidations.filter(object => object['type'] === 'MaximumDate' && object['value'] instanceof Date)[0]['value'] : undefined
        if (minimumDate !== undefined && newValue instanceof Date && newValue < minimumDate) {
          additionalValidationFailed = true
        }
        if (maximumDate !== undefined && newValue instanceof Date && newValue > maximumDate) {
          additionalValidationFailed = true
        }
        break
      }
    }
  }
  return !(mandtoryOrTypeValidationFailed || additionalValidationFailed)
}

const KrushalOCDropdown = React.forwardRef((
  {
    inputRef,
    style,
    type,
    label,
    placeHolderText,
    fieldNames,
    labelKeyInList,
    valueKeyInList,
    options,
    value,
    readOnly,
    validations,
    errorMessage,
    onChangeCallback,
    onBlurCallback,
    additionalParams,
  }, ref) => {
  useEffect(() => {
    const existingValue = lodashArray.cloneDeep(_value)
    if (!lodashArray.isEqual(existingValue, value)) {
      _setValue(value)
    }
    const existingOptions = lodashArray.cloneDeep(_options)
    const newOptionsToCheck = (options !== undefined && options !== null) ? options : []
    if (!lodashArray.isEqual(existingOptions, newOptionsToCheck)) {
      _setOptions(options)
    }
  }, [value, options])
  const [_value, _setValue] = useState(value)
  const [_options, _setOptions] = useState(options !== undefined ? options : [])
  const [_errorStatus, _setErrorStatus] = useState(undefined)

  const getValue = () => {
    return _value
  }

  const setValue = (newValue) => {
    _setValue(newValue)
  }

  const getOptions = () => {
    return _options
  }

  const setOptions = (newValue) => {
    _setOptions(newValue)
  }

  const getErrorStatus = () => {
    return _errorStatus
  }

  const setErrorStatus = (newStatus) => {
    return _setErrorStatus(newStatus)
  }

  React.useImperativeHandle(ref, () => ({
    getValue, setValue, getOptions, setOptions, getErrorStatus, setErrorStatus,
  }))

  let labelFinal = label
  if (typeof labelFinal === 'object') {
    labelFinal = extractBasedOnLanguage(labelFinal)
  }

  let placeHolderTextFinal = placeHolderText
  if (typeof placeHolderTextFinal === 'object') {
    placeHolderTextFinal = extractBasedOnLanguage(placeHolderTextFinal)
  }
  
  let errorMessageFinal = errorMessage
  if (typeof errorMessageFinal === 'object') {
    errorMessageFinal = extractBasedOnLanguage(errorMessageFinal)
  }

  const getRowBasedOnValues = () => {
    const returnValue = _options && Array.isArray(_options) ? _options.filter((object) => {
      if (_value !== undefined && _value !== null && Array.isArray(_value)) {
        if (_value.includes(object[valueKeyInList !== undefined ? valueKeyInList : 'value'])) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
      
    }) : []
    return returnValue
  }

  const getValuesBasedOnRows = (rows) => {
    const returnValue = rows && Array.isArray(rows) ? rows.map(object => object[valueKeyInList]) : []
    return returnValue
  }

  const dropdownOnChange = (newValue) => {
    const isValidValue = validateValue(newValue, type, validations)
    if (!isValidValue) {
      _setErrorStatus("error")
    } else {
      _setErrorStatus(undefined)
    }
    _setValue(newValue)
    if (onChangeCallback) {
      onChangeCallback(newValue, additionalParams)
    }
  }
  return (
    
    <div 
      // className={defaultClasses}
    >
      <Autocomplete
        style={{ width: "100%" }}
        multiple={['multi-select-l10n_2', 'multi-select_2', 'multi-select-l10n', 'multi-select'].includes(type) ? true : undefined}
        disabled={readOnly}
        // disableCloseOnSelect
        filterSelectedOptions
        // id={"select-" + index}
        options={_options}
        value={getRowBasedOnValues(_value)}
        getOptionLabel={(option) => {
          // console.log('KOCF c r ms 1, option = ', option)
          if (option && Array.isArray(option)) {
            return option.length > 0 ? option[0][labelKeyInList] : ''
          } else if (typeof option === 'object') {
            return option[labelKeyInList]
          } else {
            return ''
          }
        }}
        onChange={(event, selectedRows, param3, param4) => {
          console.log('KOC KOCDD r oC 1, event = ', event)
          console.log('KOC KOCDD r oC 2, newValues = ', selectedRows)
          console.log('KOC KOCDD r oC 3, param3 = ', param3)
          console.log('KOC KOCDD r oC 4, param4 = ', param4)
          const newValue = selectedRows && Array.isArray(selectedRows) ? getValuesBasedOnRows(selectedRows) : selectedRows && !Array.isArray(selectedRows) ? getValuesBasedOnRows([selectedRows]) : []
          dropdownOnChange(newValue)
        }}
        // defaultValue={[top100Films[13]]}
        renderInput={(params) => {
          return (
            <TextField
              {...params}
              label={labelFinal}
              placeholder={_value !== undefined && Array.isArray(_value) && _value.length > 0 ? undefined : placeHolderTextFinal}
            />
          );
        }}
      />
      {_errorStatus !== undefined && errorMessageFinal && errorMessageFinal.length > 0 ? (
          <FormHelperText type="error" style={{ color: "#d32f2f" }}>
            {errorMessageFinal}
          </FormHelperText>
        ) : (
          <></>
        )}
    </div>
  )
})

const KrushalOCInput = React.forwardRef((
  {
    definition,
    formatInformation,
    format,
    type,
    value,
    label,
    placeHolderText,
    validations,
    errorMessage,
    onChangeCallback,
    onBlurCallback,
    readOnly,
  },
  ref) => {
  const [_firstTimeDone, _setFirstTimeDone] = useState(false)
  useEffect(() => {
    let displayValueToSet
    if (_firstTimeDone === false
        || (typeof value === 'object' && !lodashArray.isEqual(_value, value))
        || (typeof value !== 'object') && value !== _value) {
      if (['positive-decimal_2', 'text-decimal_2', 'positive-decimal', 'text-decimal'].includes(type)) {
        if (value !== undefined && value !== null) {
          if (formatInformation !== undefined && formatInformation.formatType === 'double' && formatInformation.format.decimalPlaces) {
            displayValueToSet = parseFloat(value).toFixed(formatInformation.format.decimalPlaces)
          } else {
            displayValueToSet = String(value)
          }
        }
      } else if (['positive-number_2', 'text-number_2', 'positive-number', 'text-number'].includes(type)) {
        if (value !== undefined && value !== null) {
          displayValueToSet = String(value)
        }
      } else if (['text-l10n', 'text-l10n_2'].includes(type)) {
        displayValueToSet = extractBasedOnLanguage(value)
      } else {
        displayValueToSet = value
      }
      _setValue(value)
      _setDisplayValue(displayValueToSet)
    }
    _setFirstTimeDone(true)
  }, [value, _firstTimeDone])
  let initialValue = value
  if (['text-l10n', 'text-l10n_2'].includes(type) && typeof value !== 'object') {
    initialValue = {}
  }
  const [_value, _setValue] = useState(initialValue)
  const [_displayValue, _setDisplayValue] = useState('')
  
  const [_errorStatus, _setErrorStatus] = useState(undefined)

  const getValue = () => {
    return _value
  }

  const getErrorStatus = () => {
    return _errorStatus
  }

  const setErrorStatus = (newErrorStatus) => {
    return _setErrorStatus(newErrorStatus)
  }

  const getDisplayValue = () => {
    return _displayValue
  }

  React.useImperativeHandle(ref, () => ({
    getValue, getErrorStatus, getDisplayValue, setErrorStatus
  }))

  let labelFinal = label
  if (typeof labelFinal === 'object') {
    labelFinal = extractBasedOnLanguage(labelFinal)
  }

  let placeHolderTextFinal = placeHolderText
  if (typeof placeHolderTextFinal === 'object') {
    placeHolderTextFinal = extractBasedOnLanguage(placeHolderTextFinal)
  }
  
  let errorMessageFinal = errorMessage
  if (typeof errorMessageFinal === 'object') {
    errorMessageFinal = extractBasedOnLanguage(errorMessageFinal)
  }

  let elementReadOnlyFinal = false
  if ((definition && definition.readOnly === true) || readOnly === true) {
    elementReadOnlyFinal = true
  }

  let keyboardType
  if (['text-decimal', 'positive-decimal', 'text-number', 'positive-number', 'text-numeric',
        'text-decimal_2', 'positive-decimal_2', 'text-number_2', 'positive-number_2', 'text-numeric_2',
        ].includes(type)) {
    keyboardType = "number"
  }

  const inputOnChangeOrBlur = (newText, changeOrBlur) => {
    // let existingValue = _value
    const isValidValue = validateValue(newText, type, validations)
    let newValue
    if (!isValidValue) {
      //showError
      _setErrorStatus('error')
      if (changeOrBlur === 'blur') {
        newValue = null
      }
    } else {
      newValue = newText
      _setErrorStatus(undefined)
    }
    {
      switch (type) {
        case "text_2":
        case "text-area_2":
        case "text-numeric_2":
        case "text":
        case "text-area":
        case "text-numeric":
          {
            if (newValue !== undefined) {
              newValue = newText
              _setValue(newValue)
            }
          }
          break
        case "positive-number_2":
        case "text-number_2":
        case "positive-number":
        case "text-number":
          {
            if (newValue !== undefined) {
              if (newValue === null) {
                _setValue(newValue)
              } else {
                newValue = parseInt(newText)
                _setValue(newValue)
              }
            }
          }
          break
        case "positive-decimal_2":
        case "text-decimal_2":
        case "positive-decimal":
        case "text-decimal":
          {
            if (newValue !== undefined) {
              if (newValue === null) {
                _setValue(null)
              } else {
                newValue = parseFloat(newText)
                _setValue(newValue)
              }
            }
          }
          break
        case "text-l10n_2":
        case "text-l10n":
          {
            const existingValue = _value === undefined ? {} : lodashArray.cloneDeep(_value)
            if (newValue !== undefined) {
              if (newValue === null) {
                _setValue(null)
              } else {
                assignValueToL10NObject(existingValue, newText)
                _setValue(existingValue)
              }
            }
          }
          break
        }  
    }
    if (changeOrBlur === 'blur') {
      if (onBlurCallback) {
        onBlurCallback()
      }
    }
    if (changeOrBlur === 'change') {
      if (onChangeCallback) {
        onChangeCallback(newValue)
      }
    }
  }

  return (
    <>
      <div
        // className={defaultClasses}
        // key={"element" + index}
      >
        <TextField
          multiline={['text-area', 'text-area_2'].includes(type) ? true : undefined}
          minRows={['text-area', 'text-area_2'].includes(type) ? 2 : undefined}
          disabled={elementReadOnlyFinal}
          // InputLabelProps={readOnly ? { shrink: true } : undefined}
          // InputLabelProps={{ shrink: true }}
          InputLabelProps={((_displayValue !== undefined && _displayValue !== null && _displayValue !== '') || elementReadOnlyFinal) ? { shrink: true } : undefined}
          style={{ width: "100%" }}
          // {elementDefinition.type === 'postive-number'} ? 'type="number"' : ''}
          label={labelFinal || placeHolderTextFinal}
          type={keyboardType}
          variant="outlined"
          error={_errorStatus !== undefined}
          value={_displayValue}
          onChange={(event) => {
            // console.log("KOCF c tf oC 1, newValue = ", event);
            const newText = event.target.value;
            _setDisplayValue(newText)
            inputOnChangeOrBlur(newText, 'change')
          }}
          onBlur={async () => {
            // console.log(event);
            inputOnChangeOrBlur(_displayValue, 'blur')
          }}
        ></TextField>
        {_errorStatus !== undefined && errorMessageFinal && errorMessageFinal.length > 0 ? (
          <FormHelperText type="error" style={{ color: "#d32f2f" }}>
            {errorMessageFinal}
          </FormHelperText>
        ) : (
          <></>
        )}
      </div>
    </>
  )
})

const KrushalOCDateTime = React.forwardRef((
  {
    definition,
    formatInformation,
    format,
    type,
    value,
    defaultValue,
    label,
    placeHolderText,
    validations,
    errorMessage,
    onChangeCallback,
    onBlurCallback,
    readOnly,
    dateEditableText,
  },
  ref) => {
  const formatToUse = format !== undefined ? format : ['date-time', 'date-time_2'].includes(type) ? 'DD/MM/YYYY hh:mm a' : 'DD/MM/YYYY'
  const [_firstTimeDone, _setFirstTimeDone] = useState(false)
  const hasMinimumDate = validations && Array.isArray(validations) && validations.filter(object => object['type'] === 'MinimumDate' && object['value'] instanceof Date).length > 0 ? true : false
  const hasMaximumDate = validations && Array.isArray(validations) && validations.filter(object => object['type'] === 'MaximumDate' && object['value'] instanceof Date).length > 0 ? true : false
  const minimumDate = hasMinimumDate ? validations.filter(object => object['type'] === 'MinimumDate' && object['value'] instanceof Date)[0]['value'] : undefined
  const maximumDate = hasMaximumDate ? validations.filter(object => object['type'] === 'MaximumDate' && object['value'] instanceof Date)[0]['value'] : undefined
  
  useEffect(() => {
    if (_firstTimeDone === false) {
      if (value !== undefined || value !== null && (value instanceof Date)) {
        /* if (['text-l10n', 'text-l10n_2'].includes(type) && typeof value !== 'object') {
          initialValue = {}
        } */
        _setValue(value)
        _setDisplayValue(dayjs(value).format(formatToUse))
      } else {
        let initialValue = value !== undefined && value !== null && (value instanceof Date) ? value : defaultValue !== undefined && defaultValue !== null && (defaultValue instanceof Date) ? defaultValue : null // dayjs().add(7,'days').toDate()// new Date()
        _setValue(initialValue)
        if (initialValue instanceof Date) {
          _setDisplayValue(dayjs(initialValue).format(formatToUse))
        } else {
          _setDisplayValue('')
        }
      }
    } else if (_value instanceof Date && value instanceof Date && value !== _value) {
      _setValue(value)
      _setDisplayValue(dayjs(value).format(formatToUse))
    } else if ((_value instanceof Date && !(value instanceof Date)) || !(_value instanceof Date && (value instanceof Date))) {
      if (typeof value === 'string' || value instanceof String) {
        if (dayjs(value).isValid()) {
          _setValue(dayjs(value).toDate())
          _setDisplayValue(dayjs(value).format(formatToUse))
        } else {
          _setValue(null)
          _setDisplayValue('')
        }
      } else if (!(value instanceof Date)) {
        _setValue(null)
        _setDisplayValue('')
      }
    }
    _setFirstTimeDone(true)
  }, [value, _firstTimeDone])

  const [_value, _setValue] = useState(null)
  const [_displayValue, _setDisplayValue] = useState(undefined)
  
  const [_errorStatus, _setErrorStatus] = useState(undefined)

  const getValue = () => {
    return _value
  }

  const getErrorStatus = () => {
    return _errorStatus
  }

  const setErrorStatus = (newErrorStatus) => {
    _setErrorStatus(newErrorStatus)
  }

  const getDisplayValue = () => {
    return _displayValue
  }

  React.useImperativeHandle(ref, () => ({
    getValue, getErrorStatus, getDisplayValue, setErrorStatus
  }))

  let labelFinal = label
  if (typeof labelFinal === 'object') {
    labelFinal = extractBasedOnLanguage(labelFinal)
  }

  let placeHolderTextFinal = placeHolderText
  if (typeof placeHolderTextFinal === 'object') {
    placeHolderTextFinal = extractBasedOnLanguage(placeHolderTextFinal)
  }
  
  let errorMessageFinal = errorMessage
  if (typeof errorMessageFinal === 'object') {
    errorMessageFinal = extractBasedOnLanguage(errorMessageFinal)
  }

  let elementReadOnlyFinal = false
  if ((definition && definition.readOnly === true) || readOnly === true) {
    elementReadOnlyFinal = true
  }

  const onBlurOfEditableDateText = (event) => {
    console.log('KOCDT dPRI oBOEDT 1, event = ', event)
    const newDateDisplayed = event.target.value
    let isBlank = false
    let isValidDate = false
    let validDate
    if (newDateDisplayed === null || newDateDisplayed === undefined || newDateDisplayed === '') {
      isBlank = true
    } else if ((dayjs(newDateDisplayed, formatToUse,true).isValid())) {
      isValidDate = true
      validDate = dayjs(newDateDisplayed, formatToUse, true).toDate()
    }

    if (!isValidDate) {
      _setErrorStatus('format')
    } else {
      const isValidValue = validateValue(validDate, type, validations)
      if (!isValidValue) {
        _setErrorStatus('error')
        _setValue(null)
      } else {
        _setErrorStatus(undefined)
      }
    }
  }

  const onChangeOfEditableDateText = (event) => {
    console.log('KOCDT dPRI oCOEDT 1, event = ', event)
    const newDateDisplayed = event.target.value
    let isBlank = false
    let isValidDate = false
    // let validDate
    if (newDateDisplayed === null || newDateDisplayed === undefined || newDateDisplayed === '') {
      isBlank = true
      // _setErrorStatus(undefined)
    } else if ((dayjs(newDateDisplayed, formatToUse,true).isValid())) {
      isValidDate = true
      // validDate = dayjs(newDateDisplayed, formatToUse, true).toDate()
    }
    if (!isValidDate) {
      _setErrorStatus("format")
    }
  }

  const datePickerRenderInput = (params) => {
    console.log("KOCF KOCDT dPRI 1, params = ", params, ', _displayValue = ', _displayValue)
    return (
      <TextField
        fullWidth
        {...params}
        // value={_displayValue}
        onKeyDown={dateEditableText === true ? (e) => {e.preventDefault()} : undefined}
        disabled={dateEditableText === true ? false : true}
        onBlur={onBlurOfEditableDateText}
        onChange={onChangeOfEditableDateText}
      />
    )
  }

  const onDatePickerOnChange=(event) => {
    console.log("KOCF KOCDT oDPOC 1, event = ", event)
  }

  const onDatePickerOnViewChange = (param1, param2, param3) => {
    console.log("KOCF KOCDT oDPVC 1, param1 = ", param1, ', param2 = ', param2, ', param3 = ', param3)
  }

  const onDatePickerOnBlur = async (event, param2, param3, param4) => {
    console.log("KOCF KOCDT oDPOB 1, event = ", event, ', param2 = ', param2, ', param3 = ', param3, ', param4 = ', param4)
  }

  const onDatePickerOnAccept= (event, param2, param3, param4) => {
    console.log("KOCF KOCDT oDPOA 11, event = ", event, ', param2 = ', param2, ', param3 = ', param3, ', param4 = ', param4)
    const newDate = event.toDate()
    _setValue(newDate)
  }

  return (
    <div
      // className={defaultClasses}
      // key={"element" + index}
    >
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        {['date-time', 'date-time_2'].includes(type) ? (
          <DateTimePicker
            views={['day','hours', 'minutes']}
            minDate={minimumDate !== undefined ? minimumDate : undefined}
            maxDate={maximumDate !== undefined ? maximumDate : undefined}
            disabled={readOnly}
            // componentsProps={datePickerComponentsProps}
            label={label}
            inputFormat={formatToUse}
            value={dayjs(_value)}
            // defaultValue={dayjs(_value)}
            onAccept={onDatePickerOnAccept}
            onChange={onDatePickerOnChange}
            onViewChange={onDatePickerOnViewChange}
            onBlur={onDatePickerOnBlur}
            renderInput={datePickerRenderInput}
          />
        ) : (
          <DesktopDatePicker
            minDate={minimumDate !== undefined ? minimumDate : undefined}
            maxDate={maximumDate !== undefined ? maximumDate : undefined}
            disabled={readOnly}
            // componentsProps={datePickerComponentsProps}
            label={label}
            inputFormat={formatToUse}
            value={dayjs(_value)}
            // defaultValue={dayjs(_value)}
            onAccept={onDatePickerOnAccept}
            onChange={onDatePickerOnChange}
            onViewChange={onDatePickerOnViewChange}
            onBlur={onDatePickerOnBlur}
            renderInput={datePickerRenderInput}
          />
        )}                    
      </LocalizationProvider>
      {_errorStatus !== undefined && errorMessageFinal && errorMessageFinal.length > 0 ? (
        <FormHelperText type="error" style={{ color: "#d32f2f" }}>
          {_errorStatus === 'error' ? errorMessageFinal : 'Wrong Format'}
        </FormHelperText>
      ) : (
        <></>
      )}
    </div>
  )
})

const resetSelectWithNewOptionsInForm = (formDefinition, selectControlName, newOptions) => {
  if (formDefinition.formControls
    && formDefinition.formControls[selectControlName]
      && formDefinition.formControls[selectControlName].ocFormRef
      && formDefinition.formControls[selectControlName].ocFormRef.current
      && formDefinition.formControls[selectControlName].ocFormRef.current !== null) {
    formDefinition.formControls[selectControlName].ocFormRef.current.setOptions(newOptions)
    formDefinition.formControls[selectControlName].ocFormRef.current.setValue([])
  }
}

const getValueFormControl = (formDefinition, selectControlName) => {
  if (formDefinition.formControls
    && formDefinition.formControls[selectControlName]
      && formDefinition.formControls[selectControlName].ocFormRef
      && formDefinition.formControls[selectControlName].ocFormRef.current
      && formDefinition.formControls[selectControlName].ocFormRef.current !== null) {
    return formDefinition.formControls[selectControlName].ocFormRef.current.getValue()
  } else {
    return undefined
  }
}

export {
  KrushalOCForm,
  extractChangedValues,
  assignDataToFormState,
  validateFormElements,
  validateFormElements2,
  assignValuesFromControlReferencesToFormState,
  assignReferencesAsListToForm,
  validateValue,
  KrushalOCDropdown,
  KrushalOCInput,
  KrushalOCDateTime,
  resetSelectWithNewOptionsInForm,
  getValueFormControl,
};
// export default KrushalOCFormExports