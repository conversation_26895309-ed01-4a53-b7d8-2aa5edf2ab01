/* *,
:after,
:before {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
} */ 
/* TODO: once confirmed remove above part */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5fbfd;
  overflow-x: hidden;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}
/* .ant-table{
  height: 230px;
  max-height: 230px;
} */
/* .ant-table-tbody{
  font-size: 12px;
}*/

.editable-row .ant-form-item-explain {
  position: absolute;
  top: 100%;
  font-size: 12px;
}

/* .fra-table.ant-table-wrapper tfoot>tr>td {
  padding: 16px 2px !important;
} */

.fra-table.ant-table-wrapper .ant-table-cell {
  padding: 2px 2px !important; /* Adjust padding as needed */
}

/* :where(.css-dev-only-do-not-override-1qhpsh8).ant-table-wrapper .ant-table-cell,
:where(.css-dev-only-do-not-override-1qhpsh8).ant-table-wrapper .ant-table-thead>tr>th */
/* .srfr-table .ant-table-wrapper .ant-table-thead tfoot>tr>th {
  padding: 4px 4px !important;
} */
.srfr-table.ant-table-wrapper .ant-table-cell {
  padding: 2px 2px !important;
}

/*
:where(.css-dev-only-do-not-override-1qhpsh8)
.ant-table-wrapper .ant-table-cell,
:where(.css-dev-only-do-not-override-1qhpsh8)
.ant-table-wrapper .ant-table-thead>tr>th,
:where(.css-dev-only-do-not-override-1qhpsh8)
.ant-table-wrapper .ant-table-tbody>tr>th,
:where(.css-dev-only-do-not-override-1qhpsh8)
.ant-table-wrapper .ant-table-tbody>tr>td,
:where(.css-dev-only-do-not-override-1qhpsh8)
.ant-table-wrapper tfoot>tr>th,
:where(.css-dev-only-do-not-override-1qhpsh8)
.ant-table-wrapper tfoot>tr>td
*/
/* .vafr-table.ant-table-wrapper .ant-table-cell {
  padding: 2px 2px !important;
}

.vafr-table.ant-table-wrapper tr>td {
  padding: 2px 2px !important;
}

.vafr-table.ant-table-wrapper .ant-table-cell >tr>th {
  padding: 2px 2px !important;
} */

/* .vafr-table.ant-table-wrapper tfoot>tr>th {
  padding: 2px 2px !important;
}*/

.vafr-table.ant-table-wrapper .ant-table-selection-column>tr>th {
  padding: 2px 2px !important;
}

.vafr-table.ant-table-wrapper .ant-table-selection-column>tr>td {
  padding: 2px 2px !important;
}

.vafr-table .ant-table-summary .ant-table-cell {
  padding: 2px !important;
}

/* Target summary row cells within your .vafr-table specifically within tfoot */
.vafr-table tfoot .ant-table-summary .ant-table-cell {
  padding: 2px !important;
}

/* .vafr-table.ant-table-wrapper .ant-table-summary .ant-table-cell tfoot>tr>td {
  padding: 2px 2px !important;
}

.vafr-table.ant-table-wrapper .ant-table-summary .ant-table-cell tfoot>tr>td {
  padding: 2px 2px !important;
}

.vafr-table.ant-table-wrapper .ant-table-selection-column>tr>th {
  padding: 2px 2px !important;
}

.vafr-table.ant-table-wrapper .ant-table-selection-column>tr>td {
  padding: 2px 2px !important;
} */

.sr-va-in .ant-input-number-handler-wrap {
  display: none !important;
}

:where(.css-dev-only-do-not-override-1ae8k9u).ant-picker-calendar .ant-picker-cell-in-view.ant-picker-cell-selected:not(.ant-picker-cell-disabled) .ant-picker-cell-inner,
:where(.css-dev-only-do-not-override-1ae8k9u).ant-picker-calendar .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-disabled) .ant-picker-cell-inner,
:where(.css-dev-only-do-not-override-1ae8k9u).ant-picker-calendar .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-disabled) .ant-picker-cell-inner {
    background-color: transparent !important;
    color: #111;
}
