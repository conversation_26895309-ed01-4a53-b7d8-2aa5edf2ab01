name: back_end_app
version: '3.9'
services:
  back_end:
    container_name: back-end
    image: registry.gitlab.com/krushal-it/**********************/paras-live/back-end:${IMAGE_TAG:-latest}
    env_file:
      - ${KRUSHAL_HOME_LOCATION}/envs/coreapp.env
    volumes:
      - ${KRUSHAL_HOME_LOCATION}/repos/krushal_ssl_certificates/key/domain.key:/ssl/domain.key
      - ${KRUSHAL_HOME_LOCATION}/repos/krushal_ssl_certificates/key/signed.cert.chain:/ssl/signed.cert.chain
      - ${KRUSHAL_HOME_LOCATION}/logs/back-end:/app/logs
    build:
      context: ${KRUSHAL_HOME_LOCATION}/repos/back_end
      args:
        GITLAB_AUTH_TOKEN: ${READ_KRUSHAL_IT_GITLAB_AUTH_TOKEN}
    ports:
      - '443:3001'
    command: ["/bin/sh", "-lc", "npm run build && npm run prod"]
    stdin_open: true
    tty: true
    restart: unless-stopped

networks:
  default:
    name: krushal_network
    external: true
