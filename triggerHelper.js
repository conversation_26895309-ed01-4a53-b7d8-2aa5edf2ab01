const {loadConfiguration, configurationJSON} = require('@krushal-it/common-core')

const {regenerateUpdatedAtTriggers} = require('./utils/regenerateUpdatedAtTriggers')

const initialize = async () => {
  try {
    if (process.env.FORCE_LOAD_CONFIGURATION === 'True') {
      if (process.env.ENV_JSON) {
        loadConfiguration(process.env.ENV_JSON)
      } else {
        throw new Error('No configuration to load')
      }
    }
    configuration = configurationJSON()
  } catch (error) {
    console.log('Configuration not available')
    throw error
  }
  await regenerateUpdatedAtTriggers()
}
initialize()
