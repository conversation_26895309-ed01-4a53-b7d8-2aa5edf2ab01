const {
  getDBConnections,
  postProcessRecords,
  preProcessRecords,
} = require('@krushal-it/ah-orm')
const { configurationJSON, KrushalError } = require('@krushal-it/common-core')
const {errorLog} = require('@krushal-it/mobile-or-server-lib')


class DistrictListLib {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = { return_code: 0 }

      const { query } = params
      const { stateId, includeState, farmerVillageId } = query

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      let geoQuerySelect = 
        `
          select rd.district_id, rd.district_name_l10n
        `
      let geoQueryAfterSelect = 
      `
        from ${mainSchemaAddition}ref_district rd, ${mainSchemaAddition}ref_state rs
        where rd.state_id = rs.state_id
      `
      if (includeState) {
        geoQuerySelect = geoQuerySelect + ' , rs.state_name_l10n '
      }

      if (stateId) {
        geoQueryAfterSelect = geoQueryAfterSelect + ` and rd.state_id = ${stateId}`
      } else if (farmerVillageId) {
        geoQueryAfterSelect = 
          `
            from ${mainSchemaAddition}ref_district rd, ${mainSchemaAddition}ref_state rs, ${mainSchemaAddition}ref_sdtv_view rsv
            where rd.state_id = rs.state_id and 
            rd.state_id = rsv.state_id and rsv.village_id = ${farmerVillageId}
          `
      }

      const finalGeoQuery = geoQuerySelect + geoQueryAfterSelect

      const geoResults = await dbConnections.main.manager.query(finalGeoQuery)
      postProcessRecords(undefined, geoResults, {json_columns: ['district_name_l10n', 'state_name_l10n']})

      returnValue.list_data = geoResults
      return returnValue

    } catch (error) {
      errorLog("error in DistrictListLib:58",params,{message:error.message})
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class TalukListLib {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = { return_code: 0 }

      const { query } = params
      const { districtId, farmerVillageId } = query

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      let geoQuery = 
        `
          select rt.taluk_id as taluk_id, rt.taluk_name_l10n as taluk_name_l10n
          from ${mainSchemaAddition}ref_taluk rt
          where 1 = 1
          `
      if (districtId) {
        geoQuery = geoQuery + ` and rt.district_id = ${districtId}`
      } else if (farmerVillageId) {
        geoQuery =
        `
          select rt.taluk_id as taluk_id, rt.taluk_name_l10n as taluk_name_l10n
          from ${mainSchemaAddition}ref_taluk rt, ${mainSchemaAddition}ref_sdtv_view rsv
          where 1 = 1 and rt.district_id = rsv.district_id and rsv.village_id = ${farmerVillageId}
        `
      }

      const geoResults = await dbConnections.main.manager.query(geoQuery)
      postProcessRecords(undefined, geoResults, {json_columns: ['taluk_name_l10n']})

      returnValue.list_data = geoResults
      return returnValue

    } catch (error) {
      errorLog("error in TalukListLib:109",params,{message:error.message})
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class VillageListLib {
  async create /*or is it update*/(data, params) {
    try {
      const returnValue = { return_code: 0 }

      const { query } = params
      const { talukId, farmerVillageId } = query

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      let geoQuery = 
        `
          select rv.village_id as village_id, rv.village_name_l10n as village_name_l10n
          from ${mainSchemaAddition}ref_village rv
          where 1 = 1
          `
      if (talukId) {
        geoQuery = geoQuery + ` and rv.taluk_id = ${talukId}`
      } else if (farmerVillageId) {
        geoQuery = 
          `
            select rv.village_id as village_id, rv.village_name_l10n as village_name_l10n
            from ${mainSchemaAddition}ref_village rv, ${mainSchemaAddition}ref_sdtv_view rsv
            where 1 = 1
            and rv.taluk_id = rsv.taluk_id and rsv.village_id = ${farmerVillageId}
          `
      }

      const geoResults = await dbConnections.main.manager.query(geoQuery)
      postProcessRecords(undefined, geoResults, {json_columns: ['village_name_l10n']})

      returnValue.list_data = geoResults
      return returnValue

    } catch (error) {
      errorLog("error in VillageListLib:160",params,{message:error.message})
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }
}

class GeoArraysLib {
  async create /*or is it update*/(data, params) {
    
    try {
      const customerId = params.query.customerId
      const returnValue = { return_code: 0 }

      const dbConnections = getDBConnections()

      const mainSchemaAddition =
      configurationJSON().IS_NODE_SERVER &&
      configurationJSON().IS_NODE_SERVER === 1
        ? 'main.'
        : ''

      if (customerId === -1 || customerId === '-1') {
        // get all districts of maharashtra
        const stateId = 1026
        let geoQuery = 
          `
            select rd.district_id as district_id, rd.district_name_l10n as district_name_l10n
            from ${mainSchemaAddition}ref_district rd, ${mainSchemaAddition}ref_state rs
            where rd.state_id = rs.state_id
            and rd.state_id = ${stateId}
          `
        const districtResults = await dbConnections.main.manager.query(geoQuery)
        postProcessRecords(undefined, districtResults, {json_columns: ['district_name_l10n']})
        returnValue.district_id_list = districtResults
        returnValue.district_id = undefined
        returnValue.taluk_id_list = []
        returnValue.taluk_id = undefined
        returnValue.village_id_list = []
        returnValue.village_id = undefined
      } else {
        // get customer village
        const customerVillageQuery = 
          `
            select rsv.village_id as village_id, rsv.taluk_id as taluk_id, rsv.district_id as district_id, rsv.state_id as state_id
            from ${mainSchemaAddition}customer_classification cc 
            left join ${mainSchemaAddition}ref_sdtv_view rsv on cc.value_reference_id = rsv.village_id
            where cc.customer_id = '${customerId}'
            and cc.classifier_id = 2000000055
          `
        const customerVillageResults = await dbConnections.main.manager.query(customerVillageQuery)
        const customerVillageId = (customerVillageResults.length > 0) ? customerVillageResults[0].village_id : undefined
        const customerTalukId = (customerVillageResults.length > 0) ? customerVillageResults[0].taluk_id : undefined
        const customerDistrictId = (customerVillageResults.length > 0) ? customerVillageResults[0].district_id : undefined
        const customerStateId = (customerVillageResults.length > 0) ? customerVillageResults[0].state_id : undefined
        // get districts (state districts?) of that village
        // get taluks of that village
        // get villages of that village
        if (customerVillageId) {
          let geoQuery = 
            `
              select rd.district_id as district_id, rd.district_name_l10n as district_name_l10n
              from ${mainSchemaAddition}ref_district rd, ${mainSchemaAddition}ref_state rs, ${mainSchemaAddition}ref_sdtv_view rsv
              where rd.state_id = rs.state_id and 
              rd.state_id = rsv.state_id and rsv.village_id = ${customerVillageId}
                `
          const districtResults = await dbConnections.main.manager.query(geoQuery)
          postProcessRecords(undefined, districtResults, {json_columns: ['district_name_l10n']})
          returnValue.district_id = customerDistrictId
          returnValue.district_id_list = districtResults
          geoQuery = 
            `
              select rt.taluk_id as taluk_id, rt.taluk_name_l10n as taluk_name_l10n
              from ${mainSchemaAddition}ref_taluk rt, ${mainSchemaAddition}ref_sdtv_view rsv
              where 1 = 1 and rt.district_id = rsv.district_id and rsv.village_id = ${customerVillageId}
            `
          const talukResults = await dbConnections.main.manager.query(geoQuery)
          postProcessRecords(undefined, talukResults, {json_columns: ['taluk_name_l10n']})
          returnValue.taluk_id = customerTalukId
          returnValue.taluk_id_list = talukResults
          geoQuery = 
            `
              select rv.village_id as village_id, rv.village_name_l10n as village_name_l10n
              from ${mainSchemaAddition}ref_village rv, ${mainSchemaAddition}ref_sdtv_view rsv
              where 1 = 1
              and rv.taluk_id = rsv.taluk_id and rsv.village_id = ${customerVillageId}
            `
          const villageResults = await dbConnections.main.manager.query(geoQuery)
          postProcessRecords(undefined, villageResults, {json_columns: ['village_name_l10n']})
          returnValue.village_id = customerVillageId
          returnValue.village_id_list = villageResults
        } else {
          const stateId = 1026
          let geoQuery = 
            `
              select rd.district_id as district_id, rd.district_name_l10n as district_name_l10n
              from ${mainSchemaAddition}ref_district rd, ${mainSchemaAddition}ref_state rs
              where rd.state_id = rs.state_id
              and rd.state_id = ${stateId}
            `
          const districtResults = await dbConnections.main.manager.query(geoQuery)
          postProcessRecords(undefined, districtResults, {json_columns: ['district_name_l10n']})
          returnValue.district_id_list = districtResults
          returnValue.district_id = undefined
          returnValue.taluk_id_list = []
          returnValue.taluk_id = undefined
          returnValue.village_id_list = []
          returnValue.village_id = undefined
          }
      }
      return returnValue
    } catch (error) {
      errorLog("error in GeoArraysLib:275",params,{message:error.message})
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }    
  }
}

module.exports = { DistrictListLib, TalukListLib, VillageListLib, GeoArraysLib }
