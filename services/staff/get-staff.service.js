const { CONST_TYPES_STAFF } = require("../../utils/constant");
const { getStaffListByfilter , getStaffListByTypeIdsfilter} = require("./controller/staff.controller");

class GetStaffService {
  async create(data, params) {
    const token = params.headers.token;
    // TODO restrict access
    if (!CONST_TYPES_STAFF[token.user_type]) {
      return { result: false, message: "unauthorized" };
    }
    try {
      return await getStaffListByfilter(data);
    } catch (error) {
      console.error(error);
      if (error.code) return error;
    }
  }

  async get(id,params) {
    const token = params.headers.token;
    if (!CONST_TYPES_STAFF[token.user_type]) {
      return { result: false, message: "unauthorized" };
    }
    try {
     if (id == -1) {
       return staffType;
     }
    } catch (error) {
      console.error(error);
      return { result: false, message: error.message };
    }
  }
}

//getting staffs based on staff type Ids

class GetStaffTypeIdsService{
  async create(data, params) {
    const token = params.headers.token;
    // TODO restrict access
    if (!CONST_TYPES_STAFF[token.user_type]) {
      return { result: false, message: "unauthorized" };
    }
    try {
      return await getStaffListByTypeIdsfilter(data, token.user_id);
    } catch (error) {
      console.error(error);
      if (error.code) return error;
    }
  }
}

module.exports = {GetStaffService , GetStaffTypeIdsService};