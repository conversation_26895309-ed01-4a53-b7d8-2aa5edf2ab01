const {getStaffList , getStaffListByTypeId} = require("../../../queries/staff");

const getStaffListByfilter = async(data)=>{
    try {
        let { f_staff_type } = data;
        let result = await getStaffList({f_staff_type});
        return result;
    } catch (error) {
        console.error(error);
        return [];
    }
}

//getting staffs based on staff type Ids
const getStaffListByTypeIdsfilter = async (data, user_id) =>{
    try{
        let { f_staff_type , f_user_geo_filter } = data;
        let result = await getStaffListByTypeId({f_staff_type ,f_user_geo_filter , user_id});
        return result;
    } catch (error) {
        console.error(error);
        return [];
    }
}



module.exports = {getStaffListByfilter , getStaffListByTypeIdsfilter}