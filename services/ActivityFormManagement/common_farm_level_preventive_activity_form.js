const { preventive_healthcare_activities, entity_type, classifiers, document_type, record_statuses } = require('../../ENUMS.js')
const { getClassificationConfiguration, loadClassificationDataWithClassifierIds } = require('../common/classification.helper.js')
const { dbConnections, preProcessRecords, postProcessRecords } = require('@krushal-it/ah-orm')
const { getCompiledQuery } = require('../../queries/index.js')
const {handleCattleLevelUpdate, handleFarmLevelUpdate} = require('./utils.js')
const _ = require('lodash')
const { ActivityHelper } = require('../../utils/activity.js')
const { getServiceCost, getMedicineCost, getActivityCostMap } = require('../serviceDocument/controller.js')

const farmerToCattleActivityMap = {}
farmerToCattleActivityMap[preventive_healthcare_activities.FARM_TICK_CONTROL_SPRAYING] = preventive_healthcare_activities.TICK_CONTROL

module.exports = function common_farm_level_preventive_activity_form(dbManager) {
    return {
        async update(data, token) {
            return await dbManager.transaction(async (transactionalEntityManager) => {
                try {        

                    let servicedCattleCount = 0;
                    const { animal_data, care_calendar } = data

                    if (!care_calendar) 
                        throw new Error('Bad Request. Missing care_calendar_id')

                    if (!_.isArray(animal_data))
                        throw new Error('Bad Request. Missing cattle data')

                    const care_calendar_data = await transactionalEntityManager.getRepository('care_calendar').findOne({
                        where: [
                            {
                                care_calendar_id: care_calendar,
                            },
                        ],
                    })
                    
                    if(!care_calendar_data) {
                        throw new Error('Invalid care_calendar_id.')
                    }

                    // get all classification data for the requested care calendar id
                    const calendarClassificationConfAttrArray = Object.keys(getClassificationConfiguration()['CARE_CALENDAR_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP);
                    const careCalendarClassifications = await loadClassificationDataWithClassifierIds(transactionalEntityManager, 'CARE_CALENDAR_CLASSIFICATION', care_calendar, calendarClassificationConfAttrArray)
                    const careCalendarClassificationValues = careCalendarClassifications['data']

                    if (careCalendarClassificationValues['form_completion_date']) {
                        return { result: false, message: 'Form filled already!' }
                    }

                    // save completion date time to now
                    const form_completion_time_stamp = new Date().toJSON()

                    const care_calendar_classification_rows = [];
                    care_calendar_classification_rows.push({
                        classifier_id: classifiers.ACTIVITY_FORM_COMPLETION,
                        value_date: form_completion_time_stamp,
                        value_reference_id: care_calendar_data.activity_id,
                        last_modifying_user_id: token.user_id,
                        care_calendar_id: care_calendar,
                    });

                    // save form data
                    care_calendar_classification_rows.push({
                        classifier_id: classifiers.ACTIVITY_FORM_DATA,
                        value_json: data,
                        value_reference_id: care_calendar_data.activity_id,
                        last_modifying_user_id: token.user_id,
                        care_calendar_id: care_calendar,
                    });

                    const care_calendar_classification_entry = preProcessRecords(
                            dbConnections().main.entities['care_calendar_classification'], 
                            care_calendar_classification_rows,
                            { json_columns: [] })

                    await transactionalEntityManager.getRepository('care_calendar_classification').save(care_calendar_classification_entry)

                    if (care_calendar_data.entity_type_id === entity_type.CUSTOMER) {

                        const cattleActivityId = farmerToCattleActivityMap[care_calendar_data.activity_id] ? farmerToCattleActivityMap[care_calendar_data.activity_id] : care_calendar_data.activity_id
        
                        const returnValue = await handleFarmLevelUpdate(transactionalEntityManager, care_calendar_data, careCalendarClassificationValues, cattleActivityId, animal_data, form_completion_time_stamp, token);
                        servicedCattleCount = returnValue.servicedCattleCount

                    } else if (care_calendar_data.entity_type_id === entity_type.ANIMAL) {

                        const returnValue = await handleCattleLevelUpdate(transactionalEntityManager, care_calendar_data, careCalendarClassificationValues, _.head(animal_data), form_completion_time_stamp, token);
                        servicedCattleCount = returnValue.servicedCattleCount

                    } else {
                        throw new Error('Invalid entity type')
                    }

                    /* new payment process */
                    const activity_cost_map = await getActivityCostMap()
                    const service_cost = getServiceCost(care_calendar_data.activity_id, servicedCattleCount,activity_cost_map)
                    const medicine_cost = getMedicineCost(care_calendar_data.activity_id, servicedCattleCount, activity_cost_map)
                    await ActivityHelper.processPaymentDetails(transactionalEntityManager, care_calendar, service_cost, medicine_cost)
                    
                    return { result: true, message: 'Form is saved successfully!!' }
                } catch (error) {
                    throw new Error(error)
                }
            })
        },

        async get(care_calendar) {
            return await dbManager.transaction(async (transactionalEntityManager) => {
                
                if (!care_calendar) throw new Error('Missing care_calendar_id.')

                const care_calendar_data = await transactionalEntityManager.getRepository('care_calendar').findOne({
                    where: [
                        {
                            care_calendar_id: care_calendar,
                        },
                    ],
                })
                
                if(!care_calendar_data) {
                    throw new Error('Invalid care_calendar_id.')
                }

                // get all classification data for the requested care calendar id
                const calendarClasssificationConfAttrArray = Object.keys(getClassificationConfiguration()['CARE_CALENDAR_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP);
                const careCalendarClassifications = await loadClassificationDataWithClassifierIds(transactionalEntityManager, 'CARE_CALENDAR_CLASSIFICATION', care_calendar, calendarClasssificationConfAttrArray)
                const careCalendarClassificationValues = careCalendarClassifications['data']


                if (careCalendarClassificationValues['form_completion_date']) {
                    const documentEntry = await transactionalEntityManager.getRepository('document').findOne({
                        where: [
                            {
                                entity_1_entity_uuid: care_calendar,
                                document_type_id: document_type.SERVICE_DOCUMENT,
                                active: record_statuses.ACTIVE
                            }
                        ],
                        select: {
                            document_id: true,
                            document_information: true,
                            client_document_information: true
                        }
                    });

                    return {
                        result: careCalendarClassificationValues['saved_activity_form_data'],
                        service_document: postProcessRecords(undefined, documentEntry, { json_columns: ['document_information', 'client_document_information'] }),
                        form_completion_date: careCalendarClassificationValues['form_completion_date'],
                    }            
                } else {
                
                    let activity_id = care_calendar_data.activity_id;
                    let animal_ids = [];

                    if (care_calendar_data.entity_type_id === entity_type.CUSTOMER) {

                        animal_ids = await transactionalEntityManager.getRepository('entity_relationship').find({
                            where: [
                                {
                                    entity_1_entity_uuid: care_calendar_data.entity_uuid,
                                },
                            ],
                            select: {
                                entity_2_entity_uuid: true
                            }
                        })
                        animal_ids = animal_ids.map(item => item.entity_2_entity_uuid);
                        
                        if (farmerToCattleActivityMap[activity_id]) {
                            activity_id = farmerToCattleActivityMap[activity_id]
                        }

                    } else if (care_calendar_data.entity_type_id === entity_type.ANIMAL) {

                        animal_ids.push(care_calendar_data.entity_uuid)

                    } else {

                        throw new Error('Invalid entity type')
                    }

                    let query = getCompiledQuery("animal-details-for-preventive-task");

                    const animalData = await dbConnections().main.manager.createQueryBuilder().from(`(${query})`, 'preventive-animal-list').setParameters({
                        animal_ids: animal_ids,
                        activity_id: activity_id
                    }).getRawMany();

                    return {
                        result: postProcessRecords(undefined, animalData, { json_columns: ['animal_type_l10n'] }),
                        service_document: undefined,
                        form_completion_date: undefined,
                    }
                }
            })
        }
    }
}