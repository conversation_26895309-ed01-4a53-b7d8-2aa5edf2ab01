const { getCompiledQuery } = require('../../queries/index.js')
const { staff_activity_statuses, classifiers, record_statuses, yes_no_values, reproductive_activities, document_type } = require('../../ENUMS.js')
const { saveClassificationData, loadClassificationData } = require('../common/classification.helper.js')
const { getCareCalendarInfo } = require('./utils.js')
const { preProcessRecords, postProcessRecords, dbConnections } = require('@krushal-it/ah-orm')
const { propagateActivityStatus } = require('../activity_routeplan/controller.js')
const { getActivityCostMap, getMedicineCost, getServiceCost } = require('../serviceDocument/controller.js')
const { ActivityHelper } = require('../../utils/activity.js')

module.exports = function artificial_insemination_activity(dbManager) {
  return {

    async update(data, token) {

      const form_completion_time_stamp = new Date().toJSON()

      const { 
        care_calendar, 
        ai_form_heat_date_and_time, 
        ai_form_id_of_bull, 
        ai_form_company_of_semen, 
        ai_form_name_of_bull, 
        ai_task_completion_status,
        ai_form_company_name_of_semen_for_others,
        discharges_clear_transparent_with_oily_viscosity,
        temperature,
        uterine_tone_normal_and_good,
        cervix_normal,
        was_digital_ai_gun_used,
        vulva_lips_oedematous_and_pink,
        heat_symptoms_mounting_behavior,
      } = data

      if (!care_calendar) throw new Error('Missing care_calendar_id.')

      return await dbManager.transaction(async (transactionalEntityManager) => {
        try {
          
          const care_calendar_info = await getCareCalendarInfo(care_calendar, classifiers.ACTIVITY_FORM_COMPLETION, transactionalEntityManager)
          const form_completion_date = care_calendar_info.length > 0 ? care_calendar_info[0].value_date : undefined
          if (form_completion_date) {
            return { result: false, message: 'Form filled already!' }
          }


          const activity_cost_map = await getActivityCostMap()
          const service_cost = getServiceCost(reproductive_activities.AI_PENDING, 1,activity_cost_map)
          let medicine_cost = getMedicineCost(reproductive_activities.AI_PENDING,1, activity_cost_map)

          
          let formClassificationData = undefined;

          if (ai_task_completion_status[0] === yes_no_values.NO) {
            medicine_cost=0
            // cancel associated PD task
            await propagateActivityStatus(care_calendar, staff_activity_statuses.VISIT_CANCELLED, transactionalEntityManager);
            formClassificationData = {
              ai_task_completion_status: yes_no_values.NO,
              discharges_clear_transparent_with_oily_viscosity,
              temperature,
              uterine_tone_normal_and_good,
              cervix_normal,
              was_digital_ai_gun_used,
              vulva_lips_oedematous_and_pink,
              heat_symptoms_mounting_behavior,
            }
          } else if (ai_task_completion_status[0] === yes_no_values.YES) {

            if (
              !ai_form_company_of_semen || 
              !ai_form_id_of_bull || 
              !ai_form_name_of_bull || 
              !ai_task_completion_status
            ) 
            throw new Error('Bad Request..')

            formClassificationData = {
              ai_form_company_of_semen: ai_form_company_of_semen && ai_form_company_of_semen[0],
              ai_form_id_of_bull: ai_form_id_of_bull,
              ai_form_name_of_bull: ai_form_name_of_bull,
              ai_task_completion_status: yes_no_values.YES,
              ai_form_company_name_of_semen_for_others: ai_form_company_name_of_semen_for_others,
              discharges_clear_transparent_with_oily_viscosity,
              temperature,
              uterine_tone_normal_and_good,
              cervix_normal,
              was_digital_ai_gun_used,
              vulva_lips_oedematous_and_pink,
              heat_symptoms_mounting_behavior,
            }            
          } else {
            throw new Error('Invalid Request.')
          }

          await saveClassificationData(transactionalEntityManager, 'CARE_CALENDAR_CLASSIFICATION', care_calendar, formClassificationData)

          const form_completion_classifier_entry = preProcessRecords(
            dbConnections().main.entities['care_calendar_classification'],
            {
              classifier_id: classifiers.ACTIVITY_FORM_COMPLETION,
              value_date: form_completion_time_stamp,
              value_reference_id: reproductive_activities.AI_PENDING,
              last_modifying_user_id: token.user_id,
              care_calendar_id: care_calendar,
            },
            { json_columns: [] }
          )
          

          await transactionalEntityManager.getRepository('care_calendar_classification').save(form_completion_classifier_entry)
          await ActivityHelper.processPaymentDetails(transactionalEntityManager, care_calendar, service_cost, medicine_cost)

          return { result: true, message: 'saved!', form_completion_time_stamp }
        } catch (error) {
          throw error
        }
      })
    },

    async get(care_calendar) {
    
      const result = await loadClassificationData('CARE_CALENDAR_CLASSIFICATION', care_calendar, 
        [
          'ai_form_heat_date_and_time', 
          'ai_form_company_of_semen', 
          'ai_form_id_of_bull', 
          'ai_form_name_of_bull', 
          'ai_task_completion_status',
          'ai_form_company_name_of_semen_for_others',
          "discharges_clear_transparent_with_oily_viscosity",
          "temperature",
          "uterine_tone_normal_and_good",
          "cervix_normal",
          "was_digital_ai_gun_used",
          "vulva_lips_oedematous_and_pink",
          "heat_symptoms_mounting_behavior",
        ])

      const documentEntry = await dbConnections().main.repos['document'].findOne({
        where: [
          {
            entity_1_entity_uuid: care_calendar,
            document_type_id: document_type.SERVICE_DOCUMENT,
            active: record_statuses.ACTIVE,
          },
        ],
        select: {
          document_id: true,
          document_information: true,
          client_document_information: true,
        },
      })

      result['service_document'] = postProcessRecords(undefined, documentEntry, { json_columns: ['document_information', 'client_document_information'] });
      if (result.length < 0) {
        return { result: false, message: 'No data found!' }
      }

      return postProcessRecords(undefined, result, { json_columns: [] })
    },
  }
}
