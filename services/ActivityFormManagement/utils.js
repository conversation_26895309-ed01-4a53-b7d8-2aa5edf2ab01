const { entity_type, classifiers, staff_activity_statuses, record_statuses } = require('../../ENUMS.js')
const { saveClassificationData } = require('../common/classification.helper.js')
const { CareCalendarQueries } = require('../../queries/care-calendar.js')
const { dbConnections, preProcessRecords } = require('@krushal-it/ah-orm')
const taskRepository = require('../../repositories/taskRepository.js')
const { RELATION_CALENDAR, RELATION_CALENDAR_CLASSIFICATION } = require('../../utils/constant.js')

module.exports = {
  async getCareCalendarInfo(care_calendar, classifier_id, transactionalEntityManager) {
    try {
      const form_completion_query = `
            select
                cc.entity_type_id,
                cc.calendar_activity_status,
                case
                    when entity_type_id = 1000460002 
                    then (
                        select
                            er.entity_1_entity_uuid
                        from
                            main.entity_relationship er
                        where
                            er.entity_2_entity_uuid = cc.entity_uuid
							and
							er.entity_relationship_type_id = 1000210004
							and
							er.active = 1000100001                            
                    )
                    else 
                        cc.entity_uuid
                end customer_id,
                cc.entity_uuid,
                cc.activity_id,
                cc.activity_date,
                cc.visit_schedule_time,
                cc.visit_creation_time,
                ccc.value_date as form_completion_date
            from
                main.care_calendar cc
                left join main.care_calendar_classification ccc on ccc.classifier_id = ${classifiers.ACTIVITY_FORM_COMPLETION}
                and ccc.care_calendar_id = cc.care_calendar_id
                and ccc.active = ${record_statuses.ACTIVE}
            where
                cc.care_calendar_id = :care_calendar
            `

      const calendarInfo = await transactionalEntityManager.createQueryBuilder().select('*').from(`(${form_completion_query})`, 'table1').setParameters({ classifier_id, care_calendar, active: record_statuses.ACTIVE }).execute()

      return calendarInfo
    } catch (error) {
      console.log('getCareCalendarInfo', JSON.stringify(error, undefined, 4))
      throw error
    }
  },

  async handleFarmLevelUpdate(transactionalEntityManager, care_calendar_data, careCalendarClassificationValues, cattleActivityId, animal_data, form_completion_time_stamp, token) {
    let servicedCattleCount = 0
    // apply cattle level care calendar changes as per the selections of cattles on the form
    const child_care_calendar_classification = {
      linked_care_calendar_task: care_calendar_data.care_calendar_id,
    }

    for (let key of Object.keys(careCalendarClassificationValues)) {
      child_care_calendar_classification[key] = careCalendarClassificationValues[key]
    }

    for (let animal of animal_data) {
      if (animal.activity_status == staff_activity_statuses.VISIT_COMPLETED) {
        ++servicedCattleCount
      }
      // create an entry for each cattle as per the selections in the form
      const calendarEntry = {
        activity_date: care_calendar_data.activity_date,
        completion_date: form_completion_time_stamp,
        entity_uuid: animal.id,
        entity_type_id: entity_type.ANIMAL,
        activity_id: cattleActivityId,
        calendar_activity_status: animal.activity_status, // completed or cancelled
        visit_schedule_time: care_calendar_data.visit_schedule_time,
        visit_creation_time: care_calendar_data.visit_creation_time,
        activity_created_by: token.user_id,
        activity_completed_by: token.user_id,
        system_task_completion_time: form_completion_time_stamp,
        user_task_completion_time: form_completion_time_stamp,
      }
      let calendarId = await CareCalendarQueries.insertcalendarEntries(calendarEntry, { datasource: transactionalEntityManager })
      calendarId = calendarId.identifiers[0].care_calendar_id

      await saveClassificationData(transactionalEntityManager, 'CARE_CALENDAR_CLASSIFICATION', calendarId, child_care_calendar_classification)

      const child_care_calendar_classification_rows = []
      child_care_calendar_classification_rows.push({
        classifier_id: classifiers.ACTIVITY_FORM_COMPLETION,
        value_date: form_completion_time_stamp,
        value_reference_id: cattleActivityId,
        last_modifying_user_id: token.user_id,
        care_calendar_id: calendarId,
      })

      const child_form_data = { care_calendar: calendarId, animal_data: [] }
      child_form_data.animal_data.push(animal)

      child_care_calendar_classification_rows.push({
        classifier_id: classifiers.ACTIVITY_FORM_DATA,
        value_json: child_form_data,
        value_reference_id: cattleActivityId,
        last_modifying_user_id: token.user_id,
        care_calendar_id: calendarId,
      })

      const child_care_calendar_classification_entry = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], child_care_calendar_classification_rows, { json_columns: [] })

      await transactionalEntityManager.getRepository('care_calendar_classification').save(child_care_calendar_classification_entry)

      // if the task is marked as cancelled and has been requested for a reschedule
      if (animal.activity_status == staff_activity_statuses.VISIT_CANCELLED && animal.reschedule === 1) {
        const reschedledTaskDate = new Date()
        reschedledTaskDate.setDate(reschedledTaskDate.getDate() + 7)

        const rescheduledTaskEntry = {
          activity_date: reschedledTaskDate.toJSON(),
          entity_uuid: animal.id,
          entity_type_id: entity_type.ANIMAL,
          activity_id: cattleActivityId,
          calendar_activity_status: staff_activity_statuses.NEW_CASE_VISIT_PENDING,
          visit_schedule_time: reschedledTaskDate.toJSON(),
          visit_creation_time: reschedledTaskDate.toJSON(),
          activity_created_by: token.user_id,
          activity_completed_by: token.user_id,
        }

        await CareCalendarQueries.insertcalendarEntries(rescheduledTaskEntry, { datasource: transactionalEntityManager })
      }
    }

    return {
      servicedCattleCount,
    }
  },

  async handleCattleLevelUpdate(transactionalEntityManager, care_calendar_data, careCalendarClassificationValues, animal, form_completion_time_stamp, token) {
    let servicedCattleCount = 0
    if (animal.activity_status == staff_activity_statuses.VISIT_COMPLETED) {
      ++servicedCattleCount
    }
    // update the status of the care calendar based on the request
    care_calendar_data.calendar_activity_status = animal.activity_status
    care_calendar_data.completion_date = form_completion_time_stamp
    care_calendar_data.activity_completed_by = token.user_id
    care_calendar_data.system_task_completion_time = form_completion_time_stamp
    care_calendar_data.user_task_completion_time = form_completion_time_stamp

    const preprocessed_care_calendar_data = preProcessRecords(dbConnections().main.entities['care_calendar'], care_calendar_data, { json_columns: [] })
    await transactionalEntityManager.getRepository('care_calendar').save(preprocessed_care_calendar_data)

    // if the task is marked as cancelled and has been requested for a reschedule
    if (animal.activity_status == staff_activity_statuses.VISIT_CANCELLED && animal.reschedule === 1) {
      const reschedledTaskDate = new Date()
      reschedledTaskDate.setDate(reschedledTaskDate.getDate() + 7)

      const rescheduledTaskEntry = {
        activity_date: reschedledTaskDate.toJSON(),
        entity_uuid: care_calendar_data.entity_uuid,
        entity_type_id: entity_type.ANIMAL,
        activity_id: care_calendar_data.activity_id,
        calendar_activity_status: staff_activity_statuses.NEW_CASE_VISIT_PENDING,
        visit_schedule_time: reschedledTaskDate.toJSON(),
        visit_creation_time: reschedledTaskDate.toJSON(),
        activity_created_by: token.user_id,
        activity_completed_by: token.user_id,
      }

      await CareCalendarQueries.insertcalendarEntries(rescheduledTaskEntry, { datasource: transactionalEntityManager })
    }
    return {
      servicedCattleCount,
    }
  },

  // this function takes a farm level task as an input and closes/updates all the related cattle level task
  async closeRelatedCattleTask(transactionalEntityManager, farm_task_id) {
    try {
        
      // get the details of the farm level task
      const farmTaskData = await taskRepository.getTaskPrimaryData(transactionalEntityManager, farm_task_id, {
        entity_type_id: entity_type.CUSTOMER,
      })

      if (!farmTaskData[farm_task_id]) {
        return
      }

      // find all the related cattle tasks
      const relatedCattleTaskResult = await taskRepository.getTaskClassificationData(transactionalEntityManager, undefined, classifiers.LINKED_CARE_CALENDAR_TASK, {
        value_reference_uuid: farm_task_id,
      })
      
      // get the details of the cattle task
      const relatedCattleTaskIds = Object.keys(relatedCattleTaskResult)
      const relatedTaskData = await taskRepository.getTaskPrimaryData(transactionalEntityManager, relatedCattleTaskIds, {
        entity_type_id: entity_type.ANIMAL,
      })

      let recordsToUpdate = [];
      // update the time details of the cattle task with farm task data
      for (const key of Object.keys(relatedTaskData)) {
        relatedTaskData[key].completion_date = farmTaskData[farm_task_id].completion_date;
        relatedTaskData[key].system_task_completion_time = farmTaskData[farm_task_id].system_task_completion_time;
        relatedTaskData[key].user_task_completion_time = farmTaskData[farm_task_id].user_task_completion_time;
        recordsToUpdate.push(relatedTaskData[key]);
      }

      await taskRepository.updateTask(transactionalEntityManager, recordsToUpdate)
    } catch (error) {
      console.log('error in closing related cattle tasks', error)
    }
  },
}
