const { preventive_healthcare_activities, classifiers, record_statuses, document_type } = require('../../ENUMS.js')
const { saveClassificationData, loadClassificationData, getClassificationConfiguration } = require('../../services/common/classification.helper.js')
const { preProcessRecords,postProcessRecords, dbConnections } = require('@krushal-it/ah-orm')
const moment = require("moment")
const { getActivityCostMap, getServiceCost, getMedicineCost } = require('../serviceDocument/controller.js')
const { ActivityHelper } = require('../../utils/activity.js')

module.exports = function farm_management_activity(dbManager) {

    return {
        async update(data, token) {
            return await dbManager.transaction(async (transactionalEntityManager) => {
                
                const { care_calendar_id, farm_data } = data
                
                if (!care_calendar_id) {
                    throw new Error('Missing data! care_calendar_id is missing')
                }

                if (!farm_data) { 
                    throw new Error('Missing data! farm_data is missing, invalid structure or empty')
                }

                let dataKeys = Object.keys(farm_data)
                const classificationConfig = getClassificationConfiguration().CARE_CALENDAR_CLASSIFICATION.CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
                const care_calendar_classification = {
                    farm_management_form_completed_on: moment.utc().format("YYYY-MM-DD HH:mm:ss.SSS"),
                }
                for (let datakeyIndex = 0; datakeyIndex < dataKeys.length; datakeyIndex++) {
                  let key = dataKeys[datakeyIndex]
                  if (classificationConfig[key]) {
                    care_calendar_classification[key] = farm_data[key]
                  }
                } 

                const form_completion_time_stamp = new Date().toJSON();

                let form_completion_entry = await transactionalEntityManager.getRepository('care_calendar_classification').findOne({
                    where: {
                        classifier_id: classifiers.ACTIVITY_FORM_COMPLETION,
                        value_reference_id: preventive_healthcare_activities.FARM_MANAGEMENT,
                        care_calendar_id: care_calendar_id,
                    },
                })
                
                if(!form_completion_entry) {
                    form_completion_entry = {
                        classifier_id: classifiers.ACTIVITY_FORM_COMPLETION,
                        value_date: form_completion_time_stamp,
                        value_reference_id: preventive_healthcare_activities.FARM_MANAGEMENT,
                        last_modifying_user_id: token.user_id,
                        care_calendar_id: care_calendar_id,
                      }
                }
                
                
                form_completion_entry = preProcessRecords(
                    dbConnections().main.entities['care_calendar_classification'],
                    form_completion_entry, { json_columns: [] }
                  )
        
                await transactionalEntityManager.getRepository('care_calendar_classification').save(form_completion_entry)
                

                const activity_cost_map = await getActivityCostMap()
                const service_cost = getServiceCost(preventive_healthcare_activities.FARM_MANAGEMENT, 0,activity_cost_map)
                const medicine_cost = getMedicineCost(preventive_healthcare_activities.FARM_MANAGEMENT,0, activity_cost_map)
                await ActivityHelper.processPaymentDetails(transactionalEntityManager, care_calendar_id, service_cost, medicine_cost)
                
        
                return {
                    result: await saveClassificationData(transactionalEntityManager, 'CARE_CALENDAR_CLASSIFICATION', care_calendar_id, care_calendar_classification)
                }
            })
        },

        async get(data, token) {
            const { care_calendar_id, classifiers } = data
            if (!care_calendar_id || !classifiers) {
                throw new Error('Missing data! care_calendar_id or classifiers is missing')
            }
            const documentEntry = await dbConnections().main.repos['document'].findOne({
                where: [
                  {
                    entity_1_entity_uuid: care_calendar_id,
                    document_type_id: document_type.SERVICE_DOCUMENT,
                    active: record_statuses.ACTIVE,
                  },
                ],
                select: {
                  document_id: true,
                  document_information: true,
                  client_document_information: true,
                },
            })
            
            const service_document = postProcessRecords(undefined, documentEntry, { json_columns: ['document_information', 'client_document_information'] })

            
            return {
                service_document,
                classification_data: await loadClassificationData('CARE_CALENDAR_CLASSIFICATION', care_calendar_id, classifiers)
            }
        }

    }
}