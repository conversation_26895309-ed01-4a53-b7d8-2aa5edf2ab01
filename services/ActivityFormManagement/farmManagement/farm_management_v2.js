const { preProcessRecords, postProcessRecords, dbConnections } = require('@krushal-it/ah-orm')
const qHooks = require("./hooks/index")
const { Not, IsNull } = require("typeorm")
const {
  record_statuses,
  entity_type,
  farm_management_question_type,
  farm_management_question_key,
  farm_management_priority_grouping,
  farm_management_frequency_grouping_name,
  document_type,
  farm_management_frequency_grouping_name_l10n
} = require("../../../ENUMS")
const documentRepositoryInstance = require('../../../repositories/documentRepository')
const referenceRepository = require('../../../repositories/referenceRepository')

class FarmManagement {

  loadQuestions = async (cc_id) => {
    try {


      let taskQuestions = await dbConnections().main.repos['task_question'].find({
        where: {
          active: record_statuses.ACTIVE
        },
        select: {
          question_uuid: true,
          question_name: true,
          topic_question_priority: true,
          priority_grouping: true,
          topic_name: true,
          review_date_config: true,
          farmer_input_required: true,
          topic_priority: true,
          question_name_l10n: true,
          topic_name_l10n: true
        }
      })
      taskQuestions = postProcessRecords(undefined, taskQuestions, { json_columns: ['question_name_l10n', 'topic_name_l10n'] })

      let document = await dbConnections().main.repos['document'].findOne({
        where: {
          active: record_statuses.ACTIVE,
          document_type_id: document_type.FARM_MANAGEMENT_PDF,
          entity_1_entity_uuid: cc_id
        },
        select: {
          document_information: true,
          client_document_information: true
        }
      })
      document = postProcessRecords(undefined, document, { json_columns: ['document_information', 'client_document_information'] })

      let taskQuestionData = await dbConnections().main.repos['task_question_data'].find({
        where: [
          {
            active: record_statuses.ACTIVE,
            priority_grouping: Not(IsNull()),
            care_calendar_id: cc_id
          },
          {
            active: record_statuses.ACTIVE,
            review_date: Not(IsNull()),
            care_calendar_id: cc_id

          },
          {
            active: record_statuses.ACTIVE,
            care_calendar_id: cc_id,
            saved_at: Not(IsNull())

          }
        ]
      })
      taskQuestionData = postProcessRecords(undefined, taskQuestionData, { json_columns: [] })

      const overidingTaskReviewDate = taskQuestions.map(obj1 => {
        const match = taskQuestionData.find(obj2 => obj2.question_uuid === obj1.question_uuid);
        return match ? { ...obj1, review_date: match.review_date, saved_at: match.saved_at, priority_grouping: match.priority_grouping ? match.priority_grouping : obj1.priority_grouping } : obj1;
      });
      const priorityGroup = [
        { 
          priority_grouping: farm_management_priority_grouping.priority_1, 
          name: farm_management_frequency_grouping_name.VISIT_PRIORITY, 
          priority_grouping_l10n: farm_management_frequency_grouping_name_l10n.VISIT_PRIORITY 
        },
        { 
          priority_grouping: farm_management_priority_grouping.priority_2, 
          name: farm_management_frequency_grouping_name.ADDITIONAL, 
          priority_grouping_l10n: farm_management_frequency_grouping_name_l10n.ADDITIONAL 
        },
      ];

      const result = priorityGroup.map(group => {
        const items = overidingTaskReviewDate.filter(item => item.priority_grouping === group.priority_grouping);
        const structure = this.createFolderStructure(items);
        const sum_questions = structure.reduce((sum, obj) => {
          return sum += obj.total_question
        }, 0)
        const sum_answers = structure.reduce((sum, obj) => {
          return sum += obj.total_answered
        }, 0)
        return {
          title: group.name,
          title_l10n: group.priority_grouping_l10n,
          status: `${sum_answers}/${sum_questions}`,
          folders: structure
        };
      });

      const careCalendar = await dbConnections().main.repos['care_calendar'].findOne({
        where: {
          care_calendar_id: cc_id
        },
        select: {
          calendar_activity_status: true
        }
      })
      return { data: result, calendar_activity_status: careCalendar?.calendar_activity_status, document: document }

    } catch (error) {
      throw new Error("Error while loading questions.", { cause: error });

    }
  }


  createFolderStructure = (data) => {
    try {
      const groupedData = data.reduce((acc, item) => {
        if (!acc[item.topic_name]) {
          acc[item.topic_name] = {
            topic_priority: item.topic_priority,
            question_name_l10n: item.question_name_l10n,
            topic_name_l10n: item.topic_name_l10n,
            questions: []
          };
        }
        acc[item.topic_name].questions.push({
          question_id: item.question_uuid,
          title: item.question_name,
          type: "question",
          topic_question_priority: item.topic_question_priority,
          review_date: item.review_date,
          saved_at: item.saved_at,
          farmer_input_required: item.farmer_input_required,
          question_name_l10n: item.question_name_l10n,
          topic_name_l10n: item.topic_name_l10n
        });
        return acc;
      }, {});

      return Object.keys(groupedData)
        .map(topic => {
          const total_answered = groupedData[topic].questions.reduce((count, obj) => {
            if (obj.saved_at) {
              return count += 1
            }
            return count
          }, 0)
          const total_question = groupedData[topic].questions.length;
          return {
            title: topic,
            title_l10n: groupedData[topic].topic_name_l10n,
            topic_priority: groupedData[topic].topic_priority,
            question_name_l10n: groupedData[topic].question_name_l10n,
            total_question: total_question,
            total_answered: total_answered,
            status: `${total_answered}/${total_question}`,
            folders: groupedData[topic].questions.sort((a, b) => {
              if (a.review_date && b.review_date) {
                return new Date(a.review_date) - new Date(b.review_date);
              } else if (a.review_date) {
                return -1;
              } else if (b.review_date) {
                return 1;
              } else {
                return a.topic_question_priority - b.topic_question_priority;
              }
            }),
          }
        })
        .sort((a, b) => a.topic_priority - b.topic_priority);
    } catch (error) {
      throw new Error("Error while creating folder structure.", { cause: error });
    }

  }


  getTargetByQuestionId = async (question_id, care_calendar_id) => {
    try {
      let existCheck = await dbConnections().main.repos['task_question_data'].findOne({
        where: {
          question_uuid: question_id,
          care_calendar_id: care_calendar_id,
          active: record_statuses.ACTIVE
        }
      })
      existCheck = postProcessRecords(undefined, existCheck, { json_columns: ["form_configuration"] })

      let taskQuestions = await dbConnections().main.repos['task_question'].findOne({
        select: {
          form_configuration: true,
          question_name: true,
          question_name_l10n: true,
          question_uuid: true,
          farmer_input_required: true,
          topic_name_l10n: true
        },
        where: {
          active: record_statuses.ACTIVE,
          question_uuid: question_id
        }
      })
      taskQuestions = postProcessRecords(undefined, taskQuestions, { json_columns: ["question_name_l10n", 'form_configuration'] })

      if (existCheck) {

        taskQuestions["question_data_uuid"] = existCheck.question_data_uuid

        taskQuestions["form_configuration"] = this.copyValuesOnly(existCheck.form_configuration, taskQuestions.form_configuration)
      }

      const _transformation = qHooks[question_id]
        
      try {
        if(!existCheck?.saved_at) {
          await _transformation(taskQuestions.form_configuration, care_calendar_id)
        }
      } catch (error) {
        console.log("transformation failed to populate target value for daily ration")
      }

      const question_sorting = {
        "1": farm_management_question_key.CURRENT,
        "2": farm_management_question_key.TARGET,
        "3": farm_management_question_key.TARGET_DATE,
        "4": farm_management_question_key.ADVICE,
        "5": farm_management_question_key.NOTES,
        "6": farm_management_question_key.MEDIA
      }
      taskQuestions["form_configuration"] = this.sortFormFields(taskQuestions.form_configuration, question_sorting)
      return taskQuestions
    } catch (error) {
      throw new Error("Error while getting question details.", { cause: error });

    }
  }

  sortFormFields = (formData, sortingObj) => {
    const sortedKeys = Object.values(sortingObj); // Extract the sorted keys in the desired order

    // Sort the form fields array based on the sorted keys
    formData.form_fields.sort((a, b) => {
      return sortedKeys.indexOf(a.key) - sortedKeys.indexOf(b.key);
    });

    return formData;
  };
  
  saveAnswers = async (question_id, care_calendar_id, data) => {
    try {
      return await dbConnections().main.manager.transaction(async (transaction) => {
        let form_configuration = data.form_configuration
        let referenceScore = null
        const entity_task_question_data = dbConnections().main.entities["task_question_data"]
        
        // process unit component
        const unitCurrentComponent = data.form_configuration?.form_fields?.find((state) => state.type === farm_management_question_type.UNIT && state.key === farm_management_question_key.CURRENT)
        const unitTargetComponent = data.form_configuration?.form_fields?.find((state) => state.type === farm_management_question_type.UNIT && state.key === farm_management_question_key.TARGET)

        if(unitCurrentComponent && unitTargetComponent) {
          
          const unitCurrentValue = parseFloat(unitCurrentComponent?.selections?.values)
          const unitTargetValue = parseFloat(unitTargetComponent?.selections?.values)

          if(!isNaN(unitCurrentValue) && !isNaN(unitTargetValue) && unitTargetValue > 0) {
            referenceScore = Math.min(unitCurrentValue/unitTargetValue, 1);
            referenceScore = referenceScore.toFixed(2)
          }
        }

        // Advanced Select data
        const advancedSelectComponent = data.form_configuration?.form_fields?.find((state) => state.type === farm_management_question_type.ADVANCED_SELECT && state.key === farm_management_question_key.CURRENT)
        if (advancedSelectComponent && advancedSelectComponent?.selections?.values.length) {
          const targetComponent = data.form_configuration?.form_fields?.find((state) => state.key === farm_management_question_key.TARGET)
          let targetValue = null
          if (targetComponent && targetComponent?.selections?.values?.length) {
            targetValue = targetComponent?.selections?.values[0]
          }
          const reference = await referenceRepository.getReferenceDetailsByCategoryId(transaction, advancedSelectComponent?.category_id)
          const referenceWithWeightage = this.calculateWeightage(reference, targetValue)
          referenceScore = referenceWithWeightage[advancedSelectComponent?.selections?.values[0]]?.weightage ?? 0
          referenceScore = referenceScore > 1 ? 1 : referenceScore
        }
        
        // Media data
        let documentValues = []        
        const mediaComponent = data.form_configuration?.form_fields?.find((state) => state.type === farm_management_question_type.MEDIA)
        if (mediaComponent && mediaComponent?.selections?.values[0]?.value?.length) {
          const documentExistDB = mediaComponent?.selections?.values[0]?.value.filter(document => document.document_id)
          const newDocument = mediaComponent?.selections?.values[0]?.value.filter(document => !document.document_id)
          const insertDocuments = []
          for (let document of newDocument) {
            let value = {
              document_type_id: document_type.FARM_MANAGEMENT_DOCUMENT,
              entity_1_type_id: entity_type.TASK,
              entity_1_entity_uuid: care_calendar_id,
              document_information: document
            }
            const document_id = await documentRepositoryInstance.insertToDocument(value, transaction)
            document["document_id"] = Array.isArray(document_id) ? document_id[0] : document_id
            insertDocuments.push(document)
          }
          documentValues = documentExistDB ? documentExistDB.concat(insertDocuments) : insertDocuments
          form_configuration.form_fields.forEach(field => {
            if (field.key === "image" && field.type === "media") {
              field.selections.values[0].value = documentValues;
            }
          });
        }


        let getConfig = await transaction.getRepository("task_question").findOne({
          where: {
            question_uuid: question_id,
            active: record_statuses.ACTIVE
          },
          select: {
            weightage: true
          }
        })
        getConfig = postProcessRecords(undefined, getConfig, { json_columns: [] })

        let taskQuestionsData = {
          care_calendar_id: care_calendar_id,
          question_uuid: question_id,
          form_configuration: form_configuration,
          saved_at: new Date().toISOString(),
          score: referenceScore,
          weightage: getConfig?.weightage
        }
        taskQuestionsData = preProcessRecords(entity_task_question_data, taskQuestionsData, { json_columns: ["form_configuration"] })

        let getExistData = await transaction.getRepository("task_question_data").findOne({
          where: {
            care_calendar_id: care_calendar_id,
            question_uuid: question_id,
            active: record_statuses.ACTIVE
          }
        })
        if (getExistData) {
          taskQuestionsData.question_data_uuid = getExistData.question_data_uuid
        }
        return await transaction.getRepository("task_question_data").save(taskQuestionsData);

      })
    } catch (error) {
      throw new Error("Error while saving answers.", { cause: error });

    }

  }
  
  deepCopy = (obj) => {
    return JSON.parse(JSON.stringify(obj));
  }

  copyValuesOnly = (source, target) => {
    const sourceMap = source.form_fields.reduce((map, item) => {
      map[item.key] = item.selections.values;
      return map;
    }, {});

    // Update target form_fields with values from source
    const updatedTarget = {
      form_name: target.form_name,
      form_fields: target.form_fields.map(item => {
        if (sourceMap.hasOwnProperty(item.key)) {
          // Copy the values from source to target
          return {
            ...item,
            selections: {
              ...item.selections,
              values: [...sourceMap[item.key]]
            }
          };
        }
        return item; // Return item as is if no matching key
      })
    };

    return updatedTarget;
  };

  calculateWeightage = (reference, targetValue) => {
    const categoryScores = {};
    let maxScore = 0;
    if (targetValue) {
      maxScore = reference[targetValue]?.reference_information?.farm_management?.score
    }
    // First pass: Group the scores by reference_category_id and find the maximum score
    for (const key in reference) {
      const item = reference[key];
      const categoryId = item.reference_category_id;

      if (!categoryScores[categoryId]) {
        categoryScores[categoryId] = {
          maxScore: 0,
          items: [],
          hasScore: false,
        };
      }
      if (item.reference_information && item.reference_information?.farm_management && item.reference_information?.farm_management?.score) {
        const score = item.reference_information?.farm_management?.score;

        categoryScores[categoryId].maxScore = maxScore ? maxScore : Math.max(categoryScores[categoryId].maxScore, score);
        categoryScores[categoryId].hasScore = true;
      }

      categoryScores[categoryId].items.push({ id: item.reference_id, score: item.reference_information?.farm_management?.score || null });
    }
    console.log("categoryScores", categoryScores, "reference", reference)
    // Second pass: Calculate the weightage for each item by its reference_id
    const result = {};

    for (const categoryId in categoryScores) {
      const maxScore = categoryScores[categoryId].maxScore;
      const hasScore = categoryScores[categoryId].hasScore;

      categoryScores[categoryId].items.forEach((item) => {
        let weightage = 0;

        if (item.score !== null && hasScore) {
          weightage = (item.score / maxScore);
        } else if (!hasScore) {
          // If no scores exist for this category, set the weightage to 100
          weightage = 0;
        }

        result[item.id] = {
          score: item.score || 0,
          weightage: parseFloat(weightage.toFixed(2)), // Keep weightage to 2 decimal places
        };
      });
    }

    return result;
  }
}

module.exports = { FarmManagement }