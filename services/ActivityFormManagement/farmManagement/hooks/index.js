const {  postProcessRecords, dbConnections } = require('@krushal-it/ah-orm')
const { record_statuses, document_type } = require('../../../../ENUMS')

module.exports = {
  'aa941d32-6c1e-11ef-9931-dff3956cdce3': async function applyDailyDry(form_configuration, care_calendar_id) {
    const feed = await getDailyRation(care_calendar_id)
    form_configuration.form_fields.forEach(field => {
      if (field.key === 'target' && feed.daily_dry>0) {
        field.selections.values = [feed.daily_dry]
      }
    })
  },
  '2a14640a-6c1e-11ef-992f-1ba94e30ccba': async function applyDailyGreen(form_configuration, care_calendar_id) {
    const feed = await getDailyRation(care_calendar_id)
    form_configuration.form_fields.forEach(field => {
      if (field.key === 'target' && feed.daily_green>0) {
        field.selections.values = [feed.daily_green]
      }
    })
  },
  '4bb768fe-6c1f-11ef-9934-1f716160404c': async function applyDailyMineralMixture(form_configuration, care_calendar_id) {
    const feed = await getDailyRation(care_calendar_id)
    form_configuration.form_fields.forEach(field => {
      if (field.key === 'target' && feed.daily_mineral_mixture>0) {
        field.selections.values = [feed.daily_mineral_mixture]
      }
    })
  },
  'd7c58ab6-6c1e-11ef-9932-57930c9fc92a': async function applyDailyCompoundFeed1(form_configuration, care_calendar_id) {
    const feed = await getDailyRation(care_calendar_id)
    form_configuration.form_fields.forEach(field => {
      if (field.key === 'target' && feed.daily_compound_feed1>0) {
        field.selections.values = [feed.daily_compound_feed1]
      }
    })
  },
  'fb00eaac-6c1e-11ef-9933-27b5be39b487': async function applyDailyCompoundFeed2(form_configuration, care_calendar_id) {
    const feed = await getDailyRation(care_calendar_id)

    form_configuration.form_fields.forEach(field => {
      if (field.key === 'target' && feed.daily_compound_feed2>0) {
        field.selections.values = [feed.daily_compound_feed2]
      }
    })
  }
}

async function getDailyRation(careCalendarId) {
  const query = `
        WITH customer AS (
            SELECT
                CASE
                    WHEN CC.ENTITY_TYPE_ID = 1000460001 THEN (
                        SELECT
                            CUSTOMER.CUSTOMER_ID
                        FROM
                            MAIN.CUSTOMER
                        WHERE
                            CUSTOMER.CUSTOMER_ID = CC.ENTITY_UUID
                    )
                    ELSE (
                        SELECT
                            CUSTOMER.CUSTOMER_ID
                        FROM
                            MAIN.ANIMAL
                            INNER JOIN MAIN.ENTITY_RELATIONSHIP ER 
                                ON ER.ENTITY_2_ENTITY_UUID = CC.ENTITY_UUID
                                AND ER.ENTITY_RELATIONSHIP_TYPE_ID = 1000210004
                            INNER JOIN MAIN.CUSTOMER 
                                ON CUSTOMER.CUSTOMER_ID = ER.ENTITY_1_ENTITY_UUID
                    )
                END AS CUSTOMER_ID,
                CC.ENTITY_UUID
            FROM
                MAIN.CARE_CALENDAR CC
            WHERE
                CC.care_calendar_id = $1
        )
        SELECT 
            cc.value_json AS daily_feed
        FROM 
            customer
        INNER JOIN 
            MAIN.CUSTOMER_CLASSIFICATION cc 
            ON cc.classifier_id = 2000000485 
            AND cc.customer_id = customer.CUSTOMER_ID;
    `

  let result = await dbConnections().main.manager.query(query, [careCalendarId])
  result = await postProcessRecords(undefined, result, { json_columns: ['daily_feed'] })
  const feed = result?.[0]?.daily_feed || null
  if (!feed) {
    throw new Error('Feed not found in customer_classification')
  }
  return feed
}