const { preProcessRecords, postProcessRecords, dbConnections } = require('@krushal-it/ah-orm')
const taskRepository = require('../../../repositories/taskRepository')
const { loadClassificationData, saveClassificationData } = require('../../common/classification.helper')
const moment = require('moment')
const { staff_activity_statuses, record_statuses, preventive_healthcare_activities, entity_type, farm_management_question_key, farm_management_frequency, moment_keys, farm_management_priority_grouping } = require('../../../ENUMS')
class FarmManagementComplete {
  completeTask = async (data, params) => {
    try {
      const { care_calendar_id, user_completion_date_time, task_completion_location_1 } = data
      const { user_id, user_type } = params

      return await dbConnections().main.manager.transaction(async (transaction) => {
        // get current task details
        const currentTaskDetails = await taskRepository.getTaskDetails(transaction, care_calendar_id)
        const resolved_user_completion_date_time = user_completion_date_time ? user_completion_date_time : moment().toISOString()

        // attributes to mark current task as closed
        let updatedTaskDetails = {
          care_calendar_id: care_calendar_id,
          calendar_activity_status: staff_activity_statuses.VISIT_COMPLETED,
          user_task_completion_time: resolved_user_completion_date_time,
          completion_date: moment(resolved_user_completion_date_time).startOf('day').format(moment_keys.DATE_FORMAT),
          system_task_completion_time: moment().toISOString(),
          activity_completed_by: user_id,
        }

        // update the current task
        await taskRepository.updateTask(transaction, updatedTaskDetails)
        await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', care_calendar_id, { task_completion_location_1 })

        // create the next task as per the new activity date logic
        //TODO: write a function which return next activity date
        const customer_id = currentTaskDetails[care_calendar_id].primary.entity_uuid
        const activity_date = moment().add(1, 'month').format(moment_keys.DATE_FORMAT)
        const newCalendarId = await this.createTask(transaction, staff_activity_statuses.NEW_CASE_VISIT_PENDING, customer_id, activity_date, user_id)

        // get the base configuration of questions
        // get the existing responses filled for current task
        // create the response data that needs to be copied over to the next task
        const baseConfig = await transaction.getRepository('task_question').find({
          where: {
            active: record_statuses.ACTIVE,
          },
          select: {
            question_uuid: true,
            question_name: true,
            topic_question_priority: true,
            priority_grouping: true,
            topic_name: true,
            review_date_config: true,
            frequency: true,
            copy_following_visit: true,
          },
        })

        let currentTaskQuestionResponse = await transaction.getRepository('task_question_data').find({
          where: { care_calendar_id: care_calendar_id, active: record_statuses.ACTIVE },
        })
        currentTaskQuestionResponse = postProcessRecords(undefined, currentTaskQuestionResponse, { json_columns: ['form_configuration'] })

        const updatedTaskQuestionResponse = this.getUpdatedTaskQuestionResponse(currentTaskQuestionResponse, newCalendarId)
        let dataToCopy = this.overRideData(updatedTaskQuestionResponse, baseConfig, newCalendarId, activity_date)
        dataToCopy = dataToCopy.filter((item) => item)

        const entity_task_question_data = dbConnections().main.entities['task_question_data']
        dataToCopy = preProcessRecords(entity_task_question_data, dataToCopy, { json_columns: [] })

        await this.saveClassifiersForFollowUp(transaction, care_calendar_id, newCalendarId)
        const copyFromParentToFollowUp = await transaction.getRepository('task_question_data').save(dataToCopy)
        return copyFromParentToFollowUp
      })
    } catch (error) {
      throw new Error('Error while saving ticket.', { cause: error })
    }
  }

  overRideData = (data, baseConfig, follow_up_id, activity_date) => {
    try {
      const response = data.map((taskQuestionData) => {
        const taskConfig = baseConfig.find((conf) => conf.question_uuid === taskQuestionData.question_uuid)

        if (!taskConfig) {
          return
        }

        if (!taskConfig.copy_following_visit) {
          return
        }

        const resolvedTaskQuestionData = {
          question_uuid: taskConfig.question_uuid,
          care_calendar_id: follow_up_id,
          form_configuration: taskQuestionData.form_configuration || taskConfig.form_configuration,
          priority_grouping: taskQuestionData.priority_grouping || taskConfig.priority_grouping,
          review_date: taskQuestionData.review_date || activity_date,
        }

        if (taskQuestionData.saved_at) {
          // process review date
          const currentData = taskQuestionData?.form_configuration?.form_fields?.find((formField) => formField.key === farm_management_question_key.CURRENT)
          const targetData = taskQuestionData?.form_configuration?.form_fields?.find((formField) => formField.key === farm_management_question_key.TARGET)
          let isValueSame = this.arraysEqualUnordered(currentData?.selections?.values, targetData?.selections?.values)

          if (isValueSame && taskConfig.review_date_config) {
            resolvedTaskQuestionData.review_date = moment(taskQuestionData.saved_at).add(taskConfig.review_date_config, 'month').format(moment_keys.DATE_FORMAT)
          } else {
            resolvedTaskQuestionData.review_date = moment(taskQuestionData.saved_at).add(1, 'month').format(moment_keys.DATE_FORMAT)
          }
        }

        // process priority grouping
        if (taskConfig.frequency === farm_management_frequency.ONE_TIME) {
          if (taskQuestionData.saved_at) {
            resolvedTaskQuestionData.priority_grouping = farm_management_priority_grouping.priority_2
          }
        } else {
          // If review date is before the next activity date. Mark priority as 1 else 2
          if (moment(resolvedTaskQuestionData.review_date).isSameOrBefore(moment(activity_date))) {
            resolvedTaskQuestionData.priority_grouping = farm_management_priority_grouping.priority_1
          } else {
            resolvedTaskQuestionData.priority_grouping = farm_management_priority_grouping.priority_2
          }
        }

        return resolvedTaskQuestionData
      })

      return response
    } catch (error) {
      console.error('Error while overriding data', error)
      throw new Error('Error while overiding data.', { cause: error })
    }
  }

  arraysEqualUnordered = (arr1, arr2) => {
    if (!arr1 || !arr2) return false

    if (arr1.length !== arr2.length) return false

    let set1 = new Set(arr1)
    let set2 = new Set(arr2)

    for (let item of set1) {
      if (!set2.has(item)) return false
    }

    return true
  }

  saveClassifiersForFollowUp = async (transaction, parent_care_calendar_id, follow_up_id) => {
    try {
      const classifiers = ['ticket_1']
      let classificationData = await loadClassificationData('CARE_CALENDAR_CLASSIFICATION', parent_care_calendar_id, classifiers)
      const followUpCLassifiers = {
        ...classificationData,
        linked_care_calendar_task: parent_care_calendar_id,
      }
      return await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', follow_up_id, followUpCLassifiers)
    } catch (error) {
      throw new Error(error)
    }
  }

  createTask = async (transaction, activityStatus, customer_id, activity_date, user_id) => {
    try {
      let FMFollowTask = {
        entity_uuid: customer_id,
        entity_type_id: entity_type.CUSTOMER,
        activity_id: preventive_healthcare_activities.FARM_MANAGEMENT,
        activity_date: activity_date,
        calendar_activity_status: activityStatus,
        visit_schedule_time: activity_date,
        visit_creation_time: moment().toISOString(),
        activity_created_by: user_id,
      }
      const cc_id = await taskRepository.createTask(transaction, FMFollowTask)
      return cc_id
    } catch (error) {
      throw new Error(error)
    }
  }

  getUpdatedTaskQuestionResponse = (data, follow_up_id) => {
    try {
      const newDataArray = data.map((item) => {
        const newFormConfig = {
          ...item.form_configuration,
          form_fields: item?.form_configuration?.form_fields?.map((field) => {
            if (field.key === farm_management_question_key.NOTES) {
              return {
                ...field,
                selections: {
                  ...field?.selections,
                  values: [],
                },
              }
            }
            return field
          }),
        }

        return {
          question_uuid: item.question_uuid,
          form_configuration: newFormConfig,
          care_calendar_id: follow_up_id,
          saved_at: item.saved_at,
          review_date: item.review_date,
        }
      })
      return newDataArray
    } catch (error) {
      throw new error(error)
    }
  }
}

module.exports = { FarmManagementComplete }
