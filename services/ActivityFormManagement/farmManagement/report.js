const taskRepository = require('../../../repositories/taskRepository')
const customerRepository = require('../../../repositories/customerRepository')
const { postProcessRecords, dbConnections } = require('@krushal-it/ah-orm')
const { record_statuses, farm_management_question } = require('../../../ENUMS')
const moment  = require('moment')

class FarmManagementReport {

  loadCustomerLevelReport = async (customer_id, cc_id) => {
    try {
      return await dbConnections().main.manager.transaction(async (transaction) => {
        let customer = await customerRepository.getCustomerDetails(transaction, customer_id)
        customer = customer[customer_id].primary

        const care_calendars = await taskRepository.getFarmManagementTaskByCustomerId(transaction, customer_id)
        const care_calendar_ids = care_calendars.map((care_calendar) => care_calendar.care_calendar_id)

        let allTaskQuestionData = []

        if (care_calendar_ids && care_calendar_ids.length > 0) {
          allTaskQuestionData = await transaction
            .getRepository('task_question_data')
            .createQueryBuilder()
            .leftJoin('task_question', 'task_question', 'task_question_data.question_uuid = task_question.question_uuid')
            .leftJoin('care_calendar', 'care_calendar', 'care_calendar.care_calendar_id = task_question_data.care_calendar_id')
            .where('task_question_data.care_calendar_id IN (:...care_calendar_ids)', { care_calendar_ids })
            .andWhere('task_question_data.active = :activeStatus', { activeStatus: record_statuses.ACTIVE })
            .select([
              'task_question_data.form_configuration AS form_configuration', 
              'task_question_data.care_calendar_id AS care_calendar_id', 
              'task_question_data.score AS score', 
              'task_question_data.weightage AS weightage', 
              'task_question.question_name AS question_name', 
              'task_question.question_name_l10n AS question_name_l10n', 
              'task_question.topic_name_l10n AS topic_name_l10n', 
              'task_question.topic_name AS topic_name', 
              'care_calendar.activity_date AS activity_date', 
              'task_question.question_uuid AS question_uuid'
            ]).getRawMany()
            
          allTaskQuestionData = postProcessRecords(undefined, allTaskQuestionData, { json_columns: ['form_configuration', 'topic_name_l10n', 'question_name_l10n'] })
        }

        const currentTaskQuestionData = allTaskQuestionData.filter((data) => data.care_calendar_id === cc_id)
        const currentData = this.getCurrentValues(currentTaskQuestionData, [farm_management_question.LPD, farm_management_question.FAT, farm_management_question.SNF, farm_management_question.Cost_of_Milk_Production])

        const adviceData = this.processAdviceData(allTaskQuestionData);
        const currentCareCalendar = care_calendars.find((item) => item.care_calendar_id === cc_id)
        const visits = [{
          care_calendar_id: currentCareCalendar.care_calendar_id,
          categories: adviceData,
          activity_date: moment(currentCareCalendar.activity_date).format('YYYY-MM-DD'),
          visitedBy: currentCareCalendar.activity_completed_by
        }]

        const progressChart = await this.loadProgressChart(allTaskQuestionData, care_calendars)

        return { visits, progressChart, customer, currentData }
      })
    } catch (error) {
      console.log(`Error while getting farm management summary report`, error)
      throw new Error(`Error while getting farm management summary report`, { cause: error })
    }
  }

  getCurrentValues = (questionData, question_uuids) => {
    const filteredData = questionData
      .filter((item) => question_uuids.includes(item?.question_uuid))
      .map((item) => {
        const formFields = item?.form_configuration?.form_fields
        const currentField = formFields?.find((field) => field.key === 'current')

        if (currentField && Array.isArray(currentField.selections?.values) && currentField.selections.values.length > 0) {
          return {
            question_name_l10n: item.question_name_l10n || {},
            value: currentField.selections.values[0] || null,
          }
        }
        return null // Skip items that don't have the expected structure
      })
      .filter((item) => item !== null)
    return filteredData
  }

  loadProgressChart = async (questionData, care_calendars) => {
    let taskQuestions = await dbConnections().main.repos['task_question'].find({
      where: {
        active: record_statuses.ACTIVE,
      },
      select: {
        question_uuid: true,
        topic_name: true,
      },
    })
    taskQuestions = postProcessRecords(undefined, taskQuestions, { json_columns: ['question_name_l10n', 'topic_name_l10n'] })

    const topics = new Set(taskQuestions.map((item) => item.topic_name))
    const data = []

    topics.forEach((topic) => {
      const values = []
      for (const care_calendar_data of care_calendars) {
        const allQuestionsDataByTopic = questionData.filter((item) => item.care_calendar_id === care_calendar_data.care_calendar_id && item.topic_name === topic)
        if (allQuestionsDataByTopic.length === 0) {
          continue
        }

        let sumOfQuestionWeightage = 0
        let totalQuestionsAnswered = 0
        let totalWeightageByTopic = 0

        for (const questionsDataByTopic of allQuestionsDataByTopic) {
          const parsedValue = parseFloat(questionsDataByTopic.score)

          if (!isNaN(parsedValue) && parsedValue >= 0) {
            sumOfQuestionWeightage = sumOfQuestionWeightage + parsedValue
            totalQuestionsAnswered = totalQuestionsAnswered + 1
            totalWeightageByTopic = totalWeightageByTopic + (questionsDataByTopic.weightage || 1)
          }
        }

        if (totalQuestionsAnswered === 0) {
          continue
        }

        const weighted_average = (sumOfQuestionWeightage / totalWeightageByTopic) * 100
        values.push({
          visit_date: care_calendar_data.activity_date,
          weighted_average: weighted_average,
          severity: this.calculateSeverity(weighted_average),
        })
      }

      data.push({
        category: topic,
        stats: values,
      })
    })

    return data
  }

  calculateSeverity = (weightedAverage) => {
    if (weightedAverage <= 30) {
      return 1
    } else if (weightedAverage > 30 && weightedAverage <= 75) {
      return 2
    } else if (weightedAverage > 75 && weightedAverage <= 100) {
      return 3
    } else {
      return 0
    }
  }

  processAdviceData = (allTaskQuestionData) => {
    
    const topicsMap = {}

    // create a group of topic and question 
    for (const item of allTaskQuestionData) {
      if (!topicsMap[item.topic_name]) {
        topicsMap[item.topic_name] = {}
      }
      if (!topicsMap[item.topic_name][item.question_name]) {
        topicsMap[item.topic_name][item.question_name] = []
      }
      topicsMap[item.topic_name][item.question_name].push(item)
    }

    const result = []

    for (const topicName in topicsMap) {
      const topicQuestions = []
      let topic_name_l10n;
      const questionsMap = topicsMap[topicName]

      for (const questionName in questionsMap) {
        
        const questionEntries = questionsMap[questionName]
        // Sort by date in descending order
        questionEntries.sort((a, b) => new Date(b.activity_date) - new Date(a.activity_date))
        
        const firstQuestionEntry = questionEntries[0]
        const question_name_l10n = firstQuestionEntry.question_name_l10n;
        topic_name_l10n = firstQuestionEntry.topic_name_l10n;
        
        const firstValue = this.getSelectedAdviceData(firstQuestionEntry.form_configuration) // Use stringified version for comparison
        if(!firstValue.length) {
          break
        }

        let adviceMap = {}
        // Count repeats as long as values match
        for(const adviceIndex of firstValue) {

          if(!adviceMap[adviceIndex]) {
            adviceMap[adviceIndex] = {}
            adviceMap[adviceIndex]['advice_l10n'] = this.getAdviceByIndex(firstQuestionEntry.form_configuration, adviceIndex)
            adviceMap[adviceIndex]['repeatedCount'] = {},
            adviceMap[adviceIndex]['totalRepeatedCount'] = 1
          }

          for (let i = 1; i < questionEntries.length; i++) {
          
            let currentValue = this.getSelectedAdviceData(questionEntries[i].form_configuration)
            const isFound = currentValue.includes(adviceIndex);
            if(isFound) {
                adviceMap[adviceIndex].totalRepeatedCount = adviceMap[adviceIndex].totalRepeatedCount + 1
            } else {
              break;
            }
          }
        }

        // Add the topmost (latest date) entry to the topic's questions
        topicQuestions.push({
          name: questionName,
          name_l10n: question_name_l10n,
          advices: Object.values(adviceMap)
        })
      }

      // Add topic and its questions to the result
      if(topicQuestions.length > 0) {
        result.push({
          name: topicName,
          name_l10n: topic_name_l10n,
          questions: topicQuestions,
        })
  
      }
    }

    return result
  }

  getAdviceByIndex = (formConfiguration, index) => {

    let response = {};
    try {
      const adviceComponent = formConfiguration.form_fields.find((item) => item.key === 'advices')
      const adviceOptions = adviceComponent.options
      const foundAdvice  = adviceOptions.find((item) => item.value === index)
      response = foundAdvice.label_l10n
    } catch (error) {
      console.log('error getting advice selections', error, formConfiguration)
    }
    return response;
  }

  getSelectedAdviceData = (formConfiguration) => {

    let response = []
    try {
      const adviceComponent = formConfiguration.form_fields.find((item) => item.key === 'advices')
      response = adviceComponent.selections.values
    } catch (error) {
      console.log('error getting advice selections', error, formConfiguration)
    }
    return response;
  }

}

module.exports = { FarmManagementReport }
