const { dbConnections } = require('@krushal-it/ah-orm')
const { record_statuses, farm_management_question_key, farm_management_question_type, farm_management_priority_grouping, farm_management_frequency_grouping_name, farm_management_frequency_grouping_name_l10n } = require('../../../ENUMS')

const AdditionalFormconfiguration =  [
     {
        "key": "image",
        "type": "media",
        "label": "Image",
        "selections": {
          "values": []
        }
    },
     {
        "key": "notes",
        "type": "text_area",
        "label": "#/reference/label/notes",
        "selections": {
          "values": []
        }
    },
   {
        "key": "target_date",
        "mode": "date",
        "type": "date_time",
        "label": "Target Date",
        "selections": {
          "values": []
        },
        "date_format": "DD-MMM-YYYY"
    }

]



class FarmManagementConfig {
  saveOrEditBasicConfig = async (data , question_uuid) => {
    try {
        const taskQuestion = {
            question_name: data.question_name, 
            question_name_l10n: data.question_name_l10n,
            topic_name: data.topic_name,
            topic_name_l10n: data.topic_name_l10n,
            frequency: data.frequency,
            farmer_input_required: data.farmer_input_required,
            review_date_config: data.review_date_config,
            weightage: data.weightage,
            active: data.active
        }
        if(question_uuid) {
            taskQuestion['question_uuid'] = question_uuid

        }
        if(!question_uuid) {
          taskQuestion['form_configuration'] = {
            form_fields: [],
            form_name: data.topic_name
          }
        }
        return await dbConnections().main.manager.transaction(async (transaction) => {
            return await transaction.getRepository("task_question").save(
                taskQuestion
            )
        })
    }
    catch (error) {
        throw new Error(`Error while saving basic Farm level task`,{ cause: error })
    }
}

    saveAditionalConfig = async (data , question_uuid) => {
        try {            
            return await dbConnections().main.manager.transaction(async (transaction) => {
                const { additionalFields } = data
                const taskQuestion = await transaction.getRepository("task_question").findOne({
                    where: {
                      question_uuid:  question_uuid
                    },
                    select: {
                        form_configuration: true
                    }
                  })
                const formConfig = taskQuestion.form_configuration
                const formFields = formConfig.form_fields
                const additionalFieldFromCOnfig = Object.keys(AdditionalFormconfiguration)
                let removedFormFields =  formFields.filter(obj => !additionalFieldFromCOnfig.includes(obj.key));
                let addFormFields = AdditionalFormconfiguration.filter(obj => additionalFields.includes(obj.key));
                const finalFormConfig = removedFormFields.concat(addFormFields)
                formConfig['form_fields'] = finalFormConfig
                return await transaction.getRepository("task_question").save(
                    {
                        form_configuration: formConfig,
                        question_uuid: question_uuid
                    }
                )
            })
        } catch (error) {
            throw new Error(`Error while saving additional Farm level task`,{ cause: error })

        }
    }

    targetCurrentConfig = async (data , question_uuid) => {
        try {
            return await dbConnections().main.manager.transaction(async (transaction) => {

                const { target , form_type , unit , reference_id ,additionalFields ,priority_grouping ,default_value} = data
                const taskQuestion = await transaction.getRepository("task_question").findOne({
                    where: {
                      question_uuid:  question_uuid
                    },
                    select: {
                        form_configuration: true
                    }
                  })
                let formConfig = taskQuestion.form_configuration
                const formFields = formConfig.form_fields
                let finalFormFields =  formFields.filter(obj => [farm_management_question_key.ADVICE,farm_management_question_key.REFERENCEMEDIA].includes(obj.key));
                let addFormFields = AdditionalFormconfiguration.filter(obj => additionalFields.includes(obj.key));
                finalFormFields = finalFormFields.concat(addFormFields)
                if(form_type === farm_management_question_type.ADVANCED_MULTI_SELECT || form_type === farm_management_question_type.ADVANCED_SELECT) {
                    const currentAdvancedMultiSelect = this.buildAdvanacedSelect(farm_management_question_key.CURRENT,form_type,reference_id)
                    finalFormFields.push(currentAdvancedMultiSelect)
                    if(target === true) {
                        const targetAdvancedMultiSelect = this.buildAdvanacedSelect(farm_management_question_key.TARGET,form_type,reference_id,default_value)
                        finalFormFields.push(targetAdvancedMultiSelect) 
                    }
                }
                else if(form_type === farm_management_question_type.UNIT) {
                    const currentunitSelect = this.buildUnitConfig(farm_management_question_key.CURRENT,form_type,unit)
                    finalFormFields.push(currentunitSelect)
                    if(target === true) {
                        const currentunitSelect = this.buildUnitConfig(farm_management_question_key.TARGET,form_type,unit,default_value)
                        finalFormFields.push(currentunitSelect) 
                    }
                }
                else {
                    const currentCommon = this.commonConfig(farm_management_question_key.CURRENT,form_type)
                    finalFormFields.push(currentCommon)
                    if(target === true) {
                        const currentCommonSelect = this.commonConfig(farm_management_question_key.TARGET,form_type,default_value)
                        finalFormFields.push(currentCommonSelect) 
                    }
                }
                formConfig['form_fields'] = finalFormFields
                return await transaction.getRepository("task_question").save(
                    {
                        form_configuration: formConfig,
                        question_uuid: question_uuid,
                        priority_grouping: priority_grouping
                    }
                )
            })
        } catch (error) {
            throw new Error(`Error while saving target current Farm level task`,{ cause: error })
        }
    } 

    buildAdvanacedSelect = (key , type , reference_id , defaultValue = []) => {
        let advancedSelect =  {
            "key": key,
            "type": type,
            "options": `#/reference/options/${key}`,
            "label": `#/reference/label/${key}`,
            "selections": {
              "values": [],
            },
            "category_id": reference_id
          };
          if(defaultValue && defaultValue.length) {
            advancedSelect.selections["defaultValue"] =defaultValue
          }
          return advancedSelect
    }
    buildUnitConfig = (key, type , unit ,defaultValue = []) => {
        let unitConfig =      {
            "key": key,
            "type": type,
            "unit": unit,
            "label": `#/reference/label/${key}`,
            "selections": {
              "values": [],
              "defaultValue": []

            }
          }
          if(defaultValue && defaultValue.length) {
            unitConfig.selections["defaultValue"] =defaultValue
          }
          return unitConfig
    }
    commonConfig = (key , type ,defaultValue = []) => {
         let commonConfig =   {
            "key": key,
            "type": type,
            "label": `#/reference/label/${key}`,
            "selections": {
              "values": [],
              "defaultValue": []
            }
          }
          if(defaultValue && defaultValue.length) {
            commonConfig.selections["defaultValue"] =defaultValue
          }
          return commonConfig
    }

    advicesConfig = async (data , question_uuid) => {
        try {
            return await dbConnections().main.manager.transaction(async (transaction) => {
                const { advices } = data
                const taskQuestion = await transaction.getRepository("task_question").findOne({
                    where: {
                      question_uuid:  question_uuid
                    },
                    select: {
                        form_configuration: true
                    }
                  })
                let formConfig = taskQuestion.form_configuration
                const formFields = formConfig.form_fields
                let finalFormFields =  formFields.filter(obj => ![farm_management_question_key.ADVICE].includes(obj.key));
                if(advices && Array.isArray(advices) && advices.length) {
                    finalFormFields.push(this.advicesBuildConfig(advices))
                }
                formConfig['form_fields'] = finalFormFields
                return await transaction.getRepository("task_question").save(
                    {
                        form_configuration: formConfig,
                        question_uuid: question_uuid
                    }
                )
            })
        } catch (error) {
            throw new Error(`Error while saving advice  Farm level task`,{ cause: error })
        }
    }
    advicesBuildConfig = (advices) => {
      return  {
            "key": "advices",
            "type": "advanced_multi_select",
            "label": "Advices",
            "options": advices,
            "selections": {
              "values": []
            }
          }
    }


    referenceMediaConfig = async (data , question_uuid) => {
      try {
          return await dbConnections().main.manager.transaction(async (transaction) => {
              const { refrence_media , language } = data
              const taskQuestion = await transaction.getRepository("task_question").findOne({
                  where: {
                    question_uuid:  question_uuid
                  },
                  select: {
                      form_configuration: true
                  }
                })
              let formConfig = taskQuestion.form_configuration
              const formFields = formConfig.form_fields
              let finalFormFields =  formFields.filter(obj => ![farm_management_question_key.REFERENCEMEDIA].includes(obj.key));
              let refMedia =  formFields.find(obj => [farm_management_question_key.REFERENCEMEDIA].includes(obj.key));
              if(refrence_media && Object.keys(refrence_media).length) {
                  finalFormFields.push(this.refMediaBuildConfig(refrence_media,language ,refMedia))
              }
              formConfig['form_fields'] = finalFormFields
              return await transaction.getRepository("task_question").save(
                  {
                      form_configuration: formConfig,
                      question_uuid: question_uuid
                  }
              )
          })
      } catch (error) {
          throw new Error(`Error while saving advice  Farm level task`,{ cause: error })
      }
  }
  refMediaBuildConfig = (refrence_media , language , ref_media) => {
    let allMedia = typeof  ref_media?.selections?.values == 'object' ? ref_media?.selections?.values : {}
    allMedia[language] = refrence_media[language]
    return  {
          "key": "reference_media",
          "type": "reference_media",
          "selections": {
            "values": allMedia
          }
        }
  }

   getTaskList = async (data) => {
    try {
        let taskQuestions = await dbConnections().main.repos['task_question'].find({
            where: {
              active: record_statuses.ACTIVE
            },
            select: {
              question_uuid: true,
              question_name: true,
              topic_question_priority: true,
              priority_grouping: true,
              topic_name: true,
              review_date_config: true,
              farmer_input_required: true,
              topic_priority: true,
              question_name_l10n: true,
              topic_name_l10n: true,
              form_configuration: true,
              created_at: true,
              updated_at: true,
              active: true
            }
          })
          return taskQuestions
    } catch (error) {
        throw new Error(`Error while getting  Farm level task`,{ cause: error })
    }
   }
   getTaskByQuestionId = async (question_uuid) => {
    let taskQuestions = await dbConnections().main.repos['task_question'].findOne({
        where: {
          active: record_statuses.ACTIVE,
          question_uuid: question_uuid
        },
        select: {
          question_uuid: true,
          question_name: true,
          topic_question_priority: true,
          priority_grouping: true,
          topic_name: true,
          review_date_config: true,
          farmer_input_required: true,
          topic_priority: true,
          question_name_l10n: true,
          topic_name_l10n: true,
          form_configuration: true,
          active: true,
          frequency: true
        }
      })
      let category = await dbConnections().main.repos['ref_reference_category'].find({
        where: {
            active: record_statuses.ACTIVE,
        },
        select: {
            reference_category_id: true,
            reference_name: true ,
            reference_category_name_l10n: true
        }
        
      })
      const priorityGroup = [
        { 
          priority_grouping: farm_management_priority_grouping.priority_1, 
          name: farm_management_frequency_grouping_name.VISIT_PRIORITY, 
          priority_grouping_l10n : farm_management_frequency_grouping_name_l10n.VISIT_PRIORITY
        },
        { 
          priority_grouping: farm_management_priority_grouping.priority_2, 
          name: farm_management_frequency_grouping_name.ADDITIONAL, 
          priority_grouping_l10n: farm_management_frequency_grouping_name_l10n.ADDITIONAL 
        },
      ];
      const formType =[
        'numeric_input',
        'date_time',
        "advanced_select",
        "advanced_multi_select" ,
        "text_area",
        "unit" ,
        "media" ]
      const language = [{key: "en" ,label: "English" }, {key: "mr", label: "Marathi"}]
      return {taskQuestions , category ,priorityGroup, formType ,language}
   }

}
module.exports = { FarmManagementConfig }
