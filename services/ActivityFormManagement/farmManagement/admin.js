const { postProcessRecords, dbConnections } = require('@krushal-it/ah-orm')

const { loadClassificationConfigurationByMultipleParentIds , loadClassificationData } = require("../../common/classification.helper")
const { preventive_healthcare_activities, entity_type, farm_management_question_key, farm_management_question_type, farm_management_frequency, staff_activity_statuses, record_statuses, farm_management_frequency_grouping_name } = require('../../../ENUMS')
const TaskRepository = require("../../../repositories/taskRepository")
const { In } = require('typeorm')
const { FarmManagement } = require("./farm_management_v2")

class FarmManagementWeb {
    farmManagementSettings = new FarmManagement()
    //may add filters later
    loadCustomers = async (data) => {
        try {
          let customers = await dbConnections().main.manager.getRepository('customer')
                  .createQueryBuilder('customer')
                  .innerJoin('care_calendar', 'care_calendar', 'care_calendar.entity_uuid = customer.customer_id AND entity_type_id = :entity_type_Id AND activity_id= :farm_mangementactivity', { entity_type_Id: entity_type.CUSTOMER, farm_mangementactivity: preventive_healthcare_activities.FARM_MANAGEMENT })
                  .leftJoin(
                      (qb) => qb
                          .from('care_calendar', 'care_calendar_closed')
                          .select('care_calendar_closed.entity_uuid', 'customer_id')
                          .addSelect('COUNT(care_calendar_closed.care_calendar_id)', 'total_visits_completed')
                          .addSelect('MAX(care_calendar_closed.completion_date)', 'last_visit_date')
                          .where('care_calendar_closed.entity_type_id = :entity_type_Id', { entity_type_Id: entity_type.CUSTOMER })
                          .andWhere('care_calendar_closed.activity_id = :farm_management_activity', { farm_management_activity: preventive_healthcare_activities.FARM_MANAGEMENT })
                          .andWhere('care_calendar_closed.calendar_activity_status = :calendar_activity_status', { calendar_activity_status: staff_activity_statuses.VISIT_COMPLETED })
                          .groupBy('care_calendar_closed.entity_uuid'),
                      'closed_visits',
                      'closed_visits.customer_id = customer.customer_id'
                  )
                  .leftJoin(
                      (qb) => qb
                          .from('care_calendar', 'care_calendar_opened')
                          .select('care_calendar_opened.entity_uuid', 'customer_id')
                          .addSelect('MIN(care_calendar_opened.activity_date)', 'next_visit_date')
                          .where('care_calendar_opened.entity_type_id = :entity_type_Id', { entity_type_Id: entity_type.CUSTOMER })
                          .andWhere('care_calendar_opened.activity_id = :farm_management_activity', { farm_management_activity: preventive_healthcare_activities.FARM_MANAGEMENT })
                          .andWhere('care_calendar_opened.calendar_activity_status NOT IN (:...status)', { status: [staff_activity_statuses.VISIT_ABANDONED, staff_activity_statuses.VISIT_CANCELLED, staff_activity_statuses.VISIT_COMPLETED] })
                          .groupBy('care_calendar_opened.entity_uuid'),
                      'opened_visits',
                      'opened_visits.customer_id = customer.customer_id'
                  )
                  .select([
                      'customer.customer_id AS customer_id',
                      'customer.customer_name_l10n AS customer_name_l10n',
                      'customer.mobile_number AS mobile_number',
                      'customer.customer_visual_id AS customer_visual_id',
                      'COALESCE(closed_visits.total_visits_completed, 0) AS total_visits_completed',
                      'closed_visits.last_visit_date AS last_visit_date',
                      'opened_visits.next_visit_date AS next_visit_date'
                  ])
                  .groupBy([
                    'customer.customer_id',
                    'customer.customer_name_l10n',
                    'customer.mobile_number',
                    'customer.customer_visual_id',
                    'closed_visits.total_visits_completed',
                    'closed_visits.last_visit_date',
                    'opened_visits.next_visit_date'
                ])
                .getRawMany();

      
            const customerIds = customers.map(customer => customer.customer_id)
            let result= await loadClassificationConfigurationByMultipleParentIds('CUSTOMER_CLASSIFICATION',customerIds, ["alternate_mobile_number",'farmer_village_1'])
            let combinedData = [];
            const resultData = result.data
            const farmerVillageArray = Object.values(resultData).map(item => item.farmer_village_1).flat();
            let village = []
            if(farmerVillageArray && farmerVillageArray.length) {
                 village = await dbConnections().main.manager.getRepository('ref_village').find({
                   where: { village_id : In(farmerVillageArray) } , select: { village_name_l10n : true ,village_id : true}
                 })
            }
            customers.forEach(itemA => {
              const itemId = itemA.customer_id.toString();
              const villageName=  resultData[itemId]?.farmer_village_1[0] ? village.find(vil =>  vil.village_id === resultData[itemId]?.farmer_village_1[0]) : null
              combinedData.push( {
                ...itemA,
                ...resultData[itemId],
                village : villageName?.village_name_l10n
              });
            });
     
            customers = postProcessRecords(undefined, customers, { json_columns: ["customer_name_l10n"] })
            return combinedData
        } catch (error) {
            throw new Error("Error while getting customer.", { cause: error });
        }
    }
    loadCustomerTaskById = async (customer_id) => {
        try {
            const care_calendar = await TaskRepository.getAllFarmLevelTaskAndDocumentByCustomerId(dbConnections().main.manager, customer_id)
            const care_calendar_ids = care_calendar.map(cc => cc.care_calendar_id)
            const customerAdditionalDetails = await loadClassificationData('CUSTOMER_CLASSIFICATION',customer_id ,['alternate_mobile_number', 'farmer_village_1'] )
            let customer = await dbConnections().main.manager.getRepository('customer')
                .createQueryBuilder('customer')
                .select([
                    'customer.customer_id AS customer_id',
                    'customer.customer_name_l10n AS customer_name_l10n',
                    'customer.mobile_number AS mobile_number',
                    'customer.customer_visual_id AS customer_visual_id'
                ])
                .where('customer.customer_id = :customerId', { customerId: customer_id }) // Example condition
                .getRawOne(); 
            if(!care_calendar_ids.length) {
                return { task :[] , customer: {...customer , ...customerAdditionalDetails}};        
            }
            let result= await loadClassificationConfigurationByMultipleParentIds('CARE_CALENDAR_CLASSIFICATION',care_calendar_ids, ["ticket_1"])
            let combinedData = [];
            const resultData = result.data
            care_calendar.forEach(itemA => {
              const itemId = itemA.care_calendar_id.toString();
    
              combinedData.push( {
                calendar_activity_status: itemA.calendar_activity_status,
                completion_date: itemA.completion_date,
                document_id: itemA.document_id,
                activity_date: itemA.activity_date,
                ...resultData[itemId],
                care_calendar_id: itemA.care_calendar_id,

              });
            });
            combinedData.sort((a, b) => {
              if (a.completion_date === null) return 1;
              if (b.completion_date === null) return -1;
              return new Date(a.completion_date) - new Date(b.completion_date);
            });
            return { task :combinedData , customer: {...customer , ...customerAdditionalDetails}};        
        } catch (error) {
            throw new Error("Error while getting customer Task.", { cause: error });
        }
    }
    loadTableByTaskId = async (care_calendar_id) => {
        try {
            const DBmanager = dbConnections().main.manager
            const taskQuestions = await DBmanager.getRepository('task_question').find({
                where: {
                    active: record_statuses.ACTIVE
                }
            })
            let taskQuestionData = await dbConnections().main.repos['task_question_data'].find({
                where: {
                    active: record_statuses.ACTIVE,
                    care_calendar_id: care_calendar_id    
                }
              })
            taskQuestionData = postProcessRecords(undefined, taskQuestionData, { json_columns: [] })
            const referenceValues = this.getCategoryValues(taskQuestionData)
            let references = await dbConnections().main.repos['ref_reference'].find({
                where: {
                    active: record_statuses.ACTIVE,
                    reference_id : In(referenceValues)
                }
            })
            let overidingTaskReviewDate = taskQuestions.map(taskQuestion => {
                const match = taskQuestionData.find(obj2 => obj2.question_uuid === taskQuestion.question_uuid);
                if(match){
                    return  { question_name : taskQuestion.question_name,topic_name: taskQuestion.topic_name, form_configuration: match.form_configuration , review_date: match.review_date,
                         saved_at: match.saved_at, priority_grouping: match.priority_grouping ? match.priority_grouping : taskQuestion.priority_grouping ,
                         weightage: match.weightage ? match.weightage : taskQuestion.weightage,
                         score: match.score ? match.score : taskQuestion.score
                        } 
                }
                return  taskQuestion;
            });
            const response = this.getFormFieldsSelectionValues(overidingTaskReviewDate, references)
            return response
        } catch (error) {
            throw new Error("Error while loading  table  by task Id.", { cause: error });
        }
    }
    getFormFieldsSelectionValues = (data, references) => {
        // Ensure data is an array
        if (!Array.isArray(data)) {
          data = [data];
        }
      
        return data.filter(item => {
            const isAdvancedMultiSelect = item.form_configuration?.form_fields?.some(
                state => state.type === farm_management_question_type.ADVANCED_MULTI_SELECT &&
                         state.key === farm_management_question_key.CURRENT
            );
            return !(isAdvancedMultiSelect && item.frequency === farm_management_frequency.ONE_TIME);
        }).map(item => {
          const formFields = {};
          let data_available  = false
          if (item.form_configuration && item.form_configuration.form_fields) {
            item.form_configuration.form_fields.forEach(field => {
              const key = field.key;
              let values = field.selections?.values || [];
              if(field?.category_id && values.length) {
                values = references
                .filter(reference => values.includes(reference.reference_id)) 
                .map(reference => reference.reference_name);
              } 

              if(field.key === farm_management_question_key.ADVICE && values.length) {
                values = field?.options
                .filter(item => values.includes(item.value)) 
                .map(item => item.label);
              }
              if(
               ( field.key === farm_management_question_key.CURRENT || 
                field.key === farm_management_question_key.TARGET || 
                field.key === farm_management_question_key.TARGET_DATE ||
                field.key === farm_management_question_key.NOTES ||
               field.key === farm_management_question_key.ADVICE) && values.length
              ){
                data_available= true
              }
              formFields[key] = values;
            });
          }
          
          return {
            question_uuid: item.question_uuid,
            question_name: item.question_name,
            topic_name:item.topic_name,
            category: item.priority_grouping === 1 ? farm_management_frequency_grouping_name.VISIT_PRIORITY : farm_management_frequency_grouping_name.ADDITIONAL,
            answered : item.saved_at ? true : false,
            data_available: data_available,
            review_date: item.review_date,
            topic_question_priority: item.topic_question_priority,
            weightage: item.weightage,
            score: item.score ? item.score * 100 :  '-',
            ...formFields
          };
        }).sort((a, b) => {
            if (a.review_date && b.review_date) {
              return new Date(a.review_date) - new Date(b.review_date);
            } else if (a.review_date) {
              return -1;
            } else if (b.review_date) {
              return 1;
            } else {
              return a.topic_question_priority - b.topic_question_priority;
            }
          });
    }
    getCategoryValues = (data) => {
        // Ensure data is an array
        if (!Array.isArray(data)) {
          data = [data];
        }
      
        // Collect all values in a single array
        const allValues = data.reduce((values, item) => {
          const formFields = item.form_configuration?.form_fields || [];
          
          // Get values from fields that have category_id
          formFields
            .filter(field => field.category_id)
            .forEach(field => {
              const fieldValues = field.selections?.values || [];
              values.push(...fieldValues);
            });
          
          return values;
        }, [record_statuses.ACTIVE]);
      
        return [...new Set(allValues.filter(item => typeof item === 'number' && !isNaN(item)))];
      }

}
module.exports = { FarmManagementWeb }