const { validateUUID, infoLog, errorLog, generateUUID } = require('@krushal-it/mobile-or-server-lib')

const { preventive_healthcare_activities, staff_activity_statuses, record_statuses, entity_type, note_type, classifiers, document_type } = require('../../ENUMS.js')
const { saveClassificationData, getClassificationConfiguration, loadClassificationDataWithClassifierIds } = require('../common/classification.helper.js')
const { animalListForFarmTask } = require('../animal/controller.js')
const moment = require('moment')
const _ = require('lodash')
const { getCareCalendarInfo } = require('./utils.js')
const { CareCalendarQueries, CareCalendarActivityQueries } = require('../../queries/care-calendar.js')
const { cmtQuery } = require('../../queries/query_animals_cmt_data_by_care_calendar_id.js')
const { dbConnections, preProcessRecords, postProcessRecords } = require('@krushal-it/ah-orm')
const { getServiceCost, getActivityCostMap, getMedicineCost } = require('../serviceDocument/controller.js')
const { ActivityHelper } = require('../../utils/activity.js')

module.exports = function clinical_mastitis_test_activity(dbManager) {
  return {
    async update(data, token) {
      return await dbManager.transaction(async (transactionalEntityManager) => {
        try {
          const { animal_data, care_calendar } = data

          if (!care_calendar) throw new Error('Bad Request. Missing care_calendar_id')

          if (!_.isArray(animal_data)) throw new Error('Bad Request. Missing cattle data')

          const care_calendar_info = await getCareCalendarInfo(care_calendar, classifiers.ACTIVITY_FORM_COMPLETION, transactionalEntityManager)

          const { calendar_activity_status, form_completion_date, customer_id, activity_id, entity_type_id, entity_uuid, activity_date, visit_schedule_time, visit_creation_time } = care_calendar_info.length > 0 ? care_calendar_info[0] : {}

          if (!customer_id && !activity_id) {
            return { result: false, message: 'care-calendar: ' + care_calendar + ' does not exists' }
          }

          if (calendar_activity_status === staff_activity_statuses.VISIT_COMPLETED || calendar_activity_status === staff_activity_statuses.VISIT_CANCELLED || calendar_activity_status === staff_activity_statuses.VISIT_ABANDONED) {
            return { result: false, message: 'Case is already closed !!' }
          }

          if (form_completion_date) {
            return { result: false, message: 'Form filled already !!' }
          }

          const form_completion_time_stamp = new Date().toJSON()

          if (entity_type_id === entity_type.CUSTOMER) {
            for (let animal of animal_data) {
              const calendarEntry = {
                activity_date: activity_date,
                completion_date: form_completion_time_stamp,
                entity_uuid: animal.id,
                entity_type_id: entity_type.ANIMAL,
                activity_id: preventive_healthcare_activities.CMT,
                calendar_activity_status: staff_activity_statuses.VISIT_COMPLETED,
                visit_schedule_time: visit_schedule_time,
                visit_creation_time: visit_creation_time,
                activity_created_by: token.user_id,
                activity_completed_by: token.user_id,
                system_task_completion_time: form_completion_time_stamp,
                user_task_completion_time: form_completion_time_stamp,
              }

              let calendarId = await CareCalendarQueries.insertcalendarEntries(calendarEntry, { datasource: transactionalEntityManager })
              calendarId = calendarId.identifiers[0].care_calendar_id

              const child_care_calendar_classification = {
                linked_care_calendar_task: care_calendar,
                form_completion_date: form_completion_time_stamp,
              }

              for (const key of Object.keys(animal)) {
                child_care_calendar_classification[key] = animal[key]
              }

              await saveClassificationData(transactionalEntityManager, 'CARE_CALENDAR_CLASSIFICATION', calendarId, child_care_calendar_classification)

              if (animal.notes) {
                let notes_row = {
                  note_type_id: note_type.TASK_ACTIVITY_NOTE,
                  note: { ul: animal.notes },
                  entity_1_type_id: entity_type.TASK,
                  entity_1_uuid: calendarId,
                  creator_type_id: token.user_type,
                  creator_uuid: token.user_id,
                  last_modifying_user_id: token.user_id,
                  note_time: moment.utc().format('YYYY-MM-DD HH:mm:ss.SSS'),
                }
                notes_row = preProcessRecords(dbConnections().main.entities['note'], notes_row, { json_columns: ['note'] })
                await transactionalEntityManager.getRepository('note').insert(notes_row)
              }
            }
          } else if (entity_type_id === entity_type.ANIMAL) {
            const animal = animal_data[0]
            const child_care_calendar_classification = {
              linked_care_calendar_task: care_calendar,
              form_completion_date: form_completion_time_stamp,
            }

            for (const key of Object.keys(animal)) {
              child_care_calendar_classification[key] = animal[key]
            }

            await saveClassificationData(transactionalEntityManager, 'CARE_CALENDAR_CLASSIFICATION', care_calendar, child_care_calendar_classification)

            if (animal.notes) {
              let notes_row = {
                note_type_id: note_type.TASK_ACTIVITY_NOTE,
                note: { ul: animal.notes },
                entity_1_type_id: entity_type.TASK,
                entity_1_uuid: care_calendar,
                creator_type_id: token.user_type,
                creator_uuid: token.user_id,
                last_modifying_user_id: token.user_id,
                note_time: moment.utc().format('YYYY-MM-DD HH:mm:ss.SSS'),
              }
              notes_row = preProcessRecords(dbConnections().main.entities['note'], notes_row, { json_columns: ['note'] })
              await transactionalEntityManager.getRepository('note').insert(notes_row)
            }
          } else {
            return { result: false, message: 'Invalid entity type !!' }
          }

          const care_calendar_classification_entry = preProcessRecords(
            dbConnections().main.entities['care_calendar_classification'],
            {
              classifier_id: classifiers.ACTIVITY_FORM_COMPLETION,
              value_date: form_completion_time_stamp,
              value_reference_id: preventive_healthcare_activities.CMT,
              last_modifying_user_id: token.user_id,
              care_calendar_id: care_calendar,
            },
            { json_columns: [] }
          )

          await transactionalEntityManager.getRepository('care_calendar_classification').save(care_calendar_classification_entry)

          /* calculating service cost, medicine cost for the new ..... */
          const activity_cost_map = await getActivityCostMap()
          const service_cost = getServiceCost(preventive_healthcare_activities.CMT, animal_data.length, activity_cost_map)
          const medicine_cost = getMedicineCost(preventive_healthcare_activities.CMT, animal_data.length, activity_cost_map)
          await ActivityHelper.processPaymentDetails(transactionalEntityManager, care_calendar, service_cost, medicine_cost)

          return { result: true, message: 'saved!' }
        } catch (error) {
          throw new Error(error)
        }
      })
    },

    async get(care_calendar) {
      return await dbManager.transaction(async (transactionalEntityManager) => {
        const care_calendar_info = await getCareCalendarInfo(care_calendar, classifiers.ACTIVITY_FORM_COMPLETION, transactionalEntityManager)

        const { calendar_activity_status, form_completion_date, customer_id, activity_id, entity_type_id, entity_uuid } = care_calendar_info.length > 0 ? care_calendar_info[0] : {}

        if (!customer_id && !activity_id) return 'care-calendar: ' + care_calendar + ' does not exists'

        let animals = await animalListForFarmTask({
          farmer_id: customer_id,
          activity_id: activity_id,
        })

        /* if care-calendar-id is for specific animal */
        if (entity_type_id === entity_type.ANIMAL) {
          animals = [animals.find((animal) => (animal.animal_id = entity_uuid))]
        }

        if (form_completion_date) {
          const documentEntry = await dbConnections().main.repos['document'].findOne({
            where: [
              {
                entity_1_entity_uuid: care_calendar,
                document_type_id: document_type.SERVICE_DOCUMENT,
                active: record_statuses.ACTIVE,
              },
            ],
            select: {
              document_id: true,
              document_information: true,
              client_document_information: true,
            },
          })

          const service_document = postProcessRecords(undefined, documentEntry, { json_columns: ['document_information', 'client_document_information'] })

          const query = cmtQuery({
            care_calendar_id: care_calendar,
            calendar_activity_status: calendar_activity_status,
            entity_type_id: entity_type_id,
          })

          const cmtAnimals = await transactionalEntityManager.query(`${query}`)

          animals = animals.map((animal) => {
            const foundAnimal = cmtAnimals.find((anima) => anima.animal_id === animal.animal_id)
            return {
              ...animal,
              ...foundAnimal,
            }
          })

          return {
            service_document,
            animals: postProcessRecords(undefined, animals, { json_columns: ['note'] }),
            form_completion_date,
          }
        }

        return {
          animals: postProcessRecords(undefined, animals, { json_columns: ['animal_type_l10n'] }),
          form_completion_date,
        }
      })
    },
  }
}
