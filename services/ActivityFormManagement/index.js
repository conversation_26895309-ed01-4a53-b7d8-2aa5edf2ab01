const { dbConnections} = require('@krushal-it/ah-orm');
const { preventive_healthcare_activities, reproductive_activities } = require("../../ENUMS.js");

const clinical_mastitis_test_activity = require('./clinical_mastitis_test.js')
const artificial_insemination_activity = require('./artificial_insemination.js')
const pregnancy_detection_activity = require('./pregnancy_detection.js')
const farm_management_activity = require('./farm_management_form.js')
const common_farm_level_preventive_activity_form = require('./common_farm_level_preventive_activity_form.js')


const map_activity_ids_to_methods = {
    // reproductive activities
    [reproductive_activities.AI_PENDING]: artificial_insemination_activity,
    [reproductive_activities.PREGNANCY_DETECTION]: pregnancy_detection_activity,

    // preventive activities
    [preventive_healthcare_activities.FARM_MANAGEMENT]: farm_management_activity,
    [preventive_healthcare_activities.CMT]: clinical_mastitis_test_activity,
    [preventive_healthcare_activities.FARM_TICK_CONTROL_SPRAYING]: common_farm_level_preventive_activity_form,
    [preventive_healthcare_activities.TICK_CONTROL]: common_farm_level_preventive_activity_form,
    [preventive_healthcare_activities.LSD_VACCINATION]: common_farm_level_preventive_activity_form,
    [preventive_healthcare_activities.FMD_VACCINATION]: common_farm_level_preventive_activity_form,
    [preventive_healthcare_activities.FMD_HS_BQ_VACCINATION]: common_farm_level_preventive_activity_form,
    [preventive_healthcare_activities.THEILERIOSIS_VACCINATION]: common_farm_level_preventive_activity_form,
    [preventive_healthcare_activities.BRUCELLOSIS]: common_farm_level_preventive_activity_form,
    [preventive_healthcare_activities.RUMEN_MAGNET]: common_farm_level_preventive_activity_form,
    [preventive_healthcare_activities.DEWORMING_AND_TICK_CONTROL_BOLUS_B1]: common_farm_level_preventive_activity_form,
    [preventive_healthcare_activities.DEWORMING_AND_TICK_CONTROL_BOLUS_B2]: common_farm_level_preventive_activity_form,
}

module.exports = function ActivityFormControl(activity_id) {
    const dbManager = dbConnections().main.manager;
    if (map_activity_ids_to_methods[activity_id]) {
        return map_activity_ids_to_methods[activity_id](dbManager)
    } else {
        throw new Error("No implementation found for the activity " + activity_id + ".");
    }
}