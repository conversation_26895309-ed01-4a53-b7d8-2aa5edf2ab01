const { classifiers, record_statuses, reproductive_activities, document_type, preventive_healthcare_activities } = require("../../ENUMS.js");
const { saveClassificationData, loadClassificationDataWithClassifierIds, getClassificationConfiguration } = require('../common/classification.helper.js');
const { getCareCalendarInfo } = require('./utils.js')
const moment = require("moment")
const { dbConnections, preProcessRecords, postProcessRecords, } = require('@krushal-it/ah-orm');
const { ActivityHelper } = require("../../utils/activity.js");
const { getMedicineCost, getServiceCost, getActivityCostMap } = require("../serviceDocument/controller.js");
const { getCompiledQuery } = require('../../queries/index.js')
module.exports = function pregnancy_detection_activity(dbManager) {
    return {

        async update(data, token) {
            const { care_calendar, is_cattle_pregnant, number_of_months_pregnant_2 } = data
            if (!care_calendar) throw new Error("Missing care_calendar_id.");
            return await dbManager.transaction(async (transactionalEntityManager) => {
                const care_calendar_info = await getCareCalendarInfo(care_calendar, classifiers.ACTIVITY_FORM_COMPLETION, transactionalEntityManager)

                const form_completion_date = care_calendar_info.length > 0 ? care_calendar_info[0].value_date : undefined
                if (form_completion_date) {
                    return { result: false, message: "Form filled already!" }
                }

                const form_completion_time_stamp = new Date().toJSON()

                if (!is_cattle_pregnant || !care_calendar)
                    throw new Error("Missing payload.")

                let care_calendar_classification_entry = [{
                    classifier_id: classifiers.ACTIVITY_FORM_COMPLETION,
                    value_date: form_completion_time_stamp,
                    value_reference_id: reproductive_activities.PREGNANCY_DETECTION,
                    last_modifying_user_id: token.user_id,
                    care_calendar_id: care_calendar
                }];

                // @ts-ignore
                number_of_months_pregnant_2 && care_calendar_classification_entry.push({
                    classifier_id: classifiers.NUMBER_OF_MONTHS_PREGNANT_2,
                    value_double: parseFloat(number_of_months_pregnant_2),
                    value_date: form_completion_time_stamp,
                    last_modifying_user_id: token.user_id,
                    care_calendar_id: care_calendar
                });

                const entity = dbConnections().main.entities["care_calendar_classification"];

                care_calendar_classification_entry = preProcessRecords(entity, care_calendar_classification_entry, { json_columns: [] });

                await transactionalEntityManager.getRepository('care_calendar_classification').save(care_calendar_classification_entry);
            

                await saveClassificationData(transactionalEntityManager, "CARE_CALENDAR_CLASSIFICATION", care_calendar, { is_cattle_pregnant, });

                const activity_cost_map = await getActivityCostMap()
                const service_cost = getServiceCost(reproductive_activities.PREGNANCY_DETECTION, 1,activity_cost_map)
                const medicine_cost = getMedicineCost(reproductive_activities.PREGNANCY_DETECTION,1, activity_cost_map)
                await ActivityHelper.processPaymentDetails(transactionalEntityManager, care_calendar, service_cost, medicine_cost)
                
                return { result: true, message: "saved!" }
            })
        },

        async get(care_calendar) {
            return await dbManager.transaction(async (transactionalEntityManager) => {
                if (!care_calendar) 
                    return { result: false, message: 'missing care-calendar-id' }

                const query = getCompiledQuery("get-pregnancy-detection-form")
                const response = await transactionalEntityManager.createQueryBuilder()
                    .from(`(${query})`)
                    .setParameters({
                        care_calendar,
                    }).execute();
                
                // get all classification data for the requested care calendar id
                const calendarClassificationConfAttrArray = Object.keys(getClassificationConfiguration()['CARE_CALENDAR_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP);
                const careCalendarClassifications = await loadClassificationDataWithClassifierIds(transactionalEntityManager, 'CARE_CALENDAR_CLASSIFICATION', care_calendar, calendarClassificationConfAttrArray)
                const careCalendarClassificationValues = careCalendarClassifications['data']
                let documentEntry = undefined
                if (careCalendarClassificationValues['form_completion_date']) {
                    documentEntry  = await transactionalEntityManager.getRepository('document').findOne({
                        where: [
                            {
                                entity_1_entity_uuid: care_calendar,
                                document_type_id: document_type.SERVICE_DOCUMENT,
                                active: record_statuses.ACTIVE
                            }
                        ],
                        select: {
                            document_id: true,
                            document_information: true,
                            client_document_information: true
                        }
                    });

                    documentEntry = postProcessRecords(undefined, documentEntry, { json_columns: ['document_information', 'client_document_information'] })

                }
                
                return {
                    result: postProcessRecords(undefined, response, { json_columns: [] }),
                    service_document: documentEntry,
                    form_completion_date: careCalendarClassificationValues['form_completion_date'],
                }            
            })
        }

    }
}