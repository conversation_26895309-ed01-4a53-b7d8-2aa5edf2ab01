const healthScoreConfig = {
  maxKrushalScore: 600,
  minParams: 4,
  animalTypes: {
    cow_heifer_yes_yes: {
      animal_type: 'cow',
      animal_stage_1: 'heifer',
      milking: 'yes',
      preganancy: 'yes'
    },
    cow_heifer_yes_no: {
      animal_type: 'cow',
      animal_stage_1: 'heifer',
      milking: 'yes',
      preganancy: 'no'
    },
    cow_heifer_no_yes: {
      animal_type: 'cow',
      animal_stage_1: 'heifer',
      milking: 'no',
      preganancy: 'yes'
    },
    cow_heifer_no_no: {
      animal_type: 'cow',
      animal_stage_1: 'heifer',
      milking: 'no',
      preganancy: 'no'
    },
    cow_adult_yes_yes: {
      animal_type: 'cow',
      animal_stage_1: 'adult',
      milking: 'yes',
      preganancy: 'yes'
    },
    cow_adult_yes_no: {
      animal_type: 'cow',
      animal_stage_1: 'adult',
      milking: 'yes',
      preganancy: 'no'
    },
    cow_adult_no_yes: {
      animal_type: 'cow',
      animal_stage_1: 'adult',
      milking: 'no',
      preganancy: 'yes'
    },
    cow_adult_no_no: {
      animal_type: 'cow',
      animal_stage_1: 'adult',
      milking: 'no',
      preganancy: 'no'
    }

  },
  scoreConfig: {
    cow_adult_no_no: {
      animal_age_1: {
        scoreType: 'range',
        weightage: 1.67,
        ranges: [{ score: 2, min: 1, max: 2.9999, label: '2:1-2' },
          { score: 4, min: 3, max: 4.9999, label: '3:2-3' },
          { score: 5, min: 5, max: 6.9999, label: '4:6-7' },
          { score: 4.5, min: 7, max: 7.9999, label: '5:4-5' },
          { score: 3, min: 8, max: 10, label: '4:10-11' },
          { score: 2, min: 11, max: 10000, label: '2:11-10000' }
        ]
      },
      animal_temperature_1: {
        score_or_qualifier: 'qualifier',
        scoreType: 'range',
        ranges: [{ score: 0, min: 0, max: 96, label: '1:0-100' },
          { score: 1, min: 97, max: 103, label: '2:101-102' }
        ]
      },
      animal_weight_1: {
        scoreType: 'range',
        weightage: 5,
        ranges: [{ score: 1, min: 0, max: 400, label: '1:0-400' },
          { score: 1.333, min: 401, max: 500, label: '2:401-450' },
          { score: 3.667, min: 501, max: 600, label: '3:451-550' },
          { score: 5, min: 601, max: 800, label: '4:551-650' },
          { score: 3.67, min: 891, max: 10000, label: '5:651-800' }
        ]
      },
      animal_sick_last_one_year_1: {
        scoreType: 'range',
        weightage: 1.67,
        ranges: [
          { score: 1, min: 4, max: 12, label: '2:9-10' },
          { score: 2, min: 3, max: 3.9999, label: '3:7-8' },
          { score: 3, min: 2, max: 2.9999, label: '4:5-6' },
          { score: 4, min: 1, max: 1.9999, label: '5:0-4' },
          { score: 5, min: 0, max: 0.9999, label: '5:0-2' }
        ]
      },
      animal_locomotion_score_1: {
        // scoreType: 'actual',
        scoreType: 'map',
        mappings: [
          { score: 1, value: '1000650001' },
          { score: 2, value: '1000650002' },
          { score: 3, value: '1000650003' },
          { score: 4, value: '1000650004' },
          { score: 5, value: '1000650005' }
        ],
        weightage: 3.33
      },
      animal_body_score_1: {
        scoreType: 'rangeFactor',
        ranges: [
          { factor: 1, min: 0, max: 1.9, label: '2:9-10' },
          { factor: 1.33, min: 2, max: 3.0, label: '3:7-8' },
          { factor: 1.25, min: 3.0001, max: 4.0, label: '4:5-6' },
          { factor: 0.7, min: 4.0001, max: 99.9999, label: '5:0-2' }
        ],
        weightage: 10,
        function: 'bodyScore'
      },
      animal_udder_score_1: {
        scoreType: 'composite',
        weightage: 6.67,
        sub_scores: {
          udder_cleft: {
            scoreType: 'map',
            mappings: [
              { score: 1, value: '1000620001' },
              { score: 5, value: '1000620002' }
            ],
            weightage: 10
          },
          udder_depth: {
            // scoreType: 'actual',
            scoreType: 'map',
            mappings: [
              { score: 1, value: '1000590001' },
              { score: 5, value: '1000590002' }
            ],
            weightage: 10
          },
          udder_balance: {
            scoreType: 'map',
            mappings: [
              { score: 1, value: '1000610001' },
              { score: 3, value: '1000610002' },
              { score: 3, value: '1000610003' },
              { score: 5, value: '1000610004' }

            ],
            // scoreType: 'actual',
            weightage: 10
          },
          udder_teat_placement: {
            //    scoreType: 'actual',
            scoreType: 'map',
            mappings: [
              { score: 1, value: '1000630001' },
              { score: 3, value: '1000630002' },
              { score: 3, value: '1000630003' },
              { score: 5, value: '1000630004' }
            ],

            weightage: 10
          },
          udder_teat_length: {
            // scoreType: 'actual',
            scoreType: 'map',
            mappings: [
              { score: 1, value: '1000640001' },
              { score: 3, value: '1000640002' },
              { score: 5, value: '1000640003' }
            ],
            weightage: 10
          }

        }
      },
      animal_foreign_body_1: {
        // scoreType: 'actual',
        scoreType: 'map',
        mappings: [
          { score: 1, value: '1000800001' },
          { score: 3, value: '1000800002' },
          { score: 5, value: '1000800003' }
        ],
        weightage: 3.33
      },
      animal_nose_condition_score_1: {
        scoreType: 'actual',
        weightage: 0
      },
      animal_dung_score_1: {
        scoreType: 'actual',
        weightage: 0
      },
      animal_averate_lpd_1: {
        scoreType: 'range',
        weightage: 1.67,
        ranges: [
          { score: 1, min: 1, max: 10, label: '2:9-10' },
          { score: 2, min: 10.00001, max: 12, label: '3:7-8' },
          { score: 3, min: 12.00001, max: 15, label: '4:5-6' },
          { score: 4, min: 15.0001, max: 20, label: '5:0-4' },
          { score: 5, min: 20.0001, max: 999, label: '5:0-2' }
        ]
      }

    }
  },
  bodyScoreConfig: {
    keyMap: {
      body_score_observation_tail_and_pin_bone: {
        position: 0,
        // values:['sunken','slightly sunken','filled']
        values: ['**********', '**********', '**********']
      },
      body_score_observation_back_bone: {
        position: 1,
        // values:['bumby','slightly bumpy','flat']
        values: ['1000670001', '**********', '**********']
      },
      body_score_hip_and_pin_bone: {
        position: 2,
        // values:['v shape','shallow','flat']
        values: ['**********', '**********', '**********']
      }
    },
    scoreMap: {
      **********: { // sunken
        1000670001: { // bumby
          **********: 2, // v shape
          **********: 2.66, // shallow
          **********: 3.33 // Not possible using previous values // flat

        },
        **********: { // slightly bumpy
          **********: 2.66, // v shape
          **********: 3.33, // shallow
          **********: 3.33 // Not possible using previous values // flat
        },
        **********: { // flat
          **********: 3.33, // v shape not possible using previous values
          **********: 3.33, // shallow  not possible using previous values
          **********: 4 // Not possible using previous values // flat
        }
      },
      **********: { // slightly sunken
        1000670001: { // bumby
          **********: 2.66, // v shape
          **********: 2.66, // shallow
          **********: 3.33 // Not possible using previous values // flat

        },
        **********: { // slightly bumpy
          **********: 3.33, // v shape
          **********: 4, // shallow
          **********: 4 // Not possible using previous values // flat
        },
        **********: { // flat
          **********: 3.33, // v-shape // NOT APPLICABLE taking previous score
          **********: 4, // shallow // NOT APPLICABLE taking previous score
          **********: 4.5 // flat // NOT APPLICABLE taking previous score
        }
      },
      **********: { // filled
        1000670001: { // bumby
          **********: 3.33, // v shape // NOT APPLICABLE taking previous score
          **********: 4, // shallow // NOT APPLICABLE taking previous score
          **********: 4 // flat // NOT APPLICABLE taking previous score
        },
        **********: { // slightly bumpy
          **********: 4, // v shape // NOT APPLICABLE taking previous score
          **********: 4, // shallow // NOT APPLICABLE taking previous score
          **********: 4.5 // flat // NOT APPLICABLE taking previous score
        },
        **********: { // flat
          **********: 4, // v-shape// NOT APPLICABLE taking previous score
          **********: 4.5, // shallow
          **********: 5 // flat
        }
      }
    }

  }

}
module.exports = healthScoreConfig

//  bodyScoreConfig: {
//     cow_adult_no_no: {
//       body_score_observation_tail_and_pin_bone: {
//         scoreType: 'map',
//         weightage: 5,
//         mappings: [
//           { score: 5, value: '**********' }, // Filled
//           { score: 3, value: '**********' }, // slightly sunken
//           { score: 1, value: '**********' }, // sunken
//         ]
//       },
//       body_score_observation_back_bone: {
//         scoreType: 'map',
//         weightage: 5,
//         mappings: [
//           { score: 1, value: '**********' }, // Bumpy
//           { score: 3, value: '**********' }, // slightly bumpy
//           { score: 5, value: '**********' }, // flat
//         ]
//       },
//       body_score_him_and_pin_bone: {
//         scoreType: 'map',
//         weightage: 5,
//         mappings: [
//           { score: 1, value: '**********' }, // V Shape
//           { score: 3, value: '**********' }, // Shallow
//           { score: 5, value: '**********' }, // flat
//         ]
//       },

// animal_udder_hygene_score_1: {
//   scoreType: 'actual',
//   weightage: 7
// },
// animal_udder_depth_balance_score_1: {
//   scoreType: 'actual',
//   weightage: 10
// },
// animal_udder_cleft_score_1: {
//   scoreType: 'actual',
//   weightage: 10
// }

// animal_body_score_1: {
//   scoreType: 'composite',
//   weightage: 10,
//   sub_scores: {
//     animal_body_back_view_condition_1: {
//       scoreType: 'actual',
//       weightage: 10
//     },
//     animal_body_side_view_condition_1: {
//       scoreType: 'actual',
//       weightage: 10
//     }
//   }
// },
// animal_body_score_1: {
//   scoreType: 'range',
//   weightage: 10,
//   ranges: [
//     { score: 0, min: 0, max: 1.9, label: '2:9-10' },
//     { score: 1, min: 2, max: 3.4999, label: '3:7-8' },
//     { score: 3, min: 3.5, max: 4.4999, label: '4:5-6' },
//     { score: 5, min: 4.5, max: 99.9999, label: '5:0-2' }
//   ]
// },
