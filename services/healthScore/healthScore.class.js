/* eslint-disable eqeqeq */
/* eslint-disable no-unused-vars */
/* eslint-disable camelcase */
const { postProcessRecords, dbConnections, getDBConnections } = require('@krushal-it/ah-orm')
const { loadClassificationDataWithClassifierIds, getClassificationConfiguration, saveClassificationData, loadClassificationData } = require('../../services/common/classification.helper')

const { LibAnimalService } = require('../animal/animal.class')
const _ = require('lodash')

const healthScoreConfig = require('./healthScoreConfig')
let maxRawScore = 0
for (const [gKey, gObj] of Object.entries(healthScoreConfig.scoreConfig.cow_adult_no_no)) {
  if (gObj.score_or_qualifier !== 'qualifier' && gObj.weightage) {
    maxRawScore += gObj.weightage * 5
  }
}


const matchRange = (value, rangeValues) => {
  const rangeObj = _.find(rangeValues, (rangeObj) => { return value >= rangeObj.min && value <= rangeObj.max })
  if (rangeObj === undefined || rangeObj === null) {
    console.log('rangeObj not found for value', value, 'rangeValues', rangeValues)
    return null
  }
  return rangeObj.score
}

const computeValue = (data, scoreConfig, key) => {
  let value = 0
  const propertyData = data[key]
  let rangeFactor = null
  try {
    switch (scoreConfig[key].scoreType) {
      case 'range':
        value = matchRange(propertyData, scoreConfig[key].ranges)
        break
      case 'factor':
        value = scoreConfig[key].scoreFactor * propertyData
        break
      case 'map':
        value = (_.find(scoreConfig[key].mappings, (mapping) => { return mapping.value == propertyData }))?.score || 0
        console.log('mapping based value for key', key, 'propertyData', propertyData, 'value', value)
        break
      case 'composite':
        try {
          let totalSubScoreValue = 0
          let maxSubScore = 0
          Object.keys(scoreConfig[key].sub_scores).forEach((subKey) => {
            let subScoreValue = computeValue(data, scoreConfig[key].sub_scores, subKey)
            if (subScoreValue === undefined || subScoreValue === null) subScoreValue = 0
            totalSubScoreValue += subScoreValue * scoreConfig[key].sub_scores[subKey].weightage
            maxSubScore += scoreConfig[key].sub_scores[subKey].weightage * 5
          })
          value = (totalSubScoreValue / maxSubScore) * 5
          if (value === null || value === undefined) value = 0
        } catch (error) {
          console.log('error', error)
          return 0
        }
        break
      case 'rangeFactor':
        rangeFactor = _.find(scoreConfig[key].ranges, (rangeObj) => { return propertyData >= rangeObj.min && propertyData <= rangeObj.max })
        if (rangeFactor === undefined || rangeFactor === null) {
          console.log('rangeObj not found for value', propertyData, 'rangeValues', scoreConfig[key].ranges)
          return 0
        }
        value = rangeFactor.factor * propertyData
        break
      default:
        value = propertyData
    }
    if (value === undefined || value === null) {
      console.log('value not found for key', key, 'propertyData', propertyData)
      return 0
    }
  } catch (error) {
    console.log('error', error)
    return 0
  }
  return value
}

const getAllLeafValues = (obj) => {
  return _.sortBy(_.uniq(_.flatMapDeep(obj, val =>
    _.isObject(val) && !_.isEmpty(val) ? getAllLeafValues(val) : val
  )))
}

const calculateBodyScore = (data) => {
  try {
    let valueMap = []

    if (!data?.body_score_observation_tail_and_pin_bone || !data?.body_score_observation_back_bone || !data?.body_score_hip_and_pin_bone) {
      console.log('Observations not available for body score', 'body_score_observation_tail_and_pin_bone', data?.body_score_observation_tail_and_pin_bone, 'body_score_observation_back_bone', data?.body_score_observation_back_bone, 'body_score_hip_and_pin_bone', data?.body_score_hip_and_pin_bone)
      return 0
    }
    let animal_body_score_1 = healthScoreConfig.bodyScoreConfig.scoreMap[data.body_score_observation_tail_and_pin_bone][data.body_score_observation_back_bone][data.body_score_hip_and_pin_bone] || 0
    console.log('animal_body_score_1 before update', animal_body_score_1)
    // now adjust for number of calvings & pregnancy
    valueMap = getAllLeafValues(healthScoreConfig.bodyScoreConfig.scoreMap)
    console.log('valueMap', valueMap)
    if (data.number_of_calvings_1 >= 6 || data.number_of_months_pregnant_1 >= 6) {
      const index = valueMap.indexOf(animal_body_score_1)
      if (index >= 0 && index < valueMap.length - 1) {
        console.log('index', index, 'valueMap[index]', valueMap[index], 'valueMap[index + 1]', valueMap[index + 1])
        animal_body_score_1 = valueMap[index + 1]
        return animal_body_score_1
      }
    }
    console.log('animal_body_score_1', animal_body_score_1)
    return animal_body_score_1
  } catch (error) {
    console.log('error', error)
    return 0
  }
}

const calculateHealthScore = (data) => {
  let animal_health_score = 0
  let totalParams = 0
  data.animal_body_score_1 = calculateBodyScore(data)
  const additionalScores = { animal_body_score_1: data.animal_body_score_1 }

  const scoreConfig = healthScoreConfig.scoreConfig.cow_adult_no_no
  let qualifierFailed = false
  for (const key of Object.keys(scoreConfig)) {
    const propertyData = data[key]
    if (propertyData || scoreConfig[key].scoreType === 'composite') {
      const value = computeValue(data, scoreConfig, key)
      if (value === -1) { // not valid value continue to next param
        console.log('value not found for key', key, 'propertyData', propertyData)
        continue
      }
      // if this was composite add to additionalScores
      if (scoreConfig[key].scoreType === 'composite') {
        additionalScores[key] = value
      }
      totalParams++
      if (scoreConfig[key].score_or_qualifier === 'qualifier') {
        if (value <= 0) {
          console.log('this is qualifier ', key, 'value', propertyData, ' is not a allowed value')
          qualifierFailed = true
        }
        continue // this is qualifier once qualified continue to next param
      }
      animal_health_score += value * scoreConfig[key].weightage
    } // add additional compensation, normalization here
  }
  if (qualifierFailed) {
    console.log('qualifierFailed', qualifierFailed)
    return { animal_health_score: 0, animal_body_score_1: data.animal_body_score_1, additionalScores }
  }
  animal_health_score = (animal_health_score / maxRawScore) * healthScoreConfig.maxKrushalScore
  animal_health_score = Math.round(animal_health_score)
  console.log('animal_health_score', animal_health_score, 'for animal', data.visual_id, additionalScores)
  return { animal_health_score, animal_body_score_1: additionalScores.animal_body_score_1, animal_udder_score_1: additionalScores.animal_udder_score_1, additionalScores }
}

const checkCalculateHealthScore = () => {
  calculateHealthScore({ milking: true, preganancy: false, animal_stage_1: 'adult', animal_weight_1: 500 })
}

// New method based on new oberservation the values still arent correct will correct once we get from Drs
class LibHealthScore {
  async create (data, headers) {
    const returnData = calculateHealthScore(data)
    return returnData
  }

  async saveHealthScore (data, headers) {
    const { animal_id, animal_health_score, animal_body_score_1, animal_udder_score_1 } = data
    await dbConnections().main.manager.transaction(async transactionalEntityManager => {
      console.log('ad 3 animalList allFeedData.animals animal_id, animal_health_score, animal_body_score_1, animal_udder_score_1', animal_id, animal_health_score, animal_body_score_1, animal_udder_score_1)
      await saveClassificationData(transactionalEntityManager,
        'ANIMAL_CLASSIFICATION', animal_id, { animal_health_score, animal_body_score_1, animal_udder_score_1 })
    })
  }

  async loadAnimalClassiferDataWithDocuments (headers, animalId, getDeactiveDocument = false) {
    const docConsts = {
      documentTypeId: **********, // Animal classification docs
      entityTypeId: **********, // **********, //animal classification
      entityType: 'animal_classification'// animal_classification
    }
    const dataConfig = {}
    const classificationConfiguration = getClassificationConfiguration()
    dataConfig.customer = _.cloneDeep(classificationConfiguration.CUSTOMER_CLASSIFICATION)
    dataConfig.animal = _.cloneDeep(classificationConfiguration.ANIMAL_CLASSIFICATION)
    
    // I am mimicking config for base data else it fails
    const extraCustomerConfig = {
      customer_visual_id: { classifier_id: **********, column: 'value_string_256', allow_multiple: false },
      customer_name_l10n: { classifier_id: **********, column: 'value_json', allow_multiple: false, language_dependent: true },
      mobile_number: { classifier_id: 3000000003, column: 'value_string_256', allow_multiple: false }
    }
    // extend the configuration to include fields coming base animal data
    const extraAnimalConfig = {
      animal_visual_id: { classifier_id: 3000000010, column: 'value_string_256', allow_multiple: false }
    }

    dataConfig.customer.ATTRIBUTE_LIST = { ...dataConfig.customer.ATTRIBUTE_LIST, ...extraCustomerConfig }
    dataConfig.animal.ATTRIBUTE_LIST = { ...dataConfig.animal.ATTRIBUTE_LIST, ...extraAnimalConfig }

    const animalClassifiers = Object.keys(dataConfig.animal.CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP)

    try {
      const request = {
        animal_id: animalId,
        basic_info: {
          required: true
        },
        additional_info: {
          required: false,
          include_all: true
        },
        disease_info: { required: false },
        documents: { required: false }
      }
      const animalServiceHandler = new LibAnimalService()
      const response = await animalServiceHandler.getAnimalDetails(request, { headers, query: {} })

      const animal = response
      let animalClassifierData = {}
      let animalClassifierDataWithClassifierIds = {}
      await dbConnections().main.manager.transaction(async transactionalEntityManager => {
        
        const data = await loadClassificationDataWithClassifierIds(transactionalEntityManager, 'ANIMAL_CLASSIFICATION', animal.animal_id, animalClassifiers)
        animalClassifierData = data.data
        animalClassifierDataWithClassifierIds = data.dataWithClassifierIds
        const mainDBConnection = getDBConnections().main
        
        for (const docClf of Object.keys(animalClassifierDataWithClassifierIds)) {
          
          // get corresponding animal cla
          if (_.isArray(animalClassifierDataWithClassifierIds[docClf]) && animalClassifierDataWithClassifierIds[docClf].length > 0) {
            animalClassifierDataWithClassifierIds[docClf] = animalClassifierDataWithClassifierIds[docClf][0]
          }
          
          console.log('docClf', docClf, animalClassifierDataWithClassifierIds[docClf])
          
          if (animalClassifierDataWithClassifierIds[docClf]) {
            
            const documents = await loadObjects('document', [{
              document_type_id: docConsts.documentTypeId,
              entity_1_type_id: docConsts.entityTypeId,
              entity_1_entity_uuid: animalClassifierDataWithClassifierIds[docClf]
            }])

            postProcessRecords(mainDBConnection.entities.document, documents, { json_columns: ['client_document_information', 'document_information'] })
            try {
              if (documents.length > 0) {
                animalClassifierData[`${docClf}_document`] = documents.map((doc) => {
                  const clDoc = doc.client_document_information
                  const docInfo = doc.document_information
                  if (doc.active == 1000100002 && !getDeactiveDocument) return {} // this is inactivated not to be used
                  // in case of server clDoc may not be there
                  let returnData = { clientDocumentInformation: clDoc, documentInformation: docInfo, selected: true, existing: true, document_id: doc.document_id, active: doc.active, updated_at: doc.updated_at, document_type: docClf }
                  if (clDoc && Object.keys(clDoc).length > 0) {
                    returnData = { ...returnData, fileName: clDoc.fileName, type: clDoc.fileType, uri: clDoc.url }
                  } else {
                    returnData = { ...returnData, fileName: docInfo.fileName, type: docInfo.fileType, uri: docInfo.url }
                  }
                  return returnData
                })
                animalClassifierData[`${docClf}_document`] = animalClassifierData[`${docClf}_document`].filter((doc) => doc.uri)
              }
            } catch (e) {
              console.log('error in loadAnimalClassiferDataWithDocuments', e)
            }
          } else {
            console.log('no docClf', docClf)
          }
        }
      })
      // convert number_of_months_pregnant_1 from as of date to today's as of date
      if (animalClassifierData.number_of_months_pregnant_1 && animalClassifierData.number_of_months_pregnant_1 > 0) {
        if (animalClassifierData.number_of_months_pregnant_as_of_date_1) {
          const monthSec = 1000 * 60 * 60 * 24 * 30
          const number_of_months_pregnant_1 = animalClassifierData.number_of_months_pregnant_1.length ? animalClassifierData.number_of_months_pregnant_1[0] : 0
          const difference  = (new Date() - new Date(animalClassifierData.number_of_months_pregnant_as_of_date_1)) / monthSec
          animalClassifierData.number_of_months_pregnant_1 = parseInt(number_of_months_pregnant_1  + difference)
        }
        animalClassifierData.number_of_months_pregnant_as_of_date_1 = new Date().toISOString().replace('T', ' ').replace('Z', '')
      } else {
        try {
          delete animalClassifierData.number_of_months_pregnant_as_of_date_1
        } catch (e) {}
      }
      const animalData = { ...animalClassifierData, animal_id: animal.animal_id, animal_visual_id: animal.animal_visual_id }
      return animalData
    } catch (err) {
      console.log('error in loadAnimalClassiferDataWithDocuments', err)
      throw err
    }
  }

  async animalScoreHistory (animal_id) {
    const animalScoreHistory = await dbConnections().main.repos.animal_classification_history.find({
      where: { animal_id, classifier_id: ********** },
      select: { value_double: true, created_at: true },
      orderBy: {
        created_at: 'DESC'
      }
    })
    return animalScoreHistory
  }

  async reCalculateHealthScore (animal_id) {
    try {
      const classificationConfiguration = getClassificationConfiguration()
      const animalClassifiers = Object.keys(classificationConfiguration.ANIMAL_CLASSIFICATION.CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP)
      const data = await loadClassificationData('ANIMAL_CLASSIFICATION', animal_id, animalClassifiers)
      let returnData = calculateHealthScore(data)
      returnData = { ...returnData, animal_id, ...returnData.additionalScores }
      delete returnData.additionalScores
      return returnData
    } catch (e) {
      console.log('error in reCalculateHealthScore', e)
      throw e
    }
  }
}
const loadObjects = async (objName, filters) => {
  try {
    console.log('[loadObjects] data, objaName ', objName)
    const objs = await dbConnections().main.manager.getRepository(objName).find({ where: filters })
    return objs
  } catch (error) {
    console.log('error loading ', objName, error)
    return [];
  }
}

module.exports = { LibHealthScore }