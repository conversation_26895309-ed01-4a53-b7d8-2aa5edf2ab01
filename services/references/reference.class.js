const { getForms } = require("./controller");
const {errorLog} = require('@krushal-it/mobile-or-server-lib')
class LibReferenceService {
    async find(params) {
        const { entityType } = params.route;
        try {
            let payload = await getForms(entityType);
            return { result: true, data: payload };
        } catch (error) {
            errorLog("error in LibReferenceService:10",params,{message:error.message})
            return error;
        }
    }
}

module.exports = { LibReferenceService };
