const { getLocalClassifierConfig } = require("../../utils/reference");
const { getDocumentJSONConfig } = require("../../utils/document");
const { getClassificationConfiguration } = require("../common/classification.helper");
const { q_activityConfig } = require("../../queries/activity");
const { dbConnections, postProcessRecords } = require("@krushal-it/ah-orm");
const { errorLog } = require('@krushal-it/mobile-or-server-lib')

const getForms = async (entity) => {
    try {
        const configMap = {
            customer: "CUSTOMER_CLASSIFICATION",
            animal: "ANIMAL_CLASSIFICATION",
            disease: "ANIMAL_DISEASE_CLASSIFICATION",
            "care-calendar": "CARE_CALENDAR_CLASSIFICATION"
        };
        let result =  {};
        if(entity !== "staff"){
            result = await getClassificationConfiguration()[configMap[entity]]["ATTRIBUTE_LIST"] 
        }
        if (entity == "staff") {
        
            const staff = await dbConnections().main.repos["ref_reference"].find({
                where: [{ reference_category_id: 10002300, active: 1000100001 }],
                select: { reference_name_l10n: true, reference_id: true }
            })
            const mapped = staff.map(item => ({
                id: item.reference_id,
                name_l10n: item.reference_name_l10n
              }));
            result["staff"] = mapped;
            const staff_plan = await dbConnections().main.repos["ref_reference"].find({
                where: [{ reference_category_id: 10008600, active: 1000100001 }],
                select: { reference_name_l10n: true, reference_id: true }
            })
            result["staff_plan"] = staff_plan
        }

        if (entity !== "disease" && entity !== "staff") {
            result["local"] = getLocalClassifierConfig(entity);
            result["file_types"] = await getDocumentJSONConfig();
        }
        if (entity === "care-calendar") {
            let activityExtraQuery = q_activityConfig();
            let activityExtraData = await dbConnections().main.manager.query(activityExtraQuery)
            postProcessRecords(undefined, activityExtraData, { json_columns: ['category_json', 'activity_name_l10n'] })
            let formatedData = {};
            for (let indexExtra = 0; indexExtra < activityExtraData.length; indexExtra++) {
                let activity = activityExtraData[indexExtra];
                const { activity_name_l10n, activity_id } = activity;
                if (!formatedData[activity.reference_name]) {
                    formatedData[activity.reference_name] = {
                        category_parent: activity.category_parent,
                        category_json: activity.category_json,
                        options: []
                    };
                }
                formatedData[activity.reference_name].options.push({ activity_name_l10n, activity_id });
            }
            

            const medicines = await dbConnections().main.repos["ref_reference"].find({
                where: [{ reference_category_id: 10005000, active: 1000100001 }],
                select: { reference_name_l10n: true, reference_id: true }
            })
            postProcessRecords(undefined, medicines, { json_columns: ['reference_name_l10n'] })

            const statuses = await dbConnections().main.repos["ref_reference"].find({
                where: [{ reference_category_id: 10003000, active: 1000100001 }],
                select: { reference_name_l10n: true, reference_id: true }
            })
            postProcessRecords(undefined, statuses, { json_columns: ['reference_name_l10n'] })

            result = { care_alendar_config: result, activity_config: formatedData, medicine_config: medicines, status_config: statuses };
        }
        return result;
    } catch (error) {
        errorLog("error in getForms:51", entity, { message: error.message })
        throw error;
    }
};
module.exports = { getForms };