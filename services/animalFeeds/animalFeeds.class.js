/* eslint-disable no-unused-vars */
/* eslint-disable camelcase */
const { saveClassificationData } = require('../common/classification.helper')
const { configurationJSON, post } = require('@krushal-it/common-core')
const _ = require('lodash')
const { dbConnections } = require('@krushal-it/ah-orm')
const { getReferenceForCategoryId } = require('../common/common.class')
const { loadClassificationData, getClassificationConfiguration } = require('../common/classification.helper')
const { LibCustomerService } = require('../customer/customer.class')
const { LibAnimalService } = require('../animal/animal.class')
const moment = require('moment')
// "@krushal-it/ah-orm": "github:krushal-it/ah-orm#feature/bgl_sursma",
// "@krushal-it/ah-orm": "file:/home/<USER>/krushal/repos/ah-orm",
class LibAnimalFeeds {
  async get (params, id) {
    try {
      const classificationConfiguration = getClassificationConfiguration()
      console.log('ahr pr gFBI 3')
      const customerServiceHandler = new LibCustomerService()
      const customerData = await customerServiceHandler.get(id, { headers: params.headers, query: {} })
      console.log('ahr pr gFBI 4, response = ', customerData)
      const customerClassifiers = Object.keys(classificationConfiguration.CUSTOMER_CLASSIFICATION.CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP)
      const animalClassifiers = Object.keys(classificationConfiguration.ANIMAL_CLASSIFICATION.CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP)
      const customerClassifierData = await loadClassificationData('CUSTOMER_CLASSIFICATION', id, customerClassifiers)
      console.log('customerData', customerData)
      const customer = {
        ...customerClassifierData,
        customer_id: id,
        customer_name_l10n: customerData.customer_name_l10n,
        mobile_number: customerData.mobile_number,
        customer_visual_id: customerData.customer_visual_id
      }
      const animalServiceHandler = new LibAnimalService()
      const animalList = await animalServiceHandler.find({ headers: params.headers, query: { customer_id: id } })
      console.log('ad 3 animalList allFeedData.animals', animalList)
      const animals = {}
      for await (const animal of animalList.data) {
        const animalClassifierData = await loadClassificationData('ANIMAL_CLASSIFICATION', animal.animal_id, animalClassifiers)
        const animalData = { ...animalClassifierData, animal_id: animal.animal_id, animal_visual_id: animal.animal_visual_id }
        // convert pregenancy_month to as of date
        if (animalData.number_of_months_pregnant_1 && animalData.number_of_months_pregnant_1 > 0) {
          console.log("animalData.number_of_months_pregnant_1", animalData.number_of_months_pregnant_1)
          if (animalData.number_of_months_pregnant_as_of_date_1) {
            const number_of_months_pregnant_1 = animalData.number_of_months_pregnant_1[0] + moment().diff(moment(animalData.number_of_months_pregnant_as_of_date_1), 'months')
            animalData.number_of_months_pregnant_1 = number_of_months_pregnant_1 > 9 ? 0 : number_of_months_pregnant_1;
          }
          animalData.number_of_months_pregnant_as_of_date_1 = new Date().toISOString().replace('T', ' ').replace('Z', '')
        } else {
          try {
            delete animalData.number_of_months_pregnant_as_of_date_1
          } catch (e) {}
        }
        animals[animal.animal_id] = animalData
        // console.log('ad 6', animals)
      }
      const dataConfig = {}
      dataConfig.customer = _.clone(classificationConfiguration.CUSTOMER_CLASSIFICATION)
      // extend the configuration to include fields coming base customer data
      // sample config
      // customer_name_l10n: { classifier_id: 3000000002, column: 'value_json', allow_multiple: false, language_dependent: true },
      const extraCustomerConfig = {
        customer_visual_id: { classifier_id: 3000000001, column: 'value_string_256', allow_multiple: false },
        customer_name_l10n: { classifier_id: 3000000002, column: 'value_json', allow_multiple: false, language_dependent: true },
        mobile_number: { classifier_id: 3000000003, column: 'value_string_256', allow_multiple: false }
      }
      dataConfig.customer.ATTRIBUTE_LIST = { ...dataConfig.customer.ATTRIBUTE_LIST, ...extraCustomerConfig }
      // console.log('ad 1 dataConfig.customer.ATTRIBUTE_LIST', dataConfig.customer.ATTRIBUTE_LIST)
      dataConfig.animal = _.clone(classificationConfiguration.ANIMAL_CLASSIFICATION)
      // extend the configuration to include fields coming base animal data
      const extraAnimalConfig = {
        animal_visual_id: { classifier_id: 3000000010, column: 'value_string_256', allow_multiple: false }
      }
      dataConfig.animal.ATTRIBUTE_LIST = { ...dataConfig.animal.ATTRIBUTE_LIST, ...extraAnimalConfig }
      // console.log('ad 2 dataConfig.animal.ATTRIBUTE_LIST', dataConfig.animal.ATTRIBUTE_LIST)
      // console.log('ad 4 animalClassifiers', animalClassifiers)

      return { customer, animals, dataConfig }
    } catch (error) {
      console.log('error', error)
    }
  }

  async create (params, data) {
    try {
      // const classifierType = { customer: 'CUSTOMER_CLASSIFICATION', animal: 'ANIMAL_CLASSIFICATION' }[entity]
      const success = await dbConnections().main.manager.transaction(async transactionalEntityManager => {
        data.customer && Object.keys(data.customer) && await saveClassificationData(transactionalEntityManager, 'CUSTOMER_CLASSIFICATION', data.customer.customer_id, data.customer)
        if (data.animals && Object.keys(data.animals)) {
          for (const [animal_id, animal] of Object.entries(data.animals)) {
            // if there is preganancy month added add as of date
            // if (animal.number_of_months_pregnant_1 && animal.number_of_months_pregnant_1 > 0) {
            //   animal.number_of_months_pregnant_as_of_date_1 = new Date().toISOString().replace('T', ' ').replace('Z', '')
            // } else {
            //   try {
            //     delete animal.number_of_months_pregnant_as_of_date_1
            //   } catch (e) {}
            // }
            animal && await saveClassificationData(transactionalEntityManager, 'ANIMAL_CLASSIFICATION', animal.animal_id, animal)
          }
        }
        return true
      })
      return success
    } catch (e) {
      console.log('error in saveData', e)
      throw e
    }
  }

  async getAllAnimalsFeedRecommendations ({ headers, data }) {
    try {
      const { customer_id, allAnimalsData, userLanguage } = data
      let foliageReferenceData = await getReferenceForCategoryId(10001300, userLanguage)
      let concentratesReferenceData = await getReferenceForCategoryId(10013000, userLanguage)
      foliageReferenceData.forEach((foliage) => {
        Object.keys(foliage.reference_information.feed_properties).forEach((key) => {
          foliage[key] = foliage.reference_information.feed_properties[key]
        })
      })
      concentratesReferenceData.forEach((concentrate) => {
        Object.keys(concentrate.reference_information.feed_properties).forEach((key) => {
          concentrate[key] = concentrate.reference_information.feed_properties[key]
        })
      })
      foliageReferenceData = convertBooleanAndNumericValuesDeep(foliageReferenceData)
      concentratesReferenceData = convertBooleanAndNumericValuesDeep(concentratesReferenceData)
      let feedMasterData = require('./feedMaster.json')
      feedMasterData = convertBooleanAndNumericValuesDeep(feedMasterData)

      console.log('ahr gAFR 1')
      const response = await post(
        configurationJSON().SERVICE_END_POINTS.BACK_END + '/animalFeeds/getAllAnimalsFeedRecommendations',
        headers, { customer_id, allAnimalsData, foliageReferenceData, concentratesReferenceData, feedMasterData, userLanguage })
      console.log('response = ', response)
      if (response && response.data) {
        console.log('ahr gAFR 2a, response.data = ', response.data)
        return response.data
      } else {
        console.log('ahr gAFR 2b, response.data  or response not correct', response)
        throw new Error('response.data  or response not correct')
      }
    } catch (error) {
      console.log('error in getAllFeedRecommendations on router', error)
      throw error
    }
  }

  async calculateAllAnimalFeeds ({ headers, data }) {
    const { customer_id, allAnimalsData, userLanguage } = data
    const totalFoliages = []
    const totalConcentrates = []
    let weeklyFoliages = []
    let weeklyConcentrates = []
    const animalDailyFeeds = {}
    const foliages = await getReferenceForCategoryId(10001300, userLanguage)
    const concentrates = await getReferenceForCategoryId(10013000, userLanguage)
    try {
      for (const [animal_id, animal] of Object.entries(allAnimalsData.data.animals)) {
        // dont use the same headers, duplicate it as headers have side effect
        const callHeaders = { ..._.cloneDeep(headers), useCase: 'calculateAllAnimalFeeds' }
        const animalFeed = await this.calculateDailyFeeds({ headers: callHeaders, data: { allAnimalsData, animal_id, foliages, concentrates, userLanguage } })
        const { foliagesPerDay, concentratesPerDay } = animalFeed // now this isnt going come from router remove .data
        animalDailyFeeds[animal_id] = { foliagesPerDay, concentratesPerDay }
        // match the foliage and push to totalFoliages
        foliagesPerDay.forEach((foliage, index) => {
          const foundFoliage = _.find(totalFoliages, (totalFoliage) => {
            return totalFoliage.reference_id === foliage.reference_information
          })
          if (foundFoliage) {
            totalFoliages[index].quantity = totalFoliages[index].quantity + foliage.quantity
          } else {
            totalFoliages.push(foliage)
          }
        })
        // match the concentrates and push to totalConcentrates
        concentratesPerDay.forEach((concentrate, index) => {
          const foundConcentrate = _.find(totalConcentrates, (totalConcentrate) => {
            return totalConcentrate.reference_id === concentrate.reference_id
          })
          if (foundConcentrate) {
            totalConcentrates[index].quantity = totalConcentrates[index].quantity + concentrate.quantity
          } else {
            totalConcentrates.push(concentrate)
          }
        })
        // caluclate weekly totalFoliages and totalConcentrates
        weeklyFoliages = _.map(totalFoliages, (foliage) => {
          return { ...foliage, quantity: foliage.quantity * 7 }
        })
        weeklyConcentrates = _.map(totalConcentrates, (concentrate) => {
          return { ...concentrate, quantity: concentrate.quantity * 7 }
        })
      }
      return { totalFoliages, totalConcentrates, weeklyFoliages, weeklyConcentrates, animalDailyFeeds }
    } catch (error) {
      console.log('error', error)
      return { totalFoliages: [], totalConcentrates: [], weeklyFoliages: [], weeklyConcentrates: [], animalDailyFeeds: {} }
    }
  }

  async calculateDailyFeeds ({ headers, data }) {
    let { allAnimalsData, animal_id, foliages, concentrates, userLanguage } = data
    try {
    // flatten foliages & concentrates
      let feedMasterData = require('./feedMaster.json')
      // console.log('feedMasterData', feedMasterData)
      // add concentrates and foliage to feedMasterData
      //  let foliages = getReferenceForCategoryId(10001300)
      // let concentrates = getReferenceForCategoryId(10013000)
      foliages = _.map(foliages, (foliage) => {
        return { ...foliage, ...foliage.reference_information.feed_properties }
      })
      concentrates = _.map(concentrates, (concentrate) => {
        return { ...concentrate, ...concentrate.reference_information.feed_properties }
      })
      // convert %tage to .01 etc
      const usableFoliages = _.filter(foliages, (foliage) => {
        return foliage.dm >= 0 && foliage.dcp >= 0 && foliage.tdn >= 0
      })
      let usableConcentrates = _.filter(concentrates, (concentrate) => {
        return concentrate.dm >= 0 && concentrate.dcp >= 0 && concentrate.tdn >= 0
      })
      usableFoliages.forEach((foliage) => {
        foliage.dm = foliage.dm / 100; foliage.dcp = foliage.dcp / 100; foliage.tdn = foliage.tdn / 100
      })
      usableConcentrates.forEach((concentrate) => {
        concentrate.dm = concentrate.dm / 100; concentrate.dcp = concentrate.dcp / 100; concentrate.tdn = concentrate.tdn / 100
        concentrate.concentrate_type = concentrate.chuni === 'true' ? 'chuni' : concentrate.cereal === 'true' ? 'cereal' : concentrate.deolled_cake === 'true' ? 'deolled_cake' : concentrate.cereal_by_product === 'true' ? 'cereal_by_product' : null
      })
      usableConcentrates = _.filter(usableConcentrates, (concentrate) => { return concentrate.concentrate_type !== null })

      // add foliages and concentrates to feedMasterData
      feedMasterData = { ...feedMasterData, foliages: usableFoliages, concentrates: usableConcentrates }
      feedMasterData = convertBooleanAndNumericValuesDeep(feedMasterData)
      feedMasterData.concentrates_mix_master = [feedMasterData.concentrates_mix_master.cereal, feedMasterData.concentrates_mix_master.chuni, feedMasterData.concentrates_mix_master.deolled_cake, feedMasterData.concentrates_mix_master.cereal_by_product]
      //  The code upto hear can go outside function as its needed one time only
      const { animal_average_lpd_1, animal_weight_1, animal_milk_fat_1 } = allAnimalsData.data.animals[animal_id]
      if (isEmpty(animal_weight_1)) {
        console.log('animal_weight_1 is empty for animal_id', animal_id, 'animal Feed canbe be calculated for this animal')
        return { maintMacros: null, lactationMacros: null, totalMacros: null, dMRoughages: null, dMConcentrates: null, foliagesPerDay: [], shortfalls: null, concentratesPerDay: [] }
      }
      // animal weight is compulsory
      const { farmer_foliage_available_1 } = allAnimalsData.data.customer
      let foundKey = _.find(feedMasterData.cow_bw_dcp_tdn_req, (value, key) => {
        return animal_weight_1 < key
      })
      // get maint macros for given body weight
      const weightKey = foundKey.bodyWeight
      const maintMacros = _.omit(feedMasterData.cow_bw_dcp_tdn_req[weightKey], ['bodyWeight'])
      foundKey = _.find(feedMasterData.cow_milkfat_dcp_tdn_req, (value, key) => {
        return animal_milk_fat_1 < key
      })
      let totalMacros = maintMacros
      if (!isEmpty(foundKey) && !isEmpty(animal_average_lpd_1)) {
      // lactation macro only for lactating animals
        const fatKey = foundKey.fat
        let lactationMacros = _.omit(feedMasterData.cow_milkfat_dcp_tdn_req[fatKey], ['fat', 'dm'])
        // dm Value is incorrect
        lactationMacros = _.each(lactationMacros, (key, value) => { return { key, value: value * animal_average_lpd_1 } })
        // dm to lactationMacros
        lactationMacros.dm = animal_average_lpd_1 * 0.5
        totalMacros = _.mapValues(maintMacros, (value, key) => { return value + lactationMacros[key] })
      } else {
        console.log('animal_milk_fat_1 or animal_averate_lpd_1  empty for animal_id', animal_id, 'no lactation macros')
      }
      // calculate foliages  and concentrates
      const dMRoughages = totalMacros.dm * 0.67
      const dMConcentrates = totalMacros.dm * 0.33
      // if greenlegume is available 1/4 of dMRoughages should be greenlegume
      // if greenlegume is not available 1/3 of dMRoughages should be greenNonLegume
      // 2/3 from any of the dry foliages
      // order 1/3 greenlegume availble or 1/4 greenNonLegume available or 1/3 non available green legume or 1/4 non available green non legume

      const foliageClasses = {
        greenLegume: _.filter(feedMasterData.foliages, (value, key) => { return value.foliage_type === 'green' && value.legume === true }),
        greenNonLegume: _.filter(feedMasterData.foliages, (value, key) => { return value.foliage_type === 'green' && value.legume === false }),
        dryLegume: _.filter(feedMasterData.foliages, (value, key) => { return value.foliage_type === 'dry' && value.legume === true }),
        dryNonLegume: _.filter(feedMasterData.foliages, (value, key) => { return value.foliage_type === 'dry' && value.legume === false })
      }

      let foliagesPerDay = []
      let greenNonLegume = null
      let greenLegume = _.find(foliageClasses.greenLegume, (value, index) => { return farmer_foliage_available_1.includes(value.reference_id) })
      if (greenLegume) {
        foliagesPerDay.push({ type: 'greenLegume', quantity: dMRoughages * 0.25 / (greenLegume.dm), reference_id: greenLegume.reference_id, reference_name_l10n: greenLegume.reference_name_l10n })
      } else {
        greenNonLegume = _.find(foliageClasses.greenNonLegume, (value, index) => { return farmer_foliage_available_1.includes(value.reference_id) })
        if (greenNonLegume) {
          foliagesPerDay.push({ type: 'greenNonLegume', quantity: dMRoughages * 0.33 / (greenNonLegume.dm), reference_id: greenNonLegume.reference_id, reference_name_l10n: greenNonLegume.reference_name_l10n })
        } else if (foliageClasses.greenLegume.length > 0) {
          greenLegume = foliageClasses.greenLegume[0]
          foliagesPerDay.push({ type: 'greenLegume', quantity: dMRoughages * 0.25 / (greenLegume.dm), reference_id: greenLegume.reference_id, reference_name_l10n: greenLegume.reference_name_l10n })
        } else if (foliageClasses.greenNonLegume.length > 0) {
          greenNonLegume = foliageClasses.greenNonLegume[0]
          foliagesPerDay.push({ type: 'greenNonLegume', quantity: dMRoughages * 0.33 / (greenNonLegume.dm), reference_id: greenNonLegume.reference_id, reference_name_l10n: greenNonLegume.reference_name_l10n })
        } else {
        // no green foliage available
          console.log('no green foliage available')
          throw new Error('no green foliage available')
        }
      } // Now calculate dry   3/4 if green was legume or 2/3 if green was non legume preference to available foliages
      const dryAmount = greenLegume ? dMRoughages * 0.75 : dMRoughages * 0.67
      let dryLegume = _.find(foliageClasses.dryLegume, (value, index) => { return farmer_foliage_available_1.includes(value.reference_id) })
      let dryNonLegume = null
      if (dryLegume) {
        foliagesPerDay.push({ type: 'dryLegume', quantity: dryAmount / (dryLegume.dm), reference_id: dryLegume.reference_id, reference_name_l10n: dryLegume.reference_name_l10n })
      } else {
        dryNonLegume = _.find(foliageClasses.dryNonLegume, (value, index) => { return farmer_foliage_available_1.includes(value.reference_id) })
        if (dryNonLegume) {
          foliagesPerDay.push({ type: 'dryNonLegume', quantity: dryAmount / (dryNonLegume.dm), reference_id: dryNonLegume.reference_id, reference_name_l10n: dryNonLegume.reference_name_l10n })
        } else if (foliageClasses.dryLegume.length > 0) {
          dryLegume = foliageClasses.dryLegume[0]
          foliagesPerDay.push({ type: 'dryLegume', quantity: dryAmount / (dryLegume.dm), reference_id: dryLegume.reference_id, reference_name_l10n: dryLegume.reference_name_l10n })
        } else if (foliageClasses.dryNonLegume.length > 0) {
          dryNonLegume = foliageClasses.dryNonLegume[0]
          foliagesPerDay.push({ type: 'dryNonLegume', quantity: dryAmount / (dryNonLegume.dm), reference_id: dryNonLegume.reference_id, reference_name_l10n: dryNonLegume.reference_name_l10n })
        } else {
        // no dry foliage available
          console.log('no dry foliage available')
          throw new Error('no dry foliage available')
        }
      }
      // calculate total nutrients from foliagePerDay
      const nutriantsFromFoliage = { dm: 0, tdn: 0, dcp: 0 }
      _.forEach(foliagesPerDay, (value, key) => {
        const dm = feedMasterData.foliages[key].dm ? value.quantity * (feedMasterData.foliages[key].dm) : 0
        nutriantsFromFoliage.dm += dm
        nutriantsFromFoliage.tdn += feedMasterData.foliages[key].tdn ? dm * (feedMasterData.foliages[key].tdn) : 0
        nutriantsFromFoliage.dcp += feedMasterData.foliages[key].dcp ? dm * (feedMasterData.foliages[key].dcp) : 0
      })
      // calculate shortfalls
      const shortfall = { dm: 0, tdn: 0, dcp: 0 }
      shortfall.dm = totalMacros.dm - nutriantsFromFoliage.dm
      shortfall.tdn = totalMacros.tdn - nutriantsFromFoliage.tdn
      shortfall.dcp = totalMacros.dcp - nutriantsFromFoliage.dcp
      console.log('shortFall', shortfall)
      // caluclate the concentrates using api
      // add a dummy price for each concentrate i.e.  100 + index
      const updatedConcentrates = []
      _.forEach(feedMasterData.concentrates, (value, key) => {
        updatedConcentrates.push({ ...value, price: 100 })
      })
      // replace allAnimalsData.concentrates with updatedConcentrates
      allAnimalsData.concentrates = updatedConcentrates
      const callHeaders = { ..._.cloneDeep(headers), useCase: 'calculateDailyFeeds' }
      const results = await getConcentrates({ reqBody: { concentrates, shortfall, concentrates_mix_master: feedMasterData.concentrates_mix_master, userLanguage }, headers: callHeaders })
      let concentratesPerDay = (results && results.data && results.data.quantities && results.data.quantities.length > 0) ? results.data.quantities : []
      if (isEmpty(concentratesPerDay) && isEmpty(foliagesPerDay)) {
        console.log('no concentrates available or no foliages available')
        return { maintMacros: null, totalMacros: null, dMRoughages: null, dMConcentrates: null, foliagesPerDay: [], shortfalls: null, concentratesPerDay: [] }
      }
      // both foliagesPerDay and concentratesPerDay are available save them to DB
      // check if there are valid foliagesPerDay and concentratesPerDay
      foliagesPerDay = _.isArray(foliagesPerDay) ? foliagesPerDay : []
      concentratesPerDay = _.isArray(concentratesPerDay) ? concentratesPerDay : []
      foliagesPerDay = _.filter(foliagesPerDay, (value, index) => { return value.quantity > 0 })
      concentratesPerDay = _.filter(concentratesPerDay, (value, index) => { return value.quantity > 0 })

      const updateData = { animal_feed_recommendations: { foliagesPerDay, concentratesPerDay } }
      const savedResult = await saveData({
        entity: 'animal',
        parent_id: animal_id,
        data: updateData
      })
      console.log('savedResult', savedResult)
      return { maintMacros, totalMacros, dMRoughages, dMConcentrates, foliagesPerDay, shortfalls: shortfall, concentratesPerDay }
    } catch (error) {
      console.log('error', error)
      return { maintMacros: null, totalMacros: null, dMRoughages: null, dMConcentrates: null, foliagesPerDay: [], shortfalls: null, concentratesPerDay: [] }
    }
  }
}

const getConcentrates = async ({ headers, reqBody }) => {
  try {
    console.log('ahr pR gF 1')
    //  const SUPPORTED_OFFLINE = false // this has to be server call irrespective of offline or online
    console.log('ahr pR gF 2')
    const response = await post(configurationJSON().SERVICE_END_POINTS.BACK_END + '/animalFeeds/calculateConcentrates', headers, reqBody)
    // console.log('response = ', response)
    if (response && response.data) {
      console.log('ahr pR gF 2a, response.data = ', response.data)
      return response.data
    } else {
      console.log('ahr pR gF 2b, response.data  or response not correct', response)
      throw new Error('response.data  or response not correct')
    }
  } catch (error) {
    console.log('error in getConcentrates on router', error)
    throw error
  }
}

const saveData = async ({ entity, parent_id, data }) => {
  try { //  I expect this to work seemlessly online of offline
    const classifierType = { customer: 'CUSTOMER_CLASSIFICATION', animal: 'ANIMAL_CLASSIFICATION' }[entity]
    let response = -1
    await dbConnections().main.manager.transaction(async transactionalEntityManager => {
      response = await saveClassificationData(transactionalEntityManager, classifierType, parent_id, data)
      console.log('ad 10 saveClassificationData response', response)
    })
    return response
  } catch (e) {
    console.log('error in saveData', e)
    throw e
  }
}

function convertBooleanAndNumericValues (obj) {
  if (!_.isObject(obj)) {
    if (_.isString(obj)) {
      if (obj === 'true') return true
      if (obj === 'false') return false
      if (obj === 'null') return null
      if (obj === 'undefined') return undefined
      if (!isNaN(obj)) {
        const parsed = parseFloat(obj)
        if (Number.isInteger(parsed)) return parsed
        return parsed
      }
    }
    return obj
  }
  if (_.isArray(obj)) return _.map(obj, convertBooleanAndNumericValues)
  return _.mapValues(obj, (value) => convertBooleanAndNumericValues(value))
}

function convertBooleanAndNumericValuesDeep (obj) {
  return _.cloneDeep(convertBooleanAndNumericValues(obj))
}
const isEmpty = (value) => {
  if (value === undefined || value == null || value === '' | value === 'null' || value === 'undefined' ||
  value === '[]' || value === '{}' || value === '0' || value === 0 || value === '0.0' || value === 0.0 || value === [] || value === {}) {
    return true
  } else return false
}
module.exports = { LibAnimalFeeds }
