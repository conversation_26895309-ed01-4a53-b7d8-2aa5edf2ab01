/* eslint-disable eqeqeq */
/* eslint-disable no-unused-vars */
/* eslint-disable camelcase */
const { saveClassificationData } = require('../common/classification.helper')
const { configurationJSON, post } = require('@krushal-it/common-core')
const _ = require('lodash')
const { dbConnections } = require('@krushal-it/ah-orm')
const { getReferenceForCategoryId } = require('../common/common.class')
const { loadClassificationData, getClassificationConfiguration } = require('../common/classification.helper')
const { LibCustomerService } = require('../customer/customer.class')
const { LibAnimalService } = require('../animal/animal.class')
const { LibAsset } = require('../asset/asset.class')
const { LibAssetFeature } = require('../asset_feature/asset_feature.class')
const { loadClassificationDataWithClassifierIds } = require('../common/classification.helper')
const { getDBConnections } = require('@krushal-it/ah-orm')

class LibSmartFeed {
  async create (data, params) { // post
    try {
      console.log('in create')
      console.log('called with data ', data, ' and params', params)
      const queryParameters = params.query || {}
      const conflictPaths = Object.keys(queryParameters) || []
      const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
      const asset = await getDBConnections().main.manager.transaction(async (transactionalEntityManager) => {
        const assetLib = new LibAsset()
        const assetData = _.cloneDeep(data)
        for (const attribute of ['smart_ration_concentrates', 'smart_ration_foliages', 'smart_ration_other_nutrients']) {
          // assetData.smart_ration_foliages = assetData?.smart_ration_foliages ? Object.keys(assetData?.smart_ration_foliages) : []
          assetData[attribute] = assetData[attribute] ? Object.keys(assetData[attribute]) : []
        }
        // assetData.smart_ration_concentrates = assetData?.smart_ration_concentrates ? Object.keys(assetData?.smart_ration_concentrates) : []
        assetData.start_date = assetData?.start_date ? new Date(assetData?.start_date) : new Date()
        assetData.end_date = assetData?.end_date ? new Date(assetData?.end_date) : new Date('2030-01-01')
        const returnData = await assetLib.create(assetData, params, transactionalEntityManager)

        if (!returnData || !returnData?.data || returnData?.return_code != 0) {
          throw new Error("Couldn't create asset")
        }
        const asset = returnData.data
        return asset
        // since classifiers are not returned in transaction lets close txn and get the classification Data
      })
      // start a transaction to create the asset features
      const assetConfig = getClassificationConfiguration()?.ASSET_CLASSIFICATION
      const assetClassifierAttributeArray = assetConfig?.ATTRIBUTE_LIST ? Object.keys(assetConfig?.ATTRIBUTE_LIST) : []

      const result = await getDBConnections().main.manager.transaction(async (transactionalEntityManager) => {
        let assetClassificationsData = await loadClassificationDataWithClassifierIds(transactionalEntityManager, 'ASSET_CLASSIFICATION', asset.asset_id, assetClassifierAttributeArray)
        assetClassificationsData = assetClassificationsData?.dataWithClassifierIds || []
        // create asset features with each classifierIds
        // build asset_feature data from data and assetClassificationsData and save each of them
        const assetFeatures = []
        // save asset features for smart_foliages and smart_concentrates
        let classification_id = null
        for (const attribute of ['smart_ration_concentrates', 'smart_ration_foliages', 'smart_ration_other_nutrients']) {
          if (!data[attribute]) {
            continue
          }

          for (const smartRation of Object.keys(data[attribute])) {
            const rationClassificationData = assetClassificationsData[attribute] || {}
            const foundRation = rationClassificationData.filter((item) => { return Object.values(item)[0] == smartRation })
            classification_id = foundRation.length > 0 ? Object.keys(foundRation[0])[0] : null
            if (!classification_id) {
              continue
            }
            const assetFeatureData = {
              asset_classification_id: classification_id,
              feature_name_l10n: `${attribute}_${smartRation}`,
              feature_type_id: 0,
              smart_ration_weight: data[attribute][smartRation]?.smart_ration_weight,
              smart_ration_cost: data[attribute][smartRation]?.smart_ration_cost,
              smart_ration_price_per_kg: data[attribute][smartRation]?.smart_ration_price_per_kg
            }
            const assetFeatureLib = new LibAssetFeature()
            const assetFeature = await assetFeatureLib.create(assetFeatureData, params, transactionalEntityManager)
            assetFeatures.push(assetFeature)
          }
        }
        asset.assetFeatures = assetFeatures
        return { return_code: 0, data: asset }
      })
      return result
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    } // now create the features for the each asset smart feed row
  }

  async get (id, params) {
    try {
      console.log('in get')
      console.log('called with id ', id, ' and params', params)
      const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
      const mainSchemaAddition = isPostgres ? 'main.' : ''
      const assetConfig = getClassificationConfiguration()?.ASSET_CLASSIFICATION
      const assetClassifierAttributeArray = assetConfig?.ATTRIBUTE_LIST ? Object.keys(assetConfig?.ATTRIBUTE_LIST) : []
      const smartFeedData = {}
      smartFeedData.smart_ration_foliages = {}
      smartFeedData.smart_ration_concentrates = {}
      smartFeedData.smart_ration_other_nutrients = {}

      const assetQuery = `
        select er.entity_2_entity_uuid  as asset_id from ${mainSchemaAddition}entity_relationship er 
        where 
          entity_1_type_id = 1000460001 
          and entity_1_entity_uuid =  '${id}'
          and entity_relationship_type_id = 1000210027
        order by created_at desc 
        limit 1 `
      const result = await getDBConnections().main.manager.query(assetQuery)
      const asset_id = result && result.length > 0 ? result[0].asset_id : null
      if (!asset_id) return { return_code: 0, data: {} }
      let assetClassificationsData = await loadClassificationDataWithClassifierIds(null, 'ASSET_CLASSIFICATION', asset_id, assetClassifierAttributeArray)
      assetClassificationsData = assetClassificationsData?.dataWithClassifierIds || []
      // create asset features with each classifierIds
      // build asset_feature data from data and assetClassificationsData and save each of them
      // copy dm_min, dm_max, dcp and tdn (applicable only for animal)
      for (const attribute of ['dm_min', 'dm_max', 'dcp', 'tdn']) {
        smartFeedData[attribute] = assetClassificationsData[attribute] ? assetClassificationsData[attribute][0][attribute] : null
      }
      const assetFeatures = []
      // save asset features for smart_foliages and smart_concentrates
      let classification_id = null
      for (const attribute of ['smart_ration_concentrates', 'smart_ration_foliages', 'smart_ration_other_nutrients']) {
        const rationClassificationData = assetClassificationsData[attribute] || {}
        for (const smartRation of rationClassificationData) {
          classification_id = Object.keys(smartRation)[0]
          // get the asset_feature id
          const assetFeatureQuery = `
                select af.asset_feature_id from ${mainSchemaAddition}asset_feature af
                where
                  af.asset_classification_id = '${classification_id}'
                  order by created_at desc 
                  limit 1`
          const result = await getDBConnections().main.manager.query(assetFeatureQuery)
          const asset_feature_id = result && result.length > 0 ? result[0].asset_feature_id : null
          if (!asset_feature_id) continue
          // now get all the asset feature classification data
          const assetFeatureClassificationData = await loadClassificationDataWithClassifierIds(null, 'ASSET_FEATURE_CLASSIFICATION', asset_feature_id, ['smart_ration_weight', 'smart_ration_cost', 'smart_ration_price_per_kg'])
          const assetFeatureClassification = assetFeatureClassificationData?.data || []
          const rationData = {}
          for (const attribute of ['smart_ration_weight', 'smart_ration_cost', 'smart_ration_price_per_kg']) {
            rationData[attribute] = assetFeatureClassification[attribute] ? assetFeatureClassification[attribute][0][attribute] : null
          }
          smartFeedData[attribute][smartRation].push({ rationData })
        }
      }
      return { return_code: 0, data: smartFeedData }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async find (params) {
    try {
      console.log('in find')
      console.log('called with  params', params)
      // either asset_id or { entity_1_type_id, entity_1_entity_uuid, entity_relationship_type_id } should be present
      const queryParameters = params.query || {}
      if ((!queryParameters?.asset_id) && (!queryParameters?.entity_1_type_id || !queryParameters?.entity_1_entity_uuid || !queryParameters?.entity_relationship_type_id)) {
        return { return_code: -1, message: 'asset_id or { entity_1_type_id, entity_1_entity_uuid, entity_relationship_type_id } should be present' }
      }
      const feed_generation_type = queryParameters?.feed_generation_type
      const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
      const mainSchemaAddition = isPostgres ? 'main.' : ''
      const assetConfig = getClassificationConfiguration()?.ASSET_CLASSIFICATION
      const assetClassifierAttributeArray = assetConfig?.ATTRIBUTE_LIST ? Object.keys(assetConfig?.ATTRIBUTE_LIST) : []
      let assetQuery = ''
      let assetResults = []
      if (queryParameters?.asset_id) { // with asset id it should be single
        assetQuery = `
        select entity_2_entity_uuid  as asset_id, entity_1_type_id, entity_relationship_type_id , a.asset_name_l10n
        from ${mainSchemaAddition}entity_relationship er, ${mainSchemaAddition}asset a
        where
          a.asset_id = er.entity_2_entity_uuid
          and er.entity_2_entity_uuid = '${queryParameters?.asset_id}'
        order by a.created_at desc
        limit 1  
        `
        assetResults = await getDBConnections().main.manager.query(assetQuery)
      } else {
        // entity_1_type_id is asset then there will be multile assets for a single entity_1_entity_uuid and we have to pick up all
        // else there will be multiple however we have to pick up the latest one
        // if feed_generation_type is present then we have to pick up the latest
        if (queryParameters?.entity_1_type_id == 1000460012) {
          assetQuery = `
          select a.asset_id, a.created_at  ,er.entity_2_entity_uuid  as asset_id2, er.entity_1_type_id, 
            er.entity_relationship_type_id, a.asset_name_l10n,
            a2ar.entity_1_entity_uuid as animal_id
          from ${mainSchemaAddition}entity_relationship er
          inner join ${mainSchemaAddition}asset a on a.asset_id = er.entity_2_entity_uuid and a.active = 1000100001
          left join ${mainSchemaAddition}entity_relationship a2ar on a2ar.active = 1000100001
          	and a2ar.entity_relationship_type_id = 1000210028
          	and a2ar.entity_2_entity_uuid = er.entity_2_entity_uuid
          where er.entity_1_type_id = ${queryParameters?.entity_1_type_id} and er.active = 1000100001
            and er.entity_1_entity_uuid = '${queryParameters?.entity_1_entity_uuid}'
            and er.entity_relationship_type_id = ${queryParameters?.entity_relationship_type_id} 
          order by a.created_at desc`
          assetResults = await getDBConnections().main.manager.query(assetQuery)
        } else {
        // const limitQuery = queryParameters?.entity_1_type_id == 1000460012 ? '' : 'limit 1'
          assetQuery = `
          select a.asset_id, a.created_at  ,er.entity_2_entity_uuid  as asset_id2, er.entity_1_type_id, 
            er.entity_relationship_type_id, a.asset_name_l10n
          from ${mainSchemaAddition}entity_relationship er, ${mainSchemaAddition}asset a
          where
            a.asset_id = er.entity_2_entity_uuid  
            and  entity_1_type_id = ${queryParameters?.entity_1_type_id}
            and entity_1_entity_uuid =  '${queryParameters?.entity_1_entity_uuid}'
            and entity_relationship_type_id = ${queryParameters?.entity_relationship_type_id} 
          order by a.created_at desc 
          limit 1`
          assetResults = await getDBConnections().main.manager.query(assetQuery)
        }
      }
      // there will be more than 1 assets specally for animals hence the assets data will be iterated
      const smartFeedsData = []
      console.log('result', assetResults.length, assetResults)
      for (let i = 0; i < assetResults.length; i++) {
        const asset = assetResults[i]
        const asset_id = asset?.asset_id
        if (!asset_id) {
          console.log('asset_id not found', asset)
          continue
        }
        const smartFeedData = {}
        smartFeedData.smart_ration_foliages = {}
        smartFeedData.smart_ration_concentrates = {}
        smartFeedData.smart_ration_other_nutrients = {}
        smartFeedData.asset_id = asset_id
        smartFeedData.entity_relationship_type_id = asset?.entity_relationship_type_id
        smartFeedData.entity_1_type_id = asset?.entity_1_type_id
        smartFeedData.asset_name_l10n = asset?.asset_name_l10n
        if (asset?.animal_id) {
          smartFeedData.animal_id = asset?.animal_id
        }

        let assetClassificationsData = await loadClassificationDataWithClassifierIds(null, 'ASSET_CLASSIFICATION',
          asset_id, assetClassifierAttributeArray)
        const assetClassificationsDataWithId = assetClassificationsData?.dataWithClassifierIds || []
        assetClassificationsData = assetClassificationsData?.data
        // create asset features with each classifierIds
        // build asset_feature data from data and assetClassificationsData and save each of them
        // copy dm_min, dm_max, dcp and tdn (applicable only for animal)
        // for (const attribute of ['dm_min', 'dm_max', 'dcp', 'tdn', 'asset_type_id', 'asset_name_l10n', 'feed_generation_type', 'user_device_id']) {
        //   smartFeedData[attribute] = assetClassificationsData[attribute]
        // }
        for (const attribute of Object.keys(assetClassificationsData)) {
          if (['smart_ration_concentrates', 'smart_ration_foliages', 'smart_ration_other_nutrients'].includes(attribute)) continue
          smartFeedData[attribute] = assetClassificationsData[attribute]
        }

        const assetFeatures = []
        // save asset features for smart_foliages and smart_concentrates
        let classification_id = null
        for (const attribute of ['smart_ration_concentrates', 'smart_ration_foliages', 'smart_ration_other_nutrients']) {
          smartFeedData[attribute] = []
          const rationClassificationData = assetClassificationsDataWithId[attribute] || []
          for (const smartRation of rationClassificationData) {
            classification_id = Object.keys(smartRation)[0]
            const reference_id = smartRation[classification_id]

            // get the asset_feature id
            const assetFeatureQuery = `
                select af.asset_feature_id from ${mainSchemaAddition}asset_feature af
                where
                  af.asset_classification_id = '${classification_id}'
                  order by created_at desc 
                  limit 1`
            const result = await getDBConnections().main.manager.query(assetFeatureQuery)
            const asset_feature_id = result && result.length > 0 ? result[0].asset_feature_id : null
            if (!asset_feature_id) continue
            // now get all the asset feature classification data
            let assetFeatureClassificationData = await loadClassificationDataWithClassifierIds(null, 'ASSET_FEATURE_CLASSIFICATION',
              asset_feature_id, ['smart_ration_weight', 'smart_ration_cost', 'smart_ration_price_per_kg'])
            assetFeatureClassificationData = assetFeatureClassificationData?.data || []
            const rationData = { reference_id }
            for (const attribute of ['smart_ration_weight', 'smart_ration_cost', 'smart_ration_price_per_kg']) {
              rationData[attribute] = assetFeatureClassificationData[attribute] || null
            }
            smartFeedData[attribute].push(rationData)
          }
        }
        smartFeedsData.push(smartFeedData)
      }
      return { return_code: 0, data: smartFeedsData }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

module.exports = { LibSmartFeed }
