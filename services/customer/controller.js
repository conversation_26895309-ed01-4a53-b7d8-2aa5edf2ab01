const moment = require('moment')
const { configurationJSO<PERSON>, KrushalError } = require('@krushal-it/common-core');
const { entity_type, staff_activity_statuses, preventive_healthcare_activities, moment_keys } = require('../../ENUMS')
const { dbConnections } = require('@krushal-it/ah-orm');
const { generateVisualId } = require('../../utils/customer');
const { customerListing } = require('../../queries/customer');
const { ENTITY_TYPE_CUSTOMER } = require('../../utils/constant');
const { getLocalClassifierConfig } = require('../../utils/reference');
const { insertToCustomer, updateCustomerLocal } = require('../../queries/customer');
const { getClassificationConfiguration, saveClassificationData } = require('../common/classification.helper');
const { infoLog } = require('@krushal-it/mobile-or-server-lib');
const { values } = require('lodash');
const { getCompiledQuery } = require('../../queries/index');
const taskRepository = require('../../repositories/taskRepository');
const { ActivityHelper } = require('../../utils/activity')
const getCustomerList_v3 = async (options = {}) => {
  const { user_id, user_type, page_limit, page_number } = options;
  let customerOptions = {
    user_id,
    user_type,
    page_limit,
    page_number,
    f_farmer_name: options.f_farmer_name,
    f_farmer_mobile: options.f_farmer_mobile,
    f_farmer_village: options.f_farmer_village,
    f_farmer_taluka: options.f_farmer_taluka,
    f_farmer_visual_id: options.f_farmer_visual_id,
    f_paravet_name: options.f_paravet_name,
    f_village_id_array: options.f_village_id_array,
    f_village: options.f_village,
    f_not_assigned:options.f_not_assigned,
    f_not_subscribed:options.f_not_subscribed
  };
  let { result, total_page } = await customerListing(customerOptions);
  return {
    data: result,
    total_page,
  };
};

const registerCustomer = async (data, token) => {
  const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false;
  const PAGE = data.page;
  const ENTITY = ENTITY_TYPE_CUSTOMER;
  const dataSource = dbConnections().main.manager;
  let generateCustomerVisualId = false;
  const newCustomer = data.customer_id ? false : true
  const { user_id, user_type } = token
  // All of the local fiedls are expected in page 1
  if (PAGE === 1) {
    // Handle all local fiedls here
    const localCLassifier = getLocalClassifierConfig(ENTITY);
    const localCLassifierKeys = Object.keys(localCLassifier);
    const values = {};
    // Pulling keys from verified key list
    localCLassifierKeys.forEach((key) => {
      if (localCLassifier[key].isJson && data[key]) {
        values[key] = {
          ul: data[key]
        };
      } else if (data[key]) {
        values[key] = data[key];
      }
    });
    if (values['mobile_number']) {
      if (values['mobile_number'].length === 10) {
        values['mobile_number'] = `+91${values['mobile_number']}`;
      }
      if (values['alternate_mobile_number'] && values['alternate_mobile_number'].length === 10) {
        values['alternate_mobile_number'] = `+91${values['alternate_mobile_number']}`;
      }
      const mainSchemaAddition = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 'main.' : '';
      let duplicateMobileCheckQuery = `
        select * 
        from ${mainSchemaAddition}customer where active = 1000100001 and mobile_number = '${values['mobile_number']}'
      `;
      if (data.customer_id && data.customer_id !== -1) {
        duplicateMobileCheckQuery = duplicateMobileCheckQuery + ` and customer_id != '${data.customer_id}' `;
      }
      const duplicateMobileCheckQueryResult = await dbConnections().main.manager.query(duplicateMobileCheckQuery);
      if (duplicateMobileCheckQueryResult.length > 0) {
        throw new KrushalError('Duplicate Mobile Number', -1011);
      }
    }
    // if customer id is not recived, consider it as new customer to be added
    if (!data.customer_id || data.customer_id === -1) {
      generateCustomerVisualId = true;
      
      // This is important for farmer to login. if its removed from here,
      // Please make sure this is always present before new customer registeration
      values['customer_type_id'] = entity_type.CUSTOMER_LEGACY;

      data.customer_id = await insertToCustomer(values);
      data.customer_id = data.customer_id.identifiers[0].customer_id;
    } else {
      // Checks primary table if user exists with same id, if exists update local else sends not found
      let oldCustomer = await dbConnections().main.repos['customer'].find({
        where: [{ customer_id: data.customer_id }],
        select: { customer_id: true },
      });
      if (!Array.isArray(oldCustomer) || oldCustomer.length <= 0) {
        return { message: 'Not found' };
      }
      if (Object.keys(values).length > 0) {
        await updateCustomerLocal(values, {
          customer_id: data.customer_id,
        });
      }
    }
  }

  const dataKeyList = Object.keys(data);
  const classificationConfig = getClassificationConfiguration()['CUSTOMER_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP;
  let location = {};
  const upsertData = {};
  for (let keyIndex = 0; keyIndex < dataKeyList.length; keyIndex++) {
    if (classificationConfig[dataKeyList[keyIndex]]) upsertData[dataKeyList[keyIndex]] = data[dataKeyList[keyIndex]];
    if (dataKeyList[keyIndex] === 'farmer_state_1') {
      location[2000000052] = Array.isArray(data[dataKeyList[keyIndex]]) ? data[dataKeyList[keyIndex]][0] : data[dataKeyList[keyIndex]];
    }
    if (dataKeyList[keyIndex] === 'farmer_district_1') {
      location[2000000053] = Array.isArray(data[dataKeyList[keyIndex]]) ? data[dataKeyList[keyIndex]][0] : data[dataKeyList[keyIndex]];
    }
    if (dataKeyList[keyIndex] === 'farmer_taluka_1') {
      location[2000000054] = Array.isArray(data[dataKeyList[keyIndex]]) ? data[dataKeyList[keyIndex]][0] : data[dataKeyList[keyIndex]];
    }
    if (dataKeyList[keyIndex] === 'farmer_village_1') {
      location[2000000055] = Array.isArray(data[dataKeyList[keyIndex]]) ? data[dataKeyList[keyIndex]][0] : data[dataKeyList[keyIndex]];
    }
  }
  await dataSource.transaction(async (transactionalEntityManager) => {
    infoLog(upsertData);
    await saveClassificationData(transactionalEntityManager, 'CUSTOMER_CLASSIFICATION', data.customer_id, upsertData);
    if (isPostgres && generateCustomerVisualId) {
      let visual_id = await generateVisualId(location['2000000052'], location['2000000053'], location['2000000054'], { manager: transactionalEntityManager });
      if (visual_id) {
        await updateCustomerLocal({ customer_visual_id: visual_id }, { manager: transactionalEntityManager, customer_id: data.customer_id });
      }
    }
    if (newCustomer){
      //create a farm management task
      await createFarmManagement(data.customer_id , user_id, transactionalEntityManager)
    }
 });

  return data.customer_id;
};

const createFarmManagement = async (customer_id, user_id, transaction) => {
  try {
    const classifierData = {}

    let FMTask = {
      entity_uuid: customer_id,
      entity_type_id: entity_type.CUSTOMER,
      activity_id: preventive_healthcare_activities.FARM_MANAGEMENT,
      activity_date: moment().startOf('day').format(moment_keys.DATE_FORMAT),
      calendar_activity_status: staff_activity_statuses.NEW_CASE_VISIT_PENDING,
      visit_schedule_time: moment().toISOString(),
      visit_creation_time: moment().toISOString(),
      activity_created_by: user_id,
    }

    const cc_id = await taskRepository.createTask(transaction, FMTask)
    const ticketNumber = await ActivityHelper.generateTicketNumber(transaction)
    classifierData['ticket_1'] = ticketNumber
    await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', cc_id, classifierData)

    return 'successfully created FM task'
  } catch (error) {
    throw new Error(error)
  }
}

const getUpcomingFarmerTasks = async (options = {}) => {

  const { user_id } = options;
  const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0;
  const templateParameters = {
    IS_POSTGRES: isPostgres,
  };
  const queryParameters = { user_id }
  const resultSize = 1;
  let query = getCompiledQuery('get-farmer-tasks', templateParameters);

  const queryBuilder =
    await dbConnections().main.manager.createQueryBuilder()
      .from(`(${query})`, 'customer-task-list')
      .setParameters(queryParameters)
      .limit(resultSize);

  const result = await queryBuilder.execute();

  return {
    data: result
  };

};


module.exports = { getCustomerList_v3, registerCustomer, getUpcomingFarmerTasks };
