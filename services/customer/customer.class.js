const { KrushalError } = require('@krushal-it/common-core')
const { getCustomerList_v3, registerCustomer, getUpcomingFarmerTasks } = require('./controller')
const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm')
const { q_getCustomerPrimaryTable } = require('../../queries/customer')
const { getDocumentJSONConfig } = require('../../utils/document')
const { getImagesByFarmerId } = require('../../queries/document')
const { getClassificationConfiguration, loadClassificationData } = require('../common/classification.helper')
const uuid = require('uuid')
const { CONST_TYPES_STAFF } = require('../../utils/constant')
const { errorLog } = require('@krushal-it/mobile-or-server-lib')
const { validateUUID } = require('@krushal-it/mobile-or-server-lib')
const { configurationJSON } = require('@krushal-it/common-core');
const { getCompiledQuery } = require('../../queries/index.js');

class CustomerListLib {
  async create(data, params) {
    try {
      // let {token} = params.headers;
      let token = testToken
      const { user_type, user_id } = token
      const baseQuery = q_customerListing({ user_type, user_id })
      let notAndFlag = true
      let queryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${baseQuery})`)
      if (data.f_farmer_name) {
        const { f_farmer_name } = data
        let param = { f_farmer_name: `%${f_farmer_name}%` }
        let condition = `(
         UPPER(customer_name_l10n->>'ul') LIKE UPPER(:f_farmer_name) OR 
         UPPER(customer_name_l10n->>'en') LIKE UPPER(:f_farmer_name) OR 
         UPPER(customer_name_l10n->>'mr') LIKE UPPER(:f_farmer_name) OR 
         UPPER(customer_name_l10n->>'hi') LIKE UPPER(:f_farmer_name))`
        notAndFlag ? queryBuilder.where(condition, param) : queryBuilder.andWhere(condition, param)
        notAndFlag = false
      }
      if (data.f_farmer_village) {
        const { f_farmer_village } = data
        let param = { f_farmer_village: `%${f_farmer_village}%` }
        let condition = `(
        UPPER(village_name_l10n->>'en') LIKE UPPER(:f_farmer_village) OR
        UPPER(village_name_l10n->>'en') LIKE UPPER(:f_farmer_village) OR
        UPPER(village_name_l10n->>'en') LIKE UPPER(:f_farmer_village) OR
        UPPER(village_name_l10n->>'en') LIKE UPPER(:f_farmer_village) 
        )`
        notAndFlag ? queryBuilder.where(condition, param) : queryBuilder.andWhere(condition, param)
        notAndFlag = false
      }
      if (data.f_farmer_taluka) {
        const { f_farmer_taluka } = data
        let param = { f_farmer_taluka: `%${f_farmer_taluka}%` }
        let condition = `(
         UPPER(taluk_name_l10n->>'ul') LIKE UPPER(:f_farmer_taluka) OR
         UPPER(taluk_name_l10n->>'en') LIKE UPPER(:f_farmer_taluka) OR
         UPPER(taluk_name_l10n->>'mr') LIKE UPPER(:f_farmer_taluka) OR
         UPPER(taluk_name_l10n->>'hi') LIKE UPPER(:f_farmer_taluka) 
        )`
        notAndFlag ? queryBuilder.where(condition, param) : queryBuilder.andWhere(condition, param)
        notAndFlag = false
      }
      if (data.f_farmer_visual_id) {
        const { f_farmer_visual_id } = data
        let param = { f_farmer_visual_id: `%${f_farmer_visual_id}%` }
        let condition = `UPPER(customer_visual_id) LIKE UPPER(:f_farmer_visual_id)`
        notAndFlag ? queryBuilder.where(condition, param) : queryBuilder.andWhere(condition, param)
        notAndFlag = false
      }
      if (data.f_farmer_mobile) {
        const { f_farmer_mobile } = data
        let param = { f_farmer_mobile: `%${f_farmer_mobile}%` }
        let condition = `mobile_number LIKE :f_farmer_mobile`
        notAndFlag ? queryBuilder.where(condition, param) : queryBuilder.andWhere(condition, param)
        notAndFlag = false
      }
      if (data.f_paravet_name) {
        const { f_paravet_name } = data
        let param = { f_paravet_name: `%${f_paravet_name}%` }
        let condition = `(
         UPPER(paravet_name->>'ul') LIKE UPPER(:f_paravet_name) OR
         UPPER(paravet_name->>'en') LIKE UPPER(:f_paravet_name) OR
         UPPER(paravet_name->>'mr') LIKE UPPER(:f_paravet_name) OR
         UPPER(paravet_name->>'hi') LIKE UPPER(:f_paravet_name))`
        notAndFlag ? queryBuilder.where(condition, param) : queryBuilder.andWhere(condition, param)
        notAndFlag = false
      }

      queryBuilder.orderBy('greatest(max_updated_at,updated_at)', 'DESC')
      const q_count = `SELECT COUNT(*) as count FROM (${queryBuilder.getSql()}) as c`
      const params = queryBuilder.getQueryAndParameters()[1]
      let count = await dbConnections().main.manager.query(q_count, params)
      count = count[0].count
      let total_page = 1
      if (!isNaN(data.page_number) && !isNaN(data.page_limit)) {
        let offset = data.page_limit * (data.page_number - 1)
        if (offset < 0) return { message: 'Invalid page number' }
        total_page = parseInt(count / data.page_limit)
        total_page += count % data.page_limit === 0 ? 0 : 1
        queryBuilder.limit(data.page_limit).offset(offset)
      }
      let result = await queryBuilder.execute()
      return { result, total_page }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class LibCustomerService {
  async get(id, params) {
    try {
      if (!validateUUID(id)) return {}
      const { token } = params.headers
      const { user_id, user_type } = token

      let queryOption = { user_id, user_type }
      queryOption.customer_id = user_type === 1000220001 ? user_id : id

      const primaryQuery = q_getCustomerPrimaryTable(queryOption)
      let customerPrimaryData = await dbConnections().main.manager.query(primaryQuery)
      postProcessRecords(undefined, customerPrimaryData, { json_columns: ['customer_name_l10n', 'village_name_l10n'] })
      if (customerPrimaryData.length > 0) customerPrimaryData = customerPrimaryData[0]
      else return {}
      let classifiers = Object.keys(getClassificationConfiguration()['CUSTOMER_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP)
      let classificationData = await loadClassificationData('CUSTOMER_CLASSIFICATION', queryOption.customer_id, classifiers)

      if (classificationData['farmer_village_1']) {
        // get state and populate it
        const stateBasedOnVillageQuery = `
          select *
          from main.ref_sdtv_view
          where village_id = ${classificationData['farmer_village_1'][0]}
        `
        const sdtvResultsForVillage = await dbConnections().main.manager.query(stateBasedOnVillageQuery)
        if (sdtvResultsForVillage.length > 0) {
          classificationData['farmer_state_1'] = [sdtvResultsForVillage[0].state_id]
        } else {
          classificationData['farmer_state_1'] = []
        }
      }

      if (classificationData['customer_tnc_status'] === 1000105001) {
        classificationData['customer_tnc_status'] = "Accepted"
      }
      else {
        classificationData['customer_tnc_status'] = "Not Accepted"
      }

      let customer = { ...customerPrimaryData, ...classificationData }
      customer.mobile_number = customer.mobile_number.startsWith('+91') ? customer.mobile_number.slice(3) : customer.mobile_number
      let documents = await getImagesByFarmerId(id)
      customer.files = await mapDocumentType(documents)
      return customer
    } catch (error) {
      errorLog('error in LibCustomerService:35', params, { message: error.message })
      return { result: false, message: error.message }
    }
  }

  async getCustomerDetails(data, params) {
    try {
      const { token } = params.headers
      const { user_id, user_type } = token

      let queryOption = { user_id, user_type }

      // if the request comes from a customer, customer id is taken from user_id attribute
      // present in the token
      queryOption.customer_id = user_type === 1000220001 ? user_id : data.customer_id

      let customerPrimaryData = {}

      // Get basic customer details if requested
      if (data.basic_info.required) {
        const primaryQuery = q_getCustomerPrimaryTable(queryOption)
        customerPrimaryData = await dbConnections().main.manager.query(primaryQuery)
        if (customerPrimaryData.length === 0) {
          return false
        }
        postProcessRecords(undefined, customerPrimaryData, { json_columns: ['customer_name_l10n', 'village_name_l10n'] })
        customerPrimaryData = customerPrimaryData[0]
      }

      let classificationData = {}

      // Fetching classifiers of customer based on request
      if (data.additional_info.required) {
        let classifiers = Object.keys(getClassificationConfiguration()['CUSTOMER_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP)

        if (data.additional_info.include_all) {
          // use all classifiers
        } else if (data.additional_info.attributes.length > 0) {
          // find valid keys from the requested attributes
          classifiers = classifiers.filter((item) => data.additional_info.attributes.indexOf(item) !== -1)
        } else {
          // do nothing
          classifiers = []
        }

        if (classifiers.length > 0) {
          classificationData = await loadClassificationData('CUSTOMER_CLASSIFICATION', queryOption.customer_id, classifiers)
        }
      }

      if (classificationData['farmer_village_1']) {
        // get state and populate it
        const stateBasedOnVillageQuery = `
          select *
          from main.ref_sdtv_view
          where village_id = ${classificationData['farmer_village_1'][0]}
        `
        const sdtvResultsForVillage = await dbConnections().main.manager.query(stateBasedOnVillageQuery)
        if (sdtvResultsForVillage.length > 0) {
          classificationData['farmer_state_1'] = [sdtvResultsForVillage[0].state_id]
        } else {
          classificationData['farmer_state_1'] = []
        }
      }

      let customer = { ...customerPrimaryData, ...classificationData }

      if (data.documents.required) {
        let documents = await getImagesByFarmerId(queryOption.customer_id)
        customer.files = await mapDocumentType(documents)
      }

      return customer
    } catch (error) {
      errorLog('error in getCustomerDetails:35', params, { message: error.message })
      return { result: false, message: error.message }
    }
  }

  async acceptTermsAndCondition(user_id, data) {
    //classifier = {2000000207,1000260017}
    const { customer_tnc_status, customer_tnc } = data
    if (!customer_tnc_status) return "should accept T & C";
    return await dbConnections().main.manager.transaction(async (transaction) => {
      try {
        const tncData = { customer_id: user_id, classifier_id: 2000000207, value_reference_id: 1000105001 }
        let saveAcceptTnC = await transaction.getRepository("customer_classification").insert(tncData);
        const entryObject = {
          entity_1_type_id: 1000220001,
          entity_1_entity_uuid: user_id,
          document_type_id: 1000260017,
          document_information: { url: customer_tnc }
        };
        let saveDocumentTnC = await transaction.getRepository("document").insert(entryObject);
        return saveDocumentTnC


      } catch (err) {
        return err
      }
    });
    //key  = {customer_tnc_status,customer_tnc}
    //save terms and condition and pdf here
    return { user_id, data }
  }
}

const mapDocumentType = async (files) => {
  let docJSONConfig = await getDocumentJSONConfig({ key_type: 'ref_reference_reference_id' })
  for (let i = 0; i < files.length; i++) {
    files[i].document_type = docJSONConfig[files[i].document_type_id]?.ref_reference_field_name
  }
  return files
}

class LibCustomerListing {
  async create(data, params) {
    try {
      // infoLog(params);
      // TODO: check is admin or valid paravet
      const { page_number, page_limit, f_farmer_name, f_farmer_mobile, f_farmer_village, f_farmer_taluka, f_farmer_visual_id, f_paravet_name, f_village_id_array, f_village, f_not_assigned, f_not_subscribed } = data
      const { token } = params.headers
      let query = {}
      // infoLog(token);
      query['user_id'] = token.user_id
      query['user_type'] = token.user_type
      query['user_type_string'] = token.user_type_resolved.type
      query['count'] = 1

      if (page_limit) query['page_limit'] = page_limit
      if (page_number) query['page_number'] = page_number

      query = {
        ...query,
        f_farmer_name,
        f_farmer_mobile,
        f_farmer_village,
        f_farmer_taluka,
        f_farmer_visual_id,
        f_paravet_name,
        f_village_id_array,
        f_village,
        f_not_assigned,
        f_not_subscribed
      }
      const customers = await getCustomerList_v3(query)
      return customers
    } catch (error) {
      console.log(error)
      errorLog('error in LibCustomerListing:88', params, { message: error.message })
      return { result: false, error }
    }
  }
}

class LibUpsertCustomer {
  async create(data, params) {
    const token = params.headers.token
    if (!CONST_TYPES_STAFF[token.user_type]) {
      return { result: false, message: 'unauthorized' }
    }
    try {
      let id = await registerCustomer(data, token)
      return { customer_id: id }
    } catch (error) {
      errorLog('error in LibUpsertCustomer:105', params, { message: error.message })
      if (error.code || error.return_code) {
        return error
      } else {
        return new KrushalError('Unknown Error', -102)
      }
    }
  }
}

class CustomerLib {
  async find(params) {
    return { return_code: -101, message: 'in find' }
  }

  async create /*or is it update*/(data, params) {
    try {
      // incoming data
      // {mobile_phone: "9099889999", name: "Vithal Nemame", date_of_birth: "2015-06-28T13:51:13", aadhaar: "************", "gender": 1000450001}
      // id -1 would indicate create
      // check if mobile number exists - throw error on duplicate
      // begin transaction, save customer and customer classification, and if needed any er
      // customer name, mobile number*, DOB*, aadhaar number
      // return customer id
      // return { return_code: 0, customer_id, message };
      const mainDBConnection = getDBConnections().main
      const result = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        // const mainSchemaAddition = (configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1) ? "main." : ""
        // const customerRepo = mainDBConnection.repos.customer
        const customerRepo = transactionalEntityManager.getRepository('customer')
        const customerEntity = mainDBConnection.entities['customer']
        const customerClassificationEntity = mainDBConnection.entities['customer_classification']
        const mobileMatchingCustomers = await customerRepo.find({
          where: { mobile_number: data.mobile_number, active: 1000100001 },
        })
        if (mobileMatchingCustomers.length > 0) {
          // return { return_code: -1, message: "Another Farmer in the system has the same mobile number" };
          throw new KrushalError('Another Farmer in the system has the same mobile number', -1)
        }
        const customerData = { ...data }
        delete customerData.date_of_birth
        delete customerData.aadhaar
        delete customerData.gender
        delete customerData.total_income
        customerData.customer_type_id = 1000220001 // for farmer
        // const createdCustomer = await customerRepo.save(customerData)
        const preProcessedCustomerData = preProcessRecords(customerEntity, customerData, customerEntity.additionalAttributes)

        const createdCustomer = await transactionalEntityManager.createQueryBuilder().insert().into(customerEntity).values(preProcessedCustomerData).execute()

        const createdCustomerId = createdCustomer.identifiers[0].customer_id
        postUpsertProcessing('main', 'customer', { insertedPrimaryKeys: [createdCustomerId] })
        const classificationDataToSave = {}
        assignSourceObjectToTargetObjectIfKeyNotUndefined(data, classificationDataToSave, ['village_id', 'aadhaar', 'date_of_birth', 'gender', 'total_income', 'farmer_assets'])
        if (Object.keys(classificationDataToSave).length > 0) {
          const returnValue = await saveClassificationData(transactionalEntityManager, 'CUSTOMER_CLASSIFICATION', createdCustomerId, classificationDataToSave)
        }
        return {
          return_code: 0,
          message: 'Customer successfully created',
          customer_id: createdCustomerId,
        }
      })
      return result
    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async get(id, params) {
    try {
      // get details for the individual
      // id, name, mobile number, DOB, aadhaar number, gender (reference to master)
      // return { return_code: 0, data: {user_device_id: 1} };
      let customerInformation = {}
      if (id !== -1 && id !== '-1') {
        const mainDBConnection = getDBConnections().main
        const mainSchemaAddition = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 'main.' : ''
        /* const customerQuery = `
            SELECT c.customer_name_l10n, c.mobile_number, 
              aadhaar_cc.value_string_256 as aadhaar_number, 
              dob_cc.value_date as date_of_birth, 
              gender_cc.value_reference_id as gender,
              totalincome_cc.value_double as total_income,
              dairyFarmLoc_cc.value_json as farmer_dairy_farm_location,
              aadhaar_cc.customer_classification_id as aadhaar_cc_id,
              dob_cc.customer_classification_id as dob_cc_id,
              gender_cc.customer_classification_id as gender_cc_id,
              totalincome_cc.customer_classification_id as totalincome_cc_id,
              dairyFarmLoc_cc.customer_classification_id as dairyFarmLoc_cc_id
            FROM ${mainSchemaAddition}customer c
            LEFT JOIN ${mainSchemaAddition}customer_classification aadhaar_cc 
              ON c.customer_id = aadhaar_cc.customer_id AND aadhaar_cc.classifier_id in (2000000003)
              AND aadhaar_cc.active = 1000100001
            LEFT JOIN ${mainSchemaAddition}customer_classification dob_cc 
              ON c.customer_id = dob_cc.customer_id AND dob_cc.classifier_id in (2000000087)
              AND dob_cc.active = 1000100001
            LEFT JOIN ${mainSchemaAddition}customer_classification gender_cc 
              ON c.customer_id = gender_cc.customer_id AND gender_cc.classifier_id in (2000000088)
              AND gender_cc.active = 1000100001
            LEFT JOIN ${mainSchemaAddition}customer_classification totalincome_cc 
              ON c.customer_id = totalincome_cc.customer_id AND totalincome_cc.classifier_id in (2000000025)
              AND totalincome_cc.active = 1000100001
            LEFT JOIN ${mainSchemaAddition}customer_classification dairyFarmLoc_cc 
              ON c.customer_id = dairyFarmLoc_cc.customer_id AND dairyFarmLoc_cc.classifier_id in (2000000004)
              AND dairyFarmLoc_cc.active = 1000100001
            WHERE c.customer_id = '${id}' AND c.active = 1000100001
          `; */
        const customerQuery = `
          SELECT c.customer_name_l10n, c.mobile_number
          FROM ${mainSchemaAddition}customer c
          WHERE c.customer_id = '${id}' AND c.active = 1000100001
        `
        const customerResults = await mainDBConnection.manager.query(customerQuery)
        postProcessRecords(undefined, customerResults, { json_columns: ['customer_name_l10n'] })
        if (customerResults.length !== 1) {
          return {
            return_code: -2,
            message: 'Problem in data. Invalid customer Id or more than 1 classification record of classifier exists for this customer',
          }
        }
        customerInformation = customerResults[0]
        const classifierKeys = ['date_of_birth', 'total_income', 'aadhaar', 'gender', 'farmer_dairy_farm_location', 'farmer_assets']
        const returnValue = await loadClassificationData('CUSTOMER_CLASSIFICATION', id, classifierKeys)
        customerInformation = { ...customerInformation, ...returnValue }
      }
      const geoArraysResult = await getGeoArrays({}, id)
      if (geoArraysResult.return_code === 0) {
        customerInformation.district_id = geoArraysResult.district_id
        customerInformation.district_id_list = geoArraysResult.district_id_list
        customerInformation.taluk_id = geoArraysResult.taluk_id
        customerInformation.taluk_id_list = geoArraysResult.taluk_id_list
        customerInformation.village_id = geoArraysResult.village_id
        customerInformation.village_id_list = geoArraysResult.village_id_list
        return { return_code: 0, data: customerInformation }
      } else {
        return { return_code: -101, message: 'Problem in getting geography information' }
      }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async patch /* or is it update */(id, data, params) {
    try {
      // update details
      // id, name, mobile number, DOB, aadhaar number, gender (reference to master)
      // return { return_code: 0, data: {user_device_id: 1} };

      const mainDBConnection = getDBConnections().main
      const mainSchemaAddition = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 'main.' : ''
      const result = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        // const customerRepo = mainDBConnection.repos.customer
        const customerRepo = transactionalEntityManager.getRepository('customer')
        // const customerClassificationRepo = mainDBConnection.repos.customer_classification
        const customerEntity = mainDBConnection.entities['customer']
        const customerClassificationEntity = mainDBConnection.entities['customer_classification']
        const customerClassificationRepo = transactionalEntityManager.getRepository('customer_classification')

        // if customer mobile number has been sent as part of data, check if it is different from one in db
        // if different, also check if number exists in another entry
        // const customerToBeUpdated = await customerRepo.find({where: {customer_id: id}})
        // if (data.mobile_number && customerToBeUpdated.mobile_number !== data.mobile_number) {
        // }

        // or directly check if number exists in customers excluding this customer
        if (data.mobile_number) {
          const mobileDuplicateCheckQuery = `
              SELECT customer_id
              FROM ${mainSchemaAddition}customer c
              WHERE c.customer_id != '${id}'
              AND c.mobile_number = '${data.mobile_number}'
              AND c.active = 1000100001
            `
          const mobileNumberDuplicateCustomers = await transactionalEntityManager.query(mobileDuplicateCheckQuery)
          if (mobileNumberDuplicateCustomers.length > 0) {
            // return { return_code: -2, message: 'Another Farmer in the system has the same mobile number'};
            throw new KrushalError('Another Farmer in the system has the same mobile number', -2)
          }
        }
        const customerData = { ...data }
        delete customerData.date_of_birth
        delete customerData.aadhaar
        delete customerData.gender
        delete customerData.total_income

        if (Object.keys(customerData).length > 0) {
          const preProcessedCustomerData = preProcessRecords(customerEntity, customerData, { ...customerEntity.additionalAttributes, id: id })
          const customerUpdateResult = await transactionalEntityManager.getRepository('customer').createQueryBuilder().update().set(preProcessedCustomerData).where('customer_id = :id', { id: id }).execute()
        }
        const classificationDataToSave = {}
        assignSourceObjectToTargetObjectIfKeyNotUndefined(data, classificationDataToSave, ['village_id', 'aadhaar', 'date_of_birth', 'gender', 'total_income', 'farmer_assets'])
        if (Object.keys(classificationDataToSave).length > 0) {
          const returnValue = await saveClassificationData(transactionalEntityManager, 'CUSTOMER_CLASSIFICATION', id, classificationDataToSave)
        }       
        return {
          return_code: 0,
          message: 'Customer successfully updated',
          customer_id: id,
        }
      })
      return result
    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async update /* or is it update */(id, data, params) {
    //PUT
    try {
      return { return_code: -101, message: 'in update with id as ' + id }
      // update details
      // id, name, mobile number, DOB, aadhaar number, gender (reference to master)
      // return { return_code: 0, data: {user_device_id: 1} };
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class FlaggedCustomers {

  async create (data, params) {
    try {
      const {user_id, user_type } = params.headers.token;
      return await getAntibioticFlaggedCustomers(data, user_id, user_type)
    } catch (error) {
      return { return_code: -101, message: error.message }
    }
  }

}

const getAntibioticFlaggedCustomers = async (data, user_id, user_type) => {
  try {
    const isPostgres = configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0;

    if (!user_id || !user_type) throw new Error('user_id and user_type are required');

    if (data.f_farmer_name) {
      data.f_farmer_name = `%${data.f_farmer_name.toUpperCase()}%`;
    }
    const queryParameters = { ...data, user_id, user_type };
    const templateParameters = {
      ...data,
      user_id,
      user_type,
      IS_POSTGRES: isPostgres,
    };

    let query = getCompiledQuery('customer-antibiotic-report', templateParameters);
    const queryBuilder = await dbConnections().main.manager.createQueryBuilder().from(`(${query})`, 't1').setParameters(queryParameters);
    
    if (!isNaN(data.page_number) && !isNaN(data.page_limit)) {
      let offset = data.page_limit * (data.page_number - 1);
      if (offset < 0) offset = 0;
      queryBuilder.skip(offset).take(data.page_limit);
    }

    const queryResults = await queryBuilder.select('*').execute();
    postProcessRecords(
      undefined, 
      queryResults, 
      { 
        json_columns: [
          'customer_name_l10n', 
          'village_name_l10n', 
          'taluk_name_l10n'
        ] 
      });
    return { data : queryResults };
  } catch (error) {
    errorLog('error in getAntibioticFlaggedCustomers', { ...data, user_type, user_id }, { message: error });
    return -1;
  }
};


module.exports = { LibCustomerService, LibCustomerListing, LibUpsertCustomer, CustomerLib, CustomerListLib, FlaggedCustomers }
