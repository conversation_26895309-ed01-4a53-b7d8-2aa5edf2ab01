const observationRepositoryInstance = require('../../repositories/observationRepository')

class ObservationService {
    
    async getObservations(care_calendar_id) {

        try {

            const observationsResult = await observationRepositoryInstance.getAll(care_calendar_id);
            const notesResult = await observationRepositoryInstance.getNotes(care_calendar_id);

            const observations = observationsResult.reduce((processedObservations, inputElement) => {

                if(inputElement.observation_val?.config?.type != 'media') {

                    let outputElement = {};
                    outputElement['observation_category_id'] = inputElement.observation_category_id;
                    outputElement['observation_category'] = inputElement.observation_category;
                    outputElement['observation_name_id'] = inputElement.observation_name_id;
                    outputElement['observation_name'] = inputElement.observation_name;
                    outputElement['observation_val'] = inputElement.observation_val?.data?.answers;
                    
                    processedObservations.push(outputElement);
                }
                return processedObservations;
            }, []);
            
            return {
                observations: observations, 
                notes: notesResult
            }

        } catch (error) {
            throw new Error(`Error getting observations: ${error.message}`);
        }
    }
}

module.exports = new ObservationService();
