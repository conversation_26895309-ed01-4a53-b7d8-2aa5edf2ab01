const { getRequisition<PERSON><PERSON><PERSON><PERSON>In<PERSON><PERSON> , getRequisition<PERSON>or<PERSON><PERSON> , getRequisitionForStaff , getRequisitionData} = require('./controller')
 
class configureInventoryV2 {
  async create(data, params) {
    try {
      const requestType = params.route.type
      switch (requestType) {
        case 'myRequest':
          let createRequisitionIfExist = true
          if( data.createRequisitionIfExist   === false ) createRequisitionIfExist = false
          data['openRequisition'] = createRequisitionIfExist
          let userResponse = await getRequisitionForLoggedInUser(data, params)
          return userResponse
        case 'all':
          let allResponse = await getRequisitionForAll(data, params)
          return allResponse
        case 'staff':
          let { staff_id } = params.query
          let staffRequisition = await getRequisitionForStaff(data, staff_id)
          return staffRequisition
        case 'request':
          let { requisition_id } = params.query
          let requisitionData = await getRequisitionData(data, requisition_id)
          return requisitionData

      }
      return requestType
    } catch (error) {
      console.log(error)
      return error
    }
  }
}

module.exports = { configureInventoryV2 }
