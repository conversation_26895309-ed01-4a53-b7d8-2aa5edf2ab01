const { REQUISITION_INVOICE_SUBMITTED } = require('../../utils/constant')
const { allRequisition, OpenRequisitionForRequisitioner, getUsersRequisition ,getRequisitionById , loadRequisitionLineItem} = require('../../queries/inventory')
const { postProcessRecords } = require('@krushal-it/ah-orm')
const { loadClassificationData } = require("../common/classification.helper");
const { loadMeds } = require('../common/common.class')
const { getDocumentsByRequisitionId } = require('../../queries/document')

const extractBasedOnLanguageMod = (e, t) => {
  if (t === undefined) {
    t = 'en'
  }
  if (e === null || e === undefined) {
    return ''
  }
  if (typeof e === 'object') {
    if (e.ul !== undefined && e.ul !== null) {
      return e.ul
    } else if (e[t] !== undefined && e[t] !== null) {
      return e[t]
    } else if (e.en !== undefined && e.en !== null) {
      return e.en
    } else {
      return ''
    }
  } else {
    return e
  }
}

const makeFiltersForRequisition = data => {
  let values = data ? data : {}
  let filters = {
    r_id: values.filters && values.filters.requisition_id ? values.filters.requisition_id : null,
    r_requisitioner: values.filters && values.filters.requisitioner_uuid ? values.filters.requisitioner_uuid : [],
    r_status: values.filters && values.filters.status_l10n ? values.filters.status_l10n : [],
    r_fullfiller: values.filters && values.filters.fullfiller_uuid ? values.filters.fullfiller_uuid : [],
    r_approver: values.filters && values.filters.approver_uuid ? values.filters.approver_uuid : [],
    r_requisitioner_type: values.filters && values.filters.requisitioner_type_l10n ? values.filters.requisitioner_type_l10n : [],
    r_approver_type: values.filters && values.filters.approver_type_l10n ? values.filters.approver_type_l10n : [],
    r_fullfiller_type: values.filters && values.filters.fullfiller_type_l10n ? values.filters.fullfiller_type_l10n : [],
    r_requisitioner_name_l10n: values.filters && values.filters.requisitioner_name_l10n ?  values.filters.requisitioner_name_l10n[0]  : null,
    r_approver_name_l10n: values.filters && values.filters.approver_name_l10n ?  values.filters.approver_name_l10n[0]  : null,
    r_fullfiller_name_l10n: values.filters && values.filters.fullfiller_name_l10n ?  values.filters.fullfiller_name_l10n[0]  : null,
    r_requisitioner_name: values.filters && values.filters.requisitioner_name ?  values.filters.requisitioner_name[0]  : null,
    r_approver_name: values.filters && values.filters.approver_name ?  values.filters.approver_name[0]  : null,
    r_fullfiller_name: values.filters && values.filters.fullfiller_name ?  values.filters.fullfiller_name[0]  : null,
    r_created_at_from: values.filters && values.filters.created_at && values.filters.created_at[0] && values.filters.created_at[0].length > 0 ? values.filters.created_at[0][0] : null,
    r_created_at_to: values.filters && values.filters.created_at && values.filters.created_at[0] && values.filters.created_at[0].length > 1 ? values.filters.created_at[0][1] : null,
    r_inserted_at_from: values.filters && values.filters.inserted_at && values.filters.inserted_at[0] && values.filters.inserted_at[0].length > 0 ? values.filters.inserted_at[0][0] : null,
    r_inserted_at_to: values.filters && values.filters.inserted_at && values.filters.inserted_at[0] && values.filters.inserted_at[0].length > 1 ? values.filters.inserted_at[0][1] : null,
    r_updated_at_from: values.filters && values.filters.updated_at && values.filters.updated_at[0] && values.filters.updated_at[0].length > 0 ? values.filters.updated_at[0][0] : null,
    r_updated_at_to: values.filters && values.filters.updated_at && values.filters.updated_at[0] && values.filters.updated_at[0].length > 1 ? values.filters.updated_at[0][1] : null,
    r_pps_id: values.filters && values.filters.prescription_process_status_l10n ? values.filters.prescription_process_status_l10n : [],
    r_pmds_id: values.filters && values.filters.prescription_medicine_delivery_status_l10n ? values.filters.prescription_medicine_delivery_status_l10n : [],
    r_pis_id: values.filters && values.filters.prescription_invoice_status_l10n ? values.filters.prescription_invoice_status_l10n : []
  }
  if (filters.r_requistioner && filters.r_requistioner.length) {
    filters.r_requistioner = filters.r_requistioner.map(requisition => `'${requisition}'`)
  }
  if (filters.r_fullfiller && filters.r_fullfiller.length) {
    filters.r_fullfiller = filters.r_fullfiller.map(fullfilller => `'${fullfilller}'`)
  }
  if (filters.r_approver && filters.r_approver.length) {
    filters.r_approver = filters.r_approver.map(approver => `'${approver}'`)
  }
  return filters
}

const getRequisitionForLoggedInUser = async (data, params) => {
  try {
    let filters = {
      r_status: data?.filters && data?.filters.status_l10n ? data?.filters.status_l10n : []
    }
    let { user_id } = params?.headers?.token
    if(data?.openRequisition){
        await OpenRequisitionForRequisitioner(user_id, params)
    }
    let requisitionList = await getUsersRequisition(filters, user_id)
    postProcessRecords(undefined, requisitionList, { json_columns: ['requisitioner_type_l10n', 'requisitioner_name_l10n', 'approver_type_l10n', 'approver_name_l10n', 'fullfiller_type_l10n', 'fullfiller_name_l10n', 'medicine_name_l10n', 'medicine_information', 'status_l10n', 'line_item_status_l10n', 'prescription_process_status_l10n', 'prescription_medicine_delivery_status_l10n', 'prescription_invoice_status_l10n'] })
    let mappedreqlist = []
    for (let i = 0; i < requisitionList.length; i++) {
      let index = mappedreqlist.findIndex(item => item.requisition_id == requisitionList[i].requisition_id)
      if (index == -1) {
        mappedreqlist.push({
          requisition_id: requisitionList[i].requisition_id,
          requisitioner_type_id: requisitionList[i].requisitioner_type_id,
          requisitioner_type_l10n: requisitionList[i].requisitioner_type_l10n,
          requisitioner_uuid: requisitionList[i].requisitioner_uuid,
          requisitioner_name_l10n: requisitionList[i].requisitioner_name_l10n,
          requisitioner_name: extractBasedOnLanguageMod(requisitionList[i].requisitioner_name_l10n),
          approver_type_id: requisitionList[i].approver_type_id,
          approver_type_l10n: requisitionList[i].approver_type_l10n,
          approver_uuid: requisitionList[i].approver_uuid,
          approver_name_l10n: requisitionList[i].approver_name_l10n,
          approver_name: extractBasedOnLanguageMod(requisitionList[i].approver_name_l10n),
          fullfiller_type_id: requisitionList[i].fullfiller_type_id,
          fullfiller_type_l10n: requisitionList[i].fullfiller_type_l10n,
          fullfiller_uuid: requisitionList[i].fullfiller_uuid,
          fullfiller_name_l10n: requisitionList[i].fullfiller_name_l10n,
          fullfiller_name: extractBasedOnLanguageMod(requisitionList[i].fullfiller_name_l10n),
          status_id: requisitionList[i].status_id,
          status_l10n: requisitionList[i].status_l10n,
          prescription_process_status_id: requisitionList[i].prescription_process_status_id,
          prescription_medicine_delivery_status_id: requisitionList[i].prescription_medicine_delivery_status_id,
          prescription_invoice_status_id: requisitionList[i].prescription_invoice_status_id,
          prescription_process_status_l10n: requisitionList[i].prescription_process_status_l10n,
          prescription_medicine_delivery_status_l10n: requisitionList[i].prescription_medicine_delivery_status_l10n,
          prescription_invoice_status_l10n: requisitionList[i].prescription_invoice_status_l10n,
          created_at: requisitionList[i].created_at,
          updated_at: requisitionList[i].updated_at,
          inserted_at: requisitionList[i].inserted_at
        })
      }
    }
    return mappedreqlist
  } catch (e) {
    throw e
  }
}

const getRequisitionForStaff = async (data, staff_id) => {
  try {
    let filters = {
      r_status: data?.filters && data?.filters.status_l10n ? data?.filters.status_l10n : []
    }
    let requisitionList = await getUsersRequisition(filters, staff_id)
    postProcessRecords(undefined, requisitionList, { json_columns: ['requisitioner_type_l10n', 'requisitioner_name_l10n', 'approver_type_l10n', 'approver_name_l10n', 'fullfiller_type_l10n', 'fullfiller_name_l10n', 'medicine_name_l10n', 'medicine_information', 'status_l10n', 'line_item_status_l10n', 'prescription_process_status_l10n', 'prescription_medicine_delivery_status_l10n', 'prescription_invoice_status_l10n'] })
    let mappedreqlist = []
    for (let i = 0; i < requisitionList.length; i++) {
      let index = mappedreqlist.findIndex(item => item.requisition_id == requisitionList[i].requisition_id)
      if (index == -1) {

        mappedreqlist.push({
          requisition_id: requisitionList[i].requisition_id,
          requisitioner_type_id: requisitionList[i].requisitioner_type_id,
          requisitioner_type_l10n: requisitionList[i].requisitioner_type_l10n,
          requisitioner_uuid: requisitionList[i].requisitioner_uuid,
          requisitioner_name_l10n: requisitionList[i].requisitioner_name_l10n,
          requisitioner_name: extractBasedOnLanguageMod(requisitionList[i].requisitioner_name_l10n),
          approver_type_id: requisitionList[i].approver_type_id,
          approver_type_l10n: requisitionList[i].approver_type_l10n,
          approver_uuid: requisitionList[i].approver_uuid,
          approver_name_l10n: requisitionList[i].approver_name_l10n,
          approver_name: extractBasedOnLanguageMod(requisitionList[i].approver_name_l10n),
          fullfiller_type_id: requisitionList[i].fullfiller_type_id,
          fullfiller_type_l10n: requisitionList[i].fullfiller_type_l10n,
          fullfiller_uuid: requisitionList[i].fullfiller_uuid,
          fullfiller_name_l10n: requisitionList[i].fullfiller_name_l10n,
          fullfiller_name: extractBasedOnLanguageMod(requisitionList[i].fullfiller_name_l10n),
          status_id: requisitionList[i].status_id,
          status_l10n: requisitionList[i].status_l10n,
          prescription_process_status_id: requisitionList[i].prescription_process_status_id,
          prescription_medicine_delivery_status_id: requisitionList[i].prescription_medicine_delivery_status_id,
          prescription_invoice_status_id: requisitionList[i].prescription_invoice_status_id,
          prescription_process_status_l10n: requisitionList[i].prescription_process_status_l10n,
          prescription_medicine_delivery_status_l10n: requisitionList[i].prescription_medicine_delivery_status_l10n,
          prescription_invoice_status_l10n: requisitionList[i].prescription_invoice_status_l10n,
          created_at: requisitionList[i].created_at,
          updated_at: requisitionList[i].updated_at,
          inserted_at: requisitionList[i].inserted_at
        })
      }
    }
    return mappedreqlist
  } catch (e) {
    throw e
  }
}

const getRequisitionForAll = async (data, params) => {
  try {
    let values = data ? data : {}
    let filters = makeFiltersForRequisition(data)

    let requisitionList = await allRequisition(filters)

    requisitionList = requisitionList.filter(item => item.approver_uuid)
    postProcessRecords(undefined, requisitionList, { json_columns: ['requisitioner_type_l10n', 'requisitioner_name_l10n', 'approver_type_l10n', 'approver_name_l10n', 'fullfiller_type_l10n', 'fullfiller_name_l10n', 'medicine_name_l10n', 'medicine_information', 'status_l10n', 'line_item_status_l10n', 'prescription_process_status_l10n', 'prescription_medicine_delivery_status_l10n', 'prescription_invoice_status_l10n'] })
    let mappedreqlist = []
    for (let i = 0; i < requisitionList.length; i++) {
      let index = mappedreqlist.findIndex(item => item.requisition_id == requisitionList[i].requisition_id)
      if (index == -1) {
        mappedreqlist.push({
          requisition_id: requisitionList[i].requisition_id,
          requisitioner_type_id: requisitionList[i].requisitioner_type_id,
          requisitioner_type_l10n: requisitionList[i].requisitioner_type_l10n,
          requisitioner_uuid: requisitionList[i].requisitioner_uuid,
          requisitioner_name_l10n: requisitionList[i].requisitioner_name_l10n,
          requisitioner_name: extractBasedOnLanguageMod(requisitionList[i].requisitioner_name_l10n),
          approver_type_id: requisitionList[i].approver_type_id,
          approver_type_l10n: requisitionList[i].approver_type_l10n,
          approver_uuid: requisitionList[i].approver_uuid,
          approver_name_l10n: requisitionList[i].approver_name_l10n,
          approver_name: extractBasedOnLanguageMod(requisitionList[i].approver_name_l10n),
          fullfiller_type_id: requisitionList[i].fullfiller_type_id,
          fullfiller_type_l10n: requisitionList[i].fullfiller_type_l10n,
          fullfiller_uuid: requisitionList[i].fullfiller_uuid,
          fullfiller_name_l10n: requisitionList[i].fullfiller_name_l10n,
          fullfiller_name: extractBasedOnLanguageMod(requisitionList[i].fullfiller_name_l10n),
          line_items_count: requisitionList[i].line_items_count,
          status_id: requisitionList[i].status_id,
          status_l10n: requisitionList[i].status_l10n,
          prescription_process_status_id: requisitionList[i].prescription_process_status_id,
          prescription_medicine_delivery_status_id: requisitionList[i].prescription_medicine_delivery_status_id,
          prescription_invoice_status_id: requisitionList[i].prescription_invoice_status_id,
          prescription_process_status_l10n: requisitionList[i].prescription_process_status_l10n,
          prescription_medicine_delivery_status_l10n: requisitionList[i].prescription_medicine_delivery_status_l10n,
          prescription_invoice_status_l10n: requisitionList[i].prescription_invoice_status_l10n,
          created_at: requisitionList[i].created_at,
          updated_at: requisitionList[i].updated_at,
          inserted_at: requisitionList[i].inserted_at
        })
      }
    }
    let count = 0
    let total_page = 1
    count = mappedreqlist ? parseInt(mappedreqlist.length) : 0
    if (!isNaN(values.size) && !isNaN(values.start) && values.start > 0) {
      let offset = values.size + values.start ? values.start : 0
      if (offset < 0) throw new Error('Invalid page number')
      mappedreqlist = mappedreqlist.splice(offset, values.size)
      total_page = parseInt(count / values.size)
      total_page += count % values.size === 0 ? 0 : 1
    }
    return { report: mappedreqlist, count: count, return_code: 0 }
  } catch (e) {
    console.log('getRequisition', e)
    throw e
  }
}

const getRequisitionData = async (data,requisition_id) => {
    try {
        let requisitionData = await getRequisitionById(requisition_id)
        postProcessRecords(undefined, requisitionData, { json_columns: ['requisitioner_type_l10n', 'requisitioner_name_l10n', 'approver_type_l10n', 'approver_name_l10n', 'fullfiller_type_l10n', 'fullfiller_name_l10n', 'medicine_name_l10n', 'medicine_information', 'status_l10n', 'line_item_status_l10n', 'prescription_process_status_l10n', 'prescription_medicine_delivery_status_l10n', 'prescription_invoice_status_l10n'] })
        if(requisitionData && requisitionData.length) {
            let classificationData = await loadClassificationData("REQUISITION_CLASSIFICATION", requisition_id, ['pharmacy_invoice_number','zoho_bill_id']);
            let loadRequisitionLineItems = await loadRequisitionLineItem(requisition_id)
            let  medicine_ids = []
            if(loadRequisitionLineItems && loadRequisitionLineItems.length) {
                medicine_ids = loadRequisitionLineItems.map(line_item => line_item.medicine_id)
            }
            let meds = await loadMeds(medicine_ids)
            let  medicines = Object.values(meds)
            
            let requisition_line_items = []
            for (let loadRequisitionLineItem of loadRequisitionLineItems) {
                let findMeds =  medicines.find(med => med.medicine_id === loadRequisitionLineItem.medicine_id)
                requisition_line_items.push({...findMeds,...loadRequisitionLineItem})
            }
            let requisitiondocs = null
            if(requisitionData[0].status_id==REQUISITION_INVOICE_SUBMITTED){
                requisitiondocs=await getDocumentsByRequisitionId(requisition_id)
                console.log('requisitiondocs',requisitiondocs)
              }
            let mappedreq = {
                requisition_id: requisitionData[0]?.requisition_id,
                requisitioner_type_id: requisitionData[0]?.requisitioner_type_id,
                requisitioner_type_l10n: requisitionData[0]?.requisitioner_type_l10n,
                requisitioner_uuid: requisitionData[0]?.requisitioner_uuid,
                requisitioner_name_l10n: requisitionData[0]?.requisitioner_name_l10n,
                requisitioner_name: extractBasedOnLanguageMod(requisitionData[0]?.requisitioner_name_l10n),
                approver_type_id: requisitionData[0]?.approver_type_id,
                approver_type_l10n: requisitionData[0]?.approver_type_l10n,
                approver_uuid: requisitionData[0]?.approver_uuid,
                approver_name_l10n: requisitionData[0]?.approver_name_l10n,
                approver_name: extractBasedOnLanguageMod(requisitionData[0]?.approver_name_l10n),
                fullfiller_type_id: requisitionData[0]?.fullfiller_type_id,
                fullfiller_type_l10n: requisitionData[0]?.fullfiller_type_l10n,
                fullfiller_uuid: requisitionData[0]?.fullfiller_uuid,
                fullfiller_name_l10n: requisitionData[0]?.fullfiller_name_l10n,
                fullfiller_name: extractBasedOnLanguageMod(requisitionData[0]?.fullfiller_name_l10n),
                line_items_count: requisitionData[0]?.line_items_count,
                status_id: requisitionData[0]?.status_id,
                status_l10n: requisitionData[0]?.status_l10n,
                prescription_process_status_id: requisitionData[0]?.prescription_process_status_id,
                prescription_medicine_delivery_status_id: requisitionData[0]?.prescription_medicine_delivery_status_id,
                prescription_invoice_status_id: requisitionData[0]?.prescription_invoice_status_id,
                prescription_process_status_l10n: requisitionData[0]?.prescription_process_status_l10n,
                prescription_medicine_delivery_status_l10n: requisitionData[0]?.prescription_medicine_delivery_status_l10n,
                prescription_invoice_status_l10n: requisitionData[0]?.prescription_invoice_status_l10n,
                created_at: requisitionData[0]?.created_at,
                updated_at: requisitionData[0]?.updated_at,
                inserted_at: requisitionData[0]?.inserted_at,
                ...classificationData,
                requisition_line_items,
                requisitiondocs
              }
            return mappedreq
        }
        else return {}

    } catch (error) {
           throw new Error(error)
    }
}
module.exports = { getRequisitionForLoggedInUser , getRequisitionForStaff , getRequisitionForAll ,getRequisitionData}