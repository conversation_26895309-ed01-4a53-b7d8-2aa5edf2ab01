const { KrushalError } = require('@krushal-it/common-core')
const { dbConnections } = require('@krushal-it/ah-orm')
const { saveClassificationData, getClassificationConfiguration, loadClassificationDataWithClassifierIds } = require('../common/classification.helper')
const uuid = require('uuid')
const { CONST_TYPES_STAFF } = require('../../utils/constant')
const { configurationJSON } = require('@krushal-it/common-core')
const { getDBConnections } = require('@krushal-it/ah-orm')
const classificationJSON = require('../../classification.json')
const _ = require('lodash')

const tables = {
  1000460001: 'customer',
  1000460002: 'animal',
  1000460003: 'staff',
  1000460012: 'asset'
}

class LibAsset {
  async create (data, params, transactionalEntityManager) { // post
    try {
      console.log('in create')
      console.log('called with data ', data, ' and params', params)
      const queryParameters = params.query || {}
      const conflictPaths = Object.keys(queryParameters) || []
      const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
      // const mainSchemaAddition = isPostgres ? 'main.' : ''
      const assetRepo = transactionalEntityManager.getRepository('asset')
      const assets = await assetRepo.save([data])
      console.log('saved asset', assets)
      const asset = assets && assets.length > 0 ? assets[0] : null
      if (asset && asset?.asset_id) { // asset got created link with parents
        // need to commit if its new transactionalEntityManager
        // iterate through all the parent data and save them
        for (const parent of data?.parents) {
          if (parent?.entity_1_entity_uuid && parent?.entity_relationship_type_id) {
            // add the new asset to the entityRelationship table
            const entityRelationshipRepo = transactionalEntityManager.getRepository('entity_relationship')
            await entityRelationshipRepo.save(
              {
                entity_relationship_type_id: parent.entity_relationship_type_id,
                entity_1_type_id: parent?.entity_1_type_id,
                entity_1_entity_uuid: parent.entity_1_entity_uuid,
                entity_2_type_id: parent?.entity_2_type_id || 1000460012, // should be asset always
                entity_2_entity_uuid: asset.asset_id
              }
            )
          }
        }
        if (data?.parent?.entity_1_entity_uuid && data?.parent?.entity_relationship_type_id) {
          // add the new asset to the entityRelationship table
          const entityRelationshipRepo = transactionalEntityManager.getRepository('entity_relationship')
          await entityRelationshipRepo.save(
            {
              entity_relationship_type_id: data.parent.entity_relationship_type_id,
              entity_1_type_id: data.parent?.entity_1_type_id,
              entity_1_entity_uuid: data.parent.entity_1_entity_uuid,
              entity_2_type_id: data.parent?.entity_2_type_id || 1000460012, // should be asset always
              entity_2_entity_uuid: asset.asset_id
            }
          )
        }
        // save all the classifications data
        const classificationData = _.cloneDeep(data) // copy the data
        if (classificationData?.parent) delete classificationData?.parent // remove the parent
        if (classificationData?.features) delete classificationData?.features // remove the entity_type_id

        let classifierAttributeArray = Object.keys(classificationData)
        const entitiesClassificationConfiguration = getClassificationConfiguration()
        const classificationConfiguration = entitiesClassificationConfiguration.ASSET_CLASSIFICATION
        const classifierAttributes = classificationConfiguration.ATTRIBUTE_LIST
        const validAttributes = Object.keys(classifierAttributes)
        classifierAttributeArray = classifierAttributeArray.filter((classifierAttribute) => { return validAttributes.includes(classifierAttribute) })

        const result = await saveClassificationData(transactionalEntityManager, 'ASSET_CLASSIFICATION', asset.asset_id, classificationData)
        if (result && result == true) {
          const savedData = await loadClassificationDataWithClassifierIds(transactionalEntityManager, 'ASSET_CLASSIFICATION', asset.asset_id, classifierAttributeArray)
          asset.classifiersWithClassifierIds = savedData
        }
      }
      return { return_code: 0, data: asset }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async get (id, params, transactionalEntityManager) {
    try {
      console.log('in get')
      console.log('called with id ', id, ' and params', params)
      const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
      const mainSchemaAddition = isPostgres ? 'main.' : ''
      const assetRepo = transactionalEntityManager.getRepository('asset')
      const asset = await assetRepo.findOne({ where: { asset_id: id } })
      return { return_code: 0, data: asset }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async find (params, transactionalEntityManager = null) {
    console.log('in find')
    console.log('called with params', params)
    try {
      const queryParameters = params.query || {}
      const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
      const dbConnection = getDBConnections().main
      let newTransactionManager = null
      if (!transactionalEntityManager) { // no transactionalEntityManager passed
        newTransactionManager = await dbConnections.main.getConnection().manager
      }
      transactionalEntityManager = transactionalEntityManager || newTransactionManager
      const mainSchemaAddition = isPostgres ? 'main.' : ''
      const assetRepo = transactionalEntityManager.getRepository('asset')
      const asset = await assetRepo.find({ where: queryParameters })
      return { return_code: 0, data: asset }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async patch /* or is it update */(id, data, params) {
    console.log('in patch')
    console.log('called with id ', id, ' and params', params)
    return { return_code: -101, message: 'in patch' }
  }

  async update /* or is it update */(id, data, params) {
    console.log('in update')
    console.log('called with id ', id, ' and params', params)
    return { return_code: -101, message: 'in update' }
  }
}

module.exports = { LibAsset }
