const { getDBConnections, dbConnections } = require("@krushal-it/ah-orm");
const { handleFileEntries, addTodumped } = require("../../utils/document");
const {errorLog,infoLog} = require('@krushal-it/mobile-or-server-lib')
const {RELATION_DOC} = require('../../utils/constant');
const { configurationJSON } = require('@krushal-it/common-core');

const addDocumentEntry = async (data) => {
  try {
    const { document_entries } = data;
    if (!Array.isArray(document_entries) && document_entries.length > 0)
        throw new Error("document_entries must be an array");
    let promiseArray = [];
    await global.datasource;
    const document_ids = [];
    for (let entryIndex = 0; entryIndex < document_entries.length; entryIndex++) {
        const { entity, entity_1_type_id, entity_1_id, document_type, client_document_information_json, document_information_json, contentType, document_information } = document_entries[entryIndex];
        const url = client_document_information_json ? client_document_information_json.url : (document_information_json && document_information_json.url) ? document_information_json.url : document_entries[entryIndex].url
        const entryObject = { entity, url, entity_1_type_id, entity_1_id, document_type, document_information, client_document_information_json, document_information_json, contentType };
        const documentCreationResult = await handleFileEntries(entryObject)
        if (documentCreationResult.identifiers && documentCreationResult.identifiers.length > 0) {
            document_ids.push(documentCreationResult.identifiers[0].document_id)
        }
    }
    return { document_ids };
  } catch (error) {
    errorLog("error in addDocumentEntry:42",data,{message:error.message})
    return { result: false, message: "cannot add document entry" };
  }
};

const makeDocumentInactive = async (document_id) => {
  try{
      const documents =  await dbConnections().main.manager.transaction(async (transactionalEntityManager) => {
        const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0;
        const valueToBeUpdated = { active : 1000100002 };
        if (!isPostgres) {
          valueToBeUpdated['data_sync_status'] = 1000101003;
        }
        await transactionalEntityManager.createQueryBuilder().update(RELATION_DOC).set(valueToBeUpdated).where(`${RELATION_DOC}.document_id = :document_id`, { document_id }).execute();
        return true;
      });
      return { success :"true"}
  } 
  catch (err){
    return { result: false, message: "cannot edit document entry" };
  }
}

const getDocumentsByEnityUUID = (id, options = {})=>{
    const { uploaded_at } = options
    const query = "select document_id, document_information, created_at" + (uploaded_at ? ", document_meta_data_information->'uploaded_at' as uploaded_at" : "") + " from main.document where \
          entity_1_entity_uuid='"+ id + "'" + (uploaded_at ? " and document_meta_data_information->>'uploaded_at' = '" + uploaded_at + "'" : "")
    const query_response = getDBConnections().main.manager.query(query);
    return query_response;
}
module.exports = { addDocumentEntry ,makeDocumentInactive, getDocumentsByEnityUUID};