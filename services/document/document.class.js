const { deleteDocumentById, getDocumentById } = require('../../queries/document')
const { getDocumentJSONConfig } = require('../../utils/document')
const { uploadDocument, addDocumentEntry, makeDocumentInactive } = require('./controller')
const { errorLog } = require('@krushal-it/mobile-or-server-lib')
const {
  preProcessRecords,
  postProcessRecords,
  getDBConnections
} = require('@krushal-it/ah-orm')

class LibDocumentService {
  async create (data, params) {
    try {
      const { user_type, user_id } = params.headers.token
      if (user_type == 1000220001 && data.entity_1_type !== 'animal') {
        // if user is farmer override the requested changing ids
        data.entity_1_type = 1000220001
        data.entity_1_id = user_id
      }
      if (data.files) {
        throw new Error('back-end-lib does not support uploading document')
      } else {
        // No files, consider as file meta data entry request
        return { result: true, data: await addDocumentEntry(data) }
      }
    } catch (error) {
      errorLog('error in LibDocumentService:25', params, { message: error.message })
      return { result: false, message: error.message }
    }
  }

  async get (id, params) {
    try {
      // TODO: check R/W authorizations between user and entity
      return { config: await getDocumentJSONConfig(), pipeStream: false }
    } catch (error) {
      errorLog('error in LibDocumentService:34', params, { message: error.message })
      return { result: false, message: error.message }
    }
  }

  async remove (id, params) {
    try {
      // TODO: check R/W authorizations between user and entity
      await deleteDocumentById(id)
      return { result: true, message: 'Image deleted' }
    } catch (error) {
      errorLog('error in LibDocumentService:44', params, { message: error.message })
      return { result: false, message: error.message }
    }
  }

  async inactiveDocument (id) {
    try {
      const data = await makeDocumentInactive(id)
      return { data }
    } catch (err) {
      return { result: false, message: error.message }
    }
  }
}

class LibMediaService {
  async get (id, params) {
    try {
      // TODO: check R/W authorizations between user and entity
      if (id == -1) {
        return {
          config: await getDocumentJSONConfig(),
          pipeStream: false
        }
      }
      return await getDocumentById(id)
    } catch (error) {
      errorLog('error in LibMediaService:63', params, { message: error.message })
      return {
        result: false,
        message: error.message
      }
    }
  }
}

class DocumentListLib {
  async create (data, params) {
    try {
      // identify the entities and criteria
      // get list of documents based on entity type and criteria
      // return { return_code: 0, list_data: [{document_id, document_type_id, document_type, document_information -> meta_data, document_information -> link_to_document_in_s3} };

      const mainDBConnection = getDBConnections().main
      const mainSchemaAddition = (configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1) ? 'main.' : ''
      const backendUser = true
      // identify kind of user
      let documentQuery
      if (backendUser) {
        /*
        documentQuery =
          `
            SELECT d.document_id, rr.reference_name_l10n, d.document_information->>'s3Key' as "s3Key", d.document_information->>'document_name' as "documentName", d.document_information ->>'destination_file_name' as "destinationFileName"
            FROM ${mainSchemaAddition}document d, ${mainSchemaAddition}ref_reference rr
            WHERE d.entity_1_type_id = ${data.entity_type_id} AND d.entity_1_entity_uuid = '${data.entity_id}'
            AND d.document_type_id = rr.reference_id
          `
        */
        documentQuery =
          `
            SELECT d.document_id, rr.reference_name_l10n, d.document_name_l10n, d.document_information, d.client_document_information
            FROM ${mainSchemaAddition}document d, ${mainSchemaAddition}ref_reference rr
            WHERE d.entity_1_type_id = ${data.entity_type_id} AND d.entity_1_entity_uuid = '${data.entity_id}'
            AND d.document_type_id = rr.reference_id
          `
      }
      const documentResults = await mainDBConnection.manager.query(documentQuery)
      postProcessRecords(undefined, documentResults, { json_columns: ['document_information', 'client_document_information'] })
      const processedDocumentResults = documentResults.map(({ document_id, reference_name_l10n, document_name_l10n, document_information, client_document_information }) => {
        return { document_id, reference_name_l10n, document_name_l10n, s3Key: document_information.s3Key, destinationFileName: document_information.destination_file_name, localFolderName: client_document_information.localFolderName, fileName: client_document_information.fileName }
      })

      return { return_code: 0, list_data: processedDocumentResults }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class DocumentLib {
  async find (params) {
    try {
      const dbConnections = getDBConnections()
      const mainDBConnection = dbConnections.main
      const documentRepo = mainDBConnection.repos.document
      const documentRowsInDB = await documentRepo.find({ where: params.query })
      if (documentRowsInDB && documentRowsInDB.length > 0) {
        postProcessRecords(undefined, documentRowsInDB, { json_columns: ['document_information', 'client_document_information'] })
        return { return_code: 0, data: documentRowsInDB }
      } else { return { return_code: -1, data: [] } }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async create /* or is it update */(data, params) {
    try {
      // incoming data
      // {document_type_id, document_information (including meta data and link to s3), entity_type_id, entity_id}
      // id -1 would indicate create
      // begin transaction, save document
      // return document_id
      // return { return_code: 0, document_id, message };

      const mainDBConnection = getDBConnections().main
      const result = await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
        const documentRepo = transactionalEntityManager.getRepository('document')
        const documentEntity = mainDBConnection.entities.document

        const preProcessedData = preProcessRecords(documentEntity, data, documentEntity.additionalAttributes)
        const createdDocument = await transactionalEntityManager
          .createQueryBuilder()
          .insert()
          .into(documentEntity)
          .values(preProcessedData)
          .execute()

        const createdDocumentId = createdDocument.identifiers[0].document_id

        return { return_code: 0, message: 'Document successfully created', document_id: createdDocumentId }
      })
      return result
    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async inActivate (id, transactionalEntityManager, params) {
    const mainDBConnection = getDBConnections().main
    try {
      if (!transactionalEntityManager) {
        await mainDBConnection.manager.transaction(async (transactionalEntityManager) => {
          const documentRepo = transactionalEntityManager.getRepository('document')
          const documentUpdateResult = await documentRepo
            .createQueryBuilder()
            .update()
            .set({ active: 1000100002 })
            .where('document_id = :id', { id })
            .execute()
          return { return_code: 0, message: 'Document successfully inactivated', id }
        })
      } else {
        const documentRepo = transactionalEntityManager.getRepository('document')
        const documentUpdateResult = await documentRepo
          .createQueryBuilder()
          .update()
          .set({ active: 1000100002 })
          .where('document_id = :id', { id })
          .execute()
        return { return_code: 0, message: 'Document successfully inactivated', id }
      }
    } catch (error) {
      console.log(error)
      throw error
    }
  }

  async get (id, params) {
    try {
      const dbConnections = getDBConnections()
      const mainDBConnection = dbConnections.main
      const documentRepo = mainDBConnection.repos.document
      const documentRowsInDB = await documentRepo.find({ where: { document_id: id } })
      if (documentRowsInDB && documentRowsInDB.length > 0) {
        postProcessRecords(undefined, documentRowsInDB, { json_columns: ['document_information', 'client_document_information'] })
        return { return_code: 0, data: documentRowsInDB[0] }
      } else { return { return_code: -1, data: {} } }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async patch /* or is it update */ (id, data, params) {
    try {      
      return { return_code: 0, document_id: -1, message: 'Document Updated Successfully' }
    } catch (error) {
      console.log(error)
      if (error.name === 'KrushalError') {
        return { return_code: error.return_code, message: error.message }
      } else {
        return { return_code: -101, message: error.message }
      }
    }
  }

  async update /* or is it update */ (id, data, params) { // PUT
    try {
      return { return_code: -101, message: 'in update with id as ' + id }
      // update details
      // id, name, mobile number, DOB, aadhaar number, gender (reference to master)
      // return { return_code: 0, data: {user_device_id: 1} };
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }
}

module.exports = { LibDocumentService, LibMediaService, DocumentListLib, DocumentLib }
