const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm');
const { CareCalendarQueries } = require('../../queries/care-calendar.js');
const { updateCompleteTransactionLedger, updateCancelledTransactionLedger, propagateActivityStatus, generateFollowUp, getTaskPreview } = require("../activity_routeplan/controller.js")
const { preventive_healthcare_activities, record_statuses, staff_activity_statuses, classifiers, reproductive_activities } = require("../../ENUMS.js");
const moment = require("moment")
const { saveActivityClassification } = require("../observationV2/activityClassification.js");
const { closeRelatedCattleTask } = require('../ActivityFormManagement/utils.js');

module.exports = async function updateActivity_V2(data, token) {
  try {

    const cancelledAbandonedStatus = new Set([staff_activity_statuses.VISIT_CANCELLED, staff_activity_statuses.VISIT_ABANDONED]);
    const frozenStatuses = new Set([staff_activity_statuses.VISIT_COMPLETED, staff_activity_statuses.VISIT_CANCELLED, staff_activity_statuses.VISIT_ABANDONED]);
    const { care_calendar, completion_date, completion_date_gmt, reschedule_required, get_preview } = data;

    const { user_id, user_type } = token;
    const validStatus = new Set([staff_activity_statuses.VISIT_COMPLETED, staff_activity_statuses.VISIT_CANCELLED, staff_activity_statuses.VISIT_ABANDONED]);

    if (data.calendar_activity_status && !validStatus.has(parseInt(data.calendar_activity_status))) throw new Error(`cannot set calendar status to ${data.calendar_activity_status}`);
    if (data.calendar_activity_status == staff_activity_statuses.VISIT_COMPLETED && !data.completion_date) throw new Error('completion date is requied for setting task as complete');
    if (data.calendar_activity_status != staff_activity_statuses.VISIT_COMPLETED) delete data['completion_date'];
    const formattedDateTimeInGMT = new Date().toJSON()

    let calendarData = await dbConnections().main.repos['care_calendar'].find({
      where: [{ care_calendar_id: care_calendar }],
      select: { activity_id: true, calendar_activity_status: true, entity_type_id: true, entity_uuid: true },
    });
    if (calendarData.length === 0) throw new Error('Not found');
    const { activity_id, entity_type_id, entity_uuid } = calendarData[0];
    let oldStatus = parseInt(calendarData[0].calendar_activity_status);

    let responsePayload = { result: true };

    if (frozenStatuses.has(data.calendar_activity_status)) {
      data["system_task_completion_time"] = formattedDateTimeInGMT;
    }
    if (data.calendar_activity_status === staff_activity_statuses.VISIT_COMPLETED) {
      data['completion_date'] = completion_date.split("T")[0]
      data["user_task_completion_time"] = completion_date_gmt
    }
    data['activity_completed_by'] = user_id;

    await dbConnections().main.manager.transaction(async (transaction) => {

      if (!frozenStatuses.has(oldStatus)) {
        await CareCalendarQueries.updateEntryStatus(care_calendar, data, token, transaction);

        // // insert to care calendar classification
        // if (data.calendar_activity_status ==  staff_activity_statuses.VISIT_COMPLETED && vet_availablity_while_closing){
        //     let upsertData = { vet_availablity_while_closing: vet_availablity_while_closing };
        //     await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', care_calendar, upsertData);
        // }

        // generating animal level task when status is completed
        // if (data.calendar_activity_status == COMPLETED_ACTIVITY_STATUS && farmerLevelTask.has(activity_id) && data.animal_data) {
        //   await propagateTaskToAnimal({ completion_date, user_task_completion_time: completion_date_gmt, user_id, user_type, transaction, animal_data: data.animal_data, farmerTaskId: care_calendar, activity_id });
        // }

        if (activity_id === reproductive_activities.AI_PENDING && (data.calendar_activity_status == staff_activity_statuses.VISIT_CANCELLED || data.calendar_activity_status == staff_activity_statuses.VISIT_ABANDONED)) {
          await propagateActivityStatus(care_calendar, data.calendar_activity_status, transaction);
        }

        // if(activity_id === CONST_ACTIVITY_ID_AIP && data.ai_task_completion_status == NO) {
        //   await propagateActivityStatus(care_calendar, CANCELLED_ACTIVITY_STATUS , transaction);
        // }

        // close any cattle level related tasks
        await closeRelatedCattleTask(transaction, care_calendar)
      }
        if ( reschedule_required === true) {

          const existingRescheduledTask = await transaction.getRepository('care_calendar_classification').find({
            where: {
              care_calendar_id: care_calendar,
              classifier_id: classifiers.FOLLOW_UP_TASK_ID,
              active: record_statuses.ACTIVE
            },
            select: {
              value_reference_uuid: true
            }
          });
          let activity_date = data.activity_date.split("T")[0]
          let visit_schedule_time= data.activity_date_gmt
          const calendarIdArrays = existingRescheduledTask.map((elem) => elem.value_reference_uuid);
          if(calendarIdArrays && calendarIdArrays.length){

            let updateActivity ={ activity_date: activity_date ,care_calendar_id: calendarIdArrays[0] ,visit_schedule_time: visit_schedule_time }
            updateActivity = preProcessRecords(dbConnections().main.entities["care_calendar"], updateActivity , null )
            await transaction.getRepository("care_calendar").update(
              { care_calendar_id: calendarIdArrays[0] },
              updateActivity
              );

          }
          else {
            const reschedulingTaskObj = { oldStatus, care_calendar, activity_id, entity_type_id, entity_uuid, activity_date ,visit_schedule_time , visit_creation_time : formattedDateTimeInGMT, activity_created_by:user_id};

            const rescheduledTaskId = await generateFollowUp(reschedulingTaskObj, transaction);
            let care_calendar_data= {
              care_calendar_id: care_calendar,
              classifier_id: classifiers.FOLLOW_UP_TASK_ID,
              value_reference_uuid: rescheduledTaskId
            }
            
            care_calendar_data = preProcessRecords (dbConnections().main.entities["care_calendar_classification"],care_calendar_data , null)
            await transaction.getRepository("care_calendar_classification").insert(care_calendar_data);         
            await saveActivityClassification(care_calendar , rescheduledTaskId , transaction)

            if (get_preview && get_preview === true) {
              responsePayload["previewData"] = await getTaskPreview(rescheduledTaskId, activity_id, transaction);
              responsePayload["care_calendar_id"] = rescheduledTaskId;
            }
          }


        }
      if (data.calendar_activity_status == staff_activity_statuses.VISIT_COMPLETED && !frozenStatuses.has(oldStatus)) {
        await updateCompleteTransactionLedger({ care_calendar, user_id }, transaction);
        //transaction ledger update to CONFIRMED
      }

      else if (!frozenStatuses.has(oldStatus) && cancelledAbandonedStatus.has(data.calendar_activity_status)) {
        await updateCancelledTransactionLedger({ care_calendar, user_id }, transaction);
      }
    });
    return responsePayload;
  } catch (error) {  
    return -1;
  }



}