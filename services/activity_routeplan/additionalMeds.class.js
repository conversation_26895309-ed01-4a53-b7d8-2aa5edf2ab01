const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm')
const { In, Not } = require('typeorm')

const { ACTIVE, INACTIVE } = require('../../utils/constant')

const warehouseHandler = async (calendarDetails , transaction) => {
    try {
        let { newlyInserted, editedRecord, deactivatedRecord, user_id, user_type, care_calendar_id } = calendarDetails 
        let warehouseBlockQty = []
        const warehouseBlockQtyEntity = dbConnections().main.entities['warehouse_blockqty']
        let editedRecordIds = editedRecord.map(care_calendar_classification => care_calendar_classification.care_calendar_classification_id)
        let getWareHouseBlockedQty = await transaction.getRepository('warehouse_blockqty').find({
            where: { source_uuid: In(editedRecordIds), active: ACTIVE }
        })
        //for newlyInsertedIds
        for(let newInsert of newlyInserted){
            let wareHouse = {
                warehouse_type_id: user_type,
                warehouse_uuid: user_id,
                source_uuid: newInsert.care_calendar_classification_id,
                medicine_id: newInsert.value_reference_id,
                unblocked_qty: newInsert.value_double,
                last_modifying_user_id: user_id
              }
              warehouseBlockQty.push(wareHouse)
        } 
        for(let editRecord of editedRecord) {
            let findWareHouseId = getWareHouseBlockedQty.find(wareHouse => wareHouse.source_uuid === editRecord.care_calendar_classification_id)
            let wareHouse = {
                warehouse_type_id: user_type,
                warehouse_uuid: user_id,
                source_uuid: newInsert.care_calendar_classification_id,
                medicine_id: newInsert.value_reference_id,
                unblocked_qty: newInsert.value_double,
                last_modifying_user_id: user_id
              }
              if(findWareHouseId) {
                wareHouse['warehouse_blockqty_id'] = findWareHouseId.warehouse_blockqty_id
              }
              warehouseBlockQty.push(wareHouse)

        }
        let deactivatedRecordIds = deactivatedRecord.map(care_calendar_classification => care_calendar_classification.care_calendar_classification_id)
        if (deactivatedRecordIds && deactivatedRecordIds.length) {
            let deactivate = {
                active: INACTIVE
            }
            let deactivateBlockedQty = preProcessRecords(warehouseBlockQtyEntity, deactivate, null)
            if(deactivateBlockedQty.care_calendar_classification_id){
              delete deactivateBlockedQty.care_calendar_classification_id
            }
            await transaction.getRepository('warehouse_blockqty').update({ source_uuid: In(deactivatedRecordIds) }, deactivateBlockedQty)
        }
         warehouseBlockQty = preProcessRecords(warehouseBlockQtyEntity, warehouseBlockQty, null)

        return await transaction.save('warehouse_blockqty',warehouseBlockQty);
    } catch (error) {
        
    }
}
const inventoryHandler = async (calendarDetails , transaction) => {
    try {
        let { newlyInserted, editedRecord, deactivatedRecord, user_id, user_type, care_calendar_id } = calendarDetails 
        let inventoryLedger = []
        let getEditedRecordIds = editedRecord.map(care_calendar_classification => care_calendar_classification.care_calendar_classification_id)
        let creditRecordIds = deactivatedRecord.map(care_calendar_classification => care_calendar_classification.care_calendar_classification_id)
        const inventoryLedgerEntity = dbConnections().main.entities['inventory_ledger']

        //get inventory for existing source Id
        let existingInvenotyWithSameSourceUuid = await transaction.getRepository('inventory_ledger').find({
            where : { source_uuid : In(getEditedRecordIds) }
        })
        postProcessRecords(undefined, existingInvenotyWithSameSourceUuid, { json_columns: [] })

        //get inventory for deleted souce id
        let needToCreditInInventoryBasedOnDeactivatedRecords = await transaction.getRepository('inventory_ledger').find({
            where : { source_uuid : In(creditRecordIds) }
        })
        postProcessRecords(undefined, needToCreditInInventoryBasedOnDeactivatedRecords, { json_columns: [] })

        //for creating a new ledger
        for ( let newInsert of newlyInserted) {
            let inventory_ledger = {
                medicine_id: newInsert.value_reference_id,
                warehouse_type_id: user_type,
                warehouse_uuid: user_id,
                source_uuid: newInsert.care_calendar_classification_id,
                debit_qty: newInsert.value_double,
                inserted_at: new Date().toISOString()
              }
              let latestMeds= await  getLatestMeds(user_id, newInsert.value_reference_id , transaction)
              let balance = latestMeds && latestMeds.balance ? parseFloat(latestMeds.balance) - newInsert.value_double : newInsert.value_double
              inventory_ledger['balance'] = balance
              inventoryLedger.push(inventory_ledger)
        }

        // for editing exisiting one
        for ( let editRecord of editedRecord){
            let inventory_ledger = {
                medicine_id: editRecord.value_reference_id,
                warehouse_type_id: user_type,
                warehouse_uuid: user_id,
                source_uuid: editRecord.care_calendar_classification_id,
                debit_qty: editRecord.value_double,
                inserted_at: new Date().toISOString()

              }
              let latestMeds= await getLatestMeds(user_id, editRecord.value_reference_id , transaction)
            //   let balance = latestMeds && latestMeds.balance ? parseFloat(latestMeds.balance) - newInsert.value_double : newInsert.value_double
              let balanceForDebit = latestMeds && latestMeds.balance ? parseFloat(latestMeds.balance) - editRecord.value_double : editRecord.value_double
              inventory_ledger['balance'] = balanceForDebit
              let findExistingSourceUuid = existingInvenotyWithSameSourceUuid.find(inventory=> inventory.source_uuid === editRecord.care_calendar_classification_id)
              if(findExistingSourceUuid) {
                let credit_ineventory_ledger = {
                    medicine_id: editRecord.value_reference_id,
                    warehouse_type_id: user_type,
                    warehouse_uuid: user_id,
                    source_uuid: editRecord.care_calendar_classification_id,
                    credit_qty: findExistingSourceUuid.debit_qty,
                    inserted_at: new Date().toISOString()

                }
                let balanceForCredit = latestMeds && latestMeds.balance ? parseFloat(latestMeds.balance) + findExistingSourceUuid.value_double : findExistingSourceUuid.value_double
                credit_ineventory_ledger['balance'] = balanceForCredit
                inventoryLedger.push(credit_ineventory_ledger)

              }
              inventoryLedger.push(inventory_ledger) 
        }
        // for removing meds
        for( let creditRecord of deactivatedRecord) {
            let findExistingSourceUuid = needToCreditInInventoryBasedOnDeactivatedRecords.find(inventory=> inventory.source_uuid === creditRecord.care_calendar_classification_id)
            if(findExistingSourceUuid) {
              let credit_ineventory_ledger = {
                  medicine_id: creditRecord.value_reference_id,
                  warehouse_type_id: user_type,
                  warehouse_uuid: user_id,
                  source_uuid: creditRecord.care_calendar_classification_id,
                  credit_qty: findExistingSourceUuid.debit_qty,
                  inserted_at: new Date().toISOString()

              }
              let latestMeds= await getLatestMeds(user_id, creditRecord.value_reference_id , transaction)

              let balanceForCredit = latestMeds && latestMeds.balance ? parseFloat(latestMeds.balance) + findExistingSourceUuid.value_double : findExistingSourceUuid.value_double
              credit_ineventory_ledger['balance'] = balanceForCredit
              inventoryLedger.push(credit_ineventory_ledger)

            }
        }
        inventoryLedger = preProcessRecords(inventoryLedgerEntity, inventoryLedger, null)

        await transaction.save('inventory_ledger', inventoryLedger)
    } catch (error) {
        throw error
    }
}

const getLatestMeds = async (user_id , medicine_id , transaction ) => {
    try {
        let inventory = await transaction.getRepository('inventory_ledger').find({
            where: { warehouse_uuid: user_id, medicine_id: medicine_id, active: ACTIVE },
            order: { created_at: "DESC" } ,
            take: 1 // Limit the result to one record

        })
        postProcessRecords(undefined, inventory, { json_columns: [] })

        return inventory
    } catch (error) {
        
    }
}

const warehouseInventoryHandler = async (calendarDetails, transaction) => {
   try{
        await warehouseHandler(calendarDetails,transaction)
        await inventoryHandler(calendarDetails,transaction)
   } catch (err) {
     throw err
   }
}
class ActivityAdditionalMedicineServiceV2 {
  updateAdditionalMeds = async (data, params) => {
    try {
      let {  medicine, care_calendar_id } = data
      const { user_id, user_type } = params.headers.token
      return await dbConnections().main.manager.transaction(async transaction => {
        let medsToInserted = []
        let care_calendar_classifier_ids = []
        //get all existing meds
        let allAdditionalMeds = await transaction.getRepository('care_calendar_classification').find({
          where: { care_calendar_id: care_calendar_id, classifier_id: 2000000270, active: ACTIVE },
          select: { care_calendar_classification_id: true, value_double: true, value_reference_id: true }
        })
        //2
        postProcessRecords(undefined, allAdditionalMeds, { json_columns: [] })

        for (let med of medicine) {
          //2
          let elem = {
            value_reference_id: med.medicine_id,
            value_double: med.medicine_quantity,
            classifier_id: 2000000270,
            last_modifying_user_id: user_id,
            care_calendar_id: care_calendar_id
          }
          if (med['care_calendar_classification_id']) {
            elem['care_calendar_classification_id'] = med['care_calendar_classification_id']
            care_calendar_classifier_ids.push(med['care_calendar_classification_id'])
            delete elem.value_reference_id
          }
          medsToInserted.push(elem) //2 1 i 1u
        }

        let inactiveMeds = { active: INACTIVE }
        inactiveMeds = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], inactiveMeds, null)
        if(inactiveMeds.care_calendar_classification_id){
          delete inactiveMeds.care_calendar_classification_id
        }
        let deactivatedMeds = await transaction.getRepository('care_calendar_classification').update(
          {
            care_calendar_classification_id: Not(In(care_calendar_classifier_ids)),
            care_calendar_id,
            classifier_id: 2000000270
          },
          inactiveMeds
        ) //1

        medsToInserted = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], medsToInserted, null)
        //save meds //2
        let createdMeds = await transaction.getRepository('care_calendar_classification').save(medsToInserted)
        let afterInsertion = await transaction.getRepository('care_calendar_classification').find({
          where: { care_calendar_id: care_calendar_id, classifier_id: 2000000270, active: ACTIVE },
          select: { care_calendar_classification_id: true, value_double: true, value_reference_id: true }
        }) //2 2
        postProcessRecords(undefined, afterInsertion, { json_columns: [] })

        const allAdditionalMedsIds = new Set(allAdditionalMeds.map(obj => obj.care_calendar_classification_id))
        const afterInsertionIds = new Set(afterInsertion.map(obj => obj.care_calendar_classification_id))

        const newlyInserted = afterInsertion.filter(objB => !allAdditionalMedsIds.has(objB.care_calendar_classification_id))
        // const editedMedsId = afterInsertion.map((obj, index) => obj.value_double !== allAdditionalMeds[index]?.value_double ? obj : null).filter(id => id !== null)
        const uniqueObjectsMap = {};
        const editedMedsId = allAdditionalMeds.map(objA => {
        const matchingObjectB = afterInsertion.find(objB => objB.care_calendar_classification_id === objA.care_calendar_classification_id && objB.value_double !== objA.value_double);
          if (matchingObjectB && !uniqueObjectsMap[matchingObjectB.care_calendar_classification_id]) {
            uniqueObjectsMap[matchingObjectB.care_calendar_classification_id] = matchingObjectB;
             
          }

         });
         let editedRecords =  Object.values(uniqueObjectsMap);
        // const editedRecord = afterInsertion.filter(objB => allAdditionalMedsIds.has(objB.care_calendar_classification_id))
        const deactivatedRecord = allAdditionalMeds.filter(objA => !afterInsertionIds.has(objA.care_calendar_classification_id))
        await warehouseInventoryHandler({ newlyInserted, editedRecord: editedRecords, deactivatedRecord, user_id, user_type, care_calendar_id }, transaction)
        // await  insertToWarehouseBlockQty(createdMeds ,`deactivatedMeds`, care_calendar_id , user_id, user_type , transactionalEntityManager)
        return createdMeds
      })
    } catch (error) {
      return error
    }
  }
  getAdditionalMedicine = async care_calendar_id => {
    try {
      let loadAdditionalMedicine = await dbConnections().main.repos['care_calendar_classification'].find({
        select: { care_calendar_classification_id: true, value_double: true, value_reference_id: true },
        where: { active: ACTIVE, classifier_id: 2000000270, care_calendar_id }
      })
      postProcessRecords(undefined, loadAdditionalMedicine, { json_columns: [] })
      loadAdditionalMedicine = loadAdditionalMedicine.map(med => {
        return { medicine_id: med.value_reference_id, medicine_quantity: med.value_double, care_calendar_classification_id: med.care_calendar_classification_id }
      })
      return loadAdditionalMedicine
    } catch (error) {
      return -1
    }
  }
}

module.exports = { ActivityAdditionalMedicineServiceV2 }
