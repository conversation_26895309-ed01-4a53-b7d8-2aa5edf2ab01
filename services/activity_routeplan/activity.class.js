const { configurationJSON } = require('@krushal-it/common-core')
const { CareCalendarQueries } = require('../../queries/care-calendar')
const { getCalendarEntries, medicineClassifications, createTask, updateActivity, farmerLevelTask, updateFarmerForm, getLinkedActivity, getTaskList, getRoutePlan, getTasks, getActivityObservations, updateActivityObservations, getActivityDiagnosis, updateActivityDiagnosis, getActivityPrescription, updateActivityPrescription, getTaskPreviewById, getActivityObservations_v2, getActivityDiagnosis_v2, getActivityPrescription_v2, getActivityNotes, getActivityObservations_v3 } = require('./controller')
const { dbConnections, postProcessRecords } = require('@krushal-it/ah-orm')
const { loadClassificationData, getClassificationConfiguration, saveClassificationData } = require('../common/classification.helper')
const { getCompiledQuery } = require('../../queries/index.js')
const { validateUUID, infoLog, errorLog } = require('@krushal-it/mobile-or-server-lib')
const { LINKED_CARE_CALENDAR_CLASSIFIER, ANIMAL_TYPE_CALENDAR, ACTIVE, ANIMAL_EAR_TAG_CLASSIFIER, ANIMAL_TYPE_CLASSIFIER, PARAVET, CONST_ID_TYPE_STAFF_BACKOFFICE, FARMER, EXPIRY_DATE_CLASSIFIER } = require('../../utils/constant')
const { findDuplicateIds } = require("../../utils/animal")
const { getRouteDetails } = require("../activity_routeplan/controller.js");
const { generateOtp } = require("../../utils/utils.js");
const {classifiers: {ACCOUNT_PAYMENT_TYPE, TASK_MEDICINE_COST_PAYMENT_DETAILS, TASK_SERVICE_COST_PAYMENT_DETAILS}, payer, classifiers} = require('../../ENUMS.js');
const { getPaymentDetails, fetchAnimalClassification, fetchAnimalsOnAnimalCareCalendarId } = require("../serviceDocument/controller.js");
const { ActivityHelper } = require("../../utils/activity.js");
const { In } = require("typeorm");
class LibActivityService {
  async find(params) {
    try {
      const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
      const { object } = params.route
      const { user_id, user_type } = params.headers.token
      switch (object) {
        case 'config':
          return { message: "Use 'api/references/care-calendar' instead" }
        case 'care-calendar':
          const data = await getCalendarEntries({ ...params.query, user_type, user_id })
          return data
        case 'by-id':
          let { calendar_id } = params.query
          if (!validateUUID(calendar_id)) throw new Error('calendar_id is required')

          const queryParameters = { user_id, user_type, calendar_id }
          const notUsersForvill = new Set([CONST_ID_TYPE_STAFF_BACKOFFICE, FARMER])
          const templateParameters = { user_id, user_type, calendar_id, ACTIVE, notUsersForvill, EXPIRY_DATE_CLASSIFIER, IS_POSTGRES: isPostgres }

          let query = getCompiledQuery('activitybyid', templateParameters)
          const queryBuilder = await dbConnections().main.manager.createQueryBuilder().from(`(${query})`, 'a1').setParameters(queryParameters)

          let activityDetails = await queryBuilder.select('*').execute()
          postProcessRecords(undefined, activityDetails, { json_columns: ['activity_name_l10n', 'animal_type_json', 'calendar_activity_status_name_json', 'customer_name_l10n', 'paravet_name', 'village_name_l10n', 'vet_name', 'document_information', 'client_document_information'] })

          if (activityDetails.length === 0) return {}

          activityDetails = activityDetails[0]

          const classificationConfig = getClassificationConfiguration()['CARE_CALENDAR_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
          const classifiers = Object.keys(classificationConfig)
          let classificationData = await loadClassificationData('CARE_CALENDAR_CLASSIFICATION', calendar_id, classifiers)
          let activityFiles = await dbConnections().main.repos['document'].find({
            where: [{ entity_1_entity_uuid: calendar_id, document_type_id: 1000260010 }],
            select: { document_id: true, document_information: true, client_document_information: true, document_type_id: true },
          })
          postProcessRecords(undefined, activityFiles, { json_columns: ['document_information', 'client_document_information'] })

          let followUpDateTime = null
          const followUpActivity = await dbConnections().main.repos['care_calendar_classification'].find({
            where: { active: ACTIVE, care_calendar_id: calendar_id, classifier_id: 2000000186 },
            select: { value_reference_uuid: true },
          })
          if (followUpActivity.length > 0) {
            const folloUpTaskId = followUpActivity[0].value_reference_uuid
            const folloupTask = await dbConnections().main.repos['care_calendar'].findOne({
              where: { care_calendar_id: folloUpTaskId },
              select: { visit_schedule_time: true },
            })
            followUpDateTime = folloupTask && folloupTask.visit_schedule_time
          }

          let medicines = []
          for (let medClassificationIndex = 0; medClassificationIndex < medicineClassifications.length; medClassificationIndex++) {
            let medKey = medicineClassifications[medClassificationIndex][0]
            let medQntKey = medicineClassifications[medClassificationIndex][1]
            if (classificationData[medKey]) {
              medicines.push({
                [medKey]: classificationData[medKey],
                [medQntKey]: classificationData[medQntKey] || null,
              })
            }
          }
          let prescriptionPDF = await dbConnections().main.repos['document'].findOne({
            where: [{ entity_1_entity_uuid: calendar_id, document_type_id: 1000260015, active: ACTIVE }],
            select: { document_id: true, document_information: true, client_document_information: true, document_type_id: true },
          })
          prescriptionPDF = postProcessRecords(undefined, prescriptionPDF, { json_columns: ['document_information', 'client_document_information'] })

          let serviceDocument = await dbConnections().main.repos['document'].findOne({
            where: [{ entity_1_entity_uuid: calendar_id, document_type_id: 1000260026, active: ACTIVE }],
            select: { document_id: true, document_information: true, client_document_information: true, document_type_id: true },
          })
          serviceDocument = postProcessRecords(undefined, serviceDocument, { json_columns: ['document_information', 'client_document_information'] })

          if (farmerLevelTask.has(activityDetails.activity_id)) {
            const templateParameters = { farmerCalendarId: calendar_id, LINKED_CARE_CALENDAR_CLASSIFIER, ANIMAL_TYPE_CALENDAR, ACTIVE, ANIMAL_EAR_TAG_CLASSIFIER, ANIMAL_TYPE_CLASSIFIER }
            let query = getCompiledQuery('propagatedanimals', templateParameters)
            let animalData = await dbConnections().main.manager.query(query)
            postProcessRecords(undefined, animalData, { json_columns: ['reference_name_l10n'] })
            activityDetails.animals = animalData
          }

          let observation = await getActivityObservations_v2(calendar_id)
          if (!observation || observation.length == 0) {
            observation = await getActivityObservations_v3(calendar_id)
          }

          

          
          let diagnosis= await getActivityDiagnosis_v2(calendar_id);
          let prescription= await getActivityPrescription_v2(calendar_id);
          let observationNotes= await getActivityNotes(calendar_id,1000440006)
          let prescriptionNotes = await getActivityNotes(calendar_id, 1000440005)

          
          
          const return_value = {
            ...activityDetails,
            ...classificationData,
            task_files: activityFiles,
            medicines,
            observation,
            ...diagnosis,
            prescription: prescription.prescriptionsWithRouteNames,
            total_price: prescription.total_price,
            vet_consultant_cost: prescription.vet_consultant_cost,
            observationNotes,
            prescriptionNotes,
            followUpDateTime,
            prescriptionPDF,
            serviceDocument,
          }

          const payment_detail_break_up = await getPaymentDetailBreakUp(calendar_id)
          return_value["payment_detail_break_up"] = payment_detail_break_up
          

          return return_value
      }
      return params
    } catch (error) {
      errorLog('error in LibActivityService:find', params, { message: error.message })
      return { result: false, error }
    }
  }

  async create(data, params) {
    try {
      let validUserStaff = new Set([1000230001, 1000230002, 1000230003, 1000230004])
      let validUserCustomer = new Set([1000220001])
      const { object } = params.route
      let { token } = params.headers
      switch (object) {
        case 'create-task': {
          if (!validUserStaff.has(token.staff_type)) throw new Error('unauthorized')

          let taskData = await createTask(data, token)

          return { data: taskData, id: taskData.care_calendar_id }
        }

        case 'create-task-by-farmer': {
          if (!validUserCustomer.has(token.user_type)) throw new Error('unauthorized')
          let id = await updateFarmerForm(data, token)
          return { id }
        }
        default:
          throw new Error('Invalid Request')
      }
    } catch (error) {
      errorLog('error in LibActivityService:83', params, { message: error.message })
      throw error;
    }
  }
  async update(id, data, params) {
    try {
      let objective = params.route.object
      let token = params.headers.token
      let { care_calendar, staff_id, activity_id, entity_type_id } = data
      switch (objective) {
        case 'update-activity':
          return { data: await updateActivity(care_calendar, data, token) }
        case 're-assign':
          await dbConnections().main.manager.transaction(async (transactionEntityManager) => {
            await CareCalendarQueries.updateStaffActivity(
              {
                staff_id,
                activity_id,
                entity_type_id,
                entity_id: staff_id,
                staff_activity_type_id: 1001,
                activity_duration: new Date(),
              },
              {
                care_calendar,
                transaction: transactionEntityManager,
              }
            )
          })
          return { result: true }
        default:
          return { result: true }
      }
    } catch (error) {
      errorLog('error in LibActivityService:117', params, { message: error.message })
      return { result: false, error: error.message }
    }
  }
}

class LibTaskList {
  async create(data, params) {
    let { token } = params.headers
    let result = await getTaskList({ ...data, ...token })
    return result
  }
}

class LibCardService {
  async create(data, params) {
    const { subPath } = params.route
    const { customer_id } = params.query

    try {
      const { staff_id, user_id, user_type } = params.headers.token
      switch (subPath) {
        case 'ro':
          let roresult = await CareCalendarQueries.getCardOfRO({ user_id })
          return { roresult }

        case 'paravet':
          let pvResult = await CareCalendarQueries.getCardOfPV({
            user_id,
            ...data,
          })
          return pvResult
        case 'zo-total':
          let zoTotal = await CareCalendarQueries.getTotalZORO_v2({ user_id })
          return zoTotal
        case 'ro-total':
          let roTotal = await CareCalendarQueries.getActivityStats({ user_id, user_type })
          return roTotal
        case 'farmer':
          let farmer = await CareCalendarQueries.getCardOfFarmer({
            customer_id,
            ...data,
          })
          return farmer
      }
    } catch (error) {
      errorLog('error in LibActivityService:164', params, { message: error.message })
      return { result: false, message: error.message }
    }
  }
}
class LibChildCards {
  async create(data, params) {
    const { subPath } = params.route
    const { token } = params.headers
    const { user_id, user_type } = token
    try {
      switch (subPath) {
        case 'ro-lss':
          let data = await CareCalendarQueries.getActivityStatsByStaffType({ user_id, user_type, staff_type: PARAVET })
          return data
        case 'zo-lss':
          let zoData = await CareCalendarQueries.getZoCardByRo_v2({
            user_id,
          })
          return zoData
      }
    } catch (error) {
      errorLog('error in LibActivityService:186', params, { message: error.message })
      return { result: false, message: error.message }
    }
  }
}

class LibRouteDetailService {
  async create(data, params) {
    try {
      const { user_id, user_type } = params.headers.token
      return await getRouteDetails(data, user_id, user_type)
    } catch (error) {
      errorLog('error in LibRouteDetailService:create', params, { message: error.message })
      return { result: false }
    }
  }
}

class LibRoutePlanDetailService {
  async create(data, params) {
    try {
      let animalTask = await CareCalendarQueries.getRoutePlanDetails(data)
      let farmerResult = []
      if (data.animal_id === undefined) {
        farmerResult = await CareCalendarQueries.farmerRoutePlanDetail(data)
      }
      return { animalTask, farmerTask: farmerResult }
    } catch (error) {
      errorLog('error in LibRoutePlanDetailService', params, { message: error.message })
      return { result: false }
    }
  }
}
class LibRoutePlanService {
  async create(data, params) {
    try {
      const { user_id, user_type } = params.headers.token
      let result = await getRoutePlan(data, user_id, user_type)
      return { result }
    } catch (error) {
      errorLog('error in LibActivityService:213', params, { message: error.message })
      return { result: false, message: error.message }
    }
  }
}

class LibLinkedTaskService {
  async get(id, params) {
    try {
      // finding parent task
      let result = await getLinkedActivity(id)
      return { result }
    } catch (error) {
      errorLog('error in LibLinkedTaskService', { id, params }, { message: error })
      return -1
    }
  }
}

class LibTask {
  async getFarmerLevelTask(data, params) {
    const { token } = params.headers
    return await getTasks(data, token, { querType: 'farmer' })
  }
  async getAnimalLevelTask(data, params) {
    const { token } = params.headers
    return await getTasks(data, token, { querType: 'animal' })
  }
  async getTaskById(data, params) {
    const { token } = params.headers
    return await getTasks(data, token, { querType: 'id' })
  }
}

class LibActivityObservationService {
  async getObservations(data, params) {
    try {
      return { data: await getActivityObservations(data, params) }
    } catch (error) {
      errorLog('error in LibActivityObservationService', { data }, { message: error })
      return -1
    }
  }
  async updateObservation(data, params) {
    let duplicateIds = findDuplicateIds(data.observations)
    if (duplicateIds.length) return { result: false, message: `duplicate observation found ${duplicateIds.join(', ')}` }
    return await updateActivityObservations(data, params)
  }
}
class LibActivityDiagnosisService {
  async getDiagnosis(data, params) {
    try {
      return await getActivityDiagnosis(data, params)
    } catch (error) {
      errorLog('error in LibActivityDiagnosisService', { data }, { message: error })
      return -1
    }
  }

  async updateDiagnosis(data, params) {
    try {
      return await updateActivityDiagnosis(data, params)
    } catch (error) {
      errorLog('error in LibActivityDiagnosisService', { data }, { message: error })
      return -1
    }
  }
}

class LibActivityPrescriptionService {
  async getPrescription(data, params) {
    try {
      return await getActivityPrescription(data, params)
    } catch (error) {
      errorLog('error in LibActivityPrescriptionService', { data }, { message: error })
      return -1
    }
  }
  async updatePrescription(data, params) {
    try {
      return await updateActivityPrescription(data, params)
    } catch (error) {
      errorLog('error in LibActivityPrescriptionService', { data }, { message: error })
      return -1
    }
  }
}
class ActivityPreviewService {
  async create(data, params) {
    try {
      const { care_calendar_id } = data
      return getTaskPreviewById(care_calendar_id)
    } catch (error) {
      errorLog('error in ActivityPreviewService', { data }, { message: error })
      return -1
    }
  }
}

async function getPaymentDetailBreakUp(care_calendar_id) {
  const { TASK_SERVICE_COST_PAYMENT_DETAILS, TASK_MEDICINE_COST_PAYMENT_DETAILS } = classifiers
  const payment_detail_break_up = {
    medicine_cost_payer: [],
    service_cost_payer:[]
  }

  let paymentDetail = await dbConnections().main.manager.getRepository("care_calendar_classification").find({
    where: {
      care_calendar_id,
      classifier_id: In([TASK_SERVICE_COST_PAYMENT_DETAILS,TASK_MEDICINE_COST_PAYMENT_DETAILS])
    }
  })

  paymentDetail = postProcessRecords(undefined, paymentDetail, {
    json_columns: ['value_json', 'value_l10n'],
  })

  for (const detail of paymentDetail) {
    if (detail.classifier_id === TASK_SERVICE_COST_PAYMENT_DETAILS)
      payment_detail_break_up.service_cost_payer =  detail.value_json?.value
    if (detail.classifier_id === TASK_MEDICINE_COST_PAYMENT_DETAILS)
      payment_detail_break_up.medicine_cost_payer =  detail.value_json?.value
  }

  return payment_detail_break_up
}

module.exports = {
  LibActivityService,
  LibTaskList,
  LibCardService,
  LibChildCards,
  LibRoutePlanDetailService,
  LibRoutePlanService,
  LibLinkedTaskService,
  LibTask,
  LibActivityObservationService,
  LibActivityDiagnosisService,
  LibActivityPrescriptionService,
  ActivityPreviewService,
  LibRouteDetailService,
  getPaymentDetailBreakUp
}
