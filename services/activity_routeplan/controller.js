const { configurationJSON, post } = require('@krushal-it/common-core')
const { CareCalendarQueries, CareCalendarActivityQueries } = require('../../queries/care-calendar.js')
const { ActivityHelper } = require('../../utils/activity')
const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm')
const { In, Not } = require('typeorm')
const { q_getCareCalendar, q_getLinkedTask, q_taskListing, getFormCalendar } = require('../../queries/activity')
// const uuid = require("uuid");
const moment = require('moment')
const { validateUUID, infoLog, errorLog, generateUUID } = require('@krushal-it/mobile-or-server-lib')
const { saveClassificationData, getClassificationConfiguration, loadClassificationData, loadClassificationDataWithClassifierIds } = require('../common/classification.helper')
const { getCompiledQuery, getTemplate } = require('../../queries/index.js')
const _ = require('lodash')
const { LessThanOrEqual, MoreThanOrEqual } = require('typeorm')
const { addUpdateMedForWarehouse, removeMedFromWarehouse } = require('../../services/warehouse/controller.js')
const { saveActivityClassification } = require('../observationV2/activityClassification.js')

const {
  NO,
  CONST_ACTIVITY_PARAVET_FARM_DETAIL,
  RELATION_CALENDAR,
  CONST_ID_TYPE_STAFF_PARAVET,
  CONST_ID_TYPE_STAFF_BACKOFFICE,
  REPRODUCTIVE,
  CURATIVE,
  TICKET_CLASSIFIER,
  FARMER_TO_ANIMAL,
  LINKED_CARE_CALENDAR_CLASSIFIER,
  VILLAGE_CLASSIFIER,
  VILLAGE_TYPE_ID,
  PARAVET,
  ANIMAL_NAME_CLASSIFIER,
  ANIMAL_TYPE_CLASSIFIER,
  ANIMAL_EAR_TAG_CLASSIFIER,
  FARMER_TYPE_CALENDAR,
  ANIMAL_TYPE_CALENDAR,
  COMPLETED_ACTIVITY_STATUS,
  CANCELLED_ACTIVITY_STATUS,
  ABANDONED_ACTIVITY_STATUS,
  ACTIVE,
  CONST_ACTIVITY_ID_AIP,
  CONST_ACTIVITY_ID_PD,
  CONST_STATUS_ACTIVITY_PENDING,
  CONST_STATUS_ACTIVITY_COMPLETED,
  CONST_PD_DATE_OFFSET,
  CONST_ID_TYPE_CUSTOMER_FARMER,
  INACTIVE,
  CONST_ACTIVITY_ID_FARM_TICK_CONTROL_SPRAYING,
  CONST_ACTIVITY_ID_CMT,
  CONST_ACTIVITY_ID_TICK_CONTROL,
  PPU_FARMER,
  RELATION_FREELANCE_PARAVET_TO_ANIMAL,
  TASK,
  TRANSACTION_BLOCKED,
  TRANSACTION_CANCELLED,
  TRANSACTION_CONFIRMED,
  CONST_SR_FREELANCE,
  CENTRALIZED_VET,
  PPU_PARAVET_TO_TASK,
  CONST_STAFF,
  CENTRALIZED_VET_TO_TASK,
  TRANSACTION_DEBIT,
} = require('../../utils/constant')
const { classifiers } = require('../../ENUMS.js')
const { generateOtp } = require('../../utils/utils.js')
const taskRepository = require('../../repositories/taskRepository.js')
const medicineClassifications = [
  ['medicine_1', 'medicine_1_quantity'],
  ['medicine_2', 'medicine_2_quantity'],
  ['medicine_3', 'medicine_3_quantity'],
  ['medicine_4', 'medicine_4_quantity'],
  ['medicine_5', 'medicine_5_quantity'],
  ['medicine_6', 'medicine_6_quantity'],
  ['medicine_7', 'medicine_7_quantity'],
  ['medicine_8', 'medicine_8_quantity'],
  ['medicine_9', 'medicine_9_quantity'],
  ['medicine_10', 'medicine_10_quantity'],
]

//  temporary farmer level task list
const farmerLevelTask = new Set([CONST_ACTIVITY_ID_FARM_TICK_CONTROL_SPRAYING, CONST_ACTIVITY_ID_CMT, CONST_ACTIVITY_PARAVET_FARM_DETAIL])
const frozenStatuses = new Set([CONST_STATUS_ACTIVITY_COMPLETED, CANCELLED_ACTIVITY_STATUS, ABANDONED_ACTIVITY_STATUS])
const cancelledAbandonedStatus = new Set([CANCELLED_ACTIVITY_STATUS, ABANDONED_ACTIVITY_STATUS])

const getCalendarEntries = async (options) => {
  try {
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
    const { user_type, user_id } = options

    let baseQuery = q_getCareCalendar({ user_id, user_type })
    const queryBuilder = dbConnections().main.manager.createQueryBuilder().select('*').from(`(${baseQuery})`, 'care_calendar')
    if (user_type == **********) {
      options.farmer_id = user_id
      queryBuilder.andWhere('activity_id != 1000700002')
    } else if (user_type == 1000230002) {
    } else {
      queryBuilder.andWhere('activity_id != 1000700001')
    }
    if (options.dashboard == 1) {
      if (isPostgres === 1) {
        queryBuilder.andWhere('activity_date::DATE < current_date+15')
      } else {
        queryBuilder.andWhere("activity_date < date('now', 'start of day', '+15 day')")
      }
    }
    if (options.animal_id) {
      // if (!uuid.validate(options.animal_id)) return {};
      if (!validateUUID(options.animal_id)) return {}
      queryBuilder.andWhere('animal_id = :animal_id', { animal_id: options.animal_id })
    }
    if (options.farmer_id) {
      // if (!uuid.validate(options.farmer_id)) return {};
      if (!validateUUID(options.farmer_id)) return {}
      queryBuilder.andWhere('customer_id= :farmer_id', { farmer_id: options.farmer_id })
    }

    let data = await queryBuilder.execute()
    postProcessRecords(undefined, data, { json_columns: ['activity_name_l10n', 'activity_category_json', 'paravet_name'] })
    return data
  } catch (error) {
    errorLog('error in getCalendarEntries:28', options, { message: error.message })
    return -1
  }
}

const createTaskv2 = async (options, token) => {
  try {
    let { entity_uuid, activity_id, activity_date, staff_id, get_preview } = options
    const { user_id, user_type } = token

    let data = await dbConnections().main.manager.transaction(async (transactionalEntityManager) => {
      // check association with staff id and cattle if staff Id is given
      if (staff_id) {
        let checkAssociation = await checkAnimalAssociation(staff_id, entity_uuid, transactionalEntityManager)
        if (!checkAssociation) return { message: 'staff is not assosciated with this animal' }
      }
      let activeAnimal = await checkAnimalActiveness({ entity_uuid }, transactionalEntityManager)
      if (!activeAnimal) return false
      let { calender_id, ticketNumber } = await insertCareCalendarAndClassifiers(options, token, transactionalEntityManager)
      if (activity_id == CONST_ACTIVITY_ID_AIP) {
        // cancel previously generated PD tasks for this animal
        await cancelPreviousPDTask(entity_uuid, transactionalEntityManager)
        await createAITask({ calender_id, entity_uuid, activity_date })
      }

      const payLoad = { id: calender_id }
      if (get_preview === true) {
        payLoad['previewData'] = await getTaskPreview(calender_id, activity_id)
      }
      if (staff_id) {
        await freelanceMapping({ calender_id, staff_id, ticketNumber }, transactionalEntityManager)
      }
      return payLoad
    })
    return data
  } catch (error) {
    return -1
  }
}

const checkAnimalAssociation = async (staff_id, animal_id, transaction) => {
  try {
    let data = await transaction.getRepository('entity_relationship').find({
      where: {
        entity_1_entity_uuid: staff_id,
        entity_2_entity_uuid: animal_id,
        entity_relationship_type_id: RELATION_FREELANCE_PARAVET_TO_ANIMAL,
        active: ACTIVE,
      },
      select: {
        entity_relationship_type_id: true,
      },
    })
    return data.length ? true : false
  } catch (error) {
    return -1
  }
}

const freelanceMapping = async (data, transaction = null) => {
  try {
    const { calender_id, staff_id, ticketNumber } = data
    let transactionalEntityManager = transaction ? transaction : await dbConnections().main.manager
    const centralizedVet = await transactionalEntityManager.getRepository('staff').find({
      where: { staff_type_id: CENTRALIZED_VET, active: ACTIVE },
      select: { staff_id: true },
    })

    let entityRelationforPPU = {
      entity_relationship_type_id: PPU_PARAVET_TO_TASK,
      entity_1_type_id: CONST_STAFF,
      entity_1_entity_uuid: staff_id,
      entity_2_type_id: TASK,
      entity_2_entity_uuid: calender_id,
    }

    await transactionalEntityManager.getRepository('entity_relationship').insert(entityRelationforPPU)
    for (let i = 0; i < centralizedVet.length; i++) {
      let entityRelationforCentralVet = {
        entity_relationship_type_id: CENTRALIZED_VET_TO_TASK,
        entity_1_type_id: CONST_STAFF,
        entity_2_type_id: TASK,
        entity_2_entity_uuid: calender_id,
        entity_1_entity_uuid: centralizedVet[i].staff_id,
      }
      await transactionalEntityManager.getRepository('entity_relationship').insert(entityRelationforCentralVet)
    }
    let price = await getPriceforStaff(staff_id, transaction)
    let transaction_ledger = { staff_id, calender_id, price, ticketNumber }
    let insertTransactionLedger = updateTransactionLedger(transaction, transaction_ledger)
    return 'success'
  } catch (error) {
    return -1
  }
}

const updateTransactionLedger = async (transaction, data) => {
  try {
    let { staff_id, calender_id, price, ticketNumber } = data

    await transaction.getRepository('transaction_ledger').insert({
      staff_uuid: staff_id,
      amount: price,
      account_id: 'KRUSHAL',
      transaction_type: TRANSACTION_DEBIT,
      transaction_category: TRANSACTION_DEBIT,
      transaction_date: new Date(),
      entity_type_id: TASK,
      entity_uuid: calender_id,
      transaction_status: TRANSACTION_BLOCKED,
      transaction_description: { notes: ticketNumber },
    })
    return 'success'
  } catch (error) {
    return -1
  }
}

const getPriceforStaff = async (staff_id, transaction = null) => {
  try {
    let transactionalEntityManager = transaction ? transaction : await dbConnections().main.manager
    let completedActivityForStaff = await getCompletedActivityForStaff(staff_id, transaction)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    let loadSlabFromStaffClassifier = await transactionalEntityManager.getRepository('staff_classification').find({
      where: { staff_id, active: ACTIVE, value_date: MoreThanOrEqual(moment(today).format('yyyy-MM-DD')), classifier_id: ********** },
      select: { value_json: true },
      orderBy: {
        value_reference_id: 'DESC',
      },
    })
    let slabData = []
    if (loadSlabFromStaffClassifier.length) {
      slabData = loadSlabFromStaffClassifier[0].value_json.slabs
    }
    if (!loadSlabFromStaffClassifier.length) {
      slabData = await transactionalEntityManager.getRepository('ref_reference').find({
        where: { reference_category_id: 10009100, active: ACTIVE },
        select: { reference_information: true },
        orderBy: {
          reference_id: 'DESC',
        },
      })

      slabData = slabData.length && slabData[0].reference_information.slabs
    }
    let findPrice =
      slabData.length &&
      slabData.find((slab) => {
        if (slab.start <= completedActivityForStaff && (slab.end >= completedActivityForStaff || !slab.end)) {
          return slab
        }
      })
    let priceToDeductInTicket = findPrice?.price
    return priceToDeductInTicket ? priceToDeductInTicket : 0
  } catch (error) {
    return -1
  }
}

const getCompletedActivityForStaff = async (staff_id, transaction = null) => {
  try {
    let transactionalEntityManager = transaction ? transaction : await dbConnections().main.manager
    const startOfMonth = moment().startOf('month').format('YYYY-MM-DD')

    let activityForParavet = await transactionalEntityManager.query(`
    SELECT count(*) as total_completed_task from main.entity_relationship as er
     left join main.care_calendar as cc on cc.care_calendar_id= er.entity_2_entity_uuid
     where er.entity_1_entity_uuid='${staff_id}'
     and er.entity_relationship_type_id = ${PPU_PARAVET_TO_TASK}  
     and cc.created_at  >= '${startOfMonth}' 
    `)
    return activityForParavet[0].total_completed_task
  } catch (error) {
    return -1
  }
}

const createAITask = async (data, transaction = null) => {
  try {
    let { calender_id, entity_uuid, activity_date } = data
    let transactionalEntityManager = transaction ? transaction : await dbConnections().main.manager

    const followUpObj = {
      oldStatus: CONST_STATUS_ACTIVITY_PENDING - 1,
      care_calendar: calender_id,
      activity_id: CONST_ACTIVITY_ID_PD,
      entity_type_id: ANIMAL_TYPE_CALENDAR,
      entity_uuid,
      activity_date: moment(activity_date).add(CONST_PD_DATE_OFFSET, 'days').format('YYYY-MM-DD'),
    }
    await generateFollowUp(followUpObj, transactionalEntityManager)
    return true
  } catch (error) {
    return -1
  }
}

const insertCareCalendarAndClassifiers = async (options, token, transaction = null) => {
  try {
    let { entity_uuid, activity_id, activity_date, activity_date_gmt, for_specific_cattle, calendar_activity_status } = options
    const { user_id } = token
    let transactionalEntityManager = transaction ? transaction : await dbConnections().main.manager
    const currentDate = new Date()
    const formattedDateTimeInGMT = currentDate.toISOString()
    activity_date = activity_date.split('T')[0]
    let entity_type_id = ANIMAL_TYPE_CALENDAR
    if (for_specific_cattle && for_specific_cattle === NO) {
      entity_type_id = FARMER_TYPE_CALENDAR
    }
    let insertRes = await CareCalendarQueries.insertcalendarEntries(
      {
        entity_uuid,
        entity_type_id: entity_type_id,
        activity_id,
        activity_date,
        calendar_activity_status,
        visit_schedule_time: activity_date_gmt,
        visit_creation_time: formattedDateTimeInGMT,
        activity_created_by: user_id,
      },
      { datasource: transactionalEntityManager }
    )
    const ticketNumber = await ActivityHelper.generateTicketNumber(transactionalEntityManager)
    let care_calendar_id = insertRes.identifiers[0].care_calendar_id

    let dataKeyList = Object.keys(options)
    const classificationConfig = getClassificationConfiguration()['CARE_CALENDAR_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
    let upsertData = { ticket_1: ticketNumber, service_request_type: CONST_SR_FREELANCE }
    for (let datakeyIndex = 0; datakeyIndex < dataKeyList.length; datakeyIndex++) {
      let key = dataKeyList[datakeyIndex]
      if (classificationConfig[key]) {
        upsertData[key] = options[key]
      }
    }
    await saveClassificationData(transactionalEntityManager, 'CARE_CALENDAR_CLASSIFICATION', care_calendar_id, upsertData)
    return { calender_id: care_calendar_id, ticketNumber }
  } catch (error) {
    return -1
  }
}

const checkAnimalActiveness = async (data, transaction = null) => {
  try {
    let { entity_uuid } = data
    let transactionalEntityManager = transaction ? transaction : await dbConnections().main.manager
    let query = getCompiledQuery('check-active', {})
    const queryBuilder = await transactionalEntityManager.createQueryBuilder().from(`(${query})`, 'a1').setParameters({ entity_uuid })
    let checkActive = await queryBuilder.select('*').execute()
    if (checkActive.length == 0) return false
    return true
  } catch (error) {
    return -1
  }
}

const createTask = async (options, token) => {
  try {
    let { entity_uuid, entity_type_id = ANIMAL_TYPE_CALENDAR, activity_id, activity_date, ai_form_heat_date_and_time, get_preview, activity_date_gmt, for_specific_cattle, staff_id, care_calendar_id: task_id, follow_up_required, payment_details } = options

    const { user_id, user_type } = token

    if (for_specific_cattle && for_specific_cattle === NO) {
      entity_type_id = FARMER_TYPE_CALENDAR
    }

    const createdCareCalendarId = await dbConnections().main.manager.transaction(async (transactionalEntityManager) => {
      
      let ticketNumber = undefined
      let upsertData = {}

      if (task_id && follow_up_required) {
        const taskDetails = await taskRepository.getTaskDetails(transactionalEntityManager, task_id)
        entity_uuid = taskDetails[task_id].primary.entity_uuid
        entity_type_id = taskDetails[task_id].primary.entity_type_id
        activity_id = taskDetails[task_id].primary.activity_id
        const ticketNumberRow = taskDetails[task_id].classification.find((item) => item['classifier_id'] === classifiers.COMPLAINT_NUMBER)
        ticketNumber = ticketNumberRow?.value_string_256
        upsertData['linked_care_calendar_task'] = task_id
      }

      /*
      Checks if an animal/customer is active before creating the task
    */
      const templateParameters = {
        entity_type_id
      }
      let query = getCompiledQuery('check-active', templateParameters);
      const queryBuilder = await transactionalEntityManager.createQueryBuilder().from(`(${query})`, 'check-active-entity').setParameters({ entity_uuid })
      let checkActive = await queryBuilder.select('*').execute()
      if (checkActive.length == 0) {
        throw new Error('Inactive Cattle/Customer')
      }

      // create a new task entry
      const createdCareCalendarRowId = await taskRepository.createTask(transactionalEntityManager, {
        entity_uuid,
        entity_type_id,
        activity_id,
        activity_date,
        calendar_activity_status: CONST_STATUS_ACTIVITY_PENDING,
        visit_schedule_time: activity_date_gmt,
        visit_creation_time: new Date().toISOString(),
        activity_created_by: user_id,
      })

      // generate a new ticket number
      if (!ticketNumber) {
        ticketNumber = await ActivityHelper.generateTicketNumber(transactionalEntityManager)
      }

      upsertData['ticket_1'] = ticketNumber
      upsertData['ai_form_heat_date_and_time'] = ai_form_heat_date_and_time

      // save classification data of the task
      let dataKeyList = Object.keys(options)
      const classificationConfig = getClassificationConfiguration()['CARE_CALENDAR_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
      for (let datakeyIndex = 0; datakeyIndex < dataKeyList.length; datakeyIndex++) {
        let key = dataKeyList[datakeyIndex]
        if (classificationConfig[key]) {
          upsertData[key] = options[key]
        }
      }

      await saveClassificationData(transactionalEntityManager, 'CARE_CALENDAR_CLASSIFICATION', createdCareCalendarRowId, upsertData)

      if (payment_details) {
        await updateTaskToOTPFlow(createdCareCalendarRowId)
      }

      return createdCareCalendarRowId
    })

    const response = { care_calendar_id: createdCareCalendarId }

    await dbConnections().main.manager.transaction(async (transactionalEntityManager) => {
     // Handle AI Creation Flow
     if (activity_id == CONST_ACTIVITY_ID_AIP) {

      // cancel previously generated PD tasks for this animal
      await cancelPreviousPDTask(entity_uuid, transactionalEntityManager)

      //create new transaction for follow up
      const followUpObj = {
        oldStatus: CONST_STATUS_ACTIVITY_PENDING - 1,
        care_calendar: createdCareCalendarId,
        activity_id: CONST_ACTIVITY_ID_PD,
        entity_type_id: ANIMAL_TYPE_CALENDAR,
        entity_uuid,
        activity_date: moment(activity_date).add(CONST_PD_DATE_OFFSET, 'days').format('YYYY-MM-DD'),
      }
      await generateFollowUp(followUpObj, transactionalEntityManager)
    }
  })
    if (get_preview === true) {
      response['previewData'] = await getTaskPreview(createdCareCalendarId, activity_id)
    }

    if (staff_id) {
      await taskCreationForPPU(staff_id, createdCareCalendarId)
    }

    return response
  } catch (error) {
    errorLog('error in createTask', options, { message: error.message })
    throw error;
  }
}

const taskCreationForPPU = async (staff_id, care_calendar_id) => {
  try {
    const centralizedVet = await dbConnections().main.repos['staff'].find({
      where: { staff_type_id: CENTRALIZED_VET, active: ACTIVE },
      select: { staff_id: true },
    })
    let data = await dbConnections().main.manager.transaction(async (transaction) => {
      let entityRelationforPPU = {
        entity_relationship_type_id: PPU_PARAVET_TO_TASK,
        entity_1_type_id: CONST_STAFF,
        entity_1_entity_uuid: staff_id,
        entity_2_type_id: TASK,
        entity_2_entity_uuid: care_calendar_id,
      }

      await transaction.getRepository('entity_relationship').insert(entityRelationforPPU)
      for (let i = 0; i < centralizedVet.length; i++) {
        let entityRelationforCentralVet = {
          entity_relationship_type_id: CENTRALIZED_VET_TO_TASK,
          entity_1_type_id: CONST_STAFF,
          entity_2_type_id: TASK,
          entity_2_entity_uuid: care_calendar_id,
          entity_1_entity_uuid: centralizedVet[i].staff_id,
        }
        await transaction.getRepository('entity_relationship').insert(entityRelationforCentralVet)
      }
      return 'success'
    })
  } catch (errror) {
    infoLog(errror)
  }
}

// Getting used for task created from farmer's account: eg. data collection
const updateFarmerForm = async (options = {}, token) => {
  try {
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0

    const { user_id, user_type } = token
    const { calendar_id } = options
    // if (!uuid.validate(calendar_id)) throw new Error("invalid uuid");
    if (!validateUUID(calendar_id)) throw new Error('invalid uuid')
    // if (!uuid.validate(user_id)) throw new Error("invalid uuid");
    if (!validateUUID(user_id)) throw new Error('invalid uuid')
    if (user_type !== **********) throw new Error('not authorized')
    let formData = await getFormCalendar({ user_id, care_calendar_id: calendar_id })
    if (formData.length == 0) throw new Error('not a valid form')
    formData = formData[0]
    let classificationData = {}
    const calendarClassifiersMap = getClassificationConfiguration()['CARE_CALENDAR_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
    for (const key of Object.keys(calendarClassifiersMap)) {
      if (options[key]) {
        classificationData[key] = options[key]
      }
    }
    await dbConnections().main.manager.transaction(async (transactionalEntityManager) => {
      const valueToBeUpdated = { calendar_activity_status: 1000300050 }
      if (!isPostgres) {
        valueToBeUpdated['data_sync_status'] = 1000101003
      }

      await transactionalEntityManager.createQueryBuilder().update(RELATION_CALENDAR).set(valueToBeUpdated).where(`${RELATION_CALENDAR}.care_calendar_id = :calendar_id`, { calendar_id }).execute()

      if (Object.keys(classificationData).length > 0 && calendar_id) {
        await saveClassificationData(transactionalEntityManager, 'CARE_CALENDAR_CLASSIFICATION', calendar_id, classificationData)
      }
      return true
    })
  } catch (error) {
    errorLog('error in updateFarmerForm', options, { message: error.message })
    return -1
  }
}

/**
 *
 * @param {String|uuid} care_calendar Required (Care-calendar id)
 * @param {Object} data Required (Update body)
 * @param {Object} token Required (Resolved token)
 * @returns {null|-1}
 */
const updateActivity = async (care_calendar, data, token) => {
  try {
    const { follow_up_required, completion_date, get_preview, follow_up_notes, completion_date_gmt, vet_availablity_while_closing } = data
    const { user_id, user_type } = token
    const validStatus = new Set([COMPLETED_ACTIVITY_STATUS, CANCELLED_ACTIVITY_STATUS, ABANDONED_ACTIVITY_STATUS])

    if (data.calendar_activity_status && !validStatus.has(parseInt(data.calendar_activity_status))) throw new Error(`cannot set calendar status to ${data.calendar_activity_status}`)
    if (data.calendar_activity_status == COMPLETED_ACTIVITY_STATUS && !data.completion_date) throw new Error('completion date is requied for setting task as complete')
    if (data.calendar_activity_status != COMPLETED_ACTIVITY_STATUS) delete data['completion_date']
    const currentDate = new Date()
    const formattedDateTimeInGMT = currentDate.toISOString()

    let calendarData = await dbConnections().main.repos[RELATION_CALENDAR].find({
      where: [{ care_calendar_id: care_calendar }],
      select: { activity_id: true, calendar_activity_status: true, entity_type_id: true, entity_uuid: true },
    })
    if (calendarData.length === 0) throw new Error('Not found')
    const { activity_id, entity_type_id, entity_uuid } = calendarData[0]
    let oldStatus = parseInt(calendarData[0].calendar_activity_status)

    let responsePayload = { result: true }

    if (frozenStatuses.has(data.calendar_activity_status)) {
      data['system_task_completion_time'] = formattedDateTimeInGMT
    }
    if (data.calendar_activity_status == COMPLETED_ACTIVITY_STATUS) {
      data['completion_date'] = completion_date.split('T')[0]
      data['user_task_completion_time'] = completion_date_gmt
    }
    data['activity_completed_by'] = user_id

    await dbConnections().main.manager.transaction(async (transaction) => {
      if (!frozenStatuses.has(oldStatus)) {
        await CareCalendarQueries.updateEntryStatus(care_calendar, data, token, transaction)

        //insert to care calendar classification
        // if (data.calendar_activity_status == COMPLETED_ACTIVITY_STATUS && vet_availablity_while_closing){
        //     let upsertData = { vet_availablity_while_closing: vet_availablity_while_closing };
        //     await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', care_calendar, upsertData);
        // }

        // generating animal level task when status is completed
        if (data.calendar_activity_status == COMPLETED_ACTIVITY_STATUS && farmerLevelTask.has(activity_id) && data.animal_data) {
          await propagateTaskToAnimal({ completion_date, user_task_completion_time: completion_date_gmt, user_id, user_type, transaction, animal_data: data.animal_data, farmerTaskId: care_calendar, activity_id })
        }

        if (activity_id === CONST_ACTIVITY_ID_AIP && (data.calendar_activity_status == CANCELLED_ACTIVITY_STATUS || data.calendar_activity_status == ABANDONED_ACTIVITY_STATUS)) {
          await propagateActivityStatus(care_calendar, data.calendar_activity_status, transaction)
        }

        if (activity_id === CONST_ACTIVITY_ID_AIP && data.ai_task_completion_status == NO) {
          await propagateActivityStatus(care_calendar, CANCELLED_ACTIVITY_STATUS, transaction)
        }
      }
      if (follow_up_required === true) {
        const followUpTask = await transaction.getRepository('care_calendar_classification').find({
          where: {
            care_calendar_id: care_calendar,
            classifier_id: 2000000186,
            active: ACTIVE,
          },
          select: {
            value_reference_uuid: true,
          },
        })
        let activity_date = data.activity_date.split('T')[0]
        let visit_schedule_time = data.activity_date_gmt
        const calendarIdArrays = followUpTask.map((elem) => elem.value_reference_uuid)
        if (calendarIdArrays && calendarIdArrays.length) {
          let updateActivity = { activity_date: activity_date, care_calendar_id: calendarIdArrays[0], visit_schedule_time: visit_schedule_time }
          updateActivity = preProcessRecords(dbConnections().main.entities['care_calendar'], updateActivity, null)
          await transaction.getRepository('care_calendar').update({ care_calendar_id: calendarIdArrays[0] }, updateActivity)
        } else {
          const followUpObj = { oldStatus, care_calendar, activity_id, entity_type_id, entity_uuid, activity_date, visit_schedule_time, visit_creation_time: formattedDateTimeInGMT, activity_created_by: user_id }

          const followUpTaskId = await generateFollowUp(followUpObj, transaction)
          let care_calendar_data = {
            care_calendar_id: care_calendar,
            classifier_id: 2000000186,
            value_reference_uuid: followUpTaskId,
          }
          care_calendar_data = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], care_calendar_data, null)

          // call freelance helper function to create an entry to entity relationship if applicable
          await handleFreeLanceFollowUp(care_calendar, followUpTaskId, transaction)
          await saveActivityClassification(care_calendar, followUpTaskId, transaction)

          let payment_details = await loadClassificationData('CARE_CALENDAR_CLASSIFICATION', care_calendar, ['payment_details'])
          if (payment_details) {
            await updateTaskToOTPFlow(followUpTaskId)
          }

          if (get_preview === true) {
            responsePayload['previewData'] = await getTaskPreview(followUpTaskId, activity_id, transaction)
            responsePayload['care_calendar_id'] = followUpTaskId
          }
        }
      }
      if (data.calendar_activity_status == COMPLETED_ACTIVITY_STATUS && !frozenStatuses.has(oldStatus)) {
        await updateCompleteTransactionLedger({ care_calendar, user_id }, transaction)
        //transaction ledger update to CONFIRMED
      } else if (!frozenStatuses.has(oldStatus) && cancelledAbandonedStatus.has(data.calendar_activity_status)) {
        await updateCancelledTransactionLedger({ care_calendar, user_id }, transaction)
      }
    })
    return responsePayload
  } catch (error) {
    errorLog('error in updateActivity', data, { message: error.message })
    return -1
  }
}

const updateCancelledTransactionLedger = async (data, transaction) => {
  //transaction ledger update to CANCELLED
  try {
    const { care_calendar } = data
    return await transaction.getRepository('transaction_ledger').update(
      {
        entity_uuid: care_calendar,
        active: ACTIVE,
      },
      {
        transaction_status: TRANSACTION_CANCELLED,
      }
    )
  } catch (error) {
    return -1
  }
}

const updateCompleteTransactionLedger = async (data, transaction) => {
  //transaction ledger update to CONFIRMED
  try {
    const { care_calendar } = data
    return await transaction.getRepository('transaction_ledger').update(
      {
        entity_uuid: care_calendar,
        active: ACTIVE,
      },
      {
        transaction_status: 1000880001,
      }
    )
  } catch (error) {
    return -1
  }
}

/**
 * @param {String|uuid} careCalendarId
 * @returns {Array}
 */
const getLinkedActivity = async (careCalendarId) => {
  try {
    let linkedTask = await loadClassificationData('CARE_CALENDAR_CLASSIFICATION', careCalendarId, ['linked_care_calendar_task'])
    let parentId = careCalendarId
    if (linkedTask.linked_care_calendar_task) {
      parentId = linkedTask.linked_care_calendar_task
    }
    // pulling all linked tasks
    let baseQuery = q_getLinkedTask(parentId)
    let result = await dbConnections().main.manager.query(baseQuery)
    postProcessRecords(undefined, result, { json_columns: ['activity_name_json', 'activity_status_json'] })
    return result
  } catch (error) {
    errorLog('error in getLinkedActivity', { careCalendarId }, { message: error })
    return -1
  }
}

const getRoutePlan = async (data, user_id, user_type) => {
  try {
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0

    if (!user_id || !user_type) throw new Error('user_id and user_type are required')

    const notUsersForvill = new Set([CONST_ID_TYPE_STAFF_BACKOFFICE, CONST_ID_TYPE_STAFF_PARAVET])
    if (data.f_farmer_name) {
      data.f_farmer_name = `%${data.f_farmer_name.toUpperCase()}%`
    }
    const queryParameters = { ...data, user_id, user_type }
    const templateParameters = {
      ...data,
      user_id,
      user_type,
      notUsersForvill,
      REPRODUCTIVE,
      CURATIVE,
      TICKET_CLASSIFIER,
      FARMER_TO_ANIMAL,
      FARMER_TYPE_CALENDAR,
      VILLAGE_CLASSIFIER,
      VILLAGE_TYPE_ID,
      PARAVET,
      ANIMAL_NAME_CLASSIFIER,
      ANIMAL_TYPE_CLASSIFIER,
      ANIMAL_EAR_TAG_CLASSIFIER,
      ANIMAL_TYPE_CALENDAR,
      COMPLETED_ACTIVITY_STATUS,
      CANCELLED_ACTIVITY_STATUS,
      ABANDONED_ACTIVITY_STATUS,
      ACTIVE,
      IS_POSTGRES: isPostgres,
    }

    let query = getCompiledQuery('route-plan', templateParameters)
    const queryBuilder = await dbConnections().main.manager.createQueryBuilder().from(`(${query})`, 't1').setParameters(queryParameters)
    let count = await queryBuilder.select('COUNT(*) as count').execute()
    count = parseInt(count[0].count)
    let total_page = 1
    if (!isNaN(data.page_number) && !isNaN(data.page_limit)) {
      let offset = data.page_limit * (data.page_number - 1)
      if (offset < 0) return { message: 'Invalid page number' }
      queryBuilder.skip(offset).take(data.page_limit)
      total_page = parseInt(count / data.page_limit)
      total_page += count % data.page_limit === 0 ? 0 : 1
    }

    const routePlan = await queryBuilder.select('*').execute()
    postProcessRecords(undefined, routePlan, { json_columns: ['customer_name', 'fcca_name_json', 'fcca_status_json', 'fncca_name_json', 'fncca_status_json', 'taluk_name', 'village_name'] })
    return { routePlan }
  } catch (error) {
    errorLog('error in getRoutePlan', { ...data, user_type, user_id }, { message: error })
    return -1
  }
}

const getRouteDetails = async (data, user_id, user_type) => {
  try {
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
    const queryParameters = { ...data, user_id, user_type }

    // Route Date is Mandatory
    if (!data.route_date) throw new Error('route date is mandatory input')

    // At least one of customer id or animal id is expected
    if (!data.customer_id && !data.animal_id) throw new Error('At least one of customer id or animal id is expected')

    const templateParameters = {
      ...data,
      hide_closed_tickets: data['hide_closed_tickets'] || false,
      IS_POSTGRES: isPostgres,
    }

    let query = getCompiledQuery('route-details', templateParameters)
    const queryBuilder = await dbConnections().main.manager.createQueryBuilder().from(`(${query})`, 'route-details').setParameters(queryParameters)
    const responseData = await queryBuilder.select('*').execute()

    postProcessRecords(undefined, responseData, { json_columns: ['customer_name', 'village_name','activity_status_l10n','activity_name_l10n','animal_type_l10n'] })
    return { data: responseData }
  } catch (error) {
    errorLog('error in getRouteDetails', { ...data, user_type, user_id }, { message: error })
    return -1
  }
}

/**
 *
 * @param {String} [options.user_id] Required
 * @param {String|Number} [options.user_type] Required
 * @param {String|Date} [options.f_date_from] Optional(Filter)
 * @param {String|Date} [options.f_date_to] Optional(Filter)
 * @param {Array|Number|String} [options.f_activity_category] Optional(Filter)
 * @param {Array|Number|String} [options.f_activity_status] Optional(Filter)
 * @param {String} [options.f_activity_name] Optional(Filter)
 * @param {String} [options.f_complaint_number] Optional(Filter)
 * @param {String} [options.f_ear_tag] Optional(Filter)
 * @param {String} [options.f_farmer_name] Optional(Filter)
 * @param {String} [options.f_farmer_mobile] Optional(Filter)
 * @param {String} [options.f_village] Optional(Filter)
 * @param {String|Number} [options.page_number] Optional(Filter)
 * @param {String|Number} [options.page_limit] Optional(Filter)
 * @returns {Array|-1}
 */
const getTaskList = async (options = {}) => {
  try {
    if (!options.user_id || !options.user_type) throw new Error('user_id and user_type are required')

    const baseQuery = q_taskListing(options.user_type, options.user_id)
    let queryBuilder = dbConnections().main.manager.createQueryBuilder().select('*').from(`(${baseQuery})`, 'task_list')
    if (options.user_type == **********) {
      options.farmer_id = options.user_id
      queryBuilder.andWhere('activity_id != 1000700002')
    } else if (options.user_type == 1000230002) {
    } else {
      queryBuilder.andWhere('activity_id != 1000700001')
    }
    if (options.f_date_from) {
      queryBuilder.andWhere('activity_date >= :f_date_from', { f_date_from: options.f_date_from })
    }
    if (options.f_date_to) {
      queryBuilder.andWhere('activity_date <= :f_date_to', { f_date_to: options.f_date_to })
    }
    if(options.f_completion_date_from){
      queryBuilder.andWhere('completion_date >= :f_completion_date_from', { f_completion_date_from: options.f_completion_date_from })
    }
    if (options.f_completion_date_to) {
      queryBuilder.andWhere('completion_date <= :f_completion_date_to', { f_completion_date_to: options.f_completion_date_to })
    }
    if (options.f_activity_category) {
      options.f_activity_category = Array.isArray(options.f_activity_category) ? options.f_activity_category : [options.f_activity_category]
      queryBuilder.andWhere('activity_category_id IN(:...f_activity_category)', { f_activity_category: options.f_activity_category })
    }
    if (options.f_activity_status) {
      options.f_activity_status = Array.isArray(options.f_activity_status) ? options.f_activity_status : [options.f_activity_status]
      queryBuilder.andWhere('calendar_activity_status IN(:...f_activity_status)', { f_activity_status: options.f_activity_status })
    }
    if (options.f_activity_name) {
      options.f_activity_name = options.f_activity_name.toUpperCase()
      queryBuilder.andWhere('UPPER(activity_name) LIKE :f_activity_name', { f_activity_name: `%${options.f_activity_name}%` })
    }
    if (options.f_complaint_number) {
      options.f_complaint_number = options.f_complaint_number.toUpperCase()
      queryBuilder.andWhere('UPPER(complaint_number) LIKE :f_complaint_number', { f_complaint_number: `%${options.f_complaint_number}%` })
    }
    if (options.f_ear_tag) {
      options.f_ear_tag = options.f_ear_tag.toUpperCase()
      queryBuilder.andWhere('UPPER(animal_ear_tag) LIKE :f_ear_tag', { f_ear_tag: `%${options.f_ear_tag}%` })
    }
    if (options.f_farmer_name) {
      options.f_farmer_name = options.f_farmer_name.toUpperCase()
      queryBuilder.andWhere('UPPER(customer_name) LIKE :f_farmer_name', { f_farmer_name: `%${options.f_farmer_name}%` })
    }
    if (options.f_farmer_mobile) {
      queryBuilder.andWhere('mobile_number LIKE :f_farmer_mobile', { f_farmer_mobile: `%${options.f_farmer_mobile}%` })
    }
    if (options.f_village) {
      options.f_village = options.f_village.toUpperCase()
      queryBuilder.andWhere('UPPER(village_name) LIKE :f_village', { f_village: `%${options.f_village}%` })
    }
    if (options.f_paravet_name) {
      options.f_paravet_name = options.f_paravet_name.toUpperCase()
      queryBuilder.andWhere('UPPER(paravet_name) LIKE :f_paravet_name', { f_paravet_name: `%${options.f_paravet_name}%` })
    }
    const count = parseInt((await queryBuilder.select('COUNT(*) as count').execute())[0].count)
    queryBuilder.select('*')
    let total_page = 1
    options.page_number = parseInt(options.page_number)
    options.page_limit = parseInt(options.page_limit)
    if (!isNaN(options.page_number) && !isNaN(options.page_limit)) {
      if (options.page_number === 0) throw new Error('Invalid page number')
      let offset = options.page_limit * (options.page_number - 1)
      queryBuilder.skip(offset).take(options.page_limit)
      total_page = parseInt(count / options.page_limit)
      total_page += count % options.page_limit === 0 ? 0 : 1
      if (total_page === 0) return { result: [], total_page: 0 }
      if (options.page_number > total_page) throw new Error('page number out of bound')
    }
    let result = await queryBuilder.execute()
    return { result, total_page }
  } catch (error) {
    errorLog('error in getTaskList', options, { message: error })
    return -1
  }
}
/**
 * @param {*} options
 * @param {transactionalEntityManager} [options.transaction]
 * @param {Array} [options.animal_ids]
 * @param {String} [options.farmerTaskId]
 * @param {Number} [options.activity_id]
 * @returns {Boolean}
 */
const propagateTaskToAnimal = async (options = {}) => {
  try {
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
    const { completion_date } = options
    const currentDate = new Date()
    const formattedDateTimeInGMT = currentDate.toISOString()
    const { transaction } = options

    if (!options.transaction) throw new Error('transaction Object is required in options')
    if (!validateUUID(options.farmerTaskId)) throw new Error('farmerTaskId is required')
    if (!completion_date) throw new Error('Completion date is required for propagated tasks')

    // collect all the classifiers from farm level task care caelndar id
    let clssificationUpsertObj = { linked_care_calendar_task: options.farmerTaskId }
    const calendarClasssificationConf = getClassificationConfiguration()['CARE_CALENDAR_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
    const calendarClasssificationConfAttrArray = Object.keys(calendarClasssificationConf)

    const farmerCalendarClassifications = await loadClassificationDataWithClassifierIds(transaction, 'CARE_CALENDAR_CLASSIFICATION', options.farmerTaskId, calendarClasssificationConfAttrArray)
    const farmerCalendarClassificationValues = farmerCalendarClassifications['data']

    for (let key of Object.keys(farmerCalendarClassificationValues)) {
      clssificationUpsertObj[key] = farmerCalendarClassificationValues[key]
    }
    // Handle Farm Tick Control Spraying Activity
    if (options.activity_id == CONST_ACTIVITY_ID_FARM_TICK_CONTROL_SPRAYING) {
      options.animal_data = Array.isArray(options.animal_data) ? options.animal_data : [options.animal_data]

      options.animal_ids = options.animal_data.map((value) => value.id)

      for (let medClassificationIndex = 0; medClassificationIndex < medicineClassifications.length; medClassificationIndex++) {
        let medQtKey = medicineClassifications[medClassificationIndex][1]
        if (clssificationUpsertObj[medQtKey]) {
          clssificationUpsertObj[medQtKey] = isNaN(clssificationUpsertObj[medQtKey]) ? 0 : parseFloat(clssificationUpsertObj[medQtKey]) / options.animal_ids.length
        }
      }

      for (let animalIdIndex = 0; animalIdIndex < options.animal_ids.length; animalIdIndex++) {
        let animalId = options.animal_ids[animalIdIndex]
        if (!validateUUID(animalId)) throw new Error('Animal Id must be UUID')

        let calendarEntry = {
          activity_date: options.original_activity_date || new Date(),
          completion_date: options.completion_date,
          entity_uuid: animalId,
          entity_type_id: 1000460002,
          activity_id: CONST_ACTIVITY_ID_TICK_CONTROL,
          calendar_activity_status: COMPLETED_ACTIVITY_STATUS,
          visit_schedule_time: formattedDateTimeInGMT,
          visit_creation_time: formattedDateTimeInGMT,
          activity_created_by: options.user_id,
          activity_completed_by: options.user_id,
          system_task_completion_time: formattedDateTimeInGMT,
          user_task_completion_time: options.user_task_completion_time,
        }

        let calendarId = await CareCalendarQueries.insertcalendarEntries(calendarEntry, { datasource: transaction })
        calendarId = calendarId.identifiers[0].care_calendar_id
        await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', calendarId, clssificationUpsertObj)
      }
    }

    // Handle Farm Level CMT Activity
    if (options.activity_id == CONST_ACTIVITY_ID_CMT) {
      options.animal_data = Array.isArray(options.animal_data) ? options.animal_data : [options.animal_data]

      for (let animalIndex = 0; animalIndex < options.animal_data.length; animalIndex++) {
        let animal = options.animal_data[animalIndex]

        if (!validateUUID(animal.id)) throw new Error('Animal Id must be UUID')

        let calendarEntry = {
          activity_date: options.original_activity_date || new Date(),
          completion_date: options.completion_date,
          entity_uuid: animal.id,
          entity_type_id: 1000460002,
          activity_id: CONST_ACTIVITY_ID_CMT,
          calendar_activity_status: COMPLETED_ACTIVITY_STATUS,
          visit_schedule_time: formattedDateTimeInGMT,
          visit_creation_time: formattedDateTimeInGMT,
          activity_created_by: options.user_id,
          activity_completed_by: options.user_id,
          system_task_completion_time: formattedDateTimeInGMT,
          user_task_completion_time: options.user_task_completion_time,
        }

        let calendarId = await CareCalendarQueries.insertcalendarEntries(calendarEntry, { datasource: transaction })
        calendarId = calendarId.identifiers[0].care_calendar_id
        let newClassifierObj = {}
        for (let key of Object.keys(animal)) {
          if (calendarClasssificationConf.hasOwnProperty(key)) {
            newClassifierObj[key] = animal[key]
          }
        }
        await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', calendarId, { ...clssificationUpsertObj, ...newClassifierObj })

        if (animal.notes) {
          let notes_row = {
            note_type_id: 1000440001,
            note: { ul: animal.notes },
            entity_1_type_id: TASK,
            entity_1_uuid: calendarId,
            creator_type_id: options.user_type,
            creator_uuid: options.user_id,
            last_modifying_user_id: options.user_id,
            note_time: moment.utc().format('YYYY-MM-DD HH:mm:ss.SSS'),
          }
          notes_row = preProcessRecords(dbConnections().main.entities['note'], notes_row, { json_columns: ['note'] })
          await transaction.getRepository('note').insert(notes_row)
        }
      }
    }
    return true
  } catch (error) {
    errorLog('error in propagateTaskToAnimal', options, { message: error.message })
    return -1
  }
}

/**
 * Generates a single follow-up
 * @param {number|string} [data.oldStatus]
 * @param {string|uuid} [data.care_calendar]
 * @param {number|string} [data.activity_id]
 * @param {number|string} [data.entity_type_id]
 * @param {string|uuid} [data.entity_uuid]
 * @param {Date|moment|string} [data.activity_date]
 * @param {datasource} transaction
 */
const generateFollowUp = async (data, transaction) => {
  const { oldStatus, care_calendar, activity_id, entity_type_id, entity_uuid, activity_date, visit_schedule_time, visit_creation_time, activity_created_by } = data

  if (!activity_date) throw new Error('activity_date for follow-up is undefined')

  const newCalendarActivityStatus = oldStatus == CONST_STATUS_ACTIVITY_COMPLETED ? CONST_STATUS_ACTIVITY_PENDING : oldStatus + 1
  const transferableClassifierKeys = ['ticket_1', 'linked_care_calendar_task', 'payment_details']
  const classificationData = await loadClassificationData('CARE_CALENDAR_CLASSIFICATION', care_calendar, transferableClassifierKeys)
  const nextFollowupTask = { activity_id, entity_type_id, entity_uuid, calendar_activity_status: newCalendarActivityStatus, activity_date, visit_schedule_time, visit_creation_time, activity_created_by }

  if (!classificationData.linked_care_calendar_task) classificationData.linked_care_calendar_task = care_calendar

  let insertedFollowUpTask = await CareCalendarQueries.insertcalendarEntries(nextFollowupTask, { datasource: transaction })
  let followUpTaskId = insertedFollowUpTask.identifiers[0].care_calendar_id

  await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', followUpTaskId, classificationData)

  let payment_details = await loadClassificationData('CARE_CALENDAR_CLASSIFICATION', care_calendar, ['payment_details'])
  if (payment_details) {
    await updateTaskToOTPFlow(followUpTaskId)
  }

  return followUpTaskId
}

const handleFreelanceFinance = async (followup_cc_id, transaction) => {
  try {
    await transaction.getRepository('transaction_ledger').update(
      {
        entity_uuid: followup_cc_id,
        transaction_status: TRANSACTION_BLOCKED,
        active: ACTIVE,
      },
      {
        transaction_status: TRANSACTION_CONFIRMED,
      }
    )

    const parentTask = await transaction.getRepository('care_calendar_classification').find({
      where: {
        care_calendar_id: followup_cc_id,
        classifier_id: 2000000126,
        active: ACTIVE,
      },
      select: {
        value_reference_uuid: true,
      },
    })

    if (parentTask && parentTask.length > 0) {
      const parent_cc_id = parentTask[0].value_reference_uuid

      const transactionDetails = await transaction.getRepository('transaction_ledger').find({
        where: {
          entity_uuid: In([parent_cc_id, followup_cc_id]),
          active: ACTIVE,
        },
      })

      const parentTaskDetails = transactionDetails.filter(function (item) {
        return parent_cc_id.indexOf(item.entity_uuid) > -1
      })

      const currentTaskDetails = transactionDetails.filter(function (item) {
        return followup_cc_id.indexOf(item.entity_uuid) > -1
      })

      if (currentTaskDetails.length === 0 && parentTaskDetails.length > 0) {
        let transaction_entry = {
          account_id: parentTaskDetails[0].account_id,
          staff_uuid: parentTaskDetails[0].staff_uuid,
          amount: Math.round(parentTaskDetails[0].amount * 0.5),
          transaction_type: TRANSACTION_DEBIT,
          transaction_category: TRANSACTION_DEBIT,
          transaction_date: new Date().toISOString(),
          transaction_description: parentTaskDetails[0].transaction_description,
          entity_uuid: followup_cc_id,
          entity_type_id: TASK,
          transaction_status: TRANSACTION_CONFIRMED,
        }
        await transaction.getRepository('transaction_ledger').insert(transaction_entry)
      }
    }
  } catch (error) {
    console.log('something went wrong while updating finance for freelancer', error)
  }
}

const handleFreeLanceFollowUp = async (parent_cc_id, followup_cc_id, transaction) => {
  try {
    const parent_cc_id_details = await transaction.getRepository('entity_relationship').find({
      where: {
        entity_2_entity_uuid: parent_cc_id,
        entity_relationship_type_id: PPU_PARAVET_TO_TASK,
        active: ACTIVE,
      },
      select: {
        entity_1_entity_uuid: true,
      },
    })

    if (parent_cc_id_details && parent_cc_id_details.length > 0) {
      const freelance_staff_id = parent_cc_id_details[0].entity_1_entity_uuid

      let entity_relationship_entry = {
        entity_1_entity_uuid: freelance_staff_id,
        entity_2_entity_uuid: followup_cc_id,
        entity_relationship_type_id: PPU_PARAVET_TO_TASK,
      }

      await transaction.getRepository('entity_relationship').insert(entity_relationship_entry)
    }
  } catch (error) {
    console.log('something went wrong while generating followup for freelancer', error)
  }
}

const cancelPreviousPDTask = async (animal_id, transactionManager) => {
  const careCalendarRepository = transactionManager.getRepository(RELATION_CALENDAR)
  const selectAttributes = ['care_calendar_id']
  const previousPDTask = await careCalendarRepository
    .createQueryBuilder('pd_task')
    .where('entity_uuid = :entity_uuid', { entity_uuid: animal_id })
    .andWhere('activity_id = :activity_id', { activity_id: CONST_ACTIVITY_ID_PD })
    .andWhere('calendar_activity_status NOT IN (:completed, :cancelled, :abandoned)', {
      completed: COMPLETED_ACTIVITY_STATUS,
      cancelled: CANCELLED_ACTIVITY_STATUS,
      abandoned: ABANDONED_ACTIVITY_STATUS,
    })
    .getMany()

  if (previousPDTask && previousPDTask.length > 0) {
    infoLog('Previous open PD tasks ', 'no-params', previousPDTask)
    previousPDTask.map((item) => (item.calendar_activity_status = CANCELLED_ACTIVITY_STATUS))
    careCalendarRepository.save(previousPDTask)
  } else {
    infoLog('No Previous open PD tasks found')
  }
}

const propagateActivityStatus = async (parent_id, status, transaction) => {
  try {
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
    const queryOptions = { parent_id, status, ACTIVE, LINKED_CARE_CALENDAR_CLASSIFIER, IS_POSTGRES: isPostgres }
    const query = getCompiledQuery('propagte-task-status', queryOptions)
    await transaction.query(query)
  } catch (error) {
    errorLog('error at propagateActivityStatus', { parent_id, status, transaction }, { message: error })
    throw error
  }
}

const getTasks = async (data, token, options = {}) => {
  try {
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
    const validQueryType = new Set(['farmer', 'animal', 'id'])
    const { from, to } = data
    const USER_BACK_OFFICE = CONST_ID_TYPE_STAFF_BACKOFFICE
    const USER_FARMER = CONST_ID_TYPE_CUSTOMER_FARMER
    const { user_id, user_type } = token
    const current_date = moment().format('YYYY-MM-DD')
    const fromDate = moment(from)
    const toDate = moment(to)
    const fromDif = fromDate.diff(current_date, 'days')
    const toDif = toDate.diff(current_date, 'days')

    if (!token || !user_id || !user_type) {
      throw new Error('Invalid token object(unresolved or null)')
    }

    if (!data) {
      throw new Error('invalid request data')
    }
    
    if ((user_type !== USER_FARMER || user_type !== PPU_FARMER) && (!data.farmer_id || !validateUUID(data.farmer_id)) && options.querType === 'farmer') {
      // farmer ID is always required
      throw new Error('A valid farmer_id is required')
    }
    
    if (!data) {
      throw new Error('invalid request data')
    }
    
    if (!validQueryType.has(options.querType)) {
      throw new Error('Missing/Invalid querType in options')
    }
    
    if (options.querType === 'id' && !validateUUID(data.task_id)) {
      throw new Error('valid task id(uuid) is required')
    }
    
    if (options.querType === 'animal' && !validateUUID(data.animal_id)) {
      throw new Error('valid animal_id are required')
    }
    
    if (options.querType === 'farmer' && !validateUUID(data.farmer_id) && user_type !== USER_FARMER) {
      throw new Error('valid farmer id(uuid) is required')
    }

    if (fromDif > 365 || fromDif < -365) {
      throw new Error('From date must be within one year from now')
    }
    if (toDif > 365 || toDif < -365) {
      throw new Error('To date must be within one year from now')
    }

    const templateParams = {
      // valid village filter is disabled for backoffice person and farmer
      validVillage: user_type == USER_BACK_OFFICE || user_type == USER_FARMER ? 0 : 1,
      thumbnail: 0 | data.thumbnail,
      user_type,
      IS_POSTGRES: isPostgres,
    }
    const queryParams = {
      user_id,
      user_type,
      from,
      to,
    }

    if (user_type == USER_FARMER) {
      queryParams.farmer_id = user_id
      templateParams.farmerFilter = 1
      templateParams.dashboard = data.dashboard | 0
      templateParams.from = data.from
      templateParams.to = data.to
    }

    if (options.querType === 'id') {
      queryParams.care_calendar_id = data.task_id
      templateParams.f_task_id = 1
      templateParams.detailed = 1
    } else if (options.querType === 'animal') {
      templateParams.dashboard = data.dashboard | 0
      templateParams.animalFilter = 1
      templateParams.from = data.from
      templateParams.to = data.to
      queryParams.animal_id = data.animal_id
    } else if (options.querType === 'farmer' && user_type !== CONST_ID_TYPE_CUSTOMER_FARMER) {
      templateParams.farmerFilter = 1
      templateParams.dashboard = data.dashboard | 0
      queryParams.farmer_id = data.farmer_id
      templateParams.from = data.from
      templateParams.to = data.to
    }
    const taskQuery = getCompiledQuery('task', templateParams)
    const taskQueryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${taskQuery})`).setParameters(queryParams)
    const result = await taskQueryBuilder.execute()
    postProcessRecords(undefined, result, { json_columns: ['village_name_l10n', 'taluk_name', 'district_name', 'state_name', 'animal_type_json', 'calendar_activity_status_name_json', 'activity_name_l10n', 'paravet_name', 'customer_name'] })
    return result
  } catch (error) {
    errorLog('error at getTasks', { data, token, options }, { message: error })
    throw error
  }
}
const getTaskPreview = async (calendar_id, activity_id, transaction) => {
  const manager = transaction ? transaction : dbConnections().main.manager
  //const queryName = farmerLevelTask.has(activity_id) ? "farm-task-preview" : "cattle-task-preview";
  const previewQuery = getCompiledQuery('task-preview')
  const previewuQeryBuilder = manager.createQueryBuilder().from(`(${previewQuery})`)
  previewuQeryBuilder.setParameters({ care_calendar_id: calendar_id })
  return await previewuQeryBuilder.execute()
}
// Activity observation/med APIs
const observationKeysToColMaps = {
  weight_1: {
    cols: [{ client_key: 'value', db_key: 'value_double' }],
    value_reference_id: 1007000001,
    key: 'weight_1',
  },
  temperature_1: {
    cols: [{ client_key: 'value', db_key: 'value_double' }],
    value_reference_id: 1007000002,
    key: 'temperature_1',
  },
}

const refIdstoColMaps = {}
for (const value of Object.values(observationKeysToColMaps)) {
  refIdstoColMaps[value.value_reference_id] = value
}

const observationCategory = 10070000
const observationClassifierId = 2000000182

const getActivityObservations = async (data, params) => {
  const { activity_id } = data

  let observationOptions = await dbConnections().main.repos['ref_reference'].find({
    select: { reference_name_l10n: true, reference_name: true, reference_id: true },
    where: { reference_category_id: observationCategory },
  })
  observationOptions = postProcessRecords(undefined, observationOptions, { json_columns: ['reference_name_l10n'] })

  let rawActivityObservation = await dbConnections().main.repos['care_calendar_classification'].find({
    where: { care_calendar_id: activity_id, classifier_id: observationClassifierId, active: ACTIVE },
  })

  let observationNotes = await dbConnections().main.repos['note'].findOne({
    where: { note_type_id: 1000440006, entity_1_type_id: 1000220003, entity_1_uuid: activity_id, active: ACTIVE },
    select: { note: true, creator_uuid: true, creator_type_id: true },
    order: {
      created_at: 'DESC',
    },
  })
  observationNotes = postProcessRecords(undefined, observationNotes, { json_columns: ['note'] })

  rawActivityObservation = postProcessRecords(undefined, rawActivityObservation, { json_columns: ['value_l10n'] })
  // processing db data to pick appropriate set of columns accroding to the config
  const processedActivityObservations = []
  for (const rawData of rawActivityObservation) {
    if (!refIdstoColMaps[rawData.value_reference_id]) continue

    const validCols = refIdstoColMaps[rawData.value_reference_id].cols
    const processedElem = {}
    for (const col of validCols) {
      processedElem[col.client_key] = rawData[col.db_key]
    }
    processedElem.keyRef = refIdstoColMaps[rawData.value_reference_id].value_reference_id
    processedElem.id = refIdstoColMaps[rawData.value_reference_id].key
    processedElem.care_calendar_classification_id = rawData.care_calendar_classification_id
    if (Object.keys(processedElem).length > 0) {
      processedActivityObservations.push(processedElem)
    }
  }
  return { options: observationOptions, obervations: processedActivityObservations, observationNotes: observationNotes }
}

const updateActivityObservations = async (data, params) => {
  const { activity_id } = data
  const { user_id, user_type } = params.headers.token
  return await dbConnections().main.manager.transaction(async (transaction) => {
    try {
      let observationDeactivateData = { active: INACTIVE, last_modifying_user_id: user_id }
      observationDeactivateData = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], observationDeactivateData, null)
      if (observationDeactivateData.care_calendar_classification_id) {
        delete observationDeactivateData.care_calendar_classification_id
      }
      const result = await transaction.getRepository('care_calendar_classification').update(
        {
          care_calendar_id: activity_id,
          classifier_id: observationClassifierId,
          active: ACTIVE,
        },
        observationDeactivateData
      )
      let { observation_notes_1 } = data
      if (typeof observation_notes_1 === 'string' && observation_notes_1.length > 0) {
        let observationNoteDeactivateData = { active: INACTIVE, last_modifying_user_id: user_id }
        observationNoteDeactivateData = preProcessRecords(dbConnections().main.entities['note'], observationNoteDeactivateData, null)
        if (observationNoteDeactivateData.note_id) {
          delete observationNoteDeactivateData.note_id
        }
        let observationNoteDeactivate = await dbConnections().main.repos['note'].update({ note_type_id: 1000440006, entity_1_type_id: 1000220003, entity_1_uuid: activity_id, active: ACTIVE }, observationNoteDeactivateData)
      }
      const { observations } = data
      let tobeInserted = []
      for (const obs of observations) {
        const fieldconf = observationKeysToColMaps[obs.id].cols

        const entry = {
          classifier_id: observationClassifierId,
          care_calendar_id: activity_id,
          value_reference_id: observationKeysToColMaps[obs.id].value_reference_id,
          last_modifying_user_id: user_id,
        }
        for (const col of fieldconf) {
          if (!obs[col.client_key]) throw new Error(`Valid key ${col.client_key} not found`)
          entry[col.db_key] = obs[col.client_key]
        }
        tobeInserted.push(entry)
      }

      //insert observation notes in observation
      if (typeof observation_notes_1 === 'string' && observation_notes_1.length > 0) {
        let notes = {
          note: { ul: observation_notes_1 },
          note_type_id: 1000440006,
          entity_1_type_id: 1000220003,
          entity_1_uuid: activity_id,
          creator_uuid: user_id,
          creator_type_id: user_type,
        }
        notes = preProcessRecords(dbConnections().main.entities['note'], notes, { json_columns: ['note'] })
        await transaction.getRepository('note').insert(notes)
      }

      tobeInserted = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], tobeInserted, { json_columns: ['value_l10n', 'value_json'] })
      await transaction.getRepository('care_calendar_classification').insert(tobeInserted)
      return true
    } catch (error) {
      errorLog('error in updateActivityObservations', { data }, { message: error })
      throw error
    }
  })
}

const diagnosisKeysToColMaps = {
  diagnosis: {
    cols: [{ client_key: 'value', db_key: 'value_reference_id' }],
    classifier_id: 2000000184,
    key: 'diagnosis',
    category_id: 10080000,
  },
  response_to_treatment: {
    cols: [{ client_key: 'value', db_key: 'value_reference_id' }],
    classifier_id: 2000000183,
    key: 'response_to_treatment',
    category_id: 10080000,
  },
}

const getActivityDiagnosis = async (data, params) => {
  try {
    const { activity_id } = data
    let diseaseOptions = await dbConnections().main.repos['ref_diagnosis'].find({
      select: { reference_id: true, reference_name_l10n: true },
      where: { active: ACTIVE },
    })
    diseaseOptions = postProcessRecords(undefined, diseaseOptions, { json_columns: ['reference_name_l10n'] })

    let responseOption = await dbConnections().main.repos['ref_reference'].find({
      select: { reference_id: true, reference_name_l10n: true },
      where: { active: ACTIVE, reference_category_id: diagnosisKeysToColMaps.diagnosis.category_id },
    })
    responseOption = postProcessRecords(undefined, responseOption, { json_columns: ['reference_name_l10n'] })

    let diagnosisData = await dbConnections().main.repos['care_calendar_classification'].find({
      where: { care_calendar_id: activity_id, active: ACTIVE, classifier_id: diagnosisKeysToColMaps.diagnosis.classifier_id },
    })
    diagnosisData = postProcessRecords(undefined, diagnosisData, { json_columns: ['value_l10n', 'value_json'] })
    const processedDiseases = []
    for (const row of diagnosisData) {
      const entry = {}
      for (const col of diagnosisKeysToColMaps.diagnosis.cols) {
        entry[col.client_key] = row[col.db_key]
      }
      entry['care_calendar_classification_id'] = row.care_calendar_classification_id
      processedDiseases.push(entry)
    }

    let resToTreamentData = await dbConnections().main.repos['care_calendar_classification'].find({
      where: { care_calendar_id: activity_id, active: ACTIVE, classifier_id: diagnosisKeysToColMaps.response_to_treatment.classifier_id },
    })
    resToTreamentData = postProcessRecords(undefined, resToTreamentData, { json_columns: ['value_l10n', 'value_json'] })
    const processedRes = []
    for (const row of resToTreamentData) {
      const entry = {}
      for (const col of diagnosisKeysToColMaps.response_to_treatment.cols) {
        entry[col.client_key] = row[col.db_key]
      }
      processedRes.push(entry)
    }

    return {
      selected: { diagnosis: processedDiseases, response_to_treatment: processedRes },
      options: { diagnosis: diseaseOptions, response_to_treatment: responseOption },
    }
  } catch (error) {
    console.log(error)
    errorLog('error in getActivityDiagnosis', { data }, { message: error })
    return -1
  }
}

const updateActivityDiagnosis = async (data, params) => {
  const { activity_id, diagnosis, response_to_treatment } = data
  const { user_id } = params.headers.token
  return await dbConnections().main.manager.transaction(async (transaction) => {
    try {
      let updatePreviousDiagnosis = { active: INACTIVE, last_modifying_user_id: user_id }
      updatePreviousDiagnosis = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], updatePreviousDiagnosis, null)
      if (updatePreviousDiagnosis.care_calendar_classification_id) {
        delete updatePreviousDiagnosis.care_calendar_classification_id
      }
      await transaction.getRepository('care_calendar_classification').update(
        {
          care_calendar_id: activity_id,
          classifier_id: In([diagnosisKeysToColMaps.diagnosis.classifier_id, diagnosisKeysToColMaps.response_to_treatment.classifier_id]),
          active: ACTIVE,
        },
        updatePreviousDiagnosis
      )
      let toBeInserted = []
      for (const elem of diagnosis) {
        const entry = {
          classifier_id: diagnosisKeysToColMaps.diagnosis.classifier_id,
          care_calendar_id: activity_id,
          last_modifying_user_id: user_id,
        }
        for (const col of diagnosisKeysToColMaps.diagnosis.cols) {
          if (elem[col.client_key] === undefined || elem[col.client_key] === null) {
            throw new Error(`Valid key ${col.client_key} not found`)
          }
          entry[col.db_key] = elem[col.client_key]
        }
        toBeInserted.push(entry)
      }

      for (const elem of response_to_treatment) {
        const entry = {
          classifier_id: diagnosisKeysToColMaps.response_to_treatment.classifier_id,
          care_calendar_id: activity_id,
          last_modifying_user_id: user_id,
        }
        for (const col of diagnosisKeysToColMaps.response_to_treatment.cols) {
          console.log('here', `${elem[col.client_key]}`)
          if (elem[col.client_key] === undefined || elem[col.client_key] === null) {
            throw new Error(`Valid key ${col.client_key} not found`)
          }
          entry[col.db_key] = elem[col.client_key]
        }
        toBeInserted.push(entry)
      }
      toBeInserted = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], toBeInserted, { json_columns: ['value_json', 'value_l10n'] })
      await transaction.getRepository('care_calendar_classification').insert(toBeInserted)
    } catch (error) {
      console.log(error)
      errorLog('error in updateActivityDiagnosis', { data }, { message: error })
      throw error
    }
  })
}

const prescriptionKeystoColMaps = {
  medicine: {
    cols: [
      { client_key: 'id', db_key: 'value_reference_id' },
      { client_key: 'dosage', db_key: 'value_double' },
      { client_key: 'prescribed_info', db_key: 'value_json' },
    ],
    classifier_id: 2000000185,
    key: 'medicine',
  },
}
const getActivityPrescription = async (data, params) => {
  try {
    const { activity_id } = data
    let medicineOptions = await dbConnections().main.repos['ref_reference'].find({
      where: { reference_category_id: 10005000, active: ACTIVE },
      select: {
        reference_id: true,
        reference_name_l10n: true,
        reference_information: true,
      },
    })
    medicineOptions = postProcessRecords(undefined, medicineOptions, { json_columns: ['reference_name_l10n', 'reference_information'] })

    let routeOptions = await dbConnections().main.repos['ref_reference'].find({
      where: { reference_category_id: 10090000, active: ACTIVE },
      select: { reference_id: true, reference_name_l10n: true },
    })
    routeOptions = postProcessRecords(undefined, routeOptions, { json_columns: ['reference_name_l10n'] })

    let activityMedicineData = await dbConnections().main.repos['care_calendar_classification'].find({
      where: { care_calendar_id: activity_id, classifier_id: prescriptionKeystoColMaps.medicine.classifier_id, active: ACTIVE },
    })
    activityMedicineData = postProcessRecords(undefined, activityMedicineData, { json_columns: ['value_json', 'value_l10n'] })
    const processedMedData = []
    for (const elem of activityMedicineData) {
      const entry = {}
      for (const col of prescriptionKeystoColMaps.medicine.cols) {
        entry[col.client_key] = elem[col.db_key]
      }
      entry['care_calendar_classification_id'] = elem.care_calendar_classification_id
      processedMedData.push(entry)
    }
    let prescriptionNote = await dbConnections().main.repos['note'].findOne({
      where: { note_type_id: 1000440005, entity_1_type_id: 1000220003, entity_1_uuid: activity_id, active: ACTIVE },
      select: { note: true, creator_uuid: true, creator_type_id: true },
      order: {
        created_at: 'DESC',
      },
    })
    prescriptionNote = postProcessRecords(undefined, prescriptionNote, { json_columns: ['note'] })

    let followUpDate = null
    const followUpActivity = await dbConnections().main.repos['care_calendar_classification'].find({
      where: { active: ACTIVE, care_calendar_id: activity_id, classifier_id: 2000000186 },
      select: { value_reference_uuid: true },
    })
    if (followUpActivity.length > 0) {
      const folloUpTaskId = followUpActivity[0].value_reference_uuid
      const folloupTask = await dbConnections().main.repos['care_calendar'].findOne({
        where: { care_calendar_id: folloUpTaskId },
        select: { activity_date: true, visit_schedule_time: true },
      })
      followUpDate = folloupTask && folloupTask.visit_schedule_time ? folloupTask.visit_schedule_time : folloupTask.activity_date
    }

    let prescriptionPDF = await dbConnections().main.repos['document'].findOne({
      where: [{ entity_1_entity_uuid: activity_id, document_type_id: 1000260015, active: ACTIVE }],
      select: { document_id: true, document_information: true, client_document_information: true, document_type_id: true },
    })
    prescriptionPDF = postProcessRecords(undefined, prescriptionPDF, { json_columns: ['document_information', 'client_document_information'] })

    let vet_cosultant_type = await getConsultantType(activity_id)
    vet_cosultant_type = vet_cosultant_type ? vet_cosultant_type.value_reference_id : null

    return {
      suggestions_advisory_notes: prescriptionNote,
      selected: { medicine: processedMedData },
      follow_up_date: followUpDate,
      options: { medicine: medicineOptions, routes: routeOptions },
      prescriptionPDF,
      vet_cosultant_type: vet_cosultant_type,
    }
  } catch (error) {
    console.log(error)
    errorLog('error in getActivityPrescription', { data }, { message: error })
    return -1
  }
}

const updateActivityPrescription = async (data, params) => {
  console.log('updateActivityPrescription', data)
  let { activity_id, suggestions_advisory_notes, medicine, follow_up_required, follow_up_date, follow_up_date_gmt, vet_cosultant_type } = data
  const { user_id, user_type } = params.headers.token
  if (user_type == CONST_ID_TYPE_STAFF_PARAVET) {
    // if the user type is a paravet, handle it as per the access
    return updateActivityPrescriptionForParavet(data, params)
  }
  if (follow_up_required === undefined) throw new Error('follow up options not found')
  if (follow_up_required === true && !follow_up_date) throw new Error('follow up date is required')

  return await dbConnections().main.manager.transaction(async (transaction) => {
    try {
      const followUpTask = await transaction.getRepository('care_calendar_classification').find({
        where: {
          care_calendar_id: activity_id,
          classifier_id: 2000000186,
          active: ACTIVE,
        },
        select: {
          value_reference_uuid: true,
        },
      })
      if (typeof suggestions_advisory_notes === 'string' && suggestions_advisory_notes.length > 0) {
        let deactivatePrescriptionNotes = { active: INACTIVE, last_modifying_user_id: user_id }
        deactivatePrescriptionNotes = preProcessRecords(dbConnections().main.entities['note'], deactivatePrescriptionNotes, null)
        if (deactivatePrescriptionNotes.note_id) {
          delete deactivatePrescriptionNotes.note_id
        }
        let prescriptionNoteDeactivate = await dbConnections().main.repos['note'].update(
          {
            note_type_id: 1000440005,
            entity_1_type_id: 1000220003,
            entity_1_uuid: activity_id,
            active: ACTIVE,
          },
          deactivatePrescriptionNotes
        )
      }
      const calendarIdArrays = followUpTask.map((elem) => elem.value_reference_uuid)
      console.log(calendarIdArrays)

      //make status Abandon in case if followUpTask is false
      // let abandonedStatus= { calendar_activity_status: ABANDONED_ACTIVITY_STATUS }
      // abandonedStatus = preProcessRecords(dbConnections().main.entities["care_calendar"], abandonedStatus, null)
      // await transaction.getRepository("care_calendar").update({ care_calendar_id: In(calendarIdArrays) }
      // , abandonedStatus);
      const medsIdArray = medicine.map((elem) => elem.id)
      let medsNeedToBeUpdated = await getExistingMeds(activity_id, transaction)
      let existingMedsArray = medsNeedToBeUpdated.map((meds) => meds.value_reference_id)
      let getMedsWhichNeedsToBeDeleted = existingMedsArray.filter((item) => !medsIdArray.includes(item))

      let care_calendar_classification_Status_inactive = { active: INACTIVE, last_modifying_user_id: user_id }
      care_calendar_classification_Status_inactive = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], care_calendar_classification_Status_inactive, null)
      if (care_calendar_classification_Status_inactive.care_calendar_classification_id) {
        delete care_calendar_classification_Status_inactive.care_calendar_classification_id
      }
      await transaction.getRepository('care_calendar_classification').update(
        {
          care_calendar_id: activity_id,
          value_reference_id: In(getMedsWhichNeedsToBeDeleted),
          classifier_id: In([prescriptionKeystoColMaps.medicine.classifier_id]),
          active: ACTIVE,
        },
        care_calendar_classification_Status_inactive
      )
      if (vet_cosultant_type) {
        await saveConsultantType(transaction, vet_cosultant_type, activity_id)
      }

      let toBeInserted = []
      console.log('medicines', medicine)
      let blockmedicines = []
      let unblockmedicines = []

      for (const med of medicine) {
        const elem = {
          care_calendar_id: activity_id,
          classifier_id: prescriptionKeystoColMaps.medicine.classifier_id,
          last_modifying_user_id: user_id,
          active: ACTIVE,
        }
        for (const col of prescriptionKeystoColMaps.medicine.cols) {
          if (med[col.client_key] === undefined || med[col.client_key] === null) {
            throw new Error(`Valid key ${col.client_key} not found`)
          }
          elem[col.db_key] = med[col.client_key]
        }

        const exists = medsNeedToBeUpdated.find((meds) => meds.value_reference_id === med['id'] && (meds.value_json.route === med['prescribed_info']['route'] || med['id'] === 1999999999))
        if (exists && (exists['value_double'] !== med['dosage'] || !_.isEqual(exists['value_json'], med['prescribed_info']))) {
          await updateCareCalendar(transaction, elem, (care_calendar_id = exists.care_calendar_classification_id))
          //update care calendar
          // if(med['prescribed_info']['status']=='ADMINISTERED'){
          //   unblockmedicines.push({
          //     "medicine_id":med['id'],
          //     "unblocked_qty":(med['prescribed_info']['days']?med['prescribed_info']['days']:1)*parseFloat(med['dosage'])*(med['prescribed_info']['frequency']=="TID"?3:(med['prescribed_info']['frequency']=="BID"?2:1)),
          //     "unit":med['prescribed_info']['unit']?med['prescribed_info']['unit']:null
          //   })
          // }
        } else if (!exists) {
          toBeInserted.push(elem)
        }

        if (med['prescribed_info']['status'] == 'PUBLISHED') {
          blockmedicines.push({
            medicine_id: med['id'],
            days: med['prescribed_info']['days'] ? med['prescribed_info']['days'] : 1,
            frequency: med['prescribed_info']['frequency'] == 'TID' ? 3 : med['prescribed_info']['frequency'] == 'BID' ? 2 : 1,
            dosage: parseFloat(med['dosage']),
            blocked_qty: parseFloat(med['prescribed_info']['total_medicine_quantity']),
            unit: med['prescribed_info']['unit'],
          })
        }
      }
      let warehousemed = {
        warehouse_type_id: data.staff_type_id,
        warehouse_uuid: data.staff_id,
        source_type_id: data.activity_type_id,
        source_uuid: activity_id,
        medicines: blockmedicines,
      }
      console.log('blockmedicines==>', warehousemed)
      if (blockmedicines.length > 0) {
        let blockmed = await addUpdateMedForWarehouse(warehousemed, params.headers.token, transaction)
      }

      // let warehouseunblockmed={
      //   "warehouse_type_id":data.staff_type_id,
      //   "warehouse_uuid":data.staff_id,
      //   "source_type_id":data.activity_type_id,
      //   "source_uuid":activity_id,
      //   "medicines":unblockmedicines
      // }
      // if(unblockmedicines.length>0){
      //   let unblockmed = await removeMedFromWarehouse(warehouseunblockmed,params.headers.token,transaction)
      // }

      toBeInserted = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], toBeInserted, { json_columns: ['value_json', 'value_l10n'] })
      await transaction.getRepository('care_calendar_classification').insert(toBeInserted)
      if (typeof suggestions_advisory_notes === 'string' && suggestions_advisory_notes.length > 0) {
        let notes = {
          note: { ul: suggestions_advisory_notes },
          note_type_id: 1000440005,
          entity_1_type_id: 1000220003,
          entity_1_uuid: activity_id,
          creator_uuid: user_id,
          creator_type_id: user_type,
        }
        notes = preProcessRecords(dbConnections().main.entities['note'], notes, { json_columns: ['note'] })
        await transaction.getRepository('note').insert(notes)
      }
      if (follow_up_required === true) {
        const currentDate = new Date()
        const formattedDateTimeInGMT = currentDate.toISOString()
        follow_up_date = follow_up_date.split('T')[0]
        let visit_schedule_time = follow_up_date_gmt
        let parentActivity = await transaction.getRepository('care_calendar').find({ where: { care_calendar_id: activity_id }, select: { activity_id: true, entity_uuid: true, entity_type_id: true, calendar_activity_status: true } })
        if (parentActivity.length > 0) {
          if (calendarIdArrays && calendarIdArrays.length) {
            let updateActivity = { activity_date: follow_up_date, care_calendar_id: calendarIdArrays[0], visit_schedule_time: visit_schedule_time }
            updateActivity = preProcessRecords(dbConnections().main.entities['care_calendar'], updateActivity, null)
            await transaction.getRepository('care_calendar').update({ care_calendar_id: calendarIdArrays[0] }, updateActivity)
          } else {
            parentActivity = parentActivity[0]
            const transferableClassifierKeys = ['linked_care_calendar_task', 'ticket_1']
            const classificationData = await loadClassificationData('CARE_CALENDAR_CLASSIFICATION', activity_id, transferableClassifierKeys)

            const { activity_id: activity_type_id, entity_uuid, entity_type_id, calendar_activity_status } = parentActivity
            let calendar_status = calendar_activity_status
            //check if canceled or abandoned
            calendar_status = await getCalendarActivityStatus(calendar_activity_status, classificationData, transaction)
            if (!calendar_status) throw new Error('could not find previous data')
            const follow_up_id = generateUUID()
            let care_calendar_follow_up = {
              care_calendar_id: follow_up_id,
              activity_id: activity_type_id,
              activity_date: follow_up_date,
              calendar_activity_status: calendar_status,
              entity_type_id,
              entity_uuid,
              visit_schedule_time,
              visit_creation_time: formattedDateTimeInGMT,
              activity_created_by: user_id,
            }

            care_calendar_follow_up = preProcessRecords(dbConnections().main.entities['care_calendar'], care_calendar_follow_up, null)
            await transaction.getRepository('care_calendar').insert(care_calendar_follow_up)
            let care_calendar_data = {
              care_calendar_id: activity_id,
              classifier_id: 2000000186,
              value_reference_uuid: follow_up_id,
            }
            care_calendar_data = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], care_calendar_data, null)

            if (!classificationData.linked_care_calendar_task) classificationData.linked_care_calendar_task = activity_id
            let care_calendar_linked_id = {
              care_calendar_id: follow_up_id,
              classifier_id: 2000000126,
              value_reference_uuid: classificationData.linked_care_calendar_task,
              last_modifying_user_id: user_id,
            }

            let ticket = {
              care_calendar_id: follow_up_id,
              classifier_id: 2000000125,
              value_string_256: classificationData.ticket_1 ? classificationData.ticket_1 : '',
              last_modifying_user_id: user_id,
            }
            ticket = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], ticket, null)
            care_calendar_linked_id = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], care_calendar_linked_id, null)
            await transaction.getRepository('care_calendar_classification').insert(care_calendar_linked_id)
            await transaction.getRepository('care_calendar_classification').insert(care_calendar_data)
            await transaction.getRepository('care_calendar_classification').insert(ticket)
          }
        }
      }

      return true
    } catch (error) {
      console.log(error)
      errorLog('error in updateMedicines', { data }, { message: error })
      throw error
    }
  })
}

const updatePricingForPPU = async (transaction, data) => {
  let { medicine, activity_id, staff_id } = data
  let checkPublished = medicine.find((med) => med?.prescribed_info?.status === 'PUBLISHED')
  if (!checkPublished) return
  let ExistTransactionLedger = await transaction.getRepository('transaction_ledger').findOne({
    where: {
      entity_uuid: activity_id,
      active: ACTIVE,
    },
  })
  //get staff Id
  let entityRealtion = await transaction.getRepository('entity_relationship').findOne({
    where: {
      entity_relationship_type_id: PPU_PARAVET_TO_TASK,
      entity_2_entity_uuid: activity_id,
      active: ACTIVE,
    },
  })
  if (!ExistTransactionLedger) {
    await transaction.getRepository('transaction_ledger').insert({
      staff_uuid: entityRealtion.entity_1_entity_uuid,
      amount: 250,
      transaction_type: TRANSACTION_DEBIT,
      transaction_category: TRANSACTION_DEBIT,
      transaction_date: new Date(),
      entity_type_id: TASK,
      entity_uuid: activity_id,
    })
  }
}

const updateCareCalendar = async (transaction, elem, care_calendar_classification_id) => {
  dataUpdated = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], elem, null)
  if (dataUpdated.care_calendar_classification_id) {
    delete dataUpdated.care_calendar_classification_id
  }
  await transaction.getRepository('care_calendar_classification').update({ care_calendar_classification_id: care_calendar_classification_id }, dataUpdated)
}

const getExistingMeds = async (activity_id, transaction) => {
  const whereClause = {
    care_calendar_id: activity_id,
    classifier_id: prescriptionKeystoColMaps.medicine.classifier_id,
    active: ACTIVE,
  }
  let activityMedicineData = await transaction.getRepository('care_calendar_classification').find({
    where: {
      ...whereClause,
    },
  })
  activityMedicineData = postProcessRecords(dbConnections().main.entities['care_calendar_classification'], activityMedicineData, { json_columns: ['value_json', 'value_l10n'] })
  return activityMedicineData
}

const updateActivityPrescriptionForParavet = async (data, params) => {
  const { activity_id, medicine } = data
  const { user_id, user_type } = params.headers.token

  return await dbConnections().main.manager.transaction(async (transaction) => {
    try {
      let unblockmedicines = []
      console.log('medicines', medicine)
      for (const med of medicine) {
        const whereClause = {
          care_calendar_id: activity_id,
          classifier_id: prescriptionKeystoColMaps.medicine.classifier_id,
          active: ACTIVE,
          value_reference_id: med['id'],
        }

        let activityMedicine = await dbConnections().main.repos['care_calendar_classification'].find({
          where: {
            ...whereClause,
          },
        })
        activityMedicine = postProcessRecords(dbConnections().main.entities['care_calendar_classification'], activityMedicine, { json_columns: ['value_json', 'value_l10n'] })
        console.log('activityMedicine', activityMedicine)
        let activityMedicineData = activityMedicine.find((meds) => meds.value_json.route === med['prescribed_info']['route'] || med['id'] === 1999999999)
        if (!activityMedicineData) {
          activityMedicineData = activityMedicine[0]
        }
        console.log('activityMedicineData', activityMedicineData)
        if (med['prescribed_info'] && med['prescribed_info'].status) {
          const updatedValue = med['prescribed_info'].status
          if (activityMedicineData['value_json'] && activityMedicineData['value_json'].status != 'ADMINISTERED') {
            activityMedicineData['value_json'].status = updatedValue
            activityMedicineData['last_modifying_user_id'] = user_id

            if (med['prescribed_info']['status'] == 'ADMINISTERED') {
              unblockmedicines.push({
                medicine_id: med['id'],
                // "unblocked_qty":(med['prescribed_info']['days']?med['prescribed_info']['days']:1)*parseFloat(med['dosage'])*(med['prescribed_info']['frequency']=="TID"?3:(med['prescribed_info']['frequency']=="BID"?2:1)),
                unblocked_qty: parseFloat(med['prescribed_info']['total_medicine_quantity']),
                unit: med['prescribed_info']['unit'] ? med['prescribed_info']['unit'] : null,
              })
            }
          }
        }

        activityMedicineData = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], activityMedicineData, { json_columns: ['value_json', 'value_l10n'] })

        await transaction.getRepository('care_calendar_classification').save(activityMedicineData)
      }

      let warehouseunblockmed = {
        warehouse_type_id: data.staff_type_id,
        warehouse_uuid: data.staff_id,
        source_type_id: data.activity_type_id,
        source_uuid: activity_id,
        medicines: unblockmedicines,
      }
      console.log('unblockmedicines==>', unblockmedicines)
      if (unblockmedicines.length > 0) {
        let unblockmed = await removeMedFromWarehouse(warehouseunblockmed, params.headers.token, transaction)
      }

      return true
    } catch (error) {
      console.log(error)
      errorLog('error in updateMedicines for paravet', { data }, { message: error })
      throw error
    }
  })
}
const getCalendarActivityStatus = async (calendar_activity_status, classificationData, transaction) => {
  try {
    if (calendar_activity_status === 1000300051 || calendar_activity_status === 1000300052) {
      let calendar_status = false
      //get calendar activity status for followup calendar
      if (!classificationData.linked_care_calendar_task) {
        return (calendar_status = 1000300001)
      } else {
        //get all care calendar ids linked to main task
        let linked_care_calendar_task = await transaction.getRepository('care_calendar_classification').find({
          where: {
            value_reference_uuid: classificationData.linked_care_calendar_task,
            classifier_id: 2000000126,
          },
          select: {
            care_calendar_id: true,
          },
        })
        let linked_care_calendar_task_array = linked_care_calendar_task.map((elem) => elem.care_calendar_id)

        linked_care_calendar_task_array.push(classificationData.linked_care_calendar_task)
        //find previous status of care calendar
        let findPreviousStatus = await transaction.getRepository('care_calendar').find({
          where: { care_calendar_id: In(linked_care_calendar_task_array), calendar_activity_status: Not(In([1000300051, 1000300052])) },
          select: { calendar_activity_status: true },
        })
        console.log(findPreviousStatus, 'findPreviousStatus-------->', linked_care_calendar_task_array)
        if (findPreviousStatus && findPreviousStatus.length) {
          calendar_status = findPreviousStatus.length >= 16 ? 1000300001 : 1000300001 + findPreviousStatus.length
        } else calendar_status = 1000300001
      }
      return calendar_status ? calendar_status : false
    } else {
      return calendar_activity_status === 1000300016 ? 1000300001 : calendar_activity_status + 1
    }
  } catch (err) {
    return false
  }
}

const saveConsultantType = async (transaction, vet_cosultant_type, care_calendar_id) => {
  try {
    let existingConsultantType = await getConsultantType(care_calendar_id)
    if (existingConsultantType && existingConsultantType.value_reference_id === care_calendar_id) {
      return
    }
    let loadConsultantType = await dbConnections().main.repos['ref_reference'].find({
      where: {
        reference_category_id: 10007500,
        active: ACTIVE,
      },
      select: { reference_id: true, reference_information: true },
    })
    loadConsultantType = postProcessRecords(null, loadConsultantType, { json_columns: ['reference_information'] })
    let getPrice = loadConsultantType.find((cType) => cType.reference_id === vet_cosultant_type)
    let price = getPrice ? getPrice.reference_information.price : 0
    let upsertData = {
      care_calendar_id,
      value_reference_id: vet_cosultant_type,
      value_double: price,
      classifier_id: 2000000221,
    }
    if (existingConsultantType) {
      let updatePreviousRecordData = {
        care_calendar_classification_id: existingConsultantType.care_calendar_classification_id,
        classifier_id: 2000000221,
        active: INACTIVE,
      }
      updatePreviousRecordData = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], updatePreviousRecordData, null)
      await transaction.getRepository('care_calendar_classification').save(updatePreviousRecordData)
    }
    upsertData = preProcessRecords(dbConnections().main.entities['care_calendar_classification'], upsertData, null)
    await transaction.getRepository('care_calendar_classification').save(upsertData)

    return vet_cosultant_type
  } catch (error) {
    return -1
  }
}
const getConsultantType = async (care_calendar_id) => {
  try {
    const whereClause = {
      care_calendar_id: care_calendar_id,
      classifier_id: 2000000221,
      active: ACTIVE,
    }
    let existingConsultantData = await dbConnections().main.repos['care_calendar_classification'].findOne({
      where: {
        ...whereClause,
      },
      select: { care_calendar_classification_id: true, value_reference_id: true },
    })
    existingConsultantData = postProcessRecords(dbConnections().main.entities['care_calendar_classification'], existingConsultantData, {})
    return existingConsultantData
  } catch (error) {
    console.log('went wrong while getting existing consultant')
    return false
  }
}

const getTaskPreviewById = async (care_calendar_id) => {
  try {
    const activityData = await dbConnections().main.repos['care_calendar'].findOne({
      where: { care_calendar_id },
      select: { activity_id: true },
    })
    if (!activityData) return -1
    const { activity_id } = activityData
    const previewData = await getTaskPreview(care_calendar_id, activity_id)
    return previewData
  } catch (error) {
    console.log(error)
    errorLog('error in getTaskPreviewById', { data }, { message: error })
    throw error
  }
}

const getActivityObservations_v2 = async (calendar_id) => {
  try {
    let observationActivity = await CareCalendarActivityQueries.getObservation(calendar_id)
    return observationActivity
  } catch (error) {
    console.log(error)
    errorLog('error in getActivityObservation', { message: error })
    return -1
  }
}

const getActivityDiagnosis_v2 = async (calendar_id) => {
  try {
    let diagnosisActivity = await CareCalendarActivityQueries.getDiagnosis(calendar_id)
    return diagnosisActivity
  } catch (error) {
    console.log(error)
    errorLog('error in getActivityDiagnosis', { message: error })
    return -1
  }
}

const getActivityPrescription_v2 = async (calendar_id) => {
  try {
    let diagnosisActivity = await CareCalendarActivityQueries.getPrescription(calendar_id)
    return diagnosisActivity
  } catch (error) {
    console.log(error)
    errorLog('error in getActivityDiagnosis', { message: error })
    return -1
  }
}

const getActivityObservations_v3 = async (calendar_id) => {
  try {
    let observationActivity = await CareCalendarActivityQueries.getObservationV3(calendar_id)
    return observationActivity
  } catch (error) {
    console.log(error)
    errorLog('error in getActivityObservation', { message: error })
    return -1
  }
}

const getActivityNotes = async (calendar_id, noteType) => {
  try {
    let diagnosisActivity = await CareCalendarActivityQueries.notes(calendar_id, noteType)
    return diagnosisActivity
  } catch (error) {
    console.log(error)
    errorLog('error in getActivityDiagnosis', { message: error })
    return -1
  }
}

async function updateTaskToOTPFlow(care_calendar_id) {
  const otp = generateOtp(4)
  const classificationEntryObject = {
    otp: otp,
    otp_verified: 1000105002,
    invoice_generated: 1000105002,
  }

  await dbConnections().main.manager.transaction(async (transactionEntityManager) => {
    await saveClassificationData(transactionEntityManager, 'CARE_CALENDAR_CLASSIFICATION', care_calendar_id, classificationEntryObject)
  })
}

module.exports = {
  getCalendarEntries,
  createTask,
  updateActivity,
  propagateTaskToAnimal,
  medicineClassifications,
  farmerLevelTask,
  updateFarmerForm,
  getRoutePlan,
  getLinkedActivity,
  getTaskList,
  getTasks,
  getActivityObservations,
  updateActivityObservations,
  getActivityDiagnosis,
  updateActivityDiagnosis,
  getActivityPrescription,
  updateActivityPrescription,
  getTaskPreviewById,
  getActivityObservations_v2,
  getActivityDiagnosis_v2,
  getActivityPrescription_v2,
  getActivityObservations_v3,
  getActivityNotes,
  createTaskv2,
  updateCompleteTransactionLedger,
  updateCancelledTransactionLedger,
  propagateActivityStatus,
  generateFollowUp,
  getTaskPreview,
  handleFreeLanceFollowUp,
  handleFreelanceFinance,
  getRouteDetails,
  updateTaskToOTPFlow,
}
