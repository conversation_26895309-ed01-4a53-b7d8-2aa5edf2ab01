const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm');
const { loadClassificationData, saveClassificationData } = require('../common/classification.helper');
const { generateUUID, validateUUID, errorLog } = require('@krushal-it/mobile-or-server-lib');
const { configurationJSON } = require("@krushal-it/common-core");
const { getCompiledQuery } = require("../../queries/index.js");
const { removeMedFromWarehouse } = require('../../services/warehouse/controller.js')
const { saveActivityClassification } = require("../observationV2/activityClassification.js")
const { classifiers, yes_no_values } = require("../../ENUMS.js");
const _ = require('lodash')
const { In, Not } = require("typeorm");
const { handleFreeLanceFollowUp, handleFreelanceFinance, updateTaskToOTPFlow } = require('./controller.js');
const { calculateMedicineCost, getExistingMedicines } = require('../../utils/medicine.js');


const {
  ACTIVE,
  INACTIVE,
  PPU_PARAVET_TO_TASK,
  NO_MEDS,
  EXPIRY_DATE_CLASSIFIER,
  CONST_ID_TYPE_STAFF_BACKOFFICE,
  FARMER,
  RELATION_CALENDAR_CLASSIFICATION
} = require('../../utils/constant');
const { ActivityHelper } = require('../../utils/activity.js');

const prescriptionKeystoColMaps = {
  medicine: {
    cols: [{ client_key: "id", db_key: "value_reference_id" }, { client_key: "dosage", db_key: "value_double" }, { client_key: "prescribed_info", db_key: "value_json" }],
    classifier_id: 2000000185, key: "medicine"
  }
};

const overidePrescription = async (data, user_id, user_type) => {
   if(user_type !== 1000230002){
    return { message : "Only BackOffice Person can overide the API" }
   }
   let medicineFromFE = data.data;
   //get data from DB for the care calendar classification id  given
   let care_calendar_classification_id_array =  medicineFromFE.map((care_calendar)=>care_calendar.care_calendar_classification_id)
   let conn = await dbConnections().main.manager.transaction(async (transaction) => {
    const medicineData = await transaction.getRepository('care_calendar_classification').find({
        where: {
            care_calendar_classification_id: In(care_calendar_classification_id_array),
        },
        select : {
            value_json : true  , care_calendar_classification_id : true
        }
      });

     let checkAdministeredMeds =  medicineData.filter((medicine) => medicine.value_json && medicine.value_json.status !== "ADMINISTERED")
     if (checkAdministeredMeds.length) return  { message : "cannot edit Meds which are not Administered" }
     let medsToUpdate = []
     for (medicine of medicineFromFE ){
        let newMedsData = {
            care_calendar_classification_id : medicine.care_calendar_classification_id,
            value_double: medicine.dosage
        }   
        let medi = medicineData.find((med) => med.care_calendar_classification_id === medicine.care_calendar_classification_id)
        if(!medi) return { message : "Meds Id not found"}
        let value_json = medi.value_json
        value_json.frequency = medicine.frequency
        value_json.route = medicine.route
        value_json.unit = medicine.unit
        newMedsData.value_json = value_json
        medsToUpdate.push(newMedsData)
     }
     let updatedData= await transaction.getRepository("care_calendar_classification").save(medsToUpdate);

     return updatedData
   })
  return conn

}

const administerPrescription = async (data, user_id, user_type) => {
  try {
    const validStatus = new Set(['ADMINISTERED', 'CANCELLED'])

    let care_calendar_id = data.care_calendar_id
    let medicineFromFE = data.data

    let care_calendar_classification_id_array = medicineFromFE.map((care_calendar) => care_calendar.care_calendar_classification_id)

    let conn = await dbConnections().main.manager.transaction(async (transaction) => {
      const medicineData = await transaction.getRepository('care_calendar_classification').find({
        where: {
          care_calendar_classification_id: In(care_calendar_classification_id_array),
        },
        select: {
          value_reference_id: true,
          value_double: true,
          value_json: true,
          care_calendar_classification_id: true,
        },
      })
      postProcessRecords(undefined, medicineData, { json_columns: ['value_json'] })

      let updateArray = []
      let unblockmedicines = []
      let handleAnimalMedicinePrescriptionData = {}

      handleAnimalMedicinePrescriptionData.care_calendar_id = care_calendar_id
      handleAnimalMedicinePrescriptionData.medicines = []

      for (medicine of medicineFromFE) {
        let medi = medicineData.find((med) => med.care_calendar_classification_id === medicine.care_calendar_classification_id && med.value_json?.status !== medicine.status)
        if (!medi) continue

        let value_json = { ...medi.value_json }
        if (medicine.status && !validStatus.has(medicine.status)) return { message: 'Prescription status can either be ADMINISTERED or CANCELLED' }
        value_json.status = medicine.status

        updateArray.push({
          value_json,
          care_calendar_classification_id: medi.care_calendar_classification_id,
          last_modifying_user_id: user_id,
        })

        if (medicine.status === 'ADMINISTERED') {
          unblockmedicines.push({
            medicine_id: medi.value_reference_id,
            unblocked_qty: parseFloat(value_json['total_medicine_quantity']),
            unit: value_json['unit'] ? value_json['unit'] : null,
          })

          handleAnimalMedicinePrescriptionData.medicines.push({ id: medi.value_reference_id, details: value_json, quantity: medi.value_double })
        }
      }

      if (updateArray.length > 0) {

        let careCalendarRecord = await transaction.getRepository('care_calendar').findOne({
          where: {
            care_calendar_id: care_calendar_id,
          },
          select: {
            activity_id: true,
            entity_uuid: true,
            entity_type_id: true,
            calendar_activity_status: true,
          },
        })

        handleAnimalMedicinePrescriptionData.animal_id = careCalendarRecord.entity_uuid

        if (unblockmedicines.length > 0) {
          try {
            let warehouseunblockmed = {
              warehouse_type_id: user_type,
              warehouse_uuid: user_id,
              source_type_id: careCalendarRecord.activity_id,
              source_uuid: care_calendar_id,
              medicines: unblockmedicines,
            }
            let unblockmed = await removeMedFromWarehouse(warehouseunblockmed, { user_type: user_type, user_id: user_id }, transaction)
          } catch (error) {
            // do nothing
          }
        }

        const entity_care_calendar_classification = dbConnections().main.entities[RELATION_CALENDAR_CLASSIFICATION]

        let prescription_adminster_date = {
          classifier_id: classifiers.PRESCRIPTION_ADMINISTERED_AT,
          care_calendar_id: care_calendar_id,
          last_modifying_user_id: user_id,
          value_date: new Date().toJSON(),
        }
        prescription_adminster_date = preProcessRecords(entity_care_calendar_classification, prescription_adminster_date, { json_columns: [] })
        await transaction.getRepository('care_calendar_classification').save(prescription_adminster_date)

        updateArray = preProcessRecords(entity_care_calendar_classification, updateArray, { json_columns: ['value_json'] })
        let updatedData = await transaction.getRepository('care_calendar_classification').save(updateArray)

        if (handleAnimalMedicinePrescriptionData.medicines.length > 0) {
          await handleAnimalMedicineFlags(handleAnimalMedicinePrescriptionData, transaction)
        }

        return updatedData
      }
    })
    return { data: true }
  } catch (error) {
    return error
  }
}

const handleAnimalMedicineFlags = async (data, transaction) => {
  try {
    const care_calendar_id = data.care_calendar_id
    const medicines = data.medicines
    const animal_id = data.animal_id
    const medicineIds = medicines.map((item) => item.id);
    
    const medicineClassificationData = await transaction.getRepository('ref_medicine_classification').find({
      where: {
        medicine_id: In(medicineIds),
        active: ACTIVE,
        classifier_id: classifiers.MEDICINE_FLAGGED
      },
      select: {
        medicine_id: true,
        value_int: true,
        value_reference_id: true,
      },
    })
    
    if(_.isEmpty(medicineClassificationData)) {
      // No medicines are flagged medicines. hence, return from here
      return
    }

    let withdrawlDays = 0

    for (let prescribedMedicine of medicines) {

      let medicineClassificationItem = medicineClassificationData.find(item => item.medicine_id === prescribedMedicine.id);
      let isMedicineFlagged = medicineClassificationItem?.value_reference_id
      let medWithdrawlDays = medicineClassificationItem?.value_int

      if (isMedicineFlagged === yes_no_values.YES) {
        withdrawlDays = Math.max(withdrawlDays, medWithdrawlDays)
      }
    }

    if (withdrawlDays > 0) {
      
      const withdrawlEndDate = new Date();
      withdrawlEndDate.setDate(withdrawlEndDate.getDate() + withdrawlDays);

      let animalClassificationEntry = {
        animal_id: animal_id,
        classifier_id: classifiers.ANIMAL_ANTIBIOTIC_FLAG,
        value_reference_uuid: care_calendar_id,
        value_date: withdrawlEndDate.toJSON(),
        value_int: withdrawlDays,
      }

      await transaction.getRepository('animal_classification').save(animalClassificationEntry)
    }
  } catch (error) {
    errorLog('could not process the animal medicine tagging', error)
  }
}

const getStaffIdByVetId = async (calendar_id , transaction) => {
  let userQuery = `
  select * from
  (SELECT paravet.staff_id from  main.care_calendar as cc   
     left join main.customer_classification as custclass on custclass.classifier_id=2000000055 and custclass.customer_id=cc.entity_uuid and custclass.active = 1000100001
     left join  main.entity_geography as eg on eg.geography_id = custclass.value_reference_id and eg.active = 1000100001  and eg.entity_type_id = 1000230004
     left join  main.staff  as paravet on paravet.staff_id= eg.entity_uuid
     where paravet.staff_type_id=1000230004 and  cc.care_calendar_id= '${calendar_id}' order by paravet.updated_at DESC limit 1) a
     UNION
  select * from   (
     SELECT paravet.staff_id from  main.care_calendar as cc   
     left join main.entity_relationship as er on  er.entity_2_entity_uuid =cc.entity_uuid
     left join main.customer_classification as custclass on custclass.classifier_id=2000000055 and custclass.customer_id = er.entity_1_entity_uuid and custclass.active = 1000100001
     left join  main.entity_geography as eg on eg.geography_id = custclass.value_reference_id and eg.active = 1000100001  and eg.entity_type_id = 1000230004
     left join  main.staff  as paravet on paravet.staff_id= eg.entity_uuid
     where paravet.staff_type_id=1000230004 and  cc.care_calendar_id= '${calendar_id}' order by paravet.updated_at DESC limit 1) b
  `
  let userData= await transaction.query(userQuery);
  if(userData && userData.length) return userData[0].staff_id
  return false;
}


const publishPrescription = async (data ,user_id ,user_type) => {
  try {
    let vet_fee=undefined;
    let { activity_id, suggestions_advisory_notes, medicine, follow_up_required, follow_up_date  , vet_cosultant_type} = data;
    if (follow_up_required === undefined) throw new Error("follow up options not found");
    if (follow_up_required === true && !follow_up_date) throw new Error("follow up date is required");
    return await dbConnections().main.manager.transaction(async (transaction) => {
      const return_result = {
        data: true
      }

      //suggestion advisory notes
      if (typeof suggestions_advisory_notes === "string" && suggestions_advisory_notes.length > 0) {
        let dataForSuggestionNotes = {user_id ,activity_id ,suggestions_advisory_notes , user_type}
        await manageSuggestionNotes(transaction, dataForSuggestionNotes)
      }      
      let medicineData = {medicine, user_id, user_type ,activity_id}
      //get all existing meds for this care calendar existingMeds = [ {"care_calendar_id":"" ,"value_json": "" ,"value_reference_id":}]
      const oldMedicines = await getExistingMedicines(transaction, activity_id)
      let existingMeds  = await getExistingActiveMeds(transaction,activity_id);
      // validate meds along with references
      let validateDuplicates =  validateDuplicateMeds(existingMeds , medicine)
      if(validateDuplicates){
        throw new Error("Meds with same route exist")
      }
      //getMeds to Be deactivated
      let medsToDeactivate = getmedsToDeactivate(existingMeds ,medicine);
      //deactivate meds
      if(medsToDeactivate.length) {
        await deactivateMeds(transaction,medsToDeactivate)
      }
      //validate and save meds
      await saveMedicine(transaction,medicineData)
      //add to block quantity
      let blockmedicines = []
      for (let med of medicine) {
        if(med['prescribed_info']['status']=='PUBLISHED'){
          blockmedicines.push({
            "medicine_id":med['id'],
            "days":med['prescribed_info']['days']?med['prescribed_info']['days']:1,
            "frequency":med['prescribed_info']['frequency']=="TID"?3:(med['prescribed_info']['frequency']=="BID"?2:1),
            "dosage":parseFloat(med['dosage']),
            "blocked_qty":parseFloat(med['prescribed_info']['total_medicine_quantity']),
            "unit":med['prescribed_info']['unit']
          })
        }
      }
      let staffIdByVetId = await getStaffIdByVetId(activity_id,transaction)
      // let carecalendar = await transaction.getRepository("care_calendar").find({ where: { care_calendar_id: care_calendar_id }, select: { activity_id: true, entity_uuid: true, entity_type_id: true,calendar_activity_status: true } });
      let warehousemed={
        "warehouse_type_id": 1000470005,
        "warehouse_uuid": staffIdByVetId,
        "source_type_id": 1000230004,
        "source_uuid":activity_id,
        "medicines":blockmedicines
      }
      //uncomment if we dont need to block 
      // console.log('blockmedicines==>',warehousemed)
      // if(blockmedicines.length>0 && staffIdByVetId){
      //   let blockmed = await addUpdateMedForWarehouse(warehousemed,{user_type:user_type,user_id:user_id},transaction)
      // }
      //save vet consultant type 
      if (vet_cosultant_type){
        const {  fee } = await saveConsultantType(transaction, vet_cosultant_type, activity_id)
        vet_fee = fee
      }
      // follow up while publishing meds 
      if (follow_up_required === true) {
        const followUpTaskId = await addFollowUpForTask(transaction, { ...data, user_id, user_type }) 

        let payment_details =  await loadClassificationData("CARE_CALENDAR_CLASSIFICATION", activity_id, ["payment_details"]);
        if (payment_details) {
          await updateTaskToOTPFlow(followUpTaskId)
        }
      }
      //save publish date
      const entity_care_calendar_classification = dbConnections().main.entities[RELATION_CALENDAR_CLASSIFICATION]
      let  prescription_publish_date =
      {
        classifier_id: 2000000227,
        care_calendar_id: activity_id,
        last_modifying_user_id : user_id,
        value_date: new Date().toJSON()
      }
      prescription_publish_date = preProcessRecords(entity_care_calendar_classification, prescription_publish_date, { json_columns : [] })
      await transaction.getRepository("care_calendar_classification").save(prescription_publish_date);

      await handleFreelanceFinance(activity_id, transaction);
      const total_new_medicine_cost = await calculateMedicineCost(medicine)
      
      await ActivityHelper.processPaymentDetails(transaction, activity_id, vet_fee, total_new_medicine_cost)

      return return_result
    });
  }
  catch (error) {
    throw new Error ("could not save prescription")
  }
}

const deactivateMeds = async (transaction,medsToDeactivate) => {
  try {
    let whereClause = {
      active : ACTIVE,
      care_calendar_classification_id: In(medsToDeactivate),
      classifier_id :prescriptionKeystoColMaps.medicine.classifier_id

    }
    let setData = {
      active: INACTIVE
    }
    setData = preProcessRecords(dbConnections().main.entities["care_calendar_classification"], setData, null );
    return await transaction.getRepository("care_calendar_classification").update(
      whereClause,
      setData
    );

  } catch (error) {
    throw new Error ("went wrong while deactivating meds")
  }
}

const getmedsToDeactivate = (existingMeds ,payloadMeds) => {
  let medsToDeactivate = []
  for (const med of existingMeds ) {
    const exist = payloadMeds.find((meds) => meds.care_calendar_classification_id === med['care_calendar_classification_id'])
    if(!exist) medsToDeactivate.push(med["care_calendar_classification_id"])
  }
  return medsToDeactivate
}

const validateDuplicateMeds = (existingMeds , payloadMeds) => {
  // let existingMedsArray = existingMeds.map((meds) => meds.value_reference_id)
  for (const med of payloadMeds) {       
     const exists =  existingMeds.find((meds) => meds.care_calendar_classification_id !== med['care_calendar_classification_id'] && meds.value_reference_id === med['id'] && (meds.value_json.route === med['prescribed_info']['route'] || med['id'] === NO_MEDS))
     if (exists) return true
  }
  return false
}

const getExistingActiveMeds = async (transaction,care_calendar_id) => {
  try {
      let existingMeds = await transaction.getRepository("care_calendar_classification").find({
        where : {
            care_calendar_id : care_calendar_id,
            active : ACTIVE,
            classifier_id : prescriptionKeystoColMaps.medicine.classifier_id,
        },
        select : {
            care_calendar_classification_id :true ,
            value_reference_id : true ,
            value_json: true
        }
      })
      existingMeds = postProcessRecords(undefined, existingMeds, { json_columns: ["value_json"] });

      return existingMeds
  } catch (err){
    throw new Error("went wrong while getting existing meds")
  }
}


const addFollowUpForTask = async (transaction , data ) => {
  let { activity_id, follow_up_date ,follow_up_date_gmt,user_id  } = data;
  const followUpTask = await transaction.getRepository('care_calendar_classification').find({
    where: {
      care_calendar_id: activity_id,
      classifier_id: 2000000186,
      active: ACTIVE
    },
    select: {
      value_reference_uuid: true
    }
  });
  const calendarIdArrays = followUpTask.map((elem) => elem.value_reference_uuid);
  const currentDate = new Date();
  const formattedDateTimeInGMT = currentDate.toISOString();
  follow_up_date = follow_up_date.split("T")[0]
  let visit_schedule_time= follow_up_date_gmt
  let parentActivity = await transaction.getRepository("care_calendar").find({ where: { care_calendar_id: activity_id }, select: { activity_id: true, entity_uuid: true, entity_type_id: true,calendar_activity_status: true } });
  if (parentActivity.length > 0 ) {
    if(calendarIdArrays && calendarIdArrays.length){
      
      let updateActivity ={ activity_date: follow_up_date ,care_calendar_id: calendarIdArrays[0] ,visit_schedule_time: visit_schedule_time }
      updateActivity = preProcessRecords(dbConnections().main.entities["care_calendar"], updateActivity , null )
      await transaction.getRepository("care_calendar").update(
        { care_calendar_id: calendarIdArrays[0] },
        updateActivity
      );
        
      return calendarIdArrays[0]
      
    }else{
      parentActivity = parentActivity[0];
      const transferableClassifierKeys = ['linked_care_calendar_task', 'ticket_1', 'payment_details'];
      const classificationData = await loadClassificationData('CARE_CALENDAR_CLASSIFICATION', activity_id, transferableClassifierKeys);

      const { activity_id: activity_type_id, entity_uuid, entity_type_id ,calendar_activity_status} = parentActivity;
      let calendar_status= calendar_activity_status
      //check if canceled or abandoned 
      calendar_status =await  getCalendarActivityStatus(calendar_activity_status, classificationData, transaction);
      if (!calendar_status) throw new Error('could not find previous data')
      const follow_up_id = generateUUID();
      let care_calendar_follow_up= {
        care_calendar_id: follow_up_id,
        activity_id: activity_type_id,
        activity_date: follow_up_date,
        calendar_activity_status: calendar_status,
        entity_type_id,
        entity_uuid,
        visit_schedule_time,
        visit_creation_time: formattedDateTimeInGMT,
        activity_created_by: user_id
      }

      care_calendar_follow_up = preProcessRecords (dbConnections().main.entities["care_calendar"],care_calendar_follow_up , null)
      await transaction.getRepository("care_calendar").insert(care_calendar_follow_up);
      
      let care_calendar_data= {
        care_calendar_id: activity_id,
        classifier_id: 2000000186,
        value_reference_uuid: follow_up_id
      }
      care_calendar_data = preProcessRecords (dbConnections().main.entities["care_calendar_classification"],care_calendar_data , null)

      if (!classificationData.linked_care_calendar_task) classificationData.linked_care_calendar_task = activity_id;
      let care_calendar_linked_id= {
        care_calendar_id: follow_up_id,
        classifier_id: 2000000126,
        value_reference_uuid: classificationData.linked_care_calendar_task,
        last_modifying_user_id: user_id

      }
      
      let ticket= {
        care_calendar_id: follow_up_id,
        classifier_id: 2000000125,
        value_string_256: classificationData.ticket_1 ? classificationData.ticket_1 :'',
        last_modifying_user_id: user_id
      }

      let payment_details_classification_data = {
        care_calendar_id: follow_up_id,
        classifier_id: 2000000463,
        value_json: classificationData.payment_details,
        last_modifying_user_id: user_id
      }

      ticket = await preProcessRecords(dbConnections().main.entities['care_calendar_classification'], ticket, null)
      payment_details_classification_data = await preProcessRecords(dbConnections().main.entities['care_calendar_classification'], payment_details_classification_data, null)
      care_calendar_linked_id = preProcessRecords (dbConnections().main.entities["care_calendar_classification"],care_calendar_linked_id , null)
      await transaction.getRepository("care_calendar_classification").insert(care_calendar_linked_id);
      await transaction.getRepository("care_calendar_classification").insert(care_calendar_data);         
      await transaction.getRepository("care_calendar_classification").insert(ticket);         
      await transaction.getRepository("care_calendar_classification").insert(payment_details_classification_data);         
      await handleFreeLanceFollowUp(activity_id, follow_up_id, transaction);
      await saveActivityClassification(activity_id, follow_up_id, transaction)
      return follow_up_id;
    }
     
  }
} 
const getCalendarActivityStatus =async (calendar_activity_status, classificationData , transaction) => {
  try {
   if(calendar_activity_status === 1000300051 || calendar_activity_status === 1000300052){
     let calendar_status = false
     //get calendar activity status for followup calendar
     if (!classificationData.linked_care_calendar_task){
      return calendar_status = 1000300001
     } 
     else{
      //get all care calendar ids linked to main task 
        let linked_care_calendar_task= await transaction.getRepository("care_calendar_classification").find(
          {
            where: {
              value_reference_uuid: classificationData.linked_care_calendar_task,
              classifier_id: 2000000126
            },
            select: {
              care_calendar_id: true
            }
          }
        )
        let linked_care_calendar_task_array = linked_care_calendar_task.map((elem) => elem.care_calendar_id);

        linked_care_calendar_task_array.push(classificationData.linked_care_calendar_task)
       //find previous status of care calendar
        let findPreviousStatus = await transaction.getRepository("care_calendar").find(
         { 
           where: { care_calendar_id: In(linked_care_calendar_task_array) ,calendar_activity_status : Not(In([1000300051,1000300052]))},
           select: { calendar_activity_status: true } 
         });
        if(findPreviousStatus && findPreviousStatus.length){
          calendar_status= findPreviousStatus.length >= 16 ? 1000300001  : 1000300001 + findPreviousStatus.length ;
        }
        else calendar_status= 1000300001;
     }
      return calendar_status ? calendar_status : false

   }
   else{
     return  calendar_activity_status === 1000300016 ? 1000300001 :calendar_activity_status + 1; 
   }
  }
  catch (err) {
   return false
  }
}

const saveConsultantType = async (transaction , vet_cosultant_type ,care_calendar_id) => {
  try{
     let existingConsultantType = await  getConsultantType(care_calendar_id)
     if(existingConsultantType && existingConsultantType.value_reference_id === care_calendar_id){
        return 
     }
     let loadConsultantType= await dbConnections().main.repos["ref_reference"].find({
      where: {
         reference_category_id :10007500,
         active : ACTIVE

      },
      select : { reference_id :true , reference_information : true}
    });
    loadConsultantType = postProcessRecords(null , loadConsultantType ,{ json_columns: ['reference_information'] } )    
     let getPrice = loadConsultantType.find((cType)=> cType.reference_id === vet_cosultant_type)
     let price = getPrice ? getPrice.reference_information.price : 0;
     let upsertData = { 
        care_calendar_id ,
        value_reference_id : vet_cosultant_type ,
        value_double : price,
        classifier_id: 2000000221,
     }
     if(existingConsultantType){
      let updatePreviousRecordData = {
        care_calendar_classification_id : existingConsultantType.care_calendar_classification_id,
        classifier_id: 2000000221,
        active : INACTIVE
      }
      updatePreviousRecordData = preProcessRecords(dbConnections().main.entities["care_calendar_classification"], updatePreviousRecordData, null)
      await  transaction.getRepository("care_calendar_classification").save(updatePreviousRecordData);
     }
     upsertData = preProcessRecords(dbConnections().main.entities["care_calendar_classification"], upsertData, null)
     await  transaction.getRepository("care_calendar_classification").save(upsertData);

     return {vet_cosultant_type,fee:price}
  } catch (error) {
    return -1
  }
}
const getConsultantType = async (care_calendar_id) => {
  try {
      const whereClause = {
        care_calendar_id: care_calendar_id,
        classifier_id: 2000000221,
        active: ACTIVE,
      };
      let existingConsultantData = await dbConnections().main.repos["care_calendar_classification"].findOne({
        where: {
          ...whereClause
        },
        select : { care_calendar_classification_id :true , value_reference_id :true}
      });
      existingConsultantData = postProcessRecords(dbConnections().main.entities["care_calendar_classification"], existingConsultantData, {});
      return existingConsultantData;
    } catch (error) {
    return false
  }
}

const saveMedicine = async (transaction,data) => {
  try{
    const { medicine ,user_id,  user_type, activity_id } = data;
    let toBeInserted = []
    const current_date = new Date().toJSON();

    for (const med of medicine) {       
      const elem = {
        care_calendar_id: activity_id,
        classifier_id: prescriptionKeystoColMaps.medicine.classifier_id,
        last_modifying_user_id: user_id,
        active: med.is_deleted ? INACTIVE : ACTIVE,
        value_date: current_date
      };

      for (const col of prescriptionKeystoColMaps.medicine.cols) {
        if (med[col.client_key] === undefined || med[col.client_key] === null) {
          throw new Error(`Valid key ${col.client_key} not found`);
        }
        elem[col.db_key] = med[col.client_key];
      }
      if (med.care_calendar_classification_id){
        //update meds
        elem['care_calendar_classification_id'] = med.care_calendar_classification_id
        
      }
      toBeInserted.push(elem);
      
    }
    toBeInserted = preProcessRecords(dbConnections().main.entities["care_calendar_classification"], toBeInserted, { json_columns: ["value_json", "value_l10n"] });
    
    return await transaction.getRepository("care_calendar_classification").save(toBeInserted);
  } catch (error) {
    throw  new Error("could not save medicines")
  }
}

const manageSuggestionNotes = async (transaction, data) => {
  let {user_id ,activity_id ,suggestions_advisory_notes , user_type } = data
  //deactivate the old suggestion notes
  let deactivatePrescriptionNotes = { active: INACTIVE, last_modifying_user_id: user_id }
  deactivatePrescriptionNotes= preProcessRecords(dbConnections().main.entities["note"], deactivatePrescriptionNotes, null);
  if(deactivatePrescriptionNotes.note_id){
    delete deactivatePrescriptionNotes.note_id
  }
  let prescriptionNoteDeactivate = await transaction.getRepository("note").update({
    note_type_id: 1000440005, entity_1_type_id: 1000220003, entity_1_uuid: activity_id, active: ACTIVE },
    deactivatePrescriptionNotes
  );
  
  //save the new suggestion notes
  let notes= {
    note: { ul: suggestions_advisory_notes },
    note_type_id: 1000440005,
    entity_1_type_id: 1000220003,
    entity_1_uuid: activity_id,
    creator_uuid: user_id,
    creator_type_id: user_type

  }
  notes = preProcessRecords(dbConnections().main.entities["note"], notes, { json_columns: ["note"] });
  await transaction.getRepository("note").insert(notes);
  
}

const updatePricingForPPU = async (transaction , data) => {
  let  { medicine, activity_id,staff_id} = data
  let checkPublished =  medicine.find((med)=> med?.prescribed_info?.status === "PUBLISHED")
  if(!checkPublished) return ;
  let ExistTransactionLedger = await transaction.getRepository("transaction_ledger").findOne({
     where: {
       entity_uuid: activity_id,
       active: ACTIVE
     }
  });
  //get staff Id
  let entityRealtion = await transaction.getRepository("entity_relationship").findOne({
     where : {
       entity_relationship_type_id : PPU_PARAVET_TO_TASK ,
       entity_2_entity_uuid: activity_id,
       active : ACTIVE
     }
  }) 
  if(!ExistTransactionLedger){
   await transaction.getRepository("transaction_ledger").insert({
     staff_uuid : entityRealtion.entity_1_entity_uuid ,
     amount :250 ,
     transaction_type :1000790002 ,
     transaction_category:1000790002,
     transaction_date:new Date(),
     entity_type_id: 1000460004 ,
     entity_uuid: activity_id
   })
  }
}

class ActivityPrescriptionUpdateServiceV2 {
    async handleActivity(data ,params) {
        try{
            const  object  = params.route.type;
            const { user_id, user_type } = params.headers.token;
            //infoLog(page_limit)
            switch (object) {
              case "override":
                let result = await overidePrescription(data , user_id ,user_type);
                return result;
              case "administer": 
                let administer = await administerPrescription(data , user_id ,user_type);
                return administer
              case "publish":
                let publish = await publishPrescription(data, user_id, user_type)
                return publish
              default :
                return "no option for route";
            }
        } catch (error) {
            return -1
        }
    }

}


const observationKeysToColMaps = {
  weight_1: {
    cols: [{ client_key: "value", db_key: "value_double" }], value_reference_id: 1007000001, key: "weight_1"
  },
  temperature_1: {
    cols: [{ client_key: "value", db_key: "value_double" }], value_reference_id: 1007000002, key: "temperature_1"
  }
};

const observationClassifierId = 2000000182;
const ActivityObservationUpdateSeviceV2 = async (data,params) => {
  const { activity_id } = data;
  const { user_id ,user_type} = params.headers.token
  return await dbConnections().main.manager.transaction(async (transaction) => {
    try {
      const { observations } = data;
      let observationClassifierIds = observations.map((obs)=> obs.care_calendar_classification_id)
      observationClassifierIds = observationClassifierIds .filter((obs)=> { if(obs) return obs})
      let observationDeactivateData =  { active: INACTIVE, last_modifying_user_id: user_id }
        observationDeactivateData = preProcessRecords(dbConnections().main.entities["care_calendar_classification"], observationDeactivateData,null);
      if(observationDeactivateData.care_calendar_classification_id){
          delete observationDeactivateData.care_calendar_classification_id
      }
      const result = await transaction.getRepository("care_calendar_classification").update({
          care_calendar_id: activity_id,
          care_calendar_classification_id :Not(In(observationClassifierIds)),
          classifier_id : observationClassifierId,
          active: ACTIVE
      }, observationDeactivateData);

      let { observation_notes_1 } = data
      if(typeof observation_notes_1 === "string" && observation_notes_1.length > 0){ 
        let observationNoteDeactivateData =  { active: INACTIVE, last_modifying_user_id: user_id }
        observationNoteDeactivateData = preProcessRecords(dbConnections().main.entities["note"], observationNoteDeactivateData,null);
        if(observationNoteDeactivateData.note_id){
          delete observationNoteDeactivateData.note_id
        }
        let observationNoteDeactivate = await dbConnections().main.repos["note"].update(
          {note_type_id: 1000440006, entity_1_type_id: 1000220003, entity_1_uuid: activity_id, active: ACTIVE },
          observationNoteDeactivateData
        );
      } 
      let tobeInserted = [];
      for (const obs of observations) {
        const fieldconf = observationKeysToColMaps[obs.id].cols;

        const entry = {
          classifier_id: observationClassifierId,
          care_calendar_id: activity_id,
          value_reference_id: observationKeysToColMaps[obs.id].value_reference_id,
          last_modifying_user_id: user_id
        };

        for (const col of fieldconf) {
          if (!obs[col.client_key]) throw new Error(`Valid key ${col.client_key} not found`);
          entry[col.db_key] = obs[col.client_key];
        }
        if(obs.care_calendar_classification_id) {
          entry['care_calendar_classification_id'] = obs.care_calendar_classification_id
        }
        tobeInserted.push(entry);
      }
      
      //insert observation notes in observation
      if (typeof observation_notes_1 === "string" && observation_notes_1.length > 0) {
        let notes= {
          note: { ul: observation_notes_1 },
          note_type_id: 1000440006,
          entity_1_type_id: 1000220003,
          entity_1_uuid: activity_id,
          creator_uuid: user_id,
          creator_type_id: user_type

        }
        notes = preProcessRecords(dbConnections().main.entities["note"], notes, { json_columns: ["note"] });
        await transaction.getRepository("note").insert(notes);
      }

      await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', activity_id, {observation_date: new Date().toJSON()});
      tobeInserted = preProcessRecords(dbConnections().main.entities["care_calendar_classification"], tobeInserted, { json_columns: ["value_l10n", "value_json"] });
      await transaction.getRepository("care_calendar_classification").save(tobeInserted);
      return true;
    } catch (error) {
      errorLog("error in updateActivityObservations", { data }, { message: error });
      throw error;
    }
  });
}

const diagnosisKeysToColMaps = {
  diagnosis: {
    cols: [{ client_key: "value", db_key: "value_reference_id" }], classifier_id: 2000000184, key: "diagnosis", category_id: 10080000
  },
  response_to_treatment: {
    cols: [{ client_key: "value", db_key: "value_reference_id" }], classifier_id: 2000000183, key: "response_to_treatment", category_id: 10080000
  },
};

const ActivityDiagnosisUpdateServiceV2 = async (data,params) => {
  const { activity_id, diagnosis, response_to_treatment } = data;
  const { user_id } = params.headers.token;
  return await dbConnections().main.manager.transaction(async (transaction) => {
    try {
      let toBeInserted = [];
      let diagnosisIds = diagnosis.map((diag)=> diag.care_calendar_classification_id)
      diagnosisIds = diagnosisIds .filter((diag)=> { if(diag) return diag})

        let updatePreviousDiagnosis = { active: INACTIVE, last_modifying_user_id: user_id }
        updatePreviousDiagnosis = preProcessRecords(dbConnections().main.entities["care_calendar_classification"], updatePreviousDiagnosis, null);
        if(updatePreviousDiagnosis.care_calendar_classification_id){
          delete updatePreviousDiagnosis.care_calendar_classification_id
        }
        await transaction.getRepository("care_calendar_classification").update({
          care_calendar_id: activity_id, classifier_id: In([ diagnosisKeysToColMaps.response_to_treatment.classifier_id]), active: ACTIVE
        }, updatePreviousDiagnosis);
        await transaction.getRepository("care_calendar_classification").update({
          care_calendar_id: activity_id, classifier_id: diagnosisKeysToColMaps.diagnosis.classifier_id ,care_calendar_classification_id : Not(In(diagnosisIds)), active: ACTIVE
        }, updatePreviousDiagnosis);
      for (const elem of diagnosis) {
        const entry = {
          classifier_id: diagnosisKeysToColMaps.diagnosis.classifier_id,
          care_calendar_id: activity_id,
          last_modifying_user_id: user_id
        };
        if(elem.care_calendar_classification_id) {
          entry['care_calendar_classification_id'] = elem.care_calendar_classification_id
        }
        for (const col of diagnosisKeysToColMaps.diagnosis.cols) {
          if (elem[col.client_key] === undefined || elem[col.client_key] === null) {
            throw new Error(`Valid key ${col.client_key} not found`);
          }
          entry[col.db_key] = elem[col.client_key];
        }
        toBeInserted.push(entry);
      }

      for (const elem of response_to_treatment) {
        const entry = {
          classifier_id: diagnosisKeysToColMaps.response_to_treatment.classifier_id,
          care_calendar_id: activity_id,
          last_modifying_user_id: user_id
        };
        for (const col of diagnosisKeysToColMaps.response_to_treatment.cols) {
          if (elem[col.client_key] === undefined || elem[col.client_key] === null) {
            throw new Error(`Valid key ${col.client_key} not found`);
          }
          entry[col.db_key] = elem[col.client_key];
        }
        toBeInserted.push(entry);
      }
      await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', activity_id, {diagnosis_date: new Date().toJSON()});

      toBeInserted = preProcessRecords(dbConnections().main.entities["care_calendar_classification"], toBeInserted, { json_columns: ["value_json", "value_l10n"] });
      await transaction.getRepository("care_calendar_classification").save(toBeInserted);
      return true
    } catch (error) {
      errorLog("error in updateActivityDiagnosis", { data }, { message: error });
      throw error;
    }
  });
}

const activitySummaryV2 = async (care_calendar_id , params) => {
    try {
      const isPostgres = (configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1) ? 1 : 0;
      let { user_id , user_type } = params.headers.token
      if (!validateUUID(care_calendar_id)) throw new Error("care_calendar_id is required");
      const queryParameters = { user_id, user_type, care_calendar_id };
      const notUsersForvill = new Set([CONST_ID_TYPE_STAFF_BACKOFFICE, FARMER]);
      const templateParameters = { user_id, user_type,calendar_id: care_calendar_id, ACTIVE, notUsersForvill, EXPIRY_DATE_CLASSIFIER, IS_POSTGRES: isPostgres };


      let query = getCompiledQuery("calendar-summary", templateParameters);
      const queryBuilder = await dbConnections().main.manager.createQueryBuilder()
        .from(`(${query})`, "a1").setParameters(queryParameters);
      let activityDetails = await queryBuilder.select("*").execute();

      postProcessRecords(undefined, activityDetails, { json_columns: ['activity_name_l10n', 'animal_type_json', 'calendar_activity_status_name_json', 'customer_name_l10n', 'paravet_name', 'village_name_l10n', 'vet_name','document_information' ,'client_document_information'] });
      let activityFiles = await dbConnections().main.repos["document"].find({
        where: [{ entity_1_entity_uuid: care_calendar_id, document_type_id: 1000260010 }],
        select: { document_id: true, document_information: true, client_document_information: true, document_type_id: true }
      });
      let rescheduledDateTime = null;
      const followUpActivity = await dbConnections().main.repos["care_calendar_classification"].find({
        where: { active: ACTIVE, care_calendar_id: care_calendar_id, classifier_id: 2000000186 },
        select: { value_reference_uuid: true }
      });
      if (followUpActivity.length > 0) {
        const folloUpTaskId = followUpActivity[0].value_reference_uuid;
        const folloupTask = await dbConnections().main.repos["care_calendar"].findOne({
          where: { care_calendar_id: folloUpTaskId },
          select: { visit_schedule_time: true }
        });
        rescheduledDateTime = folloupTask && folloupTask.visit_schedule_time;
      }
      postProcessRecords(undefined, activityFiles, { json_columns: ['document_information', 'client_document_information'] });
      if (!activityDetails.length) throw new Error("failed to get activity summary")
      const return_result = { ...activityDetails[0], task_files: activityFiles, rescheduledDateTime}
      


      
      const otp_verified_query = `
        SELECT 
          otp_verified.value_reference_id AS otp_verified
        FROM
        main.care_calendar
        LEFT JOIN main.care_calendar_classification otp_verified ON otp_verified.classifier_id = 2000000434 
          AND otp_verified.care_calendar_id = care_calendar.care_calendar_id
        WHERE
        care_calendar.care_calendar_id = :care_calendar_id
      `
      
      const otp_verified_query_result = await dbConnections().main.manager.createQueryBuilder()
        .from(`(${otp_verified_query})`)
        .setParameters({ care_calendar_id })
        .execute();
      
      if (otp_verified_query_result.length && otp_verified_query_result[0].otp_verified ) {
        return_result.otp_verified = otp_verified_query_result[0].otp_verified
      } 
      
      
      return return_result
    } catch (error) {
      console.log(error);
      throw error;
    }
}

const activityServiceDocumentData = async (care_calendar_id , params) => {
  try {
    const isPostgres = (configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1) ? 1 : 0;
    let { user_id , user_type } = params.headers.token
    const queryParameters = { user_id, user_type, care_calendar_id };
    const notUsersForvill = new Set([CONST_ID_TYPE_STAFF_BACKOFFICE, FARMER]);
    const templateParameters = { user_id, user_type,calendar_id: care_calendar_id, ACTIVE, notUsersForvill, EXPIRY_DATE_CLASSIFIER, IS_POSTGRES: isPostgres };

    let query = getCompiledQuery("activitybyid", templateParameters);
    const queryBuilder = await dbConnections().main.manager.createQueryBuilder().from(`(${query})`, "activity-by-id").setParameters(queryParameters);
    
    let activityDetails = await queryBuilder.select("*").execute();
    postProcessRecords(undefined, activityDetails, { json_columns: ['activity_name_l10n', 'animal_type_json', 'calendar_activity_status_name_json', 'customer_name_l10n', 'paravet_name', 'village_name_l10n', 'vet_name','document_information' ,'client_document_information'] });

    if (activityDetails.length === 0) return {};

    activityDetails = activityDetails[0];
    
    return activityDetails;
    
  } catch (error) {
    throw error;
  }
}

module.exports = {
    ActivityPrescriptionUpdateServiceV2,
    ActivityObservationUpdateSeviceV2,
    ActivityDiagnosisUpdateServiceV2,
    activitySummaryV2,
    activityServiceDocumentData
}