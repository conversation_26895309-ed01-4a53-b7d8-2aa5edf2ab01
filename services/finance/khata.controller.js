const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm');
const { TRANSACTION_DEBIT ,TRANSACTION_CREDIT , ACTIVE } = require("../../utils/constant");
const { param } = require('express-validator');
let transaction_category_reference = [1000930001,1000930002,**********]
const creteNewKhata = async (data,params) =>{
    const { object } = params.route
    const { transaction_category ,amount,transaction_date,party_name,service_type ,staff_id ,notes} = data ;
    //validate khata type
    if( !transaction_category_reference.includes(transaction_category) ) {
        throw new Error("transcation type doesnt exist")
    }
    switch (object) {
        case "in":
            let inData = {
                transaction_category:transaction_category,
                account_id: "KHATA",
                amount: amount,
                transaction_date: transaction_date,
                entity_name: party_name,
                entity_service: service_type,
                staff_uuid: staff_id,
                transaction_type: TRANSACTION_CREDIT,
                transaction_description : {notes: notes}
            }
             await saveTransactionLedger(inData)
             return {success: "true"}
        case "out":
            let outData = {
                transaction_category:transaction_category,
                account_id: "KHATA",
                amount: amount,
                transaction_date: transaction_date,
                entity_name: party_name,
                entity_service: service_type,
                staff_uuid: staff_id,
                transaction_type: TRANSACTION_DEBIT,
                transaction_description : { notes: notes }


            }
             await saveTransactionLedger(outData)
             return {success: "true"}


    }
}

const saveTransactionLedger = async (data) => {
    try {
        let result = await dbConnections().main.manager.transaction(async (transactionalEntityManager) => {
            return await transactionalEntityManager.getRepository('transaction_ledger').insert(data)
        })
        return result
    }
    catch (error) {
        throw new Error("Could not save transaction")
    }
}

const getKhataDashboard = async (staff_id) => {
    try{
        let dashboard = []
        const khataCategoryDetails = await dbConnections().main.repos["ref_reference"].find({
            where: [{ reference_category_id: 10009300, active: ********** }],
            select: { reference_name_l10n: true, reference_id: true ,field_name : true ,reference_information :true},
            orderBy: { reference_id : 'ASC'}
        })
        for (let i=0 ;i < khataCategoryDetails.length; i++){
            let total_debit = 0
            let total_credit = 0
            let debitQuery = `
              SELECT COALESCE(sum(amount),0) as total_debit from main.transaction_ledger where 
              transaction_category = ${khataCategoryDetails[i].reference_id} and 
              transaction_type= ${TRANSACTION_DEBIT} and active = ${ACTIVE}  and account_id = 'KHATA'
              and staff_uuid = '${staff_id}'
            `;
            let debit = await dbConnections().main.manager.query(debitQuery, [])
             total_debit = debit.length ?  debit[0].total_debit :0

            let creditQuery = `
            SELECT COALESCE(sum(amount),0) as total_credit from main.transaction_ledger where 
            transaction_category = ${khataCategoryDetails[i].reference_id} and 
            transaction_type= ${TRANSACTION_CREDIT} and active = ${ACTIVE}  and account_id = 'KHATA'
            and staff_uuid = '${staff_id}'
          `;
          let credit = await dbConnections().main.manager.query(creditQuery, [])
             total_credit = credit.length ?  credit[0].total_credit :0
             dashboard.push({
                id : khataCategoryDetails[i].reference_id,
                reference_information: khataCategoryDetails[i].reference_information,
                reference_name_l10n: khataCategoryDetails[i].reference_name_l10n,
                credit : total_credit,
                debit : total_debit,
                balance : total_credit - total_debit
             })
        }
    return dashboard
    }catch (error) {
        throw new Error("failed to fetch data")
    }
}

const getKhataListing = async (data, params) =>{
    try {
        let { staff_id , transaction_category} = data ;
        if( !transaction_category_reference.includes(transaction_category) ) {
            throw new Error("transcation type doesnt exist")
        }
        let response= await dbConnections().main.repos["transaction_ledger"].find({
            where: [{ staff_uuid: staff_id, active: ********** ,transaction_category , account_id : "KHATA"}],
            orderBy: { created_at : 'DESC'}
        })
        const loadReference = await dbConnections().main.repos["ref_reference"].find({
            where: [{ reference_category_id: ********, active: ********** }],
            select: { reference_name_l10n: true, reference_id: true },
        })
        response= response.map((ledger)=> {
            let transaction_type_name = loadReference.find((ref) => ref.reference_id === ledger.transaction_type)
            return {...ledger,transaction_type_name: transaction_type_name ? transaction_type_name.reference_name_l10n : ledger.transaction_type}
        })
        return response
    } catch (error) {
        throw new Error("failed to fetch data")
    }
}

module.exports = {
    creteNewKhata,
    getKhataDashboard,
    getKhataListing
}