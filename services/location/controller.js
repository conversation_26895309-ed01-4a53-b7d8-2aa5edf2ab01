const { postProcessRecords } = require('@krushal-it/ah-orm')
const { getStates, getDistrictByStateId, getTalukByDistrictId, getVillageByTalukaId } = require('../../queries/location')
const { errorLog } = require('@krushal-it/mobile-or-server-lib')
const getStateList = async (params) => {
  try {
    let select = ['state_name_l10n', 'state_id', 'state_short_code', 'state_census_id']
    let stateOptions = { select }
    if (typeof params.name_start_with === 'string') {
      stateOptions['name_start_with'] = params.name_start_with
    }
    let data = await getStates(stateOptions)
    postProcessRecords(undefined, data.data, { json_columns: ['state_name_l10n'] })
    return data
  } catch (error) {
    errorLog('error in getStateList:13', params, { message: error.message })
    return { result: false, message: error.message }
  }
}
const getDistrict = async (params) => {
  try {
    let districtOptions = { state_id: params.state_id }
    let select = ['district_id', 'district_census_id', 'district_name_l10n', 'district_short_code']
    districtOptions['select'] = select
    let data = await getDistrictByStateId(districtOptions)
    postProcessRecords(undefined, data.data, { json_columns: ['district_name_l10n'] })
    return data
  } catch (error) {
    errorLog('error in getDistrict:25', params, { message: error.message })
    return { resul: false, message: error.message }
  }
}

const getTaluka = async (params) => {
  try {
    let talukaOptions = { district_id: params.district_id }
    let select = ['taluk_id', 'district_id', 'taluk_census_id', 'taluk_short_code', 'taluk_name_l10n']
    talukaOptions['select'] = select
    let data = await getTalukByDistrictId(talukaOptions)
    postProcessRecords(undefined, data.data, { json_columns: ['taluk_name_l10n'] })
    return data
  } catch (error) {
    errorLog('error in getTaluk:38', params, { message: error.message })
    return { resul: false, message: error.message }
  }
}

const getVillage = async (params) => {
  try {
    let villageOptions = { taluka_id: params.taluka_id }
    let select = ['village_id', 'taluk_id', 'village_census_id', 'village_name_l10n', 'village_short_code']
    villageOptions['select'] = select
    let data = await getVillageByTalukaId(villageOptions)
    postProcessRecords(undefined, data.data, { json_columns: ['village_name_l10n'] })
    return data
  } catch (error) {
    errorLog('error in getVillage:53', params, { message: error.message })
    return { result: false, message: error.message }
  }
}
module.exports = { getStateList, getDistrict, getTaluka, getVillage }
