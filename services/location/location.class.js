const { getStateList, getDistrict, getTaluka, getVillage } = require("./controller");
const {errorLog} = require('@krushal-it/mobile-or-server-lib')

class LibLocationService {
  async find(params) {
    try {
      const { type } = params.route;
      let data;
      switch (type) {
        case "state":
          data = await getStateList(params.query);
          return data;
        case "district":
          data = await getDistrict(params.query);
          return data;
        case "taluka":
          data = await getTaluka(params.query);
          return data;
        case "village":
          data = await getVillage(params.query);
          return data;
        default: throw new Error("invalid location type");
      }
    } catch (error) {
      errorLog("error in LibLocationService:24",params,{message:error.message})
      return { result: false, message: error.message };
    }
  }
}

module.exports = { LibLocationService };