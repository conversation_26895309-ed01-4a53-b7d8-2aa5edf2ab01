const { validateUUID,generateUUID } = require('@krushal-it/mobile-or-server-lib')
const { configurationJSON } = require('@krushal-it/common-core')
const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm')
const { RELATION_WAREHOUSE_BLOCKQTY,INACTIVE,ACTIVE,REQUISITION_DRAFT,RELATION_REFERENCE } = require('../../utils/constant')
const moment = require('moment')
const {getBlockQtyByMedAndWareUUID,insertToWarehouseBlockQty,updateWarehouseBlockQty,getMedBySourceID}= require('../../queries/warehouse')
const {addInventory}= require('../inventory_ledger/controller')
const {getInventoryKit}=require('../../queries/inventory_ledger')
const {addRequisition}=require('../requisition/controller')
const { isArray } = require('lodash')
const { sendResponse } = require('../../utils/response')
const { loadMeds } = require('../common/common.class')
const addUpdateMedForWarehouse = async (data,params,transactionalEntityManager)=>{
  // return new Promise(async (resolve,reject)=>{
    try{
      let medicines=[]
      let med=[]
      for(let i=0;i<data.medicines.length;i++){
        let indx=medicines.findIndex(item=>item.medicine_id==data.medicines[i].medicine_id)
        if(indx==-1){
          medicines.push({
            warehouse_type_id:data.warehouse_type_id,
            warehouse_uuid:data.warehouse_uuid,
            source_type_id:data.source_type_id,
            source_uuid:data.source_uuid,
            medicine_id:data.medicines[i].medicine_id,
            unit:data.medicines[i].unit,
            // blocked_qty:(data.medicines[i].days?data.medicines[i].days:1)*(data.medicines[i].frequency?data.medicines[i].frequency:1)*(data.medicines[i].dosage?data.medicines[i].dosage:0),
            blocked_qty:data.medicines[i].blocked_qty?data.medicines[i].blocked_qty:((data.medicines[i].days?data.medicines[i].days:1)*(data.medicines[i].frequency?data.medicines[i].frequency:1)*(data.medicines[i].dosage?data.medicines[i].dosage:0)),
            last_modifying_user_id:params.user_id
          })
          med.push(data.medicines[i].medicine_id)
        }
        else{
          medicines[indx].blocked_qty+=(data.medicines[i].days?data.medicines[i].days:1)*(data.medicines[i].frequency?data.medicines[i].frequency:1)*(data.medicines[i].dosage?data.medicines[i].dosage:0)
        }
      // let checkmed=getBlockQtyByMedAndWareUUID(data.warehouse_uuid,data.medicine_id)
      // if(checkmed) {
      //   let updateblockqty=updateWarehouseBlockQty({blocked_qty:checkmed.blocked_qty+data.blocked_qty},checkmed)
      //   return updateblockqty
      // }
      // else {
      //   let addblockqty=insertToWarehouseBlockQty(data)
      //   return addblockqty
      // }
      }
      let checkmed = []
      let checkmedicines = []
      if(data.source_uuid) checkmed = await getMedBySourceID(data.source_uuid,transactionalEntityManager)
      let uniquecheckmed = [...new Map(checkmed.map(item => [item['medicine_id'], item])).values()]
      for(let i=0;i<uniquecheckmed.length;i++){
        let arrfilter = checkmed.filter(item => item.medicine_id==uniquecheckmed[i].medicine_id)
        if((arrfilter.length)%2!=0) checkmedicines.push(arrfilter[arrfilter.length-1])
      }
      console.log('checkmedicines',checkmedicines,uniquecheckmed)
      console.log('medicines',medicines)
      // await dbConnections().main.manager.transaction(async (transactionalEntityManager) => {
        if(!checkmedicines || (checkmedicines && checkmedicines.length==0)){
          let addblockqty=await insertToWarehouseBlockQty(medicines,transactionalEntityManager)   
        }
        else{
          let diff_block_items = checkmedicines.filter(({ medicine_id: id1 }) => !medicines.some(({ medicine_id: id2 }) => id2 === id1));
          console.log('diff_block_items==>',diff_block_items)
          for(let i=0;i<diff_block_items.length;i++){
            let update_line_item=await updateWarehouseBlockQty({active:INACTIVE},{warehouse_blockqty_id: diff_block_items[i].warehouse_blockqty_id},transactionalEntityManager)
          }
          // let block_items = medicines.filter(({ medicine_id: id1 }) => checkmedicines.some(({ medicine_id: id2 }) => id2 === id1));
          // console.log('block_items==>',block_items)
          // for(let i=0;i<block_items.length;i++){
          for(let i=0;i<medicines.length;i++){
            let index=checkmedicines.findIndex(item=>item.medicine_id==medicines[i].medicine_id)
            if(index==-1){
              let addblockqty=await insertToWarehouseBlockQty(medicines[i],transactionalEntityManager)
            }
            else{
              let update_line_item=await updateWarehouseBlockQty(medicines[i],{warehouse_blockqty_id: checkmedicines[index].warehouse_blockqty_id},transactionalEntityManager)
            }
          }
        }
        if(med.length>0){
          let manager = transactionalEntityManager ? transactionalEntityManager : dbConnections().main.manager

          let inv_kit_query = await getInventoryKit(data.warehouse_uuid,med,transactionalEntityManager)
          let inv_kit = await manager.query(inv_kit_query)
          console.log('inv_kit',inv_kit)
          if(!isArray(inv_kit)) inv_kit=[]
          let req_line_items=[]
          // let selmedicines = await getAllMedicines(med,transactionalEntityManager)
          let loadmeds= await loadMeds(med)
          medicines = Object.values(loadmeds)
          console.log('med==>',med,selmedicines,data.warehouse_uuid,medicines)
          for(let j=0;j<selmedicines.length;j++){
            let index=inv_kit.findIndex(item=>item.medicine_id==selmedicines[j].medicine_id)
            let index2=medicines.findIndex(item=>item.medicine_id==selmedicines[j].medicine_id)
            selmedicines[j].medicine_pack_size=parseFloat(selmedicines[j].medicine_pack_size)
            selmedicines[j].medicine_mrp=parseFloat(selmedicines[j].medicine_mrp)
            if(index==-1){
              let qty=Math.floor(medicines[index2].blocked_qty/selmedicines[j].medicine_pack_size)
              qty += medicines[index2].blocked_qty % parseFloat(selmedicines[j].medicine_pack_size) === 0 ? 0 : 1
              req_line_items.push({
                medicine_id: selmedicines[j].medicine_id,
                quantity: qty*parseFloat(selmedicines[j].medicine_pack_size),
                unit: medicines[index2].unit?medicines[index2].unit:selmedicines[j].medicine_unit.ul
              })
            }
            else{
              if(!inv_kit[index].balance) inv_kit[index].balance=0
              else inv_kit[index].balance=parseFloat(inv_kit[index].balance)
              if(!inv_kit[index].blocked_qty) inv_kit[index].blocked_qty=0
              else inv_kit[index].blocked_qty=parseFloat(inv_kit[index].blocked_qty)
              if(!inv_kit[index].requisition_qty) inv_kit[index].requisition_qty=0
              else inv_kit[index].requisition_qty=parseFloat(inv_kit[index].requisition_qty)
              console.log('inv_kit_item_count',inv_kit[index].blocked_qty,inv_kit[index].balance,inv_kit[index].requisition_qty)
              if(inv_kit[index].blocked_qty>(inv_kit[index].balance+inv_kit[index].requisition_qty)){
                let add_qty=inv_kit[index].blocked_qty-(inv_kit[index].balance+inv_kit[index].requisition_qty)
                let qty=Math.floor(add_qty/selmedicines[j].medicine_pack_size)
                qty += add_qty % parseFloat(selmedicines[j].medicine_pack_size) === 0 ? 0 : 1
                req_line_items.push({
                  medicine_id: inv_kit[index].medicine_id,
                  quantity: qty*parseFloat(selmedicines[j].medicine_pack_size),
                  unit: medicines[index2].unit?medicines[index2].unit:selmedicines[j].medicine_unit.ul
                })
              }
            }
            
          }
          if(req_line_items.length>0){
            let requisition={
              requisitioner_type_id: data.warehouse_type_id,
              requisitioner_uuid: data.warehouse_uuid,
              approver_type_id: null,
              approver_uuid: null,
              fullfiller_type_id: null,
              fullfiller_uuid: null,
              requisition_line_items: req_line_items,
              status_id: REQUISITION_DRAFT,
            }
            console.log('adding requisition==>',requisition)
            // let create_req=await addRequisition(requisition,params,transactionalEntityManager)
          }
        }
        return medicines
        // resolve(medicines)
      // })
    }
    catch(e){
      console.log('addMedForWarehouse',e)
      // reject(e)
      throw e
    }
  // })
}

const removeMedFromWarehouse = async (data,params,transactionalEntityManager)=>{
  // return new Promise(async (resolve,reject)=>{
    try{
      // let {token}=params.headers
      let { user_id , user_type } = params
      let medicines=[]
      let med=data.medicines.map(item=>item.medicine_id)
      let manager = transactionalEntityManager ? transactionalEntityManager : dbConnections().main.manager
      let inv_kit_query = await getInventoryKit( user_id,med,transactionalEntityManager)
      let inv_kit = await manager.query(inv_kit_query)
      // let mederrors=[]
      console.log('inv_kit',inv_kit)
      for(let i=0;i<data.medicines.length;i++){
            medicines.push({
              // warehouse_type_id:data.warehouse_type_id,
              // warehouse_uuid:data.warehouse_uuid,
              warehouse_type_id: user_type,
              warehouse_uuid: user_id,
              source_type_id:data.source_type_id,
              source_uuid:data.source_uuid,
              medicine_id:data.medicines[i].medicine_id,
              unit:data.medicines[i].unit,
              unblocked_qty:data.medicines[i].unblocked_qty?data.medicines[i].unblocked_qty:0,
              last_modifying_user_id:params.user_id
            })

            let inventory_ledger={
              medicine_id:data.medicines[i].medicine_id,
              // warehouse_type_id:data.warehouse_type_id,
              // warehouse_uuid:data.warehouse_uuid,
              warehouse_type_id: user_type,
              warehouse_uuid : user_id,
              source_type_id:data.source_type_id,
              source_uuid:data.source_uuid,
              unit:data.medicines[i].unit,
              debit_qty:data.medicines[i].unblocked_qty,

            }
            console.log('add inv from warehouse controller',inventory_ledger)
            let add_inv_ledger=await addInventory(inventory_ledger,params,transactionalEntityManager) 
      }
        console.log('medicines',medicines)

        //uncomment the below lines of code

        let removeblockqty=null
        if(medicines.length>0){
          removeblockqty=await insertToWarehouseBlockQty(medicines,transactionalEntityManager)
        }

    
        return {removeblockqty}

    }
    catch(e){
      console.log('removeMedFromWarehouse',e)
      // reject(e)
      throw e
    }
  // })
}

module.exports= {addUpdateMedForWarehouse,removeMedFromWarehouse}
