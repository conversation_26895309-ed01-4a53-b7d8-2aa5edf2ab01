const { generateUUID, validateUUID, errorLog } = require('@krushal-it/mobile-or-server-lib');
const { dbConnections} = require('@krushal-it/ah-orm')
const _ = require('lodash');
const moment = require('moment')
const { addUpdateMedForWarehouse,removeMedFromWarehouse } = require("./controller");
const { sendResponse } = require('../../utils/response');
const { requestValidation } = require('../../utils/requestvalidation');
const { RELATION_WAREHOUSE_BLOCKQTY } = require('../../utils/constant');

class LibWarehouseService {
    constructor(){}
    
    async create(data, params) {
        try {
          let { token } = params.headers
          console.log('add req==>',data,params)
          let validatereq=requestValidation(data,RELATION_WAREHOUSE_BLOCKQTY,'add')
          if(!validatereq) return sendResponse(400,'Invalid Request Body','error')
          let warehouseblockmed = await addUpdateMedForWarehouse(data,token);
          // return { warehouseblockmed };
          return sendResponse(200,warehouseblockmed)
        } catch (error) {
          // errorLog("error in WarehouseService:1",error,{message:error.message})
          // if (error.code) return error.message;
          errorLog("error in WarehouseService:1",error)
          return sendResponse(500,error,'error')
        }
    }

    async removeBlockQty(data,params){
        try {
            let { token } = params.headers
            console.log('add req==>',data,params)
            let validatereq=requestValidation(data,RELATION_WAREHOUSE_BLOCKQTY,'remove')
            if(!validatereq) return sendResponse(400,'Invalid Request Body','error')
            let warehouseunblockmed = await removeMedFromWarehouse(data,token);
            return sendResponse(200,warehouseunblockmed)
          } catch (error) {
            // errorLog("error in removeBlockQty",error,{message:error.message})
            // if (error.code) return error.message;
            errorLog("error in removeBlockQty",error)
            return sendResponse(500,error,'error')
          }
    }
}

module.exports = { LibWarehouseService};