const { dbConnections, preProcessRecords, postProcessRecords } = require('@krushal-it/ah-orm')
const { jsonColumn } = require('@krushal-it/ah-orm/utils/ormHelper')


const config = {
  2000000277: { data: { classifier_id: true, value_int: true, value_reference_id: true, value_json: true } , jsonColumn : ['value_json']},
  2000000427: { data: { value_string_256: true } , jsonColumn : []},
  2000000426: { data: {value_json: true} , jsonColumn: ['value_json']},
  2000000269: { data: {value_date: true} , jsonColumn: []}
}

const getActivitiesObservation = async (care_calendar_id,transaction) => {
  try {
    let classifierData = []
    for (let conf of Object.keys(config)) {
      let activityClassification = await transaction.getRepository('care_calendar_classification').find({
        where: { classifier_id: conf, care_calendar_id: care_calendar_id },
        select: {...config[conf].data , classifier_id: conf },
      })
      activityClassification = postProcessRecords(undefined , activityClassification , { json_columns : config[conf].jsonColumn})
      classifierData= classifierData.concat(activityClassification)
    }
    return classifierData
  } catch (error) {
    throw new Error('error while getting classification data')
  }
}

const saveActivityClassification = async (care_calendar_id, folloupId, transaction) => {
  try {
    let activitiesObservation = await getActivitiesObservation(care_calendar_id, transaction)
    let classificationData = []
    const entityCareCalendar = dbConnections().main.entities['care_calendar_classification']
    if (activitiesObservation && activitiesObservation.length) {
      for (let activityClassification of activitiesObservation) {
        let data = {
          ...activityClassification,
          care_calendar_id: folloupId,
        }
        classificationData.push(data)
      }
      const preProcessedValues = preProcessRecords(entityCareCalendar, classificationData, entityCareCalendar.additionalAttributes)
      return await transaction.save('care_calendar_classification', preProcessedValues)
      
    }
    return []
  } catch (error) {
    throw new Error('could not save classification')
  }
}


module.exports =  { saveActivityClassification }