const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm')
const {  loadClassificationData ,saveClassificationData} = require('../common/classification.helper');
const moment = require("moment");
const { ACTIVE ,RELATION_DOC, INACTIVE} = require('../../utils/constant')
const { classifiers ,entity_type ,observation_question, document_type} = require('../../ENUMS')
const { In } = require('typeorm')
const { configurationJSON } = require('@krushal-it/common-core')
const taskRepositoryInstance = require('../../repositories/taskRepository.js')

const insertToDocument = async (value,transaction) => {
    const isPostgres = (configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1) ? true : false
    const entity_document = dbConnections().main.entities[RELATION_DOC];
    const preProcessedValue = preProcessRecords(entity_document, value, entity_document.additionalAttributes)
    let qb = await transaction.createQueryBuilder()
        .insert().into(RELATION_DOC).values(preProcessedValue)
    if (isPostgres) {
        qb = qb.returning("document_id")
    }
    let data =await qb.execute()
    if (isPostgres) {
        if(data.raw){
            data = data.identifiers.map(doc => doc.document_id)
        }

        return data
    } else {
        if (data.identifiers) {
            data = data.identifiers.map(doc => doc.document_id)
            // data = data.identifiers[0].document_id
        }
        return data
    }
};

const getMediaData = async (document_id) => {
    let loadDocument = await dbConnections().main.repos['document'].find({
        where: {
          document_id: In(document_id),
          active: ACTIVE
        },
        select: { document_information: true }
      })
      loadDocument = postProcessRecords(null, loadDocument, { json_columns: ['document_information'] })
      return loadDocument.map(doument => doument.document_information)
}

const saveMediaData = async (data, transaction) => {
    try {
        let { document_information , care_calendar_id } = data
        let valueArray = [ ]
        if(!Array.isArray(document_information) && !document_information.length) {
            return []
        }
        for(let doc_information of document_information){
            let value = {
                document_type_id: document_type.OBSERVATION,
                entity_1_type_id: entity_type.TASK,
                entity_1_entity_uuid: care_calendar_id,
                document_information: doc_information
            }
            valueArray.push(value)
        }
       let document = await insertToDocument(valueArray , transaction)
       return document

    } catch (error) {
        throw new Error("failed to insert to document")
    }
   
}

const saveCategoryConfig = async (care_calendar_id , observation_category_id , transaction) => {
    try {
       
        let categoryConfiguration = await loadCategoryConfiguration(care_calendar_id, observation_category_id, transaction)
        let saveClassification =    await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', care_calendar_id, { observation_category_configuration: categoryConfiguration});
        // throw new Error("askdasih ")
        return "true"

    } catch (error) {
        throw new Error("error while insertingcategory config")
    }
}

const loadCategoryConfiguration = async (care_calendar_id, observation_category_id , transaction) => {
    try {
        let observationCategory = new ObservationCategoryService()
        let categoryConfig = await observationCategory.getObservationCategory(care_calendar_id, transaction)
        let observationCategoryCongig = {...categoryConfig[observation_category_id] , status: "completed"}
        categoryConfig[observation_category_id] = observationCategoryCongig
        return categoryConfig
    } catch (error) {
        throw new Error("unable to load Category")
    }
}

const inactivateObservation = async (observation_category_id, value_reference_id ,care_calendar_id,transaction) => {
  try {
     let inactiveObject = {
        active: INACTIVE
     }
     const entity_entity = dbConnections().main.entities['care_calendar_classification']
     const preProcessedValue = preProcessRecords(entity_entity, inactiveObject, entity_entity.additionalAttributes)
    return await transaction.getRepository('care_calendar_classification').update(
      {
        value_int: observation_category_id,
        care_calendar_id,
        value_reference_id,
        classifier_id: classifiers.OBSERVATION_ANSWER
      },
      preProcessedValue
    ) 
  } catch (error) {
    console.log(error)
  }
}
class ObservationServiceV2 {
  async save(data, params) {
    try {
      const { observation_category_id, care_calendar_id, observations, config } = data
      const { user_id } = params.headers.token
      if (!Array.isArray(observations) && !observations.length) throw new Error('Observations cannot be empty')
      if (!Array.isArray(config) && !config.length && config.length !== observations.length) throw new Error('config should be same as observation')
      return await dbConnections().main.manager.transaction(async transaction => {

        const taskDetails = await taskRepositoryInstance.getTaskDetails(transaction, care_calendar_id)
        let observationsData = []
        for (let findConfig of config) {
          const observation = observations.find(observation => findConfig.reference_id == observation.question_id)
          let observationData = {
            care_calendar_id: care_calendar_id,
            classifier_id: classifiers.OBSERVATION_ANSWER,
            value_json: { config: findConfig, data: observation ? observation :  {answers: [] , question_id : findConfig.reference_id} },
            value_reference_id: findConfig.reference_id ,
            value_int: observation_category_id,
            last_modifying_user_id: user_id
          }
          
          try {
            if (observation.question_id === observation_question.CATTLE_WEIGHT) {
              if (Array.isArray(observation.answers) && observation.answers.length) {
                const weight = parseInt(observation.answers.length)
                if (!isNaN(weight)) {
                  const animal_id = await taskRepositoryInstance.getAnimalId(transaction, taskDetails[care_calendar_id])

                  if (animal_id) {
                    const classificationData = {
                      animal_weight_1: weight,
                    }
                    await saveClassificationData(transaction, 'ANIMAL_CLASSIFICATION', animal_id, classificationData)
                  }
                }
              }
            }
          } catch (error) {
            console.log('error in saving body weight', error)
          }

          if (findConfig.reference_id == observation_question.DOCUMENT) {
            let getDocumentIds = await saveMediaData({document_information: observation.answers , care_calendar_id} , transaction)
            observationData.value_json = { config: findConfig, data: { document_ids: getDocumentIds ,question_id: findConfig.reference_id } }
          }
          if (observation && observation.care_calendar_classification_id) {
            observationData['care_calendar_classification_id'] = observation.care_calendar_classification_id
          }
          if(!observation) {
            //if FE is not sending edited observation inactivating this
            await inactivateObservation(observation_category_id,findConfig.reference_id,care_calendar_id,transaction)
          }
          observationsData.push(observationData)
        }
        const entity_entity = dbConnections().main.entities['care_calendar_classification']
        const preProcessedValue = preProcessRecords(entity_entity, observationsData, entity_entity.additionalAttributes)
        await saveCategoryConfig(care_calendar_id, observation_category_id , transaction)
        await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', care_calendar_id, { observation_version: 'v2'})
        const observation_date = await saveClassificationData(transaction, 'CARE_CALENDAR_CLASSIFICATION', care_calendar_id, { observation_date: moment.utc().format("YYYY-MM-DD HH:mm:ss.SSS") })

        return await transaction.save('care_calendar_classification', preProcessedValue)
      })
    } catch (error) {
      return -1
    }
  }
  async getObservationAndConfig(observation_category_id, care_calendar_id) {
    try {
      let loadObservationConfig = await dbConnections().main.repos['ref_reference'].find({
        where: {
          reference_id: observation_question.QUESTION_CATEGOEY,
          active: ACTIVE
        },
        select: { reference_id: true, reference_information: true }
      })
      loadObservationConfig = postProcessRecords(null, loadObservationConfig, { json_columns: ['reference_information'] })
      if (!loadObservationConfig && !loadObservationConfig.length) return -1
      loadObservationConfig = loadObservationConfig[0].reference_information
      let loadQuestions = loadObservationConfig[observation_category_id]
      let questionIds = Object.keys(loadQuestions?.observation)
      //
      let loadExistingObservationConfigAndData = await dbConnections().main.repos['care_calendar_classification'].find({
        where: {
          classifier_id: classifiers.OBSERVATION_ANSWER,
          value_int: observation_category_id,
          active: ACTIVE,
          care_calendar_id: care_calendar_id
        },
        select: { value_json: true, care_calendar_classification_id: true, value_reference_id: true },
        order: {
          value_reference_id: 'ASC' 
        },

      })
      loadExistingObservationConfigAndData = postProcessRecords(null, loadExistingObservationConfigAndData, { json_columns: ['value_json'] })
      let config = []
      let data = []
      if (loadExistingObservationConfigAndData && loadExistingObservationConfigAndData.length) {
        for (let existingData of loadExistingObservationConfigAndData) {
          let answersData = existingData.value_json.data
          if (existingData.value_reference_id === observation_question.DOCUMENT && answersData?.document_ids?.length) {
            let documentData = await getMediaData(answersData.document_ids)
            answersData['answers'] = documentData 
          }
          if(existingData.value_reference_id === observation_question.DOCUMENT && !answersData?.document_ids?.length){
            answersData['answers'] = [] 
          }
          config.push({ ...existingData.value_json.config, reference_id: existingData.value_reference_id })
          data.push({ ...answersData, reference_id: existingData.value_reference_id, care_calendar_classification_id: existingData.care_calendar_classification_id })
        }
      } else {
        let loadConfigFromReference = await dbConnections().main.repos['ref_reference'].find({
          where: {
            reference_id: In(questionIds),
            active: ACTIVE
          },
          select: { reference_id: true, reference_information: true },
          order: {
            reference_id: 'ASC' 
          },
        })
        loadConfigFromReference = postProcessRecords(null, loadConfigFromReference, { json_columns: ['reference_information'] })
        for (let configData of loadConfigFromReference) {
          config.push({ ...configData.reference_information, reference_id: configData.reference_id })
        }
      }

      return {
        care_calendar_id,
        config: config,
        data: data
      }
    } catch (error) {
      return -1
    }
  }
}

class ObservationCategoryService {
  async getObservationCategory(care_calendar_id , transaction = null) {
    try {    
        let manager = transaction ? transaction : dbConnections().main.manager
        const loadRefFormClassification = await loadClassificationData('CARE_CALENDAR_CLASSIFICATION', care_calendar_id, ['observation_category_configuration']);
        //  uncomment if we need to use transaction with loading classification
        // let loadRefFormClassification = await manager.getRepository('care_calendar_classification').find(
        //   {  
        //     where : { 
        //         care_calendar_id : care_calendar_id,
        //         active : ACTIVE,
        //         classifier_id: 2000000426
        //      },
        //      select : {
        //         value_json : true
        //      }
        //   }
        // )
        // loadRefFormClassification = postProcessRecords(null , loadRefFormClassification , { json_columns: ['value_json']})
        // if(loadRefFormClassification && loadRefFormClassification.length) return loadRefFormClassification[0].value_json
          if (loadRefFormClassification.observation_category_configuration) return  loadRefFormClassification.observation_category_configuration
          let loadConfigFromReference = await manager.getRepository('ref_reference').find({
            where: {
              reference_id: observation_question.QUESTION_CATEGOEY,
              active: ACTIVE
            },
            select: { reference_id: true, reference_information: true }
          })
          loadConfigFromReference = postProcessRecords(null, loadConfigFromReference, { json_columns: ['reference_information'] })
          if(loadConfigFromReference && loadConfigFromReference.length) return loadConfigFromReference[0].reference_information
          return "refrence not loaded"
      
    } catch (error) {
        return error
    }
 }
}

module.exports = { ObservationServiceV2 ,ObservationCategoryService }
