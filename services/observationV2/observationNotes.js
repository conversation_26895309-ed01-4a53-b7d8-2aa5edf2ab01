const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm')
const { INACTIVE ,ACTIVE} = require('../../utils/constant')
class ObservationNotes {
   saveObservationNote = async (data , params) => {
    try {
      let { observation_notes_1 , care_calendar_id} = data
      let { user_id , user_type }  = params.headers.token 
      return await dbConnections().main.manager.transaction(async transaction => {

          if(typeof observation_notes_1 === "string" ){ 
              let observationNoteDeactivateData =  { active: INACTIVE, last_modifying_user_id: user_id }
              observationNoteDeactivateData = preProcessRecords(dbConnections().main.entities["note"], observationNoteDeactivateData,null);
              if(observationNoteDeactivateData.note_id){
                delete observationNoteDeactivateData.note_id
              }
              let observationNoteDeactivate = await dbConnections().main.repos["note"].update(
                {note_type_id: 1000440006, entity_1_type_id: 1000220003, entity_1_uuid: care_calendar_id, active: ACTIVE },
                observationNoteDeactivateData
              );
    
          }
          let notes= {
            note: { ul: observation_notes_1 },
            note_type_id: 1000440006,
            entity_1_type_id: 1000220003,
            entity_1_uuid: care_calendar_id,
            creator_uuid: user_id,
            creator_type_id: user_type
    
          }
          notes = preProcessRecords(dbConnections().main.entities["note"], notes, { json_columns: ["note"] });
          return await transaction.getRepository("note").insert(notes); 
        }
      )
    } catch (error) {
        throw new Error("failed to insert")
    }

  }
  getObservationNote = async (care_calendar_id) => {
    try {
      let observationNote = await dbConnections().main.repos["note"].findOne({
        where: { note_type_id: 1000440006, entity_1_type_id: 1000220003, entity_1_uuid: care_calendar_id, active: ACTIVE },
        select: { note: true, creator_uuid: true, creator_type_id: true },
        order: {
          created_at: 'DESC' 
        }
      });
      observationNote = postProcessRecords(undefined, observationNote, { json_columns: ["note"] });
      return observationNote
    } catch (error) {
       throw new Error("could not get observation notes") 
    }
  }
}

module.exports = { ObservationNotes }