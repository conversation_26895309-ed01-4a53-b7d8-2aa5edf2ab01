const { KrushalError } = require('@krushal-it/common-core')
const { dbConnections } = require('@krushal-it/ah-orm')
const { getClassificationConfiguration, loadClassificationDataWithClassifierIds, saveClassificationData } = require('../common/classification.helper')
const uuid = require('uuid')
const { CONST_TYPES_STAFF } = require('../../utils/constant')
const { configurationJSON } = require('@krushal-it/common-core')
const { getDBConnections } = require('@krushal-it/ah-orm')
const _ = require('lodash')

//  CUD must be called with transactionalEntityManager
class LibAssetFeature {
  async create (data, params, transactionalEntityManager) { // post
    try {
      console.log('in create')
      console.log('called with data ', data, ' and params', params)
      const queryParameters = params.query || {}
      if (!data?.asset_id && !data?.asset_classification_id) {
        return { return_code: -101, message: 'asset_id or asset_classification_id is required' }
      }
      const conflictPaths = Object.keys(queryParameters) || []
      const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
      // const mainSchemaAddition = isPostgres ? 'main.' : ''
      const assetFeatureRepo = transactionalEntityManager.getRepository('asset_feature')
      const assetFeatures = await assetFeatureRepo.save([data])
      console.log('saved assetFeature', assetFeatures)
      const assetFeature = assetFeatures && assetFeatures.length > 0 ? assetFeatures[0] : null
      if (assetFeature && assetFeature?.asset_feature_id) { // assetFeature got created link with parent
        // need to commit if its new transactionalEntityManager

        // save all the classifications data
        const classificationData = _.cloneDeep(data) // copy the data

        let classifierAttributeArray = Object.keys(classificationData)
        const entitiesClassificationConfiguration = getClassificationConfiguration()
        const classificationConfiguration = entitiesClassificationConfiguration.ASSET_FEATURE_CLASSIFICATION
        const classifierAttributes = classificationConfiguration.ATTRIBUTE_LIST
        const validAttributes = Object.keys(classifierAttributes)

        classifierAttributeArray = classifierAttributeArray.filter((classifierAttribute) => { return validAttributes.includes(classifierAttribute) })
        const result = await saveClassificationData(transactionalEntityManager, 'ASSET_FEATURE_CLASSIFICATION', assetFeature.asset_feature_id, classificationData)
        if (result && result == true) {
          const savedData = await loadClassificationDataWithClassifierIds(transactionalEntityManager, 'ASSET_FEATURE_CLASSIFICATION', assetFeature.asset_feature_id, classifierAttributeArray)
          assetFeature.classifiersWithClassifierIds = savedData
        }
      }
      return { return_code: 0, data: assetFeature }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async get (id, params) {
    try {
      console.log('in get')
      console.log('called with id ', id, ' and params', params)
      const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
      const dbConnection = getDBConnections().main
      const newTransactionManager = null
      const mainSchemaAddition = isPostgres ? 'main.' : ''
      const assetFeatureRepo = transactionalEntityManager.getRepository('asset_feature')
      const asset_feature = await assetFeatureRepo.findOne({ where: { asset_feature_id: id } })
      return { return_code: 0, data: asset_feature }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async find (params, transactionalEntityManager = null) {
    console.log('in find')
    console.log('called with params', params)
    try {
      const queryParameters = params.query || {}
      const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
      const dbConnection = getDBConnections().main
      let newTransactionManager = null
      if (!transactionalEntityManager) { // no transactionalEntityManager passed
        newTransactionManager = await dbConnections.main.getConnection().manager
      }
      transactionalEntityManager = transactionalEntityManager || newTransactionManager
      const mainSchemaAddition = isPostgres ? 'main.' : ''
      const assetFeatureRepo = transactionalEntityManager.getRepository('asset_feature')
      const asset_feature = await assetFeatureRepo.find({ where: queryParameters })
      return { return_code: 0, data: asset_feature }
    } catch (error) {
      console.log(error)
      return { return_code: -101, message: error.message }
    }
  }

  async patch /* or is it update */(id, data, params) {
    console.log('in patch')
    console.log('called with id ', id, ' and params', params)
    return { return_code: -101, message: 'in patch' }
  }

  async update /* or is it update */(id, data, params) {
    console.log('in update')
    console.log('called with id ', id, ' and params', params)
    return { return_code: -101, message: 'in update' }
  }
}

module.exports = { LibAssetFeature }
