const { generateUUID, validateUUID, errorLog } = require('@krushal-it/mobile-or-server-lib');
const { dbConnections} = require('@krushal-it/ah-orm')
const _ = require('lodash');
const moment = require('moment')
const { getLatestInventory,getMedInventory,getInvLedger } = require("./controller");
const { sendResponse } = require('../../utils/response');

class LibInventoryLedgerService{
    async getInventoryKitList(params) {
        try {
          if(!params.query.id) return sendResponse(400,'ID required','error')
          const inventory_ledger = await getLatestInventory(params);
          return sendResponse(200,inventory_ledger)
        } catch (error) {
          // errorLog("error in getInventoryKitService:1",params,{message:error.message})
          // return { result: false, message:error.message };
          errorLog("error in getInventoryKitService:1",params,error)
          return sendResponse(500,error,'error')
        }
    }

    async getMedicineInventory(data,params){
      try {
        let med_inv = await getMedInventory(data,params);
        if('count' in med_inv) return med_inv
        else return sendResponse(200,med_inv)
      } catch (error) {
        // errorLog("error in getInventoryKitService:1",params,{message:error.message})
        // return { result: false, message:error.message };
        errorLog("error in getInventoryKitService:1",params,error)
        return sendResponse(500,error,'error')
      }
    }

    async getInventoryLedger(data,params){
      try {
        if(data.start && data.size && (data.start*data.size<0)) return sendResponse(400,'Invalid page number','error')
        let inv_ledger = await getInvLedger(data,params);
        if('count' in inv_ledger) return inv_ledger
        else return sendResponse(200,inv_ledger)
      } catch (error) {
        errorLog("error in getInventoryLedger:1",params,error)
        return sendResponse(500,error,'error')
      }
    }
}

module.exports={LibInventoryLedgerService}