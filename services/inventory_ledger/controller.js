const { validateUUID,generateUUID } = require('@krushal-it/mobile-or-server-lib')
const { configurationJSON } = require('@krushal-it/common-core')
const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm')
const { RELATION_REQUISITION,INACTIVE,ACTIVE } = require('../../utils/constant')
const { getInventoryKit,getLatestInventoryForUUIDMedid,insertInventoryLedger,updateInventoryLedger,getMedInventoryKit,getInventoryLedgerQuery } = require('../../queries/inventory_ledger')
const moment = require('moment')
const { sendResponse } = require('../../utils/response')
const { loadMeds } = require('../common/common.class')

const getLatestInventory =async(options)=>{
    // return new Promise(async (resolve,reject)=>{
        try{
            let id=options.query.id
            const ledgerquery = await getInventoryKit(id,null)
            let inventorylist = await dbConnections().main.manager.query(ledgerquery)
            inventorylist=inventorylist.filter(item=>parseFloat(item.balance)!=0)
            postProcessRecords(undefined, inventorylist, { json_columns: ['requisitioner_type_name_l10n','requisitioner_name_l10n','medicine_name_l10n','medicine_information'] });
            // let medicines = await getAllMedicines(null)
            let loadmeds= await loadMeds()
            let medicines = Object.values(loadmeds)
            for(let i=0;i<inventorylist.length;i++){
                let medindex=medicines.findIndex(item=>item.medicine_id == inventorylist[i].medicine_id)
                // let med={...medicines[medindex]}
                // delete med.medicine_id
                inventorylist[i]={ ...inventorylist[i], ...medicines[medindex]}
            }
            // return { data: inventorylist }
            // resolve(inventorylist)
            return inventorylist
        }
        catch(e){
            console.log('getLatestInventory',e)
            // reject(e)
            throw e
        }
    // })
}

const addInventory = async(data,params,transactionalEntityManager)=>{
    // return new Promise(async (resolve,reject)=>{
        console.log('addinventorydata',data)
        try{
            let values={...data}
            // let {token}=params.headers
            // let {user_id}=token.user_id
            values['last_modifying_user_id']= params.user_id
            values['inserted_at'] = new Date().toISOString()

            if(!('balance' in values)){
                let latestinv=await getLatestInventoryForUUIDMedid(values.warehouse_uuid,values.medicine_id,transactionalEntityManager)
                let latestinventoryforuuidmedid=latestinv[0]
                console.log('latestinventoryforuuidmedid',latestinventoryforuuidmedid)
                if(values.credit_qty) values['balance']=latestinventoryforuuidmedid && latestinventoryforuuidmedid.balance?parseFloat(latestinventoryforuuidmedid.balance)+values.credit_qty:values.credit_qty
                if(values.debit_qty) values['balance']=latestinventoryforuuidmedid && latestinventoryforuuidmedid.balance?parseFloat(latestinventoryforuuidmedid.balance)-values.debit_qty:(0-values.debit_qty)
            }
            let inventoryitem= await insertInventoryLedger(values,transactionalEntityManager)
            // resolve(inventoryitem)
            return inventoryitem
        }
        catch(e){
            console.log('addInventory',e)
            // reject(e)
            throw e
        }
    // })
}

const updateInventory = async(data,params,transactionalEntityManager)=>{
    // return new Promise(async (resolve,reject)=>{
        try{
            let values={...data}
            // let {token}=params.headers
            // let {user_id}=token.user_id
            values['last_modifying_user_id']= params.user_id
            let inventoryitem= await updateInventoryLedger(values,transactionalEntityManager)
            // resolve(inventoryitem)
            return inventoryitem
        }
        catch(e){
            console.log('updateInventory',e)
            // reject(e)
            throw e
        }
    // })
}

const getMedInventory = async(data,params)=>{
    try{
        let values=data?data:{}
        let id=params.query.id
        let staff_id=params.query.staff_id
        let filters={
            "med_id":params.query.id,
            "staff_id":params.query.staff_id,
            "staff_type_id":values.filters && values.filters.staff_type_l10n?values.filters.staff_type_l10n:[],
            "staff_name":values.filters && values.filters.staff_name?values.filters.staff_name[0]:null,
            "medicine_id":values.filters && values.filters.medicine_name_l10n?values.filters.medicine_name_l10n:[],
        }
        const ledgerquery = await getMedInventoryKit(filters)
        let inventorylist = await dbConnections().main.manager.query(ledgerquery)
        inventorylist=inventorylist.filter(item=>parseFloat(item.balance)!=0)
        postProcessRecords(undefined, inventorylist, { json_columns: ['staff_type_l10n','staff_name_l10n','medicine_name_l10n','medicine_information'] });
        let medlist=[]
        // let medicines = await getAllMedicines(values.filters && values.filters.medicines_name_110n?values.filters.medicines_name_110n:[])
        let loadmeds= await loadMeds()
        let medicines = Object.values(loadmeds)
        for(let i=0;i<inventorylist.length;i++){
            let index=medlist.findIndex(item=>item.medicine_id==inventorylist[i].medicine_id)
            let medindex=medicines.findIndex(item=>item.medicine_id == inventorylist[i].medicine_id)
            // let med={...medicines[medindex]}
            // delete med.medicine_id
            if(!id && !staff_id){
                if(index==-1){
                    medlist.push({
                        medicine_id:inventorylist[i].medicine_id,
                        medicine_name_l10n:inventorylist[i].medicine_name_l10n,
                        total_qty:parseFloat(inventorylist[i].balance),
                        // medicine_information:inventorylist[i].medicine_information
                        ...medicines[medindex]
                    })
                }
                else medlist[index].total_qty+=parseFloat(inventorylist[i].balance)
            }
            else medlist.push({...inventorylist[i],...medicines[medindex]})
        }

        let count=0
        let total_page = 1

        count = medlist?parseInt(medlist.length):0
        if (!isNaN(values.size) && !isNaN(values.start) && values.start>0) {
            let offset = values.size + values.start?values.start:0
            if (offset < 0) throw new Error('Invalid page number')
            medlist=medlist.splice(offset,values.size)
            total_page = parseInt(count / values.size)
            total_page += count % values.size === 0 ? 0 : 1
        }

        let medicinelist = await dbConnections().main.manager.query(`select medicine_id,medicine_name from main.ref_medicine where active=${ACTIVE}`)
        let med = medicinelist.map(item => {return {value:item.medicine_id,text:item.medicine_name}} )
        return{report:medlist,return_code:0,count:count,medicine_filter_values:med}
    }
    catch(e){
        console.log('getMedInventory',e)
        throw e
    }
}

const getInvLedger = async(data,params)=>{
    try{
        let values=data?data:{}
        let {token}=params.headers
        let filters={
            "il_inventory_ledger_id":values.filters && values.filters.inventory_ledger_id?values.filters.inventory_ledger_id[0]:null,
            "il_warehouse_uuid":values.filters && values.filters.warehouse_uuid?values.filters.warehouse_uuid:[],
            "il_warehouse_type":values.filters && values.filters.warehouse_type_l10n?values.filters.warehouse_type_l10n:[],
            "il_warehouse_name":values.filters && values.filters.warehouse_name?values.filters.warehouse_name[0]:null,
            "il_medicine_id":values.filters && values.filters.medicine_name_l10n?values.filters.medicine_name_l10n:[],
            // "il_medicine_name":values.filters && values.filters.medicine_name_l10n?values.filters.medicine_name_l10n[0]:null,
            "il_created_at_from":values.filters && values.filters.updated_at && values.filters.updated_at[0] && values.filters.updated_at[0].length>0?values.filters.updated_at[0][0]:null,
            "il_created_at_to":values.filters && values.filters.updated_at && values.filters.updated_at[0] && values.filters.updated_at[0].length>1?values.filters.updated_at[0][1]:null,
        }
        let invledgerlist = await getInventoryLedgerQuery(filters)
        // let medicines = await getAllMedicines(null)
        console.log('before invledgerlist',invledgerlist.length)
        postProcessRecords(undefined, invledgerlist, { json_columns: ['warehouse_type_l10n','warehouse_name_l10n','medicine_name_l10n'] });

        // for(let i=0;i<invledgerlist.length;i++){
        //     let medindex=medicines.findIndex(item => item.medicine_id == invledgerlist[i].medicine_id)
        //     invledgerlist[i]={...invledgerlist[i],...medicines[medindex]}
        // }

        let count=0
        let total_page = 1

        count = invledgerlist?parseInt(invledgerlist.length):0
        // if (!isNaN(values.start) && !isNaN(values.size)) {
        if (!isNaN(values.size) && !isNaN(values.start) && values.start>0) {
            // let offset = values.size * values.start
            let offset = values.size + values.start?values.start:0
            if (offset < 0) throw new Error('Invalid page number')
            invledgerlist=invledgerlist.splice(offset,values.size)
            total_page = parseInt(count / values.size)
            total_page += count % values.size === 0 ? 0 : 1
        }

        let medicines = await dbConnections().main.manager.query(`select medicine_id,medicine_name from main.ref_medicine where active=${ACTIVE}`)
        let med = medicines.map(item => {return {value:item.medicine_id,text:item.medicine_name}} )
        return{report:invledgerlist,return_code:0,count:count,medicine_filter_values:med}
    }
    catch(e){
        console.log('removeMedFromWarehouse',e)
        throw e
    }
}

module.exports= {getLatestInventory,addInventory,updateInventory,getMedInventory,getInvLedger}
