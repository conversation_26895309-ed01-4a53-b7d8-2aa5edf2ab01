const { generateUUID, validateUUID, errorLog } = require('@krushal-it/mobile-or-server-lib');
const { dbConnections} = require('@krushal-it/ah-orm')
const _ = require('lodash');
const moment = require('moment')
const { getRequisition,addRequisition,updateReq,getRequsitionStatus, getInvoiceDataForRequisition } = require("./controller");
const {REQUISITION_DRAFT, RELATION_REQUISITION_LINE_ITEMS}=require('../../utils/constant');
const { sendResponse } = require('../../utils/response');
const { requestValidation, validateRequisition } = require('../../utils/requestvalidation');

class LibRequisitionService {
  // async find(params) {
  //   try {
  //     const requsistion = await getRequisition(params.query);
  //     return requsistion;
  //   } catch (error) {
  //     errorLog("error in getRequisitionServiceforRequisitioner:1",params,{message:error.message})
  //     return { result: false, message:error.message };
  //   }
  // }

  async getReqList(data,params){
    try {
      // if(data.start && data.size && (data.start*data.size<0)) return sendResponse(400,'Invalid page number','error')
      let requisition = await getRequisition(data,params);
      // return requsistion;
      if('count' in requisition) return requisition
      else  return sendResponse(200,requisition)
    } catch (error) {
      // errorLog("error in getRequisitionService:1",params,{message:error.message})
      // return { result: false, message:error.message };
      errorLog("error in getRequisitionService:1",params,error)
      return sendResponse(500,error,'error')
    }
  }

  async create(data, params) {
    let { token } = params.headers
    try {
      console.log('add req==>',data,params)
      if(data.status_id!=REQUISITION_DRAFT && (!data.requisition_line_items || data.requisition_line_items && data.requisition_line_items.length==0)) return sendResponse(400,'Requisition line items required','error')
      let validatereq= validateRequisition(data,'add')
      if(!validatereq) return sendResponse(400,'Invalid Request Body','error')
      let statuscheck=this.checkStatus(data.status_id)
      if(!statuscheck) return sendResponse(400,'Invalid Requesition Status','error')
      let aid = await addRequisition(data,token);
      // return { aid };
      return sendResponse(200,aid)
    } catch (error) {
      // errorLog("error in LibAddRequisitionService:1",error,{message:error.message})
      // if (error.code) return error.message;
      errorLog("error in LibAddRequisitionService:1",error)
      return sendResponse(500,error,'error')
    }
  }

  async update(data,params){
    let { token } = params.headers
    try {
      console.log('update req==>',data,params)
      if(data.status_id!=REQUISITION_DRAFT && (!data.requisition_line_items || data.requisition_line_items && data.requisition_line_items.length==0)) return sendResponse(400,'Requisition line items required','error')
      let validatereq= validateRequisition(data,'update')
      if(!validatereq) return sendResponse(400,'Invalid Request Body','error')
      let statuscheck=this.checkStatus(data.status_id)
      if(!statuscheck) return sendResponse(400,'Invalid Requesition Status','error')
      let aid = await updateReq(data,token);
      // return { aid };
      return sendResponse(200,aid)
    } catch (error) {
      // errorLog("error in LibUpdateRequisitionService:1",data,{message:error.message})
      // if (error.code) return error.message;
      errorLog("error in LibUpdateRequisitionService:1",error)
      return sendResponse(500,error,'error')
    }
  }

  async getReqStatus(params){
    try {
      console.log('get req status==>',params)
      let reqstatus = await getRequsitionStatus();
      // return reqstatus;
      return sendResponse(200,reqstatus)
    } catch (error) {
      // errorLog("error in getRequisitionStatus:1",error,{message:error.message})
      // if (error.code) return error.message;
      errorLog("error in getRequisitionStatus:1",error)
      return sendResponse(500,error,'error')
    }
  }

  checkStatus(val){
    let req_status=[1000820001,1000820002,1000820003,1000820004,1000820005,1000830001,1000830002,1000830003,1000840001,1000840002]
    if(req_status.includes(val)) return true
    else return false
  }

  async getInvoiceData(params){
    try {
      console.log('add invoice for req==>',params)
      let invdata = await getInvoiceDataForRequisition(params.query.id,params);
      // return { meddetails };
      return sendResponse(200,invdata)
    } catch (error) {
      // errorLog("error in getQtyDetails:1",error,{message:error.message})
      // if (error.code) return error.message;
      errorLog("error in addInvoice:1",error)
      return sendResponse(500,error,'error')
    }
  }

}

module.exports = { LibRequisitionService};
