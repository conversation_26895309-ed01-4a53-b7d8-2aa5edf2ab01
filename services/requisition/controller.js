const { validateUUID,generateUUID } = require('@krushal-it/mobile-or-server-lib')
const { configurationJSON } = require('@krushal-it/common-core')
const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm')
const { getClassificationConfiguration } = require("../common/classification.helper");
const { RELATION_REQUISITION,RELATION_REQUISITION_LINE_ITEMS,RELATION_INVENTORY_LEDGER,RELATION_REFERENCE,INACTIVE,ACTIVE,RELATION_STAFF,RELATION_ENTITY_STATUS,
  REQUISITION_DRAFT,REQUISITION_PENDING,REALATION_ENTITY_GEO,CONST_ID_TYPE_STAFF_VET,REFERENCE_TYPE_REQUISITION,
  PARAVET, REQUISITION_RECEIVED_BY_REQUISITIONER, REQUISITION_REJECTED_BY_APPROVER,REQUISITION_REJECTED_BY_FULLFILLER,REQUISITION_REJECTED_BY_REQUISITIONER,CONST_PARTNER_PHARMACY, REQUISITION_INVOICE_GENERATED, REQUISITION_INVOICE_SUBMITTED } = require('../../utils/constant');
const { insertRequisition,insertRequisitionLineItems,insertRequisitionEntityStatus,
  insertRequisitionClassification,updateRequisitionClassification,getClassificationByRequisition,
  updateEntityStatusToAdjustEndTimeForRequisitionStatus,getLineItemsByRequisition, 
  updateRequisitionLineItems, updateRequisition,getAllRequisition,
  invoiceRequisition,getRequisitionClassificationQuery } = require('../../queries/requisition');
const { getInventoryKit,getLatestInventoryForUUIDMedid,insertInventoryLedger } = require('../../queries/inventory_ledger');
const { addInventory }= require('../inventory_ledger/controller')
const { getAllMedicines } = require('../medicine/controller')
const { getDocumentsByRequisitionId } = require('../../queries/document')
const { loadMeds } = require('../common/common.class')


const getRequisition = async (data,params) => {
  // return new Promise(async (resolve,reject)=>{
    try{
      let values=data?data:{}
      let {token}=params.headers
      let action=params.route.type
      let admin=params.query.type=='admin'? 1 : 0
      let mobile=params.query.type=='mobile'? 1 : 0
      console.log(params.query,admin)
      let filters={
        "r_id":values.filters && values.filters.requisition_id?values.filters.requisition_id:null,
        "r_requisitioner":values.filters && values.filters.requisitioner_uuid?values.filters.requisitioner_uuid:[],
        "r_status":values.filters && values.filters.status_l10n?values.filters.status_l10n:[],
        "r_fullfiller":values.filters && values.filters.fullfiller_uuid?values.filters.fullfiller_uuid:[],
        "r_approver":values.filters && values.filters.approver_uuid?values.filters.approver_uuid:[],
        "r_requisitioner_type":values.filters && values.filters.requisitioner_type_l10n?values.filters.requisitioner_type_l10n:[],
        "r_approver_type":values.filters && values.filters.approver_type_l10n?values.filters.approver_type_l10n:[],
        "r_fullfiller_type":values.filters && values.filters.fullfiller_type_l10n?values.filters.fullfiller_type_l10n:[],
        "r_requisitioner_name_l10n":values.filters && values.filters.requisitioner_name_l10n?((admin || action)?values.filters.requisitioner_name_l10n[0]:values.filters.requisitioner_name_l10n):null,
        "r_approver_name_l10n":values.filters && values.filters.approver_name_l10n?((admin || action)?values.filters.approver_name_l10n[0]:values.filters.approver_name_l10n):null,
        "r_fullfiller_name_l10n":values.filters && values.filters.fullfiller_name_l10n?((admin || action)?values.filters.fullfiller_name_l10n[0]:values.filters.fullfiller_name_l10n):null,
        "r_requisitioner_name":values.filters && values.filters.requisitioner_name?((admin || action)?values.filters.requisitioner_name[0]:values.filters.requisitioner_name):null,
        "r_approver_name":values.filters && values.filters.approver_name?((admin || action)?values.filters.approver_name[0]:values.filters.approver_name):null,
        "r_fullfiller_name":values.filters && values.filters.fullfiller_name?((admin || action)?values.filters.fullfiller_name[0]:values.filters.fullfiller_name):null,
        "r_created_at_from":values.filters && values.filters.created_at && values.filters.created_at[0] && values.filters.created_at[0].length>0?values.filters.created_at[0][0]:null,
        "r_created_at_to":values.filters && values.filters.created_at && values.filters.created_at[0] && values.filters.created_at[0].length>1?values.filters.created_at[0][1]:null,
        "r_inserted_at_from":values.filters && values.filters.inserted_at && values.filters.inserted_at[0] && values.filters.inserted_at[0].length>0?values.filters.inserted_at[0][0]:null,
        "r_inserted_at_to":values.filters && values.filters.inserted_at && values.filters.inserted_at[0] && values.filters.inserted_at[0].length>1?values.filters.inserted_at[0][1]:null,
        "r_updated_at_from":values.filters && values.filters.updated_at && values.filters.updated_at[0] && values.filters.updated_at[0].length>0?values.filters.updated_at[0][0]:null,
        "r_updated_at_to":values.filters && values.filters.updated_at && values.filters.updated_at[0] && values.filters.updated_at[0].length>1?values.filters.updated_at[0][1]:null,
        "r_pps_id":values.filters && values.filters.prescription_process_status_l10n?values.filters.prescription_process_status_l10n:[],
        "r_pmds_id":values.filters && values.filters.prescription_medicine_delivery_status_l10n?values.filters.prescription_medicine_delivery_status_l10n:[],
        "r_pis_id":values.filters && values.filters.prescription_invoice_status_l10n?values.filters.prescription_invoice_status_l10n:[],
      }
      let requisitionerid=(params.query && params.query.requisitioner_id) ? params.query.requisitioner_id: undefined
      console.log('requisitionerid',requisitionerid)
      if(requisitionerid){
        let openreqlist = await OpenRequisitionForRequisitioner(requisitionerid,params)
        console.log('openreqlist==>2',openreqlist)
        // if(!values.r_requisitioner || values.r_requisitioner && values.r_requisitioner.length==0) values.r_requisitioner=[requisitionerid]
        filters.r_requisitioner=[requisitionerid]
      }
      if(params.query.id){
        filters.r_id = params.query.id
      }
      if(filters.r_requistioner){
        for(let i=0;i<filters.r_requisitioner.length;i++){
          filters.r_requisitioner[i]=`'${filters.r_requisitioner[i]}'`
        }
      }
      if(filters.r_fullfiller){
        for(let i=0;i<filters.r_fullfiller.length;i++){
          filters.r_fullfiller[i]=`'${filters.r_fullfiller[i]}'`
        }
      }
      if(filters.r_approver){
        for(let i=0;i<filters.r_approver.length;i++){
          filters.r_approver[i]=`'${filters.r_approver[i]}'`
        }
      }
      console.log('after converting',filters)
      let requisitionList = await getAllRequisition(filters)
      let medicines = []
      let requisitionclassifiers=[]
      let requisitiondocs=[]
      if(filters.r_id){
        // medicines = await getAllMedicines(null)
        let loadmeds= await loadMeds()
        medicines = Object.values(loadmeds)
        requisitionclassifiers = await getRequisitionClassification(filters.r_id?filters.r_id:null)
        if(requisitionList[0].status_id==REQUISITION_INVOICE_SUBMITTED){
          requisitiondocs=await getDocumentsByRequisitionId(filters.r_id)
          console.log('requisitiondocs',requisitiondocs)
        }
      }
      if(action && action=='open') requisitionList=requisitionList.filter(item=>item.approver_uuid)
      console.log('before requisitionlist',admin,requisitionList.length)
      postProcessRecords(undefined, requisitionList, { json_columns: ['requisitioner_type_l10n','requisitioner_name_l10n','approver_type_l10n','approver_name_l10n','fullfiller_type_l10n','fullfiller_name_l10n','medicine_name_l10n','medicine_information','status_l10n','line_item_status_l10n','prescription_process_status_l10n','prescription_medicine_delivery_status_l10n','prescription_invoice_status_l10n'] });
      let mappedreqlist=[]
      for(let i=0;i<requisitionList.length;i++){
        let index=mappedreqlist.findIndex(item=>item.requisition_id == requisitionList[i].requisition_id)
        let medindex=medicines.findIndex(item => item.medicine_id == requisitionList[i].medicine_id)
        let reqclassifierindex = requisitionclassifiers.findIndex(item=>item.requisition_id == requisitionList[i].requisition_id)
        // let med={...medicines[medindex]}
        // delete med.medicine_id
        let med=medindex==-1?{}:{...medicines[medindex]}
        let reqclassifier=reqclassifierindex==-1?{}:{...requisitionclassifiers[reqclassifierindex]}
        if(index==-1){
          mappedreqlist.push({
            requisition_id:requisitionList[i].requisition_id,
            requisitioner_type_id:requisitionList[i].requisitioner_type_id,
            requisitioner_type_l10n:requisitionList[i].requisitioner_type_l10n,
            requisitioner_uuid:requisitionList[i].requisitioner_uuid,
            requisitioner_name_l10n:requisitionList[i].requisitioner_name_l10n,
            requisitioner_name: extractBasedOnLanguageMod(requisitionList[i].requisitioner_name_l10n),
            approver_type_id:requisitionList[i].approver_type_id,
            approver_type_l10n:requisitionList[i].approver_type_l10n,
            approver_uuid:requisitionList[i].approver_uuid,
            approver_name_l10n:requisitionList[i].approver_name_l10n,
            approver_name: extractBasedOnLanguageMod(requisitionList[i].approver_name_l10n),
            fullfiller_type_id:requisitionList[i].fullfiller_type_id,
            fullfiller_type_l10n:requisitionList[i].fullfiller_type_l10n,
            fullfiller_uuid:requisitionList[i].fullfiller_uuid,
            fullfiller_name_l10n:requisitionList[i].fullfiller_name_l10n,
            fullfiller_name: extractBasedOnLanguageMod(requisitionList[i].fullfiller_name_l10n),
            line_items_count:requisitionList[i].line_items_count,
            status_id:requisitionList[i].status_id,
            status_l10n:requisitionList[i].status_l10n,
            prescription_process_status_id:requisitionList[i].prescription_process_status_id,
            prescription_medicine_delivery_status_id:requisitionList[i].prescription_medicine_delivery_status_id,
            prescription_invoice_status_id:requisitionList[i].prescription_invoice_status_id,
            prescription_process_status_l10n:requisitionList[i].prescription_process_status_l10n,
            prescription_medicine_delivery_status_l10n:requisitionList[i].prescription_medicine_delivery_status_l10n,
            prescription_invoice_status_l10n:requisitionList[i].prescription_invoice_status_l10n,
            created_at:requisitionList[i].created_at,
            updated_at:requisitionList[i].updated_at,
            inserted_at: requisitionList[i].inserted_at,
            requisition_line_items:requisitionList[i].requisition_line_items_id?[
              {
                requisition_line_items_id:requisitionList[i].requisition_line_items_id,
                medicine_id:requisitionList[i].medicine_id,
                // medicine_name_l10n:requisitionList[i].medicine_name_l10n,
                unit:requisitionList[i].unit,
                quantity:requisitionList[i].quantity,
                item_status_id:requisitionList[i].item_status_id,
                item_status_l10n:requisitionList[i].item_status_l10n,
                // medicine_information:requisitionList[i].medicine_information,
                ...med
              }
            ]:[],
            files:requisitiondocs,
            ...reqclassifier
          })
        }
        else{
          let rlidx=mappedreqlist[index].requisition_line_items.findIndex(item=>item.requisition_line_items_id==requisitionList[i].requisition_line_items_id)
          if(requisitionList[i].requisition_line_items_id && rlidx==-1){
            mappedreqlist[index].requisition_line_items.push({
              requisition_line_items_id:requisitionList[i].requisition_line_items_id,
              medicine_id:requisitionList[i].medicine_id,
              // medicine_name_l10n:requisitionList[i].medicine_name_l10n,
              unit:requisitionList[i].unit,
              quantity:requisitionList[i].quantity,
              item_status_id:requisitionList[i].item_status_id,
              item_status_l10n:requisitionList[i].item_status_l10n,
              // medicine_information:requisitionList[i].medicine_information,
              ...med
            })
          }
        }
      }
      let count=0
      let total_page = 1
      if((admin || action) && !mobile){
        count = mappedreqlist?parseInt(mappedreqlist.length):0
        // if (!isNaN(values.start) && !isNaN(values.size)) {
        if (!isNaN(values.size) && !isNaN(values.start) && values.start>0) {
          // let offset = values.page_limit * (values.page_number - 1)
          // let offset = values.size * values.start
          let offset = values.size + values.start?values.start:0
          // if (offset < 0) return { message: 'Invalid page number' }
          // if (offset < 0) return sendResponse(400,'Invalid page number','error')
          if (offset < 0) throw new Error('Invalid page number')
          mappedreqlist=mappedreqlist.splice(offset,values.size)
          total_page = parseInt(count / values.size)
          total_page += count % values.size === 0 ? 0 : 1
        }
      }
      console.log('after requisitionlist',mobile,admin,action,mappedreqlist.length,total_page,values.size,count)
      // console.log(mappedreqlist[0].requisition_line_items)
      if((admin || action) && !mobile) return{report:mappedreqlist,count:count,return_code:0}
      else return mappedreqlist
      // if(admin) resolve({mappedreqlist,total_page:total_page})
      // else resolve(mappedreqlist)
    }
    catch(e){
      console.log('getRequisition',e)
      // return sendResponse(500,e,'error')
      // reject(e)
      throw e
    }
  // })
}

const OpenRequisitionForRequisitioner= async(id,params,transaction)=>{
  // return new Promise(async (resolve,reject)=>{
    try{
      let manager = transaction ? transaction : dbConnections().main.manager
      // await manager.transaction(async (transactionalEntityManager) => {
        console.log(id)
        let openquery=`SELECT requisition_id,requisitioner_uuid,status_id FROM main.${RELATION_REQUISITION} where requisitioner_uuid='${id}' AND status_id=${REQUISITION_DRAFT} AND active=${ACTIVE}`
        let openreqlist= await manager.query(openquery)
        console.log('openreqlist==>1',openreqlist)
        if(!openreqlist || openreqlist.length==0){
          console.log('called inside openreq')
          let staffquery=`SELECT staff_id,staff_type_id FROM main.${RELATION_STAFF} where staff_id='${id}'`
          let staff=await manager.query(staffquery)
          console.log('staff===>',staff)
          let req= await addRequisition({
            requisitioner_type_id:staff[0].staff_type_id,
            requisitioner_uuid:staff[0].staff_id,
            status_id:REQUISITION_DRAFT
          },params,transaction)
          openreqlist=await manager.query(openquery)
        }
        // resolve(openreqlist)
        return openreqlist
      // })
    }
    catch(e){
      console.log('OpenRequisitionForRequisitioner',e)
      // reject(e)
      throw e
    }
  // })
}

const addRequisition = async (data,params,transactionManager) => {
  // return new Promise(async (resolve,reject)=>{
    // let transactionalEntityManager = transactionManager ? transactionManager.manager :await dbConnections().main.manager.transaction()
    // console.log('transactionalEntityManager',transactionalEntityManager)
    try{
      let requisition=null
      // let {token}=params.headers
      // let {user_id}=token.user_id
      console.log('addrequisition==>',data,params)
      let values= {...data}
      if(values.status_id!= REQUISITION_RECEIVED_BY_REQUISITIONER){
        let approverfullfiller=await getApproverFullfiller(values.requisitioner_uuid)
        console.log('addApproverFullfiller==>',approverfullfiller)
        if(approverfullfiller && approverfullfiller.length>0){
          values['approver_type_id']=approverfullfiller[0].approver_type_id
          values['approver_uuid']=approverfullfiller[0].approver_uuid
        }
        if(!values.fullfiller_uuid && approverfullfiller && approverfullfiller.length>0){
          values['fullfiller_type_id']=approverfullfiller[0].fullfiller_type_id
          values['fullfiller_uuid']=approverfullfiller[0].fullfiller_uuid
        }
      }
      if(values.status_id!=REQUISITION_DRAFT && (!values.requisition_line_items || values.requisition_line_items && values.requisition_line_items.length==0)) throw new Error('Requisition line items required')
      // if(values.status_id!=REQUISITION_DRAFT && (!values.requisition_line_items || values.requisition_line_items && values.requisition_line_items.length==0)) return sendResponse(400,'Requisition line items required','error')
      values['line_items_count']=values.requisition_line_items && Array.isArray(values.requisition_line_items)?values.requisition_line_items.length:0
      values['last_modifying_user_id']=params.user_id
      // let manager = transactionManager ? transactionManager.manager : dbConnections().main.manager
      if(transactionManager){
        requisition = await addTransacReq(values,params,transactionManager)
      }
      else{
        await dbConnections().main.manager.transaction(async (transactionalEntityManager) => {
          requisition = await addTransacReq(values,params,transactionalEntityManager)
        })
      }
      return requisition
      // if (!transactionManager) {
      //   await transactionalEntityManager.commitTransaction();
      // }
      // if (!transactionManager && transactionalEntityManager && !transactionalEntityManager.isReleased) {
      //   await transactionalEntityManager.release();
      // }
      // resolve(requisition)
    }
    catch(e){
      console.log('addRequisition',e)
      // return sendResponse(400,e,'error')
      // if (!transactionManager && transactionalEntityManager && !transactionalEntityManager.isReleased) {
      //   await transactionalEntityManager.rollbackTransaction();
      //   await transactionalEntityManager.release();
      // }
      // reject(e)
      throw e
    }
  // })
}

const addTransacReq = async(values,params,transactionalEntityManager)=>{
  try{
    let requisition=null
    let requisition_line_items=values.requisition_line_items
    delete values.requisition_line_items
    let manager = transactionalEntityManager ? transactionalEntityManager : dbConnections().main.manager
    if(values.status_id==REQUISITION_DRAFT){
      let openquery=`SELECT requisition_id,requisitioner_uuid,status_id FROM main.${RELATION_REQUISITION} where requisitioner_uuid='${values.requisitioner_uuid}' AND status_id=${REQUISITION_DRAFT} AND active = ${ACTIVE}`
      let openreqlist=await manager.query(openquery)
      console.log('openreqlist in add',openreqlist)
      if(openreqlist && openreqlist.length>0){
        values['requisition_id']=openreqlist[0].requisition_id
        values['status_id']=REQUISITION_DRAFT
      }
      else{
        requisition=await insertRequisition(values,transactionalEntityManager)
      }
    }
    else{
      requisition=await insertRequisition(values,transactionalEntityManager)
    }
    values['requisition_id']=values['requisition_id']?values['requisition_id']:requisition
    if(requisition_line_items && Array.isArray(requisition_line_items)){
      // requisition_line_items=requisition_line_items.forEach(lt => {
      //   lt['requisitioner_uuid']=values.requisitioner_uuid
      //   lt['requisition_id']=values.requisition_id?values.requisititon_id:requisition
      //   lt['item_status_id']=values.status_id
      // });
      let inv_kit_query=` 
      SELECT 
          warehouse_uuid,
          medicine_id
      FROM (
          SELECT 
              warehouse_uuid,
              medicine_id
          FROM main.${RELATION_INVENTORY_LEDGER}
          WHERE warehouse_uuid='${values.requisitioner_uuid}' AND active = ${ACTIVE}
          GROUP BY warehouse_uuid, medicine_id
        )  as requi
      `
      let inv_ledger= await manager.query(inv_kit_query)
      for(let i=0;i<requisition_line_items.length;i++){
        requisition_line_items[i]['requisitioner_uuid']=values.requisitioner_uuid
        requisition_line_items[i]['requisition_id']=values.requisition_id?values.requisition_id:requisition
        requisition_line_items[i]['item_status_id']=values.status_id
        requisition_line_items[i]['last_modifying_user_id']=params.user_id

        let index=inv_ledger.findIndex(item=>item.medicine_id===requisition_line_items[i].medicine_id)
        if(index==-1 && values.status_id!=REQUISITION_RECEIVED_BY_REQUISITIONER){
          let x={
            requisition_id:requisition_line_items[i].requisition_id,
            medicine_id:requisition_line_items[i].medicine_id,
            warehouse_type_id:values.requisitioner_type_id,
            warehouse_uuid:values.requisitioner_uuid,
            unit:requisition_line_items[i].unit,
            credit_qty:0,
            balance:0,

          }
          console.log('add inv from add requisition',x,params)
          let add_inv_ledger = await addInventory(x,params,transactionalEntityManager) 
        }
      }
      if(values.status_id==REQUISITION_DRAFT){
        let openitemslist=await updateExisitingOpenReqLineItems(requisition_line_items,values.requisitioner_uuid,transactionalEntityManager)
        requisition_line_items=requisition_line_items.filter(({ medicine_id: id1 }) => !openitemslist.some(({ medicine_id: id2 }) => id2 === id1))
      }
      if(requisition_line_items && requisition_line_items.length>0){
        let req_line_items= await insertRequisitionLineItems(requisition_line_items,transactionalEntityManager)
      } 
      console.log('after requisition create==>',requisition_line_items,values,JSON.stringify(requisition))
      if(requisition && values.status_id==REQUISITION_RECEIVED_BY_REQUISITIONER){
        for(let i=0;i<requisition_line_items.length;i++){
          // let add_inv_ledger=await addItemsToInventoryLedger(line_items[i],values)
          let x={
            requisition_id:requisition_line_items[i].requisition_id,
            medicine_id:requisition_line_items[i].medicine_id,
            warehouse_type_id:values.requisitioner_type_id,
            warehouse_uuid:values.requisitioner_uuid,
            unit:requisition_line_items[i].unit,
            credit_qty:parseFloat(requisition_line_items[i].quantity),
          }
          let add_inv_ledger=await addInventory(x,params,transactionalEntityManager)
          if(values.fullfiller_type_id==CONST_ID_TYPE_STAFF_VET || values.fullfiller_type_id==PARAVET){
            let y={
              requisition_id:requisition_line_items[i].requisition_id,
              medicine_id:requisition_line_items[i].medicine_id,
              warehouse_type_id:values.fullfiller_type_id,
              warehouse_uuid:values.fullfiller_uuid,
              unit:requisition_line_items[i].unit,
              debit_qty:parseFloat(requisition_line_items[i].quantity),

            }
            let rem_inv_ledger=await addInventory(y,transactionalEntityManager)
          }
        }
      }
      console.log('add req inv_ledger',inv_ledger)
    }
    // if(requisition){
      console.log('inside req_status',requisition)
      let req_status=await addRequisitionStatus(values,params,transactionalEntityManager)
    // }
    return requisition
  }
  catch(e){
    console.log('add transcreq==>',e)
    throw e
  }
}

const updateReq = async (data,params) =>{
  // return new Promise(async (resolve,reject)=>{
    try{
      // let invoice=null
      console.log("updaterequisition==>",data,params)
      // let {token}=params.headers
      // let {user_id}=token.user_id
      let values= {
        requisition_id:data.requisition_id,
        requisitioner_type_id:data.requisitioner_type_id,
        requisitioner_uuid:data.requisitioner_uuid,
        approver_type_id: data.approver_type_id,
        approver_uuid: data.approver_uuid,
        fullfiller_type_id: data.fullfiller_type_id,
        fullfiller_uuid: data.fullfiller_uuid,
        status_id:data.status_id,
        last_modifying_user_id:params.user_id,
        updated_at:new Date(),
        requisition_line_items:[]
      }
      for(let i=0;i<data.requisition_line_items.length;i++){
        x={
          medicine_id:data.requisition_line_items[i].medicine_id,
          quantity: data.requisition_line_items[i].quantity,
          requisition_id: data.requisition_line_items[i].requisition_id?data.requisition_line_items[i].requisition_id:values.requisition_id,
          requisitioner_uuid: data.requisition_line_items[i].requisitioner_uuid?data.requisition_line_items[i].requisitioner_uuid:values.requisitioner_uuid,
          unit: data.requisition_line_items[i].unit,
          item_status_id:data.status_id,
          last_modifying_user_id:params.user_id
        }
        if(data.requisition_line_items[i].requisition_line_items_id) x['requisition_line_items_id']=data.requisition_line_items[i].requisition_line_items_id
        values.requisition_line_items.push(x)
      }
      console.log('after constructing',values)
      values['line_items_count']=values.requisition_line_items.length
      // if(values.status_id==REQUISITION_PENDING || values.status_id == REQUISITION_DRAFT){
      if(!values.approver_uuid || !values.fullfiller_uuid){
        let approverfullfiller=await getApproverFullfiller(values.requisitioner_uuid)
        console.log('updateApproverFullfiller==>',approverfullfiller)
        if(approverfullfiller && approverfullfiller.length>0){
          values['approver_type_id']=approverfullfiller[0].approver_type_id
          values['approver_uuid']=approverfullfiller[0].approver_uuid
          values['fullfiller_type_id']=approverfullfiller[0].fullfiller_type_id
          values['fullfiller_uuid']=approverfullfiller[0].fullfiller_uuid
        }
      }
      await dbConnections().main.manager.transaction(async (transactionalEntityManager) => {
        // if(values.status_id!=REQUISITION_DRAFT && (!values.requisition_line_items || values.requisition_line_items && values.requisition_line_items.length==0)) throw new Error('Requisition line items required')
        // if(values.status_id!=REQUISITION_DRAFT && (!values.requisition_line_items || values.requisition_line_items && values.requisition_line_items.length==0)) return sendResponse(400,'Requisition line items required','error')
        let requisition_line_items=values.requisition_line_items
        delete values.requisition_line_items
        let update_req=await updateRequisition(values,{requisition_id:values.requisition_id},transactionalEntityManager)
        let update_req_line_items= await updateReqLineItems(requisition_line_items,values,params,transactionalEntityManager)

        // if(values.status_id != REQUISITION_DRAFT) {
          let req_status=await addRequisitionStatus(values,params,transactionalEntityManager)
        // }

        // if(values.status_id == REQUISITION_RECEIVED_BY_REQUISITIONER && values.fullfiller_type_id==CONST_PARTNER_PHARMACY){
        //   values.status_id=REQUISITION_INVOICE_GENERATED
        //   let updaterequisition=updateReq(values,params)
        // }
        // if(values.status_id ==  REQUISITION_INVOICE_GENERATED){
        //   invoice = getInvoiceDataForRequisition(values.requisition_id,params,transactionalEntityManager)
        // }
        // if(invoice) return ({invoice_data:invoice,requisition_id:values.requisition_id})
        // else return values.requisition_id
        if(values.status_id == REQUISITION_INVOICE_SUBMITTED){
          let reqclassifications=[
            {
              requisition_id:values.requisition_id,
              classifier_id:2000000278,
              value_string_256:data.pharmacy_invoice_number,
              last_modifying_user_id:params.user_id
            },
            {
              requisition_id:values.requisition_id,
              classifier_id:2000000279,
              value_string_256:data.zoho_bill_id,
              last_modifying_user_id:params.user_id
            }
          ]
          let req_classification= await insertRequisitionClassification(reqclassifications,transactionalEntityManager)
        }
        return values.requisition_id
        // resolve(values.requisition_id)
      })
    }
    catch(e){
      console.log('updateReq',e)
      // reject(e)
      throw e
    }
  // })
}

const updateReqLineItems =async(requisition_line_items,values,params,transactionalEntityManager)=>{
  // return new Promise(async (resolve,reject)=>{
    try{
      let req_items=await getLineItemsByRequisition(values.requisition_id)
      console.log('req_items',req_items)
      let new_req_items=requisition_line_items.filter(item=>!item.requisition_line_items_id);
      let req_line_items=requisition_line_items.filter(item=> item.requisition_line_items_id);
      console.log('req_line_items==>',req_line_items)
      if(new_req_items.length>0){
        // new_req_items=new_req_items.forEach(lt => {
        //   lt['requisitioner_uuid']=values.requisitioner_uuid
        //   lt['requisition_id']=values.requisition_id
        //   lt['item_status_id']=values.status_id
        // });
        for(let i=0;i<new_req_items.length;i++){
          new_req_items[i]['requisitioner_uuid']=values.requisitioner_uuid
          new_req_items[i]['requisition_id']=values.requisition_id
          new_req_items[i]['item_status_id']=values.status_id
        }
        console.log('new_req_items==>',new_req_items)
        let req_line_items= await insertRequisitionLineItems(new_req_items,transactionalEntityManager)
      }
      let diff_line_items = req_items.filter(({ medicine_id: id1 }) => !req_line_items.some(({ medicine_id: id2 }) => id2 === id1));
      console.log('diff_line_items==>',diff_line_items)
      if(values.status_id==REQUISITION_DRAFT || values.status_id==REQUISITION_PENDING){
        for(let i=0;i<diff_line_items.length;i++){
          let update_line_item=await updateRequisitionLineItems({active:INACTIVE},{requisition_line_items_id: diff_line_items[i].requisition_line_items_id},transactionalEntityManager)
        }
      }
      else{
        let openreq= await OpenRequisitionForRequisitioner(values.requisitioner_uuid,params,transactionalEntityManager)
        console.log('openreq==>',openreq)
        let updateexistingopenmed=await updateExisitingOpenReqLineItems(diff_line_items,values.requisitioner_uuid,transactionalEntityManager)
        console.log('updateexistingopenmed',updateexistingopenmed)
        let inactivemed = diff_line_items.filter(({ medicine_id: id1 }) => !updateexistingopenmed.some(({ medicine_id: id2 }) => id2 === id1))
        console.log('inactivemed==>',inactivemed)
        for(let i=0;i<inactivemed.length;i++){
          let update_line_item=await updateRequisitionLineItems({requisition_id:openreq[0].requisition_id,item_status_id:REQUISITION_DRAFT},{requisition_line_items_id: inactivemed[i].requisition_line_items_id})
          console.log('update_line_item===>',update_line_item)
        }
      }
      let line_items = req_line_items.filter(({ medicine_id: id1 }) => req_items.some(({ medicine_id: id2 }) => id2 === id1));
      console.log('line_items==>',line_items)
      for(let i=0;i<line_items.length;i++){
        line_items[i].item_status_id=values.status_id
        // let index=req_line_items.findIndex(item=>item.medicine_id==line_items[i].medicine_id)
        // line_items[i].quantity=req_line_items[index].quantity
        console.log('each line_item',line_items[i])
        let line_item=await updateRequisitionLineItems(line_items[i],{requisition_line_items_id: line_items[i].requisition_line_items_id},transactionalEntityManager)
        if(line_items[i].item_status_id==REQUISITION_RECEIVED_BY_REQUISITIONER){
          // let add_inv_ledger=await addItemsToInventoryLedger(line_items[i],values)
          let x={
            requisition_id:line_items[i].requisition_id,
            medicine_id:line_items[i].medicine_id,
            warehouse_type_id:values.requisitioner_type_id,
            warehouse_uuid:values.requisitioner_uuid,
            unit:line_items[i].unit,
            credit_qty:parseFloat(line_items[i].quantity)
          }
          let add_inv_ledger=await addInventory(x,params,transactionalEntityManager)
        }
      }
      if(values.status_id==REQUISITION_REJECTED_BY_APPROVER || values.status_id==REQUISITION_REJECTED_BY_FULLFILLER || values.status_id==REQUISITION_REJECTED_BY_REQUISITIONER){
        let openreq= await OpenRequisitionForRequisitioner(values.requisitioner_uuid,params,transactionalEntityManager)
        console.log('openreq_rejected_line_items==>',openreq)
        let updateexistingopenmed=await updateExisitingOpenReqLineItems(line_items,values.requisitioner_uuid,transactionalEntityManager)
        console.log('updateexistingopenmed_rejected_line_items',updateexistingopenmed)
        let inactivemed = line_items.filter(({ medicine_id: id1 }) => !updateexistingopenmed.some(({ medicine_id: id2 }) => id2 === id1))
        console.log('inactivemed_rejected_line_items==>',inactivemed)
        for(let i=0;i<inactivemed.length;i++){
          delete inactivemed[i].requisition_line_items_id
          delete inactivemed[i].created_at,
          delete inactivemed[i].updated_at,      
          inactivemed[i]['requisition_id']=openreq[0].requisition_id
          inactivemed[i].item_status_id=REQUISITION_DRAFT
        }
        console.log('rejected_open_line_item',inactivemed)
        let insert_rejected_line_item=await insertRequisitionLineItems(inactivemed,transactionalEntityManager)
        console.log('insert_rejected_line_item===>',insert_rejected_line_item)
      }
      // resolve(true)
      return true
    }
    catch(e){
      console.log('updateReqLineItemsError',e)
      // reject(e)
      throw e
    }
  // })
}

const updateExisitingOpenReqLineItems = async(req_line_items,requisitioner_uuid,transactionalEntityManager)=>{
  // return new Promise(async (resolve,reject)=>{
    try{
      let manager= transactionalEntityManager?transactionalEntityManager:dbConnections().main.manager
      let openitemsquery=`SELECT * FROM main.${RELATION_REQUISITION_LINE_ITEMS} where requisitioner_uuid='${requisitioner_uuid}' AND item_status_id=${REQUISITION_DRAFT} AND active=${ACTIVE}`
      let openitemslist=await manager.query(openitemsquery)
      let activemed=openitemslist.filter(({ medicine_id: id1 }) => req_line_items.some(({ medicine_id: id2 }) => id2 === id1))
      console.log('activemed==>',activemed)
      for(let i=0;i<activemed.length;i++){
        let index=req_line_items.findIndex(item => item.medicine_id==activemed[i].medicine_id)
        if(index!=-1){
          activemed[i].quantity=parseFloat(activemed[i].quantity)
          activemed[i].quantity+=req_line_items[index].quantity
          let line_item=await updateRequisitionLineItems(activemed[i],{requisition_line_items_id: activemed[i].requisition_line_items_id},transactionalEntityManager)
          if(req_line_items[index].requisition_line_items_id){
            let line_item2=await updateRequisitionLineItems({active:INACTIVE},{requisition_line_items_id: req_line_items[index].requisition_line_items_id},transactionalEntityManager)
          }
        }
      }
      // resolve(openitemslist)
      return openitemslist
    }
    catch(e){
      console.log('updateExisitingOpenReqLineItemsError',e)
      // reject(e)
      throw e
    }
  // })
}

const getRequsitionStatus = async()=>{
  // return new Promise(async (resolve,reject)=>{
    try{
      let reqstatusquery=`SELECT * FROM main.${RELATION_REFERENCE} WHERE reference_category_id in (10008200,10008300,10008400)`
      let requsitionstatuses = await dbConnections().main.manager.query(reqstatusquery)
      postProcessRecords(undefined, requsitionstatuses, { json_columns: ['reference_name_l10n'] });
      // return { data: requsitionstatuses }
      // resolve(200,requsitionstatuses)
      return requsitionstatuses
    }
    catch(e){
      console.log('getRequsitionStatus',e)
      // reject(e)
      return e
    }
  // })
}

const getApproverFullfiller = async(id)=>{
  // return new Promise(async (resolve,reject)=>{
    try{
      let approverfullfillerquery=`SELECT 	
                          pv.geography_id,
                          v.entity_type_id as approver_type_id,
                          v.entity_uuid as approver_uuid,
                          pa.entity_type_id as fullfiller_type_id,
                          pa.entity_uuid as fullfiller_uuid
                          FROM main.${REALATION_ENTITY_GEO} as pv
                          LEFT JOIN main.${REALATION_ENTITY_GEO} as v
                          ON pv.geography_id=v.geography_id AND v.entity_type_id=${CONST_ID_TYPE_STAFF_VET} AND v.active=${ACTIVE}
                          LEFT JOIN main.${REALATION_ENTITY_GEO} as pa
                          ON pv.geography_id=pa.geography_id AND pa.entity_type_id=${CONST_PARTNER_PHARMACY} AND pa.active=${ACTIVE}
                          WHERE pv.entity_uuid='${id}' and pa.geography_id is not null AND pv.active=${ACTIVE} ORDER BY v.updated_at ASC LIMIT 1`
      // let geoquery=`SELECT geography_id FROM main.${REALATION_ENTITY_GEO} WHERE entity_uuid='${id}'`
      // let geo=await dbConnections().main.manager.query(geoquery)
      // let approverquery=`SELECT entity_type_id,entity_uuid,geography_id FROM main.${REALATION_ENTITY_GEO} WHERE geography_id=${geo[0].geography_id} AND entity_type_id=${CONST_ID_TYPE_STAFF_VET}`
      let approverfullfiller=await dbConnections().main.manager.query(approverfullfillerquery)
      console.log('getapproverfullfiller==>',approverfullfiller)
      if(approverfullfiller.length>0){
        if(!approverfullfiller[0].approver_uuid) throw new Error('Approver Not tagged for this village')
        if(!approverfullfiller[0].fullfiller_uuid) throw new Error('Fullfiller Not tagged for this village')
      }
      else throw new Error('Approver and Fullfiller Not tagged for this village')
      // resolve(approverfullfiller)
      return approverfullfiller
    }
    catch(e){
      console.log('getApproverError',e)
      // reject(e)
      throw e
    }
  // })
}

const addRequisitionStatus = async (requisition,params,transactionalEntityManager)=>{
  // return new Promise(async (resolve,reject)=>{
    try{
      // let {token}=params.headers
      let manager = transactionalEntityManager ? transactionalEntityManager : dbConnections().main.manager
      let requisitionstatus = await manager.query(`SELECT status_id FROM main.${RELATION_ENTITY_STATUS} WHERE entity_1_entity_uuid='${requisition.requisition_id}' AND active = ${ACTIVE} ORDER BY status_id DESC`)
      if(requisitionstatus.length==0 || requisitionstatus[0].status_id!=requisition.status_id){
        console.log('params',params)
        let add_req_entity_status={
          status_category_id:Math.floor(parseInt(requisition.status_id)/100),
          status_id:requisition.status_id,
          entity_1_type_id:REFERENCE_TYPE_REQUISITION,
          entity_1_entity_uuid:requisition.requisition_id,
          last_modifying_user_id:params.user_id,
        }
        console.log('req_entity_status',add_req_entity_status)
        let update_prev_req_entity_status = await updateEntityStatusToAdjustEndTimeForRequisitionStatus(add_req_entity_status,transactionalEntityManager)

        let req_entity_status = await insertRequisitionEntityStatus(add_req_entity_status,transactionalEntityManager)
        // resolve(req_entity_status)
        return req_entity_status
      }
      else return true
    }
    catch(e){
      console.log('addRequisitionStatusError',e)
      // reject(e)
      throw e
    }
  // })
}

const getInvoiceDataForRequisition = async (requisition_id,params,transactionalEntityManager)=>{
  try{
    let invreqquery = await invoiceRequisition(requisition_id,transactionalEntityManager)
    postProcessRecords(undefined, invreqquery, { json_columns: ['requisitioner_type_l10n','requisitioner_name_l10n','approver_type_l10n','approver_name_l10n','fullfiller_type_l10n','fullfiller_name_l10n','medicine_name_l10n','medicine_information','status_l10n'] });
   // let medicines = await getAllMedicines(null,transactionalEntityManager)
    let loadmeds= await loadMeds()
    let medicines = Object.values(loadmeds)
    let partnerclassificationConfig = getClassificationConfiguration()['PARTNER_CLASSIFICATION']
    let partnerclassiferidmapper = partnerclassificationConfig.CLASSIFIER_ID_TO_CLASSIFIER_ATTRIBUTE_MAP
    let partnerattrlist=partnerclassificationConfig.ATTRIBUTE_LIST
    let invreq = []
    for(let inv of invreqquery){
      let index=invreq.findIndex(item=>item.requisition_line_items_id==inv.requisition_line_items_id)
      let medindex=medicines.findIndex(item=>item.medicine_id == inv.medicine_id)
      // let med={...medicines[medindex]}
      // delete med.medicine_id
      let classifier=partnerclassiferidmapper[inv.classifier_id][0]
      let col=partnerattrlist[classifier].column
      let x={}
      x[classifier]=(col=='value_reference_id'?(inv[col]==1000105001?true:(inv[col]==1000105002?false:inv['reference_name_l10n'])):inv[col])
      if(index==-1){
        invreq.push({
          ...inv,
          ...x,
          ...medicines[medindex]
        })
        // if(inv.fullfiller_classifier_id==2000000229) invreq.push({...inv,fullfiller_address:inv.fullfiller_long_string,...med})
        // if(inv.fullfiller_classifier_id==2000000230) invreq.push({...inv,fullfiller_dlno:inv.fullfiller_string,...med})
        // if(inv.fullfiller_classifier_id==2000000231) invreq.push({...inv,fullfiller_gst:inv.fullfiller_string,...med})
        // if(inv.fullfiller_classifier_id==2000000232) invreq.push({...inv,fullfiller_pan:inv.fullfiller_string,...med})
        // if(inv.fullfiller_classifier_id==2000000233) invreq.push({...inv,fullfiller_gst_code:inv.fullfiller_int,...med})
        // if(inv.fullfiller_classifier_id==2000000234) invreq.push({...inv,fullfiller_gst_state:inv.fullfiller_string,...med})
        // if(inv.fullfiller_classifier_id==2000000238) invreq.push({...inv,fullfiller_salesperson:inv.fullfiller_string,...med})
      }
      else{
        // if(inv.fullfiller_classifier_id==2000000229) invreq[index]['fullfiller_address']=inv.fullfiller_long_string
        // if(inv.fullfiller_classifier_id==2000000230) invreq[index]['fullfiller_dlno']=inv.fullfiller_string
        // if(inv.fullfiller_classifier_id==2000000231) invreq[index]['fullfiller_gst']=inv.fullfiller_string
        // if(inv.fullfiller_classifier_id==2000000232) invreq[index]['fullfiller_pan']=inv.fullfiller_string
        // if(inv.fullfiller_classifier_id==2000000233) invreq[index]['fullfiller_gst_code']=inv.fullfiller_int
        // if(inv.fullfiller_classifier_id==2000000234) invreq[index]['fullfiller_gst_state']=inv.fullfiller_string
        // if(inv.fullfiller_classifier_id==2000000238) invreq[index]['fullfiller_salesperson']=inv.fullfiller_string
        invreq[index]={...invreq[index],...x}
      }
    }
    console.log('invreq===>',invreq)
    let calctotal={
      total_cgst:0,
      total_sgst:0,
      total_taxable:0,
      total_discount:0
    }
    for(let i=0;i<invreq.length;i++){
      invreq[i].quantity=parseFloat(invreq[i].quantity)
      invreq[i].qty=Math.abs(invreq[i].quantity/parseFloat(invreq[i].medicine_pack_size))
      invreq[i].medicine_mrp=parseFloat(invreq[i].medicine_mrp)
      calctotal.total_cgst+=((invreq[i].qty*invreq[i].medicine_mrp)*(6/100))
      calctotal.total_sgst+=((invreq[i].qty*invreq[i].medicine_mrp)*(6/100))
      calctotal.total_taxable+=(invreq[i].qty*invreq[i].medicine_mrp)
    }
    let invdata={
      REQUISITIONER_NAME:invreq[0].requisitioner_name_l10n.en?invreq[0].requisitioner_name_l10n.en:invreq[0].requisitioner_name_l10n.ul,
      REQUISITION_CREATED_AT:invreq[0].created_at,
      PARTNER_ID:invreq[0].fullfiller_uuid,
      PARTNER_ZOHO_ID:invreq[0].partner_zoho_id,
      PHARMACY_NAME:invreq[0].fullfiller_name_l10n?.en?invreq[0].fullfiller_name_l10n?.en:invreq[0].fullfiller_name_l10n?.ul,
      ADDRESS_OF_PHARMACY:invreq[0].partner_address?invreq[0].partner_address.ul:'NA',
      MOBILE_NUMBER_OF_PHARMACY:invreq[0].fullfiller_mobile_number,
      DISTRIBUTOR_LICENSE_NUMBER:invreq[0].pharmacy_distribution_license_number?invreq[0].pharmacy_distribution_license_number:'NA',
      PHARMACY_GST:invreq[0].partner_gst?invreq[0].partner_gst:'NA',
      PHARMACY_PAN:invreq[0].partner_pan?invreq[0].partner_pan:'NA',
      GST_CODE_OF_PHARMACY:invreq[0].partner_gst_code?(invreq[0].partner_gst_code+' '):'NA',
      GST_STATE_OF_PHARMACY:invreq[0].partner_gst_state?(invreq[0].partner_gst_state+' '):'NA',
      PHARMACY_INVOICE_NO:'NA',
      INVOICE_DATE:'NA',
      INVOICE_DUE_DATE:'NA',
      SALES_PERSON_NAME:invreq[0].partner_sales_person?invreq[0].partner_sales_person:'NA',
      VET_NAME:invreq[0].approver_name_l10n?.en?invreq[0].approver_name_l10n?.en:invreq[0].approver_name_l10n?.ul,
      LOCATION_NAME_OF_COMPANY_BASED_ON_STATE:'NA',
      GST_OF_KRUSHAL_BASED_ON_STATE:'NA',
      DUE_BALANCE:(calctotal.total_cgst + calctotal.total_sgst + calctotal.total_taxable + calctotal.total_discount).toFixed(2),
      CODE_AND_STATE_OF_KRUSHAL_BASED_ON_STATE:'NA',
      items: invreq.map(item => ({
        MEDICINE_MFR:'NA',
        HSN_NO:'NA',
        MEDICINE_PRESENTATION_NAME:item.medicine_name_l10n?.ul,
        MEDICINE_PACK_SIZE:item.medicine_pack_size,
        MEDICINE_QNTY:item.qty,
        BATCH_NUMBER_OF_MEDICINE:'NA',
        BATCH_EXPIRY_DATE:'NA',
        MEDICINE_MRP:(item.medicine_mrp).toFixed(2),
        PURCHASE_RATE:(item.medicine_mrp).toFixed(2),
        DISCOUNT:(0).toFixed(2),
        QNTY_PURCHASE_RATE:(item.qty*parseFloat(item.medicine_mrp)).toFixed(2),
        CGST_RATE:6,
        CGST_AMOUNT:(item.qty*parseFloat(item.medicine_mrp)*(6/100)).toFixed(2),
        SGST_RATE:6,
        SGST_AMOUNT:(item.qty*parseFloat(item.medicine_mrp)*(6/100)).toFixed(2),
        ZOHO_ITEM_ID:item.medicine_zoho_item_id,
        UNIT:item.medicine_unit?item.medicine_unit.ul:'NA'
      })),
      PHARMACY_BANK_NAME:'NA',
      ACC_NO:'NA',
      IFSC_CODE:'NA',
      // TOTAL_CGST:invreq.reduce((total, item) => total + ((item.quantity*parseFloat(item.medicine_information?.mrp)*(6/100)).toFixed(2)), 0),
      TOTAL_CGST:(calctotal.total_cgst).toFixed(2),
      TOTAL_SGST:(calctotal.total_sgst).toFixed(2),
      SIGMA_TAXABLE_AMOUNT:(calctotal.total_taxable).toFixed(2),
      TOTAL_CGST_AND_TOTAL_SGST:(calctotal.total_cgst + calctotal.total_sgst).toFixed(2),
      TOTAL_DISCOUNT:(calctotal.total_discount).toFixed(2),
      NET_AMT:(calctotal.total_cgst + calctotal.total_sgst + calctotal.total_taxable + calctotal.total_discount).toFixed(2),
      AMOUNT_IN_WORDS: inWords(calctotal.total_cgst + calctotal.total_sgst + calctotal.total_taxable + calctotal.total_discount),
      partner_gst:invreqquery[0] && (invreqquery[0].partner_gst?invreqquery[0].partner_gst:null)
     
    }
    return invdata
  }
  catch(e){
    console.log('generateInvoiceError',e)
    throw e
  }
}

const getRequisitionClassification = async(requisitions,transactionalEntityManager)=>{
    try{
      let allreqclassifications = await getRequisitionClassificationQuery(requisitions,transactionalEntityManager)
      postProcessRecords(undefined, allreqclassifications, { json_columns: ['value_json','value_l10n','reference_name_l10n'] });
      let reqclassificationConfig = getClassificationConfiguration()['REQUISITION_CLASSIFICATION']
      let reqclassiferidmapper = reqclassificationConfig.CLASSIFIER_ID_TO_CLASSIFIER_ATTRIBUTE_MAP
      let reqattrlist=reqclassificationConfig.ATTRIBUTE_LIST
      let reqclassfiers=[]
      for(let i=0;i<allreqclassifications.length;i++){
        let index=reqclassfiers.findIndex(item=>item.requisition_id==allreqclassifications[i].requisition_id)
        let classifier=reqclassiferidmapper[allreqclassifications[i].classifier_id][0]
        let col=reqattrlist[classifier].column
        let x={}
        x[classifier]=(col=='value_reference_id'?(allreqclassifications[i][col]==1000105001?true:(allreqclassifications[i][col]==1000105002?false:allreqclassifications[i]['reference_name_l10n'])):allreqclassifications[i][col])
        if(index==-1){
          reqclassfiers.push({
            requisition_id:allreqclassifications[i].requisition_id,
            ...x
          })
        }
        else{
          reqclassfiers[index]={...reqclassfiers[index],...x}
        }
      }
      return reqclassfiers
    }
    catch(e){
      console.log('getAllMedicines',e)
      throw e
    }
}

const inWords =(number)=>{
  let a = ['','one ','two ','three ','four ', 'five ','six ','seven ','eight ','nine ','ten ','eleven ','twelve ','thirteen ','fourteen ','fifteen ','sixteen ','seventeen ','eighteen ','nineteen '];
  let b = ['', '', 'twenty','thirty','forty','fifty', 'sixty','seventy','eighty','ninety'];
  if(number.toString().includes('.')){
      num=number.toString().split('.')[0]
      num2=number.toString().split('.')[1]
  }
  else {
      num=number
      num2='00'
  }
  if ((num = num.toString()).length > 9) return 'overflow';
  n = ('000000000' + num).substr(-9).match(/^(\d{2})(\d{2})(\d{2})(\d{1})(\d{2})$/);
  n1=('00' + num2).substr(-2).match(/^(\d{2})$/)
  console.log(number,number.toString().split('.'),num2,n1,a[Number(n1[1])] || b[n1[1][0]] + ' ' + a[n1[1][1]])
  if (!n) return; var str = '';
  str += (n[1] != 0) ? (a[Number(n[1])] || b[n[1][0]] + ' ' + a[n[1][1]]) + 'crore ' : '';
  str += (n[2] != 0) ? (a[Number(n[2])] || b[n[2][0]] + ' ' + a[n[2][1]]) + 'lakh ' : '';
  str += (n[3] != 0) ? (a[Number(n[3])] || b[n[3][0]] + ' ' + a[n[3][1]]) + 'thousand ' : '';
  str += (n[4] != 0) ? (a[Number(n[4])] || b[n[4][0]] + ' ' + a[n[4][1]]) + 'hundred ' : '';
  str += (n[5] != 0) ? ((str != '') ? 'and ' : '') + (a[Number(n[5])] || b[n[5][0]] + ' ' + a[n[5][1]]) + 'Rupees ' : 'Rupees ';
  str+=(n1[1] != 0) ? (a[Number(n1[1])] || b[n1[1][0]] + ' ' + a[n1[1][1]]) + 'Paisa only ' : 'only'
  return str;
}

function extractBasedOnLanguageMod(e, t) {
  if (t === undefined) {
    t = 'en'
  }
  if (e === null || e === undefined) {
    return ''
  }
  if (typeof e === 'object') {
    if (e.ul !== undefined && e.ul !== null) {
      return e.ul
    } else if (e[t] !== undefined && e[t] !== null) {
      return e[t]
    } else if (e.en !== undefined && e.en !== null) {
      return e.en
    } else {
      return ''
    }
  } else {
    return e
  }
}
module.exports= {getRequisition,addRequisition,updateReq,getRequsitionStatus,getInvoiceDataForRequisition}
