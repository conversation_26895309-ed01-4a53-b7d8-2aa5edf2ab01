const { validateUUID,generateUUID } = require('@krushal-it/mobile-or-server-lib')
const { configurationJSON } = require('@krushal-it/common-core')
const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm')
const { getClassificationConfiguration } = require("../common/classification.helper");
const { getMedicinesQuery } = require('../../queries/medicine');
const { getInventoryKit } = require('../../queries/inventory_ledger');
const { loadMeds } = require('../common/common.class')
const getAllMedicines = async(medicines,transactionalEntityManager)=>{
    // return new Promise(async (resolve,reject)=>{
      try{
        // let medquery=`SELECT * FROM main.${RELATION_REFERENCE} WHERE reference_category_id=10005000`
        // let medicines = await dbConnections().main.manager.query(medquery)
        // postProcessRecords(undefined, medicines, { json_columns: ['reference_name_l10n','reference_information'] });
        // let medclassificationConfig = getClassificationConfiguration()['MEDICINE_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
        // console.log('medclassificationConfig = ', medclassificationConfig,getClassificationConfiguration()['MEDICINE_CLASSIFICATION'].CLASSIFIER_ID_TO_CLASSIFIER_ATTRIBUTE_MAP)
        // return medicines
        let allmedicines = await getMedicinesQuery(medicines,transactionalEntityManager)
        postProcessRecords(undefined, allmedicines, { json_columns: ['medicine_name_l10n','medicine_information','value_json','value_l10n','reference_name_l10n'] });
        let medclassificationConfig = getClassificationConfiguration()['MEDICINE_CLASSIFICATION']
        let medclassiferidmapper = medclassificationConfig.CLASSIFIER_ID_TO_CLASSIFIER_ATTRIBUTE_MAP
        let medattrlist=medclassificationConfig.ATTRIBUTE_LIST
        // console.log('medclassifieridmapper',medclassificationConfig,medclassiferidmapper)
        let med=[]
        for(let i=0;i<allmedicines.length;i++){
          let index=med.findIndex(item=>item.medicine_id==allmedicines[i].medicine_id)
          let classifier=medclassiferidmapper[allmedicines[i].classifier_id] ? medclassiferidmapper[allmedicines[i].classifier_id][0] : false;
          if(!classifier) continue
          let col=medattrlist[classifier].column
          let x={}
          x[classifier]=(col=='value_reference_id'?(allmedicines[i][col]==1000105001?true:(allmedicines[i][col]==1000105002?false:allmedicines[i]['reference_name_l10n'])):allmedicines[i][col])
          if(index==-1){
            med.push({
              medicine_id:allmedicines[i].medicine_id,
              medicine_information:allmedicines[i].medicine_information,
              medicine_name_l10n:allmedicines[i].medicine_name_l10n,
              ...x
            })
          }
          else{
            med[index]={...med[index],...x}
          }
        }
        // console.log('medicine',med)
        return med
  
        // resolve(medicines)
      }
      catch(e){
        console.log('getAllMedicines',e)
        // reject(e)
        throw e
      }
    // })
}
  
const getMedAllQtyDetails = async(data,params)=>{
// return new Promise(async (resolve,reject)=>{
    try{
        // let lineitemsquery=`SELECT * FROM main.${RELATION_REQUISITION_LINE_ITEMS} where requisition_id='${options.id}'`
        // let lineitems=await dbConnections().main.manager.query(lineitemsquery)
        // console.log('lineitems==>',lineitems)
        // let medicines=[]
        // if(lineitems && lineitems.length>0){
        //   for(let i=0;i<lineitems.length;i++){
        //     medicines.push(lineitems[i].medicine_id)
        //   }
        let meddetailquery = await getInventoryKit(data.requisitioner_uuid,data.medicines)
        let meddetaillist = await dbConnections().main.manager.query(meddetailquery)
        postProcessRecords(undefined, meddetaillist, { json_columns: ['requisitioner_type_name_l10n','requisitioner_name_l10n','medicine_name_l10n','medicine_information'] });
        
        //let medicines = await getAllMedicines(data.medicines)
        let loadmeds= await loadMeds(data.medicines)
        let medicines = Object.values(loadmeds)
        for(let i=0;i<meddetaillist.length;i++){
        let medindex=medicines.findIndex(item=>item.medicine_id == meddetaillist[i].medicine_id)
        // let med={...medicines[medindex]}
        // delete med.medicine_id
        meddetaillist[i]={...meddetaillist[i],...medicines[medindex]}
        }
        return meddetaillist 
        // resolve(meddetaillist)
    // }
    // else throw new Error('No Items Found')
    }
    catch(e){
    console.log('getReqMedQtyDetails',e)
    // reject(e)
    throw e
    }
// })
}

module.exports= {getMedAllQtyDetails}