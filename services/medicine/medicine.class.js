const { generateUUID, validateUUID, errorLog } = require('@krushal-it/mobile-or-server-lib');
const { dbConnections} = require('@krushal-it/ah-orm')
const _ = require('lodash');
const moment = require('moment')
const { getMedAllQtyDetails } = require("./controller");
const { sendResponse } = require('../../utils/response');
const { requestValidation, validateRequisition } = require('../../utils/requestvalidation');
const { loadMeds } = require('../../services/common/common.class')

class LibMedicineService {
    
    async getMedicines(params){
        try {
          console.log('get medicines==>',params)
          let loadmeds= await loadMeds()
          let  medicines = Object.values(loadmeds)
          // let medicines = await getAllMedicines();
          // return medicines;
          return sendResponse(200,medicines)
        } catch (error) {
          // errorLog("error in getMedicines:1",error,{message:error.message})
          // if (error.code) return error.message;
          errorLog("error in getMedicines:1",error)
          return sendResponse(500,error,'error')
        }
      }
    
    async getMedQtyDetails(data,params){
        try {
            console.log('med qty details==>',data,params)
            if(!data.requisitioner_uuid || !data.medicines || data.medicines.length==0) return sendResponse(400,'Requisitioner ID and Medicines required','error')
            let meddetails = await getMedAllQtyDetails(data,params);
            // return { meddetails };
            return sendResponse(200,meddetails)
        } catch (error) {
            // errorLog("error in getQtyDetails:1",error,{message:error.message})
            // if (error.code) return error.message;
            errorLog("error in getQtyDetails:1",error)
            return sendResponse(500,error,'error')
        }
    }
}

module.exports = { LibMedicineService };