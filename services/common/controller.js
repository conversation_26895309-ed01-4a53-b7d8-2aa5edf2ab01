const { getDBConnections, dbConnections, postProcessRecords } = require("@krushal-it/ah-orm");
const { getFormCalendar } = require("../../queries/activity");
const { getDocuments } = require("../../queries/document");
const { getCompiledQuery } = require("../../queries/handlebarConfig");
const { getNote, insertNote } = require("../../queries/notes");
const { loadClassificationData } = require("../common/classification.helper");

const { errorLog } = require('@krushal-it/mobile-or-server-lib');
const { CONST_ID_CATEGORY_ACTIVITY_STATUS, ACTIVE, CONST_ID_CATEGORY_TASK, PARAVET, FARMER, CONST_ID_CATEGORY_STAFF, STATUS_CATEGORY, BACK_OFFICE_PERSON ,CENTRALIZED_VET} = require("../../utils/constant");

const resolveToken = async (params, options = {}) => {
    try {
        const isresolve = params.headers.token;
        let documentOptions = { entity_id: isresolve.user_id, entity_type_id: isresolve.user_type };
        // Add more cases if more categories are added
        switch (isresolve.user_type) {
            case 1000220001:
                isresolve.tnc = false;
                let classificationData = await loadClassificationData('CUSTOMER_CLASSIFICATION',  isresolve.user_id , ['customer_tnc_status'])
                if(classificationData['customer_tnc_status'] && classificationData['customer_tnc_status'] === 1000105001){
                    isresolve.tnc = true
                }
                documentOptions["document_type_id"] = 1000220001;
                break;
            default:
                documentOptions["document_type_id"] = 1000260004;
        }
        documentOptions["limit"] = 1;
        const user_document = await getDocuments(documentOptions);
        if (Array.isArray(user_document) && user_document.length > 0) {
            isresolve.image = user_document[0].document_document_id;
            isresolve.document_information = user_document[0].document_document_information;
            isresolve.client_document_information = user_document[0].document_client_document_information;
        } else {
            isresolve.image = null;
            isresolve.document_information = null;
            isresolve.client_document_information = null;
        }
        if(isresolve.user_type === 1000220001){
            documentOptions['document_type_id'] = 1000260017
            const userTermsDocument = await getDocuments(documentOptions);
            if (Array.isArray(userTermsDocument) && userTermsDocument.length > 0) {
                isresolve.userTerms_document_information = userTermsDocument[0].document_document_information;
                isresolve.termsDocumentId= userTermsDocument[0].document_document_id
            } else {
                isresolve.userTerms_document_information = null;
                isresolve.termsDocumentId= null
            }
        }
        const query_to_get_FCM_ID = " \
        select fcm_id from main.user_device \
        where \
            entity_uuid = '"+params.headers.token["user_id"]+"' and active=1000100001 \
        order by \
            updated_at \
        desc limit 1 "
        
        const query_res = await getDBConnections().main.manager.query(query_to_get_FCM_ID)
        isresolve.subscription_status = query_res.length ? (query_res[0].fcm_id != null ? true : false) : false
        return isresolve;
    } catch (error) {
        errorLog("error in resolveToken", params, { message: error.message });
        return { result: true, message: "Bad request" };
    }
};

const getNotes = async (params, options = {}) => {
    try {
        let noteOptions = {};
        const { calendar_id } = params.query;
        const { subPath } = params.route;
        let linkedTask = await loadClassificationData("CARE_CALENDAR_CLASSIFICATION", calendar_id, ["linked_care_calendar_task"]);
        let parentId = calendar_id;
        if (linkedTask.linked_care_calendar_task) {
            parentId = linkedTask.linked_care_calendar_task;
        }
        switch (subPath) {
            case "get-task-notes":
                if (!calendar_id)
                    return { result: false, message: "calendar_id is required" };
                noteOptions["note_type_id"] = 1000440001;
                noteOptions["entity_1_type_id"] = 1000220003;
                noteOptions["entity_1_uuid"] = parentId;
                return await getNote(noteOptions);
        }
    } catch (error) {
        errorLog("error in getNotes:46", params, { message: error.message });
        return {
            result: false,
            message: error.message,
        };
    }
};

const addNote = async (data, options = {}) => {
    try {
        const { params } = options;
        const { subPath } = params.route;
        const { user_id, user_type } = params.headers.token;
        const { note, calendar_id } = data;
        if (!note) throw new Error("note required");
        let value = {};
        switch (subPath) {
            case "add-task-note":
                value = {
                    note: { ul: note },
                    note_type_id: 1000440001,
                    creator_type_id: user_type,
                    creator_uuid: user_id,
                    entity_1_type_id: 1000220003,
                    entity_1_uuid: calendar_id,
                    note_time: new Date()
                };
                let inserted = await insertNote(value);
                return { note_id: inserted.identifiers[0].note_id };
        }
    } catch (error) {
        errorLog("error in addNote:54", data, { message: error.message });
        return { result: false, message: error.message };
    }
};
const getCalendar = async (params) => {
    try {
        let queryOptions = {};
        let { user_id } = params.headers.token;
        queryOptions['user_id'] = user_id;
        return await getFormCalendar(queryOptions);
    }
    catch (error) {
        errorLog("error in getCalendar", params, { message: error });
        return { result: false, message: error.message };
    }
};

const getFilterOptions = async (data, params) => {
    try {
        const { user_id, user_type } = params.headers.token;
        if (!data.filterList) throw new Error("filter List can not be null");

        let filterList = Array.isArray(data.filterList) ? data.filterList : [data.filterList];
        if (filterList.length === 0) throw new Error("filter list is empty");

        let result = {};
        for (let filterIndex = 0; filterIndex < filterList.length; filterIndex++) {
            const fromDB = await filterOptionsFromDb(filterList[filterIndex], user_type, user_id);
            if (fromDB)
                result[filterList[filterIndex].key] = fromDB;
        }

        if (result.length === 0) throw new Error("Invalid filter list");

        return result;
    } catch (error) {
        console.log(error);
        errorLog("error in getFilterOptions", { data, params }, { message: JSON.stringify(error) });
        return -1;
    }
};

/**
 * Utility function, return vaild filter options 
 * according to the user and passed filter name.
 * @param {string} filter_name
 * @param {string|number} user_type 
 * @param {string|uuid} user_id
 * @returns {Array} 
 * Note - Every db query must return one of these fields [id,name,name_l10n],
   rename the fileds in SELECT clause while adding a new query to this util
   or re-map the result with appropriate keys
 */
const filterOptionsFromDb = async (filter, user_type, user_id, options = {}) => {
    const { key, match_literal, value } = filter;
    if (!key) throw new Error("Filter key is not defined");
    switch (key) {
        case "f_village":
            if (!match_literal) {
                let villageQuery = getCompiledQuery("village-filter-options", { user_type, user_id });
                const villageQueryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${villageQuery})`)
                    .setParameters({ user_id, user_type });
                const villageData = await villageQueryBuilder.execute();
                postProcessRecords(undefined, villageData, { json_columns: ['name_l10n'] });

                return villageData;
            }
        case "f_staff_village":
            if (!match_literal) {
                const staffVillageQuery = getCompiledQuery("staff-village-filter-options", { user_type });
                const staffVillageOptions = await dbConnections().main.manager.createQueryBuilder().from(`(${staffVillageQuery})`)
                    .execute();
                postProcessRecords(undefined, staffVillageOptions, { json_columns: ["name_l10n"] });
                return staffVillageOptions;
            }
        case "f_staff_type": {
            if (!match_literal) {
                let staffTypeOptions = await dbConnections().main.repos["ref_reference"].find({
                    select: { reference_id: true, reference_name_l10n: true },
                    where: { reference_category_id: CONST_ID_CATEGORY_STAFF, active: ACTIVE }
                });
                postProcessRecords(undefined, staffTypeOptions, { json_columns: ["reference_name_l10n"] });
                staffTypeOptions = staffTypeOptions.map((val) => { return { id: val.reference_id, name_l10n: val.reference_name_l10n }; });
                return staffTypeOptions;
            }
        }
        case "f_staff_name": {
            if (user_type != BACK_OFFICE_PERSON) return [];
            if (!match_literal) {
                return [];
            }
            else {
                if (!value || typeof value !== "string") throw new Error("a string value is required to get literal match suggestions");
                const staffSuggestionQuery = getCompiledQuery("staff-name-filter-options");
                const staffSuggestionQueryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${staffSuggestionQuery})`);
                staffSuggestionQueryBuilder.setParameters({ f_staff_name: `%${value}%` });
                const staffNameSuggestions = await staffSuggestionQueryBuilder.execute();
                postProcessRecords(undefined,staffNameSuggestions,{json_columns:["name_l10n"]})
                return staffNameSuggestions;
            }
        }
        case "f_active_status":
            if (!match_literal) {
                let activeStatusOptions = await dbConnections().main.repos["ref_reference"].find({
                    select: { reference_id: true, reference_name_l10n: true },
                    where: { reference_category_id: STATUS_CATEGORY, active: ACTIVE }
                });
                postProcessRecords(undefined, activeStatusOptions, { json_columns: ["reference_name_l10n"] });
                activeStatusOptions = activeStatusOptions.map((val) => { return { id: val.reference_id, name_l10n: val.reference_name_l10n }; });
                return activeStatusOptions;
            }
        case "f_activity_status":
            if (!match_literal) {
                let statusData = await dbConnections().main.repos["ref_reference"].find({
                    where: [{ reference_category_id: CONST_ID_CATEGORY_ACTIVITY_STATUS, active: ACTIVE }],
                    select: { reference_name: true, reference_name_l10n: true, reference_id: true }
                });
                postProcessRecords(undefined, statusData, { json_columns: ['reference_name_l10n'] });
                statusData = statusData.map((val) => { return { id: val.reference_id, name: val.reference_name, name_l10n: val.reference_name_l10n }; });
                return statusData;
            }

        case "f_activity_category":
            if (!match_literal) {
                let categoryData = await dbConnections().main.repos["ref_reference"].find({
                    where: [{ reference_category_id: CONST_ID_CATEGORY_TASK, active: ACTIVE }],
                    select: { reference_name: true, reference_name_l10n: true, reference_id: true }
                });
                postProcessRecords(undefined, categoryData, { json_columns: ['reference_name_l10n'] });
                categoryData = categoryData.map((val) => { return { id: val.reference_id, name: val.reference_name, name_l10n: val.reference_name_l10n }; });
                return categoryData;
            }
        case "f_paravet":
            if (user_type != PARAVET && user_type != FARMER) {
                if (!match_literal) {
                    let paravetQuery = getCompiledQuery("paravet-filter-options", { user_id, user_type });
                    const paravetQueryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${paravetQuery})`)
                        .setParameters({ user_id, user_type });
                    const paravetData = await paravetQueryBuilder.execute();
                    postProcessRecords(undefined, paravetData, { json_columns: ['name_l10n'] });
                    return paravetData;
                }
            }
            throw new Error("invalid user type to read paravet list");
        case "f_freelance_paravet":
            if (user_type === CENTRALIZED_VET) {
                if (!match_literal) {
                    let freelanceParavetQuery = getCompiledQuery("freelance-paravet-filter-options", { user_id, user_type });
                    const freelanceParavetQueryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${freelanceParavetQuery})`)
                        .setParameters({ f_staff_name: `%${value}%`  });
                    const freelanceParavetData = await freelanceParavetQueryBuilder.execute();
                    postProcessRecords(undefined, freelanceParavetData, { json_columns: ['name_l10n'] });
                    return freelanceParavetData;
                }
            }
            throw new Error("invalid user type to read freelance paravet list");
        case "f_farmer_name":
            if (!match_literal) {
                return []; //TODO to be handled later
            }
            else {
                if (!value || typeof value !== "string") throw new Error("a string value is required to get literal match suggestions");
                let farmerQuery = getCompiledQuery("farmer-filter-options", { user_type });
                const farmerQueryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${farmerQuery})`);
                farmerQueryBuilder.setParameters({ f_farmer_name: `%${value}%`, user_id, user_type });
                const farmerData = await farmerQueryBuilder.execute();
                postProcessRecords(undefined, farmerData, { json_columns: ['name_l10n'] });
                return farmerData;
            }
        case "f_village_v2": 
            if (!match_literal) {
                let relation_type = 1000210010
                let villageQuery = getCompiledQuery("village-filter-options-v2",{relation_type , user_id ,user_type });
                const villageQueryBuilder = dbConnections().main.manager.createQueryBuilder().from(`(${villageQuery})`)
                    .setParameters({relation_type , user_id  });
                let  villageData = await villageQueryBuilder.execute();
                postProcessRecords(undefined, villageData, { json_columns: ['name_l10n'] });
                villageData = villageData.filter((village)=> village.id != null)   
                return villageData;
            }
        default: return;
    }
};
module.exports = { resolveToken, getNotes, addNote, getCalendar, getFilterOptions };