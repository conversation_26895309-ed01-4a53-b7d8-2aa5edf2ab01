const { dbConnections, getDBConnections, postProcessRecords } = require('@krushal-it/ah-orm')
const { configurationJSON, assignL10NObjectOrObjectArrayToItSelf } = require('@krushal-it/common-core')
const { errorLog } = require('@krushal-it/mobile-or-server-lib')
const _ = require('lodash')
const { In } = require('typeorm')
const { q_getStaffByCustomer } = require('../../queries/staff')
const { CONST_TYPES_STAFF, ACTIVE, PAY_PER_USE_PARAVET } = require('../../utils/constant')
const { resolveToken, getNotes, addNote, getCalendar, getFilterOptions } = require('./controller')
const { getCompiledQuery } = require('../../queries/index.js')
const { loadClassificationConfigurationByMultipleParentIds } = require('./classification.helper.js')
const classificationJson = require('../../classification.json')

global.references1 = []
class ReferencesLib {
  async create(data, params) {}
}

class LibCommonServices {
  async find(params) {
    try {
      const { subPath } = params.route
      const { user_type, user_id } = params.headers.token
      switch (subPath) {
        case 'resolve-token':
          return await resolveToken(params)
        case 'farmer-get-paravet':
          if (user_type && user_type === 1000220001) {
            const paravetQuery = q_getStaffByCustomer({ customer_id: user_id, staff_type: 1000230004 })
            let paravetData = await dbConnections().main.manager.query(paravetQuery)
            paravetData = paravetData.length >= 0 ? paravetData[0] : {}
            const roQuery = q_getStaffByCustomer({ customer_id: user_id, staff_type: 1000230001 })
            let roData = await dbConnections().main.manager.query(roQuery)
            roData = roData.length >= 0 ? roData[0] : {}
            const krushal = {
              contact: '+919876543210',
              image: { url: '/public/1671174173344-69download (3).jfif' },
            }
            return { paravet: paravetData, krushal, route_officer: roData }
          } else if (user_type && user_type === 1000220007) {
            const ppuParavetQuery = getCompiledQuery('get-ppu-staff-by-customer')
            let ppuParavetData = await dbConnections().main.manager.createQueryBuilder().from(`(${ppuParavetQuery})`).setParameters({ customer_id: user_id, staff_type: PAY_PER_USE_PARAVET }).execute()
            ppuParavetData = ppuParavetData.length >= 0 ? ppuParavetData : {}
            return { paravet: ppuParavetData }
          } else return []
        case 'get-task-notes':
          return await getNotes(params)
        case 'calendar-form':
          return await getCalendar(params)
      }
    } catch (error) {
      errorLog('error in LibCommonServices:33', params, { message: error.message })
      return {
        result: false,
        message: error.message,
      }
    }
  }

  async create(data, params) {
    try {
      console.log('bel s c c LCS c 1, params = ', params)
      console.log('bel s c c LCS c 2, params.route = ', params.route)
      const { subPath } = params.route
      console.log('bel s c c LCS c 4, subPath = ', subPath)
      const { user_type } = params.headers.token
      if (!CONST_TYPES_STAFF[user_type]) throw new Error('no-auth')
      console.log('bel s c c LCS c 5')
      switch (subPath) {
        case 'add-task-note':
          console.log('bel s c c LCS c 6')
          const addNoteResult = await addNote(data, { params })
          return addNoteResult
      }
    } catch (error) {
      errorLog('error in LibCommonServices:52', data, { message: error.message })
      return {
        result: false,
        message: error.message,
      }
    }
  }
}

class References2Lib {
  async create(data, params) {
    try {
      const mainSchemaAddition = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 'main.' : ''
      let response = []
      const language = data.language || 'en' // defaulting to english
      const categoryIdArray = data.category_id_array
      
      let whereCondition = {
        active: ACTIVE,
      }
      if (Array.isArray(categoryIdArray) && categoryIdArray.length) {
        whereCondition['reference_category_id'] = In(categoryIdArray)
      }

      let referenceResults = await dbConnections().main.repos['ref_reference'].find({
        where: whereCondition,
        select: { 
          reference_id: true, 
          reference_name_l10n: true,
          reference_information: true, 
          reference_category_id: true,
        },
      })

      postProcessRecords(undefined, referenceResults, {
        json_columns: ['reference_name_l10n', 'reference_information'],
      })

      const categoryGroups = referenceResults.reduce((groups, referenceRow) => {
        const categoryId = referenceRow.reference_category_id
        if (!groups[categoryId]) {
          groups[categoryId] = []
        }
        groups[categoryId].push({
          reference_id: referenceRow.reference_id,
          reference_name_l10n: referenceRow.reference_name_l10n,
          reference_information: referenceRow.reference_information,
        })
        return groups
      }, {})

      response = Object.entries(categoryGroups).map(([referenceCategoryId, categoryReferences]) => ({
        reference_category_id: parseInt(referenceCategoryId),
        list_data: assignL10NObjectOrObjectArrayToItSelf(categoryReferences, 'reference_name_l10n', language),
        list_data_l10: categoryReferences,
      }))

      const returnValue = { return_code: 0, data: response }
      return returnValue
    } catch (error) {
      console.error(error)
      return { return_code: -101, message: error.message }
    }
  }
}

class FilterService {
  async create(data, params) {
    return { data: await getFilterOptions(data, params) }
  }
}

class CacheReferenceLib {
  async loadReferences(userLanguage) {
    try {
      const referenceLib2 = new References2Lib()
      const postData = { language: userLanguage }
      const referenceResults = await referenceLib2.create(postData, {})
      global.references1 = referenceResults.data
    } catch (error) {
      console.log('error in loading references, error = ', error)
      throw error
    }
  }

  async getReferenceForCategoryId(categoryId, userLanguage, valuesToBeFiltered) {
    if (!global.references1 || (Array.isArray(global.references1) && global.references1.length === 0)) {
      await this.loadReferences(userLanguage)
    }

    const referenceCategoryIdIndex = _.findIndex(global.references1, (referenceCategory) => {
      return referenceCategory.reference_category_id === categoryId
    })

    if (referenceCategoryIdIndex >= 0) {
      const referenceTypesFromServer = global.references1[referenceCategoryIdIndex]

      let listData = referenceTypesFromServer.list_data
      // convert based on language
      if (valuesToBeFiltered && Array.isArray(valuesToBeFiltered) && valuesToBeFiltered.length > 0) {
        listData = listData.filter((object) => {
          if (valuesToBeFiltered.includes(object.reference_id)) {
            return true
          } else {
            return false
          }
        })
      }
      return listData
    } else {
      throw new Error('No reference data available', categoryId)
    }
  }
}

const getReferenceForCategoryId = async (categoryId, userLanguage, valuesToBeFiltered) => {
  const cacheReferenceLib = new CacheReferenceLib()
  return await cacheReferenceLib.getReferenceForCategoryId(categoryId, userLanguage, valuesToBeFiltered)
}

const loadMeds = async (medicineIds, classifierFromPayload = false) => {
  let whereCondition = { active: ACTIVE }
  if (medicineIds && medicineIds.length) {
    whereCondition['medicine_id'] = In(medicineIds)
  }
  let refMedicines = await dbConnections().main.repos['ref_medicine'].find({
    select: { medicine_id: true, medicine_name: true, medicine_name_l10n: true },
    where: whereCondition,
  })
  postProcessRecords(undefined, refMedicines, {
    json_columns: ['medicine_name_l10n'],
  })
  let classifiers = []
  if (!classifierFromPayload) {
    const medicineAttribute = classificationJson?.CLASSIFICATION_CONFIGURATION?.MEDICINE_CLASSIFICATION?.ATTRIBUTE_LIST
    classifiers = Object.keys(medicineAttribute)
  }
  let medIds = refMedicines.map((med) => med.medicine_id)
  let result = await loadClassificationConfigurationByMultipleParentIds('MEDICINE_CLASSIFICATION', medIds, classifiers)
  let response = {}

  refMedicines.forEach((item) => {
    const itemId = item.medicine_id.toString()
    response[itemId] = {
      ...item,
      ...result.data[itemId],
    }
  })

  return response
}

class References3Lib {
  async create(data, params) {
    try {
      let categoryIdArray = data?.categoryIdArray || null
      let whereCondition = {
        active: ACTIVE,
      }
      if (Array.isArray(categoryIdArray) && categoryIdArray.length) {
        whereCondition['reference_category_id'] = In(categoryIdArray)
      }
      let loadRef = await dbConnections().main.repos['ref_reference'].find({
        where: whereCondition,
        select: { reference_id: true, reference_information: true, reference_category_id: true, reference_name: true, reference_name_l10n: true },
      })
      loadRef = postProcessRecords(null, loadRef, { json_columns: ['reference_information', 'reference_name_l10n'] })

      return loadRef
    } catch (error) {
      console.error(error)
      return { return_code: -101, message: error.message }
    }
  }
}

module.exports = { LibCommonServices, ReferencesLib, References2Lib, FilterService, CacheReferenceLib, getReferenceForCategoryId, loadMeds, References3Lib }