const { configurationJSON, KrushalError } = require('@krushal-it/common-core')
const { infoLog } = require('@krushal-it/mobile-or-server-lib')
const { getDBConnections, postProcessRecords, preProcessRecords, dbConnections } = require('@krushal-it/ah-orm')
const { In } = require('typeorm')
const moment = require('moment')
const classificationJson = require('../../classification.json')
global.classificationConfiguration = null
const additionalCharactersForOthers = '_others'

const loadClassificationConfiguration = async () => {
  const baseClassificationConfiguration = classificationJson.CLASSIFICATION_CONFIGURATION
  // console.log('bel c ch lCC 1, baseClassificationConfiguration = ', baseClassificationConfiguration)
  for (const entityKey in baseClassificationConfiguration) {
    // console.log('bel c ch lCC 2, entityKey = ', entityKey)
    const classifierIdToClassifierAttributeMap = {}
    const classifierAttributeToClassifierIdMap = {}
    const entityClassificationConfiguration = { ...baseClassificationConfiguration[entityKey] }
    const db = baseClassificationConfiguration[entityKey].DB
    for (const attributeKey in entityClassificationConfiguration.ATTRIBUTE_LIST) {
      // console.log('bel c ch lCC 3, attributeKey = ', attributeKey)
      const attributeConfiguration = entityClassificationConfiguration.ATTRIBUTE_LIST[attributeKey]
      // console.log('s c ch lCC 1, attributeKey = ', attributeKey, ', attributeConfiguration.category_id = ', attributeConfiguration.category_id)
      // console.log('bel c ch lCC 4, attributeConfiguration = ', attributeConfiguration)
      if (attributeConfiguration.category_id && db) {
        const categoryOption = await dbConnections()[db].repos.ref_reference.find({
          where: [{ reference_category_id: attributeConfiguration.category_id, active: 1000100001 }],
          select: { reference_name_l10n: true, reference_id: true, reference_information: true }
        })
        attributeConfiguration.category_options = categoryOption
      }
      // classifierIdToClassifierAttributeMap[attributeConfiguration.classifier_id] = attributeKey
      if (classifierIdToClassifierAttributeMap[attributeConfiguration.classifier_id]) {
        // console.log('s c ch lCC 2')
        const classifierKeyArray = classifierIdToClassifierAttributeMap[attributeConfiguration.classifier_id]
        const modifiedClassifierKeyArray = [...classifierKeyArray, attributeKey]
        classifierIdToClassifierAttributeMap[attributeConfiguration.classifier_id] = modifiedClassifierKeyArray
      } else {
        // console.log('s c ch lCC 3')
        classifierIdToClassifierAttributeMap[attributeConfiguration.classifier_id] = [attributeKey]
      }
      // console.log('s c ch lCC 4, classifierIdToClassifierAttributeMap[attributeConfiguration.classifier_id] = ', classifierIdToClassifierAttributeMap[attributeConfiguration.classifier_id])
      classifierAttributeToClassifierIdMap[attributeKey] = attributeConfiguration.classifier_id
    }
    entityClassificationConfiguration.CLASSIFIER_ID_TO_CLASSIFIER_ATTRIBUTE_MAP = classifierIdToClassifierAttributeMap
    entityClassificationConfiguration.CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP = classifierAttributeToClassifierIdMap
    // console.log('bel c ch lCC 5, classifierIdToClassifierAttributeMap = ', classifierIdToClassifierAttributeMap)
    // console.log('bel c ch lCC 6, classifierAttributeToClassifierIdMap = ', classifierAttributeToClassifierIdMap)
    baseClassificationConfiguration[entityKey] = entityClassificationConfiguration
  }
  // infoLog('bel c ch lCC 4, baseClassificationConfiguration = ', baseClassificationConfiguration)
  global.classificationConfiguration = baseClassificationConfiguration
}

const getClassificationConfiguration = () => {
  if (global.classificationConfiguration === null) {
    throw new Error('Classification Configuration Not Loaded')
  } else {
    return global.classificationConfiguration
  }
}

const loadClassificationData = async (classificationEntityName, classificationParentEntityId, classifierAttributeArray) => {
  try {
    // console.log('bel c ch lCD 1')
    const returnValue = await loadClassificationDataInner(classificationEntityName, classificationParentEntityId, classifierAttributeArray)
    // console.log('bel c ch lCD 2, returnValue.data = ', returnValue.data)
    return returnValue.data
  } catch (error) {
    console.log('bel c ch lCD 10, error')
    console.log('bel c ch lCD 11, error = ', error)
    throw error
  }
}

const loadClassificationDataInner = async (classificationEntityName, classificationParentEntityId, classifierAttributeArray) => {
  try {
    // console.log('bel c ch lCDI 1')
    const returnValue = {}
    const returnClassifierIds = {}
    const entitiesClassificationConfiguration = getClassificationConfiguration()
    const classificationConfiguration = entitiesClassificationConfiguration[classificationEntityName]
    const classificationTableName = classificationConfiguration.CLASSIFICATION_TABLE
    const dbName = classificationConfiguration.DB
    const classifierAttributes = classificationConfiguration.ATTRIBUTE_LIST
    const classificationTableNamePrimaryKeyColumn = classificationConfiguration.CLASSIFICATION_TABLE_PRIMARY_KEY
    const classificationTableNameEntityColumn = classificationConfiguration.CLASSIFICATION_TABLE_ENTITY_COLUMN
    const classifierIdToClassifierAttributeMapping = classificationConfiguration.CLASSIFIER_ID_TO_CLASSIFIER_ATTRIBUTE_MAP
    const classifierAttributeToClassifierIdMapping = classificationConfiguration.CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
    const dbConnection = getDBConnections()[dbName]
    const classificationTableRepo = dbConnection.repos[classificationTableName]
    const classificationTableEntity = dbConnection.entities[classificationTableName]

    let childClassifierAttributeArray = []

    for (const attribute of classifierAttributeArray) {
      const asOfDateEntry = asOfDateDependencyMap.find((item) => item.parent === attribute)
      if (asOfDateEntry && asOfDateEntry.child) {
        childClassifierAttributeArray.push(asOfDateEntry.child)
      }
    }

    classifierAttributeArray.push(...childClassifierAttributeArray)
    
    const classifierIdArray = classifierAttributeArray.map((object) => {
      if (object !== undefined && object !== null && classifierAttributeToClassifierIdMapping[object]) {
        return classifierAttributeToClassifierIdMapping[object]
      }
    })

    const whereClauseString = ' entity_classification.active = :active_flag and entity_classification.' + classificationTableNameEntityColumn + ' = :classificationParentEntityId and entity_classification.classifier_id in (:...classifier_id_array) '
    const whereClauseValues = { active_flag: 1000100001, classificationParentEntityId, classifier_id_array: classifierIdArray }
    const selectAttributes = [classificationTableNamePrimaryKeyColumn, 'classifier_id', 'value_int', 'value_double', 'value_json', 'value_l10n', 'value_string_256', 'value_date', 'value_reference_id', 'value_string_256_encrypted', 'value_reference_uuid', 'updated_at', 'created_at']
    // const matchingDeviceTableRows = await classificationTableRepo.find({where: whereClause})
    const classifierTableRows = await classificationTableRepo.createQueryBuilder('entity_classification').select(selectAttributes).where(whereClauseString, whereClauseValues).getRawMany()
    postProcessRecords(classificationTableEntity, classifierTableRows, { json_columns: ['value_json', 'value_l10n'] })
    // console.log('bel c ch lCD 1, classifierTableRows = ', classifierTableRows)

    for (const classifierTableRow of classifierTableRows) {
      const classifierId = classifierTableRow.classifier_id
      const possibleClassifierKeysForClassifierIdArray = classifierIdToClassifierAttributeMapping[String(classifierId)]
      const primaryColumnValue = classifierTableRow[classificationTableNamePrimaryKeyColumn]
      for (const classifierAttribute of possibleClassifierKeysForClassifierIdArray) {
        if (!classifierAttributeArray.includes(classifierAttribute)) {
          continue
        }
        // const classifierAttribute = classifierAttributeArray[0]
        const classifierAttributeDetails = classifierAttributes[classifierAttribute]
        const classifierAttributeColumn = classifierAttributeDetails.column
        const classifierAttributeAllowMultiple = classifierAttributeDetails.allow_multiple
        const isSelect = classifierAttributeDetails.is_select ? classifierAttributeDetails.is_select : false
        const pickNull = classifierAttributeDetails.pick_null ? classifierAttributeDetails.pick_null : false
        const isDouble = classifierAttributeDetails.column === 'value_double'

        // infoLog('bel c ch lCD 2, classifierAttribute = ', classifierAttribute, ', classifierId = ', classifierId, ', classifierAttributeAllowMultiple = ', classifierAttributeAllowMultiple, ', classifierAttributeColumn = ', classifierAttributeColumn, ', isSelect = ', isSelect)

        let columnValue = classifierTableRow[classifierAttributeColumn]
        if (columnValue !== null || (columnValue === null && pickNull)) {
          try {
            if (isDouble) {
              columnValue = parseFloat(columnValue)
            }
          } catch (error) {
            console.log('bel c ch lCD 12, error = ', error)
          }
          if (!classifierAttributeAllowMultiple && returnValue[classifierAttribute]) {
            console.log('ERROR, picking up first row in query')
          } else if (!classifierAttributeAllowMultiple && isSelect) {
            // if (configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1) {
            returnValue[classifierAttribute] = [columnValue]
            returnClassifierIds[classifierAttribute] = [primaryColumnValue]
            // } else {
            //   returnValue[classifierAttribute] = columnValue;
            // }
          } else if (!classifierAttributeAllowMultiple && !isSelect) {
            returnValue[classifierAttribute] = columnValue
            returnClassifierIds[classifierAttribute] = primaryColumnValue
          } else if (classifierAttributeAllowMultiple) {
            if (returnValue[classifierAttribute]) {
              returnValue[classifierAttribute].push(columnValue)
              const valueObj = {}
              valueObj[`${primaryColumnValue}`] = columnValue
              returnClassifierIds[classifierAttribute].push(valueObj)
            } else {
              returnValue[classifierAttribute] = [columnValue]
              const valueObj = {}
              valueObj[`${primaryColumnValue}`] = columnValue
              returnClassifierIds[classifierAttribute] = [valueObj]
            }
          }
          try {
            if (classifierAttributeDetails.other_load_columns && classifierAttributeDetails.other_load_column_aliases) {
              if (Array.isArray(classifierAttributeDetails.other_load_columns) && classifierAttributeDetails.other_load_columns.length > 0 &&
                Array.isArray(classifierAttributeDetails.other_load_column_aliases) && classifierAttributeDetails.other_load_column_aliases.length > 0 &&
                classifierAttributeDetails.other_load_columns.length === classifierAttributeDetails.other_load_column_aliases.length) {
                for (let counter = 0; counter < classifierAttributeDetails.other_load_columns.length; counter++) {
                  const columnToPickup = classifierAttributeDetails.other_load_columns[counter]
                  const aliasToUse = classifierAttributeDetails.other_load_column_aliases[counter]
                  returnValue[aliasToUse] = classifierTableRow[columnToPickup]
                }
              }
            }
          } catch (error) {
            console.log('error in capturing alias values for alternate columns')
            console.log('Suppressed error')
          }
        }
      }
    }
    
    loadAsOfDateClassifiers(returnValue)

    return { data: returnValue, dataWithClassifierIds: returnClassifierIds }
  } catch (error) {
    console.log('bel c ch lCD 10, error')
    console.log('bel c ch lCD 11, error = ', error)
    throw error
  }
}

const loadClassificationDataWithClassifierIds = async (transactionalEntityManager, classificationEntityName, classificationParentEntityId, classifierAttributeArray) => {
  try {
    // console.log('bel c ch lCDWCI 1')
    const returnValue = await loadClassificationDataInner(classificationEntityName, classificationParentEntityId, classifierAttributeArray)
    // console.log('bel c ch lCDWCI 2, returnValue = ', returnValue)
    return returnValue
  } catch (error) {
    console.log('bel c ch lCD 10, error')
    console.log('bel c ch lCD 11, error = ', error)
    throw error
  }
}

const saveClassificationData = async (transactionalEntityManager, classificationEntityName, classificationParentEntityId, classificationData) => {
  try {
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
    handleAsOfDateClassifiers(classificationData)
    let classifierAttributeArray = Object.keys(classificationData)
    const entitiesClassificationConfiguration = getClassificationConfiguration()
    const classificationConfiguration = entitiesClassificationConfiguration[classificationEntityName]
    const dbName = classificationConfiguration.DB
    const classificationTableName = classificationConfiguration.CLASSIFICATION_TABLE
    const classifierAttributes = classificationConfiguration.ATTRIBUTE_LIST
    const classificationTableNamePrimaryKeyColumn = classificationConfiguration.CLASSIFICATION_TABLE_PRIMARY_KEY
    const classificationTableNameEntityColumn = classificationConfiguration.CLASSIFICATION_TABLE_ENTITY_COLUMN
    const classifierIdToClassifierAttributeMapping = classificationConfiguration.CLASSIFIER_ID_TO_CLASSIFIER_ATTRIBUTE_MAP
    // console.log('bel c ch sCC 0a, classifierIdToClassifierAttributeMapping = ', classifierIdToClassifierAttributeMapping)
    const classifierAttributeToClassifierIdMapping = classificationConfiguration.CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
    const dbConnection = getDBConnections()[dbName]
    const classificationTableRepo = transactionalEntityManager.getRepository(classificationTableName)
    const classificationTableEntity = dbConnection.entities[classificationTableName]
    const validAttributes = Object.keys(classifierAttributes)
    // trim the classifierAttributeArray to only those which are valid
    // console.log('bel c ch sCC 0b, before classifierAttributeArray = ', classifierAttributeArray.length)
    classifierAttributeArray = classifierAttributeArray.filter((classifierAttribute) => { return validAttributes.includes(classifierAttribute) })
    // console.log('bel c ch sCC 0c, after classifierAttributeArray = ', classifierAttributeArray.length)

    // get current values
    //    select current values
    //    for each key, get value, and also store the id
    // for non multi-select
    //    if value is same, don't change
    //    else, soft delete previous value, insert new value
    // for multi-select
    //    get values not existing on server - soft delete them
    //    check values not existing from client - insert them

    let classifierIdArray = []
    for (const classifierAttribute1 of classifierAttributeArray) {
      const classifierIdToAdd = classifierAttributeToClassifierIdMapping[classifierAttribute1]
      if (classifierIdToAdd !== undefined && classifierIdToAdd !== null) {
        if (classifierIdArray.includes(classifierIdToAdd)) {
          // ensure that 2 classifier keys which map to the same classifier id are not trying to insert / update the same classifier
          throw new KrushalError('Multiple Classifiers Keys with same classifier_id getting saved', -103)
        } else {
          classifierIdArray.push(classifierIdToAdd)
        }
      }
    }
    classifierIdArray = classifierIdArray.filter((classifierId) => { return classifierId !== undefined && classifierId !== null })

    // console.log('bel c ch sCC 0b, classifierIdArray = ', classifierIdArray)
    if (classifierIdArray.length === 0) return true
    const whereClauseString = ' entity_classification.active = :active_flag and entity_classification.' + classificationTableNameEntityColumn + ' = :classificationParentEntityId and entity_classification.classifier_id in (:...classifier_id_array) '
    const whereClauseValues = { active_flag: 1000100001, classificationParentEntityId, classifier_id_array: classifierIdArray }
    const selectAttributes = [classificationTableNamePrimaryKeyColumn, 'classifier_id', 'value_int', 'value_double', 'value_json', 'value_l10n', 'value_string_256', 'value_date', 'value_reference_id', 'value_string_256_encrypted', 'value_reference_uuid']
    // const matchingDeviceTableRows = await classificationTableRepo.find({where: whereClause})
    const classifierTableRows = await classificationTableRepo.createQueryBuilder('entity_classification').select(selectAttributes).where(whereClauseString, whereClauseValues).getRawMany()
    postProcessRecords(classificationTableEntity, classifierTableRows, { json_columns: ['value_json', 'value_l10n'] })
    // console.log('bel c ch sCC 1, classifierTableRows = ', classifierTableRows)

    const existingValues = {}
    for (const classifierTableRow of classifierTableRows) {
      const classifierId = classifierTableRow.classifier_id
      const possibleClassifierKeysForClassifierIdArray = classifierIdToClassifierAttributeMapping[String(classifierId)]
      for (const classifierAttribute of possibleClassifierKeysForClassifierIdArray) {
        if (!classifierAttributeArray.includes(classifierAttribute)) {
          continue
        }
        // const classifierAttribute = classifierIdToClassifierAttributeMapping[String(classifierId)]
        const classifierAttributeDetails = classifierAttributes[classifierAttribute]
        const classifierAttributeColumnName = classifierAttributeDetails.column
        const classifierAttributeAllowMultiple = classifierAttributeDetails.allow_multiple
        const isSelect = classifierAttributeDetails.is_select ? classifierAttributeDetails.is_select : false
        const pickNull = classifierAttributeDetails.pick_null ? classifierAttributeDetails.pick_null : false
        const isDouble = classifierAttributeDetails.column === 'value_double'

        const classificationRowPrimaryKey = classifierTableRow[classificationTableNamePrimaryKeyColumn]
        let columnValue = classifierTableRow[classifierAttributeColumnName]
        const columnObject = existingValues[classifierAttribute] ? existingValues[classifierAttribute] : {}
        if (columnValue !== null) {
          try {
            if (isDouble) {
              columnValue = parseFloat(columnValue)
            }
          } catch (error) {
            console.log('bel c ch lCD 12, error = ', error)
          }
          if (!classifierAttributeAllowMultiple) {
            columnObject[classificationTableNamePrimaryKeyColumn] = classificationRowPrimaryKey
            columnObject.value = columnValue
          } else if (classifierAttributeAllowMultiple) {
            if (Object.keys(columnObject).length === 0) {
              columnObject[classificationTableNamePrimaryKeyColumn] = []
              columnObject.value = []
            }
            columnObject[classificationTableNamePrimaryKeyColumn].push(classificationRowPrimaryKey)
            columnObject.value.push(columnValue)
          }
          existingValues[classifierAttribute] = columnObject
        }
      }
    }

    // console.log('bel c ch sCC 5, existingValues = ', existingValues)
    correctAsofDateClassifier(classificationData, existingValues)
    const toBeInsertedClassificationRecords = []

    for (const classifierAttribute of classifierAttributeArray) {
      // console.log('bel c ch sCC 5a, classifierAttributeClassifierId = ', classifierAttributeClassifierId)
      // const classifierAttribute = classifierIdToClassifierAttributeMapping[classifierAttributeClassifierId]
      const valueFromClient = classificationData[classifierAttribute]
      const existingValue = existingValues[classifierAttribute] ? existingValues[classifierAttribute].value : undefined
      const existingValueOthers = existingValues[classifierAttribute] ? existingValues[classifierAttribute].valueOthers : undefined
      const existingPrimaryKey = existingValues[classifierAttribute] ? existingValues[classifierAttribute][classificationTableNamePrimaryKeyColumn] : undefined
      const classifierAttributeDetails = classifierAttributes[classifierAttribute]
      if (classifierAttributeDetails === undefined || classifierAttributeDetails === null) {
        continue
      }
      // console.log('classifierAttributeDetails', classifierAttributeDetails, 'classifierAttribute', classifierAttribute)
      const classifierAttributeColumnName = classifierAttributeDetails.column
      const classifierAttributeClassifierId = classifierAttributeDetails.classifier_id
      // console.log('bel c ch sCC 3, classifierAttribute = ', classifierAttribute, ', classifierAttributeColumnName = ', classifierAttributeColumnName, ', classifierAttributeClassifierId = ', classifierAttributeClassifierId, ', existingPrimaryKey = ', existingPrimaryKey, ', valueFromClient = ', valueFromClient, ', existingValue = ', existingValue)
      if (Array.isArray(valueFromClient)) {
        // either select or multi-select
        const otherClassifierAttribute = classifierAttribute + additionalCharactersForOthers
        const valueOthersFromClient = classificationData[otherClassifierAttribute]
        const classifierAttributeOtherColumnName = classifierAttributeDetails.others_column
        if (classifierAttributeDetails.is_select === true && classifierAttributeDetails.allow_multiple === false) {
          if (existingValue === undefined && valueFromClient.length !== 0) {
            // insert
            const classificationData = {}
            if (valueFromClient[0] !== -1000 && valueFromClient[0] !== '-1000') {
              classificationData[classifierAttributeColumnName] = valueFromClient[0]
              if (classifierAttributeOtherColumnName) {
                classificationData[classifierAttributeOtherColumnName] = null
              }
            } else {
              classificationData[classifierAttributeColumnName] = valueFromClient[0]
              classificationData[classifierAttributeOtherColumnName] = valueOthersFromClient
            }
            classificationData.classifier_id = classifierAttributeClassifierId
            classificationData[classificationTableNameEntityColumn] = classificationParentEntityId
            // console.log('bel c ch sCC 3a 1, classificationData = ', classificationData)
            toBeInsertedClassificationRecords.push(classificationData)
          } else if (existingValue !== undefined && valueFromClient.length === 0) {
            // soft delete
            const valueToBeUpdated = { active: 1000100002 }
            if (!isPostgres) {
              valueToBeUpdated.data_sync_status = 1000101003
            }
            await classificationTableRepo
              .createQueryBuilder()
              .update()
              .set(valueToBeUpdated)
              .where(' ' + classificationTableNamePrimaryKeyColumn + '= :id', {
                id: existingPrimaryKey
              })
              .execute()
          } else if ((existingValue !== undefined && valueFromClient.length > 0 && valueFromClient[0] !== existingValue) || valueOthersFromClient !== existingValueOthers) {
            // update
            const columnsToBeUpdated = {}

            if (valueFromClient.includes(-1000) || valueFromClient.includes('-1000')) {
              columnsToBeUpdated[classifierAttributeColumnName] = valueFromClient[0]
              columnsToBeUpdated[classifierAttributeOtherColumnName] = valueOthersFromClient
            } else {
              columnsToBeUpdated[classifierAttributeColumnName] = valueFromClient[0]
              if (classifierAttributeOtherColumnName) {
                columnsToBeUpdated[classifierAttributeOtherColumnName] = null
              }
            }
            // console.log('bel c ch sCC 10, columnsToBeUpdated = ', columnsToBeUpdated)
            if (!isPostgres) {
              columnsToBeUpdated.data_sync_status = 1000101003
            }

            await classificationTableRepo
              .createQueryBuilder()
              .update()
              .set(columnsToBeUpdated)
              .where(' ' + classificationTableNamePrimaryKeyColumn + '= :id', {
                id: existingPrimaryKey
              })
              .execute()
          }
        } else {
          // has to be multi-select
          for (const individualValueFromClient of valueFromClient) {
            if (Array.isArray(existingValue)) {
              // check if value exists
              if (!existingValue.includes(individualValueFromClient)) {
                // this is new, add
                const classificationData = {}
                classificationData[classifierAttributeColumnName] = individualValueFromClient
                classificationData.classifier_id = classifierAttributeClassifierId
                classificationData[classificationTableNameEntityColumn] = classificationParentEntityId
                if (individualValueFromClient === -1000 || individualValueFromClient === '-1000') {
                  classificationData[classifierAttributeColumnName] = individualValueFromClient
                  classificationData[classifierAttributeOtherColumnName] = valueOthersFromClient
                } else {
                  classificationData[classifierAttributeColumnName] = individualValueFromClient
                  if (classifierAttributeOtherColumnName) {
                    classificationData[classifierAttributeOtherColumnName] = null
                  }
                }
                // console.log('bel c ch sCC 3a 2, classificationData = ', classificationData)
                toBeInsertedClassificationRecords.push(classificationData)
              } else if (existingValue.includes(individualValueFromClient) && (individualValueFromClient === -1000 || individualValueFromClient === '-1000')) {
                // check for others and the value of others
                // console.log('bel c ch sCC 26')
                if (valueOthersFromClient !== existingValueOthers) {
                  // update since value in others is different
                  const indexIntoValuesArray = existingValue.indexOf(individualValueFromClient)
                  const classificationTablePrimarykeyToBeUpdated = existingPrimaryKey[indexIntoValuesArray]
                  const columnsToBeUpdated = {}
                  columnsToBeUpdated[classifierAttributeOtherColumnName] = valueOthersFromClient

                  if (!isPostgres) {
                    columnsToBeUpdated.data_sync_status = 1000101003
                  }

                  // console.log('bel c ch sCC 20, columnsToBeUpdated = ', columnsToBeUpdated)
                  await classificationTableRepo
                    .createQueryBuilder()
                    .update()
                    .set(columnsToBeUpdated)
                    .where(' ' + classificationTableNamePrimaryKeyColumn + '= :id', {
                      id: classificationTablePrimarykeyToBeUpdated
                    })
                    .execute()
                }
              }
            } else {
              const classificationData = {}
              classificationData[classifierAttributeColumnName] = individualValueFromClient
              classificationData.classifier_id = classifierAttributeClassifierId
              classificationData[classificationTableNameEntityColumn] = classificationParentEntityId
              if (individualValueFromClient === -1000 || individualValueFromClient === '-1000') {
                classificationData[classifierAttributeColumnName] = individualValueFromClient
                classificationData[classifierAttributeOtherColumnName] = valueOthersFromClient
              } else {
                classificationData[classifierAttributeColumnName] = individualValueFromClient
                if (classifierAttributeOtherColumnName) {
                  classificationData[classifierAttributeOtherColumnName] = null
                }
              }
              // console.log('bel c ch sCC 3a 3, classificationData = ', classificationData)
              toBeInsertedClassificationRecords.push(classificationData)
            }
          }
        }
        if (classifierAttributeDetails.allow_multiple && Array.isArray(existingValue)) {
          const multiSelectIndexesToBeDisabled = []
          for (const counter in existingValue) {
            const individualValueInDB = existingValue[counter]
            if (!valueFromClient.includes(individualValueInDB)) {
              multiSelectIndexesToBeDisabled.push(counter)
            }
          }

          const multiSelectEntityClassificationPrimaryKeyInDBArray = existingValues[classifierAttribute][classificationTableNamePrimaryKeyColumn]
          const primaryKeyToBeDisabledArray = []
          for (const multiSelectIndexToBeDisabled of multiSelectIndexesToBeDisabled) {
            const primaryKeyToBeDisabled = multiSelectEntityClassificationPrimaryKeyInDBArray[multiSelectIndexToBeDisabled]
            primaryKeyToBeDisabledArray.push(primaryKeyToBeDisabled)
          }
          if (primaryKeyToBeDisabledArray.length > 0) {
            const valueToBeUpdated = { active: 1000100002 }
            if (!isPostgres) {
              valueToBeUpdated.data_sync_status = 1000101003
            }

            await classificationTableRepo
              .createQueryBuilder()
              .update()
              .set(valueToBeUpdated)
              .where(' ' + classificationTableNamePrimaryKeyColumn + ' in (:...id)', {
                id: primaryKeyToBeDisabledArray
              })
              .execute()
          }
        }
      } else {
        // console.log('bel c ch sCC 3b')
        if (existingValue === undefined && valueFromClient !== null && valueFromClient) {
          // insert
          const classificationData = {}
          classificationData[classifierAttributeColumnName] = valueFromClient
          classificationData.classifier_id = classifierAttributeClassifierId
          classificationData[classificationTableNameEntityColumn] = classificationParentEntityId
          // console.log('bel c ch sCC 3a 4, classificationData = ', classificationData)
          toBeInsertedClassificationRecords.push(classificationData)
        } else if (existingValue !== undefined && valueFromClient === null) {
          // soft delete
          const valueToBeUpdated = { active: 1000100002 }
          if (!isPostgres) {
            valueToBeUpdated.data_sync_status = 1000101003
          }
          const additionAttributesForPreProcessing = { id: existingPrimaryKey, ...classificationTableEntity.additionalAttributes }
          const updatedValuesToBeUpdated = preProcessRecords(classificationTableEntity, valueToBeUpdated, additionAttributesForPreProcessing)
          await classificationTableRepo
            .createQueryBuilder()
            .update()
            .set(updatedValuesToBeUpdated)
            .where(' ' + classificationTableNamePrimaryKeyColumn + '= :id', {
              id: existingPrimaryKey
            })
            .execute()
        } else if (existingValue !== undefined && valueFromClient !== existingValue) {
          // update
          const columnsToBeUpdated = {}
          columnsToBeUpdated[classifierAttributeColumnName] = valueFromClient
          // console.log('bel c ch sCC 30, columnsToBeUpdated = ', columnsToBeUpdated)
          // console.log('bel c ch sCC 31, existingPrimaryKey = ', existingPrimaryKey)

          if (!isPostgres) {
            columnsToBeUpdated.data_sync_status = 1000101003
          }

          const additionAttributesForPreProcessing = { id: existingPrimaryKey, ...classificationTableEntity.additionalAttributes }
          // console.log('bel c ch sCC 32, additionAttributesForPreProcessing = ', additionAttributesForPreProcessing)
          const updatedColumnsToBeUpdated = preProcessRecords(classificationTableEntity, columnsToBeUpdated, additionAttributesForPreProcessing)
          // console.log('bel c ch sCC 33, updatedColumnsToBeUpdated = ', updatedColumnsToBeUpdated)

          await classificationTableRepo
            .createQueryBuilder()
            .update()
            .set(updatedColumnsToBeUpdated)
            .where(' ' + classificationTableNamePrimaryKeyColumn + '= :id', {
              id: existingPrimaryKey
            })
            .execute()
        }
        // throw new Error('Forcefully breaking transaction')
      }
    }
    // console.log('bel c ch sCC 4, toBeInsertedClassificationRecords = ', toBeInsertedClassificationRecords)

    if (toBeInsertedClassificationRecords.length > 0) {
      const preProcessedToBeInsertedClassificationRecords = preProcessRecords(classificationTableEntity, toBeInsertedClassificationRecords, classificationTableEntity.additionalAttributes)
      // console.log('bel c ch sCC 40, preProcessedToBeInsertedClassificationRecords = ', preProcessedToBeInsertedClassificationRecords)
      await classificationTableRepo.createQueryBuilder().insert().into(classificationTableEntity).values(preProcessedToBeInsertedClassificationRecords).execute()
    }
    return true
  } catch (error) {
    console.log('bel c ch sCD 13, error')
    console.log('bel c ch sCD 14, error = ', error)
    throw error
  }
}

// temporaray function to clean the prepopulated dependent classifications if exists in the object(recieved from FE)
// and populates the appropriate dependent "as of date" classifiers
// all of the conditions are hard coded
// This method is suppossed replaced with appropriate handler once all conditions and classification are finalized

const asOfDateDependencyMap = [
  { parent: 'pregnancy_range_of_animal_1', child: 'pregnancy_status_as_of_date_1' },
  { parent: 'last_deworming_status_1', child: 'last_deworming_status_as_of_date_1' },
  { parent: 'last_tick_control_status_1', child: 'last_tick_control_status_as_of_date_1' },
  { parent: 'months_since_last_calving_1', child: 'month_since_last_calving_as_of_date_1' },
  { parent: 'last_fmd_vaccination_date_range_1', child: 'last_fmd_vaccination_date_range_as_of_date_1' },
  { parent: 'last_fmd_or_hs_or_bq_vaccination_date_1', child: 'last_fmd_hs_bq_vaccination_date_range_as_of_date_1' },
  { parent: 'theileriosis_vaccination_status_1', child: 'theilriosis_vaccination_status_as_of_date_1' },
  { parent: 'number_of_months_pregnant_1', child: 'number_of_months_pregnant_as_of_date_1' },
  { parent: 'current_lpd', child: 'current_lpd_as_of_date' },
  { parent: 'current_solid_non_fat', child: 'current_solid_non_fat_as_of_date' },
  { parent: 'current_total_fat', child: 'current_total_fat_as_of_date' },
  { parent: 'current_total_solids', child: 'current_total_solids_as_of_date' },
  { parent: 'animal_milk_fat_1', child: 'animal_milk_fat_as_of_date_1' },
  { parent: 'animal_average_lpd_1', child: 'animal_average_lpd_as_of_date_1' },
  { parent: 'animal_age_1', child: 'age_of_animal_as_of_1', type: 'age_in_years' },
  { parent: 'cost_of_milk_production' , child: 'cost_of_milk_production_as_of_date'}
]

const loadAsOfDateClassifiers = (classificationData) => {
  try {
    for (const item of asOfDateDependencyMap) {
      const { parent, child, type } = item
      const isParentValArray = Array.isArray(classificationData[parent])

      if (type && !isParentValArray && classificationData[parent] && classificationData[child] && classificationData[child] instanceof Date) {
        if (type === 'age_in_years') {
          const diff = moment().diff(moment(classificationData[child]), 'months')
          classificationData[parent] = classificationData[parent] + parseFloat((diff / 12).toFixed(1))
        } else {
          // do nothing
        }
      }
    }
  } catch (error) {
    console.log('error while updating the value as per as of date', error)
  }
}


const handleAsOfDateClassifiers = (classificationData) => {

  for (const depMap of asOfDateDependencyMap) {
    const { parent, child } = depMap
    if(!classificationData[child]) {
      delete classificationData[child]
      if (classificationData[parent] === null || (Array.isArray(classificationData[parent]) && classificationData[parent].length === 0)) {
        classificationData[child] = null
      } else if (classificationData[parent]) {
        classificationData[child] = new Date().toJSON()
      }  
    }
  }
}

const correctAsofDateClassifier = (classificationData, existingValues) => {
  
  for (const depMap of asOfDateDependencyMap) {
    const { parent, child } = depMap
    if (!classificationData[child]) {
      if (existingValues[parent] && classificationData[parent] === existingValues[parent].value) {
        if (existingValues[child]) {
          classificationData[child] = existingValues[child].value
        } else {
          classificationData[child] = new Date()
        }
      }
    }
  }
}

const saveClassifications = async (headers, reqBody) => {
  // console.log('bel c ch sC 1')
  let { classificationEntityName, classificationParentEntityId, classificationData, classifierAttributeArray } = reqBody
  try {
    if (classifierAttributeArray == null || classifierAttributeArray == undefined || classifierAttributeArray == [] || classifierAttributeArray == {}) {
    // no classifier use full list
      const classificationConfiguration = getClassificationConfiguration()
      classifierAttributeArray = Object.keys(classificationConfiguration[classificationEntityName].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP)
    }
    const result = await dbConnections().main.manager.transaction(async transactionalEntityManager => {
      const result = await saveClassificationData(transactionalEntityManager, classificationEntityName, classificationParentEntityId, classificationData)
      if (result && result == true) {
      // load just saved classification data so that caller can use the ids
        const savedData = await loadClassificationDataWithClassifierIds(transactionalEntityManager, classificationEntityName, classificationParentEntityId, classifierAttributeArray)
        return savedData
      } else {
        throw new Error('Error in saveClassifications')
      }
    })
    return result
  } catch (error) {
    console.log('bel c ch sCD 13, error in saveClassifications')
    console.log('bel c ch sCD 14, error = ', error)
    throw error
  }
}

const loadClassificationConfigurationByMultipleParentIds = async (classificationEntityName, classificationParentEntityId, classifierAttributeArray) => {
  try {
    // console.log('bel c ch lCDI 1')
    const returnValue = {}
    const returnClassifierIds = {}
    const entitiesClassificationConfiguration = getClassificationConfiguration()
    const classificationConfiguration = entitiesClassificationConfiguration[classificationEntityName]
    const classificationTableName = classificationConfiguration.CLASSIFICATION_TABLE
    const dbName = classificationConfiguration.DB
    const classifierAttributes = classificationConfiguration.ATTRIBUTE_LIST
    const classificationTableNamePrimaryKeyColumn = classificationConfiguration.CLASSIFICATION_TABLE_PRIMARY_KEY
    const classificationTableNameEntityColumn = classificationConfiguration.CLASSIFICATION_TABLE_ENTITY_COLUMN
    const classifierIdToClassifierAttributeMapping = classificationConfiguration.CLASSIFIER_ID_TO_CLASSIFIER_ATTRIBUTE_MAP
    const classifierAttributeToClassifierIdMapping = classificationConfiguration.CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
    const dbConnection = getDBConnections()[dbName]
    const classificationTableRepo = dbConnection.repos[classificationTableName]
    const classificationTableEntity = dbConnection.entities[classificationTableName]

    const classifierIdArray = classifierAttributeArray.map((object) => {
      if (object !== undefined && object !== null && classifierAttributeToClassifierIdMapping[object]) {
        return classifierAttributeToClassifierIdMapping[object]
      }
    })

    const whereClauseString = ' entity_classification.active = :active_flag and entity_classification.' + classificationTableNameEntityColumn + ' in (:...classificationParentEntityId) and entity_classification.classifier_id in (:...classifier_id_array) '
    const whereClauseValues = { active_flag: 1000100001, classificationParentEntityId, classifier_id_array: classifierIdArray }
    const selectAttributes = [classificationTableNamePrimaryKeyColumn, classificationTableNameEntityColumn, 'classifier_id', 'value_int', 'value_double', 'value_json', 'value_string_256', 'value_date', 'value_reference_id', 'value_string_256_encrypted', 'value_reference_uuid', 'updated_at', 'created_at']
    // const matchingDeviceTableRows = await classificationTableRepo.find({where: whereClause})
    const classifierTableRows = await classificationTableRepo.createQueryBuilder('entity_classification').select(selectAttributes).where(whereClauseString, whereClauseValues).getRawMany()
    postProcessRecords(classificationTableEntity, classifierTableRows, { json_columns: ['value_json'] })
    // console.log('bel c ch lCD 1, classifierTableRows = ', classifierTableRows)

    for (const classifierTableRow of classifierTableRows) {
      const parent_id = classifierTableRow[classificationTableNameEntityColumn]
      const classifierId = classifierTableRow.classifier_id
      const possibleClassifierKeysForClassifierIdArray = classifierIdToClassifierAttributeMapping[String(classifierId)]
      const primaryColumnValue = classifierTableRow[classificationTableNamePrimaryKeyColumn]
      for (const classifierAttribute of possibleClassifierKeysForClassifierIdArray) {
        if (!classifierAttributeArray.includes(classifierAttribute)) {
          continue
        }
        if (!returnValue[parent_id]) {
          returnValue[parent_id] = { }
          returnValue[parent_id][classificationTableNameEntityColumn] = parent_id
        }
        if (!returnClassifierIds[parent_id]) {
          returnClassifierIds[parent_id] = { }
          returnClassifierIds[parent_id][classificationTableNameEntityColumn] = parent_id
        }
        // const classifierAttribute = classifierAttributeArray[0]
        const classifierAttributeDetails = classifierAttributes[classifierAttribute]
        const classifierAttributeColumn = classifierAttributeDetails.column
        const classifierAttributeAllowMultiple = classifierAttributeDetails.allow_multiple
        const isSelect = classifierAttributeDetails.is_select ? classifierAttributeDetails.is_select : false
        const pickNull = classifierAttributeDetails.pick_null ? classifierAttributeDetails.pick_null : false
        const isDouble = classifierAttributeDetails.column === 'value_double'

        // infoLog('bel c ch lCD 2, classifierAttribute = ', classifierAttribute, ', classifierId = ', classifierId, ', classifierAttributeAllowMultiple = ', classifierAttributeAllowMultiple, ', classifierAttributeColumn = ', classifierAttributeColumn, ', isSelect = ', isSelect)

        let columnValue = classifierTableRow[classifierAttributeColumn]
        if (columnValue !== null || (columnValue === null && pickNull)) {
          try {
            if (isDouble) {
              columnValue = parseFloat(columnValue)
            }
          } catch (error) {
            console.log('bel c ch lCD 12, error = ', error)
          }
          if (!classifierAttributeAllowMultiple && returnValue[parent_id][classifierAttribute]) {
          } else if (!classifierAttributeAllowMultiple && isSelect) {
            // if (configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1) {
            returnValue[parent_id][classifierAttribute] = [columnValue]
            returnClassifierIds[parent_id][classifierAttribute] = [primaryColumnValue]
            // } else {
            //   returnValue[classifierAttribute] = columnValue;
            // }
          } else if (!classifierAttributeAllowMultiple && !isSelect) {
            returnValue[parent_id][classifierAttribute] = columnValue
            returnClassifierIds[parent_id][classifierAttribute] = primaryColumnValue
          } else if (classifierAttributeAllowMultiple) {
            if (returnValue[parent_id][classifierAttribute]) {
              returnValue[parent_id][classifierAttribute].push(columnValue)
              returnClassifierIds[parent_id][classifierAttribute].push(primaryColumnValue)
            } else {
              returnValue[parent_id][classifierAttribute] = [columnValue]
              returnClassifierIds[parent_id][classifierAttribute] = [primaryColumnValue]
            }
          }
          try {
            if (classifierAttributeDetails.other_load_columns && classifierAttributeDetails.other_load_column_aliases) {
              if (Array.isArray(classifierAttributeDetails.other_load_columns) && classifierAttributeDetails.other_load_columns.length > 0 &&
                Array.isArray(classifierAttributeDetails.other_load_column_aliases) && classifierAttributeDetails.other_load_column_aliases.length > 0 &&
                classifierAttributeDetails.other_load_columns.length === classifierAttributeDetails.other_load_column_aliases.length) {
                for (let counter = 0; counter < classifierAttributeDetails.other_load_columns.length; counter++) {
                  const columnToPickup = classifierAttributeDetails.other_load_columns[counter]
                  const aliasToUse = classifierAttributeDetails.other_load_column_aliases[counter]
                  returnValue[parent_id][aliasToUse] = classifierTableRow[columnToPickup]
                }
              }
            }
          } catch (error) {
            console.log('error in capturing alias values for alternate columns')
            console.log('Suppressed error')
          }
        }
      }
    }
    return { data: returnValue, dataWithClassifierIds: returnClassifierIds }
  } catch (error) {
    console.log('bel c ch lCD 10, error')
    console.log('bel c ch lCD 11, error = ', error)
    throw error
  }
}

module.exports = { loadClassificationConfiguration, getClassificationConfiguration, loadClassificationData, loadClassificationDataWithClassifierIds, saveClassificationData, saveClassifications, loadClassificationConfigurationByMultipleParentIds }
