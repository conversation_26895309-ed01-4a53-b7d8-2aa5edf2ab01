const { generateUUID, validateUUID, errorLog } = require('@krushal-it/mobile-or-server-lib');
const { dbConnections} = require('@krushal-it/ah-orm')
const _ = require('lodash');
const moment = require('moment')

const { getAnimalsByFarmerId, getAnimalById, registerAnimal, animalListForFarmTask, getAnimalDetails, setAnimalActiveStatus , getAnimalHeathRecordDetails ,getMedicationRecords ,getExistingAnimalByIds,deactivateAnimalMilk,insertToAnimalMilkClassification,getloggedMilkingAnimal, updateCreditAmount} = require("./controller");
class LibAnimalService {
  async find(params) {
    try {
      let { page_number, page_limit, customer_id } =
        params.query;
      const { user_id, user_type } = params.headers.token;
      let query = { user_id, user_type };
      if (user_type == 1000220001) customer_id = user_id;
      // if (!uuidValidate(customer_id)) return {};
      if (!validateUUID(customer_id)) return {};
      if (page_limit) query["pageLimit"] = page_limit;
      if (page_number) query["pageNumber"] = page_number;
      if (customer_id) query["customer_id"] = customer_id;
      const animals = await getAnimalsByFarmerId(query);
      return animals;
    } catch (error) {
      errorLog("error in LibAnimalService:18",params,{message:error.message})
      return { result: false, message:error.message };
    }
  }

  async get(id, params) {
    return getAnimalById({ id });
  }

  async getAnimalDetails(data, params) {
    return getAnimalDetails(data, params);
  }

}
class LibUpsertAnimalService {
  async create(data, params) {
    try {
      const { user_id } = params.headers.token;
      if (!data.animal_id){
        data["sales_registration_id_1"] = user_id;
      }
      let aid = await registerAnimal(data);
      await updateCreditAmount(data, aid);
      return { key: aid };
    } catch (error) {
      errorLog("error in LibUpsertAnimalService:35",data,{message:error.message})
      if (error.code) return error.message;
    }
  }
}
class LibAnimalListFarmTaskService {
  async create(data, params) {
    try {
      let result = await animalListForFarmTask(data);
      return { result };
    } catch (error) {
      console.error(error);
      return { result: [] };
    }
  }
}
class LibUpdateAnimalActiveStatus {
  async create(data, params) {
    const result = setAnimalActiveStatus(data, params);
    return result;
  }
}

class LibAnimalHealthRecord {
  async getAnimalHeathRecord(id) {
    try {
     const result =await getAnimalHeathRecordDetails(id);
     return result;
    }
    catch (err) {
      return -1;
    }
  }
  async getMedicationBySrNo(SrNo) {
    try{
      const result = await getMedicationRecords(SrNo);
      return result;
    }
    catch (err) {
      return -1
    }
  }
}

//classifier id **********
class LibAnimalMilking {
  async createOrUpdateMilkLog (data,params) {
    let { milking_date , milkingDetail} = data
    const animalIds = milkingDetail.map((animal) => animal.cattle_id)
    //get existing milking status for catlle Ids For following Date
    const existingAnimalByDate =await getExistingAnimalByIds(animalIds,milking_date);
    //existing animal Ids
    const existingAnimalIds = existingAnimalByDate.map((animal)=>animal.animal_id);
    //get existing animal By ids 
    const animals_to_beDeactivated = this.getAnimalMilkingToBeDeactivated(milkingDetail,existingAnimalByDate); 
    // const animals_to_be_excluded_while_inserting = 
    const deactive_animal_classification_id = animals_to_beDeactivated.map((animal) => animal.animal_classification_id)
    //deactivate the ids
    const deactive_animal_ids = animals_to_beDeactivated.map((animal) => animal.animal_id)

    const idsToBeExludedForInsertAndDelete = existingAnimalIds.filter(item => !deactive_animal_ids.includes(item));
    //remove data to be exluded in payload while inserting
    milkingDetail = milkingDetail.filter(item => {
      if(!idsToBeExludedForInsertAndDelete.includes(item.cattle_id) && item.cattle_milking_log !== null){
        return item
      }
    });
    const response = await dbConnections().main.manager.transaction(async (transaction) => {
      const deactivateAnimalIds =await deactivateAnimalMilk(transaction,deactive_animal_classification_id);
      const animalMilkClassification =await insertToAnimalMilkClassification(transaction,milkingDetail,milking_date);
      return animalMilkClassification
    });


    return response;
    //create new entry 
  }
  getAnimalMilkingToBeDeactivated(milkingDetail,existingAnimalByDate) {
    let convertedMilkingData= this.convertMilkingpayloadToClassiifer(milkingDetail);
    const result = convertedMilkingData.map(item1 => {
      const matchingItem = existingAnimalByDate.find(item2 => 
        item1.animal_id === item2.animal_id 
      );
      if(matchingItem && matchingItem.value_json && !_.isEqual(matchingItem.value_json,item1.value_json)){
         return matchingItem ? matchingItem : null;
      }
      return null;
    });
    return result.filter((animal_classification) => animal_classification !== null); 
  }
  convertMilkingpayloadToClassiifer(milkingDetail){
    const convertedData = [];

    // Loop through each milkingDetail entry
    milkingDetail.forEach((detail) => {
      const { cattle_id, cattle_milking_log } = detail;
      
      // Create an object for each entry in the desired format
      let entry = {
        value_json: cattle_milking_log,
        animal_id: cattle_id,
        classifier_id: **********
      };

      // Push the object into the convertedData array
      convertedData.push(entry);
    });
    return convertedData
  }
  async getAnimalMilikingByFarmerId(farmer_id,params){
    const { date }= { date:params.query.date}

   let query = { user_id:farmer_id, user_type:1000220001 };
    const animals =  await getAnimalsByFarmerId(query);
    //get all animal Ids of that farmer
    const animalIds = animals.data.map((animal)=>animal.animal_id)
    //get start date and end date of the month and year
    const {startDateStr,endDateStr} =this.getStartEndDate(date)
    const loggedMilking = await getloggedMilkingAnimal(animalIds,startDateStr,endDateStr)

    return {...loggedMilking}
  }
  async getAnimalMilikingByAnimalId(params){
    const { date ,animal_id}= { date:params.query.date ,animal_id: params.query.animal_id}
    const {startDateStr,endDateStr} =this.getStartEndDate(date)
    const loggedMilking = await getloggedMilkingAnimal([animal_id],startDateStr,endDateStr ,true)

    return {...loggedMilking}
  }
  getStartEndDate(date){
    const inputDate = moment(date);
    // Get the start date of the month
    const startDate = inputDate.clone().startOf('month');
    // Get the end date of the month
    const endDate = inputDate.clone().endOf('month');
    // Format the start and end dates as strings
    const startDateStr = startDate.format('YYYY-MM-DD');
    const endDateStr = endDate.format('YYYY-MM-DD');
    return {startDateStr,endDateStr}
  }
}

module.exports = { LibAnimalService, LibUpdateAnimalActiveStatus, LibUpsertAnimalService, LibAnimalListFarmTaskService , LibAnimalHealthRecord ,LibAnimalMilking};
