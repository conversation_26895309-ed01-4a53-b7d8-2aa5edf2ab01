// @ts-ignore
const { validateUUID, generateUUID } = require('@krushal-it/mobile-or-server-lib')
// @ts-ignore
const { configurationJSON } = require('@krushal-it/common-core')
// @ts-ignore
const { dbConnections, postProcessRecords, preProcessRecords } = require('@krushal-it/ah-orm')
const { insertToEntity } = require('../../queries/entity')
const { generateAnimalVisualId } = require('../../utils/animal')
const { getentityRelation_v2 } = require('../../queries/entity')
const { getDocumentJSONConfig } = require('../../utils/document')
const { getImagesByAnimalAId } = require('../../queries/document')
const { q_getAnimalPrimaryById, q_animalsWithM1orM2Activity } = require('../../queries/animal')
const { deleteDiseasess, insertToDisease } = require('../../queries/disease')
const { getLocalClassifierConfig } = require('../../utils/reference')
const { q_getAnimalByFarmersId, insertToAnimal, q_animalForFarmActivity } = require('../../queries/animal')
const { CONST_TYPES_RELATIONSHIP, ENTITY_TYPE_ANIMAL,BACK_OFFICE_PERSON, ACTIVE, INACTIVE, CONST_ACTIVITY_ID_CMT, CONST_ACTIVITY_ID_FARM_TICK_CONTROL_SPRAYING ,CONST_ACTIVITY_PARAVET_FARM_DETAIL, RELATION_CUSTOMER_CLASSIFICATION, RELATION_REFERENCE, RELATION_RELATIONSHIP} = require('../../utils/constant')
const { loadClassificationData, saveClassificationData, getClassificationConfiguration } = require('../common/classification.helper')
// @ts-ignore
const { errorLog, infoLog } = require('@krushal-it/mobile-or-server-lib')
// @ts-ignore
const { In, Between } = require('typeorm')
const { insertNote } = require('../../queries/notes')
const { getCompiledQuery } = require('../../queries/index.js')
// @ts-ignore
const moment = require('moment')
const { classifiers, record_statuses, relationship_types } = require('../../ENUMS.js')
const { ensureArray } = require('../../utils/utils.js')

const getAnimalsByFarmerId = async (options) => {
  let { pageLimit, pageNumber } = options
  if (pageNumber <= 0) return { data: [], totalPages: 0, messsage: 'Invalid page number' }
  // @ts-ignore
  let pageOffset = parseInt((pageNumber - 1) * pageLimit)
  const queryAnimalByFarmer = q_getAnimalByFarmersId(options)
  const countQuery = `SELECT COUNT(*) as count FROM (${queryAnimalByFarmer}) as t`
  const countRes = await dbConnections().main.manager.query(countQuery)
  const { count } = countRes[0]
  // @ts-ignore
  let totalPages = parseInt(count / parseInt(pageLimit))
  // @ts-ignore
  totalPages += parseInt(count % parseInt(pageLimit)) > 0 ? 1 : 0
  if (pageNumber > totalPages) {
    return { data: [], totalPages, messsage: `Max allowed page number is ${totalPages}` }
  }
  if (!pageLimit && !pageNumber) {
    pageLimit = count
    pageOffset = 0
  }
  const animalList = await dbConnections().main.manager.createQueryBuilder().select('*').from(`(${queryAnimalByFarmer})`, 'animals').skip(pageOffset).take(pageLimit).execute()
  postProcessRecords(undefined, animalList, { json_columns: ['animal_breed_1', 'subscription_plan_1', 'document_information', 'client_document_information'] })
  return { data: animalList, totalPages }
}

const getAnimalDetails = async (data, params) => {
  try {
    const { token } = params.headers
    const { user_id, user_type } = token
    let queryOptions = { user_id, user_type, animal_id: data.animal_id }

    let animalPrimaryData = {}

    if (data.basic_info.required) {
      const animalPrimaryQuery = q_getAnimalPrimaryById(queryOptions)
      animalPrimaryData = await dbConnections().main.manager.query(animalPrimaryQuery)
      if (animalPrimaryData.length === 0) {
        console.log('animal data not found with id ', queryOptions.animal_id)
        return false
      }
      animalPrimaryData = animalPrimaryData[0]
    }

    let classificationData = {}

    if (data.additional_info.required) {
      let classifiers = Object.keys(getClassificationConfiguration()['ANIMAL_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP)

      if (data.additional_info.include_all) {
        // use all classifiers
      } else if (data.additional_info.attributes.length > 0) {
        // find intersection of the input and classifier attributes array
        classifiers = classifiers.filter((item) => data.additional_info.attributes.indexOf(item) !== -1)
      } else {
        // do nothing
        console.log('no additional attributes requested for animal')
        classifiers = []
      }

      if (classifiers.length > 0) {
        classificationData = await loadClassificationData('ANIMAL_CLASSIFICATION', queryOptions.animal_id, classifiers)
      }
    }

    let diseaseData = []

    if (data.disease_info.required) {
      let classifiers = Object.keys(getClassificationConfiguration()['ANIMAL_DISEASE_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP)

      if (data.disease_info.include_all) {
        // use all classifiers
      } else if (data.disease_info.attributes.length > 0) {
        // find intersection of the input and classifier attributes array
        classifiers = classifiers.filter((item) => data.additional_info.attributes.indexOf(item) !== -1)
      } else {
        // do nothing
        console.log('no additional attributes requested for diseases')
        classifiers = []
      }

      if (classifiers.length > 0) {
        let diseaseArray = await dbConnections().main.repos['animal_disease'].find({
          where: [{ animal_id: queryOptions.animal_id, active: 1000100001 }],
          select: { animal_disease_id: true, disease_id: true },
        })

        for (let diseaseIndex = 0; diseaseIndex < diseaseArray.length; diseaseIndex++) {
          const { disease_id, animal_disease_id } = diseaseArray[diseaseIndex]
          let diseaseClassification = await loadClassificationData('ANIMAL_DISEASE_CLASSIFICATION', animal_disease_id, classifiers)
          diseaseData.push({ disease_id, animal_disease_id, ...diseaseClassification })
        }
      }
    }

    let animal = { ...animalPrimaryData, ...classificationData }
    // @ts-ignore
    animal.disease = diseaseData

    if (data.documents.required) {
      let document_type_id = data.documents.document_type_ids ? data.documents.document_type_ids : []
      let document_ids = document_type_id.length ? document_type_id : false
      let documents = await getImagesByAnimalAId(queryOptions.animal_id, document_ids)
      // @ts-ignore
      animal.files = await mapDocumentType(documents)
    }

    return animal
  } catch (error) {
    errorLog('error in getAnimalDetails:77', params, { message: error.message })
    return { result: false, message: error.message }
  }
}

const getAnimalById = async (options = {}) => {
  try {
    console.log('bel s a c gABI 1, options = ', options)
    //TODO: control access
    const { id } = options
    const FARMER_TO_ANIMAL = CONST_TYPES_RELATIONSHIP.farmer_animal
    // if (!uuid.validate(id)) return {};
    if (!validateUUID(id)) return {}
    let queryOptions = { animal_id: id }
    const animalPrimaryQuery = q_getAnimalPrimaryById(queryOptions)
    let animalPrimary = await dbConnections().main.manager.query(animalPrimaryQuery)
    if (animalPrimary.length == 0) {
      return {}
    }
    animalPrimary = animalPrimary[0]
    const classifiers = Object.keys(getClassificationConfiguration()['ANIMAL_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP)
    let animalClassification = await loadClassificationData('ANIMAL_CLASSIFICATION', queryOptions.animal_id, classifiers)
    let is_freelance = false
    if (animalClassification.subscription_plan_1 && animalClassification.subscription_plan_1.length && animalClassification.subscription_plan_1[0] === 1000240046) {
      is_freelance = true
    }
    let animal = { ...animalPrimary, ...animalClassification, is_freelance }
    if (animalClassification.sales_attribution_id_1) {
      let staff = await dbConnections().main.repos['staff'].findOne({
        where: [{ staff_id: animalClassification.sales_attribution_id_1 }],
        select: { staff_name_l10n: true },
      })
      staff = postProcessRecords(undefined, staff, { json_columns: ['staff_name_l10n'] })
      if (staff) {
        animal = { ...animal, staff_name_l10n: staff.staff_name_l10n }
      }
    }
    const owner = await getentityRelation_v2({
      entity_relationship_type_id: FARMER_TO_ANIMAL,
      entity_2_entity_uuid: id,
      select: ['entity_1_entity_uuid'],
    })
    let documents = await getImagesByAnimalAId(id)
    animal.files = await mapDocumentType(documents)
    animal.owner = owner
    animal.disease = []
    let diseaseArray = await dbConnections().main.repos['animal_disease'].find({
      where: [{ animal_id: id, active: 1000100001 }],
      select: { animal_disease_id: true, disease_id: true },
    })

    if (diseaseArray.length > 0) {
      const diseaseClassifiers = Object.keys(getClassificationConfiguration()['ANIMAL_DISEASE_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP)
      for (let diseaseIndex = 0; diseaseIndex < diseaseArray.length; diseaseIndex++) {
        const { disease_id, animal_disease_id } = diseaseArray[diseaseIndex]
        let diseaseClassification = await loadClassificationData('ANIMAL_DISEASE_CLASSIFICATION', animal_disease_id, diseaseClassifiers)
        animal.disease.push({ disease_id, animal_disease_id, ...diseaseClassification })
      }
    }
    return animal
  } catch (error) {
    errorLog('error in getAnimalById:77', options, { message: error.message })
    return { result: false, message: error.message }
  }
}
const mapDocumentType = async (files) => {
  let docJSONConfig = await getDocumentJSONConfig({ key_type: 'ref_reference_reference_id' })
  for (let i = 0; i < files.length; i++) {
    files[i].document_type = docJSONConfig[files[i].document_type_id].ref_reference_field_name
  }
  return files
}

const registerAnimal = async (data) => {
  console.log('bel s a c rA 1, data = ', data)
  const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
  const PAGE = data.page
  const ENTITY = ENTITY_TYPE_ANIMAL
  const dataSource = dbConnections().main.manager

  // All of the local fiedls are expected in page 1
  if (PAGE === 1) {
    console.log('bel s a c rA 1a')
    // Handle all local fiedls here
    const localCLassifier = getLocalClassifierConfig(ENTITY)
    const localCLassifierKeys = Object.keys(localCLassifier)
    localCLassifierKeys.push('animal_type_for_animal_table')
    const values = {}
    // Pulling keys from verified key list
    localCLassifierKeys.forEach((key) => {
      if (data[key]) {
        values[key] = data[key]
      }
    })
    console.log('bel s a c rA 1b, values = ', values)
    if (data['animal_type_for_animal_table']) {
      console.log('bel s a c rA 1c')
      values['animal_type'] = data['animal_type_for_animal_table']
      delete values['animal_type_for_animal_table']
    }
    console.log('bel s a c rA 1d, values = ', values)
    //customer_id will come from frontend
    //Insert
    if (!data.animal_id) {
      console.log('bel s a c rA 1e')
      if (isPostgres) {
        let animal_visual_id = await generateAnimalVisualId(data.customer_id)
        values['animal_visual_id'] = animal_visual_id
      }
      console.log('bel s a c rA 1f, values = ', values)
      data.animal_id = await insertToAnimal(values)
      console.log('bel s a c rA 1g')
      data.animal_id = data.animal_id.identifiers[0].animal_id
      const farmerAnimalRelation = CONST_TYPES_RELATIONSHIP.farmer_animal
      // @ts-ignore
      data.customer_id = await insertToEntity(data.customer_id, data.animal_id, farmerAnimalRelation)
    }
    //remove attribute id if present in edit
    else {
      if (data.sales_attribution_id_1) {
        delete data['sales_attribution_id_1']
      }
    }
    console.log('bel s a c rA 1h')
  }

  const dataKeyList = Object.keys(data)
  const classificationConfig = getClassificationConfiguration()['ANIMAL_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
  const upsertData = {}
  for (let keyIndex = 0; keyIndex < dataKeyList.length; keyIndex++) {
    if (classificationConfig[dataKeyList[keyIndex]]) {
      upsertData[dataKeyList[keyIndex]] = data[dataKeyList[keyIndex]]
    }
  }
  console.log('bel s a c rA 2, upsertData = ', upsertData)
  await dataSource.transaction(async (transactionalEntityManager) => {
    console.log('bel s a c rA 3')
    await saveClassificationData(transactionalEntityManager, 'ANIMAL_CLASSIFICATION', data.animal_id, upsertData)
    if (Array.isArray(data.disease)) {
      console.log('bel s a c rA 4')
      let diseaseClassifiers = getClassificationConfiguration()['ANIMAL_DISEASE_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
      await deleteDiseasess(data.animal_id, { datasource: transactionalEntityManager })
      for (let diseaseIndex = 0; diseaseIndex < data.disease.length; diseaseIndex++) {
        let disease = data.disease[diseaseIndex]
        let { animal_disease_type_1, disease_date } = disease
        let disease_id = animal_disease_type_1
        if (Array.isArray(disease_id) && disease_id.length > 0) {
          disease_id = disease_id[0]
        }
        if (!disease_id) continue
        let diseasekeys = Object.keys(disease)
        let diseaseUpsertData = {}
        for (let diseasekeysIndex = 0; diseasekeysIndex < diseasekeys.length; diseasekeysIndex++) {
          if (diseaseClassifiers[diseasekeys[diseasekeysIndex]]) {
            diseaseUpsertData[diseasekeys[diseasekeysIndex]] = disease[diseasekeys[diseasekeysIndex]]
          }
        }
        let localValue = { animal_id: data.animal_id, disease_id }
        if (disease_date && disease_date !== null && disease_date !== '') {
          try {
            const diseaseDate = new Date(disease_date)
            diseaseDate.setHours(15)
            diseaseDate.setMinutes(0)
            diseaseDate.setSeconds(0)
            localValue.disease_date = diseaseDate
          } catch (error) {
            console.log('could not create date object')
          }
        }
        let insertedId = await insertToDisease(localValue, transactionalEntityManager)
        insertedId = insertedId.identifiers[0].animal_disease_id
        infoLog(insertedId)
        await saveClassificationData(transactionalEntityManager, 'ANIMAL_DISEASE_CLASSIFICATION', insertedId, diseaseUpsertData)
      }
    }
  })
  console.log('bel s a c rA 5')
  return data.animal_id
}

const animalListForFarmTask = async (options = {}) => {
  try {
    console.log('bel s a c aLFFT 1')
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? true : false
    if (!options.activity_id) throw new Error('activity_id is required in options')
    if (!options.farmer_id) throw new Error('farmer_id is required in options')
    const { activity_id, farmer_id } = options
    let baseQueryAllAnimals = q_animalForFarmActivity()
    let queryBuilderAnimals = dbConnections().main.manager.createQueryBuilder().select('*').from(`(${baseQueryAllAnimals})`, 'farmAnimal')

    // get the list of cattles in the farm for CMT
    if (activity_id == CONST_ACTIVITY_ID_CMT || activity_id == CONST_ACTIVITY_PARAVET_FARM_DETAIL) {
      queryBuilderAnimals.andWhere('customer_id = :farmer_id', { farmer_id })
      const animals_data = await queryBuilderAnimals.execute()

      // calculate the pregnant months upto date
      // Iterate through each animal data
      for (const animal_data of animals_data) {
        // Check if the animal is pregnant
        if (animal_data.number_of_months_pregnant && animal_data.number_of_months_pregnant > 0) {
          // Check if there's a recorded date for the number of months pregnant
          if (animal_data.number_of_months_pregnant_as_of_date) {
            const startDate = moment(animal_data.number_of_months_pregnant_as_of_date);
            const duration = moment.duration(moment().diff(startDate));
            const months = Math.floor(parseInt(animal_data.number_of_months_pregnant) + duration.asMonths());
            if (months > 9) {
              animal_data.number_of_months_pregnant = 0
            } else {
              animal_data.number_of_months_pregnant = months
            }         
          }
        }
        // calculate the age of the animal upto date in milliseconds
        const ageInYears = animal_data.animal_dob?Math.floor(moment().diff(moment(animal_data.animal_dob), 'years', true)):-1;
        animal_data.age_in_years = ageInYears;
        delete animal_data.number_of_months_pregnant_as_of_date
      }
      // Return the data
      return animals_data
    }

    if (activity_id == CONST_ACTIVITY_ID_FARM_TICK_CONTROL_SPRAYING) {
      queryBuilderAnimals.andWhere('customer_id = :farmer_id', { farmer_id })

      let baseQueryInvalidAnimals = q_animalsWithM1orM2Activity()
      let queryBuilderInvalidAnimals = dbConnections().main.manager.createQueryBuilder().select('DISTINCT animal_id').from(`(${baseQueryInvalidAnimals})`, 'invalidAnimals').where('1=1')
      if (isPostgres) {
        queryBuilderInvalidAnimals = queryBuilderInvalidAnimals.andWhere('(DATE(dtcbm1)  BETWEEN DATE( :activity_date )-10 AND DATE( :activity_date )+10)', { activity_date: options.activity_date })
      } else {
        queryBuilderInvalidAnimals = queryBuilderInvalidAnimals.andWhere("(DATE(dtcbm1)  BETWEEN DATE( :activity_date, '-10 days' ) AND DATE( :activity_date, '+10 days' ) )", { activity_date: options.activity_date })
      }
      queryBuilderInvalidAnimals = queryBuilderInvalidAnimals.andWhere('customer_id = :farmer_id', { farmer_id })

      let invalidAnimalList = await queryBuilderInvalidAnimals.execute()
      if (invalidAnimalList.length > 0) {
        let invalidIds = invalidAnimalList.map((val) => val.animal_id)
        queryBuilderAnimals.andWhere('animal_id NOT IN (:...invalidIds)', { invalidIds })
      }
      const data = await queryBuilderAnimals.execute()
      return data
    }
    return []
  } catch (error) {
    console.log(error)
    throw error
  }
}

const setAnimalActiveStatus = async (data, params) => {
  try {
    const validStatus = new Set([ACTIVE, INACTIVE])
    const isPostgres = configurationJSON().IS_NODE_SERVER && configurationJSON().IS_NODE_SERVER === 1 ? 1 : 0
    const { user_type, user_id } = params.headers.token

    if (!validateUUID(user_id)) return {}

    if (user_type !== BACK_OFFICE_PERSON) throw new Error('not authorized')

    let { animal_id, note, active_status } = data

    active_status = parseInt(active_status)
    if (!validStatus.has(active_status)) throw new Error('not a valid status')

    animal_id = Array.isArray(animal_id) ? animal_id : [animal_id]
    let classificationData = {}

    const animalClassfierMap = getClassificationConfiguration()['ANIMAL_CLASSIFICATION'].CLASSIFIER_ATTRIBUTE_TO_CLASSIFIER_ID_MAP
    for (const key of Object.keys(animalClassfierMap)) {
      if (data[key]) {
        classificationData[key] = data[key]
      }
    }
    const notes = []
    for (const id of animal_id) {
      let newValue = {
        note_id: generateUUID(),
        note: { ul: note },
        note_type_id: 1000440004,
        creator_type_id: user_type,
        creator_uuid: user_id,
        entity_1_type_id: 1000220002,
        entity_1_uuid: id,
        note_time: new Date(),
      }
      notes.push(newValue)
    }
    const valueToBeUpdated = { active: active_status }
    if (!isPostgres) {
      valueToBeUpdated['data_sync_status'] = 1000101003
    }
    await dbConnections().main.manager.transaction(async (transactionalEntityManager) => {
      await transactionalEntityManager.getRepository('animal').update({ animal_id: In(animal_id) }, valueToBeUpdated)
      for (const id of animal_id) {
        if (Object.keys(classificationData).length > 0 && id) {
          await saveClassificationData(transactionalEntityManager, 'ANIMAL_CLASSIFICATION', id, classificationData)
        }
      }
      await insertNote(notes, transactionalEntityManager)
    })

    return { result: true }
  } catch (error) {
    errorLog('error in LibMakeAnimalInactive', params, { message: error.message })
    return { result: false, message: error.message }
  }
}

const getAnimalHeathRecordDetails = async (id) => {
  try {
    let query = getCompiledQuery('animal-history', { animal_id: id })
    console.log('les asjdha dba d 1 ', query)

    // const propagatedAnimalQuery = await queryBuilder.select("*").execute();
    let animalData = await dbConnections().main.manager.query(query)
    return animalData
  } catch (err) {
    return { error: err }
  }
}

const getMedicationRecords = async (SrNo) => {
  try {
    const prescriptionKeystoColMaps = {
      medicine: {
        cols: [
          { client_key: 'id', db_key: 'value_reference_id' },
          { client_key: 'dosage', db_key: 'value_double' },
          { client_key: 'prescribed_info', db_key: 'value_json' },
        ],
        classifier_id: 2000000185,
        key: 'medicine',
      },
    }
    let care_calendar = await dbConnections().main.repos['care_calendar_classification'].find({
      where: { active: ACTIVE, classifier_id: 2000000125, value_string_256: SrNo },
      select: { care_calendar_id: true },
    })
    const calendarIdArrays = care_calendar.map((elem) => elem.care_calendar_id)
    let care_calendar_data = await dbConnections().main.repos['care_calendar'].find({
      where: { care_calendar_id: In(calendarIdArrays) },
      select: { care_calendar_id: true, activity_date: true },
    })

    let activityMedicineData = await dbConnections().main.repos['care_calendar_classification'].find({
      where: { care_calendar_id: In(calendarIdArrays), classifier_id: prescriptionKeystoColMaps.medicine.classifier_id, active: ACTIVE },
      select: { value_reference_id: true, value_double: true, value_json: true, care_calendar_id: true },
    })
    const calendarIdMap = new Map()

    // Populate calendarIdMap with objects from care calendar using their IDs as keys
    care_calendar_data.forEach((calendar) => {
      calendarIdMap.set(calendar.care_calendar_id, { ...calendar, medicines: [] })
    })
    activityMedicineData = postProcessRecords(undefined, activityMedicineData, { json_columns: ['value_json', 'value_l10n'] })

    activityMedicineData.forEach((med) => {
      if (calendarIdMap.has(med.care_calendar_id)) {
        if (med.value_json && med.value_json.status === 'ADMINISTERED') {
          calendarIdMap.get(med.care_calendar_id).medicines.push({ ...med })
        }
      }
    })
    let data = Array.from(calendarIdMap.values())
    data = data.filter((item) => item.medicines.length > 0)

    return data
  } catch (err) {
    console.log(err)
    return err
  }
}

//classifier id 2000000192
const getExistingAnimalByIds = async (AnimalIds, date) => {
  try {
    let animal_milk = await dbConnections().main.repos['animal_classification'].find({
      where: { active: ACTIVE, classifier_id: 2000000192, value_date: date, animal_id: In(AnimalIds) },
    })
    animal_milk = postProcessRecords(undefined, animal_milk, { json_columns: ['value_date'] })
    return animal_milk
  } catch (error) {
    return error
  }
}
const deactivateAnimalMilk = async (transaction, animal_classification_id) => {
  try {
    let inactiveAnimalMilk = {
      active: INACTIVE,
    }
    //add pre process record
    inactiveAnimalMilk = preProcessRecords(dbConnections().main.entities['animal_classification'], inactiveAnimalMilk, null)
    if (inactiveAnimalMilk) delete inactiveAnimalMilk.animal_classification_id
    const result = await transaction.getRepository('animal_classification').update(
      {
        animal_classification_id: In(animal_classification_id),
        classifier_id: 2000000192,
        active: ACTIVE,
      },
      inactiveAnimalMilk
    )
    return result
  } catch (error) {
    return error
  }
}

const insertToAnimalMilkClassification = async (transaction, milkingDetail, milking_date) => {
  try {
    const convertedData = []

    // Loop through each milkingDetail entry
    milkingDetail.forEach((detail) => {
      const { cattle_id, cattle_milking_log } = detail

      // Create an object for each entry in the desired format
      let entry = {
        value_json: cattle_milking_log,
        animal_id: cattle_id,
        value_date: milking_date,
        classifier_id: 2000000192,
      }
      entry = preProcessRecords(dbConnections().main.entities['animal_classification'], entry, { json_columns: ['value_json'] })

      // Push the object into the convertedData array
      convertedData.push(entry)
    })
    return await transaction.save('animal_classification', convertedData)
  } catch (error) {
    return error
  }
}

const getloggedMilkingAnimal = async (animalIds, startDate, endDate, singleAnimal = false) => {
  try {
    let animal_milk = await dbConnections().main.repos['animal_classification'].find({
      where: {
        active: ACTIVE,
        classifier_id: 2000000192,
        value_date: Between(startDate, endDate),
        animal_id: In(animalIds),
      },
      select: {
        value_date: true,
        value_json: true,
        animal_id: true,
      },
    })
    let calulations = {}
    let total_count_inLtr = 0
    let estimate = 0
    animal_milk = postProcessRecords(undefined, animal_milk, { json_columns: ['value_date'] })
    let result = animal_milk.reduce((acc, item) => {
      // Extract date without time
      let date = new Date(item.value_date).toISOString().split('T')[0]
      // Check if there is an existing entry with the same date
      const existingEntry = acc.find((entry) => entry.date === date)

      const value_json = {
        morning_quantity: item.value_json.morning,
        evening_quantity: item.value_json.evening,
      }
      // Prepare the cattle data
      const cattleData = {
        animal_id: item.animal_id,
        value_json: value_json,
      }
      // @ts-ignore
      total_count_inLtr = parseFloat(total_count_inLtr) + parseFloat(item.value_json.morning ? item.value_json.morning : 0) + parseFloat(item.value_json.evening ? item.value_json.evening : 0)

      if (existingEntry && !singleAnimal) {
        // If an entry with the same date exists, add cattle data to it
        existingEntry.cattle.push(cattleData)
      } else {
        // If no entry with the same date exists, create a new entry

        acc.push({
          date,
          milking_date: moment(date).add(1, 'days').format('YYYY-MM-DD'),
          cattle: !singleAnimal ? [cattleData] : value_json,
        })
      }

      return acc
    }, [])

    result = result.map((item) => {
      // @ts-ignore
      let notFilled = false
      // if(item.value_json.morning !== undefined && item.value_json.evening !== undefined){
      //   notFilled =true;
      // }
      let status = 'Pending'

      if (singleAnimal && item.cattle.morning_quantity !== undefined && item.cattle.evening_quantity !== undefined) {
        status = 'Completed'
      }
      if (!singleAnimal && item.cattle.length === animalIds.length) {
        let moning_len = item.cattle.filter((cattle) => cattle.value_json.morning_quantity !== undefined).length
        let evening_len = item.cattle.filter((cattle) => cattle.value_json.evening_quantity !== undefined).length
        if (moning_len === animalIds.length && evening_len === animalIds.length) {
          status = 'Completed'
        }
      }
      return { status, ...item }
    })

    calulations['monthly_milk'] = total_count_inLtr
    const dateToCheck = moment(startDate)
    const daysInMonth = dateToCheck.daysInMonth()
    const currentDate = moment()
    if (dateToCheck.isSame(currentDate, 'month')) {
      const daysCompleted = currentDate.date()
      const average_milk = total_count_inLtr / daysCompleted
      calulations['average_milk'] = average_milk
      const remaining_days = daysInMonth - daysCompleted
      estimate = average_milk * remaining_days
    } else {
      calulations['average_milk'] = total_count_inLtr / daysInMonth
    }
    calulations['estimate'] = estimate
    return { calulations, animal_milk: result }
  } catch (error) {
    return error
  }
}

// checks if there is a credit given to the customer per cattle registration
// updates the customer credit limit accordingly
const updateCreditAmount = async (data, animal_id) => {

  await dbConnections().main.manager.transaction(async (transactionalEntityManager) => {

    // check if the request for registration or update conatins the subscription plan information
    if (data['subscription_plan_1'] && data['healthcare_plan_subscription_date_1']) {

      const cattleSubscriptionPlanId = ensureArray(data['subscription_plan_1']);
      // get the plan details
      // @ts-ignore
      let subscriptionPlanInfo = await transactionalEntityManager.getRepository(RELATION_REFERENCE).findOne({
        where: [
          {
            reference_id: cattleSubscriptionPlanId[0],
            active: record_statuses.ACTIVE,
          },
        ],
      })
      const planCreditAmount = subscriptionPlanInfo.reference_information?.credit_details?.amount

      if (planCreditAmount) {
        // @ts-ignore
        const entityRelationshipDetails = await transactionalEntityManager.getRepository(RELATION_RELATIONSHIP).findOne({
          where: [
            {
              entity_2_entity_uuid: animal_id,
              entity_relationship_type_id: relationship_types.FARMER_TO_ANIMAL,
              active: record_statuses.ACTIVE,
            },
          ],
        })

        // get existing customer credit value
        let customerCreditData = await transactionalEntityManager.getRepository(RELATION_CUSTOMER_CLASSIFICATION).findOne({
          where: [
            {
              customer_id: entityRelationshipDetails.entity_1_entity_uuid,
              classifier_id: classifiers.CUSTOMER_CREDIT_LIMIT,
              active: record_statuses.ACTIVE,
            },
          ],
        })

        let currentCustomerCredit = parseFloat(customerCreditData?.value_double ?? 0.0) ;
        customerCreditData = customerCreditData ?? {};
        customerCreditData['value_double'] = currentCustomerCredit + planCreditAmount
        customerCreditData['customer_id'] = entityRelationshipDetails.entity_1_entity_uuid
        customerCreditData['classifier_id'] = classifiers.CUSTOMER_CREDIT_LIMIT

        await transactionalEntityManager.getRepository(RELATION_CUSTOMER_CLASSIFICATION).save(customerCreditData);

      }
    }
  })

  return false
}

module.exports = { getAnimalsByFarmerId, getAnimalById, registerAnimal, animalListForFarmTask, getAnimalDetails, setAnimalActiveStatus, getAnimalHeathRecordDetails, getMedicationRecords, getExistingAnimalByIds, deactivateAnimalMilk, insertToAnimalMilkClassification, getloggedMilkingAnimal, updateCreditAmount }
