const { classifiers } = require("../../../ENUMS");
exports.task_details = `SELECT
    CARE_CALENDAR.entity_type_id,
    SR.VALUE_STRING_256 AS ticket_no,
    CARE_CALENDAR.activity_id,
    CARE_CALENDAR.entity_uuid,
    CARE_CALENDAR.activity_date,
    CARE_CALENDAR.USER_TASK_COMPLETION_TIME AS activity_completed_on,
    REF_ACTIVITY.ACTIVITY_NAME_L10N AS activity_name,
    FOLLOW_UP.ACTIVITY_DATE AS follow_up_date
FROM
    MAIN.CARE_CALENDAR
    LEFT JOIN MAIN.CARE_CA<PERSON>NDAR_CLASSIFICATION SR ON SR.CARE_CALENDAR_ID = CARE_CALENDAR.CARE_CALENDAR_ID
    AND SR.CLASSIFIER_ID = ${classifiers.COMPLAINT_NUMBER}
    LEFT JOIN MAIN.CARE_CALENDAR_CLASSIFICATION CCC ON CCC.VALUE_REFERENCE_UUID = CARE_CALENDAR.CARE_CALENDAR_ID
    AND CCC.CLASSIFIER_ID = ${classifiers.FOLLOW_UP_TASK_ID}
    LEFT JOIN MAIN.CARE_CALENDAR FOLLOW_UP ON FOLLOW_UP.CARE_CALENDAR_ID = CCC.CARE_CALENDAR_ID
LEFT JOIN MAIN.REF_ACTIVITY ON REF_ACTIVITY.ACTIVITY_ID = CARE_CALENDAR.ACTIVITY_ID
WHERE
    CARE_CALENDAR.CARE_CALENDAR_ID = :care_calendar_id
`