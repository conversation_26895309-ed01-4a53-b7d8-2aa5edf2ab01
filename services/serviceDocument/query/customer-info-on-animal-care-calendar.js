const { classifiers, record_statuses, relationship_types } = require("../../../ENUMS");

exports.customer_info_on_animal_care_calendar=`
SELECT
    CUSTOMER.customer_id,
    CUSTOMER.customer_visual_id,
    CUSTOMER.customer_name_l10n,
    CUSTOMER.mobile_number,
    GEO.village_name_l10n,
    GEO.taluk_name_l10n,
    GEO.district_name_l10n
FROM
    MAIN.CARE_CALENDAR
    LEFT JOIN MAIN.ENTITY_RELATIONSHIP ER ON ER.ENTITY_RELATIONSHIP_TYPE_ID = ${relationship_types.FARMER_TO_ANIMAL}
    AND ER.ENTITY_2_ENTITY_UUID = CARE_CALENDAR.ENTITY_UUID
    LEFT JOIN MAIN.CUSTOMER ON CUSTOMER.CUSTOMER_ID = ER.ENTITY_1_ENTITY_UUID
    AND CUSTOMER.ACTIVE = ${record_statuses.ACTIVE}
    LEFT JOIN MAIN.CUSTOMER_CLASSIFICATION VILLAGE ON VILLAGE.CUSTOMER_ID = CUSTOMER.CUSTOMER_ID
    AND VILLAGE.CLASSIFIER_ID = ${classifiers.VILLAGE_FARMER_BELONGS_TO}
    LEFT JOIN MAIN.REF_SDTV_VIEW GEO ON GEO.VILLAGE_ID = VILLAGE.VALUE_REFERENCE_ID
WHERE
    CARE_CALENDAR_ID = :care_calendar_id
`