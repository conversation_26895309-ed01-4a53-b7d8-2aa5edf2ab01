const { classifiers, staff_activity_statuses, record_statuses } = require("../../../ENUMS");

exports.animals_on_farmer_care_calendar = `
SELECT
    CARE_CALENDAR.care_calendar_id,
    ANIMAL.animal_id
FROM
    MAIN.CARE_CALENDAR
    LEFT JOIN MAIN.CARE_<PERSON><PERSON><PERSON>AR_CLASSIFICATION CCC ON CCC.CLASSIFIER_ID = ${classifiers.LINKED_CARE_CALENDAR_TASK}
    AND CCC.VALUE_REFERENCE_UUID = CARE_CALENDAR.CARE_CALENDAR_ID
    LEFT JOIN MAIN.CARE_CALENDAR LINKED_TASK ON LINKED_TASK.CARE_CALENDAR_ID = CCC.CARE_CALENDAR_ID
    AND LINKED_TASK.CALENDAR_ACTIVITY_STATUS = ${staff_activity_statuses.VISIT_COMPLETED}
    LEFT JOIN MAIN.ANIMAL ON ANIMAL.ANIMAL_ID = LINKED_TASK.ENTITY_UUID AND ANIM<PERSON>.ACTIVE = ${record_statuses.ACTIVE}
WHERE
    CARE_CALENDAR.CARE_CALENDAR_ID = :care_calendar_id
`