const { staff_type,record_statuses, geography_types, classifiers } = require("../../../ENUMS");

exports.get_staffs_on_customer= `
    SELECT
        PARAVET_EG.ENTITY_UUID AS paravet_id,
        PARAVET.STAFF_NAME_L10N AS paravet_name,
        PARAVET.MOBILE_NUMBER AS paravet_mobile_number,
        VET_EG.ENTITY_UUID AS vet_id,
        VET.STAFF_NAME_L10N AS vet_name,
        VET.MOBILE_NUMBER AS vet_mobile_number
    FROM
        MAIN.CUSTOMER
        LEFT JOIN MAIN.CUSTOMER_CLASSIFICATION VILLAGE ON VILLAGE.CLASSIFIER_ID = ${classifiers.VILLAGE_FARMER_BELONGS_TO}
	    AND VILLAGE.CUSTOMER_ID = CUSTOMER.CUSTOMER_ID
        LEFT JOIN MAIN.ENTITY_GEOGRAPHY AS VET_EG ON VET_EG.GEOGRAPHY_TYPE_ID = ${geography_types.VILLAGE}
        AND VET_EG.ENTITY_TYPE_ID = ${staff_type.VET}
        AND VET_EG.GEOGRAPHY_ID = VILLAGE.VALUE_REFERENCE_ID
        AND VET_EG.ACTIVE = ${record_statuses.ACTIVE}
        LEFT JOIN MAIN.ENTITY_GEOGRAPHY AS PARAVET_EG ON PARAVET_EG.GEOGRAPHY_TYPE_ID = ${geography_types.VILLAGE}
        AND PARAVET_EG.ENTITY_TYPE_ID = ${staff_type.PARAVET}
        AND PARAVET_EG.GEOGRAPHY_ID = VILLAGE.VALUE_REFERENCE_ID
        AND PARAVET_EG.ACTIVE = ${record_statuses.ACTIVE}
        LEFT JOIN MAIN.STAFF VET ON VET.STAFF_ID = VET_EG.ENTITY_UUID
        LEFT JOIN MAIN.STAFF PARAVET ON PARAVET.STAFF_ID = PARAVET_EG.ENTITY_UUID
    WHERE
        CUSTOMER.CUSTOMER_ID = :customer_id
` 