const { classifiers } = require("../../../ENUMS");

exports.animal_classification = `
SELECT
    animal.animal_visual_id,
    animal_name.value_string_256 as animal_name,
    EAR_TAG.value_string_256 as animal_ear_tag,
    ANIMAL_SUBSCRIPTION_PLAN.value_reference_id as animal_subscription_plan
FROM
    MAIN.ANIMAL
    LEFT JOIN MAIN.ANIMAL_CLASSIFICATION EAR_TAG ON EAR_TAG.ANIMAL_ID = ANIMAL.ANIMAL_ID
    AND EAR_TAG.CLASSIFIER_ID = ${classifiers.EAR_TAG_NUMBER}
    LEFT JOIN MAIN.ANIMAL_CLASSIFICATION ANIMAL_NAME ON ANIMAL_NAME.ANIMAL_ID = ANIMAL.ANIMAL_ID
    AND ANIMAL_NAME.CLASSIFIER_ID = ${classifiers.NAME_OF_ANIMAL}
    LEFT JOIN MAIN.ANIMAL_CLASSIFICATION ANIMAL_SUBSCRIPTION_PLAN ON ANIMAL_SUBSCRIPTION_PLAN.ANIMAL_ID = ANIMAL.ANIMAL_ID
    AND ANIMAL_SUBSCRIPTION_PLAN.CLASSIFIER_ID = ${classifiers.SUBSCRIPTION_PLAN}
WHERE
    ANIMAL.ANIMAL_ID IN (:...animal_ids)
`