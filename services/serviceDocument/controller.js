const { dbConnections, preProcessRecords, postProcessRecords } = require('@krushal-it/ah-orm')
const { customer_info_on_animal_care_calendar } = require('./query/customer-info-on-animal-care-calendar')
const {customer_info_on_customer_care_calendar} = require("./query/customer-info-on-customer-care-calendar")
const { get_staffs_on_customer } = require('./query/get-staffs-on-customer')
const { animals_on_farmer_care_calendar } = require('./query/animals-on-farmer-care-calendar')
const { animals_on_animal_care_calendar } = require('./query/animals-on-animal-care-calendar')
const { animal_classification } = require('./query/animal-classification')
const { activity_configuration,record_statuses, classifiers } = require('../../ENUMS')
const { task_details } = require('./query/task-details')
const { loadClassificationData, loadClassificationDataWithClassifierIds } = require('../../services/common/classification.helper')
const { In } = require("typeorm");
const taskRepositoryInstance = require('../../repositories/taskRepository')
const customerRepositoryInstance = require('../../repositories/customerRepository')


async function getPaymentDetailBreakUp(care_calendar_id) {
    const { TASK_SERVICE_COST_PAYMENT_DETAILS, TASK_MEDICINE_COST_PAYMENT_DETAILS } = classifiers

    return await dbConnections().main.manager.transaction(async (transactionEntityManager) => {
        const taskDetails = await taskRepositoryInstance.getTaskDetails(transactionEntityManager, care_calendar_id)
        const customer_id = await taskRepositoryInstance.getCustomerId(transactionEntityManager, taskDetails[care_calendar_id])
        const customerDetails = await customerRepositoryInstance.getCustomerDetails(transactionEntityManager,  customer_id)
    
        const currentCreditLimitClassificationRow = customerDetails[customer_id].classification.find((item) => item['classifier_id'] === classifiers.CUSTOMER_CREDIT_LIMIT)
        const currentCreditUsageClassificationRow = customerDetails[customer_id].classification.find((item) => item['classifier_id'] === classifiers.CUSTOMER_CREDIT_USAGE)
        
        let currentCreditLimit = (currentCreditLimitClassificationRow && currentCreditLimitClassificationRow.value_double >= 0 ) ? parseFloat(currentCreditLimitClassificationRow.value_double): -1
        let currentCreditUsage = (currentCreditUsageClassificationRow && currentCreditUsageClassificationRow.value_double >= 0) ? parseFloat(currentCreditUsageClassificationRow.value_double) : -1

        const payment_detail_break_up = {
            medicine_cost_payer: [],
            service_cost_payer: [],
            current_credit_limit: currentCreditLimit,
            current_credit_usage: currentCreditUsage
        }
      
        let paymentDetail = await dbConnections().main.manager.getRepository("care_calendar_classification").find({
          where: {
            care_calendar_id,
            classifier_id: In([TASK_SERVICE_COST_PAYMENT_DETAILS,TASK_MEDICINE_COST_PAYMENT_DETAILS])
          }
        })
      
        paymentDetail = postProcessRecords(undefined, paymentDetail, {
          json_columns: ['value_json', 'value_l10n'],
        })
      
        for (const detail of paymentDetail) {
          if (detail.classifier_id === TASK_SERVICE_COST_PAYMENT_DETAILS)
            payment_detail_break_up.service_cost_payer =  detail.value_json.value
          if (detail.classifier_id === TASK_MEDICINE_COST_PAYMENT_DETAILS)
            payment_detail_break_up.medicine_cost_payer =  detail.value_json.value
        }
      
        return payment_detail_break_up
    })

}


async function getTaskDetails(care_calendar_id) {

    const queryResponse = await dbConnections().main.manager
        .createQueryBuilder()
        .from(`(${task_details})`)
        .setParameters({ care_calendar_id })
        .execute()
    if (queryResponse.length) {
        postProcessRecords(undefined, queryResponse, { json_columns: [ 'activity_name'] })
        return queryResponse[0]
    } else {
        throw new Error("The Given care_calendar_id does not exist.")
    }

}


async function fetchAnimalsOnFarmerCareCalendarId(care_calendar_id) {
    const animals = await dbConnections().main.manager
        .createQueryBuilder()
        .from(`(${animals_on_farmer_care_calendar})`)
        .setParameters({care_calendar_id})
        .execute()
    
    return animals
}
async function fetchAnimalsOnAnimalCareCalendarId(care_calendar_id) {
    const animals = await dbConnections().main.manager
        .createQueryBuilder()
        .from(`(${animals_on_animal_care_calendar})`)
        .setParameters({care_calendar_id})
        .execute()
    return animals
}

async function fetchAnimalClassification(animal_ids){
    const animals = await dbConnections().main.manager
        .createQueryBuilder()
        .from(`(${animal_classification})`)
        .setParameters({animal_ids})
        .execute()
    return animals
}

async function getCustomerInfoOnAnimalCareCalendarId(care_calendar_id) {
    const customer_info = await dbConnections().main.manager
        .createQueryBuilder()
        .from(`(${customer_info_on_animal_care_calendar})`)
        .setParameters({care_calendar_id})
        .execute()
    
    postProcessRecords(undefined, customer_info, { json_columns: ['customer_name_l10n','village_name_l10n', 'taluk_name_l10n', 'district_name_l10n'] })
    
    return customer_info[0]
}

async function getCustomerInfoOnCustomerCareCalendarId(care_calendar_id) {
    const customer_info = await dbConnections().main.manager
    .createQueryBuilder()
    .from(`(${customer_info_on_customer_care_calendar})`)
    .setParameters({care_calendar_id})
    .execute()
    postProcessRecords(undefined, customer_info, { json_columns: ['customer_name_l10n','village_name_l10n', 'taluk_name_l10n', 'district_name_l10n'] })
    return customer_info[0]
}

async function getStaffInfoOnCustomer(customer_id) {
    const staff_info = await dbConnections().main.manager
    .createQueryBuilder()
    .from(`(${get_staffs_on_customer})`)
    .setParameters({customer_id})
        .execute()
    
    postProcessRecords(undefined, staff_info, { json_columns: ['paravet_name','vet_name'] })
    return staff_info[0]
}

function getServiceCost(activity_id, cattle_count, activity_cost_map) {
    const service_cost = activity_cost_map[activity_id] ? activity_cost_map[activity_id].cost : 0
    const multiplier = activity_cost_map[activity_id] ? activity_cost_map[activity_id].multiplier : 0
    const serviceCostMultiPlierCount = Math.max(cattle_count, 1) - 1

    const total_service_cost = service_cost + (multiplier * service_cost * serviceCostMultiPlierCount)
    return total_service_cost
}

function getMedicineCost(activity_id, cattle_count,activity_cost_map) {
    const medicine_cost = activity_cost_map[activity_id]? activity_cost_map[activity_id].medicine_cost : 0
    const total_medicine_cost = medicine_cost * cattle_count
    return total_medicine_cost
}


async function getServiceDocumentData(care_calendar_id) {
    
    const activity_cost_map = await getActivityCostMap()
    
    const rx_data = {
        animals: [],
        customer: {},
        staff: {},
        task: {},
    }

    rx_data.task = await getTaskDetails(care_calendar_id)
    
    const { entity_type_id, entity_uuid } = rx_data.task 
   
    switch (entity_type_id) {
        case 1000460001: {
            const animal_ids = await fetchAnimalsOnFarmerCareCalendarId(care_calendar_id).then(animals => animals.map(animal => animal.animal_id))
            if (animal_ids.length) {
                rx_data.animals = await fetchAnimalClassification(animal_ids)
            }
            rx_data.customer = await getCustomerInfoOnCustomerCareCalendarId(care_calendar_id)
            break
        }
       case 1000460002: {
            const animal_ids = await fetchAnimalsOnAnimalCareCalendarId(care_calendar_id).then(animals => animals.map(animal => animal.animal_id))
            if (animal_ids.length) {
                rx_data.animals = await fetchAnimalClassification(animal_ids)
            }
            rx_data.customer = await getCustomerInfoOnAnimalCareCalendarId(care_calendar_id)
            break
        }
        default:
            throw new Error("Invalid entity!")
    }

    const {customer_id} = rx_data.customer
    rx_data.staff = await getStaffInfoOnCustomer(customer_id)
    
    const payment_detail_break_up = await getPaymentDetailBreakUp(care_calendar_id)
    rx_data["payment_detail_break_up"] = payment_detail_break_up
    
    return rx_data
}

async function getActivityCostMap() {
    const res = await dbConnections().main.manager
        .createQueryBuilder()
        .select("reference_information")
        .from('ref_reference', 'rf')
        .where('rf.reference_id = :reference_id')
        .setParameters({reference_id:activity_configuration.activity_cost_map})
        .execute()
    
    postProcessRecords(undefined, res, { json_columns: ['reference_information'] })        
    
    const activity_cost_map = res[0].reference_information
    
    return activity_cost_map
}


module.exports = {
    getActivityCostMap,
    getServiceDocumentData,
    fetchAnimalsOnFarmerCareCalendarId,
    fetchAnimalsOnAnimalCareCalendarId,
    fetchAnimalClassification,
    getCustomerInfoOnAnimalCareCalendarId,
    getServiceCost,
    getMedicineCost
}