{
    "workbench.colorCustomizations": {
        "activityBar.background": "#452045",
        "titleBar.activeBackground": "#602C60",
        "titleBar.activeForeground": "#FCF9FC"
    },
    "files.exclude": {
        "*/**/.github/": true,
        "*/**/cache/": true,
        "*/**/infinitewp/": true,
        "*/**/node_modules/": true,
        "*/**/uploads/": true,
        "wp-admin/": true,
        "wp-config.*": false,
        "wp-includes/": true,
        "xmlrpc.php": true
    },
    "search.exclude": {
        "build/": true,
    },
    "prettier.eslintIntegration": true,
    "editor.formatOnSave": true,
    "terminal.integrated.useWslProfiles": false,
    "java.compile.nullAnalysis.mode": "automatic",
    "gitflow4code.features": [
        {
            "name": "feature/20231129_RS_f_r_3_1_0_desc_fixing_repeat_location_requests",
            "base": "release_3.1.0"
        }
    ],
    "java.project.explorer.showNonJavaResources": false,
    "cSpell.words": [
        "RNFS"
    ],
}