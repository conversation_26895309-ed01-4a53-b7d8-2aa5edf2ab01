{
  "gitflow4code.releases": [
    {
      "name": "release/rel_offline_poc",
      "base": "develop"
    },
    {
      "name": "release/rel_farmer_onboarding_v1",
      "base": "develop"
    },
    {
      "name": "release/requirement_15",
      "base": "develop"
    },
    {
      "name": "release/requirement_16",
      "base": "develop"
    },
    {
      "name": "release/as-of-date-implementation",
      "base": "develop"
    },
    {
      "name": "release/classification_helper_fix",
      "base": "develop"
    },
    {
      "name": "release/requirement_21",
      "base": "develop"
    }
  ],
  "workbench.colorCustomizations": {
    "activityBar.background": "#5B0936",
    "titleBar.activeBackground": "#7F0C4B",
    "titleBar.activeForeground": "#FFFCFD"
  },
}
