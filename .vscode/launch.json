{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: Remote Attach",
      "type": "python",
      "request": "attach",
      "connect": {
        "port": 44101,
        "host": "0.0.0.0"
      },
      "pathMappings": [
        {
          "localRoot": "${workspaceFolder}",
          "remoteRoot": "/"
        }
      ]
    },
    {
      "name": "Python: dev2 Attach",
      "type": "python",
      "request": "attach",
      "connect": {
        "port": 44101,
        "host": "dev2.krushal.in"
      },
      "pathMappings": [
        {
          "localRoot": "${workspaceFolder}",
          "remoteRoot": "/"
        }
      ]
    }
  ]
}