{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        // {
        //     "type": "node",
        //     "request": "launch",
        //     "name": "Launch Program",
        //     "skipFiles": [
        //         "<node_internals>/**"
        //     ],
        //     "program": "${workspaceFolder}/src/Components/Dashboard/BgLocation/MapComponents.js"
        // },
        {
            "name": "Launch Chrome",
            "request": "launch",
            "type": "chrome",
            "url": "http://localhost:3003",
            "webRoot": "${workspaceFolder}"
        },
        {
            "name": "Attach to Chrome",
            "port": 9222,
            "request": "attach",
            "type": "chrome",
            "webRoot": "${workspaceFolder}"
        }
    ]
}