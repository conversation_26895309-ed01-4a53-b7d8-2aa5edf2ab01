const {infoLog} = require('@krushal-it/mobile-or-server-lib')

const postUpsertProcessing = (databaseName, tableName, data)  => {
  const {insertedPrimaryKeys, updatedPrimaryKeys, unknownOperatonPrimaryKeys} = data
  infoLog('databaseName = ', databaseName, ', tableName = ', tableName, ', insertedPrimaryKeys = ', insertedPrimaryKeys, ', updatedPrimaryKeys = ', updatedPrimaryKeys, ', unknownOperatonPrimaryKeys = ', unknownOperatonPrimaryKeys)
}

const assignSourceObjectToTargetObjectIfKeyNotUndefined = (sourceObject, targetObject, keysToAssign) => {
  const objectKeys = keysToAssign ? keysToAssign : Object.keys(sourceObject)
  for (const objectKey of objectKeys) {
    if (sourceObject[objectKey] !== undefined) {
      targetObject[objectKey] = sourceObject[objectKey]
    }
  }
}

module.exports = { postUpsertProcessing, assignSourceObjectToTargetObjectIfKeyNotUndefined }