const { Column } = require("typeorm");
// const { v1: uuidv1 } = require("uuid");
const { v1 } = require("uuid");
const uuid = require("uuid");

const { configurationJSON } = require('@krushal-it/common-core')

const defaultValueForUUIDPrimaryKey = () => {
  if (process.env.IS_SERVER === "True" && process.env.DB_TYPE === "POSTGRES") {
    return "common.uuid_generate_v1mc()";
  } else {
    return "(lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))";
  }
};

const defaultValueForDataSyncStatus = () => {
  if (process.env.IS_SERVER === "True" && process.env.DB_TYPE === "POSTGRES") {
    return 1000101001;
  } else {
    return 1000101003;
  }
};

const defaultValueForDocumentSyncStatus = () => {
  if (process.env.IS_SERVER === "True" && process.env.DB_TYPE === "POSTGRES") {
    return 1000102001;
  } else {
    return 1000102002;
  }
};

const defaultValueForCreatedAt = () => {
  if (process.env.IS_SERVER === "True" && process.env.DB_TYPE === "POSTGRES") {
    return "NOW()";
  } else {
    return "CURRENT_TIMESTAMP";
  }
};

const defaultEmptyJSONValue = () => {
  if (process.env.IS_SERVER === "True" && process.env.DB_TYPE === "POSTGRES") {
    return "'{}'::jsonb";
  } else {
    return "{}";
  }
};

const jsonColumn = () => {
  if (process.env.IS_SERVER === "True" && process.env.DB_TYPE === "POSTGRES") {
    return "jsonb";
  } else {
    return "text";
  }
};

const timestampColumn = () => {
  if (process.env.IS_SERVER === "True" && process.env.DB_TYPE === "POSTGRES") {
    return "timestamp";
  } else {
    return "datetime";
  }
};

const defaultTimestampColumn = (defaultTimestamp) => {
  if (process.env.IS_SERVER === "True" && process.env.DB_TYPE === "POSTGRES") {
    return "TO_TIMESTAMP('" + defaultTimestamp + "', 'YYYY-MM-DD HH24:MI:SS')";
  } else {
    return "'" + defaultTimestamp + "'";
  }
};

/*
import in node
    "uuid": "9.0.0"
import in react native
    "uuid": "npm:react-native-uuid@2.0.1"
*/
const reactNativeOrMobileDependentUUID = () => {
  if (!v1) {
    return uuid.default.v1();
  } else {
    v1();
  }
};

const optionallyAddPrimaryKeyToRecords = (entity, insertRecords) => {
  const primaryKey = entity.primaryColumns[0].propertyName;
  if (process.env.IS_SERVER === "True" && process.env.DB_TYPE === "POSTGRES") {
  } else {
    if (Array.isArray(insertRecords)) {
      for (insertRecord of insertRecords) {
        insertRecord[primaryKey] = reactNativeOrMobileDependentUUID();
      }
    } else {
      insertRecords[primaryKey] = reactNativeOrMobileDependentUUID();
    }
  }
};

const removeAttributesNotBelongingToTableFromEntity = (entityColumns, entityRecord) => {
  let strictCheckOnColumns = true
  if (configurationJSON().DB && configurationJSON().DB.ORM_STRICT_COLUMN_CHECK && configurationJSON().DB.ORM_STRICT_COLUMN_CHECK === 1) {
    strictCheckOnColumns = false
  }
  const keysInEntityRecord = Object.keys(entityRecord)
  const keysInEntityRecordNotAColumn = keysInEntityRecord.filter(key => !entityColumns.includes(key))
  if (keysInEntityRecordNotAColumn.length > 0 && strictCheckOnColumns) {
    // console.log('aho u oH rANBTTFE 1, keysInEntityRecordNotAColumn = ', keysInEntityRecordNotAColumn)
    throw new Error('Additional Columns in entity')
  }
  for (const keyInEntityRecordNotAColumn of keysInEntityRecordNotAColumn) {
    delete entityRecord[keyInEntityRecordNotAColumn]
  }
}
const removeAttributesNotBelongingToTableFromUpsertRecordOrRecords = (entityColumns, upsertRecords) => {
  if (Array.isArray(upsertRecords)) {
    for (const upsertRecord of upsertRecords) {
      removeAttributesNotBelongingToTableFromEntity(entityColumns, upsertRecord)
    }
  } else {
    removeAttributesNotBelongingToTableFromEntity(entityColumns, upsertRecords)
  }
}

const preProcessIndividualRecord = (entity, additionalConfiguration, entityColumns, primaryKey, dataSyncStatusValue, documentSyncStatusValue, numberOfRecords, id, upsertRecord) => {
  // console.log('aho u oH pPIR 1, entityColunms = ', entityColumns)
  removeAttributesNotBelongingToTableFromEntity(entityColumns, upsertRecord)
  if (id && numberOfRecords === 1) {
    upsertRecord[primaryKey] = id  
  } else if (id && numberOfRecords > 1) {
    throw new Error('Cannot assign same id to more than 1 record')
  } else if (!upsertRecord[primaryKey] && !id) {
    if (process.env.IS_SERVER === "True" && process.env.DB_TYPE === "POSTGRES") {
    } else {
      upsertRecord[primaryKey] = reactNativeOrMobileDependentUUID()
  }
  }
  if (process.env.IS_SERVER === "True" && process.env.DB_TYPE === "POSTGRES") {
    if (additionalConfiguration && additionalConfiguration.setClientDelta && additionalConfiguration.setClientDelta === 1) {
      if (entity.additionalAttributes && entity.additionalAttributes.json_columns) {
        //destringify
        for (const jsonColumn of entity.additionalAttributes.json_columns) {
          if (upsertRecord[jsonColumn]) {
            const jsonColumnInObject = upsertRecord[jsonColumn]
            const parsedJSONObject = JSON.parse(jsonColumnInObject)
            upsertRecord[jsonColumn] = parsedJSONObject
          }
        }
      }
    }
  } else {
    upsertRecord['data_sync_status'] = dataSyncStatusValue
    if (entity.schema === "main" && entity.tableName === "document" && documentSyncStatusValue) {
      upsertRecord['document_sync_status'] = documentSyncStatusValue
    }
    
    // convert json objects to json only if it is not in the context of sync
    if (additionalConfiguration && additionalConfiguration.syncServerDeltaOnMobile) {
      // nothing to do as of now
    } else {
      if (entity.additionalAttributes && entity.additionalAttributes.json_columns) {
        for (const jsonColumn of entity.additionalAttributes.json_columns) {
          if (upsertRecord[jsonColumn]) {
            const jsonColumnInObject = upsertRecord[jsonColumn]
            const stringifiedJSONObject = JSON.stringify(jsonColumnInObject)
            upsertRecord[jsonColumn] = stringifiedJSONObject
          }
        }
      }
    }
  }
}

const preProcessRecordsV1 = (entity, upsertRecords, additionalConfiguration) => {
  const primaryKey = entity.primaryColumns[0].propertyName;
  let id
  if (additionalConfiguration && additionalConfiguration.id) {
    id = additionalConfiguration.id
  }
  let dataSyncStatusValue = 1000101003
  let documentSyncStatusValue = undefined
  if (additionalConfiguration && additionalConfiguration.syncServerDeltaOnMobile) {
    dataSyncStatusValue = 1000101001
    if (additionalConfiguration && additionalConfiguration.syncOperation && additionalConfiguration.syncOperation === "insert") {
      documentSyncStatusValue = 1000102003
    }
  }
  // console.log('entity.columns = ', entity.columns)
  const entityColumns = entity.columns.map(columnInformation => columnInformation.databaseName)
  let numberOfRecords = 1
  if (Array.isArray(upsertRecords)) {
    numberOfRecords = upsertRecords.length
    for (const upsertRecord of upsertRecords) {
      preProcessIndividualRecord(entity, additionalConfiguration, entityColumns, primaryKey, dataSyncStatusValue, documentSyncStatusValue, numberOfRecords, id, upsertRecord)
    }
  } else {
    preProcessIndividualRecord(entity, additionalConfiguration, entityColumns, primaryKey, dataSyncStatusValue, documentSyncStatusValue, numberOfRecords, id, upsertRecords)
  }
}

const preProcessRecords = (entity, upsertRecords, additionalConfiguration) => {
  const primaryKey = entity.primaryColumns[0].propertyName;
  let id
  if (additionalConfiguration && additionalConfiguration.id) {
    id = additionalConfiguration.id
  }
  let dataSyncStatusValue = 1000101003
  let documentSyncStatusValue = undefined
  if (additionalConfiguration && additionalConfiguration.syncServerDeltaOnMobile) {
    dataSyncStatusValue = 1000101001
    if (additionalConfiguration && additionalConfiguration.syncOperation && additionalConfiguration.syncOperation === "insert") {
      documentSyncStatusValue = 1000102003
    }
  }
  // console.log('entity.columns = ', entity.columns)
  const entityColumns = entity.columns.map(columnInformation => columnInformation.databaseName)
  let numberOfRecords = 1
  let returnValue
  // console.log('aho u oH prePR 1, entityColunms = ', entityColumns)
  if (Array.isArray(upsertRecords)) {
    returnValue = []
    numberOfRecords = upsertRecords.length
    for (const upsertRecord of upsertRecords) {
      const deepCopiedRecord = {...upsertRecord}
      returnValue.push(deepCopiedRecord)
      preProcessIndividualRecord(entity, additionalConfiguration, entityColumns, primaryKey, dataSyncStatusValue, documentSyncStatusValue, numberOfRecords, id, deepCopiedRecord)
    }
  } else {
    const deepCopiedRecord = {...upsertRecords}
    returnValue = deepCopiedRecord
    preProcessIndividualRecord(entity, additionalConfiguration, entityColumns, primaryKey, dataSyncStatusValue, documentSyncStatusValue, numberOfRecords, id, deepCopiedRecord)
  }
  return returnValue
}

const postProcessIndividualRecord = (entity, primaryKey, configuration, upsertedRecord) => {
  if (configuration.getServerDelta && configuration.getServerDelta === 1) {
    delete upsertedRecord['data_sync_status']
    delete upsertedRecord['created_at']
    delete upsertedRecord['updated_at']
    if (entity.schema === 'main' && entity.tableName === 'document') {
      delete upsertedRecord['client_document_information']
      delete upsertedRecord['document_sync_status']
    }
  }
  if (configuration && configuration.getServerDelta && configuration.getServerDelta === 1) {
    if (entity && entity.additionalAttributes && entity.additionalAttributes.json_columns) {
      for (const jsonColumn of entity.additionalAttributes.json_columns) {
        const jsonColumnOnServer = upsertedRecord[jsonColumn]
        const stringifiedJSONColumn = JSON.stringify(jsonColumnOnServer)
        upsertedRecord[jsonColumn] = stringifiedJSONColumn
      }
    }
  }
  if (process.env.IS_SERVER === "True" && process.env.DB_TYPE === "POSTGRES") {
    if (configuration && configuration.double_columns) {
      for (const doubleColumn of configuration.double_columns) {
        const doubleColumnOnClientAsString = upsertedRecord[doubleColumn]
        const doubleColumnValue = (doubleColumnOnClientAsString && doubleColumnOnClientAsString !== null && doubleColumnOnClientAsString !== '') ? parseFloat(doubleColumnOnClientAsString) : undefined
        upsertedRecord[doubleColumn] = doubleColumnValue
      }
    }
  } else {
    if (configuration && configuration.json_columns) {
      for (const jsonColumn of configuration.json_columns) {
        const jsonColumnOnClientAsString = upsertedRecord[jsonColumn]
        const jsonifiedColumn = (jsonColumnOnClientAsString && jsonColumnOnClientAsString !== null && jsonColumnOnClientAsString !== '') ? JSON.parse(jsonColumnOnClientAsString): {}
        upsertedRecord[jsonColumn] = jsonifiedColumn
      }
    }
  }

  return upsertedRecord
}
const postProcessRecords = (entity, upsertedRecords, configuration) => {
  const primaryKey = entity ? entity.primaryColumns[0].propertyName : undefined;
  if(upsertedRecords === null || upsertedRecords === undefined ){
    return null
  }
  if (Array.isArray(upsertedRecords)) {
    const processedRecords = []
    for (const upsertedRecord of upsertedRecords) {
      const processedRecord = postProcessIndividualRecord(entity, primaryKey, configuration, upsertedRecord)
      processedRecords.push(processedRecord)
    }
    return processedRecords
  } else {
    const processedRecord = postProcessIndividualRecord(entity, primaryKey, configuration, upsertedRecords)
    return processedRecord
  }
}

const postProcessSelectQueryResults = (postProcessConfiguration, resultArray) => {
}

module.exports = {
  defaultValueForUUIDPrimaryKey,
  defaultValueForDataSyncStatus,
  defaultValueForDocumentSyncStatus,
  defaultValueForCreatedAt,
  jsonColumn,
  timestampColumn,
  defaultTimestampColumn,
  defaultEmptyJSONValue,
  optionallyAddPrimaryKeyToRecords,
  preProcessRecordsV1,
  preProcessRecords,
  postProcessRecords,
  postProcessSelectQueryResults
};
