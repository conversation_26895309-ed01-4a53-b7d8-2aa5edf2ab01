const { configurationJSON } = require("@krushal-it/common-core")
const { nanoid } = require('nanoid');
const{ verifyEntity} = require('./filters/index')

function getUniqueObjectKeyWithExt(extension) {
	return `${getNanoId()}.${extension}`
}

function getNanoId() {
    return nanoid(16);
}

function getObjectInfo(entity,extension) {
    const root_location = configurationJSON().OBJECT_STORAGE.ROOT_LOCATION
    entity = verifyEntity(entity)
    
    const location = `${root_location}/${entity}`
    const fileName = getUniqueObjectKeyWithExt(extension)
    

    return {location, fileName}
}
module.exports = {getUniqueObjectKeyWithExt, getNanoId, getObjectInfo }