const getDBConnections = () => {
  return global.dbConnections
}

const additionalAttributes = {
  main: {
    document: {
      json_columns: ['document_name_l10n', 'document_information', 'client_document_information', 'document_meta_data_information'],
    },
    animal: {
      json_columns: ['animal_name_l10n'],
    },
    animal_classification: {
      json_columns: ['value_json', 'value_l10n'],
    },
    animal_classification_history: {
      json_columns: ['value_json', 'value_l10n']
    },
    customer: {
      json_columns: ['customer_name_l10n'],
    },
    customer_classification: {
      json_columns: ['value_json', 'value_l10n'],
    },
  user_event: {
      json_columns: ['additional_data']
    },
  
    staff: {
      json_columns: ['staff_name_l10n'],
    },
    staff_classification: {
      json_columns: ['value_json', 'value_l10n'],
    },
    entity_relationship: {
      json_columns: ['entity_information'],
    },
    ref_state: {
      json_columns: ['state_name_l10n'],
    },
    ref_district: {
      json_columns: ['district_name_l10n'],
    },
    ref_taluk: {
      json_columns: ['taluk_name_l10n'],
    },
    ref_village: {
      json_columns: ['village_name_l10n'],
    },
    ref_reference_category: {
      json_columns: ['reference_category_name_l10n'],
    },
    ref_reference: {
      json_columns: ['reference_name_l10n', 'reference_information'],
    },
    animal_disease_classification: {
      json_columns: ['value_json', 'value_l10n'],
    },
    care_calendar_classification: {
      json_columns: ['value_json', 'value_l10n'],
    },
    dump_doc: {
      json_columns: ['error'],
    },
    ref_activity: {
      json_columns: ['activity_name_l10n','activity_information'],
    },
    note: {
      json_columns: ['note'],
    },
    ref_diagnosis: {
      json_columns: ['reference_name_l10n', 'reference_information'],
    },
    ref_medicine: {
      json_columns: ['medicine_name_l10n']
    },
    ref_medicine_classification: {
      json_columns: ['value_json','value_l10n']
    },
    entity_status : {
      json_columns: ['status_information']
    },
    requisition_classification : {
      json_columns: ['value_json','value_l10n']
     },
     partner : {
      json_columns: ['partner_name_l10n']
     },
     partner_classification: {
      json_columns: ['value_json','value_l10n']
     },
     task_question: {
      json_columns: ['question_name_l10n','topic_name_l10n','form_configuration']
     },
     task_question_data: {
      json_columns: ['form_configuration']
     }
  },
}

const syncFilters = {
  main: {
    staff: {
      columnFilters: [
        {
          filter: 'staff_id_array',
          column: 'staff_id',
        },
      ],
    },
    staff_classification: {
      columnFilters: [
        {
          filter: 'staff_id_array',
          column: 'staff_id',
        },
      ],
    },
    animal_classification: {
      columnFilters: [
        {
          filter: 'animal_id_array',
          column: 'animal_id',
        },
      ],
    },
    animal: {
      columnFilters: [
        {
          filter: 'animal_id_array',
          column: 'animal_id',
        },
      ],
    },
    customer_classification: {
      columnFilters: [
        {
          filter: 'customer_id_array',
          column: 'customer_id',
        },
      ],
    },
    customer: {
      columnFilters: [
        {
          filter: 'customer_id_array',
          column: 'customer_id',
        },
      ],
    },
    ref_village: {
      columnFilters: [
        {
          filter: 'village_id_array',
          column: 'village_id',
        },
      ],
    },
    ref_taluk: {
      customQuery: {
        query:
          "select * from main.ref_taluk where taluk_id in ( \
                  select taluk_id from main.ref_sdtv_view where village_id in (:...village_id_array_as_comma_separated_concatenated_strings) \
                ) and updated_at > ':last_sync_time' and updated_at < ':sync_reference_time'",
      },
    },
    ref_district: {
      customQuery: {
        query:
          "select * from main.ref_district where district_id in ( \
                  select district_id from main.ref_sdtv_view where village_id in (:...village_id_array_as_comma_separated_concatenated_strings) \
                ) and updated_at > ':last_sync_time' and updated_at < ':sync_reference_time'",
      },
    },
    ref_state: {
      customQuery: {
        query:
          "select * from main.ref_state where state_id in ( \
                  select state_id from main.ref_sdtv_view where village_id in (:...village_id_array_as_comma_separated_concatenated_strings) \
                ) and updated_at > ':last_sync_time' and updated_at < ':sync_reference_time'",
      },
    },
    document: {
      customQuery: {
        query:
          " \
                  select * from main.document \
                  where updated_at > ':last_sync_time' and updated_at < ':sync_reference_time' \
                  and ( \
                    ( \
                      entity_1_type_id in (1000460002, 1000220002) \
                      and entity_1_entity_uuid in (:...animal_id_array_as_comma_separated_concatenated_strings) \
                    ) \
                    or \
                    ( \
                      entity_1_type_id in (1000460001, 1000220001) \
                      and entity_1_entity_uuid in (:...customer_id_array_as_comma_separated_concatenated_strings) \
                    ) \
                    or \
                    ( \
                      entity_1_type_id in (1000460003, 1000230005, 1000230004, 1000230003, 1000230002, 1000230001) \
                      and entity_1_entity_uuid in (:...staff_id_array_as_comma_separated_concatenated_strings) \
                    ) \
                    or \
                    ( \
                      entity_1_type_id in (1000460004, 1000220003 , 1000260027, 1000260028) \
                      and entity_1_entity_uuid in (:...task_id_array_for_documents_as_comma_separated_concatenated_strings) \
                    ) \
                    or \
                    ( \
                      entity_1_type_id in (1000460005, 1000220004) \
                      and entity_1_entity_uuid in (:...animal_classification_id_array_as_comma_separated_concatenated_strings) \
                    ) \
                    or \
                    ( \
                      document_type_id in (1000260014) \
                    ) \
                  ) \
                ",
      },
    },
    entity_geography1: {
      columnFilters: [
        { 
          filter: 'geography_type_id_village',
          column: 'geography_type_id',
        },
        {
          filter: 'entity_geography_id_array',
          column: 'entity_uuid',
        },
      ],
    },
    note: {
      columnFilters: [
        {
          filter: 'care_calendar_id_array',
          column: 'entity_1_uuid',
        },
      ],
    },
    animal_disease_classification: {
      columnFilters: [
        {
          filter: 'animal_disease_id_array',
          column: 'animal_disease_id',
        },
      ],
    },
    animal_disease: {
      columnFilters: [
        {
          filter: 'animal_id_array',
          column: 'animal_id',
        },
      ],
    },
    care_calendar: {
      columnFilters: [
        {
          filter: 'care_calendar_id_array',
          column: 'care_calendar_id',
        },
      ],
    },
    care_calendar_classification: {
      columnFilters: [
        {
          filter: 'care_calendar_id_array',
          column: 'care_calendar_id',
        },
      ],
    },
    staff_activity: {
      columnFilters: [
        {
          filter: 'care_calendar_id_array',
          column: 'care_calendar',
        },
      ],
    },
    entity_relationship: {
      customQuery: {
        query:
          " \
                  select * \
                  from main.entity_relationship \
                  where updated_at > ':last_sync_time' and updated_at < ':sync_reference_time' \
                  and ( \
                  ( \
                    entity_relationship_type_id = 1000210004 \
                    and entity_1_entity_uuid in (:...customer_id_array_as_comma_separated_concatenated_strings) \
                  ) \
                  ) \
                ",
      },
    },
    warehouse_blockqty: {
      columnFilters: [
        {
          filter: 'staff_id_array',
          column: 'warehouse_uuid',
        }
      ]
    },
    inventory_ledger: {
      columnFilters: [
        {
          filter: 'staff_id_array',
          column: 'warehouse_uuid',
        }
      ]
    },
    requisition: {
      columnFilters: [
        {
          filter: 'requisition_id_array',
          column: 'requisition_id',
        }
      ]
    },
    requisition_line_items: {
      columnFilters: [
        {
          filter: 'requisition_id_array',
          column: 'requisition_id',
        }
      ]
    },
    requisition_classification: {
      columnFilters: [
        {
          filter: 'requisition_id_array',
          column: 'requisition_id',
        }
      ]
    },
    entity_status: {
      columnFilters: [
        {
          filter: 'requisition_id_array',
          column: 'entity_1_entity_uuid',
        }
      ]
    },
    partner: {
      columnFilters: [
        {
          filter: 'partner_id_array',
          column: 'partner_id',
        }
      ]
    },
    partner_classification: {
      columnFilters: [
        {
          filter: 'partner_id_array',
          column: 'partner_id',
        }
      ]
    },
    task_question_data: {
      columnFilters: [
        {
          filter: 'care_calendar_id_array',
          column: 'care_calendar_id',
        }
      ]
    }

  },
}

const syncConfiguration = {
  main: {
    one_way_sync_tables: ['ref_reference_category', 'ref_reference', 'ref_activity', 'ref_state', 'ref_district', 'ref_taluk', 'ref_village', 'staff', 'staff_classification', 'entity_geography', 'ref_diagnosis','ref_medicine','ref_medicine_classification','task_question'],
    two_way_sync_tables: ['customer', 'customer_classification', 'animal', 'animal_classification', 'document', 'entity_relationship', 'care_calendar', 'care_calendar_classification', 'animal_disease', 'animal_disease_classification', 'note', 'staff_activity','requisition','requisition_line_items','requisition_classification','warehouse_blockqty','inventory_ledger','entity_status','partner','partner_classification','task_question_data'],
    client_to_service_only_sync_tables: [],//["user_event"],
    tables_not_to_deleted_on_user_change: ['ref_reference_category', 'ref_reference', 'ref_activity', 'ref_diagnosis'],
    sync_times_to_be_sent_on_initial_upload: ['ref_reference_category', 'ref_reference', 'ref_activity', 'ref_diagnosis'],
    insert_batch_size: { ref_reference: 20, customer: 20, customer_classification: 20, animal: 20, animal_classification: 20, animal_disease: 20, animal_disease_classification: 20, entity_relationship: 20, entity_geography: 20, document: 20, staff_activity: 20, care_calendar: 20, care_calendar_classification: 20, note: 20 ,ref_medicine :20 ,ref_medicine_classification: 20 ,entity_status: 20 , requisition: 20,requisition_line_items: 20, requisition_classification : 20, warehouse_blockqty: 20 ,inventory_ledger: 20,partner: 20 ,partner_classification: 20 ,ref_activity: 20,task_question_data:20,task_question:20},
  },
  config: {},
}

const initializeRepositories = async (dbConnections, dbName) => {
  try {
    let dbConnection
    if (!dbConnections[dbName].default.isInitialized) {
      dbConnection = await dbConnections[dbName].default.initialize()
      dbConnections[dbName].manager = dbConnection.manager
      dbConnections[dbName].repos = {}
      dbConnections[dbName].tablePaths = {}
      dbConnections[dbName].entities = {}
      // dbConnections[dbName].additionalAttributes = {}
      dbConnections[dbName].syncConfiguration = syncConfiguration[dbName]
      // dbConnections[dbName].syncFilters = syncFilters[dbName]
      for (const metaData of dbConnection.entityMetadatas) {
        const repo = dbConnection.getRepository(metaData)
        dbConnections[dbName].tablePaths[metaData.tableName] = metaData.tablePath
        dbConnections[dbName].repos[metaData.tableName] = repo
        dbConnections[dbName].entities[metaData.tableName] = metaData
        if (additionalAttributes[dbName] && additionalAttributes[dbName][metaData.tableName]) {
          // dbConnections[dbName].additionalAttributes[metaData.tableName] = additionalAttributes[dbName][metaData.tableName]
          metaData.additionalAttributes = additionalAttributes[dbName][metaData.tableName]
        }
        if (syncFilters[dbName] && syncFilters[dbName][metaData.tableName]) {
          // dbConnections[dbName].additionalAttributes[metaData.tableName] = additionalAttributes[dbName][metaData.tableName]
          metaData.syncAttributes = syncFilters[dbName][metaData.tableName]
        }
      }
    }
  } catch (error) {
    console.log('error = ', error)
    throw error
  }
}

const initializeAllRepositories = async (dbConnections) => {
  for (const dbConnectionName in dbConnections) {
    const dbConnection = dbConnections[dbConnectionName]
    await initializeRepositories(dbConnections, dbConnection.default.name)
  }
  global.dbConnections = dbConnections
}

const dbConnections = () => {
  let dbConnections
  if (process.env.IS_SERVER === 'True') {
    dbConnections = require('../db/datasources/server')
    // dbConnections.main.default.initialize()
  } else {
    dbConnections = require('../db/datasources/mobile')
  }
  return dbConnections
}

module.exports = { initializeAllRepositories, initializeRepositories, dbConnections, getDBConnections }
