const { dbConnections } = require("@krushal-it/ah-orm");
const { getMaxVisualInTaluka } = require("../queries/customer");
const { getLocationEntityById } = require("../queries/location");
const {infoLog} = require('@krushal-it/mobile-or-server-lib')

const { 
  RELATION_STATE, 
  RELATION_DISTRICT, 
  RELATION_TALUKA, 
  CONST_PADDING_VISUAL_ID_CUSTOMER 
} = require("./constant");

const generateVisualId = async (state_id, district_id, taluka_id, options = {}) => {
  let manager = options.manager ? options.manager : dbConnections().main.manager;
  // TODO : remove it once state is set up
  if (!state_id) state_id = 1026;
  if (!state_id || !district_id || !taluka_id) {
    infoLog("err_state", state_id);
    infoLog("err_dist", district_id);
    infoLog("err_tal", taluka_id);
    return null;
  }
  let state = await getLocationEntityById(RELATION_STATE, [state_id]);
  if (!Array.isArray(state) || state.length === 0 || !state[0].state_short_code) {
    infoLog("err_2_state", state);
    return null;
  }
  state = state[0].state_short_code;

  let district = await getLocationEntityById(RELATION_DISTRICT, [district_id]);
  if (!Array.isArray(district) || district.length === 0 || !district[0].district_short_code) {
    infoLog("err_2_district", district);
    return null;
  }
  district = district[0].district_short_code;

  let taluka = await getLocationEntityById(RELATION_TALUKA, [taluka_id]);
  if (!Array.isArray(taluka) || taluka.length === 0 || !taluka[0].taluk_short_code) {
    infoLog("err_2_taluka", taluka);
    return null;
  }
  taluka = taluka[0].taluk_short_code;
  let [max_id, error] = await getMaxVisualInTaluka(taluka_id, { manager });

  if (error) return null;
  max_id = String(max_id).padStart(CONST_PADDING_VISUAL_ID_CUSTOMER, "0");
  let visual_id = `${state}${district}${taluka}${max_id}`;
  return visual_id;
};

module.exports = {
  generateVisualId 
};
