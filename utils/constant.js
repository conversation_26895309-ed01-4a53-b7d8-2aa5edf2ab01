exports.SCHEMA_MAIN = 'main'
exports.RELATION_ACTIVITY = 'ref_activity'
exports.RELATION_ANIMAL = 'animal'
exports.RELATION_ANIMAL_CLASSIFICATION = 'animal_classification'
exports.RELATION_ANIMAL_DISEASE = 'animal_disease'
exports.RELATION_CALENDAR = 'care_calendar'
exports.RELATION_CUSTOMER = 'customer'
exports.RELATION_CUSTOMER_CLASSIFICATION = 'customer_classification'
exports.RELATION_DOC = 'document'
exports.RELATION_DISEASE_CLASSIFICATION = 'animal_disease_classification'
exports.RELATION_DUMP_DOC = 'dump_document'
exports.RELATION_JOBTRACKER = 'job_tracker'
exports.RELATION_NOTE = 'note'
exports.RELATION_STAFF = 'staff'
exports.RELATION_STAFF_ACTIVITY = 'staff_activity'
exports.RELATION_STATE = 'ref_state'
exports.RELATION_TALUKA = 'ref_taluk'
exports.RELATION_VILLAGE = 'ref_village'
exports.RELATION_DISTRICT = 'ref_district'
exports.RELATION_REFERENCE = 'ref_reference'
exports.RELATION_RELATIONSHIP = 'entity_relationship'
exports.REALATION_ENTITY_GEO = 'entity_geography'
exports.RELATION_CALENDAR_CLASSIFICATION = 'care_calendar_classification'
exports.RELATION_REQUISITION = "requisition";
exports.RELATION_REQUISITION_CLASSIFCATION = 'requisition_classification'
exports.RELATION_WAREHOUSE_BLOCKQTY = "warehouse_blockqty"
exports.RELATION_REQUISITION_LINE_ITEMS = "requisition_line_items"
exports.RELATION_INVENTORY_LEDGER = "inventory_ledger"
exports.RELATION_ENTITY_STATUS="entity_status"

exports.ENTITY_TYPE_ANIMAL = 'animal'
exports.ENTITY_TYPE_CARE_CALENDAR = 'care-calendar'
exports.ENTITY_TYPE_CUSTOMER = 'customer'
exports.ENTITY_TYPE_DISEASE = 'disease'
exports.ENTITY_TYPE_FARMER = 'farmer'
exports.ENTITY_TYPE_TASK = 'task'
exports.ENTITY_TYPE_STAFF_BACK_OFFICE_PERSON = 'back_office_person'
exports.ENTITY_TYPE_STAFF_PARAVET = 'paravet'
exports.ENTITY_TYPE_STAFF_ROUTE_OFFICER = 'route_officer'
exports.ENTITY_TYPE_STAFF_ZONAL_OFFICER = 'zonal_officer'
exports.ENTITY_TYPE_STAFF_VET = 'vet'

exports.JOB_TYPE_CALENDAR = 'care-calendar'
exports.JOB_TYPE_TRANSLATE = 'translate'

exports.JOB_ACTION_START = 'start'
exports.JOB_ACTION_STATUS = 'status'

exports.STATUS_COMPLETE = 'COMPLETED'
exports.STATUS_FAILED = 'FAILED'
exports.STATUS_PARTIAL = 'PARTIAL'
exports.STATUS_PENDING = 'PENDING'

exports.LOCATION_TYPE_DISTRICT = 'district'
exports.LOCATION_TYPE_STATE = 'state'
exports.LOCATION_TYPE_TALUKA = 'taluk'
exports.LOCATION_TYPE_VILLAGE = 'village'

exports.SEQ_CUSTOMER_VISUAL_ID = 'customer_visual_id'
exports.CONST_ID_TYPE_VILLAGE = 1000320004
exports.CONST_STATUS_ACTIVITY_COMPLETED = 1000300050
exports.ACTIVE = **********
exports.INACTIVE = **********
exports.STATUS_CATEGORY = 10001000
exports.PARAVET = **********
exports.PAY_PER_USE_PARAVET = **********
exports.CENTRALIZED_VET = **********
exports.BACK_OFFICE_PERSON = **********
exports.FARMER = **********
exports.PPU_FARMER = **********
exports.PREVENTIVE = **********
exports.CURATIVE = **********
exports.REPRODUCTIVE = **********
exports.ANIMAL_SUBSCRIPTION_DATE_CLASSIFIER = **********
exports.ANIMAL_SUBSCRIPTION_PLAN_CLASSIFIER = **********
exports.ANIMAL_HEALTH_SCORE = **********
exports.ANIMAL_INACTIVE_REASON_CLASSIFIER = **********
exports.ANIMAL_INACTIVE_DATE_CLASSIFIER = **********
exports.TICKET_CLASSIFIER = **********
exports.CONST_ID_TYPE_STAFF_BACKOFFICE = **********
exports.FARMER = **********
exports.EXPIRY_DATE_CLASSIFIER = **********
exports.ANIMAL_NAME_CLASSIFIER = **********
exports.ANIMAL_EAR_TAG_CLASSIFIER = **********
exports.ANIMAL_TYPE_CLASSIFIER = **********

exports.CLASSIFIER_FREELANCE_SLAB_SR_TICKET = **********

exports.BREED_OF_ANIMAL = **********
exports.LINKED_CARE_CALENDAR_CLASSIFIER = **********
exports.CONST_ID_TYPE_STAFF_RO = **********
exports.CONST_ID_TYPE_STAFF_ZO = **********
exports.CONST_ID_TYPE_STAFF_VET = **********
exports.CONST_ID_TYPE_CUSTOMER_FARMER = **********
exports.CONST_ID_CATEGORY_STAFF = 10002300
exports.CONST_ID_CATEGORY_TASK = 10002700
exports.CONST_ID_CATEGORY_MED = 10005000
exports.CONST_ID_CATEGORY_ACTIVITY_STATUS = 10003000
exports.CONST_ENTITY_TYPE_ID_PARAVET = **********
exports.CONST_ID_CLASSIFIER_VILLAGE = 2000000055
exports.CONST_ID_CLASSIFIER_TALUKA = 2000000054
exports.CONST_ID_CLASSIFIER_DISTRICT = 2000000053
exports.CONST_ID_CLASSIFIER_STATE = 2000000052
exports.CONST_PADDING_VISUAL_ID_CUSTOMER = 6
exports.CONST_PADDING_VISUAL_ID_ANIMAL = 3
exports.CONST_PD_DATE_OFFSET = 90
exports.VILLAGE_TYPE_ID = 1000320004
exports.FARMER_TO_ANIMAL = 1000210004
exports.PPU_PARAVET_TO_FARMER = 1000210010
exports.PPU_PARAVET_TO_TASK = 1000210011
exports.CENTRALIZED_VET_TO_TASK = 1000210012
exports.ANIMAL_TYPE_CALENDAR = 1000460002
exports.FARMER_TYPE_CALENDAR = 1000460001
exports.CONST_ID_ACTIVE = **********
exports.CONST_ID_TYPE_STAFF_PARAVET = **********
exports.COMPLETED_ACTIVITY_STATUS = 1000300050
exports.CANCELLED_ACTIVITY_STATUS = 1000300051
exports.ABANDONED_ACTIVITY_STATUS = 1000300052
exports.CONST_STATUS_ACTIVITY_PENDING = 1000300001
exports.VILLAGE_CLASSIFIER = 2000000055
exports.DOCUMENT_CUSTOMER_THUMBNAIL_TYPE = 1000260003
exports.VET_SIGNATURE = 1000260011
exports.ANIMAL_THUMBNAIL_PHOTO = 1000260001
exports.DOCUMENT_STAFF_THUMBNAIL_TYPE = 1000260004
exports.CUSTOMER_TERMS_AND_CONDITION_STATUS = 2000000207
exports.FREELANCE_DEFAULT_PLAN = 1000860001
exports.STAFF_PAN = 2000000223
exports.SLAB_FOR_STAFF = **********
exports.RELATION_FREELANCE_PARAVET_TO_ANIMAL = 1000210013
exports.FREELANCE_CARE_CATTLE_DEFAULT = 1000240046
exports.FREELANCER_TRAINING_VIDEOS = 1000260019
exports.TASK = 1000460004

exports.FREELANCE_SLAB_CLASSIFIER = 10009100
exports.FREELANCE_SLAB = 1000910001
exports.TRANSACTION_CONFIRMED = 1000880001
exports.TRANSACTION_BLOCKED = 1000880002
exports.TRANSACTION_CANCELLED = 1000880003

exports.TRANSACTION_CREDIT = 1000790001
exports.TRANSACTION_DEBIT = 1000790002

exports.CONST_ARRAY_REFERENCE_COLS = ['reference_id', 'reference_name', 'field_name', 'type_of_data', 'reference_name_l10n']
exports.CONST_TYPES_RELATIONSHIP = {
  routeofficer_paravet: 1000210001,
  paravet_farmer: 1000210002,
  backofficeperson_paravet: 1000210003,
  farmer_animal: 1000210004,
}

exports.CONST_TYPES_STAFF = {
  **********: { type: 'route_officer' },
  **********: { type: 'zonal_officer' },
  **********: { type: 'back_office_person' },
  **********: { type: 'vet' },
  **********: { type: 'paravet' },
  **********: { type: 'pay_per_use_paravet' },
  **********: { type: 'centralized_vet' },
}

exports.CONST_TYPES_CUSTOMER = {
  **********: { type: 'farmer' },
  **********: { type: 'PPU Farmer' },
}

exports.CONST_ACTIVITY_ID_AIP = 1000470001
exports.CONST_ACTIVITY_ID_PD = 1000470003

exports.CONST_ACTIVITY_ID_CMT = 1000180015
exports.CONST_ACTIVITY_ID_FARM_TICK_CONTROL_SPRAYING = 1000180011
exports.CONST_ACTIVITY_ID_TICK_CONTROL = 1000180005

exports.CONST_ACTIVITY_PARAVET_FARM_DETAIL = 1000700002

exports.CONST_STAFF = 1000460003

exports.CONST_SR_KRUSHAL = 1000920001
exports.CONST_SR_FREELANCE = 1000920002

exports.BALANCE_SHEET = 1000930001
exports.LOAN_TAKEN = 1000930002
exports.LOAN_GIVEN = 1000930003


exports.NO = 1000105002

exports.CONST_ACTIVITY_PARAVET_FARM_DETAIL = 1000700002;
exports.NO_MEDS = 1999999999

exports.PUBLISHED_DATE_ClASSIFIER = 2000000227;
exports.ADMINISTERED_DATE_ClASSIFIER = 2000000228;
exports.DIAGONISIS_DATE_CLASSIFIER=2000000268;
exports.OBSERVATION_DATE_CLASSIFIER=2000000269;

exports.AI_FORM_COMPLETE_DATE = 2000000271;
exports.CMT_FORM_COMPLETE_DATE = 2000000272;
exports.PD_FORM_COMPLETE_DATE = 2000000273;
exports.FMT_FORM_COMPLETE_DATE = 2000000274;
exports.FARM_MANAGEMENT = 2000000300;

exports.CONST_ACTIVITY_ID_AIP = 1000470001;
exports.CONST_ACTIVITY_ID_PD = 1000470003;
// exports.REQUISITION_PENDING = 1010000001;
// exports.REQUISITION_APPROVED = 1010000002;
// exports.REQUISITION_REJECTED = 1010000003;
// exports.REQUISITION_OPEN = 1010000004;
// exports.REQUISITION_DELIVERED = 1010000005;
// exports.REQUISITION_RECEIVED = 1010000006;


exports.CONST_ACTIVITY_PARAVET_FARM_DETAIL = 1000700002;

exports.CONST_PRESCRTIPTION_PROCESS_STATUS=10008200;
exports.CONST_PRESCRTIPTION_MEDICINE_DELIVERY_STATUS=10008300;
exports.CONST_PRESCRTIPTION_INVOICE_STATUS=10008400;
exports.REQUISITION_DRAFT=1000820001;
exports.REQUISITION_PENDING=1000820002;
exports.REQUISITION_APPROVED_BY_APPROVER=1000820003;
exports.REQUISITION_REJECTED_BY_APPROVER=1000820004;
exports.REQUISITION_REJECTED_BY_FULLFILLER=1000820005;
exports.REQUISITION_DELVIVERED_BY_FULLFILLER=1000830001;
exports.REQUISITION_RECEIVED_BY_REQUISITIONER=1000830002;
exports.REQUISITION_REJECTED_BY_REQUISITIONER=1000830003;
exports.REQUISITION_INVOICE_GENERATED=1000840001;
exports.REQUISITION_INVOICE_SUBMITTED=1000840002;

exports.REFERENCE_TYPE_REQUISITION=1000460006

exports.CONST_PARTNER_PHARMACY=1000850001;
