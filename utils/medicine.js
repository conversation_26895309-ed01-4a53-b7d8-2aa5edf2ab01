const { dbConnections, postProcessRecords } = require('@krushal-it/ah-orm')
const {classifiers, record_statuses} = require("../ENUMS");
const { loadMeds } = require("../services/common/common.class");

async function calculateMedicineCost(medicines) {
  const medicine_ids = medicines.map(med => med.id)
  const prescribedMedicines = await loadMeds(medicine_ids);
  let total_price = 0
  for (const medicine of medicines) {
    const
      frequency = medicine.prescribed_info.frequency,
      quantity = medicine.dosage,
      days = medicine.prescribed_info.days

    const id = medicine.id
    const prescribedMedicinesClassification = prescribedMedicines[id];
    let frequency_count = 1;
    if (frequency === 'BID') {
      frequency_count = 2
    } else if (frequency === 'TID') {
      frequency_count = 3
    }
    if (prescribedMedicinesClassification) {
      let actual_mrp = prescribedMedicinesClassification.medicine_mrp ? prescribedMedicinesClassification.medicine_mrp : 0;
      const pack_size = prescribedMedicinesClassification.medicine_pack_size ? prescribedMedicinesClassification.medicine_pack_size : 0;

      if (pack_size != 0) {
        mrp = (actual_mrp / pack_size) * quantity;
      }
      mrp = mrp * days * frequency_count
    }

    total_price = total_price + mrp;
  }

  return total_price
}

const prescriptionKeystoColMaps = {
  medicine: {
    cols: [{ client_key: "id", db_key: "value_reference_id" }, { client_key: "dosage", db_key: "value_double" }, { client_key: "prescribed_info", db_key: "value_json" }],
    classifier_id: classifiers.ACTIVITY_MEDICINES, key: "medicine"
  }
};

async function getExistingMedicines(transaction,care_calendar_id){
  let activityMedicineData = await transaction.getRepository("care_calendar_classification").find({
    where: { care_calendar_id, classifier_id: prescriptionKeystoColMaps.medicine.classifier_id, active: record_statuses.ACTIVE }
  });
  activityMedicineData = postProcessRecords(undefined, activityMedicineData, { json_columns: ["value_json", "value_l10n"] });
  const processedMedData = [];
  for (const elem of activityMedicineData) {
    const entry = {};
    for (const col of prescriptionKeystoColMaps.medicine.cols) {
      entry[col.client_key] = elem[col.db_key];
    }
    entry["care_calendar_classification_id"] = elem.care_calendar_classification_id
    processedMedData.push(entry);
  }

  return processedMedData
}


module.exports = {
  calculateMedicineCost,
  getExistingMedicines
}