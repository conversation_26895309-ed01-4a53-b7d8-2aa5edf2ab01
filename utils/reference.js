const {
  ENTITY_TYPE_CUSTOMER,
  ENTITY_TYPE_ANIMAL,
} = require('./constant');

const {
  customerLocalClassifier,
  animalLocalClassifier, 
} = require('./ref_config');

const getLocalClassifierConfig = (entity) => {
  switch (entity) {
    case ENTITY_TYPE_CUSTOMER:
      return customerLocalClassifier;
    case ENTITY_TYPE_ANIMAL:
      return animalLocalClassifier;
    default:
      return {};
  }
};

module.exports = {
  getLocalClassifierConfig,
};
