const { isNumber } = require('lodash')
const {RELATION_REQUISITION,RELATION_REQUISITION_LINE_ITEMS,RELATION_WAREHOUSE_BLOCKQTY}= require('./constant')

const requestValidation = (data,type,action)=>{
    let mandantoryJSONData={}
    mandantoryJSONData[RELATION_REQUISITION]={
        requisitioner_type_id:0,
        requisitioner_uuid:'',
        status_id:0,
        requisition_line_items:[]
    }

    mandantoryJSONData[RELATION_REQUISITION_LINE_ITEMS]={
        medicine_id:0,
        unit:'',
        quantity:0
    }

    mandantoryJSONData[RELATION_WAREHOUSE_BLOCKQTY]={
        warehouse_type_id:0,
        warehouse_uuid:'',
        source_type_id:0,
        source_uuid:'',
        medicines:[]
    }
    let recdata=Object.keys(data)
    let mandatedata=Object.keys(mandantoryJSONData[type])
    for(let i=0;i<mandatedata.length;i++){
        let index=recdata.findIndex(item=>item==mandatedata[i])
        if(index==-1) return false
        // if(!recdata.includes(mandatedata[i]) || typeof(mandantoryJSONData[type][mandatedata[i]])!=typeof(data[recdata[index]]) || (Array.isArray(data[recdata[index]]) && data[recdata[index]].length==0 && type!=RELATION_REQUISITION)){
        if(!recdata.includes(mandatedata[i]) || (Array.isArray(data[recdata[index]]) && data[recdata[index]].length==0 && type!=RELATION_REQUISITION) ||(isNumber(data[recdata[index]]) && (data[recdata[index]])<0)){
            return false
        }
        else{
            if(type==RELATION_WAREHOUSE_BLOCKQTY && Array.isArray(mandantoryJSONData[type][mandatedata[i]])){
                let med=['medicine_id']
                if(action=='add') med=med.concat(['days','frequency','dosage'])
                if(action=='remove') med=med.concat(['unblocked_qty'])
                for(let j=0;j<med.length;j++){
                    if(!isNaN(mandantoryJSONData[type][mandatedata[i]][med[j]]) || mandantoryJSONData[type][mandatedata[i]][med[j]]<=0) return false
                }
            }
        }  
    }
    return true
}

const validateRequisition = (data,action)=>{
    let valid=true
      valid = requestValidation(data,RELATION_REQUISITION,action)
      if(valid){
        for(let i=0;i<data.requisition_line_items.length;i++){
          valid=requestValidation(data.requisition_line_items[i],RELATION_REQUISITION_LINE_ITEMS,action)
          if(!valid) break
        }
      }
    return valid
}


module.exports={requestValidation,validateRequisition}