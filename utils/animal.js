const { dbConnections } = require("@krushal-it/ah-orm");
const {errorLog} = require('@krushal-it/mobile-or-server-lib')

const { 
  RELATION_RELATIONSHIP, 
  RELATION_CUSTOMER, 
  CONST_PADDING_VISUAL_ID_ANIMAL, 
  CONST_TYPES_RELATIONSHIP 
} = require("./constant");

const generateAnimalVisualId = async(farmer_id)=>{
    try {
        let visualId = await dbConnections().main.manager.transaction(async(transactionalEntityManager)=>{
          let condition = {
            entity_1_entity_id: farmer_id,
            entity_relationship_type_id: CONST_TYPES_RELATIONSHIP.farmer_animal,
          }; 
          let customer = await transactionalEntityManager
            .getRepository(RELATION_CUSTOMER)
            .createQueryBuilder()
            .where("customer_id= :customer_id", { customer_id:farmer_id })
            .execute();
          if(customer.length<=0) return null;
          let customer_visual_id = customer[0].customer_customer_visual_id;
          let animals = await transactionalEntityManager
              .getRepository(RELATION_RELATIONSHIP)
              .createQueryBuilder()
              .where(
                "entity_relationship_type_id= :entity_relationship_type_id AND entity_1_entity_uuid= :entity_1_entity_id",condition)
              .execute();
          let count = String(animals.length+1).padStart(CONST_PADDING_VISUAL_ID_ANIMAL,'0');
          let animal_visual_id =  `${customer_visual_id}/A${count}`;
          return animal_visual_id;
        });
        return visualId;
    } catch (error) {
      errorLog("error in LibCommonServices:33",params,{message:error.message})
      return null;
    }
  }

  const findDuplicateIds = (observations) => {
    const idSet = new Set();
    const duplicateIds = [];
  
    for (const observation of observations) {
        if (idSet.has(observation.id)) {
            if (duplicateIds.indexOf(observation.id) === -1)
            duplicateIds.push(observation.id);
        } else {
            idSet.add(observation.id);
        }
    }
  
    return duplicateIds;
  }

module.exports = {generateAnimalVisualId, findDuplicateIds}