const {
  dbConnections,
  initializeAllRepositories,
} = require("./connectionHelper");

const regenerateUpdatedAtTriggers = async () => {
  const allDBConnections = dbConnections();
  await initializeAllRepositories(allDBConnections);
  for (const dbConnectionStringName in allDBConnections) {
    const dbConnection = allDBConnections[dbConnectionStringName];
    if (
      dbConnection.default.options &&
      dbConnection.default.options.execute_updated_at_triggers
    ) {
      for (const metaData of dbConnection.default.entityMetadatas) {
        if (process.env.IS_SERVER === "True") {
          const regenerateUpdatedAtTriggerQuery =
            'CREATE OR REPLACE TRIGGER "' +
            metaData.target +
            '_updated_at_update" BEFORE UPDATE ON "' +
            metaData.schema +
            '"."' +
            metaData.target +
            '" FOR EACH ROW EXECUTE PROCEDURE "common".set_updated_at_column_to_now();';
          try {
            await dbConnection.manager.query(regenerateUpdatedAtTriggerQuery);
          } catch (error) {
            console.log('trigger error = ', error)
          }
        } else {
          const primaryKey = metaData.primaryColumns[0].databaseName;
          const dropTriggerQuery =
            "DROP TRIGGER " + metaData.target + "_updated_at_update";
          try {
            await dbConnection.manager.query(dropTriggerQuery);
          } catch (error) {
          }
          const regenerateUpdatedAtTriggerQuery =
            "CREATE TRIGGER " +
            metaData.target +
            "_updated_at_update " +
            "AFTER UPDATE ON " +
            metaData.target +
            " " +
            "FOR EACH ROW " +
            "WHEN NEW.updated_at <= OLD.updated_at " +
            "BEGIN	" +
            "  UPDATE " +
            metaData.target +
            " SET updated_at = CURRENT_TIMESTAMP WHERE " +
            primaryKey +
            " = OLD." +
            primaryKey +
            "; " +
            "END; ";
          try {
            await dbConnection.manager.query(regenerateUpdatedAtTriggerQuery);
          } catch (error) {
            console.log('trigger error = ', error)
          }
        }
      }
    }
  }
};

module.exports = { regenerateUpdatedAtTriggers };