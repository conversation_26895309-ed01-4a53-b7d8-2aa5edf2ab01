const moment = require("moment")
const { getTime } = require("../../helpers/set_3_pm")
const AnimalQueries = require("../../../database_utils/queries/animal/animal.query")
const CustomerQueries = require("../../../database_utils/queries/customer/customer.query")
const { preventiveHealthCareAnimal, preventiveHealthCareFarmer } = require("../../configs/care.config")
const { CareCalendarQueries } = require("@krushal-it/back-end-lib")
const { dbConnections } = require("@krushal-it/ah-orm")
const { entity_type, data_map } = require("@krushal-it/back-end-lib/ENUMS")
const { referenceRepositoryInstance } = require("@krushal-it/back-end-lib")
const _ = require("lodash")
const { applyRules } = require("../../../services/rules/rules.service")

const currTime = getTime(new Date())
class CareCalendarJob {
  constructor(options) {
    this.lastJobTimestamp = options.scheduledTime
    this.care_calendar_entries = []
  }

  async #getPreventiveCalendarConfig() {
    // check if there a care calendar config available in database
    let healthCalendarRow = await referenceRepositoryInstance.getReferenceDetails(dbConnections().main.manager, data_map.PREVENTIVE_CALENDAR_CONFIG)
    let healthCalendarData = healthCalendarRow[data_map.PREVENTIVE_CALENDAR_CONFIG]?.reference_information || {}

    // if the entry in database is empty, read from the care.config file
    if (_.isEmpty(healthCalendarData["animal"])) {
      healthCalendarData["animal"] = preventiveHealthCareAnimal
    }

    if (_.isEmpty(healthCalendarData["farmer"])) {
      healthCalendarData["farmer"] = preventiveHealthCareFarmer
    }

    this.healthCalendarData = healthCalendarData
  }

  async #getAnimals() {
    this.cattleList = await AnimalQueries.animalForCalendar({
      created_after: this.lastJobTimestamp
    })

    let validCattleList = []

    for (const cattle of this.cattleList) {
      // these are the facts which are applied to the rules
      const facts = {
        subscription_plan: cattle["healthcare_subscription_plan"]
      }

      // returns the matching rules based on the provided facts
      const matchingRulesData = await applyRules(facts, "care_calendar_rules")

      if (matchingRulesData["calendar-generation-validation"]?.valid) {
        validCattleList.push(cattle)
      }
    }

    this.cattleList = validCattleList

    this.cattleList.forEach(cattle => {
      if (cattle["pregnancy_month_1"]) cattle["pregnancy_month_1"] = parseInt(cattle["pregnancy_month_1"])
    })

  }

  async #getFarmers() {
    if(Array.isArray(this.cattleList) && this.cattleList.length > 0) {
      this.farmerList = await CustomerQueries.getDetailsForPlannedFarmLevelTask({
        last_job_run_time: this.lastJobTimestamp
      })
    } else {
      this.farmerList = []
    }
  }

  async #createCalendarEntriesAnimalLevel() {
    this.cattleList.forEach(cattle => {
      if (!cattle.healthcare_plan_subscription_date_1) {
        console.warn("Missing subscription date. Skipping care calendar generation for the cattle with id ", cattle.animal_id)
        return
      }

      let start_date_of_enrolment = moment(cattle.healthcare_plan_subscription_date_1)

      // Adding 12 months as the end date of enrolment as all plans are 1 year based
      // This could be changed as per the plan type if there is such requirement in future
      let end_date_of_enrolment = moment(cattle.healthcare_plan_subscription_date_1).add(1, "year")

      for (const [activity_id, activity_config] of Object.entries(this.healthCalendarData["animal"])) {
        const { scheduledArray, specific, checks } = activity_config

        if (specific) {
          specific.forEach(specificItem => {
            let { month: scheduled_month, leniency } = specificItem

            const activityStartDate = moment()
              .month(scheduled_month - 1)
              .startOf("month")
            const activityEndDate = activityStartDate
              .clone()
              .add(leniency.day, "days")
              .add(leniency.month, "month")
              .add(leniency.year, "years")
              .startOf("month")

            if (start_date_of_enrolment.isSameOrBefore(activityEndDate)) {
              const activity_date = moment.max(start_date_of_enrolment, activityStartDate)

              if (this.#checks(cattle, checks, activity_date)) {
                this.care_calendar_entries.push({
                  entity_type_id: entity_type.ANIMAL,
                  entity_uuid: cattle.animal_id,
                  activity_id: activity_id,
                  activity_date: moment(activity_date.toJSON()).format("YYYY-MM-DD")
                })
              }
            } else {
              const activityStartDate = moment()
                .month(scheduled_month - 1)
                .startOf("month")
                .add(1, "year")
              const activityEndDate = activityStartDate.clone().endOf("month")

              if (activityEndDate.isSameOrBefore(end_date_of_enrolment)) {
                const activity_date = activityStartDate.clone()
                if (this.#checks(cattle, checks, activity_date)) {
                  this.care_calendar_entries.push({
                    entity_type_id: entity_type.ANIMAL,
                    entity_uuid: cattle.animal_id,
                    activity_id: activity_id,
                    activity_date: moment(activity_date.toJSON()).format("YYYY-MM-DD")
                  })
                }
              }
            }
          })
        } else if (scheduledArray) {
          let activity_date = start_date_of_enrolment.clone()

          scheduledArray.forEach(scheduledArrayItem => {
            const { day, month, year } = scheduledArrayItem
            activity_date.add(day, "days")
            activity_date.add(month, "months")
            activity_date.add(year, "years")

            if (this.#checks(cattle, checks, activity_date)) {
              this.care_calendar_entries.push({
                entity_type_id: entity_type.ANIMAL,
                entity_uuid: cattle.animal_id,
                activity_id: activity_id,
                activity_date: moment(activity_date.toJSON()).format("YYYY-MM-DD")
              })
            }
          })
        }
      }
    })
  }

  async #createCalendarEntriesFarmerLevel() {
    this.farmerList.forEach(farmerItem => {
      let { farmer_id, min_sub_date, max_sub_date } = farmerItem

      let current_start_date_of_enrolment = moment(min_sub_date)

      // Adding 12 months as the end date of enrolment as all plans are 1 year based
      // This could be changed as per the plan type if there is such requirement in future
      let current_end_date_of_enrolment = current_start_date_of_enrolment.clone().add(1, "year")

      let previous_end_date_of_enrolment = max_sub_date ? moment(max_sub_date).add(1, "year") : moment(new Date(0))

      for (const [activity_id, activity_config] of Object.entries(this.healthCalendarData["farmer"])) {
        const { scheduledArray, specific, checks } = activity_config

        if (scheduledArray) {
          let activity_date = current_start_date_of_enrolment.clone()

          scheduledArray.forEach(scheduledArrayItem => {
            const { day, month, year } = scheduledArrayItem

            activity_date.add(day, "days")
            activity_date.add(month, "months")
            activity_date.add(year, "years")

            if (activity_date.isAfter(previous_end_date_of_enrolment) && activity_date.isSameOrBefore(current_end_date_of_enrolment)) {
              this.care_calendar_entries.push({
                entity_type_id: entity_type.CUSTOMER,
                entity_uuid: farmer_id,
                activity_id: activity_id,
                activity_date: moment(activity_date.toJSON()).format("YYYY-MM-DD")
              })
            }
          })
        }
      }
    })
  }

  /**
   *
   * @param {Object} data containing key value pairs
   * @param {Array} checkArray Array of checks
   * @param {moment} activity_date activity_date
   * @returns {Boolean}
   */
  #checks(data, checkArray, activity_date) {
    for (let checkIndex = 0; checkIndex < checkArray.length; checkIndex++) {
      let { key, type } = checkArray[checkIndex]

      if (!data[key]) continue

      switch (type) {
        case "month_diff":
          let { lt, gt } = checkArray[checkIndex]
          let monthDiff = activity_date.diff(currTime, "months")
          let total = parseInt(data[key]) + monthDiff
          if (total >= lt && total <= gt) return false
          break

        case "exists_in_array":
          const { array_val } = checkArray[checkIndex]
          if (!array_val.includes(data[key])) return false
          break
      }
    }
    return true
  }

  async #insertCalendarEntries() {
    if (this.care_calendar_entries.length) {
      await CareCalendarQueries.insertcalendarEntries(this.care_calendar_entries, { datasource: dbConnections().main.manager })
    }
  }

  async start() {
    try {
      console.log("JOB:care-calendar ✅ STARTED")

      await this.#getPreventiveCalendarConfig()

      await this.#getAnimals()
      await this.#createCalendarEntriesAnimalLevel()

      await this.#getFarmers()
      await this.#createCalendarEntriesFarmerLevel()

      await this.#insertCalendarEntries()

      console.log("JOB:care-calendar ✅ COMPLETED")
    } catch (error) {
      console.log("JOB:care-calendar ✅ FAILED")
      console.error(error)
      throw error
    }
  }
}

module.exports = CareCalendarJob