const { dbConnections } = require("@krushal-it/ah-orm")
const { classifiers,yes_no_values } = require("../ENUMS")

async function verifyOtp(data) {
  const { care_calendar_id } = data
  const parameters = { care_calendar_id: care_calendar_id, classifier_id: classifiers.OTP }
  const otp = await dbConnections()
    .main.manager.createQueryBuilder()
    .select("otp.value_int as otp")
    .from("main.care_calendar_classification", "otp")
    .where("otp.care_calendar_id = :care_calendar_id and classifier_id = :classifier_id")
    .setParameters(parameters)
    .execute()
    
  if (!otp.length) {
      throw new Error("Given care_calendar is not part of OTP flow.")
  }
   

  const otp_value = otp[0].otp
  

  if (otp_value === data.otp) {
    await updateOtpVerifiedClassification(care_calendar_id)
    return { isValid: true }
  } else {
    return { isValid: false }
  }
}




async function getOtp(care_calendar_id) {
  const parameters = { care_calendar_id: care_calendar_id, classifier_id: classifiers.OTP }
  const otp = await dbConnections()
    .main.manager.createQueryBuilder()
    .select("otp.value_int as otp")
    .from("main.care_calendar_classification", "otp")
    .where("otp.care_calendar_id = :care_calendar_id and classifier_id = :classifier_id")
    .setParameters(parameters)
    .execute()
  if (!otp.length) {
    throw new Error("Otp is not present for this task")
  }
  const otp_value = otp[0].otp

  return {
    otp: otp_value
  }
}


async function updateOtpVerifiedClassification(care_calendar_id) {
  try {
    const data = await dbConnections().main.manager.createQueryBuilder()
    .update('care_calendar_classification')
    .set({ value_reference_id: yes_no_values.YES })
    .where({ classifier_id: classifiers.OTP_VERIFIED, care_calendar_id:care_calendar_id })
    .execute()
  } catch (error) {
    throw new Error(error)
  }
  
}
module.exports = {
  verifyOtp,
  getOtp
}
