const { RELATION_CALENDAR_CLASSIFICATION } = require('./constant')

const { account_payment_type, classifiers, data_map, payer } = require('../ENUMS.js')

const taskRepositoryInstance = require('../repositories/taskRepository')
const customerRepositoryInstance = require('../repositories/customerRepository')
const animalRepositoryInstance = require('../repositories/animalRepository')
const referenceRepositoryInstance = require('../repositories/referenceRepository')

const { saveClassificationData } = require('../services/common/classification.helper.js')

class ActivityHelper {
  static async generateTicketNumber(transaction) {
    const maxTicketNumber = await transaction.getRepository(RELATION_CALENDAR_CLASSIFICATION).createQueryBuilder().select('MAX(value_string_256)', 'maxTicketNumber').where('classifier_id = :classifier_id', { classifier_id: ********** }).getRawOne()

    let newTicketNumber = 'SR000001'
    let lastNumber = -1

    if (maxTicketNumber.maxTicketNumber === null) {
      return newTicketNumber
    }

    if (maxTicketNumber.maxTicketNumber.startsWith('SR')) {
      lastNumber = parseInt(maxTicketNumber.maxTicketNumber.substring(2), 10)
    } else {
      // When we deploy the change of CCCT To SR, The first request will return CCCT request number.
      // To handle that we have to write the below piece of code. Also to handle any scenario where we still
      // get the request number in CCCT format
      lastNumber = parseInt(maxTicketNumber.maxTicketNumber.substring(4), 10)
    }

    const newNumber = lastNumber + 1
    newTicketNumber = `SR${newNumber.toString().padStart(6, '0')}`

    return newTicketNumber
  }

  static async processPaymentDetails(transaction, care_calendar_id, service_cost, medicine_cost) {
    // get task details
    const taskDetails = await taskRepositoryInstance.getTaskDetails(transaction, care_calendar_id)

    if (!taskDetails[care_calendar_id]) {
      throw new Error('Task details not found')
    }

    const taskDataMain = taskDetails[care_calendar_id].primary
    const taskDataClassification = taskDetails[care_calendar_id].classification

    const customer_id = await taskRepositoryInstance.getCustomerId(transaction, taskDetails[care_calendar_id])
    const animal_id = await taskRepositoryInstance.getAnimalId(transaction, taskDetails[care_calendar_id])

    const customerDetails = await customerRepositoryInstance.getCustomerDetails(transaction, customer_id)
    const animalDetails = await animalRepositoryInstance.getCattleDetails(transaction, animal_id)

    const currentCreditLimitClassificationRow = customerDetails[customer_id].classification.find((item) => item['classifier_id'] === classifiers.CUSTOMER_CREDIT_LIMIT)
    const currentCreditUsageClassificationRow = customerDetails[customer_id].classification.find((item) => item['classifier_id'] === classifiers.CUSTOMER_CREDIT_USAGE)
    const animalSubscriptionPlanClassificationRow = animalDetails[animal_id].classification.find((item) => item['classifier_id'] === classifiers.SUBSCRIPTION_PLAN)
    const taskPaymentDetailsClassificationRow = taskDataClassification.find((item) => item['classifier_id'] === classifiers.TASK_PAYMENT_DETAILS)

    const animalSubscriptionPlan = animalSubscriptionPlanClassificationRow?.value_reference_id

    // default values to check if customer credit limit flow is required
    let currentCreditUsage = 0
    let currentCreditLimit = 0
    let handleCreditLimitFlag = false;

    if (currentCreditLimitClassificationRow && currentCreditLimitClassificationRow.value_double && currentCreditLimitClassificationRow.value_double >= 0) {
      currentCreditLimit = parseFloat(currentCreditLimitClassificationRow.value_double)
      currentCreditUsage = (currentCreditUsageClassificationRow && currentCreditUsageClassificationRow.value_double) ? parseFloat(currentCreditUsageClassificationRow.value_double) : 0
      handleCreditLimitFlag = true;
    }

    let paymentDetailsToApply = undefined

    if (taskPaymentDetailsClassificationRow && taskPaymentDetailsClassificationRow.value_json) {
      paymentDetailsToApply = taskPaymentDetailsClassificationRow.value_json
    } else {
      paymentDetailsToApply = await this.getPaymentDetailsBySubscriptionPlan(animalSubscriptionPlan, transaction)
    }

    let taskCostClassifications = []

    if (service_cost >= 0) {
      const result = await this.handleTaskPaymentFlow(taskDetails[care_calendar_id], paymentDetailsToApply?.service_cost, service_cost, 'service_cost', currentCreditLimit, currentCreditUsage,handleCreditLimitFlag)
      taskCostClassifications.push(...result.taskClassifications)
      currentCreditUsage = result.currentCreditUsage
    }

    if (medicine_cost >= 0) {
      const result = await this.handleTaskPaymentFlow(taskDetails[care_calendar_id], paymentDetailsToApply?.medicine_cost, medicine_cost, 'medicine_cost', currentCreditLimit, currentCreditUsage,handleCreditLimitFlag)
      taskCostClassifications.push(...result.taskClassifications)
      currentCreditUsage = result.currentCreditUsage
    }

    // save taskCostClassifications
    await taskRepositoryInstance.saveTaskClassifications(transaction, taskCostClassifications)

    // save new currentCreditUsage in customer classifications
    await saveClassificationData(transaction, 'CUSTOMER_CLASSIFICATION', customer_id, { customer_credit_usage: currentCreditUsage })

    return true
  }

  static async handleTaskPaymentFlow(taskData, taskPaymentDetails, taskItemCost, taskCostType, currentCreditLimit, currentCreditUsage, handleCreditLimitFlag) {
    let taskClassifications = []

    let taskCostClassifier = classifiers.TASK_SERVICE_COST_PAYMENT_DETAILS
    if (taskCostType === 'medicine_cost') {
      taskCostClassifier = classifiers.TASK_MEDICINE_COST_PAYMENT_DETAILS
    }

    if(!taskPaymentDetails) {

      taskClassifications.push({
        care_calendar_id: taskData.primary.care_calendar_id,
        classifier_id: taskCostClassifier,
        value_double: taskItemCost,
        value_json: {
          value: [],
        },
      })

      return {
        taskClassifications,
        currentCreditUsage,
      }
    }

    if (!handleCreditLimitFlag) {
      taskClassifications.push({
        care_calendar_id: taskData.primary.care_calendar_id,
        classifier_id: taskCostClassifier,
        value_double: taskItemCost,
        value_json: {
          value: [
            {
              payer_type: taskPaymentDetails.payer_type,
              payment_type: taskPaymentDetails.payment_type,
              amount: taskItemCost,
            },
          ],
        },
      })

      return {
        taskClassifications,
        currentCreditUsage,
      }
    }

    if (taskPaymentDetails.payment_type === account_payment_type.PAID_BY_PROVIDER_WITH_CREDIT_LIMIT) {
      // sometimes a task can be submitted multiple times.
      // in this case, we have to fist adjust the credit usage by removing the last used one
      // and the recalculate the credit usage
      let previouslyAppliedCredit = 0
      const taskCreditUsageRow = taskData.classification.find((item) => item['classifier_id'] === taskCostClassifier)
      if (taskCreditUsageRow && taskCreditUsageRow.value_json && taskCreditUsageRow.value_json.value) {
        for (let element of taskCreditUsageRow.value_json.value) {
          const { payer_type, payment_type, amount } = element
          if (payment_type === account_payment_type.PAID_BY_PROVIDER_WITH_CREDIT_LIMIT) {
            previouslyAppliedCredit = previouslyAppliedCredit + amount
          }
        }
      }

      currentCreditUsage = currentCreditUsage - previouslyAppliedCredit

      // if the current limit is already met or crossed before availing the current service
      // everything has to be paid by the customer
      if (currentCreditUsage >= currentCreditLimit) {
        taskClassifications.push({
          care_calendar_id: taskData.primary.care_calendar_id,
          classifier_id: taskCostClassifier,
          value_double: taskItemCost,
          value_json: {
            value: [
              {
                payer_type: payer.CUSTOMER,
                payment_type: account_payment_type.SELF_PAY,
                amount: taskItemCost,
              },
            ],
          },
        })
        // if the current limit is crossed after availing the current service
        // the cost share has to be calculated for provider and customer
      } else if (currentCreditUsage + taskItemCost > currentCreditLimit) {
        taskClassifications.push({
          care_calendar_id: taskData.primary.care_calendar_id,
          classifier_id: taskCostClassifier,
          value_double: taskItemCost,
          value_json: {
            value: [
              {
                payer_type: payer.CUSTOMER,
                payment_type: account_payment_type.SELF_PAY,
                amount: currentCreditUsage + taskItemCost - currentCreditLimit,
              },
              {
                payer_type: taskPaymentDetails.payer_type,
                payment_type: account_payment_type.PAID_BY_PROVIDER_WITH_CREDIT_LIMIT,
                amount: currentCreditLimit - currentCreditUsage,
              },
            ],
          },
        })
        // if the limit is not reached after availing the current service
        // it will be paid by the provider
      } else {
        taskClassifications.push({
          care_calendar_id: taskData.primary.care_calendar_id,
          classifier_id: taskCostClassifier,
          value_double: taskItemCost,
          value_json: {
            value: [
              {
                payer_type: taskPaymentDetails.payer_type,
                payment_type: account_payment_type.PAID_BY_PROVIDER_WITH_CREDIT_LIMIT,
                amount: taskItemCost,
              },
            ],
          },
        })
      }
      // update the current usage
      // current usage cannot be more than limit
      currentCreditUsage = Math.min(currentCreditUsage + taskItemCost, currentCreditLimit)
    } else {
      taskClassifications.push({
        care_calendar_id: taskData.primary.care_calendar_id,
        classifier_id: taskCostClassifier,
        value_double: taskItemCost,
        value_json: {
          value: [
            {
              payer_type: taskPaymentDetails.payer_type,
              payment_type: taskPaymentDetails.payment_type,
              amount: taskItemCost,
            },
          ],
        },
      })
    }

    return {
      taskClassifications,
      currentCreditUsage,
    }
  }

  static async getPaymentDetailsBySubscriptionPlan(animalSubscriptionPlan, transaction) {

    const refData = await referenceRepositoryInstance.getReferenceDetails(transaction, [
      data_map.DEFAULT_PAYMENT_MAPPING_BY_PLAN,
      data_map.SUBSCRIPTION_PLAN_MAPPING
    ])
    
    const defaultPaymentMappingByPlan = refData[data_map.DEFAULT_PAYMENT_MAPPING_BY_PLAN]
    const subscriptionPlanIdtoName = refData[data_map.SUBSCRIPTION_PLAN_MAPPING]
    
    const planPaymentDetailsName = subscriptionPlanIdtoName.reference_information[animalSubscriptionPlan]
    return defaultPaymentMappingByPlan.reference_information[planPaymentDetailsName]
  }
}

module.exports = {
  ActivityHelper,
}
