[tool.poetry]
name = "location_analytics"
version = "0.1.0"
description = "test"
authors = ["Suresh Mali <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
fastapi = "0.93.0"
uvicorn = {extras = ["standard"], version = "0.21.0"}
gunicorn = "20.1.0"
sqlalchemy = {extras = ["asyncio"], version = "^2.0.5.post1"}
psycopg2 = "^2.9.5"
asyncpg = "^0.27.0"
pydantic = "^1.10.6"
pandas = "^1.5.3"
apscheduler = "^3.10.1"

[tool.poetry.group.dev.dependencies]
debugpy = "^1.6.6"
folium = "^0.14.0"
jupyter = "^1.0.0"
autopep8 = "^2.0.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
